{"name": "@wallet-standard/core", "version": "1.1.1", "author": "Solana Maintainers <<EMAIL>>", "repository": "https://github.com/wallet-standard/wallet-standard", "license": "Apache-2.0", "publishConfig": {"access": "public"}, "files": ["lib", "src", "LICENSE"], "engines": {"node": ">=16"}, "type": "module", "sideEffects": false, "main": "./lib/cjs/index.js", "module": "./lib/esm/index.js", "types": "./lib/types/index.d.ts", "exports": {"require": "./lib/cjs/index.js", "import": "./lib/esm/index.js", "types": "./lib/types/index.d.ts"}, "dependencies": {"@wallet-standard/app": "^1.1.0", "@wallet-standard/base": "^1.1.0", "@wallet-standard/errors": "^0.1.1", "@wallet-standard/features": "^1.1.0", "@wallet-standard/wallet": "^1.1.0"}, "devDependencies": {"shx": "^0.4.0"}, "scripts": {"clean": "shx mkdir -p lib && shx rm -rf lib", "package": "shx mkdir -p lib/cjs && shx echo '{ \"type\": \"commonjs\" }' > lib/cjs/package.json"}}