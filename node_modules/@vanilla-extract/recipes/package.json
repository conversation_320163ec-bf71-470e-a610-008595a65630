{"name": "@vanilla-extract/recipes", "version": "0.5.7", "description": "Create multi-variant styles with a type-safe runtime API, heavily inspired by https://stitches.dev", "sideEffects": false, "main": "dist/vanilla-extract-recipes.cjs.js", "module": "dist/vanilla-extract-recipes.esm.js", "types": "dist/vanilla-extract-recipes.cjs.d.ts", "exports": {"./package.json": "./package.json", ".": {"module": "./dist/vanilla-extract-recipes.esm.js", "default": "./dist/vanilla-extract-recipes.cjs.js"}, "./createRuntimeFn": {"module": "./createRuntimeFn/dist/vanilla-extract-recipes-createRuntimeFn.esm.js", "default": "./createRuntimeFn/dist/vanilla-extract-recipes-createRuntimeFn.cjs.js"}}, "files": ["/dist", "/createRuntimeFn"], "preconstruct": {"entrypoints": ["index.ts", "createRuntimeFn.ts"]}, "repository": {"type": "git", "url": "https://github.com/vanilla-extract-css/vanilla-extract.git", "directory": "packages/recipes"}, "author": "SEEK", "license": "MIT", "peerDependencies": {"@vanilla-extract/css": "^1.0.0"}, "devDependencies": {"@vanilla-extract/css": "^1.17.4"}}