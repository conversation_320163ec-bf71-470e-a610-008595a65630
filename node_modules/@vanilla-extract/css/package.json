{"name": "@vanilla-extract/css", "version": "1.17.4", "description": "Zero-runtime Stylesheets-in-TypeScript", "sideEffects": true, "main": "dist/vanilla-extract-css.cjs.js", "module": "dist/vanilla-extract-css.esm.js", "types": "dist/vanilla-extract-css.cjs.d.ts", "browser": {"./dist/vanilla-extract-css.cjs.js": "./dist/vanilla-extract-css.browser.cjs.js", "./dist/vanilla-extract-css.esm.js": "./dist/vanilla-extract-css.browser.esm.js"}, "exports": {"./package.json": "./package.json", ".": {"types": "./dist/vanilla-extract-css.cjs.d.ts", "browser": {"module": "./dist/vanilla-extract-css.browser.esm.js", "default": "./dist/vanilla-extract-css.browser.cjs.js"}, "module": "./dist/vanilla-extract-css.esm.js", "default": "./dist/vanilla-extract-css.cjs.js"}, "./recipe": {"types": "./recipe/dist/vanilla-extract-css-recipe.cjs.d.ts", "browser": {"module": "./recipe/dist/vanilla-extract-css-recipe.browser.esm.js", "default": "./recipe/dist/vanilla-extract-css-recipe.browser.cjs.js"}, "module": "./recipe/dist/vanilla-extract-css-recipe.esm.js", "default": "./recipe/dist/vanilla-extract-css-recipe.cjs.js"}, "./functionSerializer": {"types": "./functionSerializer/dist/vanilla-extract-css-functionSerializer.cjs.d.ts", "browser": {"module": "./functionSerializer/dist/vanilla-extract-css-functionSerializer.browser.esm.js", "default": "./functionSerializer/dist/vanilla-extract-css-functionSerializer.browser.cjs.js"}, "module": "./functionSerializer/dist/vanilla-extract-css-functionSerializer.esm.js", "default": "./functionSerializer/dist/vanilla-extract-css-functionSerializer.cjs.js"}, "./adapter": {"types": "./adapter/dist/vanilla-extract-css-adapter.cjs.d.ts", "browser": {"module": "./adapter/dist/vanilla-extract-css-adapter.browser.esm.js", "default": "./adapter/dist/vanilla-extract-css-adapter.browser.cjs.js"}, "module": "./adapter/dist/vanilla-extract-css-adapter.esm.js", "default": "./adapter/dist/vanilla-extract-css-adapter.cjs.js"}, "./transformCss": {"types": "./transformCss/dist/vanilla-extract-css-transformCss.cjs.d.ts", "browser": {"module": "./transformCss/dist/vanilla-extract-css-transformCss.browser.esm.js", "default": "./transformCss/dist/vanilla-extract-css-transformCss.browser.cjs.js"}, "module": "./transformCss/dist/vanilla-extract-css-transformCss.esm.js", "default": "./transformCss/dist/vanilla-extract-css-transformCss.cjs.js"}, "./fileScope": {"types": "./fileScope/dist/vanilla-extract-css-fileScope.cjs.d.ts", "browser": {"module": "./fileScope/dist/vanilla-extract-css-fileScope.browser.esm.js", "default": "./fileScope/dist/vanilla-extract-css-fileScope.browser.cjs.js"}, "module": "./fileScope/dist/vanilla-extract-css-fileScope.esm.js", "default": "./fileScope/dist/vanilla-extract-css-fileScope.cjs.js"}, "./fileScope/package.json": "./fileScope/package.json", "./injectStyles": {"types": "./injectStyles/dist/vanilla-extract-css-injectStyles.cjs.d.ts", "browser": {"module": "./injectStyles/dist/vanilla-extract-css-injectStyles.browser.esm.js", "default": "./injectStyles/dist/vanilla-extract-css-injectStyles.browser.cjs.js"}, "module": "./injectStyles/dist/vanilla-extract-css-injectStyles.esm.js", "default": "./injectStyles/dist/vanilla-extract-css-injectStyles.cjs.js"}, "./disableRuntimeStyles": {"types": "./disableRuntimeStyles/dist/vanilla-extract-css-disableRuntimeStyles.cjs.d.ts", "browser": {"module": "./disableRuntimeStyles/dist/vanilla-extract-css-disableRuntimeStyles.browser.esm.js", "default": "./disableRuntimeStyles/dist/vanilla-extract-css-disableRuntimeStyles.browser.cjs.js"}, "module": "./disableRuntimeStyles/dist/vanilla-extract-css-disableRuntimeStyles.esm.js", "default": "./disableRuntimeStyles/dist/vanilla-extract-css-disableRuntimeStyles.cjs.js"}}, "preconstruct": {"entrypoints": ["index.ts", "recipe.ts", "functionSerializer.ts", "adapter.ts", "transformCss.ts", "fileScope.ts", "injectStyles.ts", "disableRuntimeStyles.ts"]}, "files": ["/dist", "/recipe", "/functionSerializer", "/adapter", "/transformCss", "/fileScope", "/injectStyles", "/disableRuntimeStyles"], "repository": {"type": "git", "url": "https://github.com/vanilla-extract-css/vanilla-extract.git", "directory": "packages/css"}, "author": "SEEK", "license": "MIT", "dependencies": {"@emotion/hash": "^0.9.0", "css-what": "^6.1.0", "cssesc": "^3.0.0", "csstype": "^3.0.7", "dedent": "^1.5.3", "deep-object-diff": "^1.1.9", "deepmerge": "^4.2.2", "lru-cache": "^10.4.3", "media-query-parser": "^2.0.2", "modern-ahocorasick": "^1.0.0", "picocolors": "^1.0.0", "@vanilla-extract/private": "^1.0.9"}, "devDependencies": {"@types/cssesc": "^3.0.0"}}