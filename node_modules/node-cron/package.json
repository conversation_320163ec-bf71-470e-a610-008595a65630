{"name": "node-cron", "version": "4.2.1", "description": "A Lightweight Task Scheduler for Node.js", "author": "<PERSON>", "license": "ISC", "homepage": "https://github.com/merencia/node-cron", "main": "./dist/cjs/node-cron.js", "module": "./dist/esm/node-cron.js", "type": "commonjs", "exports": {".": {"require": {"types": "./dist/cjs/node-cron.d.ts", "default": "./dist/cjs/node-cron.js"}, "import": {"types": "./dist/esm/node-cron.d.ts", "default": "./dist/esm/node-cron.js"}}}, "scripts": {"test": "c8 --reporter text --reporter=lcov --exclude '**/*.test.ts' mocha --recursive './src/**/*.test.ts'", "test:nocov": "mocha --recursive **/*.test.ts", "lint": "./node_modules/.bin/eslint ./src", "check": "npm run lint && npm test", "build:esm": "tsc -p tsconfig.esm.json", "build:cjs": "tsc -p tsconfig.cjs.json", "build": "npm run build:esm; npm run build:cjs", "prepublishOnly": "npm run build"}, "engines": {"node": ">=6.0.0"}, "files": ["dist/"], "repository": {"type": "git", "url": "git+https://github.com/merencia/node-cron.git"}, "keywords": ["cron", "scheduler", "schedule", "task", "job"], "bugs": {"url": "https://github.com/merencia/node-cron/issues"}, "devDependencies": {"@eslint/js": "^9.26.0", "@tsconfig/recommended": "^1.0.8", "@types/chai": "^5.2.1", "@types/expect": "^1.20.4", "@types/mocha": "^10.0.10", "@types/node": "^22.15.3", "@types/sinon": "^17.0.4", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "c8": "^10.1.3", "chai": "^5.2.0", "eslint": "^9.26.0", "eslint-plugin-mocha": "^10.5.0", "eslint-plugin-node": "^11.1.0", "globals": "^16.1.0", "mocha": "^11.1.0", "sinon": "^20.0.0", "tsup": "^8.4.0", "tsx": "^4.19.3", "typescript": "^5.8.3", "typescript-eslint": "^8.32.0"}, "c8": {"reporter": ["html", "text"], "exclude": ["src/test-assets/**"]}}