ramda: 367.12 KB (5.50%)
lodash: 255.69 KB (3.83%)
ramda-adjunct: 255.04 KB (3.82%)
autolinker: 203.32 KB (3.04%)
@swagger-api/apidom-ns-openapi-3-0: 192.35 KB (2.88%)
swagger-client: 181.4 KB (2.72%)
immutable: 139.01 KB (2.08%)
@swagger-api/apidom-ns-openapi-3-1: 133.71 KB (2.00%)
react-dom: 129.93 KB (1.95%)
remarkable: 125.56 KB (1.88%)
highlight.js: 111.85 KB (1.67%)
js-yaml: 105.01 KB (1.57%)
readable-stream: 96.66 KB (1.45%)
@swagger-api/apidom-reference: 84.47 KB (1.26%)
core-js-pure: 83.37 KB (1.25%)
dompurify: 60.6 KB (0.907%)
minim: 57.35 KB (0.859%)
buffer: 56.99 KB (0.853%)
@swagger-api/apidom-core: 54.82 KB (0.821%)
@swagger-api/apidom-ast: 52.36 KB (0.784%)
@swagger-api/apidom-ns-json-schema-draft-4: 52.02 KB (0.779%)
react-syntax-highlighter: 40.21 KB (0.602%)
short-unique-id: 37.48 KB (0.561%)
apg-lite: 37.34 KB (0.559%)
react-redux: 36.62 KB (0.548%)
@swaggerexpert/cookie: 35.16 KB (0.526%)
@swagger-api/apidom-ns-json-schema-2019-09: 34.97 KB (0.524%)
fast-json-patch: 31.89 KB (0.478%)
@swaggerexpert/json-pointer: 30.48 KB (0.456%)
@swagger-api/apidom-ns-json-schema-draft-7: 23.54 KB (0.353%)
@swagger-api/apidom-ns-json-schema-2020-12: 22.33 KB (0.334%)
@swagger-api/apidom-ns-json-schema-draft-6: 21.56 KB (0.323%)
reselect: 21.47 KB (0.321%)
openapi-path-templating: 20.81 KB (0.312%)
neotraverse: 20.33 KB (0.304%)
sha.js: 18.57 KB (0.278%)
ts-mixer: 17.56 KB (0.263%)
tslib: 17.23 KB (0.258%)
redux: 16.37 KB (0.245%)
url-parse: 16.23 KB (0.243%)
events: 14.54 KB (0.218%)
openapi-server-url-templating: 14.38 KB (0.215%)
zenscroll: 12.31 KB (0.184%)
react-debounce-input: 11.95 KB (0.179%)
react-immutable-proptypes: 11.82 KB (0.177%)
ret: 10.82 KB (0.162%)
lodash.debounce: 10.53 KB (0.158%)
unraw: 9.9 KB (0.148%)
string_decoder: 9.24 KB (0.138%)
xml: 7.39 KB (0.111%)
react-copy-to-clipboard: 7.33 KB (0.110%)
react: 6.95 KB (0.104%)
randexp: 6.15 KB (0.0920%)
react-immutable-pure-component: 6.01 KB (0.0899%)
redux-immutable: 5.43 KB (0.0813%)
process: 5.29 KB (0.0792%)
drange: 4.8 KB (0.0719%)
lowlight: 4.42 KB (0.0662%)
scheduler: 4.33 KB (0.0648%)
deep-extend: 4.19 KB (0.0628%)
@babel/runtime: 3.99 KB (0.0598%)
deepmerge: 3.95 KB (0.0592%)
base64-js: 3.84 KB (0.0575%)
stream-browserify: 3.76 KB (0.0563%)
@swagger-api/apidom-error: 3.45 KB (0.0516%)
copy-to-clipboard: 3.29 KB (0.0492%)
format: 3.26 KB (0.0488%)
css.escape: 3.08 KB (0.0461%)
serialize-error: 2.93 KB (0.0438%)
use-sync-external-store: 2.81 KB (0.0421%)
prop-types: 2.6 KB (0.0389%)
querystringify: 2.5 KB (0.0375%)
xml-but-prettier: 2.17 KB (0.0324%)
ieee754: 2.1 KB (0.0315%)
safe-buffer: 1.63 KB (0.0244%)
util-deprecate: 1.58 KB (0.0236%)
randombytes: 1.54 KB (0.0231%)
js-file-download: 1.52 KB (0.0227%)
classnames: 1.49 KB (0.0224%)
repeat-string: 1.18 KB (0.0177%)
@babel/runtime-corejs3: 781 B (0.0114%)
toggle-selection: 780 B (0.0114%)
requires-port: 753 B (0.0110%)
inherits: 753 B (0.0110%)
@swagger-api/apidom-json-pointer: 709 B (0.0104%)
fault: 691 B (0.0101%)
<self>: 3.08 MB (47.2%)
