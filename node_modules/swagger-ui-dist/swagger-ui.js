!function webpackUniversalModuleDefinition(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.SwaggerUICore=t():e.SwaggerUICore=t()}(this,(()=>(()=>{"use strict";var e={158:e=>{e.exports=require("buffer")}},t={};function __webpack_require__(r){var a=t[r];if(void 0!==a)return a.exports;var n=t[r]={exports:{}};return e[r](n,n.exports,__webpack_require__),n.exports}__webpack_require__.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return __webpack_require__.d(t,{a:t}),t},__webpack_require__.d=(e,t)=>{for(var r in t)__webpack_require__.o(t,r)&&!__webpack_require__.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},__webpack_require__.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),__webpack_require__.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})};var r={};__webpack_require__.d(r,{default:()=>To});var a={};__webpack_require__.r(a),__webpack_require__.d(a,{CLEAR:()=>D,CLEAR_BY:()=>U,NEW_AUTH_ERR:()=>L,NEW_SPEC_ERR:()=>$,NEW_SPEC_ERR_BATCH:()=>V,NEW_THROWN_ERR:()=>R,NEW_THROWN_ERR_BATCH:()=>J,clear:()=>clear,clearBy:()=>clearBy,newAuthErr:()=>newAuthErr,newSpecErr:()=>newSpecErr,newSpecErrBatch:()=>newSpecErrBatch,newThrownErr:()=>newThrownErr,newThrownErrBatch:()=>newThrownErrBatch});var n={};__webpack_require__.r(n),__webpack_require__.d(n,{AUTHORIZE:()=>he,AUTHORIZE_OAUTH2:()=>ye,CONFIGURE_AUTH:()=>fe,LOGOUT:()=>ge,RESTORE_AUTHORIZATION:()=>Se,SHOW_AUTH_POPUP:()=>de,authPopup:()=>authPopup,authorize:()=>authorize,authorizeAccessCodeWithBasicAuthentication:()=>authorizeAccessCodeWithBasicAuthentication,authorizeAccessCodeWithFormParams:()=>authorizeAccessCodeWithFormParams,authorizeApplication:()=>authorizeApplication,authorizeOauth2:()=>authorizeOauth2,authorizeOauth2WithPersistOption:()=>authorizeOauth2WithPersistOption,authorizePassword:()=>authorizePassword,authorizeRequest:()=>authorizeRequest,authorizeWithPersistOption:()=>authorizeWithPersistOption,configureAuth:()=>configureAuth,logout:()=>logout,logoutWithPersistOption:()=>logoutWithPersistOption,persistAuthorizationIfNeeded:()=>persistAuthorizationIfNeeded,preAuthorizeImplicit:()=>preAuthorizeImplicit,restoreAuthorization:()=>restoreAuthorization,showDefinitions:()=>showDefinitions});var s={};__webpack_require__.r(s),__webpack_require__.d(s,{authorized:()=>be,definitionsForRequirements:()=>definitionsForRequirements,definitionsToAuthorize:()=>we,getConfigs:()=>Ce,getDefinitionsByNames:()=>getDefinitionsByNames,isAuthorized:()=>isAuthorized,selectAuthPath:()=>selectAuthPath,shownDefinitions:()=>ve});var o={};__webpack_require__.r(o),__webpack_require__.d(o,{TOGGLE_CONFIGS:()=>Me,UPDATE_CONFIGS:()=>Pe,downloadConfig:()=>downloadConfig,getConfigByUrl:()=>getConfigByUrl,loaded:()=>actions_loaded,toggle:()=>toggle,update:()=>update});var l={};__webpack_require__.r(l),__webpack_require__.d(l,{get:()=>get});var c={};__webpack_require__.r(c),__webpack_require__.d(c,{transform:()=>transform});var i={};__webpack_require__.r(i),__webpack_require__.d(i,{transform:()=>parameter_oneof_transform});var p={};__webpack_require__.r(p),__webpack_require__.d(p,{allErrors:()=>Xe,lastError:()=>Ge});var m={};__webpack_require__.r(m),__webpack_require__.d(m,{SHOW:()=>rt,UPDATE_FILTER:()=>et,UPDATE_LAYOUT:()=>Ze,UPDATE_MODE:()=>tt,changeMode:()=>changeMode,show:()=>actions_show,updateFilter:()=>updateFilter,updateLayout:()=>updateLayout});var u={};__webpack_require__.r(u),__webpack_require__.d(u,{current:()=>current,currentFilter:()=>currentFilter,isShown:()=>isShown,showSummary:()=>nt,whatMode:()=>whatMode});var d={};__webpack_require__.r(d),__webpack_require__.d(d,{taggedOperations:()=>taggedOperations});var h={};__webpack_require__.r(h),__webpack_require__.d(h,{getActiveLanguage:()=>lt,getDefaultExpanded:()=>ct,getGenerators:()=>ot,getSnippetGenerators:()=>getSnippetGenerators});var g={};__webpack_require__.r(g),__webpack_require__.d(g,{JsonSchemaArrayItemFile:()=>JsonSchemaArrayItemFile,JsonSchemaArrayItemText:()=>JsonSchemaArrayItemText,JsonSchemaForm:()=>JsonSchemaForm,JsonSchema_array:()=>JsonSchema_array,JsonSchema_boolean:()=>JsonSchema_boolean,JsonSchema_object:()=>JsonSchema_object,JsonSchema_string:()=>JsonSchema_string});var y={};__webpack_require__.r(y),__webpack_require__.d(y,{allowTryItOutFor:()=>allowTryItOutFor,basePath:()=>cr,canExecuteScheme:()=>canExecuteScheme,consumes:()=>ar,consumesOptionsFor:()=>consumesOptionsFor,contentTypeValues:()=>contentTypeValues,currentProducesFor:()=>currentProducesFor,definitions:()=>lr,externalDocs:()=>Yt,findDefinition:()=>findDefinition,getOAS3RequiredRequestBodyContentType:()=>getOAS3RequiredRequestBodyContentType,getParameter:()=>getParameter,hasHost:()=>fr,host:()=>ir,info:()=>Gt,isMediaTypeSchemaPropertiesEqual:()=>isMediaTypeSchemaPropertiesEqual,isOAS3:()=>Xt,lastError:()=>Dt,mutatedRequestFor:()=>mutatedRequestFor,mutatedRequests:()=>yr,operationScheme:()=>operationScheme,operationWithMeta:()=>operationWithMeta,operations:()=>rr,operationsWithRootInherited:()=>mr,operationsWithTags:()=>dr,parameterInclusionSettingFor:()=>parameterInclusionSettingFor,parameterValues:()=>parameterValues,parameterWithMeta:()=>parameterWithMeta,parameterWithMetaByIdentity:()=>parameterWithMetaByIdentity,parametersIncludeIn:()=>parametersIncludeIn,parametersIncludeType:()=>parametersIncludeType,paths:()=>er,produces:()=>nr,producesOptionsFor:()=>producesOptionsFor,requestFor:()=>requestFor,requests:()=>gr,responseFor:()=>responseFor,responses:()=>hr,schemes:()=>pr,security:()=>sr,securityDefinitions:()=>or,semver:()=>Zt,spec:()=>spec,specJS:()=>Ft,specJson:()=>Bt,specJsonWithResolvedSubtrees:()=>Ht,specResolved:()=>Wt,specResolvedSubtree:()=>specResolvedSubtree,specSource:()=>zt,specStr:()=>Kt,tagDetails:()=>tagDetails,taggedOperations:()=>selectors_taggedOperations,tags:()=>ur,url:()=>Ut,validOperationMethods:()=>tr,validateBeforeExecute:()=>validateBeforeExecute,validationErrors:()=>validationErrors,version:()=>Qt});var f={};__webpack_require__.r(f),__webpack_require__.d(f,{CLEAR_REQUEST:()=>Jr,CLEAR_RESPONSE:()=>Rr,CLEAR_VALIDATE_PARAMS:()=>$r,LOG_REQUEST:()=>Tr,SET_MUTATED_REQUEST:()=>Mr,SET_REQUEST:()=>Pr,SET_RESPONSE:()=>qr,SET_SCHEME:()=>Ur,UPDATE_EMPTY_PARAM_INCLUSION:()=>Ir,UPDATE_JSON:()=>kr,UPDATE_OPERATION_META_VALUE:()=>Vr,UPDATE_PARAM:()=>Ar,UPDATE_RESOLVED:()=>Lr,UPDATE_RESOLVED_SUBTREE:()=>Dr,UPDATE_SPEC:()=>Or,UPDATE_URL:()=>Nr,VALIDATE_PARAMS:()=>jr,changeConsumesValue:()=>changeConsumesValue,changeParam:()=>changeParam,changeParamByIdentity:()=>changeParamByIdentity,changeProducesValue:()=>changeProducesValue,clearRequest:()=>clearRequest,clearResponse:()=>clearResponse,clearValidateParams:()=>clearValidateParams,execute:()=>actions_execute,executeRequest:()=>executeRequest,invalidateResolvedSubtreeCache:()=>invalidateResolvedSubtreeCache,logRequest:()=>logRequest,parseToJson:()=>parseToJson,requestResolvedSubtree:()=>requestResolvedSubtree,resolveSpec:()=>resolveSpec,setMutatedRequest:()=>setMutatedRequest,setRequest:()=>setRequest,setResponse:()=>setResponse,setScheme:()=>setScheme,updateEmptyParamInclusion:()=>updateEmptyParamInclusion,updateJsonSpec:()=>updateJsonSpec,updateResolved:()=>updateResolved,updateResolvedSubtree:()=>updateResolvedSubtree,updateSpec:()=>updateSpec,updateUrl:()=>updateUrl,validateParams:()=>validateParams});var S={};__webpack_require__.r(S),__webpack_require__.d(S,{executeRequest:()=>wrap_actions_executeRequest,updateJsonSpec:()=>wrap_actions_updateJsonSpec,updateSpec:()=>wrap_actions_updateSpec,validateParams:()=>wrap_actions_validateParams});var E={};__webpack_require__.r(E),__webpack_require__.d(E,{Button:()=>Button,Col:()=>Col,Collapse:()=>Collapse,Container:()=>Container,Input:()=>Input,Link:()=>Link,Row:()=>Row,Select:()=>Select,TextArea:()=>TextArea});var _={};__webpack_require__.r(_),__webpack_require__.d(_,{basePath:()=>wn,consumes:()=>bn,definitions:()=>Sn,findDefinition:()=>fn,hasHost:()=>En,host:()=>vn,produces:()=>Cn,schemes:()=>xn,securityDefinitions:()=>_n,validOperationMethods:()=>wrap_selectors_validOperationMethods});var v={};__webpack_require__.r(v),__webpack_require__.d(v,{definitionsToAuthorize:()=>On});var w={};__webpack_require__.r(w),__webpack_require__.d(w,{callbacksOperations:()=>An,findSchema:()=>findSchema,isOAS3:()=>selectors_isOAS3,isOAS30:()=>selectors_isOAS30,isSwagger2:()=>selectors_isSwagger2,servers:()=>kn});var b={};__webpack_require__.r(b),__webpack_require__.d(b,{CLEAR_REQUEST_BODY_VALIDATE_ERROR:()=>Xn,CLEAR_REQUEST_BODY_VALUE:()=>Gn,SET_REQUEST_BODY_VALIDATE_ERROR:()=>Hn,UPDATE_ACTIVE_EXAMPLES_MEMBER:()=>zn,UPDATE_REQUEST_BODY_INCLUSION:()=>Kn,UPDATE_REQUEST_BODY_VALUE:()=>Dn,UPDATE_REQUEST_BODY_VALUE_RETAIN_FLAG:()=>Un,UPDATE_REQUEST_CONTENT_TYPE:()=>Bn,UPDATE_RESPONSE_CONTENT_TYPE:()=>Fn,UPDATE_SELECTED_SERVER:()=>Ln,UPDATE_SERVER_VARIABLE_VALUE:()=>Wn,clearRequestBodyValidateError:()=>clearRequestBodyValidateError,clearRequestBodyValue:()=>clearRequestBodyValue,initRequestBodyValidateError:()=>initRequestBodyValidateError,setActiveExamplesMember:()=>setActiveExamplesMember,setRequestBodyInclusion:()=>setRequestBodyInclusion,setRequestBodyValidateError:()=>setRequestBodyValidateError,setRequestBodyValue:()=>setRequestBodyValue,setRequestContentType:()=>setRequestContentType,setResponseContentType:()=>setResponseContentType,setRetainRequestBodyValueFlag:()=>setRetainRequestBodyValueFlag,setSelectedServer:()=>setSelectedServer,setServerVariableValue:()=>setServerVariableValue});var C={};__webpack_require__.r(C),__webpack_require__.d(C,{activeExamplesMember:()=>ss,hasUserEditedBody:()=>rs,requestBodyErrors:()=>ns,requestBodyInclusionSetting:()=>as,requestBodyValue:()=>es,requestContentType:()=>os,responseContentType:()=>ls,selectDefaultRequestBodyValue:()=>selectDefaultRequestBodyValue,selectedServer:()=>Zn,serverEffectiveValue:()=>ps,serverVariableValue:()=>cs,serverVariables:()=>is,shouldRetainRequestBodyValue:()=>ts,validOperationMethods:()=>us,validateBeforeExecute:()=>ms,validateShallowRequired:()=>validateShallowRequired});const x=require("react");var O=__webpack_require__.n(x);const N=require("redux"),k=require("immutable");var A=__webpack_require__.n(k);const I=require("deep-extend");var j=__webpack_require__.n(I);const q=require("redux-immutable"),P=require("serialize-error"),M=require("lodash/merge");var T=__webpack_require__.n(M);const R="err_new_thrown_err",J="err_new_thrown_err_batch",$="err_new_spec_err",V="err_new_spec_err_batch",L="err_new_auth_err",D="err_clear",U="err_clear_by";function newThrownErr(e){return{type:R,payload:(0,P.serializeError)(e)}}function newThrownErrBatch(e){return{type:J,payload:e}}function newSpecErr(e){return{type:$,payload:e}}function newSpecErrBatch(e){return{type:V,payload:e}}function newAuthErr(e){return{type:L,payload:e}}function clear(e={}){return{type:D,payload:e}}function clearBy(e=()=>!0){return{type:U,payload:e}}const K=function makeWindow(){var e={location:{},history:{},open:()=>{},close:()=>{},File:function(){},FormData:function(){}};if("undefined"==typeof window)return e;try{e=window;for(var t of["File","Blob","FormData"])t in window&&(e[t]=window[t])}catch(e){console.error(e)}return e}(),z=(require("lodash/camelCase"),require("lodash/upperFirst"),require("lodash/memoize"));var B=__webpack_require__.n(z);const F=require("lodash/find");var W=__webpack_require__.n(F);const H=require("lodash/some");var X=__webpack_require__.n(H);const G=require("lodash/eq");var Y=__webpack_require__.n(G);const Q=require("lodash/isFunction");var Z=__webpack_require__.n(Q);const ee=require("css.escape");var te=__webpack_require__.n(ee);const re=require("randombytes");var ae=__webpack_require__.n(re);const ne=require("sha.js");var se=__webpack_require__.n(ne);const oe=A().Set.of("type","format","items","default","maximum","exclusiveMaximum","minimum","exclusiveMinimum","maxLength","minLength","pattern","maxItems","minItems","uniqueItems","enum","multipleOf");function getParameterSchema(e,{isOAS3:t}={}){if(!A().Map.isMap(e))return{schema:A().Map(),parameterContentMediaType:null};if(!t)return"body"===e.get("in")?{schema:e.get("schema",A().Map()),parameterContentMediaType:null}:{schema:e.filter(((e,t)=>oe.includes(t))),parameterContentMediaType:null};if(e.get("content")){const t=e.get("content",A().Map({})).keySeq().first();return{schema:e.getIn(["content",t,"schema"],A().Map()),parameterContentMediaType:t}}return{schema:e.get("schema")?e.get("schema",A().Map()):A().Map(),parameterContentMediaType:null}}var le=__webpack_require__(158).Buffer;const ce="default",isImmutable=e=>A().Iterable.isIterable(e),immutableToJS=e=>isImmutable(e)?e.toJS():e;function objectify(e){return isObject(e)?immutableToJS(e):{}}function fromJSOrdered(e){if(isImmutable(e))return e;if(e instanceof K.File)return e;if(!isObject(e))return e;if(Array.isArray(e))return A().Seq(e).map(fromJSOrdered).toList();if(Z()(e.entries)){const t=function createObjWithHashedKeys(e){if(!Z()(e.entries))return e;const t={},r="_**[]",a={};for(let n of e.entries())if(t[n[0]]||a[n[0]]&&a[n[0]].containsMultiple){if(!a[n[0]]){a[n[0]]={containsMultiple:!0,length:1},t[`${n[0]}${r}${a[n[0]].length}`]=t[n[0]],delete t[n[0]]}a[n[0]].length+=1,t[`${n[0]}${r}${a[n[0]].length}`]=n[1]}else t[n[0]]=n[1];return t}(e);return A().OrderedMap(t).map(fromJSOrdered)}return A().OrderedMap(e).map(fromJSOrdered)}function normalizeArray(e){return Array.isArray(e)?e:[e]}function isFn(e){return"function"==typeof e}function isObject(e){return!!e&&"object"==typeof e}function isFunc(e){return"function"==typeof e}function isArray(e){return Array.isArray(e)}const ie=B();function objMap(e,t){return Object.keys(e).reduce(((r,a)=>(r[a]=t(e[a],a),r)),{})}function objReduce(e,t){return Object.keys(e).reduce(((r,a)=>{let n=t(e[a],a);return n&&"object"==typeof n&&Object.assign(r,n),r}),{})}function systemThunkMiddleware(e){return({dispatch:t,getState:r})=>t=>r=>"function"==typeof r?r(e()):t(r)}function validateValueBySchema(e,t,r,a,n){if(!t)return[];let s=[],o=t.get("nullable"),l=t.get("required"),c=t.get("maximum"),i=t.get("minimum"),p=t.get("type"),m=t.get("format"),u=t.get("maxLength"),d=t.get("minLength"),h=t.get("uniqueItems"),g=t.get("maxItems"),y=t.get("minItems"),f=t.get("pattern");const S=r||!0===l,E=null!=e,_=S||E&&"array"===p||!(!S&&!E),v=o&&null===e;if(S&&!E&&!v&&!a&&!p)return s.push("Required field is not provided"),s;if(v||!p||!_)return[];let w="string"===p&&e,b="array"===p&&Array.isArray(e)&&e.length,C="array"===p&&A().List.isList(e)&&e.count();const x=[w,b,C,"array"===p&&"string"==typeof e&&e,"file"===p&&e instanceof K.File,"boolean"===p&&(e||!1===e),"number"===p&&(e||0===e),"integer"===p&&(e||0===e),"object"===p&&"object"==typeof e&&null!==e,"object"===p&&"string"==typeof e&&e].some((e=>!!e));if(S&&!x&&!a)return s.push("Required field is not provided"),s;if("object"===p&&(null===n||"application/json"===n)){let r=e;if("string"==typeof e)try{r=JSON.parse(e)}catch(e){return s.push("Parameter string value must be valid JSON"),s}t&&t.has("required")&&isFunc(l.isList)&&l.isList()&&l.forEach((e=>{void 0===r[e]&&s.push({propKey:e,error:"Required property not found"})})),t&&t.has("properties")&&t.get("properties").forEach(((e,t)=>{const o=validateValueBySchema(r[t],e,!1,a,n);s.push(...o.map((e=>({propKey:t,error:e}))))}))}if(f){let t=((e,t)=>{if(!new RegExp(t).test(e))return"Value must follow pattern "+t})(e,f);t&&s.push(t)}if(y&&"array"===p){let t=((e,t)=>{if(!e&&t>=1||e&&e.length<t)return`Array must contain at least ${t} item${1===t?"":"s"}`})(e,y);t&&s.push(t)}if(g&&"array"===p){let t=((e,t)=>{if(e&&e.length>t)return`Array must not contain more then ${t} item${1===t?"":"s"}`})(e,g);t&&s.push({needRemove:!0,error:t})}if(h&&"array"===p){let t=((e,t)=>{if(e&&("true"===t||!0===t)){const t=(0,k.fromJS)(e),r=t.toSet();if(e.length>r.size){let e=(0,k.Set)();if(t.forEach(((r,a)=>{t.filter((e=>isFunc(e.equals)?e.equals(r):e===r)).size>1&&(e=e.add(a))})),0!==e.size)return e.map((e=>({index:e,error:"No duplicates allowed."}))).toArray()}}})(e,h);t&&s.push(...t)}if(u||0===u){let t=((e,t)=>{if(e.length>t)return`Value must be no longer than ${t} character${1!==t?"s":""}`})(e,u);t&&s.push(t)}if(d){let t=((e,t)=>{if(e.length<t)return`Value must be at least ${t} character${1!==t?"s":""}`})(e,d);t&&s.push(t)}if(c||0===c){let t=((e,t)=>{if(e>t)return`Value must be less than or equal to ${t}`})(e,c);t&&s.push(t)}if(i||0===i){let t=((e,t)=>{if(e<t)return`Value must be greater than or equal to ${t}`})(e,i);t&&s.push(t)}if("string"===p){let t;if(t="date-time"===m?(e=>{if(isNaN(Date.parse(e)))return"Value must be a DateTime"})(e):"uuid"===m?(e=>{if(e=e.toString().toLowerCase(),!/^[{(]?[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}[)}]?$/.test(e))return"Value must be a Guid"})(e):(e=>{if(e&&"string"!=typeof e)return"Value must be a string"})(e),!t)return s;s.push(t)}else if("boolean"===p){let t=(e=>{if("true"!==e&&"false"!==e&&!0!==e&&!1!==e)return"Value must be a boolean"})(e);if(!t)return s;s.push(t)}else if("number"===p){let t=(e=>{if(!/^-?\d+(\.?\d+)?$/.test(e))return"Value must be a number"})(e);if(!t)return s;s.push(t)}else if("integer"===p){let t=(e=>{if(!/^-?\d+$/.test(e))return"Value must be an integer"})(e);if(!t)return s;s.push(t)}else if("array"===p){if(!b&&!C)return s;e&&e.forEach(((e,r)=>{const o=validateValueBySchema(e,t.get("items"),!1,a,n);s.push(...o.map((e=>({index:r,error:e}))))}))}else if("file"===p){let t=(e=>{if(e&&!(e instanceof K.File))return"Value must be a file"})(e);if(!t)return s;s.push(t)}return s}const btoa=e=>{let t;return t=e instanceof le?e:le.from(e.toString(),"utf-8"),t.toString("base64")},pe={operationsSorter:{alpha:(e,t)=>e.get("path").localeCompare(t.get("path")),method:(e,t)=>e.get("method").localeCompare(t.get("method"))},tagsSorter:{alpha:(e,t)=>e.localeCompare(t)}},buildFormData=e=>{let t=[];for(let r in e){let a=e[r];void 0!==a&&""!==a&&t.push([r,"=",encodeURIComponent(a).replace(/%20/g,"+")].join(""))}return t.join("&")},shallowEqualKeys=(e,t,r)=>!!W()(r,(r=>Y()(e[r],t[r])));function requiresValidationURL(e){return!(!e||e.indexOf("localhost")>=0||e.indexOf("127.0.0.1")>=0||"none"===e)}const createDeepLinkPath=e=>"string"==typeof e||e instanceof String?e.trim().replace(/\s/g,"%20"):"",escapeDeepLinkPath=e=>te()(createDeepLinkPath(e).replace(/%20/g,"_")),getExtensions=e=>{const t=/^x-/;return k.Map.isMap(e)?e.filter(((e,r)=>t.test(r))):Object.keys(e).filter((e=>t.test(e)))},getCommonExtensions=e=>e.filter(((e,t)=>/^pattern|maxLength|minLength|maximum|minimum/.test(t)));function deeplyStripKey(e,t,r=()=>!0){if("object"!=typeof e||Array.isArray(e)||null===e||!t)return e;const a=Object.assign({},e);return Object.keys(a).forEach((e=>{e===t&&r(a[e],e)?delete a[e]:a[e]=deeplyStripKey(a[e],t,r)})),a}function stringify(e){if("string"==typeof e)return e;if(e&&e.toJS&&(e=e.toJS()),"object"==typeof e&&null!==e)try{return JSON.stringify(e,null,2)}catch(t){return String(e)}return null==e?"":e.toString()}function paramToIdentifier(e,{returnAll:t=!1,allowHashes:r=!0}={}){if(!A().Map.isMap(e))throw new Error("paramToIdentifier: received a non-Im.Map parameter as input");const a=e.get("name"),n=e.get("in");let s=[];return e&&e.hashCode&&n&&a&&r&&s.push(`${n}.${a}.hash-${e.hashCode()}`),n&&a&&s.push(`${n}.${a}`),s.push(a),t?s:s[0]||""}function paramToValue(e,t){return paramToIdentifier(e,{returnAll:!0}).map((e=>t[e])).filter((e=>void 0!==e))[0]}function b64toB64UrlEncoded(e){return e.replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"")}const isEmptyValue=e=>!e||!(!isImmutable(e)||!e.isEmpty()),idFn=e=>e;class Store{constructor(e={}){j()(this,{state:{},plugins:[],system:{configs:{},fn:{},components:{},rootInjects:{},statePlugins:{}},boundSystem:{},toolbox:{}},e),this.getSystem=this._getSystem.bind(this),this.store=function configureStore(e,t,r){return function createStoreWithMiddleware(e,t,r){let a=[systemThunkMiddleware(r)];const n=K.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__||N.compose;return(0,N.createStore)(e,t,n((0,N.applyMiddleware)(...a)))}(e,t,r)}(idFn,(0,k.fromJS)(this.state),this.getSystem),this.buildSystem(!1),this.register(this.plugins)}getStore(){return this.store}register(e,t=!0){var r=combinePlugins(e,this.getSystem());systemExtend(this.system,r),t&&this.buildSystem();callAfterLoad.call(this.system,e,this.getSystem())&&this.buildSystem()}buildSystem(e=!0){let t=this.getStore().dispatch,r=this.getStore().getState;this.boundSystem=Object.assign({},this.getRootInjects(),this.getWrappedAndBoundActions(t),this.getWrappedAndBoundSelectors(r,this.getSystem),this.getStateThunks(r),this.getFn(),this.getConfigs()),e&&this.rebuildReducer()}_getSystem(){return this.boundSystem}getRootInjects(){return Object.assign({getSystem:this.getSystem,getStore:this.getStore.bind(this),getComponents:this.getComponents.bind(this),getState:this.getStore().getState,getConfigs:this._getConfigs.bind(this),Im:A(),React:O()},this.system.rootInjects||{})}_getConfigs(){return this.system.configs}getConfigs(){return{configs:this.system.configs}}setConfigs(e){this.system.configs=e}rebuildReducer(){this.store.replaceReducer(function buildReducer(e,t){return function allReducers(e,t){let r=Object.keys(e).reduce(((r,a)=>(r[a]=function makeReducer(e,t){return(r=new k.Map,a)=>{if(!e)return r;let n=e[a.type];if(n){const e=wrapWithTryCatch(n,t)(r,a);return null===e?r:e}return r}}(e[a],t),r)),{});if(!Object.keys(r).length)return idFn;return(0,q.combineReducers)(r)}(objMap(e,(e=>e.reducers)),t)}(this.system.statePlugins,this.getSystem))}getType(e){let t=e[0].toUpperCase()+e.slice(1);return objReduce(this.system.statePlugins,((r,a)=>{let n=r[e];if(n)return{[a+t]:n}}))}getSelectors(){return this.getType("selectors")}getActions(){return objMap(this.getType("actions"),(e=>objReduce(e,((e,t)=>{if(isFn(e))return{[t]:e}}))))}getWrappedAndBoundActions(e){return objMap(this.getBoundActions(e),((e,t)=>{let r=this.system.statePlugins[t.slice(0,-7)].wrapActions;return r?objMap(e,((e,t)=>{let a=r[t];return a?(Array.isArray(a)||(a=[a]),a.reduce(((e,t)=>{let newAction=(...r)=>t(e,this.getSystem())(...r);if(!isFn(newAction))throw new TypeError("wrapActions needs to return a function that returns a new function (ie the wrapped action)");return wrapWithTryCatch(newAction,this.getSystem)}),e||Function.prototype)):e})):e}))}getWrappedAndBoundSelectors(e,t){return objMap(this.getBoundSelectors(e,t),((t,r)=>{let a=[r.slice(0,-9)],n=this.system.statePlugins[a].wrapSelectors;return n?objMap(t,((t,r)=>{let s=n[r];return s?(Array.isArray(s)||(s=[s]),s.reduce(((t,r)=>{let wrappedSelector=(...n)=>r(t,this.getSystem())(e().getIn(a),...n);if(!isFn(wrappedSelector))throw new TypeError("wrapSelector needs to return a function that returns a new function (ie the wrapped action)");return wrappedSelector}),t||Function.prototype)):t})):t}))}getStates(e){return Object.keys(this.system.statePlugins).reduce(((t,r)=>(t[r]=e.get(r),t)),{})}getStateThunks(e){return Object.keys(this.system.statePlugins).reduce(((t,r)=>(t[r]=()=>e().get(r),t)),{})}getFn(){return{fn:this.system.fn}}getComponents(e){const t=this.system.components[e];return Array.isArray(t)?t.reduce(((e,t)=>t(e,this.getSystem()))):void 0!==e?this.system.components[e]:this.system.components}getBoundSelectors(e,t){return objMap(this.getSelectors(),((r,a)=>{let n=[a.slice(0,-9)];return objMap(r,(r=>(...a)=>{let s=wrapWithTryCatch(r,this.getSystem).apply(null,[e().getIn(n),...a]);return"function"==typeof s&&(s=wrapWithTryCatch(s,this.getSystem)(t())),s}))}))}getBoundActions(e){e=e||this.getStore().dispatch;const t=this.getActions(),process=e=>"function"!=typeof e?objMap(e,(e=>process(e))):(...t)=>{var r=null;try{r=e(...t)}catch(e){r={type:R,error:!0,payload:(0,P.serializeError)(e)}}finally{return r}};return objMap(t,(t=>(0,N.bindActionCreators)(process(t),e)))}getMapStateToProps(){return()=>Object.assign({},this.getSystem())}getMapDispatchToProps(e){return t=>j()({},this.getWrappedAndBoundActions(t),this.getFn(),e)}}function combinePlugins(e,t){return isObject(e)&&!isArray(e)?T()({},e):isFunc(e)?combinePlugins(e(t),t):isArray(e)?e.map((e=>combinePlugins(e,t))).reduce(systemExtend,{components:t.getComponents()}):{}}function callAfterLoad(e,t,{hasLoaded:r}={}){let a=r;return isObject(e)&&!isArray(e)&&"function"==typeof e.afterLoad&&(a=!0,wrapWithTryCatch(e.afterLoad,t.getSystem).call(this,t)),isFunc(e)?callAfterLoad.call(this,e(t),t,{hasLoaded:a}):isArray(e)?e.map((e=>callAfterLoad.call(this,e,t,{hasLoaded:a}))):a}function systemExtend(e={},t={}){if(!isObject(e))return{};if(!isObject(t))return e;t.wrapComponents&&(objMap(t.wrapComponents,((r,a)=>{const n=e.components&&e.components[a];n&&Array.isArray(n)?(e.components[a]=n.concat([r]),delete t.wrapComponents[a]):n&&(e.components[a]=[n,r],delete t.wrapComponents[a])})),Object.keys(t.wrapComponents).length||delete t.wrapComponents);const{statePlugins:r}=e;if(isObject(r))for(let e in r){const a=r[e];if(!isObject(a))continue;const{wrapActions:n,wrapSelectors:s}=a;if(isObject(n))for(let r in n){let a=n[r];Array.isArray(a)||(a=[a],n[r]=a),t&&t.statePlugins&&t.statePlugins[e]&&t.statePlugins[e].wrapActions&&t.statePlugins[e].wrapActions[r]&&(t.statePlugins[e].wrapActions[r]=n[r].concat(t.statePlugins[e].wrapActions[r]))}if(isObject(s))for(let r in s){let a=s[r];Array.isArray(a)||(a=[a],s[r]=a),t&&t.statePlugins&&t.statePlugins[e]&&t.statePlugins[e].wrapSelectors&&t.statePlugins[e].wrapSelectors[r]&&(t.statePlugins[e].wrapSelectors[r]=s[r].concat(t.statePlugins[e].wrapSelectors[r]))}}return j()(e,t)}function wrapWithTryCatch(e,t,{logErrors:r=!0}={}){return"function"!=typeof e?e:function(...a){try{return e.call(this,...a)}catch(e){if(r){const{uncaughtExceptionHandler:r}=t().getConfigs();"function"==typeof r?r(e):console.error(e)}return null}}}const me=require("url-parse");var ue=__webpack_require__.n(me);const de="show_popup",he="authorize",ge="logout",ye="authorize_oauth2",fe="configure_auth",Se="restore_authorization";function showDefinitions(e){return{type:de,payload:e}}function authorize(e){return{type:he,payload:e}}const authorizeWithPersistOption=e=>({authActions:t})=>{t.authorize(e),t.persistAuthorizationIfNeeded()};function logout(e){return{type:ge,payload:e}}const logoutWithPersistOption=e=>({authActions:t})=>{t.logout(e),t.persistAuthorizationIfNeeded()},preAuthorizeImplicit=e=>({authActions:t,errActions:r})=>{let{auth:a,token:n,isValid:s}=e,{schema:o,name:l}=a,c=o.get("flow");delete K.swaggerUIRedirectOauth2,"accessCode"===c||s||r.newAuthErr({authId:l,source:"auth",level:"warning",message:"Authorization may be unsafe, passed state was changed in server Passed state wasn't returned from auth server"}),n.error?r.newAuthErr({authId:l,source:"auth",level:"error",message:JSON.stringify(n)}):t.authorizeOauth2WithPersistOption({auth:a,token:n})};function authorizeOauth2(e){return{type:ye,payload:e}}const authorizeOauth2WithPersistOption=e=>({authActions:t})=>{t.authorizeOauth2(e),t.persistAuthorizationIfNeeded()},authorizePassword=e=>({authActions:t})=>{let{schema:r,name:a,username:n,password:s,passwordType:o,clientId:l,clientSecret:c}=e,i={grant_type:"password",scope:e.scopes.join(" "),username:n,password:s},p={};switch(o){case"request-body":!function setClientIdAndSecret(e,t,r){t&&Object.assign(e,{client_id:t});r&&Object.assign(e,{client_secret:r})}(i,l,c);break;case"basic":p.Authorization="Basic "+btoa(l+":"+c);break;default:console.warn(`Warning: invalid passwordType ${o} was passed, not including client id and secret`)}return t.authorizeRequest({body:buildFormData(i),url:r.get("tokenUrl"),name:a,headers:p,query:{},auth:e})};const authorizeApplication=e=>({authActions:t})=>{let{schema:r,scopes:a,name:n,clientId:s,clientSecret:o}=e,l={Authorization:"Basic "+btoa(s+":"+o)},c={grant_type:"client_credentials",scope:a.join(" ")};return t.authorizeRequest({body:buildFormData(c),name:n,url:r.get("tokenUrl"),auth:e,headers:l})},authorizeAccessCodeWithFormParams=({auth:e,redirectUrl:t})=>({authActions:r})=>{let{schema:a,name:n,clientId:s,clientSecret:o,codeVerifier:l}=e,c={grant_type:"authorization_code",code:e.code,client_id:s,client_secret:o,redirect_uri:t,code_verifier:l};return r.authorizeRequest({body:buildFormData(c),name:n,url:a.get("tokenUrl"),auth:e})},authorizeAccessCodeWithBasicAuthentication=({auth:e,redirectUrl:t})=>({authActions:r})=>{let{schema:a,name:n,clientId:s,clientSecret:o,codeVerifier:l}=e,c={Authorization:"Basic "+btoa(s+":"+o)},i={grant_type:"authorization_code",code:e.code,client_id:s,redirect_uri:t,code_verifier:l};return r.authorizeRequest({body:buildFormData(i),name:n,url:a.get("tokenUrl"),auth:e,headers:c})},authorizeRequest=e=>({fn:t,getConfigs:r,authActions:a,errActions:n,oas3Selectors:s,specSelectors:o,authSelectors:l})=>{let c,{body:i,query:p={},headers:m={},name:u,url:d,auth:h}=e,{additionalQueryStringParams:g}=l.getConfigs()||{};if(o.isOAS3()){let e=s.serverEffectiveValue(s.selectedServer());c=ue()(d,e,!0)}else c=ue()(d,o.url(),!0);"object"==typeof g&&(c.query=Object.assign({},c.query,g));const y=c.toString();let f=Object.assign({Accept:"application/json, text/plain, */*","Content-Type":"application/x-www-form-urlencoded","X-Requested-With":"XMLHttpRequest"},m);t.fetch({url:y,method:"post",headers:f,query:p,body:i,requestInterceptor:r().requestInterceptor,responseInterceptor:r().responseInterceptor}).then((function(e){let t=JSON.parse(e.data),r=t&&(t.error||""),s=t&&(t.parseError||"");e.ok?r||s?n.newAuthErr({authId:u,level:"error",source:"auth",message:JSON.stringify(t)}):a.authorizeOauth2WithPersistOption({auth:h,token:t}):n.newAuthErr({authId:u,level:"error",source:"auth",message:e.statusText})})).catch((e=>{let t=new Error(e).message;if(e.response&&e.response.data){const r=e.response.data;try{const e="string"==typeof r?JSON.parse(r):r;e.error&&(t+=`, error: ${e.error}`),e.error_description&&(t+=`, description: ${e.error_description}`)}catch(e){}}n.newAuthErr({authId:u,level:"error",source:"auth",message:t})}))};function configureAuth(e){return{type:fe,payload:e}}function restoreAuthorization(e){return{type:Se,payload:e}}const persistAuthorizationIfNeeded=()=>({authSelectors:e,getConfigs:t})=>{if(!t().persistAuthorization)return;const r=e.authorized().toJS();localStorage.setItem("authorized",JSON.stringify(r))},authPopup=(e,t)=>()=>{K.swaggerUIRedirectOauth2=t,K.open(e)},Ee={[de]:(e,{payload:t})=>e.set("showDefinitions",t),[he]:(e,{payload:t})=>{let r=(0,k.fromJS)(t),a=e.get("authorized")||(0,k.Map)();return r.entrySeq().forEach((([t,r])=>{if(!isFunc(r.getIn))return e.set("authorized",a);let n=r.getIn(["schema","type"]);if("apiKey"===n||"http"===n)a=a.set(t,r);else if("basic"===n){let e=r.getIn(["value","username"]),n=r.getIn(["value","password"]);a=a.setIn([t,"value"],{username:e,header:"Basic "+btoa(e+":"+n)}),a=a.setIn([t,"schema"],r.get("schema"))}})),e.set("authorized",a)},[ye]:(e,{payload:t})=>{let r,{auth:a,token:n}=t;a.token=Object.assign({},n),r=(0,k.fromJS)(a);let s=e.get("authorized")||(0,k.Map)();return s=s.set(r.get("name"),r),e.set("authorized",s)},[ge]:(e,{payload:t})=>{let r=e.get("authorized").withMutations((e=>{t.forEach((t=>{e.delete(t)}))}));return e.set("authorized",r)},[fe]:(e,{payload:t})=>e.set("configs",t),[Se]:(e,{payload:t})=>e.set("authorized",(0,k.fromJS)(t.authorized))},_e=require("reselect"),state=e=>e,ve=(0,_e.createSelector)(state,(e=>e.get("showDefinitions"))),we=(0,_e.createSelector)(state,(()=>({specSelectors:e})=>{let t=e.securityDefinitions()||(0,k.Map)({}),r=(0,k.List)();return t.entrySeq().forEach((([e,t])=>{let a=(0,k.Map)();a=a.set(e,t),r=r.push(a)})),r})),selectAuthPath=(e,t)=>({specSelectors:e})=>(0,k.List)(e.isOAS3()?["components","securitySchemes",t]:["securityDefinitions",t]),getDefinitionsByNames=(e,t)=>({specSelectors:e})=>{console.warn("WARNING: getDefinitionsByNames is deprecated and will be removed in the next major version.");let r=e.securityDefinitions(),a=(0,k.List)();return t.valueSeq().forEach((e=>{let t=(0,k.Map)();e.entrySeq().forEach((([e,a])=>{let n,s=r.get(e);"oauth2"===s.get("type")&&a.size&&(n=s.get("scopes"),n.keySeq().forEach((e=>{a.contains(e)||(n=n.delete(e))})),s=s.set("allowedScopes",n)),t=t.set(e,s)})),a=a.push(t)})),a},definitionsForRequirements=(e,t=(0,k.List)())=>({authSelectors:e})=>{const r=e.definitionsToAuthorize()||(0,k.List)();let a=(0,k.List)();return r.forEach((e=>{let r=t.find((t=>t.get(e.keySeq().first())));r&&(e.forEach(((t,a)=>{if("oauth2"===t.get("type")){const n=r.get(a);let s=t.get("scopes");k.List.isList(n)&&k.Map.isMap(s)&&(s.keySeq().forEach((e=>{n.contains(e)||(s=s.delete(e))})),e=e.set(a,t.set("scopes",s)))}})),a=a.push(e))})),a},be=(0,_e.createSelector)(state,(e=>e.get("authorized")||(0,k.Map)())),isAuthorized=(e,t)=>({authSelectors:e})=>{let r=e.authorized();return k.List.isList(t)?!!t.toJS().filter((e=>-1===Object.keys(e).map((e=>!!r.get(e))).indexOf(!1))).length:null},Ce=(0,_e.createSelector)(state,(e=>e.get("configs"))),execute=(e,{authSelectors:t,specSelectors:r})=>({path:a,method:n,operation:s,extras:o})=>{let l={authorized:t.authorized()&&t.authorized().toJS(),definitions:r.securityDefinitions()&&r.securityDefinitions().toJS(),specSecurity:r.security()&&r.security().toJS()};return e({path:a,method:n,operation:s,securities:l,...o})},loaded=(e,t)=>r=>{const{getConfigs:a,authActions:n}=t,s=a();if(e(r),s.persistAuthorization){const e=localStorage.getItem("authorized");e&&n.restoreAuthorization({authorized:JSON.parse(e)})}},wrap_actions_authorize=(e,t)=>r=>{e(r);if(t.getConfigs().persistAuthorization)try{const[{schema:e,value:t}]=Object.values(r),a="apiKey"===e.get("type"),n="cookie"===e.get("in");a&&n&&(document.cookie=`${e.get("name")}=${t}; SameSite=None; Secure`)}catch(e){console.error("Error persisting cookie based apiKey in document.cookie.",e)}},wrap_actions_logout=(e,t)=>r=>{const a=t.getConfigs(),n=t.authSelectors.authorized();try{a.persistAuthorization&&Array.isArray(r)&&r.forEach((e=>{const t=n.get(e,{}),r="apiKey"===t.getIn(["schema","type"]),a="cookie"===t.getIn(["schema","in"]);if(r&&a){const e=t.getIn(["schema","name"]);document.cookie=`${e}=; Max-Age=-99999999`}}))}catch(e){console.error("Error deleting cookie based apiKey from document.cookie.",e)}e(r)},xe=require("prop-types");var Oe=__webpack_require__.n(xe);const Ne=require("lodash/omit");var ke=__webpack_require__.n(Ne);class LockAuthIcon extends O().Component{mapStateToProps(e,t){return{state:e,ownProps:ke()(t,Object.keys(t.getSystem()))}}render(){const{getComponent:e,ownProps:t}=this.props,r=e("LockIcon");return O().createElement(r,t)}}const Ae=LockAuthIcon;class UnlockAuthIcon extends O().Component{mapStateToProps(e,t){return{state:e,ownProps:ke()(t,Object.keys(t.getSystem()))}}render(){const{getComponent:e,ownProps:t}=this.props,r=e("UnlockIcon");return O().createElement(r,t)}}const Ie=UnlockAuthIcon;function auth(){return{afterLoad(e){this.rootInjects=this.rootInjects||{},this.rootInjects.initOAuth=e.authActions.configureAuth,this.rootInjects.preauthorizeApiKey=preauthorizeApiKey.bind(null,e),this.rootInjects.preauthorizeBasic=preauthorizeBasic.bind(null,e)},components:{LockAuthIcon:Ae,UnlockAuthIcon:Ie,LockAuthOperationIcon:Ae,UnlockAuthOperationIcon:Ie},statePlugins:{auth:{reducers:Ee,actions:n,selectors:s,wrapActions:{authorize:wrap_actions_authorize,logout:wrap_actions_logout}},configs:{wrapActions:{loaded}},spec:{wrapActions:{execute}}}}}function preauthorizeBasic(e,t,r,a){const{authActions:{authorize:n},specSelectors:{specJson:s,isOAS3:o}}=e,l=o()?["components","securitySchemes"]:["securityDefinitions"],c=s().getIn([...l,t]);return c?n({[t]:{value:{username:r,password:a},schema:c.toJS()}}):null}function preauthorizeApiKey(e,t,r){const{authActions:{authorize:a},specSelectors:{specJson:n,isOAS3:s}}=e,o=s()?["components","securitySchemes"]:["securityDefinitions"],l=n().getIn([...o,t]);return l?a({[t]:{value:r,schema:l.toJS()}}):null}const je=require("js-yaml");var qe=__webpack_require__.n(je);const Pe="configs_update",Me="configs_toggle";function update(e,t){return{type:Pe,payload:{[e]:t}}}function toggle(e){return{type:Me,payload:e}}const actions_loaded=()=>()=>{},downloadConfig=e=>t=>{const{fn:{fetch:r}}=t;return r(e)},getConfigByUrl=(e,t)=>r=>{const{specActions:a,configsActions:n}=r;if(e)return n.downloadConfig(e).then(next,next);function next(n){n instanceof Error||n.status>=400?(a.updateLoadingStatus("failedConfig"),a.updateLoadingStatus("failedConfig"),a.updateUrl(""),console.error(n.statusText+" "+e.url),t(null)):t(((e,t)=>{try{return qe().load(e)}catch(e){return t&&t.errActions.newThrownErr(new Error(e)),{}}})(n.text,r))}},get=(e,t)=>e.getIn(Array.isArray(t)?t:[t]),Te={[Pe]:(e,t)=>e.merge((0,k.fromJS)(t.payload)),[Me]:(e,t)=>{const r=t.payload,a=e.get(r);return e.set(r,!a)}};function configsPlugin(){return{statePlugins:{configs:{reducers:Te,actions:o,selectors:l}}}}const setHash=e=>e?history.pushState(null,null,`#${e}`):window.location.hash="",Re=require("zenscroll");var Je=__webpack_require__.n(Re);const $e="layout_scroll_to",Ve="layout_clear_scroll";const Le={fn:{getScrollParent:function getScrollParent(e,t){const r=document.documentElement;let a=getComputedStyle(e);const n="absolute"===a.position,s=t?/(auto|scroll|hidden)/:/(auto|scroll)/;if("fixed"===a.position)return r;for(let t=e;t=t.parentElement;)if(a=getComputedStyle(t),(!n||"static"!==a.position)&&s.test(a.overflow+a.overflowY+a.overflowX))return t;return r}},statePlugins:{layout:{actions:{scrollToElement:(e,t)=>r=>{try{t=t||r.fn.getScrollParent(e),Je().createScroller(t).to(e)}catch(e){console.error(e)}},scrollTo:e=>({type:$e,payload:Array.isArray(e)?e:[e]}),clearScrollTo:()=>({type:Ve}),readyToScroll:(e,t)=>r=>{const a=r.layoutSelectors.getScrollToKey();A().is(a,(0,k.fromJS)(e))&&(r.layoutActions.scrollToElement(t),r.layoutActions.clearScrollTo())},parseDeepLinkHash:e=>({layoutActions:t,layoutSelectors:r,getConfigs:a})=>{if(a().deepLinking&&e){let a=e.slice(1);"!"===a[0]&&(a=a.slice(1)),"/"===a[0]&&(a=a.slice(1));const n=a.split("/").map((e=>e||"")),s=r.isShownKeyFromUrlHashArray(n),[o,l="",c=""]=s;if("operations"===o){const e=r.isShownKeyFromUrlHashArray([l]);l.indexOf("_")>-1&&(console.warn("Warning: escaping deep link whitespace with `_` will be unsupported in v4.0, use `%20` instead."),t.show(e.map((e=>e.replace(/_/g," "))),!0)),t.show(e,!0)}(l.indexOf("_")>-1||c.indexOf("_")>-1)&&(console.warn("Warning: escaping deep link whitespace with `_` will be unsupported in v4.0, use `%20` instead."),t.show(s.map((e=>e.replace(/_/g," "))),!0)),t.show(s,!0),t.scrollTo(s)}}},selectors:{getScrollToKey:e=>e.get("scrollToKey"),isShownKeyFromUrlHashArray(e,t){const[r,a]=t;return a?["operations",r,a]:r?["operations-tag",r]:[]},urlHashArrayFromIsShownKey(e,t){let[r,a,n]=t;return"operations"==r?[a,n]:"operations-tag"==r?[a]:[]}},reducers:{[$e]:(e,t)=>e.set("scrollToKey",A().fromJS(t.payload)),[Ve]:e=>e.delete("scrollToKey")},wrapActions:{show:(e,{getConfigs:t,layoutSelectors:r})=>(...a)=>{if(e(...a),t().deepLinking)try{let[e,t]=a;e=Array.isArray(e)?e:[e];const n=r.urlHashArrayFromIsShownKey(e);if(!n.length)return;const[s,o]=n;if(!t)return setHash("/");2===n.length?setHash(createDeepLinkPath(`/${encodeURIComponent(s)}/${encodeURIComponent(o)}`)):1===n.length&&setHash(createDeepLinkPath(`/${encodeURIComponent(s)}`))}catch(e){console.error(e)}}}}}},De=require("react-immutable-proptypes");var Ue=__webpack_require__.n(De);const operation_wrapper=(e,t)=>class OperationWrapper extends O().Component{onLoad=e=>{const{operation:r}=this.props,{tag:a,operationId:n}=r.toObject();let{isShownKey:s}=r.toObject();s=s||["operations",a,n],t.layoutActions.readyToScroll(s,e)};render(){return O().createElement("span",{ref:this.onLoad},O().createElement(e,this.props))}},operation_tag_wrapper=(e,t)=>class OperationTagWrapper extends O().Component{onLoad=e=>{const{tag:r}=this.props,a=["operations-tag",r];t.layoutActions.readyToScroll(a,e)};render(){return O().createElement("span",{ref:this.onLoad},O().createElement(e,this.props))}};function deep_linking(){return[Le,{statePlugins:{configs:{wrapActions:{loaded:(e,t)=>(...r)=>{e(...r);const a=decodeURIComponent(window.location.hash);t.layoutActions.parseDeepLinkHash(a)}}}},wrapComponents:{operation:operation_wrapper,OperationTag:operation_tag_wrapper}}]}const Ke=require("lodash/reduce");var ze=__webpack_require__.n(Ke);function transform(e){return e.map((e=>{let t="is not of a type(s)",r=e.get("message").indexOf(t);if(r>-1){let t=e.get("message").slice(r+19).split(",");return e.set("message",e.get("message").slice(0,r)+function makeNewMessage(e){return e.reduce(((e,t,r,a)=>r===a.length-1&&a.length>1?e+"or "+t:a[r+1]&&a.length>2?e+t+", ":a[r+1]?e+t+" ":e+t),"should be a")}(t))}return e}))}const Be=require("lodash/get");var Fe=__webpack_require__.n(Be);function parameter_oneof_transform(e,{jsSpec:t}){return e}const We=[c,i];function transformErrors(e){let t={jsSpec:{}},r=ze()(We,((e,r)=>{try{return r.transform(e,t).filter((e=>!!e))}catch(t){return console.error("Transformer error:",t),e}}),e);return r.filter((e=>!!e)).map((e=>(!e.get("line")&&e.get("path"),e)))}let He={line:0,level:"error",message:"Unknown error"};const Xe=(0,_e.createSelector)((e=>e),(e=>e.get("errors",(0,k.List)()))),Ge=(0,_e.createSelector)(Xe,(e=>e.last()));function err(e){return{statePlugins:{err:{reducers:{[R]:(e,{payload:t})=>{let r=Object.assign(He,t,{type:"thrown"});return e.update("errors",(e=>(e||(0,k.List)()).push((0,k.fromJS)(r)))).update("errors",(e=>transformErrors(e)))},[J]:(e,{payload:t})=>(t=t.map((e=>(0,k.fromJS)(Object.assign(He,e,{type:"thrown"})))),e.update("errors",(e=>(e||(0,k.List)()).concat((0,k.fromJS)(t)))).update("errors",(e=>transformErrors(e)))),[$]:(e,{payload:t})=>{let r=(0,k.fromJS)(t);return r=r.set("type","spec"),e.update("errors",(e=>(e||(0,k.List)()).push((0,k.fromJS)(r)).sortBy((e=>e.get("line"))))).update("errors",(e=>transformErrors(e)))},[V]:(e,{payload:t})=>(t=t.map((e=>(0,k.fromJS)(Object.assign(He,e,{type:"spec"})))),e.update("errors",(e=>(e||(0,k.List)()).concat((0,k.fromJS)(t)))).update("errors",(e=>transformErrors(e)))),[L]:(e,{payload:t})=>{let r=(0,k.fromJS)(Object.assign({},t));return r=r.set("type","auth"),e.update("errors",(e=>(e||(0,k.List)()).push((0,k.fromJS)(r)))).update("errors",(e=>transformErrors(e)))},[D]:(e,{payload:t})=>{if(!t||!e.get("errors"))return e;let r=e.get("errors").filter((e=>e.keySeq().every((r=>{const a=e.get(r),n=t[r];return!n||a!==n}))));return e.merge({errors:r})},[U]:(e,{payload:t})=>{if(!t||"function"!=typeof t)return e;let r=e.get("errors").filter((e=>t(e)));return e.merge({errors:r})}},actions:a,selectors:p}}}}function opsFilter(e,t){return e.filter(((e,r)=>-1!==r.indexOf(t)))}function filter(){return{fn:{opsFilter}}}const Ye=require("@babel/runtime-corejs3/helpers/extends");var Qe=__webpack_require__.n(Ye);const arrow_up=({className:e=null,width:t=20,height:r=20,...a})=>O().createElement("svg",Qe()({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",className:e,width:t,height:r,"aria-hidden":"true",focusable:"false"},a),O().createElement("path",{d:"M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"})),arrow_down=({className:e=null,width:t=20,height:r=20,...a})=>O().createElement("svg",Qe()({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",className:e,width:t,height:r,"aria-hidden":"true",focusable:"false"},a),O().createElement("path",{d:"M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"})),arrow=({className:e=null,width:t=20,height:r=20,...a})=>O().createElement("svg",Qe()({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",className:e,width:t,height:r,"aria-hidden":"true",focusable:"false"},a),O().createElement("path",{d:"M13.25 10L6.109 2.58c-.268-.27-.268-.707 0-.979.268-.27.701-.27.969 0l7.83 7.908c.268.271.268.709 0 .979l-7.83 7.908c-.268.271-.701.27-.969 0-.268-.269-.268-.707 0-.979L13.25 10z"})),components_close=({className:e=null,width:t=20,height:r=20,...a})=>O().createElement("svg",Qe()({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",className:e,width:t,height:r,"aria-hidden":"true",focusable:"false"},a),O().createElement("path",{d:"M14.348 14.849c-.469.469-1.229.469-1.697 0L10 11.819l-2.651 3.029c-.469.469-1.229.469-1.697 0-.469-.469-.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-.469-.469-.469-1.228 0-1.697.469-.469 1.228-.469 1.697 0L10 8.183l2.651-3.031c.469-.469 1.228-.469 1.697 0 .469.469.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c.469.469.469 1.229 0 1.698z"})),copy=({className:e=null,width:t=15,height:r=16,...a})=>O().createElement("svg",Qe()({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 15 16",className:e,width:t,height:r,"aria-hidden":"true",focusable:"false"},a),O().createElement("g",{transform:"translate(2, -1)"},O().createElement("path",{fill:"#ffffff",fillRule:"evenodd",d:"M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"}))),lock=({className:e=null,width:t=20,height:r=20,...a})=>O().createElement("svg",Qe()({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",className:e,width:t,height:r,"aria-hidden":"true",focusable:"false"},a),O().createElement("path",{d:"M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8zM12 8H8V5.199C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8z"})),unlock=({className:e=null,width:t=20,height:r=20,...a})=>O().createElement("svg",Qe()({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",className:e,width:t,height:r,"aria-hidden":"true",focusable:"false"},a),O().createElement("path",{d:"M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"})),icons=()=>({components:{ArrowUpIcon:arrow_up,ArrowDownIcon:arrow_down,ArrowIcon:arrow,CloseIcon:components_close,CopyIcon:copy,LockIcon:lock,UnlockIcon:unlock}}),Ze="layout_update_layout",et="layout_update_filter",tt="layout_update_mode",rt="layout_show";function updateLayout(e){return{type:Ze,payload:e}}function updateFilter(e){return{type:et,payload:e}}function actions_show(e,t=!0){return e=normalizeArray(e),{type:rt,payload:{thing:e,shown:t}}}function changeMode(e,t=""){return e=normalizeArray(e),{type:tt,payload:{thing:e,mode:t}}}const at={[Ze]:(e,t)=>e.set("layout",t.payload),[et]:(e,t)=>e.set("filter",t.payload),[rt]:(e,t)=>{const r=t.payload.shown,a=(0,k.fromJS)(t.payload.thing);return e.update("shown",(0,k.fromJS)({}),(e=>e.set(a,r)))},[tt]:(e,t)=>{let r=t.payload.thing,a=t.payload.mode;return e.setIn(["modes"].concat(r),(a||"")+"")}},current=e=>e.get("layout"),currentFilter=e=>e.get("filter"),isShown=(e,t,r)=>(t=normalizeArray(t),e.get("shown",(0,k.fromJS)({})).get((0,k.fromJS)(t),r)),whatMode=(e,t,r="")=>(t=normalizeArray(t),e.getIn(["modes",...t],r)),nt=(0,_e.createSelector)((e=>e),(e=>!isShown(e,"editor"))),taggedOperations=(e,t)=>(r,...a)=>{let n=e(r,...a);const{fn:s,layoutSelectors:o,getConfigs:l}=t.getSystem(),c=l(),{maxDisplayedTags:i}=c;let p=o.currentFilter();return p&&!0!==p&&(n=s.opsFilter(n,p)),i>=0&&(n=n.slice(0,i)),n};function plugins_layout(){return{statePlugins:{layout:{reducers:at,actions:m,selectors:u},spec:{wrapSelectors:d}}}}function logs({configs:e}){const t={debug:0,info:1,log:2,warn:3,error:4},getLevel=e=>t[e]||-1;let{logLevel:r}=e,a=getLevel(r);function log(e,...t){getLevel(e)>=a&&console[e](...t)}return log.warn=log.bind(null,"warn"),log.error=log.bind(null,"error"),log.info=log.bind(null,"info"),log.debug=log.bind(null,"debug"),{rootInjects:{log}}}let st=!1;function on_complete(){return{statePlugins:{spec:{wrapActions:{updateSpec:e=>(...t)=>(st=!0,e(...t)),updateJsonSpec:(e,t)=>(...r)=>{const a=t.getConfigs().onComplete;return st&&"function"==typeof a&&(setTimeout(a,0),st=!1),e(...r)}}}}}}const extractKey=e=>{const t="_**[]";return e.indexOf(t)<0?e:e.split(t)[0].trim()},escapeShell=e=>"-d "===e||/^[_\/-]/g.test(e)?e:"'"+e.replace(/'/g,"'\\''")+"'",escapeCMD=e=>"-d "===(e=e.replace(/\^/g,"^^").replace(/\\"/g,'\\\\"').replace(/"/g,'""').replace(/\n/g,"^\n"))?e.replace(/-d /g,"-d ^\n"):/^[_\/-]/g.test(e)?e:'"'+e+'"',escapePowershell=e=>{if("-d "===e)return e;if(/\n/.test(e)){return`@"\n${e.replace(/`/g,"``").replace(/\$/g,"`$")}\n"@`}if(!/^[_\/-]/.test(e)){return`'${e.replace(/'/g,"''")}'`}return e};const curlify=(e,t,r,a="")=>{let n=!1,s="";const addWords=(...e)=>s+=" "+e.map(t).join(" "),addWordsWithoutLeadingSpace=(...e)=>s+=e.map(t).join(" "),addNewLine=()=>s+=` ${r}`,addIndent=(e=1)=>s+="  ".repeat(e);let o=e.get("headers");s+="curl"+a;const l=e.get("curlOptions");if(k.List.isList(l)&&!l.isEmpty()&&addWords(...e.get("curlOptions")),addWords("-X",e.get("method")),addNewLine(),addIndent(),addWordsWithoutLeadingSpace(`${e.get("url")}`),o&&o.size)for(let t of e.get("headers").entries()){addNewLine(),addIndent();let[e,r]=t;addWordsWithoutLeadingSpace("-H",`${e}: ${r}`),n=n||/^content-type$/i.test(e)&&/^multipart\/form-data$/i.test(r)}const c=e.get("body");if(c)if(n&&["POST","PUT","PATCH"].includes(e.get("method")))for(let[e,t]of c.entrySeq()){let r=extractKey(e);addNewLine(),addIndent(),addWordsWithoutLeadingSpace("-F"),t instanceof K.File&&"string"==typeof t.valueOf()?addWords(`${r}=${t.data}${t.type?`;type=${t.type}`:""}`):t instanceof K.File?addWords(`${r}=@${t.name}${t.type?`;type=${t.type}`:""}`):addWords(`${r}=${t}`)}else if(c instanceof K.File)addNewLine(),addIndent(),addWordsWithoutLeadingSpace(`--data-binary '@${c.name}'`);else{addNewLine(),addIndent(),addWordsWithoutLeadingSpace("-d ");let t=c;k.Map.isMap(t)?addWordsWithoutLeadingSpace(function getStringBodyOfMap(e){let t=[];for(let[r,a]of e.get("body").entrySeq()){let e=extractKey(r);a instanceof K.File?t.push(`  "${e}": {\n    "name": "${a.name}"${a.type?`,\n    "type": "${a.type}"`:""}\n  }`):t.push(`  "${e}": ${JSON.stringify(a,null,2).replace(/(\r\n|\r|\n)/g,"\n  ")}`)}return`{\n${t.join(",\n")}\n}`}(e)):("string"!=typeof t&&(t=JSON.stringify(t)),addWordsWithoutLeadingSpace(t))}else c||"POST"!==e.get("method")||(addNewLine(),addIndent(),addWordsWithoutLeadingSpace("-d ''"));return s},requestSnippetGenerator_curl_powershell=e=>curlify(e,escapePowershell,"`\n",".exe"),requestSnippetGenerator_curl_bash=e=>curlify(e,escapeShell,"\\\n"),requestSnippetGenerator_curl_cmd=e=>curlify(e,escapeCMD,"^\n"),request_snippets_selectors_state=e=>e||(0,k.Map)(),ot=(0,_e.createSelector)(request_snippets_selectors_state,(e=>{const t=e.get("languages"),r=e.get("generators",(0,k.Map)());return!t||t.isEmpty()?r:r.filter(((e,r)=>t.includes(r)))})),getSnippetGenerators=e=>({fn:t})=>ot(e).map(((e,r)=>{const a=(e=>t[`requestSnippetGenerator_${e}`])(r);return"function"!=typeof a?null:e.set("fn",a)})).filter((e=>e)),lt=(0,_e.createSelector)(request_snippets_selectors_state,(e=>e.get("activeLanguage"))),ct=(0,_e.createSelector)(request_snippets_selectors_state,(e=>e.get("defaultExpanded"))),it=require("classnames");var pt=__webpack_require__.n(it);const mt=require("react-copy-to-clipboard"),ut={cursor:"pointer",lineHeight:1,display:"inline-flex",backgroundColor:"rgb(250, 250, 250)",paddingBottom:"0",paddingTop:"0",border:"1px solid rgb(51, 51, 51)",borderRadius:"4px 4px 0 0",boxShadow:"none",borderBottom:"none"},dt={cursor:"pointer",lineHeight:1,display:"inline-flex",backgroundColor:"rgb(51, 51, 51)",boxShadow:"none",border:"1px solid rgb(51, 51, 51)",paddingBottom:"0",paddingTop:"0",borderRadius:"4px 4px 0 0",marginTop:"-5px",marginRight:"-5px",marginLeft:"-5px",zIndex:"9999",borderBottom:"none"},request_snippets=({request:e,requestSnippetsSelectors:t,getComponent:r})=>{const a=(0,x.useRef)(null),n=r("ArrowUpIcon"),s=r("ArrowDownIcon"),o=r("SyntaxHighlighter",!0),[l,c]=(0,x.useState)(t.getSnippetGenerators()?.keySeq().first()),[i,p]=(0,x.useState)(t?.getDefaultExpanded()),m=t.getSnippetGenerators(),u=m.get(l),d=u.get("fn")(e),handleSetIsExpanded=()=>{p(!i)},handleGetBtnStyle=e=>e===l?dt:ut,handlePreventYScrollingBeyondElement=e=>{const{target:t,deltaY:r}=e,{scrollHeight:a,offsetHeight:n,scrollTop:s}=t;a>n&&(0===s&&r<0||n+s>=a&&r>0)&&e.preventDefault()};return(0,x.useEffect)((()=>{}),[]),(0,x.useEffect)((()=>{const e=Array.from(a.current.childNodes).filter((e=>!!e.nodeType&&e.classList?.contains("curl-command")));return e.forEach((e=>e.addEventListener("mousewheel",handlePreventYScrollingBeyondElement,{passive:!1}))),()=>{e.forEach((e=>e.removeEventListener("mousewheel",handlePreventYScrollingBeyondElement)))}}),[e]),O().createElement("div",{className:"request-snippets",ref:a},O().createElement("div",{style:{width:"100%",display:"flex",justifyContent:"flex-start",alignItems:"center",marginBottom:"15px"}},O().createElement("h4",{onClick:()=>handleSetIsExpanded(),style:{cursor:"pointer"}},"Snippets"),O().createElement("button",{onClick:()=>handleSetIsExpanded(),style:{border:"none",background:"none"},title:i?"Collapse operation":"Expand operation"},i?O().createElement(s,{className:"arrow",width:"10",height:"10"}):O().createElement(n,{className:"arrow",width:"10",height:"10"}))),i&&O().createElement("div",{className:"curl-command"},O().createElement("div",{style:{paddingLeft:"15px",paddingRight:"10px",width:"100%",display:"flex"}},m.entrySeq().map((([e,t])=>O().createElement("div",{className:pt()("btn",{active:e===l}),style:handleGetBtnStyle(e),key:e,onClick:()=>(e=>{l!==e&&c(e)})(e)},O().createElement("h4",{style:e===l?{color:"white"}:{}},t.get("title")))))),O().createElement("div",{className:"copy-to-clipboard"},O().createElement(mt.CopyToClipboard,{text:d},O().createElement("button",null))),O().createElement("div",null,O().createElement(o,{language:u.get("syntax"),className:"curl microlight",renderPlainText:({children:e,PlainTextViewer:t})=>O().createElement(t,{className:"curl"},e)},d))))},plugins_request_snippets=()=>({components:{RequestSnippets:request_snippets},fn:{requestSnippetGenerator_curl_bash,requestSnippetGenerator_curl_cmd,requestSnippetGenerator_curl_powershell},statePlugins:{requestSnippets:{selectors:h}}});class ModelCollapse extends x.Component{static defaultProps={collapsedContent:"{...}",expanded:!1,title:null,onToggle:()=>{},hideSelfOnExpand:!1,specPath:A().List([])};constructor(e,t){super(e,t);let{expanded:r,collapsedContent:a}=this.props;this.state={expanded:r,collapsedContent:a||ModelCollapse.defaultProps.collapsedContent}}componentDidMount(){const{hideSelfOnExpand:e,expanded:t,modelName:r}=this.props;e&&t&&this.props.onToggle(r,t)}UNSAFE_componentWillReceiveProps(e){this.props.expanded!==e.expanded&&this.setState({expanded:e.expanded})}toggleCollapsed=()=>{this.props.onToggle&&this.props.onToggle(this.props.modelName,!this.state.expanded),this.setState({expanded:!this.state.expanded})};onLoad=e=>{if(e&&this.props.layoutSelectors){const t=this.props.layoutSelectors.getScrollToKey();A().is(t,this.props.specPath)&&this.toggleCollapsed(),this.props.layoutActions.readyToScroll(this.props.specPath,e.parentElement)}};render(){const{title:e,classes:t}=this.props;return this.state.expanded&&this.props.hideSelfOnExpand?O().createElement("span",{className:t||""},this.props.children):O().createElement("span",{className:t||"",ref:this.onLoad},O().createElement("button",{"aria-expanded":this.state.expanded,className:"model-box-control",onClick:this.toggleCollapsed},e&&O().createElement("span",{className:"pointer"},e),O().createElement("span",{className:"model-toggle"+(this.state.expanded?"":" collapsed")}),!this.state.expanded&&O().createElement("span",null,this.state.collapsedContent)),this.state.expanded&&this.props.children)}}const useTabs=({initialTab:e,isExecute:t,schema:r,example:a})=>{const n=(0,x.useMemo)((()=>({example:"example",model:"model"})),[]),s=(0,x.useMemo)((()=>Object.keys(n)),[n]).includes(e)&&r&&!t?e:n.example,o=(e=>{const t=(0,x.useRef)();return(0,x.useEffect)((()=>{t.current=e})),t.current})(t),[l,c]=(0,x.useState)(s),i=(0,x.useCallback)((e=>{c(e.target.dataset.name)}),[]);return(0,x.useEffect)((()=>{o&&!t&&a&&c(n.example)}),[o,t,a]),{activeTab:l,onTabChange:i,tabs:n}},model_example=({schema:e,example:t,isExecute:r=!1,specPath:a,includeWriteOnly:n=!1,includeReadOnly:s=!1,getComponent:o,getConfigs:l,specSelectors:c})=>{const{defaultModelRendering:i,defaultModelExpandDepth:p}=l(),m=o("ModelWrapper"),u=o("HighlightCode",!0),d=ae()(5).toString("base64"),h=ae()(5).toString("base64"),g=ae()(5).toString("base64"),y=ae()(5).toString("base64"),f=c.isOAS3(),{activeTab:S,tabs:E,onTabChange:_}=useTabs({initialTab:i,isExecute:r,schema:e,example:t});return O().createElement("div",{className:"model-example"},O().createElement("ul",{className:"tab",role:"tablist"},O().createElement("li",{className:pt()("tabitem",{active:S===E.example}),role:"presentation"},O().createElement("button",{"aria-controls":h,"aria-selected":S===E.example,className:"tablinks","data-name":"example",id:d,onClick:_,role:"tab"},r?"Edit Value":"Example Value")),e&&O().createElement("li",{className:pt()("tabitem",{active:S===E.model}),role:"presentation"},O().createElement("button",{"aria-controls":y,"aria-selected":S===E.model,className:pt()("tablinks",{inactive:r}),"data-name":"model",id:g,onClick:_,role:"tab"},f?"Schema":"Model"))),S===E.example&&O().createElement("div",{"aria-hidden":S!==E.example,"aria-labelledby":d,"data-name":"examplePanel",id:h,role:"tabpanel",tabIndex:"0"},t||O().createElement(u,null,"(no example available")),S===E.model&&O().createElement("div",{className:"model-container","aria-hidden":S===E.example,"aria-labelledby":g,"data-name":"modelPanel",id:y,role:"tabpanel",tabIndex:"0"},O().createElement(m,{schema:e,getComponent:o,getConfigs:l,specSelectors:c,expandDepth:p,specPath:a,includeReadOnly:s,includeWriteOnly:n})))};class ModelWrapper extends x.Component{onToggle=(e,t)=>{this.props.layoutActions&&this.props.layoutActions.show(this.props.fullPath,t)};render(){let{getComponent:e,getConfigs:t}=this.props;const r=e("Model");let a;return this.props.layoutSelectors&&(a=this.props.layoutSelectors.isShown(this.props.fullPath)),O().createElement("div",{className:"model-box"},O().createElement(r,Qe()({},this.props,{getConfigs:t,expanded:a,depth:1,onToggle:this.onToggle,expandDepth:this.props.expandDepth||0})))}}const ht=require("react-immutable-pure-component");var gt,yt=__webpack_require__.n(ht);function _extends(){return _extends=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var a in r)({}).hasOwnProperty.call(r,a)&&(e[a]=r[a])}return e},_extends.apply(null,arguments)}const rolling_load=e=>x.createElement("svg",_extends({xmlns:"http://www.w3.org/2000/svg",width:200,height:200,className:"rolling-load_svg__lds-rolling",preserveAspectRatio:"xMidYMid",style:{backgroundImage:"none",backgroundPosition:"initial initial",backgroundRepeat:"initial initial"},viewBox:"0 0 100 100"},e),gt||(gt=x.createElement("circle",{cx:50,cy:50,r:35,fill:"none",stroke:"#555",strokeDasharray:"164.93361431346415 56.97787143782138",strokeWidth:10},x.createElement("animateTransform",{attributeName:"transform",begin:"0s",calcMode:"linear",dur:"1s",keyTimes:"0;1",repeatCount:"indefinite",type:"rotate",values:"0 50 50;360 50 50"})))),decodeRefName=e=>{const t=e.replace(/~1/g,"/").replace(/~0/g,"~");try{return decodeURIComponent(t)}catch{return t}};class Model extends(yt()){static propTypes={schema:Ue().map.isRequired,getComponent:Oe().func.isRequired,getConfigs:Oe().func.isRequired,specSelectors:Oe().object.isRequired,name:Oe().string,displayName:Oe().string,isRef:Oe().bool,required:Oe().bool,expandDepth:Oe().number,depth:Oe().number,specPath:Ue().list.isRequired,includeReadOnly:Oe().bool,includeWriteOnly:Oe().bool};getModelName=e=>-1!==e.indexOf("#/definitions/")?decodeRefName(e.replace(/^.*#\/definitions\//,"")):-1!==e.indexOf("#/components/schemas/")?decodeRefName(e.replace(/^.*#\/components\/schemas\//,"")):void 0;getRefSchema=e=>{let{specSelectors:t}=this.props;return t.findDefinition(e)};render(){let{getComponent:e,getConfigs:t,specSelectors:r,schema:a,required:n,name:s,isRef:o,specPath:l,displayName:c,includeReadOnly:i,includeWriteOnly:p}=this.props;const m=e("ObjectModel"),u=e("ArrayModel"),d=e("PrimitiveModel");let h="object",g=a&&a.get("$$ref"),y=a&&a.get("$ref");if(!s&&g&&(s=this.getModelName(g)),y){const e=this.getModelName(y),t=this.getRefSchema(e);k.Map.isMap(t)?(a=t.mergeDeep(a),g||(a=a.set("$$ref",y),g=y)):k.Map.isMap(a)&&1===a.size&&(a=null,s=y)}if(!a)return O().createElement("span",{className:"model model-title"},O().createElement("span",{className:"model-title__text"},c||s),!y&&O().createElement(rolling_load,{height:"20px",width:"20px"}));const f=r.isOAS3()&&a.get("deprecated");switch(o=void 0!==o?o:!!g,h=a&&a.get("type")||h,h){case"object":return O().createElement(m,Qe()({className:"object"},this.props,{specPath:l,getConfigs:t,schema:a,name:s,deprecated:f,isRef:o,includeReadOnly:i,includeWriteOnly:p}));case"array":return O().createElement(u,Qe()({className:"array"},this.props,{getConfigs:t,schema:a,name:s,deprecated:f,required:n,includeReadOnly:i,includeWriteOnly:p}));default:return O().createElement(d,Qe()({},this.props,{getComponent:e,getConfigs:t,schema:a,name:s,deprecated:f,required:n}))}}}class Models extends x.Component{getSchemaBasePath=()=>this.props.specSelectors.isOAS3()?["components","schemas"]:["definitions"];getCollapsedContent=()=>" ";handleToggle=(e,t)=>{const{layoutActions:r}=this.props;r.show([...this.getSchemaBasePath(),e],t),t&&this.props.specActions.requestResolvedSubtree([...this.getSchemaBasePath(),e])};onLoadModels=e=>{e&&this.props.layoutActions.readyToScroll(this.getSchemaBasePath(),e)};onLoadModel=e=>{if(e){const t=e.getAttribute("data-name");this.props.layoutActions.readyToScroll([...this.getSchemaBasePath(),t],e)}};render(){let{specSelectors:e,getComponent:t,layoutSelectors:r,layoutActions:a,getConfigs:n}=this.props,s=e.definitions(),{docExpansion:o,defaultModelsExpandDepth:l}=n();if(!s.size||l<0)return null;const c=this.getSchemaBasePath();let i=r.isShown(c,l>0&&"none"!==o);const p=e.isOAS3(),m=t("ModelWrapper"),u=t("Collapse"),d=t("ModelCollapse"),h=t("JumpToPath",!0),g=t("ArrowUpIcon"),y=t("ArrowDownIcon");return O().createElement("section",{className:i?"models is-open":"models",ref:this.onLoadModels},O().createElement("h4",null,O().createElement("button",{"aria-expanded":i,className:"models-control",onClick:()=>a.show(c,!i)},O().createElement("span",null,p?"Schemas":"Models"),i?O().createElement(g,null):O().createElement(y,null))),O().createElement(u,{isOpened:i},s.entrySeq().map((([s])=>{const o=[...c,s],i=A().List(o),p=e.specResolvedSubtree(o),u=e.specJson().getIn(o),g=k.Map.isMap(p)?p:A().Map(),y=k.Map.isMap(u)?u:A().Map(),f=g.get("title")||y.get("title")||s,S=r.isShown(o,!1);S&&0===g.size&&y.size>0&&this.props.specActions.requestResolvedSubtree(o);const E=O().createElement(m,{name:s,expandDepth:l,schema:g||A().Map(),displayName:f,fullPath:o,specPath:i,getComponent:t,specSelectors:e,getConfigs:n,layoutSelectors:r,layoutActions:a,includeReadOnly:!0,includeWriteOnly:!0}),_=O().createElement("span",{className:"model-box"},O().createElement("span",{className:"model model-title"},f));return O().createElement("div",{id:`model-${s}`,className:"model-container",key:`models-section-${s}`,"data-name":s,ref:this.onLoadModel},O().createElement("span",{className:"models-jump-to-path"},O().createElement(h,{path:i})),O().createElement(d,{classes:"model-box",collapsedContent:this.getCollapsedContent(s),onToggle:this.handleToggle,title:_,displayName:f,modelName:s,specPath:i,layoutSelectors:r,layoutActions:a,hideSelfOnExpand:!0,expanded:l>0&&S},E))})).toArray()))}}const enum_model=({value:e,getComponent:t})=>{let r=t("ModelCollapse"),a=O().createElement("span",null,"Array [ ",e.count()," ]");return O().createElement("span",{className:"prop-enum"},"Enum:",O().createElement("br",null),O().createElement(r,{collapsedContent:a},"[ ",e.map(String).join(", ")," ]"))};function isAbsoluteUrl(e){return e.match(/^(?:[a-z]+:)?\/\//i)}function buildBaseUrl(e,t){return e?isAbsoluteUrl(e)?function addProtocol(e){return e.match(/^\/\//i)?`${window.location.protocol}${e}`:e}(e):new URL(e,t).href:t}function safeBuildUrl(e,t,{selectedServer:r=""}={}){try{return function buildUrl(e,t,{selectedServer:r=""}={}){if(!e)return;if(isAbsoluteUrl(e))return e;const a=buildBaseUrl(r,t);return isAbsoluteUrl(a)?new URL(e,a).href:new URL(e,window.location.href).href}(e,t,{selectedServer:r})}catch{return}}function sanitizeUrl(e){if("string"!=typeof e||""===e.trim())return"";const t=e.trim(),r="about:blank";try{const e=`https://base${String(Math.random()).slice(2)}`,a=new URL(t,e),n=a.protocol.slice(0,-1);return["javascript","data","vbscript"].includes(n.toLowerCase())?r:a.origin===e?t.startsWith("/")?`${a.pathname}${a.search}${a.hash}`:t.startsWith("./")?`.${a.pathname}${a.search}${a.hash}`:t.startsWith("../")?`..${a.pathname}${a.search}${a.hash}`:`${a.pathname.substring(1)}${a.search}${a.hash}`:String(a)}catch{return r}}class ObjectModel extends x.Component{render(){let{schema:e,name:t,displayName:r,isRef:a,getComponent:n,getConfigs:s,depth:o,onToggle:l,expanded:c,specPath:i,...p}=this.props,{specSelectors:m,expandDepth:u,includeReadOnly:d,includeWriteOnly:h}=p;const{isOAS3:g}=m,y=o>2||2===o&&"items"!==i.last();if(!e)return null;const{showExtensions:f}=s(),S=f?getExtensions(e):(0,k.List)();let E=e.get("description"),_=e.get("properties"),v=e.get("additionalProperties"),w=e.get("title")||r||t,b=e.get("required"),C=e.filter(((e,t)=>-1!==["maxProperties","minProperties","nullable","example"].indexOf(t))),x=e.get("deprecated"),N=e.getIn(["externalDocs","url"]),A=e.getIn(["externalDocs","description"]);const I=n("JumpToPath",!0),j=n("Markdown",!0),q=n("Model"),P=n("ModelCollapse"),M=n("Property"),T=n("Link"),R=n("ModelExtensions"),JumpToPathSection=()=>O().createElement("span",{className:"model-jump-to-path"},O().createElement(I,{path:i})),J=O().createElement("span",null,O().createElement("span",null,"{"),"...",O().createElement("span",null,"}"),a?O().createElement(JumpToPathSection,null):""),$=m.isOAS3()?e.get("allOf"):null,V=m.isOAS3()?e.get("anyOf"):null,L=m.isOAS3()?e.get("oneOf"):null,D=m.isOAS3()?e.get("not"):null,U=w&&O().createElement("span",{className:"model-title"},a&&e.get("$$ref")&&O().createElement("span",{className:pt()("model-hint",{"model-hint--embedded":y})},e.get("$$ref")),O().createElement("span",{className:"model-title__text"},w));return O().createElement("span",{className:"model"},O().createElement(P,{modelName:t,title:U,onToggle:l,expanded:!!c||o<=u,collapsedContent:J},O().createElement("span",{className:"brace-open object"},"{"),a?O().createElement(JumpToPathSection,null):null,O().createElement("span",{className:"inner-object"},O().createElement("table",{className:"model"},O().createElement("tbody",null,E?O().createElement("tr",{className:"description"},O().createElement("td",null,"description:"),O().createElement("td",null,O().createElement(j,{source:E}))):null,N&&O().createElement("tr",{className:"external-docs"},O().createElement("td",null,"externalDocs:"),O().createElement("td",null,O().createElement(T,{target:"_blank",href:sanitizeUrl(N)},A||N))),x?O().createElement("tr",{className:"property"},O().createElement("td",null,"deprecated:"),O().createElement("td",null,"true")):null,_&&_.size?_.entrySeq().filter((([,e])=>(!e.get("readOnly")||d)&&(!e.get("writeOnly")||h))).map((([e,r])=>{let a=g()&&r.get("deprecated"),l=k.List.isList(b)&&b.contains(e),c=["property-row"];return a&&c.push("deprecated"),l&&c.push("required"),O().createElement("tr",{key:e,className:c.join(" ")},O().createElement("td",null,e,l&&O().createElement("span",{className:"star"},"*")),O().createElement("td",null,O().createElement(q,Qe()({key:`object-${t}-${e}_${r}`},p,{required:l,getComponent:n,specPath:i.push("properties",e),getConfigs:s,schema:r,depth:o+1}))))})).toArray():null,0===S.size?null:O().createElement(O().Fragment,null,O().createElement("tr",null,O().createElement("td",null," ")),O().createElement(R,{extensions:S,propClass:"extension"})),v&&v.size?O().createElement("tr",null,O().createElement("td",null,"< * >:"),O().createElement("td",null,O().createElement(q,Qe()({},p,{required:!1,getComponent:n,specPath:i.push("additionalProperties"),getConfigs:s,schema:v,depth:o+1})))):null,$?O().createElement("tr",null,O().createElement("td",null,"allOf ->"),O().createElement("td",null,$.map(((e,t)=>O().createElement("div",{key:t},O().createElement(q,Qe()({},p,{required:!1,getComponent:n,specPath:i.push("allOf",t),getConfigs:s,schema:e,depth:o+1}))))))):null,V?O().createElement("tr",null,O().createElement("td",null,"anyOf ->"),O().createElement("td",null,V.map(((e,t)=>O().createElement("div",{key:t},O().createElement(q,Qe()({},p,{required:!1,getComponent:n,specPath:i.push("anyOf",t),getConfigs:s,schema:e,depth:o+1}))))))):null,L?O().createElement("tr",null,O().createElement("td",null,"oneOf ->"),O().createElement("td",null,L.map(((e,t)=>O().createElement("div",{key:t},O().createElement(q,Qe()({},p,{required:!1,getComponent:n,specPath:i.push("oneOf",t),getConfigs:s,schema:e,depth:o+1}))))))):null,D?O().createElement("tr",null,O().createElement("td",null,"not ->"),O().createElement("td",null,O().createElement("div",null,O().createElement(q,Qe()({},p,{required:!1,getComponent:n,specPath:i.push("not"),getConfigs:s,schema:D,depth:o+1}))))):null))),O().createElement("span",{className:"brace-close"},"}")),C.size?C.entrySeq().map((([e,t])=>O().createElement(M,{key:`${e}-${t}`,propKey:e,propVal:t,propClass:"property"}))):null)}}class ArrayModel extends x.Component{render(){let{getComponent:e,getConfigs:t,schema:r,depth:a,expandDepth:n,name:s,displayName:o,specPath:l}=this.props,c=r.get("description"),i=r.get("items"),p=r.get("title")||o||s,m=r.filter(((e,t)=>-1===["type","items","description","$$ref","externalDocs"].indexOf(t))),u=r.getIn(["externalDocs","url"]),d=r.getIn(["externalDocs","description"]);const h=e("Markdown",!0),g=e("ModelCollapse"),y=e("Model"),f=e("Property"),S=e("Link"),E=p&&O().createElement("span",{className:"model-title"},O().createElement("span",{className:"model-title__text"},p));return O().createElement("span",{className:"model"},O().createElement(g,{title:E,expanded:a<=n,collapsedContent:"[...]"},"[",m.size?m.entrySeq().map((([e,t])=>O().createElement(f,{key:`${e}-${t}`,propKey:e,propVal:t,propClass:"property"}))):null,c?O().createElement(h,{source:c}):m.size?O().createElement("div",{className:"markdown"}):null,u&&O().createElement("div",{className:"external-docs"},O().createElement(S,{target:"_blank",href:sanitizeUrl(u)},d||u)),O().createElement("span",null,O().createElement(y,Qe()({},this.props,{getConfigs:t,specPath:l.push("items"),name:null,schema:i,required:!1,depth:a+1}))),"]"))}}const ft="property primitive";class Primitive extends x.Component{render(){let{schema:e,getComponent:t,getConfigs:r,name:a,displayName:n,depth:s,expandDepth:o}=this.props;const{showExtensions:l}=r();if(!e||!e.get)return O().createElement("div",null);let c=e.get("type"),i=e.get("format"),p=e.get("xml"),m=e.get("enum"),u=e.get("title")||n||a,d=e.get("description");const h=getExtensions(e);let g=e.filter(((e,t)=>-1===["enum","type","format","description","$$ref","externalDocs"].indexOf(t))).filterNot(((e,t)=>h.has(t))),y=e.getIn(["externalDocs","url"]),f=e.getIn(["externalDocs","description"]);const S=t("Markdown",!0),E=t("EnumModel"),_=t("Property"),v=t("ModelCollapse"),w=t("Link"),b=t("ModelExtensions"),C=u&&O().createElement("span",{className:"model-title"},O().createElement("span",{className:"model-title__text"},u));return O().createElement("span",{className:"model"},O().createElement(v,{title:C,expanded:s<=o,collapsedContent:"[...]"},O().createElement("span",{className:"prop"},a&&s>1&&O().createElement("span",{className:"prop-name"},u),O().createElement("span",{className:"prop-type"},c),i&&O().createElement("span",{className:"prop-format"},"($",i,")"),g.size?g.entrySeq().map((([e,t])=>O().createElement(_,{key:`${e}-${t}`,propKey:e,propVal:t,propClass:ft}))):null,l&&h.size>0?O().createElement(b,{extensions:h,propClass:`${ft} extension`}):null,d?O().createElement(S,{source:d}):null,y&&O().createElement("div",{className:"external-docs"},O().createElement(w,{target:"_blank",href:sanitizeUrl(y)},f||y)),p&&p.size?O().createElement("span",null,O().createElement("br",null),O().createElement("span",{className:ft},"xml:"),p.entrySeq().map((([e,t])=>O().createElement("span",{key:`${e}-${t}`,className:ft},O().createElement("br",null),"   ",e,": ",String(t)))).toArray()):null,m&&O().createElement(E,{value:m,getComponent:t}))))}}class Schemes extends O().Component{UNSAFE_componentWillMount(){let{schemes:e}=this.props;this.setScheme(e.first())}UNSAFE_componentWillReceiveProps(e){this.props.currentScheme&&e.schemes.includes(this.props.currentScheme)||this.setScheme(e.schemes.first())}onChange=e=>{this.setScheme(e.target.value)};setScheme=e=>{let{path:t,method:r,specActions:a}=this.props;a.setScheme(e,t,r)};render(){let{schemes:e,currentScheme:t}=this.props;return O().createElement("label",{htmlFor:"schemes"},O().createElement("span",{className:"schemes-title"},"Schemes"),O().createElement("select",{onChange:this.onChange,value:t,id:"schemes"},e.valueSeq().map((e=>O().createElement("option",{value:e,key:e},e))).toArray()))}}class SchemesContainer extends O().Component{render(){const{specActions:e,specSelectors:t,getComponent:r}=this.props,a=t.operationScheme(),n=t.schemes(),s=r("schemes");return n&&n.size?O().createElement(s,{currentScheme:a,schemes:n,specActions:e}):null}}const St=require("react-debounce-input");var Et=__webpack_require__.n(St);const _t={value:"",onChange:()=>{},schema:{},keyName:"",required:!1,errors:(0,k.List)()};class JsonSchemaForm extends x.Component{static defaultProps=_t;componentDidMount(){const{dispatchInitialValue:e,value:t,onChange:r}=this.props;e?r(t):!1===e&&r("")}render(){let{schema:e,errors:t,value:r,onChange:a,getComponent:n,fn:s,disabled:o}=this.props;const l=e&&e.get?e.get("format"):null,c=e&&e.get?e.get("type"):null,i=s.getSchemaObjectType(e),p=s.isFileUploadIntended(e);let getComponentSilently=e=>n(e,!1,{failSilently:!0}),m=c?getComponentSilently(l?`JsonSchema_${c}_${l}`:`JsonSchema_${c}`):n("JsonSchema_string");return p||!k.List.isList(c)||"array"!==i&&"object"!==i||(m=n("JsonSchema_object")),m||(m=n("JsonSchema_string")),O().createElement(m,Qe()({},this.props,{errors:t,fn:s,getComponent:n,value:r,onChange:a,schema:e,disabled:o}))}}class JsonSchema_string extends x.Component{static defaultProps=_t;onChange=e=>{const t=this.props.schema&&"file"===this.props.schema.get("type")?e.target.files[0]:e.target.value;this.props.onChange(t,this.props.keyName)};onEnumChange=e=>this.props.onChange(e);render(){let{getComponent:e,value:t,schema:r,errors:a,required:n,description:s,disabled:o}=this.props;const l=r&&r.get?r.get("enum"):null,c=r&&r.get?r.get("format"):null,i=r&&r.get?r.get("type"):null,p=r&&r.get?r.get("in"):null;if(t?(isImmutable(t)||"object"==typeof t)&&(t=stringify(t)):t="",a=a.toJS?a.toJS():[],l){const r=e("Select");return O().createElement(r,{className:a.length?"invalid":"",title:a.length?a:"",allowedValues:[...l],value:t,allowEmptyValue:!n,disabled:o,onChange:this.onEnumChange})}const m=o||p&&"formData"===p&&!("FormData"in window),u=e("Input");return i&&"file"===i?O().createElement(u,{type:"file",className:a.length?"invalid":"",title:a.length?a:"",onChange:this.onChange,disabled:m}):O().createElement(Et(),{type:c&&"password"===c?"password":"text",className:a.length?"invalid":"",title:a.length?a:"",value:t,minLength:0,debounceTimeout:350,placeholder:s,onChange:this.onChange,disabled:m})}}class JsonSchema_array extends x.PureComponent{static defaultProps=_t;constructor(e,t){super(e,t),this.state={value:valueOrEmptyList(e.value),schema:e.schema}}UNSAFE_componentWillReceiveProps(e){const t=valueOrEmptyList(e.value);t!==this.state.value&&this.setState({value:t}),e.schema!==this.state.schema&&this.setState({schema:e.schema})}onChange=()=>{this.props.onChange(this.state.value)};onItemChange=(e,t)=>{this.setState((({value:r})=>({value:r.set(t,e)})),this.onChange)};removeItem=e=>{this.setState((({value:t})=>({value:t.delete(e)})),this.onChange)};addItem=()=>{const{fn:e}=this.props;let t=valueOrEmptyList(this.state.value);this.setState((()=>({value:t.push(e.getSampleSchema(this.state.schema.get("items"),!1,{includeWriteOnly:!0}))})),this.onChange)};onEnumChange=e=>{this.setState((()=>({value:e})),this.onChange)};render(){let{getComponent:e,required:t,schema:r,errors:a,fn:n,disabled:s}=this.props;a=a.toJS?a.toJS():Array.isArray(a)?a:[];const o=a.filter((e=>"string"==typeof e)),l=a.filter((e=>void 0!==e.needRemove)).map((e=>e.error)),c=this.state.value,i=!!(c&&c.count&&c.count()>0),p=r.getIn(["items","enum"]),m=r.get("items"),u=n.getSchemaObjectType(m),d=n.getSchemaObjectTypeLabel(m),h=r.getIn(["items","format"]),g=r.get("items");let y,f=!1,S="file"===u||"string"===u&&"binary"===h;if(u&&h?y=e(`JsonSchema_${u}_${h}`):"boolean"!==u&&"array"!==u&&"object"!==u||(y=e(`JsonSchema_${u}`)),!k.List.isList(m?.get("type"))||"array"!==u&&"object"!==u||(y=e("JsonSchema_object")),y||S||(f=!0),p){const r=e("Select");return O().createElement(r,{className:a.length?"invalid":"",title:a.length?a:"",multiple:!0,value:c,disabled:s,allowedValues:p,allowEmptyValue:!t,onChange:this.onEnumChange})}const E=e("Button");return O().createElement("div",{className:"json-schema-array"},i?c.map(((t,r)=>{const o=(0,k.fromJS)([...a.filter((e=>e.index===r)).map((e=>e.error))]);return O().createElement("div",{key:r,className:"json-schema-form-item"},S?O().createElement(JsonSchemaArrayItemFile,{value:t,onChange:e=>this.onItemChange(e,r),disabled:s,errors:o,getComponent:e}):f?O().createElement(JsonSchemaArrayItemText,{value:t,onChange:e=>this.onItemChange(e,r),disabled:s,errors:o}):O().createElement(y,Qe()({},this.props,{value:t,onChange:e=>this.onItemChange(e,r),disabled:s,errors:o,schema:g,getComponent:e,fn:n})),s?null:O().createElement(E,{className:`btn btn-sm json-schema-form-item-remove ${l.length?"invalid":null}`,title:l.length?l:"",onClick:()=>this.removeItem(r)}," - "))})):null,s?null:O().createElement(E,{className:`btn btn-sm json-schema-form-item-add ${o.length?"invalid":null}`,title:o.length?o:"",onClick:this.addItem},"Add ",d," item"))}}class JsonSchemaArrayItemText extends x.Component{static defaultProps=_t;onChange=e=>{const t=e.target.value;this.props.onChange(t,this.props.keyName)};render(){let{value:e,errors:t,description:r,disabled:a}=this.props;return e?(isImmutable(e)||"object"==typeof e)&&(e=stringify(e)):e="",t=t.toJS?t.toJS():[],O().createElement(Et(),{type:"text",className:t.length?"invalid":"",title:t.length?t:"",value:e,minLength:0,debounceTimeout:350,placeholder:r,onChange:this.onChange,disabled:a})}}class JsonSchemaArrayItemFile extends x.Component{static defaultProps=_t;onFileChange=e=>{const t=e.target.files[0];this.props.onChange(t,this.props.keyName)};render(){let{getComponent:e,errors:t,disabled:r}=this.props;const a=e("Input"),n=r||!("FormData"in window);return O().createElement(a,{type:"file",className:t.length?"invalid":"",title:t.length?t:"",onChange:this.onFileChange,disabled:n})}}class JsonSchema_boolean extends x.Component{static defaultProps=_t;onEnumChange=e=>this.props.onChange(e);render(){let{getComponent:e,value:t,errors:r,schema:a,required:n,disabled:s}=this.props;r=r.toJS?r.toJS():[];let o=a&&a.get?a.get("enum"):null,l=!o||!n,c=!o&&["true","false"];const i=e("Select");return O().createElement(i,{className:r.length?"invalid":"",title:r.length?r:"",value:String(t),disabled:s,allowedValues:o?[...o]:c,allowEmptyValue:l,onChange:this.onEnumChange})}}const stringifyObjectErrors=e=>e.map((e=>{const t=void 0!==e.propKey?e.propKey:e.index;let r="string"==typeof e?e:"string"==typeof e.error?e.error:null;if(!t&&r)return r;let a=e.error,n=`/${e.propKey}`;for(;"object"==typeof a;){const e=void 0!==a.propKey?a.propKey:a.index;if(void 0===e)break;if(n+=`/${e}`,!a.error)break;a=a.error}return`${n}: ${a}`}));class JsonSchema_object extends x.PureComponent{constructor(){super()}static defaultProps=_t;onChange=e=>{this.props.onChange(e)};handleOnChange=e=>{const t=e.target.value;this.onChange(t)};render(){let{getComponent:e,value:t,errors:r,disabled:a}=this.props;const n=e("TextArea");return r=r.toJS?r.toJS():Array.isArray(r)?r:[],O().createElement("div",null,O().createElement(n,{className:pt()({invalid:r.length}),title:r.length?stringifyObjectErrors(r).join(", "):"",value:stringify(t),disabled:a,onChange:this.handleOnChange}))}}function valueOrEmptyList(e){return k.List.isList(e)?e:Array.isArray(e)?(0,k.fromJS)(e):(0,k.List)()}const ModelExtensions=({extensions:e,propClass:t=""})=>e.entrySeq().map((([e,r])=>{const a=immutableToJS(r)??null;return O().createElement("tr",{key:e,className:t},O().createElement("td",null,e),O().createElement("td",null,JSON.stringify(a)))})).toArray(),vt=require("lodash/isPlainObject");var wt=__webpack_require__.n(vt);const hasSchemaType=(e,t)=>{const r=k.Map.isMap(e);if(!r&&!wt()(e))return!1;const a=r?e.get("type"):e.type;return t===a||Array.isArray(t)&&t.includes(a)},getType=(e,t=new WeakSet)=>{if(null==e)return"any";if(t.has(e))return"any";t.add(e);const{type:r,items:a}=e;return Object.hasOwn(e,"items")?(()=>{if(a)return`array<${getType(a,t)}>`;return"array<any>"})():r},getSchemaObjectTypeLabel=e=>getType(immutableToJS(e)),json_schema_5=()=>({components:{modelExample:model_example,ModelWrapper,ModelCollapse,Model,Models,EnumModel:enum_model,ObjectModel,ArrayModel,PrimitiveModel:Primitive,ModelExtensions,schemes:Schemes,SchemesContainer,...g},fn:{hasSchemaType,getSchemaObjectTypeLabel}}),bt=require("xml");var Ct=__webpack_require__.n(bt);const xt=require("randexp");var Ot=__webpack_require__.n(xt);const Nt=require("lodash/isEmpty");var kt=__webpack_require__.n(Nt);const shallowArrayEquals=e=>t=>Array.isArray(e)&&Array.isArray(t)&&e.length===t.length&&e.every(((e,r)=>e===t[r])),list=(...e)=>e;class Cache extends Map{delete(e){const t=Array.from(this.keys()).find(shallowArrayEquals(e));return super.delete(t)}get(e){const t=Array.from(this.keys()).find(shallowArrayEquals(e));return super.get(t)}has(e){return-1!==Array.from(this.keys()).findIndex(shallowArrayEquals(e))}}const utils_memoizeN=(e,t=list)=>{const{Cache:r}=B();B().Cache=Cache;const a=B()(e,t);return B().Cache=r,a},At={string:e=>e.pattern?(e=>{try{const t=/(?<=(?<!\\)\{)(\d{3,})(?=\})|(?<=(?<!\\)\{\d*,)(\d{3,})(?=\})|(?<=(?<!\\)\{)(\d{3,})(?=,\d*\})/g,r=e.replace(t,"100"),a=new(Ot())(r);return a.max=100,a.gen()}catch(e){return"string"}})(e.pattern):"string",string_email:()=>"<EMAIL>","string_date-time":()=>(new Date).toISOString(),string_date:()=>(new Date).toISOString().substring(0,10),string_time:()=>(new Date).toISOString().substring(11),string_uuid:()=>"3fa85f64-5717-4562-b3fc-2c963f66afa6",string_hostname:()=>"example.com",string_ipv4:()=>"*************",string_ipv6:()=>"2001:0db8:5b96:0000:0000:426f:8e17:642a",number:()=>0,number_float:()=>0,integer:()=>0,boolean:e=>"boolean"!=typeof e.default||e.default},primitive=e=>{e=objectify(e);let{type:t,format:r}=e,a=At[`${t}_${r}`]||At[t];return isFunc(a)?a(e):"Unknown Type: "+e.type},sanitizeRef=e=>deeplyStripKey(e,"$$ref",(e=>"string"==typeof e&&e.indexOf("#")>-1)),It=["maxProperties","minProperties"],jt=["minItems","maxItems"],qt=["minimum","maximum","exclusiveMinimum","exclusiveMaximum"],Pt=["minLength","maxLength"],mergeJsonSchema=(e,t,r={})=>{const a={...e};if(["example","default","enum","xml","type",...It,...jt,...qt,...Pt].forEach((e=>(e=>{void 0===a[e]&&void 0!==t[e]&&(a[e]=t[e])})(e))),void 0!==t.required&&Array.isArray(t.required)&&(void 0!==a.required&&a.required.length||(a.required=[]),t.required.forEach((e=>{a.required.includes(e)||a.required.push(e)}))),t.properties){a.properties||(a.properties={});let e=objectify(t.properties);for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&(e[n]&&e[n].deprecated||e[n]&&e[n].readOnly&&!r.includeReadOnly||e[n]&&e[n].writeOnly&&!r.includeWriteOnly||a.properties[n]||(a.properties[n]=e[n],!t.required&&Array.isArray(t.required)&&-1!==t.required.indexOf(n)&&(a.required?a.required.push(n):a.required=[n])))}return t.items&&(a.items||(a.items={}),a.items=mergeJsonSchema(a.items,t.items,r)),a},sampleFromSchemaGeneric=(e,t={},r=void 0,a=!1)=>{e&&isFunc(e.toJS)&&(e=e.toJS());let n=void 0!==r||e&&void 0!==e.example||e&&void 0!==e.default;const s=!n&&e&&e.oneOf&&e.oneOf.length>0,o=!n&&e&&e.anyOf&&e.anyOf.length>0;if(!n&&(s||o)){const r=objectify(s?e.oneOf[0]:e.anyOf[0]);if(!(e=mergeJsonSchema(e,r,t)).xml&&r.xml&&(e.xml=r.xml),void 0!==e.example&&void 0!==r.example)n=!0;else if(r.properties){e.properties||(e.properties={});let a=objectify(r.properties);for(let n in a)Object.prototype.hasOwnProperty.call(a,n)&&(a[n]&&a[n].deprecated||a[n]&&a[n].readOnly&&!t.includeReadOnly||a[n]&&a[n].writeOnly&&!t.includeWriteOnly||e.properties[n]||(e.properties[n]=a[n],!r.required&&Array.isArray(r.required)&&-1!==r.required.indexOf(n)&&(e.required?e.required.push(n):e.required=[n])))}}const l={};let{xml:c,type:i,example:p,properties:m,additionalProperties:u,items:d}=e||{},{includeReadOnly:h,includeWriteOnly:g}=t;c=c||{};let y,{name:f,prefix:S,namespace:E}=c,_={};if(a&&(f=f||"notagname",y=(S?S+":":"")+f,E)){l[S?"xmlns:"+S:"xmlns"]=E}a&&(_[y]=[]);const schemaHasAny=t=>t.some((t=>Object.prototype.hasOwnProperty.call(e,t)));e&&!i&&(m||u||schemaHasAny(It)?i="object":d||schemaHasAny(jt)?i="array":schemaHasAny(qt)?(i="number",e.type="number"):n||e.enum||(i="string",e.type="string"));const handleMinMaxItems=t=>{if(null!=e?.maxItems&&(t=t.slice(0,e?.maxItems)),null!=e?.minItems){let r=0;for(;t.length<e?.minItems;)t.push(t[r++%t.length])}return t},v=objectify(m);let w,b=0;const hasExceededMaxProperties=()=>e&&null!==e.maxProperties&&void 0!==e.maxProperties&&b>=e.maxProperties,canAddProperty=t=>!e||null===e.maxProperties||void 0===e.maxProperties||!hasExceededMaxProperties()&&(!(t=>!(e&&e.required&&e.required.length&&e.required.includes(t)))(t)||e.maxProperties-b-(()=>{if(!e||!e.required)return 0;let t=0;return a?e.required.forEach((e=>t+=void 0===_[e]?0:1)):e.required.forEach((e=>t+=void 0===_[y]?.find((t=>void 0!==t[e]))?0:1)),e.required.length-t})()>0);if(w=a?(r,n=void 0)=>{if(e&&v[r]){if(v[r].xml=v[r].xml||{},v[r].xml.attribute){const e=Array.isArray(v[r].enum)?v[r].enum[0]:void 0,t=v[r].example,a=v[r].default;return void(l[v[r].xml.name||r]=void 0!==t?t:void 0!==a?a:void 0!==e?e:primitive(v[r]))}v[r].xml.name=v[r].xml.name||r}else v[r]||!1===u||(v[r]={xml:{name:r}});let s=sampleFromSchemaGeneric(e&&v[r]||void 0,t,n,a);canAddProperty(r)&&(b++,Array.isArray(s)?_[y]=_[y].concat(s):_[y].push(s))}:(r,n)=>{if(canAddProperty(r)){if(Object.prototype.hasOwnProperty.call(e,"discriminator")&&e.discriminator&&Object.prototype.hasOwnProperty.call(e.discriminator,"mapping")&&e.discriminator.mapping&&Object.prototype.hasOwnProperty.call(e,"$$ref")&&e.$$ref&&e.discriminator.propertyName===r){for(let t in e.discriminator.mapping)if(-1!==e.$$ref.search(e.discriminator.mapping[t])){_[r]=t;break}}else _[r]=sampleFromSchemaGeneric(v[r],t,n,a);b++}},n){let n;if(n=sanitizeRef(void 0!==r?r:void 0!==p?p:e.default),!a){if("number"==typeof n&&"string"===i)return`${n}`;if("string"!=typeof n||"string"===i)return n;try{return JSON.parse(n)}catch(e){return n}}if(e||(i=Array.isArray(n)?"array":typeof n),"array"===i){if(!Array.isArray(n)){if("string"==typeof n)return n;n=[n]}const r=e?e.items:void 0;r&&(r.xml=r.xml||c||{},r.xml.name=r.xml.name||c.name);let s=n.map((e=>sampleFromSchemaGeneric(r,t,e,a)));return s=handleMinMaxItems(s),c.wrapped?(_[y]=s,kt()(l)||_[y].push({_attr:l})):_=s,_}if("object"===i){if("string"==typeof n)return n;for(let t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e&&v[t]&&v[t].readOnly&&!h||e&&v[t]&&v[t].writeOnly&&!g||(e&&v[t]&&v[t].xml&&v[t].xml.attribute?l[v[t].xml.name||t]=n[t]:w(t,n[t])));return kt()(l)||_[y].push({_attr:l}),_}return _[y]=kt()(l)?n:[{_attr:l},n],_}if("object"===i){for(let e in v)Object.prototype.hasOwnProperty.call(v,e)&&(v[e]&&v[e].deprecated||v[e]&&v[e].readOnly&&!h||v[e]&&v[e].writeOnly&&!g||w(e));if(a&&l&&_[y].push({_attr:l}),hasExceededMaxProperties())return _;if(!0===u)a?_[y].push({additionalProp:"Anything can be here"}):_.additionalProp1={},b++;else if(u){const r=objectify(u),n=sampleFromSchemaGeneric(r,t,void 0,a);if(a&&r.xml&&r.xml.name&&"notagname"!==r.xml.name)_[y].push(n);else{const t=r["x-additionalPropertiesName"]||"additionalProp",s=null!==e.minProperties&&void 0!==e.minProperties&&b<e.minProperties?e.minProperties-b:3;for(let e=1;e<=s;e++){if(hasExceededMaxProperties())return _;if(a){const r={};r[t+e]=n.notagname,_[y].push(r)}else _[t+e]=n;b++}}}return _}if("array"===i){if(!d)return;let r;if(a&&(d.xml=d.xml||e?.xml||{},d.xml.name=d.xml.name||c.name),Array.isArray(d.anyOf))r=d.anyOf.map((e=>sampleFromSchemaGeneric(mergeJsonSchema(e,d,t),t,void 0,a)));else if(Array.isArray(d.oneOf))r=d.oneOf.map((e=>sampleFromSchemaGeneric(mergeJsonSchema(e,d,t),t,void 0,a)));else{if(!(!a||a&&c.wrapped))return sampleFromSchemaGeneric(d,t,void 0,a);r=[sampleFromSchemaGeneric(d,t,void 0,a)]}return r=handleMinMaxItems(r),a&&c.wrapped?(_[y]=r,kt()(l)||_[y].push({_attr:l}),_):r}let C;if(e&&Array.isArray(e.enum))C=normalizeArray(e.enum)[0];else{if(!e)return;if(C=primitive(e),"number"==typeof C){let t=e.minimum;null!=t&&(e.exclusiveMinimum&&t++,C=t);let r=e.maximum;null!=r&&(e.exclusiveMaximum&&r--,C=r)}if("string"==typeof C&&(null!==e.maxLength&&void 0!==e.maxLength&&(C=C.slice(0,e.maxLength)),null!==e.minLength&&void 0!==e.minLength)){let t=0;for(;C.length<e.minLength;)C+=C[t++%C.length]}}if("file"!==i)return a?(_[y]=kt()(l)?C:[{_attr:l},C],_):C},inferSchema=e=>(e.schema&&(e=e.schema),e.properties&&(e.type="object"),e),createXMLExample=(e,t,r)=>{const a=sampleFromSchemaGeneric(e,t,r,!0);if(a)return"string"==typeof a?a:Ct()(a,{declaration:!0,indent:"\t"})},sampleFromSchema=(e,t,r)=>sampleFromSchemaGeneric(e,t,r,!1),resolver=(e,t,r)=>[e,JSON.stringify(t),JSON.stringify(r)],Mt=utils_memoizeN(createXMLExample,resolver),Tt=utils_memoizeN(sampleFromSchema,resolver),getSchemaObjectType=e=>immutableToJS(e)?.type??"string",Rt=[{when:/json/,shouldStringifyTypes:["string"]}],Jt=["object"],get_json_sample_schema=e=>(t,r,a,n)=>{const{fn:s}=e(),o=s.memoizedSampleFromSchema(t,r,n),l=typeof o,c=Rt.reduce(((e,t)=>t.when.test(a)?[...e,...t.shouldStringifyTypes]:e),Jt);return X()(c,(e=>e===l))?JSON.stringify(o,null,2):o},get_yaml_sample_schema=e=>(t,r,a,n)=>{const{fn:s}=e(),o=s.getJsonSampleSchema(t,r,a,n);let l;try{l=qe().dump(qe().load(o),{lineWidth:-1},{schema:je.JSON_SCHEMA}),"\n"===l[l.length-1]&&(l=l.slice(0,l.length-1))}catch(e){return console.error(e),"error: could not generate yaml example"}return l.replace(/\t/g,"  ")},get_xml_sample_schema=e=>(t,r,a)=>{const{fn:n}=e();if(t&&!t.xml&&(t.xml={}),t&&!t.xml.name){if(!t.$$ref&&(t.type||t.items||t.properties||t.additionalProperties))return'<?xml version="1.0" encoding="UTF-8"?>\n\x3c!-- XML example cannot be generated; root element name is undefined --\x3e';if(t.$$ref){let e=t.$$ref.match(/\S*\/(\S+)$/);t.xml.name=e[1]}}return n.memoizedCreateXMLExample(t,r,a)},get_sample_schema=e=>(t,r="",a={},n=void 0)=>{const{fn:s}=e();return"function"==typeof t?.toJS&&(t=t.toJS()),"function"==typeof n?.toJS&&(n=n.toJS()),/xml/.test(r)?s.getXmlSampleSchema(t,a,n):/(yaml|yml)/.test(r)?s.getYamlSampleSchema(t,a,r,n):s.getJsonSampleSchema(t,a,r,n)},json_schema_5_samples=({getSystem:e})=>{const t=get_json_sample_schema(e),r=get_yaml_sample_schema(e),a=get_xml_sample_schema(e),n=get_sample_schema(e);return{fn:{jsonSchema5:{inferSchema,sampleFromSchema,sampleFromSchemaGeneric,createXMLExample,memoizedSampleFromSchema:Tt,memoizedCreateXMLExample:Mt,getJsonSampleSchema:t,getYamlSampleSchema:r,getXmlSampleSchema:a,getSampleSchema:n,mergeJsonSchema},inferSchema,sampleFromSchema,sampleFromSchemaGeneric,createXMLExample,memoizedSampleFromSchema:Tt,memoizedCreateXMLExample:Mt,getJsonSampleSchema:t,getYamlSampleSchema:r,getXmlSampleSchema:a,getSampleSchema:n,mergeJsonSchema,getSchemaObjectType}}},$t=require("lodash/constant");var Vt=__webpack_require__.n($t);const Lt=["get","put","post","delete","options","head","patch","trace"],spec_selectors_state=e=>e||(0,k.Map)(),Dt=(0,_e.createSelector)(spec_selectors_state,(e=>e.get("lastError"))),Ut=(0,_e.createSelector)(spec_selectors_state,(e=>e.get("url"))),Kt=(0,_e.createSelector)(spec_selectors_state,(e=>e.get("spec")||"")),zt=(0,_e.createSelector)(spec_selectors_state,(e=>e.get("specSource")||"not-editor")),Bt=(0,_e.createSelector)(spec_selectors_state,(e=>e.get("json",(0,k.Map)()))),Ft=(0,_e.createSelector)(Bt,(e=>e.toJS())),Wt=(0,_e.createSelector)(spec_selectors_state,(e=>e.get("resolved",(0,k.Map)()))),specResolvedSubtree=(e,t)=>e.getIn(["resolvedSubtrees",...t],void 0),mergerFn=(e,t)=>k.Map.isMap(e)&&k.Map.isMap(t)?t.get("$$ref")?t:(0,k.OrderedMap)().mergeWith(mergerFn,e,t):t,Ht=(0,_e.createSelector)(spec_selectors_state,(e=>(0,k.OrderedMap)().mergeWith(mergerFn,e.get("json"),e.get("resolvedSubtrees")))),spec=e=>Bt(e),Xt=(0,_e.createSelector)(spec,(()=>!1)),Gt=(0,_e.createSelector)(spec,(e=>returnSelfOrNewMap(e&&e.get("info")))),Yt=(0,_e.createSelector)(spec,(e=>returnSelfOrNewMap(e&&e.get("externalDocs")))),Qt=(0,_e.createSelector)(Gt,(e=>e&&e.get("version"))),Zt=(0,_e.createSelector)(Qt,(e=>/v?([0-9]*)\.([0-9]*)\.([0-9]*)/i.exec(e).slice(1))),er=(0,_e.createSelector)(Ht,(e=>e.get("paths"))),tr=Vt()(["get","put","post","delete","options","head","patch"]),rr=(0,_e.createSelector)(er,(e=>{let t=(0,k.List)();return!k.Map.isMap(e)||e.isEmpty()||e.forEach(((e,r)=>{if(!e||!e.forEach)return{};e.forEach(((e,a)=>{Lt.indexOf(a)<0||(t=t.push((0,k.fromJS)({path:r,method:a,operation:e,id:`${a}-${r}`})))}))})),t})),ar=(0,_e.createSelector)(spec,(e=>(0,k.Set)(e.get("consumes")))),nr=(0,_e.createSelector)(spec,(e=>(0,k.Set)(e.get("produces")))),sr=(0,_e.createSelector)(spec,(e=>e.get("security",(0,k.List)()))),or=(0,_e.createSelector)(spec,(e=>e.get("securityDefinitions"))),findDefinition=(e,t)=>{const r=e.getIn(["resolvedSubtrees","definitions",t],null),a=e.getIn(["json","definitions",t],null);return r||a||null},lr=(0,_e.createSelector)(spec,(e=>{const t=e.get("definitions");return k.Map.isMap(t)?t:(0,k.Map)()})),cr=(0,_e.createSelector)(spec,(e=>e.get("basePath"))),ir=(0,_e.createSelector)(spec,(e=>e.get("host"))),pr=(0,_e.createSelector)(spec,(e=>e.get("schemes",(0,k.Map)()))),mr=(0,_e.createSelector)([rr,ar,nr],((e,t,r)=>e.map((e=>e.update("operation",(e=>k.Map.isMap(e)?e.withMutations((e=>(e.get("consumes")||e.update("consumes",(e=>(0,k.Set)(e).merge(t))),e.get("produces")||e.update("produces",(e=>(0,k.Set)(e).merge(r))),e))):(0,k.Map)())))))),ur=(0,_e.createSelector)(spec,(e=>{const t=e.get("tags",(0,k.List)());return k.List.isList(t)?t.filter((e=>k.Map.isMap(e))):(0,k.List)()})),tagDetails=(e,t)=>(ur(e)||(0,k.List)()).filter(k.Map.isMap).find((e=>e.get("name")===t),(0,k.Map)()),dr=(0,_e.createSelector)(mr,ur,((e,t)=>e.reduce(((e,t)=>{let r=(0,k.Set)(t.getIn(["operation","tags"]));return r.count()<1?e.update("default",(0,k.List)(),(e=>e.push(t))):r.reduce(((e,r)=>e.update(r,(0,k.List)(),(e=>e.push(t)))),e)}),t.reduce(((e,t)=>e.set(t.get("name"),(0,k.List)())),(0,k.OrderedMap)())))),selectors_taggedOperations=e=>({getConfigs:t})=>{let{tagsSorter:r,operationsSorter:a}=t();return dr(e).sortBy(((e,t)=>t),((e,t)=>{let a="function"==typeof r?r:pe.tagsSorter[r];return a?a(e,t):null})).map(((t,r)=>{let n="function"==typeof a?a:pe.operationsSorter[a],s=n?t.sort(n):t;return(0,k.Map)({tagDetails:tagDetails(e,r),operations:s})}))},hr=(0,_e.createSelector)(spec_selectors_state,(e=>e.get("responses",(0,k.Map)()))),gr=(0,_e.createSelector)(spec_selectors_state,(e=>e.get("requests",(0,k.Map)()))),yr=(0,_e.createSelector)(spec_selectors_state,(e=>e.get("mutatedRequests",(0,k.Map)()))),responseFor=(e,t,r)=>hr(e).getIn([t,r],null),requestFor=(e,t,r)=>gr(e).getIn([t,r],null),mutatedRequestFor=(e,t,r)=>yr(e).getIn([t,r],null),allowTryItOutFor=()=>!0,parameterWithMetaByIdentity=(e,t,r)=>{const a=Ht(e).getIn(["paths",...t,"parameters"],(0,k.OrderedMap)()),n=e.getIn(["meta","paths",...t,"parameters"],(0,k.OrderedMap)());return a.map((e=>{const t=n.get(`${r.get("in")}.${r.get("name")}`),a=n.get(`${r.get("in")}.${r.get("name")}.hash-${r.hashCode()}`);return(0,k.OrderedMap)().merge(e,t,a)})).find((e=>e.get("in")===r.get("in")&&e.get("name")===r.get("name")),(0,k.OrderedMap)())},parameterInclusionSettingFor=(e,t,r,a)=>{const n=`${a}.${r}`;return e.getIn(["meta","paths",...t,"parameter_inclusions",n],!1)},parameterWithMeta=(e,t,r,a)=>{const n=Ht(e).getIn(["paths",...t,"parameters"],(0,k.OrderedMap)()).find((e=>e.get("in")===a&&e.get("name")===r),(0,k.OrderedMap)());return parameterWithMetaByIdentity(e,t,n)},operationWithMeta=(e,t,r)=>{const a=Ht(e).getIn(["paths",t,r],(0,k.OrderedMap)()),n=e.getIn(["meta","paths",t,r],(0,k.OrderedMap)()),s=a.get("parameters",(0,k.List)()).map((a=>parameterWithMetaByIdentity(e,[t,r],a)));return(0,k.OrderedMap)().merge(a,n).set("parameters",s)};function getParameter(e,t,r,a){return t=t||[],e.getIn(["meta","paths",...t,"parameters"],(0,k.fromJS)([])).find((e=>k.Map.isMap(e)&&e.get("name")===r&&e.get("in")===a))||(0,k.Map)()}const fr=(0,_e.createSelector)(spec,(e=>{const t=e.get("host");return"string"==typeof t&&t.length>0&&"/"!==t[0]}));function parameterValues(e,t,r){return t=t||[],operationWithMeta(e,...t).get("parameters",(0,k.List)()).reduce(((e,t)=>{let a=r&&"body"===t.get("in")?t.get("value_xml"):t.get("value");return k.List.isList(a)&&(a=a.filter((e=>""!==e))),e.set(paramToIdentifier(t,{allowHashes:!1}),a)}),(0,k.fromJS)({}))}function parametersIncludeIn(e,t=""){if(k.List.isList(e))return e.some((e=>k.Map.isMap(e)&&e.get("in")===t))}function parametersIncludeType(e,t=""){if(k.List.isList(e))return e.some((e=>k.Map.isMap(e)&&e.get("type")===t))}function contentTypeValues(e,t){t=t||[];let r=Ht(e).getIn(["paths",...t],(0,k.fromJS)({})),a=e.getIn(["meta","paths",...t],(0,k.fromJS)({})),n=currentProducesFor(e,t);const s=r.get("parameters")||new k.List,o=a.get("consumes_value")?a.get("consumes_value"):parametersIncludeType(s,"file")?"multipart/form-data":parametersIncludeType(s,"formData")?"application/x-www-form-urlencoded":void 0;return(0,k.fromJS)({requestContentType:o,responseContentType:n})}function currentProducesFor(e,t){t=t||[];const r=Ht(e).getIn(["paths",...t],null);if(null===r)return;const a=e.getIn(["meta","paths",...t,"produces_value"],null),n=r.getIn(["produces",0],null);return a||n||"application/json"}function producesOptionsFor(e,t){t=t||[];const r=Ht(e),a=r.getIn(["paths",...t],null);if(null===a)return;const[n]=t,s=a.get("produces",null),o=r.getIn(["paths",n,"produces"],null),l=r.getIn(["produces"],null);return s||o||l}function consumesOptionsFor(e,t){t=t||[];const r=Ht(e),a=r.getIn(["paths",...t],null);if(null===a)return;const[n]=t,s=a.get("consumes",null),o=r.getIn(["paths",n,"consumes"],null),l=r.getIn(["consumes"],null);return s||o||l}const operationScheme=(e,t,r)=>{let a=e.get("url").match(/^([a-z][a-z0-9+\-.]*):/),n=Array.isArray(a)?a[1]:null;return e.getIn(["scheme",t,r])||e.getIn(["scheme","_defaultScheme"])||n||""},canExecuteScheme=(e,t,r)=>["http","https"].indexOf(operationScheme(e,t,r))>-1,validationErrors=(e,t)=>{t=t||[];const r=e.getIn(["meta","paths",...t,"parameters"],(0,k.fromJS)([])),a=[];if(0===r.length)return a;const getErrorsWithPaths=(e,t=[])=>{const getNestedErrorsWithPaths=(e,t)=>{const r=[...t,e.get("propKey")||e.get("index")];return k.Map.isMap(e.get("error"))?getErrorsWithPaths(e.get("error"),r):{error:e.get("error"),path:r}};return k.List.isList(e)?e.map((e=>k.Map.isMap(e)?getNestedErrorsWithPaths(e,t):{error:e,path:t})):getNestedErrorsWithPaths(e,t)};return r.forEach(((e,t)=>{const r=t.split(".").slice(1,-1).join("."),n=e.get("errors");if(n&&n.count()){getErrorsWithPaths(n).forEach((({error:e,path:t})=>{a.push(((e,t,r)=>`For '${r}'${(t=t.reduce(((e,t)=>"number"==typeof t?`${e}[${t}]`:e?`${e}.${t}`:t),""))?` at path '${t}'`:""}: ${e}.`)(e,t,r))}))}})),a},validateBeforeExecute=(e,t)=>0===validationErrors(e,t).length,getOAS3RequiredRequestBodyContentType=(e,t)=>{let r={requestBody:!1,requestContentType:{}},a=e.getIn(["resolvedSubtrees","paths",...t,"requestBody"],(0,k.fromJS)([]));return a.size<1||(a.getIn(["required"])&&(r.requestBody=a.getIn(["required"])),a.getIn(["content"]).entrySeq().forEach((e=>{const t=e[0];if(e[1].getIn(["schema","required"])){const a=e[1].getIn(["schema","required"]).toJS();r.requestContentType[t]=a}}))),r},isMediaTypeSchemaPropertiesEqual=(e,t,r,a)=>{if((r||a)&&r===a)return!0;let n=e.getIn(["resolvedSubtrees","paths",...t,"requestBody","content"],(0,k.fromJS)([]));if(n.size<2||!r||!a)return!1;let s=n.getIn([r,"schema","properties"],(0,k.fromJS)([])),o=n.getIn([a,"schema","properties"],(0,k.fromJS)([]));return!!s.equals(o)};function returnSelfOrNewMap(e){return k.Map.isMap(e)?e:new k.Map}const Sr=require("lodash/isString");var Er=__webpack_require__.n(Sr);const _r=require("lodash/debounce");var vr=__webpack_require__.n(_r);const wr=require("lodash/set");var br=__webpack_require__.n(wr);const Cr=require("lodash/fp/assocPath");var xr=__webpack_require__.n(Cr);const Or="spec_update_spec",Nr="spec_update_url",kr="spec_update_json",Ar="spec_update_param",Ir="spec_update_empty_param_inclusion",jr="spec_validate_param",qr="spec_set_response",Pr="spec_set_request",Mr="spec_set_mutated_request",Tr="spec_log_request",Rr="spec_clear_response",Jr="spec_clear_request",$r="spec_clear_validate_param",Vr="spec_update_operation_meta_value",Lr="spec_update_resolved",Dr="spec_update_resolved_subtree",Ur="set_scheme",toStr=e=>Er()(e)?e:"";function updateSpec(e){const t=toStr(e).replace(/\t/g,"  ");if("string"==typeof e)return{type:Or,payload:t}}function updateResolved(e){return{type:Lr,payload:e}}function updateUrl(e){return{type:Nr,payload:e}}function updateJsonSpec(e){return{type:kr,payload:e}}const parseToJson=e=>({specActions:t,specSelectors:r,errActions:a})=>{let{specStr:n}=r,s=null;try{e=e||n(),a.clear({source:"parser"}),s=qe().load(e,{schema:je.JSON_SCHEMA})}catch(e){return console.error(e),a.newSpecErr({source:"parser",level:"error",message:e.reason,line:e.mark&&e.mark.line?e.mark.line+1:void 0})}return s&&"object"==typeof s?t.updateJsonSpec(s):t.updateJsonSpec({})};let Kr=!1;const resolveSpec=(e,t)=>({specActions:r,specSelectors:a,errActions:n,fn:{fetch:s,resolve:o,AST:l={}},getConfigs:c})=>{Kr||(console.warn("specActions.resolveSpec is deprecated since v3.10.0 and will be removed in v4.0.0; use requestResolvedSubtree instead!"),Kr=!0);const{modelPropertyMacro:i,parameterMacro:p,requestInterceptor:m,responseInterceptor:u}=c();void 0===e&&(e=a.specJson()),void 0===t&&(t=a.url());let d=l.getLineNumberForPath?l.getLineNumberForPath:()=>{},h=a.specStr();return o({fetch:s,spec:e,baseDoc:String(new URL(t,document.baseURI)),modelPropertyMacro:i,parameterMacro:p,requestInterceptor:m,responseInterceptor:u}).then((({spec:e,errors:t})=>{if(n.clear({type:"thrown"}),Array.isArray(t)&&t.length>0){let e=t.map((e=>(console.error(e),e.line=e.fullPath?d(h,e.fullPath):null,e.path=e.fullPath?e.fullPath.join("."):null,e.level="error",e.type="thrown",e.source="resolver",Object.defineProperty(e,"message",{enumerable:!0,value:e.message}),e)));n.newThrownErrBatch(e)}return r.updateResolved(e)}))};let zr=[];const Br=vr()((()=>{const e=zr.reduce(((e,{path:t,system:r})=>(e.has(r)||e.set(r,[]),e.get(r).push(t),e)),new Map);zr=[],e.forEach((async(e,t)=>{if(!t)return void console.error("debResolveSubtrees: don't have a system to operate on, aborting.");if(!t.fn.resolveSubtree)return void console.error("Error: Swagger-Client did not provide a `resolveSubtree` method, doing nothing.");const{errActions:r,errSelectors:a,fn:{resolveSubtree:n,fetch:s,AST:o={}},specSelectors:l,specActions:c}=t,i=o.getLineNumberForPath??Vt()(void 0),p=l.specStr(),{modelPropertyMacro:m,parameterMacro:u,requestInterceptor:d,responseInterceptor:h}=t.getConfigs();try{const t=await e.reduce((async(e,t)=>{let{resultMap:o,specWithCurrentSubtrees:c}=await e;const{errors:g,spec:y}=await n(c,t,{baseDoc:String(new URL(l.url(),document.baseURI)),modelPropertyMacro:m,parameterMacro:u,requestInterceptor:d,responseInterceptor:h});if(a.allErrors().size&&r.clearBy((e=>"thrown"!==e.get("type")||"resolver"!==e.get("source")||!e.get("fullPath")?.every(((e,r)=>e===t[r]||void 0===t[r])))),Array.isArray(g)&&g.length>0){let e=g.map((e=>(e.line=e.fullPath?i(p,e.fullPath):null,e.path=e.fullPath?e.fullPath.join("."):null,e.level="error",e.type="thrown",e.source="resolver",Object.defineProperty(e,"message",{enumerable:!0,value:e.message}),e)));r.newThrownErrBatch(e)}return y&&l.isOAS3()&&"components"===t[0]&&"securitySchemes"===t[1]&&await Promise.all(Object.values(y).filter((e=>"openIdConnect"===e?.type)).map((async e=>{const t={url:e.openIdConnectUrl,requestInterceptor:d,responseInterceptor:h};try{const r=await s(t);r instanceof Error||r.status>=400?console.error(r.statusText+" "+t.url):e.openIdConnectData=JSON.parse(r.text)}catch(e){console.error(e)}}))),br()(o,t,y),c=xr()(t,y,c),{resultMap:o,specWithCurrentSubtrees:c}}),Promise.resolve({resultMap:(l.specResolvedSubtree([])||(0,k.Map)()).toJS(),specWithCurrentSubtrees:l.specJS()}));c.updateResolvedSubtree([],t.resultMap)}catch(e){console.error(e)}}))}),35),requestResolvedSubtree=e=>t=>{zr.find((({path:r,system:a})=>a===t&&r.toString()===e.toString()))||(zr.push({path:e,system:t}),Br())};function changeParam(e,t,r,a,n){return{type:Ar,payload:{path:e,value:a,paramName:t,paramIn:r,isXml:n}}}function changeParamByIdentity(e,t,r,a){return{type:Ar,payload:{path:e,param:t,value:r,isXml:a}}}const updateResolvedSubtree=(e,t)=>({type:Dr,payload:{path:e,value:t}}),invalidateResolvedSubtreeCache=()=>({type:Dr,payload:{path:[],value:(0,k.Map)()}}),validateParams=(e,t)=>({type:jr,payload:{pathMethod:e,isOAS3:t}}),updateEmptyParamInclusion=(e,t,r,a)=>({type:Ir,payload:{pathMethod:e,paramName:t,paramIn:r,includeEmptyValue:a}});function clearValidateParams(e){return{type:$r,payload:{pathMethod:e}}}function changeConsumesValue(e,t){return{type:Vr,payload:{path:e,value:t,key:"consumes_value"}}}function changeProducesValue(e,t){return{type:Vr,payload:{path:e,value:t,key:"produces_value"}}}const setResponse=(e,t,r)=>({payload:{path:e,method:t,res:r},type:qr}),setRequest=(e,t,r)=>({payload:{path:e,method:t,req:r},type:Pr}),setMutatedRequest=(e,t,r)=>({payload:{path:e,method:t,req:r},type:Mr}),logRequest=e=>({payload:e,type:Tr}),executeRequest=e=>({fn:t,specActions:r,specSelectors:a,getConfigs:n,oas3Selectors:s})=>{let{pathName:o,method:l,operation:c}=e,{requestInterceptor:i,responseInterceptor:p}=n(),m=c.toJS();if(c&&c.get("parameters")&&c.get("parameters").filter((e=>e&&!0===e.get("allowEmptyValue"))).forEach((t=>{if(a.parameterInclusionSettingFor([o,l],t.get("name"),t.get("in"))){e.parameters=e.parameters||{};const r=paramToValue(t,e.parameters);(!r||r&&0===r.size)&&(e.parameters[t.get("name")]="")}})),e.contextUrl=ue()(a.url()).toString(),m&&m.operationId?e.operationId=m.operationId:m&&o&&l&&(e.operationId=t.opId(m,o,l)),a.isOAS3()){const t=`${o}:${l}`;e.server=s.selectedServer(t)||s.selectedServer();const r=s.serverVariables({server:e.server,namespace:t}).toJS(),a=s.serverVariables({server:e.server}).toJS();e.serverVariables=Object.keys(r).length?r:a,e.requestContentType=s.requestContentType(o,l),e.responseContentType=s.responseContentType(o,l)||"*/*";const n=s.requestBodyValue(o,l),c=s.requestBodyInclusionSetting(o,l);n&&n.toJS?e.requestBody=n.map((e=>k.Map.isMap(e)?e.get("value"):e)).filter(((e,t)=>(Array.isArray(e)?0!==e.length:!isEmptyValue(e))||c.get(t))).toJS():e.requestBody=n}let u=Object.assign({},e);u=t.buildRequest(u),r.setRequest(e.pathName,e.method,u);e.requestInterceptor=async t=>{let a=await i.apply(void 0,[t]),n=Object.assign({},a);return r.setMutatedRequest(e.pathName,e.method,n),a},e.responseInterceptor=p;const d=Date.now();return t.execute(e).then((t=>{t.duration=Date.now()-d,r.setResponse(e.pathName,e.method,t)})).catch((t=>{"Failed to fetch"===t.message&&(t.name="",t.message='**Failed to fetch.**  \n**Possible Reasons:** \n  - CORS \n  - Network Failure \n  - URL scheme must be "http" or "https" for CORS request.'),r.setResponse(e.pathName,e.method,{error:!0,err:t})}))},actions_execute=({path:e,method:t,...r}={})=>a=>{let{fn:{fetch:n},specSelectors:s,specActions:o}=a,l=s.specJsonWithResolvedSubtrees().toJS(),c=s.operationScheme(e,t),{requestContentType:i,responseContentType:p}=s.contentTypeValues([e,t]).toJS(),m=/xml/i.test(i),u=s.parameterValues([e,t],m).toJS();return o.executeRequest({...r,fetch:n,spec:l,pathName:e,method:t,parameters:u,requestContentType:i,scheme:c,responseContentType:p})};function clearResponse(e,t){return{type:Rr,payload:{path:e,method:t}}}function clearRequest(e,t){return{type:Jr,payload:{path:e,method:t}}}function setScheme(e,t,r){return{type:Ur,payload:{scheme:e,path:t,method:r}}}const Fr={[Or]:(e,t)=>"string"==typeof t.payload?e.set("spec",t.payload):e,[Nr]:(e,t)=>e.set("url",t.payload+""),[kr]:(e,t)=>e.set("json",fromJSOrdered(t.payload)),[Lr]:(e,t)=>e.setIn(["resolved"],fromJSOrdered(t.payload)),[Dr]:(e,t)=>{const{value:r,path:a}=t.payload;return e.setIn(["resolvedSubtrees",...a],fromJSOrdered(r))},[Ar]:(e,{payload:t})=>{let{path:r,paramName:a,paramIn:n,param:s,value:o,isXml:l}=t,c=s?paramToIdentifier(s):`${n}.${a}`;const i=l?"value_xml":"value";return e.setIn(["meta","paths",...r,"parameters",c,i],(0,k.fromJS)(o))},[Ir]:(e,{payload:t})=>{let{pathMethod:r,paramName:a,paramIn:n,includeEmptyValue:s}=t;if(!a||!n)return console.warn("Warning: UPDATE_EMPTY_PARAM_INCLUSION could not generate a paramKey."),e;const o=`${n}.${a}`;return e.setIn(["meta","paths",...r,"parameter_inclusions",o],s)},[jr]:(e,{payload:{pathMethod:t,isOAS3:r}})=>{const a=Ht(e).getIn(["paths",...t]),n=parameterValues(e,t).toJS();return e.updateIn(["meta","paths",...t,"parameters"],(0,k.fromJS)({}),(s=>a.get("parameters",(0,k.List)()).reduce(((a,s)=>{const o=paramToValue(s,n),l=parameterInclusionSettingFor(e,t,s.get("name"),s.get("in")),c=((e,t,{isOAS3:r=!1,bypassRequiredCheck:a=!1}={})=>{let n=e.get("required"),{schema:s,parameterContentMediaType:o}=getParameterSchema(e,{isOAS3:r});return validateValueBySchema(t,s,n,a,o)})(s,o,{bypassRequiredCheck:l,isOAS3:r});return a.setIn([paramToIdentifier(s),"errors"],(0,k.fromJS)(c))}),s)))},[$r]:(e,{payload:{pathMethod:t}})=>e.updateIn(["meta","paths",...t,"parameters"],(0,k.fromJS)([]),(e=>e.map((e=>e.set("errors",(0,k.fromJS)([])))))),[qr]:(e,{payload:{res:t,path:r,method:a}})=>{let n;n=t.error?Object.assign({error:!0,name:t.err.name,message:t.err.message,statusCode:t.err.statusCode},t.err.response):t,n.headers=n.headers||{};let s=e.setIn(["responses",r,a],fromJSOrdered(n));return K.Blob&&n.data instanceof K.Blob&&(s=s.setIn(["responses",r,a,"text"],n.data)),s},[Pr]:(e,{payload:{req:t,path:r,method:a}})=>e.setIn(["requests",r,a],fromJSOrdered(t)),[Mr]:(e,{payload:{req:t,path:r,method:a}})=>e.setIn(["mutatedRequests",r,a],fromJSOrdered(t)),[Vr]:(e,{payload:{path:t,value:r,key:a}})=>{let n=["paths",...t],s=["meta","paths",...t];return e.getIn(["json",...n])||e.getIn(["resolved",...n])||e.getIn(["resolvedSubtrees",...n])?e.setIn([...s,a],(0,k.fromJS)(r)):e},[Rr]:(e,{payload:{path:t,method:r}})=>e.deleteIn(["responses",t,r]),[Jr]:(e,{payload:{path:t,method:r}})=>e.deleteIn(["requests",t,r]),[Ur]:(e,{payload:{scheme:t,path:r,method:a}})=>r&&a?e.setIn(["scheme",r,a],t):r||a?void 0:e.setIn(["scheme","_defaultScheme"],t)},wrap_actions_updateSpec=(e,{specActions:t})=>(...r)=>{e(...r),t.parseToJson(...r)},wrap_actions_updateJsonSpec=(e,{specActions:t})=>(...r)=>{e(...r),t.invalidateResolvedSubtreeCache();const[a]=r,n=Fe()(a,["paths"])||{};Object.keys(n).forEach((e=>{const r=Fe()(n,[e]);wt()(r)&&r.$ref&&t.requestResolvedSubtree(["paths",e])})),t.requestResolvedSubtree(["components","securitySchemes"])},wrap_actions_executeRequest=(e,{specActions:t})=>r=>(t.logRequest(r),e(r)),wrap_actions_validateParams=(e,{specSelectors:t})=>r=>e(r,t.isOAS3()),plugins_spec=()=>({statePlugins:{spec:{wrapActions:{...S},reducers:{...Fr},actions:{...f},selectors:{...y}}}}),Wr=require("swagger-client/es/resolver/strategies/generic");var Hr=__webpack_require__.n(Wr);const Xr=require("swagger-client/es/resolver/strategies/openapi-2");var Gr=__webpack_require__.n(Xr);const Yr=require("swagger-client/es/resolver/strategies/openapi-3-0");var Qr=__webpack_require__.n(Yr);const Zr=require("swagger-client/es/resolver/strategies/openapi-3-1-apidom");var ea=__webpack_require__.n(Zr);const ta=require("swagger-client/es/resolver"),ra=require("swagger-client/es/execute"),aa=require("swagger-client/es/http");var na=__webpack_require__.n(aa);const sa=require("swagger-client/es/subtree-resolver"),oa=require("swagger-client/es/helpers"),configs_wrap_actions_loaded=(e,t)=>(...r)=>{e(...r);const a=t.getConfigs().withCredentials;t.fn.fetch.withCredentials=a};function swagger_client({configs:e,getConfigs:t}){return{fn:{fetch:(0,aa.makeHttp)(na(),e.preFetch,e.postFetch),buildRequest:ra.buildRequest,execute:ra.execute,resolve:(0,ta.makeResolve)({strategies:[ea(),Qr(),Gr(),Hr()]}),resolveSubtree:async(e,r,a={})=>{const n=t(),s={modelPropertyMacro:n.modelPropertyMacro,parameterMacro:n.parameterMacro,requestInterceptor:n.requestInterceptor,responseInterceptor:n.responseInterceptor,strategies:[ea(),Qr(),Gr(),Hr()]};return(0,sa.makeResolveSubtree)(s)(e,r,a)},serializeRes:aa.serializeRes,opId:oa.opId},statePlugins:{configs:{wrapActions:{loaded:configs_wrap_actions_loaded}}}}}function util(){return{fn:{shallowEqualKeys,sanitizeUrl}}}const la=require("react-dom");var ca=__webpack_require__.n(la);const ia=require("react-redux"),pa=require("lodash/identity");var ma=__webpack_require__.n(pa);const withSystem=e=>t=>{const{fn:r}=e();class WithSystem extends x.Component{render(){return O().createElement(t,Qe()({},e(),this.props,this.context))}}return WithSystem.displayName=`WithSystem(${r.getDisplayName(t)})`,WithSystem},withRoot=(e,t)=>r=>{const{fn:a}=e();class WithRoot extends x.Component{render(){return O().createElement(ia.Provider,{store:t},O().createElement(r,Qe()({},this.props,this.context)))}}return WithRoot.displayName=`WithRoot(${a.getDisplayName(r)})`,WithRoot},withConnect=(e,t,r)=>(0,N.compose)(r?withRoot(e,r):ma(),(0,ia.connect)(((r,a)=>{const n={...a,...e()},s=t.prototype?.mapStateToProps||(e=>({state:e}));return s(r,n)})),withSystem(e))(t),handleProps=(e,t,r,a)=>{for(const n in t){const s=t[n];"function"==typeof s&&s(r[n],a[n],e())}},withMappedContainer=(e,t,r)=>(t,a)=>{const{fn:n}=e(),s=r(t,"root");class WithMappedContainer extends x.Component{constructor(t,r){super(t,r),handleProps(e,a,t,{})}UNSAFE_componentWillReceiveProps(t){handleProps(e,a,t,this.props)}render(){const e=ke()(this.props,a?Object.keys(a):[]);return O().createElement(s,e)}}return WithMappedContainer.displayName=`WithMappedContainer(${n.getDisplayName(s)})`,WithMappedContainer},render=(e,t,r,a)=>n=>{const s=r(e,t,a)("App","root"),{createRoot:o}=ca();o(n).render(O().createElement(s,null))},getComponent=(e,t,r)=>(a,n,s={})=>{if("string"!=typeof a)throw new TypeError("Need a string, to fetch a component. Was given a "+typeof a);const o=r(a);return o?n?"root"===n?withConnect(e,o,t()):withConnect(e,o):o:(s.failSilently||e().log.warn("Could not find component:",a),null)},getDisplayName=e=>e.displayName||e.name||"Component",view=({getComponents:e,getStore:t,getSystem:r})=>{const a=(n=getComponent(r,t,e),ie(n,((...e)=>JSON.stringify(e))));var n;const s=(e=>utils_memoizeN(e,((...e)=>e)))(withMappedContainer(r,0,a));return{rootInjects:{getComponent:a,makeMappedContainer:s,render:render(r,t,getComponent,e)},fn:{getDisplayName}}},view_legacy=({React:e,getSystem:t,getStore:r,getComponents:a})=>{const n={},s=parseInt(e?.version,10);return s>=16&&s<18&&(n.render=((e,t,r,a)=>n=>{const s=r(e,t,a)("App","root");ca().render(O().createElement(s,null),n)})(t,r,getComponent,a)),{rootInjects:n}};function downloadUrlPlugin(e){let{fn:t}=e;const r={download:e=>({errActions:r,specSelectors:a,specActions:n,getConfigs:s})=>{let{fetch:o}=t;const l=s();function next(t){if(t instanceof Error||t.status>=400)return n.updateLoadingStatus("failed"),r.newThrownErr(Object.assign(new Error((t.message||t.statusText)+" "+e),{source:"fetch"})),void(!t.status&&t instanceof Error&&function checkPossibleFailReasons(){try{let t;if("URL"in K?t=new URL(e):(t=document.createElement("a"),t.href=e),"https:"!==t.protocol&&"https:"===K.location.protocol){const e=Object.assign(new Error(`Possible mixed-content issue? The page was loaded over https:// but a ${t.protocol}// URL was specified. Check that you are not attempting to load mixed content.`),{source:"fetch"});return void r.newThrownErr(e)}if(t.origin!==K.location.origin){const e=Object.assign(new Error(`Possible cross-origin (CORS) issue? The URL origin (${t.origin}) does not match the page (${K.location.origin}). Check the server returns the correct 'Access-Control-Allow-*' headers.`),{source:"fetch"});r.newThrownErr(e)}}catch(e){return}}());n.updateLoadingStatus("success"),n.updateSpec(t.text),a.url()!==e&&n.updateUrl(e)}e=e||a.url(),n.updateLoadingStatus("loading"),r.clear({source:"fetch"}),o({url:e,loadSpec:!0,requestInterceptor:l.requestInterceptor||(e=>e),responseInterceptor:l.responseInterceptor||(e=>e),credentials:"same-origin",headers:{Accept:"application/json,*/*"}}).then(next,next)},updateLoadingStatus:e=>{let t=[null,"loading","failed","success","failedConfig"];return-1===t.indexOf(e)&&console.error(`Error: ${e} is not one of ${JSON.stringify(t)}`),{type:"spec_update_loading_status",payload:e}}};let a={loadingStatus:(0,_e.createSelector)((e=>e||(0,k.Map)()),(e=>e.get("loadingStatus")||null))};return{statePlugins:{spec:{actions:r,reducers:{spec_update_loading_status:(e,t)=>"string"==typeof t.payload?e.set("loadingStatus",t.payload):e},selectors:a}}}}const ua=require("react-syntax-highlighter/dist/esm/light");var da=__webpack_require__.n(ua);const ha=require("react-syntax-highlighter/dist/esm/languages/hljs/javascript");var ga=__webpack_require__.n(ha);const ya=require("react-syntax-highlighter/dist/esm/languages/hljs/json");var fa=__webpack_require__.n(ya);const Sa=require("react-syntax-highlighter/dist/esm/languages/hljs/xml");var Ea=__webpack_require__.n(Sa);const _a=require("react-syntax-highlighter/dist/esm/languages/hljs/bash");var va=__webpack_require__.n(_a);const wa=require("react-syntax-highlighter/dist/esm/languages/hljs/yaml");var ba=__webpack_require__.n(wa);const Ca=require("react-syntax-highlighter/dist/esm/languages/hljs/http");var xa=__webpack_require__.n(Ca);const Oa=require("react-syntax-highlighter/dist/esm/languages/hljs/powershell");var Na=__webpack_require__.n(Oa);const after_load=()=>{da().registerLanguage("json",fa()),da().registerLanguage("js",ga()),da().registerLanguage("xml",Ea()),da().registerLanguage("yaml",ba()),da().registerLanguage("http",xa()),da().registerLanguage("bash",va()),da().registerLanguage("powershell",Na()),da().registerLanguage("javascript",ga())},ka=require("react-syntax-highlighter/dist/esm/styles/hljs/agate");var Aa=__webpack_require__.n(ka);const Ia=require("react-syntax-highlighter/dist/esm/styles/hljs/arta");var ja=__webpack_require__.n(Ia);const qa=require("react-syntax-highlighter/dist/esm/styles/hljs/monokai");var Pa=__webpack_require__.n(qa);const Ma=require("react-syntax-highlighter/dist/esm/styles/hljs/nord");var Ta=__webpack_require__.n(Ma);const Ra=require("react-syntax-highlighter/dist/esm/styles/hljs/obsidian");var Ja=__webpack_require__.n(Ra);const $a=require("react-syntax-highlighter/dist/esm/styles/hljs/tomorrow-night");var Va=__webpack_require__.n($a);const La=require("react-syntax-highlighter/dist/esm/styles/hljs/idea");var Da=__webpack_require__.n(La);const Ua={agate:Aa(),arta:ja(),monokai:Pa(),nord:Ta(),obsidian:Ja(),"tomorrow-night":Va(),idea:Da()},Ka=Aa(),components_SyntaxHighlighter=({language:e,className:t="",getConfigs:r,syntaxHighlighting:a={},children:n=""})=>{const s=r().syntaxHighlight.theme,{styles:o,defaultStyle:l}=a,c=o?.[s]??l;return O().createElement(da(),{language:e,className:t,style:c},n)},za=require("js-file-download");var Ba=__webpack_require__.n(za);const components_HighlightCode=({fileName:e="response.txt",className:t,downloadable:r,getComponent:a,canCopy:n,language:s,children:o})=>{const l=(0,x.useRef)(null),c=a("SyntaxHighlighter",!0),handlePreventYScrollingBeyondElement=e=>{const{target:t,deltaY:r}=e,{scrollHeight:a,offsetHeight:n,scrollTop:s}=t;a>n&&(0===s&&r<0||n+s>=a&&r>0)&&e.preventDefault()};return(0,x.useEffect)((()=>{const e=Array.from(l.current.childNodes).filter((e=>!!e.nodeType&&e.classList.contains("microlight")));return e.forEach((e=>e.addEventListener("mousewheel",handlePreventYScrollingBeyondElement,{passive:!1}))),()=>{e.forEach((e=>e.removeEventListener("mousewheel",handlePreventYScrollingBeyondElement)))}}),[o,t,s]),O().createElement("div",{className:"highlight-code",ref:l},n&&O().createElement("div",{className:"copy-to-clipboard"},O().createElement(mt.CopyToClipboard,{text:o},O().createElement("button",null))),r?O().createElement("button",{className:"download-contents",onClick:()=>{Ba()(o,e)}},"Download"):null,O().createElement(c,{language:s,className:pt()(t,"microlight"),renderPlainText:({children:e,PlainTextViewer:r})=>O().createElement(r,{className:t},e)},o))},components_PlainTextViewer=({className:e="",children:t})=>O().createElement("pre",{className:pt()("microlight",e)},t),wrap_components_SyntaxHighlighter=(e,t)=>({renderPlainText:r,children:a,...n})=>{const s=t.getConfigs().syntaxHighlight.activated,o=t.getComponent("PlainTextViewer");return s||"function"!=typeof r?s?O().createElement(e,n,a):O().createElement(o,null,a):r({children:a,PlainTextViewer:o})},SyntaxHighlightingPlugin1=()=>({afterLoad:after_load,rootInjects:{syntaxHighlighting:{styles:Ua,defaultStyle:Ka}},components:{SyntaxHighlighter:components_SyntaxHighlighter,HighlightCode:components_HighlightCode,PlainTextViewer:components_PlainTextViewer}}),SyntaxHighlightingPlugin2=()=>({wrapComponents:{SyntaxHighlighter:wrap_components_SyntaxHighlighter}}),syntax_highlighting=()=>[SyntaxHighlightingPlugin1,SyntaxHighlightingPlugin2],versions_after_load=()=>{const{GIT_DIRTY:e,GIT_COMMIT:t,PACKAGE_VERSION:r,BUILD_TIME:a}={PACKAGE_VERSION:"5.26.2",GIT_COMMIT:"g60dee8b6",GIT_DIRTY:!0,BUILD_TIME:"Wed, 16 Jul 2025 12:18:09 GMT"};K.versions=K.versions||{},K.versions.swaggerUI={version:r,gitRevision:t,gitDirty:e,buildTimestamp:a}},versions=()=>({afterLoad:versions_after_load}),Fa=require("lodash/zipObject");var Wa=__webpack_require__.n(Fa);const Ha=console.error,withErrorBoundary=e=>t=>{const{getComponent:r,fn:a}=e(),n=r("ErrorBoundary"),s=a.getDisplayName(t);class WithErrorBoundary extends x.Component{render(){return O().createElement(n,{targetName:s,getComponent:r,fn:a},O().createElement(t,Qe()({},this.props,this.context)))}}var o;return WithErrorBoundary.displayName=`WithErrorBoundary(${s})`,(o=t).prototype&&o.prototype.isReactComponent&&(WithErrorBoundary.prototype.mapStateToProps=t.prototype.mapStateToProps),WithErrorBoundary},fallback=({name:e})=>O().createElement("div",{className:"fallback"},"😱 ",O().createElement("i",null,"Could not render ","t"===e?"this component":e,", see the console."));class ErrorBoundary extends x.Component{static defaultProps={targetName:"this component",getComponent:()=>fallback,fn:{componentDidCatch:Ha},children:null};static getDerivedStateFromError(e){return{hasError:!0,error:e}}constructor(...e){super(...e),this.state={hasError:!1,error:null}}componentDidCatch(e,t){this.props.fn.componentDidCatch(e,t)}render(){const{getComponent:e,targetName:t,children:r}=this.props;if(this.state.hasError){const r=e("Fallback");return O().createElement(r,{name:t})}return r}}const Xa=ErrorBoundary,safe_render=({componentList:e=[],fullOverride:t=!1}={})=>({getSystem:r})=>{const a=t?e:["App","BaseLayout","VersionPragmaFilter","InfoContainer","ServersContainer","SchemesContainer","AuthorizeBtnContainer","FilterContainer","Operations","OperationContainer","parameters","responses","OperationServers","Models","ModelWrapper",...e],n=Wa()(a,Array(a.length).fill(((e,{fn:t})=>t.withErrorBoundary(e))));return{fn:{componentDidCatch:Ha,withErrorBoundary:withErrorBoundary(r)},components:{ErrorBoundary:Xa,Fallback:fallback},wrapComponents:n}};class App extends O().Component{getLayout(){const{getComponent:e,layoutSelectors:t}=this.props,r=t.current(),a=e(r,!0);return a||(()=>O().createElement("h1",null,' No layout defined for "',r,'" '))}render(){const e=this.getLayout();return O().createElement(e,null)}}const Ga=App;class AuthorizationPopup extends O().Component{close=()=>{let{authActions:e}=this.props;e.showDefinitions(!1)};render(){let{authSelectors:e,authActions:t,getComponent:r,errSelectors:a,specSelectors:n,fn:{AST:s={}}}=this.props,o=e.shownDefinitions();const l=r("auths"),c=r("CloseIcon");return O().createElement("div",{className:"dialog-ux"},O().createElement("div",{className:"backdrop-ux"}),O().createElement("div",{className:"modal-ux"},O().createElement("div",{className:"modal-dialog-ux"},O().createElement("div",{className:"modal-ux-inner"},O().createElement("div",{className:"modal-ux-header"},O().createElement("h3",null,"Available authorizations"),O().createElement("button",{type:"button",className:"close-modal",onClick:this.close},O().createElement(c,null))),O().createElement("div",{className:"modal-ux-content"},o.valueSeq().map(((o,c)=>O().createElement(l,{key:c,AST:s,definitions:o,getComponent:r,errSelectors:a,authSelectors:e,authActions:t,specSelectors:n}))))))))}}class AuthorizeBtn extends O().Component{render(){let{isAuthorized:e,showPopup:t,onClick:r,getComponent:a}=this.props;const n=a("authorizationPopup",!0),s=a("LockAuthIcon",!0),o=a("UnlockAuthIcon",!0);return O().createElement("div",{className:"auth-wrapper"},O().createElement("button",{className:e?"btn authorize locked":"btn authorize unlocked",onClick:r},O().createElement("span",null,"Authorize"),e?O().createElement(s,null):O().createElement(o,null)),t&&O().createElement(n,null))}}class AuthorizeBtnContainer extends O().Component{render(){const{authActions:e,authSelectors:t,specSelectors:r,getComponent:a}=this.props,n=r.securityDefinitions(),s=t.definitionsToAuthorize(),o=a("authorizeBtn");return n?O().createElement(o,{onClick:()=>e.showDefinitions(s),isAuthorized:!!t.authorized().size,showPopup:!!t.shownDefinitions(),getComponent:a}):null}}class AuthorizeOperationBtn extends O().Component{onClick=e=>{e.stopPropagation();let{onClick:t}=this.props;t&&t()};render(){let{isAuthorized:e,getComponent:t}=this.props;const r=t("LockAuthOperationIcon",!0),a=t("UnlockAuthOperationIcon",!0);return O().createElement("button",{className:"authorization__btn","aria-label":e?"authorization button locked":"authorization button unlocked",onClick:this.onClick},e?O().createElement(r,{className:"locked"}):O().createElement(a,{className:"unlocked"}))}}class Auths extends O().Component{constructor(e,t){super(e,t),this.state={}}onAuthChange=e=>{let{name:t}=e;this.setState({[t]:e})};submitAuth=e=>{e.preventDefault();let{authActions:t}=this.props;t.authorizeWithPersistOption(this.state)};logoutClick=e=>{e.preventDefault();let{authActions:t,definitions:r}=this.props,a=r.map(((e,t)=>t)).toArray();this.setState(a.reduce(((e,t)=>(e[t]="",e)),{})),t.logoutWithPersistOption(a)};close=e=>{e.preventDefault();let{authActions:t}=this.props;t.showDefinitions(!1)};render(){let{definitions:e,getComponent:t,authSelectors:r,errSelectors:a}=this.props;const n=t("AuthItem"),s=t("oauth2",!0),o=t("Button");let l=r.authorized(),c=e.filter(((e,t)=>!!l.get(t))),i=e.filter((e=>"oauth2"!==e.get("type"))),p=e.filter((e=>"oauth2"===e.get("type")));return O().createElement("div",{className:"auth-container"},!!i.size&&O().createElement("form",{onSubmit:this.submitAuth},i.map(((e,s)=>O().createElement(n,{key:s,schema:e,name:s,getComponent:t,onAuthChange:this.onAuthChange,authorized:l,errSelectors:a,authSelectors:r}))).toArray(),O().createElement("div",{className:"auth-btn-wrapper"},i.size===c.size?O().createElement(o,{className:"btn modal-btn auth",onClick:this.logoutClick,"aria-label":"Remove authorization"},"Logout"):O().createElement(o,{type:"submit",className:"btn modal-btn auth authorize","aria-label":"Apply credentials"},"Authorize"),O().createElement(o,{className:"btn modal-btn auth btn-done",onClick:this.close},"Close"))),p&&p.size?O().createElement("div",null,O().createElement("div",{className:"scope-def"},O().createElement("p",null,"Scopes are used to grant an application different levels of access to data on behalf of the end user. Each API may declare one or more scopes."),O().createElement("p",null,"API requires the following scopes. Select which ones you want to grant to Swagger UI.")),e.filter((e=>"oauth2"===e.get("type"))).map(((e,t)=>O().createElement("div",{key:t},O().createElement(s,{authorized:l,schema:e,name:t})))).toArray()):null)}}class auth_item_Auths extends O().Component{render(){let{schema:e,name:t,getComponent:r,onAuthChange:a,authorized:n,errSelectors:s,authSelectors:o}=this.props;const l=r("apiKeyAuth"),c=r("basicAuth");let i;const p=e.get("type");switch(p){case"apiKey":i=O().createElement(l,{key:t,schema:e,name:t,errSelectors:s,authorized:n,getComponent:r,onChange:a,authSelectors:o});break;case"basic":i=O().createElement(c,{key:t,schema:e,name:t,errSelectors:s,authorized:n,getComponent:r,onChange:a,authSelectors:o});break;default:i=O().createElement("div",{key:t},"Unknown security definition type ",p)}return O().createElement("div",{key:`${t}-jump`},i)}}class AuthError extends O().Component{render(){let{error:e}=this.props,t=e.get("level"),r=e.get("message"),a=e.get("source");return O().createElement("div",{className:"errors"},O().createElement("b",null,a," ",t),O().createElement("span",null,r))}}class ApiKeyAuth extends O().Component{constructor(e,t){super(e,t);let{name:r,schema:a}=this.props,n=this.getValue();this.state={name:r,schema:a,value:n}}getValue(){let{name:e,authorized:t}=this.props;return t&&t.getIn([e,"value"])}onChange=e=>{let{onChange:t}=this.props,r=e.target.value,a=Object.assign({},this.state,{value:r});this.setState(a),t(a)};render(){let{schema:e,getComponent:t,errSelectors:r,name:a,authSelectors:n}=this.props;const s=t("Input"),o=t("Row"),l=t("Col"),c=t("authError"),i=t("Markdown",!0),p=t("JumpToPath",!0),m=n.selectAuthPath(a);let u=this.getValue(),d=r.allErrors().filter((e=>e.get("authId")===a));return O().createElement("div",null,O().createElement("h4",null,O().createElement("code",null,a||e.get("name"))," (apiKey)",O().createElement(p,{path:m})),u&&O().createElement("h6",null,"Authorized"),O().createElement(o,null,O().createElement(i,{source:e.get("description")})),O().createElement(o,null,O().createElement("p",null,"Name: ",O().createElement("code",null,e.get("name")))),O().createElement(o,null,O().createElement("p",null,"In: ",O().createElement("code",null,e.get("in")))),O().createElement(o,null,O().createElement("label",{htmlFor:"api_key_value"},"Value:"),u?O().createElement("code",null," ****** "):O().createElement(l,null,O().createElement(s,{id:"api_key_value",type:"text",onChange:this.onChange,autoFocus:!0}))),d.valueSeq().map(((e,t)=>O().createElement(c,{error:e,key:t}))))}}class BasicAuth extends O().Component{constructor(e,t){super(e,t);let{schema:r,name:a}=this.props,n=this.getValue().username;this.state={name:a,schema:r,value:n?{username:n}:{}}}getValue(){let{authorized:e,name:t}=this.props;return e&&e.getIn([t,"value"])||{}}onChange=e=>{let{onChange:t}=this.props,{value:r,name:a}=e.target,n=this.state.value;n[a]=r,this.setState({value:n}),t(this.state)};render(){let{schema:e,getComponent:t,name:r,errSelectors:a,authSelectors:n}=this.props;const s=t("Input"),o=t("Row"),l=t("Col"),c=t("authError"),i=t("JumpToPath",!0),p=t("Markdown",!0),m=n.selectAuthPath(r);let u=this.getValue().username,d=a.allErrors().filter((e=>e.get("authId")===r));return O().createElement("div",null,O().createElement("h4",null,"Basic authorization",O().createElement(i,{path:m})),u&&O().createElement("h6",null,"Authorized"),O().createElement(o,null,O().createElement(p,{source:e.get("description")})),O().createElement(o,null,O().createElement("label",{htmlFor:"auth_username"},"Username:"),u?O().createElement("code",null," ",u," "):O().createElement(l,null,O().createElement(s,{id:"auth_username",type:"text",required:"required",name:"username",onChange:this.onChange,autoFocus:!0}))),O().createElement(o,null,O().createElement("label",{htmlFor:"auth_password"},"Password:"),u?O().createElement("code",null," ****** "):O().createElement(l,null,O().createElement(s,{id:"auth_password",autoComplete:"new-password",name:"password",type:"password",onChange:this.onChange}))),d.valueSeq().map(((e,t)=>O().createElement(c,{error:e,key:t}))))}}function Example(e){const{example:t,showValue:r,getComponent:a}=e,n=a("Markdown",!0),s=a("HighlightCode",!0);return t&&k.Map.isMap(t)?O().createElement("div",{className:"example"},t.get("description")?O().createElement("section",{className:"example__section"},O().createElement("div",{className:"example__section-header"},"Example Description"),O().createElement("p",null,O().createElement(n,{source:t.get("description")}))):null,r&&t.has("value")?O().createElement("section",{className:"example__section"},O().createElement("div",{className:"example__section-header"},"Example Value"),O().createElement(s,null,stringify(t.get("value")))):null):null}class ExamplesSelect extends O().PureComponent{static defaultProps={examples:(0,k.Map)({}),onSelect:(...e)=>console.log("DEBUG: ExamplesSelect was not given an onSelect callback",...e),currentExampleKey:null,showLabels:!0};_onSelect=(e,{isSyntheticChange:t=!1}={})=>{"function"==typeof this.props.onSelect&&this.props.onSelect(e,{isSyntheticChange:t})};_onDomSelect=e=>{if("function"==typeof this.props.onSelect){const t=e.target.selectedOptions[0].getAttribute("value");this._onSelect(t,{isSyntheticChange:!1})}};getCurrentExample=()=>{const{examples:e,currentExampleKey:t}=this.props,r=e.get(t),a=e.keySeq().first(),n=e.get(a);return r||n||(0,k.Map)({})};componentDidMount(){const{onSelect:e,examples:t}=this.props;if("function"==typeof e){const e=t.first(),r=t.keyOf(e);this._onSelect(r,{isSyntheticChange:!0})}}UNSAFE_componentWillReceiveProps(e){const{currentExampleKey:t,examples:r}=e;if(r!==this.props.examples&&!r.has(t)){const e=r.first(),t=r.keyOf(e);this._onSelect(t,{isSyntheticChange:!0})}}render(){const{examples:e,currentExampleKey:t,isValueModified:r,isModifiedValueAvailable:a,showLabels:n}=this.props;return O().createElement("div",{className:"examples-select"},n?O().createElement("span",{className:"examples-select__section-label"},"Examples: "):null,O().createElement("select",{className:"examples-select-element",onChange:this._onDomSelect,value:a&&r?"__MODIFIED__VALUE__":t||""},a?O().createElement("option",{value:"__MODIFIED__VALUE__"},"[Modified value]"):null,e.map(((e,t)=>O().createElement("option",{key:t,value:t},k.Map.isMap(e)&&e.get("summary")||t))).valueSeq()))}}const stringifyUnlessList=e=>k.List.isList(e)?e:stringify(e);class ExamplesSelectValueRetainer extends O().PureComponent{static defaultProps={userHasEditedBody:!1,examples:(0,k.Map)({}),currentNamespace:"__DEFAULT__NAMESPACE__",setRetainRequestBodyValueFlag:()=>{},onSelect:(...e)=>console.log("ExamplesSelectValueRetainer: no `onSelect` function was provided",...e),updateValue:(...e)=>console.log("ExamplesSelectValueRetainer: no `updateValue` function was provided",...e)};constructor(e){super(e);const t=this._getCurrentExampleValue();this.state={[e.currentNamespace]:(0,k.Map)({lastUserEditedValue:this.props.currentUserInputValue,lastDownstreamValue:t,isModifiedValueSelected:this.props.userHasEditedBody||this.props.currentUserInputValue!==t})}}componentWillUnmount(){this.props.setRetainRequestBodyValueFlag(!1)}_getStateForCurrentNamespace=()=>{const{currentNamespace:e}=this.props;return(this.state[e]||(0,k.Map)()).toObject()};_setStateForCurrentNamespace=e=>{const{currentNamespace:t}=this.props;return this._setStateForNamespace(t,e)};_setStateForNamespace=(e,t)=>{const r=(this.state[e]||(0,k.Map)()).mergeDeep(t);return this.setState({[e]:r})};_isCurrentUserInputSameAsExampleValue=()=>{const{currentUserInputValue:e}=this.props;return this._getCurrentExampleValue()===e};_getValueForExample=(e,t)=>{const{examples:r}=t||this.props;return stringifyUnlessList((r||(0,k.Map)({})).getIn([e,"value"]))};_getCurrentExampleValue=e=>{const{currentKey:t}=e||this.props;return this._getValueForExample(t,e||this.props)};_onExamplesSelect=(e,{isSyntheticChange:t}={},...r)=>{const{onSelect:a,updateValue:n,currentUserInputValue:s,userHasEditedBody:o}=this.props,{lastUserEditedValue:l}=this._getStateForCurrentNamespace(),c=this._getValueForExample(e);if("__MODIFIED__VALUE__"===e)return n(stringifyUnlessList(l)),this._setStateForCurrentNamespace({isModifiedValueSelected:!0});"function"==typeof a&&a(e,{isSyntheticChange:t},...r),this._setStateForCurrentNamespace({lastDownstreamValue:c,isModifiedValueSelected:t&&o||!!s&&s!==c}),t||"function"==typeof n&&n(stringifyUnlessList(c))};UNSAFE_componentWillReceiveProps(e){const{currentUserInputValue:t,examples:r,onSelect:a,userHasEditedBody:n}=e,{lastUserEditedValue:s,lastDownstreamValue:o}=this._getStateForCurrentNamespace(),l=this._getValueForExample(e.currentKey,e),c=r.filter((e=>k.Map.isMap(e)&&(e.get("value")===t||stringify(e.get("value"))===t)));if(c.size){let t;t=c.has(e.currentKey)?e.currentKey:c.keySeq().first(),a(t,{isSyntheticChange:!0})}else t!==this.props.currentUserInputValue&&t!==s&&t!==o&&(this.props.setRetainRequestBodyValueFlag(!0),this._setStateForNamespace(e.currentNamespace,{lastUserEditedValue:e.currentUserInputValue,isModifiedValueSelected:n||t!==l}))}render(){const{currentUserInputValue:e,examples:t,currentKey:r,getComponent:a,userHasEditedBody:n}=this.props,{lastDownstreamValue:s,lastUserEditedValue:o,isModifiedValueSelected:l}=this._getStateForCurrentNamespace(),c=a("ExamplesSelect");return O().createElement(c,{examples:t,currentExampleKey:r,onSelect:this._onExamplesSelect,isModifiedValueAvailable:!!o&&o!==s,isValueModified:void 0!==e&&l&&e!==this._getCurrentExampleValue()||n})}}function oauth2_authorize_authorize({auth:e,authActions:t,errActions:r,configs:a,authConfigs:n={},currentServer:s}){let{schema:o,scopes:l,name:c,clientId:i}=e,p=o.get("flow"),m=[];switch(p){case"password":return void t.authorizePassword(e);case"application":case"clientCredentials":case"client_credentials":return void t.authorizeApplication(e);case"accessCode":case"authorizationCode":case"authorization_code":m.push("response_type=code");break;case"implicit":m.push("response_type=token")}"string"==typeof i&&m.push("client_id="+encodeURIComponent(i));let u=a.oauth2RedirectUrl;if(void 0===u)return void r.newAuthErr({authId:c,source:"validation",level:"error",message:"oauth2RedirectUrl configuration is not passed. Oauth2 authorization cannot be performed."});m.push("redirect_uri="+encodeURIComponent(u));let d=[];if(Array.isArray(l)?d=l:A().List.isList(l)&&(d=l.toArray()),d.length>0){let e=n.scopeSeparator||" ";m.push("scope="+encodeURIComponent(d.join(e)))}let h=btoa(new Date);if(m.push("state="+encodeURIComponent(h)),void 0!==n.realm&&m.push("realm="+encodeURIComponent(n.realm)),("authorizationCode"===p||"authorization_code"===p||"accessCode"===p)&&n.usePkceWithAuthorizationCodeGrant){const t=function generateCodeVerifier(){return b64toB64UrlEncoded(ae()(32).toString("base64"))}(),r=function createCodeChallenge(e){return b64toB64UrlEncoded(se()("sha256").update(e).digest("base64"))}(t);m.push("code_challenge="+r),m.push("code_challenge_method=S256"),e.codeVerifier=t}let{additionalQueryStringParams:g}=n;for(let e in g)void 0!==g[e]&&m.push([e,g[e]].map(encodeURIComponent).join("="));const y=o.get("authorizationUrl");let f;f=s?ue()(sanitizeUrl(y),s,!0).toString():sanitizeUrl(y);let S,E=[f,m.join("&")].join("string"!=typeof y||y.includes("?")?"&":"?");S="implicit"===p?t.preAuthorizeImplicit:n.useBasicAuthenticationWithAccessCodeGrant?t.authorizeAccessCodeWithBasicAuthentication:t.authorizeAccessCodeWithFormParams,t.authPopup(E,{auth:e,state:h,redirectUrl:u,callback:S,errCb:r.newAuthErr})}class Oauth2 extends O().Component{constructor(e,t){super(e,t);let{name:r,schema:a,authorized:n,authSelectors:s}=this.props,o=n&&n.get(r),l=s.getConfigs()||{},c=o&&o.get("username")||"",i=o&&o.get("clientId")||l.clientId||"",p=o&&o.get("clientSecret")||l.clientSecret||"",m=o&&o.get("passwordType")||"basic",u=o&&o.get("scopes")||l.scopes||[];"string"==typeof u&&(u=u.split(l.scopeSeparator||" ")),this.state={appName:l.appName,name:r,schema:a,scopes:u,clientId:i,clientSecret:p,username:c,password:"",passwordType:m}}close=e=>{e.preventDefault();let{authActions:t}=this.props;t.showDefinitions(!1)};authorize=()=>{let{authActions:e,errActions:t,getConfigs:r,authSelectors:a,oas3Selectors:n}=this.props,s=r(),o=a.getConfigs();t.clear({authId:name,type:"auth",source:"auth"}),oauth2_authorize_authorize({auth:this.state,currentServer:n.serverEffectiveValue(n.selectedServer()),authActions:e,errActions:t,configs:s,authConfigs:o})};onScopeChange=e=>{let{target:t}=e,{checked:r}=t,a=t.dataset.value;if(r&&-1===this.state.scopes.indexOf(a)){let e=this.state.scopes.concat([a]);this.setState({scopes:e})}else!r&&this.state.scopes.indexOf(a)>-1&&this.setState({scopes:this.state.scopes.filter((e=>e!==a))})};onInputChange=e=>{let{target:{dataset:{name:t},value:r}}=e,a={[t]:r};this.setState(a)};selectScopes=e=>{e.target.dataset.all?this.setState({scopes:Array.from((this.props.schema.get("allowedScopes")||this.props.schema.get("scopes")).keys())}):this.setState({scopes:[]})};logout=e=>{e.preventDefault();let{authActions:t,errActions:r,name:a}=this.props;r.clear({authId:a,type:"auth",source:"auth"}),t.logoutWithPersistOption([a])};render(){let{schema:e,getComponent:t,authSelectors:r,errSelectors:a,name:n,specSelectors:s}=this.props;const o=t("Input"),l=t("Row"),c=t("Col"),i=t("Button"),p=t("authError"),m=t("JumpToPath",!0),u=t("Markdown",!0),d=t("InitializedInput"),{isOAS3:h}=s;let g=h()?e.get("openIdConnectUrl"):null;const y="implicit",f="password",S=h()?g?"authorization_code":"authorizationCode":"accessCode",E=h()?g?"client_credentials":"clientCredentials":"application",_=r.selectAuthPath(n);let v=!!(r.getConfigs()||{}).usePkceWithAuthorizationCodeGrant,w=e.get("flow"),b=w===S&&v?w+" with PKCE":w,C=e.get("allowedScopes")||e.get("scopes"),x=!!r.authorized().get(n),N=a.allErrors().filter((e=>e.get("authId")===n)),k=!N.filter((e=>"validation"===e.get("source"))).size,A=e.get("description");return O().createElement("div",null,O().createElement("h4",null,n," (OAuth2, ",b,") ",O().createElement(m,{path:_})),this.state.appName?O().createElement("h5",null,"Application: ",this.state.appName," "):null,A&&O().createElement(u,{source:e.get("description")}),x&&O().createElement("h6",null,"Authorized"),g&&O().createElement("p",null,"OpenID Connect URL: ",O().createElement("code",null,g)),(w===y||w===S)&&O().createElement("p",null,"Authorization URL: ",O().createElement("code",null,e.get("authorizationUrl"))),(w===f||w===S||w===E)&&O().createElement("p",null,"Token URL:",O().createElement("code",null," ",e.get("tokenUrl"))),O().createElement("p",{className:"flow"},"Flow: ",O().createElement("code",null,b)),w!==f?null:O().createElement(l,null,O().createElement(l,null,O().createElement("label",{htmlFor:"oauth_username"},"username:"),x?O().createElement("code",null," ",this.state.username," "):O().createElement(c,{tablet:10,desktop:10},O().createElement("input",{id:"oauth_username",type:"text","data-name":"username",onChange:this.onInputChange,autoFocus:!0}))),O().createElement(l,null,O().createElement("label",{htmlFor:"oauth_password"},"password:"),x?O().createElement("code",null," ****** "):O().createElement(c,{tablet:10,desktop:10},O().createElement("input",{id:"oauth_password",type:"password","data-name":"password",onChange:this.onInputChange}))),O().createElement(l,null,O().createElement("label",{htmlFor:"password_type"},"Client credentials location:"),x?O().createElement("code",null," ",this.state.passwordType," "):O().createElement(c,{tablet:10,desktop:10},O().createElement("select",{id:"password_type","data-name":"passwordType",onChange:this.onInputChange},O().createElement("option",{value:"basic"},"Authorization header"),O().createElement("option",{value:"request-body"},"Request body"))))),(w===E||w===y||w===S||w===f)&&(!x||x&&this.state.clientId)&&O().createElement(l,null,O().createElement("label",{htmlFor:`client_id_${w}`},"client_id:"),x?O().createElement("code",null," ****** "):O().createElement(c,{tablet:10,desktop:10},O().createElement(d,{id:`client_id_${w}`,type:"text",required:w===f,initialValue:this.state.clientId,"data-name":"clientId",onChange:this.onInputChange}))),(w===E||w===S||w===f)&&O().createElement(l,null,O().createElement("label",{htmlFor:`client_secret_${w}`},"client_secret:"),x?O().createElement("code",null," ****** "):O().createElement(c,{tablet:10,desktop:10},O().createElement(d,{id:`client_secret_${w}`,initialValue:this.state.clientSecret,type:"password","data-name":"clientSecret",onChange:this.onInputChange}))),!x&&C&&C.size?O().createElement("div",{className:"scopes"},O().createElement("h2",null,"Scopes:",O().createElement("a",{onClick:this.selectScopes,"data-all":!0},"select all"),O().createElement("a",{onClick:this.selectScopes},"select none")),C.map(((e,t)=>O().createElement(l,{key:t},O().createElement("div",{className:"checkbox"},O().createElement(o,{"data-value":t,id:`${t}-${w}-checkbox-${this.state.name}`,disabled:x,checked:this.state.scopes.includes(t),type:"checkbox",onChange:this.onScopeChange}),O().createElement("label",{htmlFor:`${t}-${w}-checkbox-${this.state.name}`},O().createElement("span",{className:"item"}),O().createElement("div",{className:"text"},O().createElement("p",{className:"name"},t),O().createElement("p",{className:"description"},e))))))).toArray()):null,N.valueSeq().map(((e,t)=>O().createElement(p,{error:e,key:t}))),O().createElement("div",{className:"auth-btn-wrapper"},k&&(x?O().createElement(i,{className:"btn modal-btn auth authorize",onClick:this.logout,"aria-label":"Remove authorization"},"Logout"):O().createElement(i,{className:"btn modal-btn auth authorize",onClick:this.authorize,"aria-label":"Apply given OAuth2 credentials"},"Authorize")),O().createElement(i,{className:"btn modal-btn auth btn-done",onClick:this.close},"Close")))}}class Clear extends x.Component{onClick=()=>{let{specActions:e,path:t,method:r}=this.props;e.clearResponse(t,r),e.clearRequest(t,r)};render(){return O().createElement("button",{className:"btn btn-clear opblock-control__btn",onClick:this.onClick},"Clear")}}const Headers=({headers:e})=>O().createElement("div",null,O().createElement("h5",null,"Response headers"),O().createElement("pre",{className:"microlight"},e)),Duration=({duration:e})=>O().createElement("div",null,O().createElement("h5",null,"Request duration"),O().createElement("pre",{className:"microlight"},e," ms"));class LiveResponse extends O().Component{shouldComponentUpdate(e){return this.props.response!==e.response||this.props.path!==e.path||this.props.method!==e.method||this.props.displayRequestDuration!==e.displayRequestDuration}render(){const{response:e,getComponent:t,getConfigs:r,displayRequestDuration:a,specSelectors:n,path:s,method:o}=this.props,{showMutatedRequest:l,requestSnippetsEnabled:c}=r(),i=l?n.mutatedRequestFor(s,o):n.requestFor(s,o),p=e.get("status"),m=i.get("url"),u=e.get("headers").toJS(),d=e.get("notDocumented"),h=e.get("error"),g=e.get("text"),y=e.get("duration"),f=Object.keys(u),S=u["content-type"]||u["Content-Type"],E=t("responseBody"),_=f.map((e=>{var t=Array.isArray(u[e])?u[e].join():u[e];return O().createElement("span",{className:"headerline",key:e}," ",e,": ",t," ")})),v=0!==_.length,w=t("Markdown",!0),b=t("RequestSnippets",!0),C=t("curl",!0);return O().createElement("div",null,i&&c?O().createElement(b,{request:i}):O().createElement(C,{request:i}),m&&O().createElement("div",null,O().createElement("div",{className:"request-url"},O().createElement("h4",null,"Request URL"),O().createElement("pre",{className:"microlight"},m))),O().createElement("h4",null,"Server response"),O().createElement("table",{className:"responses-table live-responses-table"},O().createElement("thead",null,O().createElement("tr",{className:"responses-header"},O().createElement("td",{className:"col_header response-col_status"},"Code"),O().createElement("td",{className:"col_header response-col_description"},"Details"))),O().createElement("tbody",null,O().createElement("tr",{className:"response"},O().createElement("td",{className:"response-col_status"},p,d?O().createElement("div",{className:"response-undocumented"},O().createElement("i",null," Undocumented ")):null),O().createElement("td",{className:"response-col_description"},h?O().createElement(w,{source:`${""!==e.get("name")?`${e.get("name")}: `:""}${e.get("message")}`}):null,g?O().createElement(E,{content:g,contentType:S,url:m,headers:u,getConfigs:r,getComponent:t}):null,v?O().createElement(Headers,{headers:_}):null,a&&y?O().createElement(Duration,{duration:y}):null)))))}}class OnlineValidatorBadge extends O().Component{constructor(e,t){super(e,t);let{getConfigs:r}=e,{validatorUrl:a}=r();this.state={url:this.getDefinitionUrl(),validatorUrl:void 0===a?"https://validator.swagger.io/validator":a}}getDefinitionUrl=()=>{let{specSelectors:e}=this.props;return new(ue())(e.url(),K.location).toString()};UNSAFE_componentWillReceiveProps(e){let{getConfigs:t}=e,{validatorUrl:r}=t();this.setState({url:this.getDefinitionUrl(),validatorUrl:void 0===r?"https://validator.swagger.io/validator":r})}render(){let{getConfigs:e}=this.props,{spec:t}=e(),r=sanitizeUrl(this.state.validatorUrl);return"object"==typeof t&&Object.keys(t).length?null:this.state.url&&requiresValidationURL(this.state.validatorUrl)&&requiresValidationURL(this.state.url)?O().createElement("span",{className:"float-right"},O().createElement("a",{target:"_blank",rel:"noopener noreferrer",href:`${r}/debug?url=${encodeURIComponent(this.state.url)}`},O().createElement(ValidatorImage,{src:`${r}?url=${encodeURIComponent(this.state.url)}`,alt:"Online validator badge"}))):null}}class ValidatorImage extends O().Component{constructor(e){super(e),this.state={loaded:!1,error:!1}}componentDidMount(){const e=new Image;e.onload=()=>{this.setState({loaded:!0})},e.onerror=()=>{this.setState({error:!0})},e.src=this.props.src}UNSAFE_componentWillReceiveProps(e){if(e.src!==this.props.src){const t=new Image;t.onload=()=>{this.setState({loaded:!0})},t.onerror=()=>{this.setState({error:!0})},t.src=e.src}}render(){return this.state.error?O().createElement("img",{alt:"Error"}):this.state.loaded?O().createElement("img",{src:this.props.src,alt:this.props.alt}):null}}class Operations extends O().Component{render(){let{specSelectors:e}=this.props;const t=e.taggedOperations();return 0===t.size?O().createElement("h3",null," No operations defined in spec!"):O().createElement("div",null,t.map(this.renderOperationTag).toArray(),t.size<1?O().createElement("h3",null," No operations defined in spec! "):null)}renderOperationTag=(e,t)=>{const{specSelectors:r,getComponent:a,oas3Selectors:n,layoutSelectors:s,layoutActions:o,getConfigs:l}=this.props,c=r.validOperationMethods(),i=a("OperationContainer",!0),p=a("OperationTag"),m=e.get("operations");return O().createElement(p,{key:"operation-"+t,tagObj:e,tag:t,oas3Selectors:n,layoutSelectors:s,layoutActions:o,getConfigs:l,getComponent:a,specUrl:r.url()},O().createElement("div",{className:"operation-tag-content"},m.map((e=>{const r=e.get("path"),a=e.get("method"),n=A().List(["paths",r,a]);return-1===c.indexOf(a)?null:O().createElement(i,{key:`${r}-${a}`,specPath:n,op:e,path:r,method:a,tag:t})})).toArray()))}}class OperationTag extends O().Component{static defaultProps={tagObj:A().fromJS({}),tag:""};render(){const{tagObj:e,tag:t,children:r,oas3Selectors:a,layoutSelectors:n,layoutActions:s,getConfigs:o,getComponent:l,specUrl:c}=this.props;let{docExpansion:i,deepLinking:p}=o();const m=l("Collapse"),u=l("Markdown",!0),d=l("DeepLink"),h=l("Link"),g=l("ArrowUpIcon"),y=l("ArrowDownIcon");let f,S=e.getIn(["tagDetails","description"],null),E=e.getIn(["tagDetails","externalDocs","description"]),_=e.getIn(["tagDetails","externalDocs","url"]);f=isFunc(a)&&isFunc(a.selectedServer)?safeBuildUrl(_,c,{selectedServer:a.selectedServer()}):_;let v=["operations-tag",t],w=n.isShown(v,"full"===i||"list"===i);return O().createElement("div",{className:w?"opblock-tag-section is-open":"opblock-tag-section"},O().createElement("h3",{onClick:()=>s.show(v,!w),className:S?"opblock-tag":"opblock-tag no-desc",id:v.map((e=>escapeDeepLinkPath(e))).join("-"),"data-tag":t,"data-is-open":w},O().createElement(d,{enabled:p,isShown:w,path:createDeepLinkPath(t),text:t}),S?O().createElement("small",null,O().createElement(u,{source:S})):O().createElement("small",null),f?O().createElement("div",{className:"info__externaldocs"},O().createElement("small",null,O().createElement(h,{href:sanitizeUrl(f),onClick:e=>e.stopPropagation(),target:"_blank"},E||f))):null,O().createElement("button",{"aria-expanded":w,className:"expand-operation",title:w?"Collapse operation":"Expand operation",onClick:()=>s.show(v,!w)},w?O().createElement(g,{className:"arrow"}):O().createElement(y,{className:"arrow"}))),O().createElement(m,{isOpened:w},r))}}class Operation extends x.PureComponent{static defaultProps={operation:null,response:null,request:null,specPath:(0,k.List)(),summary:""};render(){let{specPath:e,response:t,request:r,toggleShown:a,onTryoutClick:n,onResetClick:s,onCancelClick:o,onExecute:l,fn:c,getComponent:i,getConfigs:p,specActions:m,specSelectors:u,authActions:d,authSelectors:h,oas3Actions:g,oas3Selectors:y}=this.props,f=this.props.operation,{deprecated:S,isShown:E,path:_,method:v,op:w,tag:b,operationId:C,allowTryItOut:x,displayRequestDuration:N,tryItOutEnabled:k,executeInProgress:I}=f.toJS(),{description:j,externalDocs:q,schemes:P}=w;const M=q?safeBuildUrl(q.url,u.url(),{selectedServer:y.selectedServer()}):"";let T=f.getIn(["op"]),R=T.get("responses"),J=function getList(e,t){if(!A().Iterable.isIterable(e))return A().List();let r=e.getIn(Array.isArray(t)?t:[t]);return A().List.isList(r)?r:A().List()}(T,["parameters"]),$=u.operationScheme(_,v),V=["operations",b,C],L=getExtensions(T);const D=i("responses"),U=i("parameters"),K=i("execute"),z=i("clear"),B=i("Collapse"),F=i("Markdown",!0),W=i("schemes"),H=i("OperationServers"),X=i("OperationExt"),G=i("OperationSummary"),Y=i("Link"),{showExtensions:Q}=p();if(R&&t&&t.size>0){let e=!R.get(String(t.get("status")))&&!R.get("default");t=t.set("notDocumented",e)}let Z=[_,v];const ee=u.validationErrors([_,v]);return O().createElement("div",{className:S?"opblock opblock-deprecated":E?`opblock opblock-${v} is-open`:`opblock opblock-${v}`,id:escapeDeepLinkPath(V.join("-"))},O().createElement(G,{operationProps:f,isShown:E,toggleShown:a,getComponent:i,authActions:d,authSelectors:h,specPath:e}),O().createElement(B,{isOpened:E},O().createElement("div",{className:"opblock-body"},T&&T.size||null===T?null:O().createElement(rolling_load,{height:"32px",width:"32px",className:"opblock-loading-animation"}),S&&O().createElement("h4",{className:"opblock-title_normal"}," Warning: Deprecated"),j&&O().createElement("div",{className:"opblock-description-wrapper"},O().createElement("div",{className:"opblock-description"},O().createElement(F,{source:j}))),M?O().createElement("div",{className:"opblock-external-docs-wrapper"},O().createElement("h4",{className:"opblock-title_normal"},"Find more details"),O().createElement("div",{className:"opblock-external-docs"},q.description&&O().createElement("span",{className:"opblock-external-docs__description"},O().createElement(F,{source:q.description})),O().createElement(Y,{target:"_blank",className:"opblock-external-docs__link",href:sanitizeUrl(M)},M))):null,T&&T.size?O().createElement(U,{parameters:J,specPath:e.push("parameters"),operation:T,onChangeKey:Z,onTryoutClick:n,onResetClick:s,onCancelClick:o,tryItOutEnabled:k,allowTryItOut:x,fn:c,getComponent:i,specActions:m,specSelectors:u,pathMethod:[_,v],getConfigs:p,oas3Actions:g,oas3Selectors:y}):null,k?O().createElement(H,{getComponent:i,path:_,method:v,operationServers:T.get("servers"),pathServers:u.paths().getIn([_,"servers"]),getSelectedServer:y.selectedServer,setSelectedServer:g.setSelectedServer,setServerVariableValue:g.setServerVariableValue,getServerVariable:y.serverVariableValue,getEffectiveServerValue:y.serverEffectiveValue}):null,k&&x&&P&&P.size?O().createElement("div",{className:"opblock-schemes"},O().createElement(W,{schemes:P,path:_,method:v,specActions:m,currentScheme:$})):null,!k||!x||ee.length<=0?null:O().createElement("div",{className:"validation-errors errors-wrapper"},"Please correct the following validation errors and try again.",O().createElement("ul",null,ee.map(((e,t)=>O().createElement("li",{key:t}," ",e," "))))),O().createElement("div",{className:k&&t&&x?"btn-group":"execute-wrapper"},k&&x?O().createElement(K,{operation:T,specActions:m,specSelectors:u,oas3Selectors:y,oas3Actions:g,path:_,method:v,onExecute:l,disabled:I}):null,k&&t&&x?O().createElement(z,{specActions:m,path:_,method:v}):null),I?O().createElement("div",{className:"loading-container"},O().createElement("div",{className:"loading"})):null,R?O().createElement(D,{responses:R,request:r,tryItOutResponse:t,getComponent:i,getConfigs:p,specSelectors:u,oas3Actions:g,oas3Selectors:y,specActions:m,produces:u.producesOptionsFor([_,v]),producesValue:u.currentProducesFor([_,v]),specPath:e.push("responses"),path:_,method:v,displayRequestDuration:N,fn:c}):null,Q&&L.size?O().createElement(X,{extensions:L,getComponent:i}):null)))}}class OperationContainer extends x.PureComponent{constructor(e,t){super(e,t);const{tryItOutEnabled:r}=e.getConfigs();this.state={tryItOutEnabled:r,executeInProgress:!1}}static defaultProps={showSummary:!0,response:null,allowTryItOut:!0,displayOperationId:!1,displayRequestDuration:!1};mapStateToProps(e,t){const{op:r,layoutSelectors:a,getConfigs:n}=t,{docExpansion:s,deepLinking:o,displayOperationId:l,displayRequestDuration:c,supportedSubmitMethods:i}=n(),p=a.showSummary(),m=r.getIn(["operation","__originalOperationId"])||r.getIn(["operation","operationId"])||(0,oa.opId)(r.get("operation"),t.path,t.method)||r.get("id"),u=["operations",t.tag,m],d=i.indexOf(t.method)>=0&&(void 0===t.allowTryItOut?t.specSelectors.allowTryItOutFor(t.path,t.method):t.allowTryItOut),h=r.getIn(["operation","security"])||t.specSelectors.security();return{operationId:m,isDeepLinkingEnabled:o,showSummary:p,displayOperationId:l,displayRequestDuration:c,allowTryItOut:d,security:h,isAuthorized:t.authSelectors.isAuthorized(h),isShown:a.isShown(u,"full"===s),jumpToKey:`paths.${t.path}.${t.method}`,response:t.specSelectors.responseFor(t.path,t.method),request:t.specSelectors.requestFor(t.path,t.method)}}componentDidMount(){const{isShown:e}=this.props,t=this.getResolvedSubtree();e&&void 0===t&&this.requestResolvedSubtree()}UNSAFE_componentWillReceiveProps(e){const{response:t,isShown:r}=e,a=this.getResolvedSubtree();t!==this.props.response&&this.setState({executeInProgress:!1}),r&&void 0===a&&this.requestResolvedSubtree()}toggleShown=()=>{let{layoutActions:e,tag:t,operationId:r,isShown:a}=this.props;const n=this.getResolvedSubtree();a||void 0!==n||this.requestResolvedSubtree(),e.show(["operations",t,r],!a)};onCancelClick=()=>{this.setState({tryItOutEnabled:!this.state.tryItOutEnabled})};onTryoutClick=()=>{this.setState({tryItOutEnabled:!this.state.tryItOutEnabled})};onResetClick=e=>{const t=this.props.oas3Selectors.selectDefaultRequestBodyValue(...e),r=this.props.oas3Selectors.requestContentType(...e);if("application/x-www-form-urlencoded"===r||"multipart/form-data"===r){const r=JSON.parse(t);Object.entries(r).forEach((([e,t])=>{Array.isArray(t)?r[e]=r[e].map((e=>"object"==typeof e?JSON.stringify(e,null,2):e)):"object"==typeof t&&(r[e]=JSON.stringify(r[e],null,2))})),this.props.oas3Actions.setRequestBodyValue({value:(0,k.fromJS)(r),pathMethod:e})}else this.props.oas3Actions.setRequestBodyValue({value:t,pathMethod:e})};onExecute=()=>{this.setState({executeInProgress:!0})};getResolvedSubtree=()=>{const{specSelectors:e,path:t,method:r,specPath:a}=this.props;return a?e.specResolvedSubtree(a.toJS()):e.specResolvedSubtree(["paths",t,r])};requestResolvedSubtree=()=>{const{specActions:e,path:t,method:r,specPath:a}=this.props;return a?e.requestResolvedSubtree(a.toJS()):e.requestResolvedSubtree(["paths",t,r])};render(){let{op:e,tag:t,path:r,method:a,security:n,isAuthorized:s,operationId:o,showSummary:l,isShown:c,jumpToKey:i,allowTryItOut:p,response:m,request:u,displayOperationId:d,displayRequestDuration:h,isDeepLinkingEnabled:g,specPath:y,specSelectors:f,specActions:S,getComponent:E,getConfigs:_,layoutSelectors:v,layoutActions:w,authActions:b,authSelectors:C,oas3Actions:x,oas3Selectors:N,fn:A}=this.props;const I=E("operation"),j=this.getResolvedSubtree()||(0,k.Map)(),q=(0,k.fromJS)({op:j,tag:t,path:r,summary:e.getIn(["operation","summary"])||"",deprecated:j.get("deprecated")||e.getIn(["operation","deprecated"])||!1,method:a,security:n,isAuthorized:s,operationId:o,originalOperationId:j.getIn(["operation","__originalOperationId"]),showSummary:l,isShown:c,jumpToKey:i,allowTryItOut:p,request:u,displayOperationId:d,displayRequestDuration:h,isDeepLinkingEnabled:g,executeInProgress:this.state.executeInProgress,tryItOutEnabled:this.state.tryItOutEnabled});return O().createElement(I,{operation:q,response:m,request:u,isShown:c,toggleShown:this.toggleShown,onTryoutClick:this.onTryoutClick,onResetClick:this.onResetClick,onCancelClick:this.onCancelClick,onExecute:this.onExecute,specPath:y,specActions:S,specSelectors:f,oas3Actions:x,oas3Selectors:N,layoutActions:w,layoutSelectors:v,authActions:b,authSelectors:C,getComponent:E,getConfigs:_,fn:A})}}const Ya=require("lodash/toString");var Qa=__webpack_require__.n(Ya);class OperationSummary extends x.PureComponent{static defaultProps={operationProps:null,specPath:(0,k.List)(),summary:""};render(){let{isShown:e,toggleShown:t,getComponent:r,authActions:a,authSelectors:n,operationProps:s,specPath:o}=this.props,{summary:l,isAuthorized:c,method:i,op:p,showSummary:m,path:u,operationId:d,originalOperationId:h,displayOperationId:g}=s.toJS(),{summary:y}=p,f=s.get("security");const S=r("authorizeOperationBtn",!0),E=r("OperationSummaryMethod"),_=r("OperationSummaryPath"),v=r("JumpToPath",!0),w=r("CopyToClipboardBtn",!0),b=r("ArrowUpIcon"),C=r("ArrowDownIcon"),x=f&&!!f.count(),N=x&&1===f.size&&f.first().isEmpty(),k=!x||N;return O().createElement("div",{className:`opblock-summary opblock-summary-${i}`},O().createElement("button",{"aria-expanded":e,className:"opblock-summary-control",onClick:t},O().createElement(E,{method:i}),O().createElement("div",{className:"opblock-summary-path-description-wrapper"},O().createElement(_,{getComponent:r,operationProps:s,specPath:o}),m?O().createElement("div",{className:"opblock-summary-description"},Qa()(y||l)):null),g&&(h||d)?O().createElement("span",{className:"opblock-summary-operation-id"},h||d):null),O().createElement(w,{textToCopy:`${o.get(1)}`}),k?null:O().createElement(S,{isAuthorized:c,onClick:()=>{const e=n.definitionsForRequirements(f);a.showDefinitions(e)}}),O().createElement(v,{path:o}),O().createElement("button",{"aria-label":`${i} ${u.replace(/\//g,"​/")}`,className:"opblock-control-arrow","aria-expanded":e,tabIndex:"-1",onClick:t},e?O().createElement(b,{className:"arrow"}):O().createElement(C,{className:"arrow"})))}}class OperationSummaryMethod extends x.PureComponent{static defaultProps={operationProps:null};render(){let{method:e}=this.props;return O().createElement("span",{className:"opblock-summary-method"},e.toUpperCase())}}class OperationSummaryPath extends x.PureComponent{render(){let{getComponent:e,operationProps:t}=this.props,{deprecated:r,isShown:a,path:n,tag:s,operationId:o,isDeepLinkingEnabled:l}=t.toJS();const c=n.split(/(?=\/)/g);for(let e=1;e<c.length;e+=2)c.splice(e,0,O().createElement("wbr",{key:e}));const i=e("DeepLink");return O().createElement("span",{className:r?"opblock-summary-path__deprecated":"opblock-summary-path","data-path":n},O().createElement(i,{enabled:l,isShown:a,path:createDeepLinkPath(`${s}/${o}`),text:c}))}}const operation_extensions=({extensions:e,getComponent:t})=>{let r=t("OperationExtRow");return O().createElement("div",{className:"opblock-section"},O().createElement("div",{className:"opblock-section-header"},O().createElement("h4",null,"Extensions")),O().createElement("div",{className:"table-container"},O().createElement("table",null,O().createElement("thead",null,O().createElement("tr",null,O().createElement("td",{className:"col_header"},"Field"),O().createElement("td",{className:"col_header"},"Value"))),O().createElement("tbody",null,e.entrySeq().map((([e,t])=>O().createElement(r,{key:`${e}-${t}`,xKey:e,xVal:t})))))))},operation_extension_row=({xKey:e,xVal:t})=>{const r=t?t.toJS?t.toJS():t:null;return O().createElement("tr",null,O().createElement("td",null,e),O().createElement("td",null,JSON.stringify(r)))};function createHtmlReadyId(e,t="_"){return e.replace(/[^\w-]/g,t)}class Responses extends O().Component{static defaultProps={tryItOutResponse:null,produces:(0,k.fromJS)(["application/json"]),displayRequestDuration:!1};onChangeProducesWrapper=e=>this.props.specActions.changeProducesValue([this.props.path,this.props.method],e);onResponseContentTypeChange=({controlsAcceptHeader:e,value:t})=>{const{oas3Actions:r,path:a,method:n}=this.props;e&&r.setResponseContentType({value:t,path:a,method:n})};render(){let{responses:e,tryItOutResponse:t,getComponent:r,getConfigs:a,specSelectors:n,fn:s,producesValue:o,displayRequestDuration:l,specPath:c,path:i,method:p,oas3Selectors:m,oas3Actions:u}=this.props,d=function defaultStatusCode(e){let t=e.keySeq();return t.contains(ce)?ce:t.filter((e=>"2"===(e+"")[0])).sort().first()}(e);const h=r("contentType"),g=r("liveResponse"),y=r("response");let f=this.props.produces&&this.props.produces.size?this.props.produces:Responses.defaultProps.produces;const S=n.isOAS3()?function getAcceptControllingResponse(e){if(!A().OrderedMap.isOrderedMap(e))return null;if(!e.size)return null;const t=e.find(((e,t)=>t.startsWith("2")&&Object.keys(e.get("content")||{}).length>0)),r=e.get("default")||A().OrderedMap(),a=(r.get("content")||A().OrderedMap()).keySeq().toJS().length?r:null;return t||a}(e):null,E=createHtmlReadyId(`${p}${i}_responses`),_=`${E}_select`;return O().createElement("div",{className:"responses-wrapper"},O().createElement("div",{className:"opblock-section-header"},O().createElement("h4",null,"Responses"),n.isOAS3()?null:O().createElement("label",{htmlFor:_},O().createElement("span",null,"Response content type"),O().createElement(h,{value:o,ariaControls:E,ariaLabel:"Response content type",className:"execute-content-type",contentTypes:f,controlId:_,onChange:this.onChangeProducesWrapper}))),O().createElement("div",{className:"responses-inner"},t?O().createElement("div",null,O().createElement(g,{response:t,getComponent:r,getConfigs:a,specSelectors:n,path:this.props.path,method:this.props.method,displayRequestDuration:l}),O().createElement("h4",null,"Responses")):null,O().createElement("table",{"aria-live":"polite",className:"responses-table",id:E,role:"region"},O().createElement("thead",null,O().createElement("tr",{className:"responses-header"},O().createElement("td",{className:"col_header response-col_status"},"Code"),O().createElement("td",{className:"col_header response-col_description"},"Description"),n.isOAS3()?O().createElement("td",{className:"col col_header response-col_links"},"Links"):null)),O().createElement("tbody",null,e.entrySeq().map((([e,l])=>{let h=t&&t.get("status")==e?"response_current":"";return O().createElement(y,{key:e,path:i,method:p,specPath:c.push(e),isDefault:d===e,fn:s,className:h,code:e,response:l,specSelectors:n,controlsAcceptHeader:l===S,onContentTypeChange:this.onResponseContentTypeChange,contentType:o,getConfigs:a,activeExamplesKey:m.activeExamplesMember(i,p,"responses",e),oas3Actions:u,getComponent:r})})).toArray()))))}}function getKnownSyntaxHighlighterLanguage(e){return function canJsonParse(e){try{return!!JSON.parse(e)}catch(e){return null}}(e)?"json":null}class Response extends O().Component{constructor(e,t){super(e,t),this.state={responseContentType:""}}static defaultProps={response:(0,k.fromJS)({}),onContentTypeChange:()=>{}};_onContentTypeChange=e=>{const{onContentTypeChange:t,controlsAcceptHeader:r}=this.props;this.setState({responseContentType:e}),t({value:e,controlsAcceptHeader:r})};getTargetExamplesKey=()=>{const{response:e,contentType:t,activeExamplesKey:r}=this.props,a=this.state.responseContentType||t,n=e.getIn(["content",a],(0,k.Map)({})).get("examples",null).keySeq().first();return r||n};render(){let{path:e,method:t,code:r,response:a,className:n,specPath:s,fn:o,getComponent:l,getConfigs:c,specSelectors:i,contentType:p,controlsAcceptHeader:m,oas3Actions:u}=this.props,{inferSchema:d,getSampleSchema:h}=o,g=i.isOAS3();const{showExtensions:y}=c();let f=y?getExtensions(a):null,S=a.get("headers"),E=a.get("links");const _=l("ResponseExtension"),v=l("headers"),w=l("HighlightCode",!0),b=l("modelExample"),C=l("Markdown",!0),x=l("operationLink"),N=l("contentType"),A=l("ExamplesSelect"),I=l("Example");var j,q;const P=this.state.responseContentType||p,M=a.getIn(["content",P],(0,k.Map)({})),T=M.get("examples",null);if(g){const e=M.get("schema");j=e?d(e.toJS()):null,q=e?(0,k.List)(["content",this.state.responseContentType,"schema"]):s}else j=a.get("schema"),q=a.has("schema")?s.push("schema"):s;let R,J,$=!1,V={includeReadOnly:!0};if(g)if(J=M.get("schema")?.toJS(),k.Map.isMap(T)&&!T.isEmpty()){const e=this.getTargetExamplesKey(),getMediaTypeExample=e=>k.Map.isMap(e)?e.get("value"):void 0;R=getMediaTypeExample(T.get(e,(0,k.Map)({}))),void 0===R&&(R=getMediaTypeExample(T.values().next().value)),$=!0}else void 0!==M.get("example")&&(R=M.get("example"),$=!0);else{J=j,V={...V,includeWriteOnly:!0};const e=a.getIn(["examples",P]);e&&(R=e,$=!0)}const L=((e,t)=>{if(null==e)return null;const r=getKnownSyntaxHighlighterLanguage(e)?"json":null;return O().createElement("div",null,O().createElement(t,{className:"example",language:r},stringify(e)))})(h(J,P,V,$?R:void 0),w);return O().createElement("tr",{className:"response "+(n||""),"data-code":r},O().createElement("td",{className:"response-col_status"},r),O().createElement("td",{className:"response-col_description"},O().createElement("div",{className:"response-col_description__inner"},O().createElement(C,{source:a.get("description")})),y&&f.size?f.entrySeq().map((([e,t])=>O().createElement(_,{key:`${e}-${t}`,xKey:e,xVal:t}))):null,g&&a.get("content")?O().createElement("section",{className:"response-controls"},O().createElement("div",{className:pt()("response-control-media-type",{"response-control-media-type--accept-controller":m})},O().createElement("small",{className:"response-control-media-type__title"},"Media type"),O().createElement(N,{value:this.state.responseContentType,contentTypes:a.get("content")?a.get("content").keySeq():(0,k.Seq)(),onChange:this._onContentTypeChange,ariaLabel:"Media Type"}),m?O().createElement("small",{className:"response-control-media-type__accept-message"},"Controls ",O().createElement("code",null,"Accept")," header."):null),k.Map.isMap(T)&&!T.isEmpty()?O().createElement("div",{className:"response-control-examples"},O().createElement("small",{className:"response-control-examples__title"},"Examples"),O().createElement(A,{examples:T,currentExampleKey:this.getTargetExamplesKey(),onSelect:a=>u.setActiveExamplesMember({name:a,pathMethod:[e,t],contextType:"responses",contextName:r}),showLabels:!1})):null):null,L||j?O().createElement(b,{specPath:q,getComponent:l,getConfigs:c,specSelectors:i,schema:fromJSOrdered(j),example:L,includeReadOnly:!0}):null,g&&T?O().createElement(I,{example:T.get(this.getTargetExamplesKey(),(0,k.Map)({})),getComponent:l,getConfigs:c,omitValue:!0}):null,S?O().createElement(v,{headers:S,getComponent:l}):null),g?O().createElement("td",{className:"response-col_links"},E?E.toSeq().entrySeq().map((([e,t])=>O().createElement(x,{key:e,name:e,link:t,getComponent:l}))):O().createElement("i",null,"No links")):null)}}const response_extension=({xKey:e,xVal:t})=>O().createElement("div",{className:"response__extension"},e,": ",String(t)),Za=require("xml-but-prettier");var en=__webpack_require__.n(Za);const tn=require("lodash/toLower");var rn=__webpack_require__.n(tn);class ResponseBody extends O().PureComponent{state={parsedContent:null};updateParsedContent=e=>{const{content:t}=this.props;if(e!==t)if(t&&t instanceof Blob){var r=new FileReader;r.onload=()=>{this.setState({parsedContent:r.result})},r.readAsText(t)}else this.setState({parsedContent:t.toString()})};componentDidMount(){this.updateParsedContent(null)}componentDidUpdate(e){this.updateParsedContent(e.content)}render(){let{content:e,contentType:t,url:r,headers:a={},getComponent:n}=this.props;const{parsedContent:s}=this.state,o=n("HighlightCode",!0),l="response_"+(new Date).getTime();let c,i;if(r=r||"",(/^application\/octet-stream/i.test(t)||a["Content-Disposition"]&&/attachment/i.test(a["Content-Disposition"])||a["content-disposition"]&&/attachment/i.test(a["content-disposition"])||a["Content-Description"]&&/File Transfer/i.test(a["Content-Description"])||a["content-description"]&&/File Transfer/i.test(a["content-description"]))&&(e.size>0||e.length>0))if("Blob"in window){let n=t||"text/html",s=e instanceof Blob?e:new Blob([e],{type:n}),o=window.URL.createObjectURL(s),l=[n,r.substr(r.lastIndexOf("/")+1),o].join(":"),c=a["content-disposition"]||a["Content-Disposition"];if(void 0!==c){let e=function extractFileNameFromContentDispositionHeader(e){let t;if([/filename\*=[^']+'\w*'"([^"]+)";?/i,/filename\*=[^']+'\w*'([^;]+);?/i,/filename="([^;]*);?"/i,/filename=([^;]*);?/i].some((r=>(t=r.exec(e),null!==t))),null!==t&&t.length>1)try{return decodeURIComponent(t[1])}catch(e){console.error(e)}return null}(c);null!==e&&(l=e)}i=K.navigator&&K.navigator.msSaveOrOpenBlob?O().createElement("div",null,O().createElement("a",{href:o,onClick:()=>K.navigator.msSaveOrOpenBlob(s,l)},"Download file")):O().createElement("div",null,O().createElement("a",{href:o,download:l},"Download file"))}else i=O().createElement("pre",{className:"microlight"},"Download headers detected but your browser does not support downloading binary via XHR (Blob).");else if(/json/i.test(t)){let t=null;getKnownSyntaxHighlighterLanguage(e)&&(t="json");try{c=JSON.stringify(JSON.parse(e),null,"  ")}catch(t){c="can't parse JSON.  Raw result:\n\n"+e}i=O().createElement(o,{language:t,downloadable:!0,fileName:`${l}.json`,canCopy:!0},c)}else/xml/i.test(t)?(c=en()(e,{textNodesOnSameLine:!0,indentor:"  "}),i=O().createElement(o,{downloadable:!0,fileName:`${l}.xml`,canCopy:!0},c)):i="text/html"===rn()(t)||/text\/plain/.test(t)?O().createElement(o,{downloadable:!0,fileName:`${l}.html`,canCopy:!0},e):"text/csv"===rn()(t)||/text\/csv/.test(t)?O().createElement(o,{downloadable:!0,fileName:`${l}.csv`,canCopy:!0},e):/^image\//i.test(t)?t.includes("svg")?O().createElement("div",null," ",e," "):O().createElement("img",{src:window.URL.createObjectURL(e)}):/^audio\//i.test(t)?O().createElement("pre",{className:"microlight"},O().createElement("audio",{controls:!0,key:r},O().createElement("source",{src:r,type:t}))):"string"==typeof e?O().createElement(o,{downloadable:!0,fileName:`${l}.txt`,canCopy:!0},e):e.size>0?s?O().createElement("div",null,O().createElement("p",{className:"i"},"Unrecognized response type; displaying content as text."),O().createElement(o,{downloadable:!0,fileName:`${l}.txt`,canCopy:!0},s)):O().createElement("p",{className:"i"},"Unrecognized response type; unable to display."):null;return i?O().createElement("div",null,O().createElement("h5",null,"Response body"),i):null}}class Parameters extends x.Component{constructor(e){super(e),this.state={callbackVisible:!1,parametersVisible:!0}}static defaultProps={onTryoutClick:Function.prototype,onCancelClick:Function.prototype,tryItOutEnabled:!1,allowTryItOut:!0,onChangeKey:[],specPath:[]};onChange=(e,t,r)=>{let{specActions:{changeParamByIdentity:a},onChangeKey:n}=this.props;a(n,e,t,r)};onChangeConsumesWrapper=e=>{let{specActions:{changeConsumesValue:t},onChangeKey:r}=this.props;t(r,e)};toggleTab=e=>"parameters"===e?this.setState({parametersVisible:!0,callbackVisible:!1}):"callbacks"===e?this.setState({callbackVisible:!0,parametersVisible:!1}):void 0;onChangeMediaType=({value:e,pathMethod:t})=>{let{specActions:r,oas3Selectors:a,oas3Actions:n}=this.props;const s=a.hasUserEditedBody(...t),o=a.shouldRetainRequestBodyValue(...t);n.setRequestContentType({value:e,pathMethod:t}),n.initRequestBodyValidateError({pathMethod:t}),s||(o||n.setRequestBodyValue({value:void 0,pathMethod:t}),r.clearResponse(...t),r.clearRequest(...t),r.clearValidateParams(t))};render(){let{onTryoutClick:e,onResetClick:t,parameters:r,allowTryItOut:a,tryItOutEnabled:n,specPath:s,fn:o,getComponent:l,getConfigs:c,specSelectors:i,specActions:p,pathMethod:m,oas3Actions:u,oas3Selectors:d,operation:h}=this.props;const g=l("parameterRow"),y=l("TryItOutButton"),f=l("contentType"),S=l("Callbacks",!0),E=l("RequestBody",!0),_=n&&a,v=i.isOAS3(),w=`${createHtmlReadyId(`${m[1]}${m[0]}_requests`)}_select`,b=h.get("requestBody"),C=Object.values(r.reduce(((e,t)=>{if(k.Map.isMap(t)){const r=t.get("in");e[r]??=[],e[r].push(t)}return e}),{})).reduce(((e,t)=>e.concat(t)),[]);return O().createElement("div",{className:"opblock-section"},O().createElement("div",{className:"opblock-section-header"},v?O().createElement("div",{className:"tab-header"},O().createElement("div",{onClick:()=>this.toggleTab("parameters"),className:`tab-item ${this.state.parametersVisible&&"active"}`},O().createElement("h4",{className:"opblock-title"},O().createElement("span",null,"Parameters"))),h.get("callbacks")?O().createElement("div",{onClick:()=>this.toggleTab("callbacks"),className:`tab-item ${this.state.callbackVisible&&"active"}`},O().createElement("h4",{className:"opblock-title"},O().createElement("span",null,"Callbacks"))):null):O().createElement("div",{className:"tab-header"},O().createElement("h4",{className:"opblock-title"},"Parameters")),a?O().createElement(y,{isOAS3:i.isOAS3(),hasUserEditedBody:d.hasUserEditedBody(...m),enabled:n,onCancelClick:this.props.onCancelClick,onTryoutClick:e,onResetClick:()=>t(m)}):null),this.state.parametersVisible?O().createElement("div",{className:"parameters-container"},C.length?O().createElement("div",{className:"table-container"},O().createElement("table",{className:"parameters"},O().createElement("thead",null,O().createElement("tr",null,O().createElement("th",{className:"col_header parameters-col_name"},"Name"),O().createElement("th",{className:"col_header parameters-col_description"},"Description"))),O().createElement("tbody",null,C.map(((e,t)=>O().createElement(g,{fn:o,specPath:s.push(t.toString()),getComponent:l,getConfigs:c,rawParam:e,param:i.parameterWithMetaByIdentity(m,e),key:`${e.get("in")}.${e.get("name")}`,onChange:this.onChange,onChangeConsumes:this.onChangeConsumesWrapper,specSelectors:i,specActions:p,oas3Actions:u,oas3Selectors:d,pathMethod:m,isExecute:_})))))):O().createElement("div",{className:"opblock-description-wrapper"},O().createElement("p",null,"No parameters"))):null,this.state.callbackVisible?O().createElement("div",{className:"callbacks-container opblock-description-wrapper"},O().createElement(S,{callbacks:(0,k.Map)(h.get("callbacks")),specPath:s.slice(0,-1).push("callbacks")})):null,v&&b&&this.state.parametersVisible&&O().createElement("div",{className:"opblock-section opblock-section-request-body"},O().createElement("div",{className:"opblock-section-header"},O().createElement("h4",{className:`opblock-title parameter__name ${b.get("required")&&"required"}`},"Request body"),O().createElement("label",{id:w},O().createElement(f,{value:d.requestContentType(...m),contentTypes:b.get("content",(0,k.List)()).keySeq(),onChange:e=>{this.onChangeMediaType({value:e,pathMethod:m})},className:"body-param-content-type",ariaLabel:"Request content type",controlId:w}))),O().createElement("div",{className:"opblock-description-wrapper"},O().createElement(E,{setRetainRequestBodyValueFlag:e=>u.setRetainRequestBodyValueFlag({value:e,pathMethod:m}),userHasEditedBody:d.hasUserEditedBody(...m),specPath:s.slice(0,-1).push("requestBody"),requestBody:b,requestBodyValue:d.requestBodyValue(...m),requestBodyInclusionSetting:d.requestBodyInclusionSetting(...m),requestBodyErrors:d.requestBodyErrors(...m),isExecute:_,getConfigs:c,activeExamplesKey:d.activeExamplesMember(...m,"requestBody","requestBody"),updateActiveExamplesKey:e=>{this.props.oas3Actions.setActiveExamplesMember({name:e,pathMethod:this.props.pathMethod,contextType:"requestBody",contextName:"requestBody"})},onChange:(e,t)=>{if(t){const r=d.requestBodyValue(...m),a=k.Map.isMap(r)?r:(0,k.Map)();return u.setRequestBodyValue({pathMethod:m,value:a.setIn(t,e)})}u.setRequestBodyValue({value:e,pathMethod:m})},onChangeIncludeEmpty:(e,t)=>{u.setRequestBodyInclusion({pathMethod:m,value:t,name:e})},contentType:d.requestContentType(...m)}))))}}const parameter_extension=({xKey:e,xVal:t})=>O().createElement("div",{className:"parameter__extension"},e,": ",String(t)),an={onChange:()=>{},isIncludedOptions:{}};class ParameterIncludeEmpty extends x.Component{static defaultProps=an;componentDidMount(){const{isIncludedOptions:e,onChange:t}=this.props,{shouldDispatchInit:r,defaultValue:a}=e;r&&t(a)}onCheckboxChange=e=>{const{onChange:t}=this.props;t(e.target.checked)};render(){let{isIncluded:e,isDisabled:t}=this.props;return O().createElement("div",null,O().createElement("label",{htmlFor:"include_empty_value",className:pt()("parameter__empty_value_toggle",{disabled:t})},O().createElement("input",{id:"include_empty_value",type:"checkbox",disabled:t,checked:!t&&e,onChange:this.onCheckboxChange}),"Send empty value"))}}class ParameterRow extends x.Component{constructor(e,t){super(e,t),this.setDefaultValue()}UNSAFE_componentWillReceiveProps(e){let t,{specSelectors:r,pathMethod:a,rawParam:n}=e,s=r.isOAS3(),o=r.parameterWithMetaByIdentity(a,n)||new k.Map;if(o=o.isEmpty()?n:o,s){let{schema:e}=getParameterSchema(o,{isOAS3:s});t=e?e.get("enum"):void 0}else t=o?o.get("enum"):void 0;let l,c=o?o.get("value"):void 0;void 0!==c?l=c:n.get("required")&&t&&t.size&&(l=t.first()),void 0!==l&&l!==c&&this.onChangeWrapper(function numberToString(e){return"number"==typeof e?e.toString():e}(l)),this.setDefaultValue()}onChangeWrapper=(e,t=!1)=>{let r,{onChange:a,rawParam:n}=this.props;return r=""===e||e&&0===e.size?null:e,a(n,r,t)};_onExampleSelect=e=>{this.props.oas3Actions.setActiveExamplesMember({name:e,pathMethod:this.props.pathMethod,contextType:"parameters",contextName:this.getParamKey()})};onChangeIncludeEmpty=e=>{let{specActions:t,param:r,pathMethod:a}=this.props;const n=r.get("name"),s=r.get("in");return t.updateEmptyParamInclusion(a,n,s,e)};setDefaultValue=()=>{let{specSelectors:e,pathMethod:t,rawParam:r,oas3Selectors:a,fn:n}=this.props;const s=e.parameterWithMetaByIdentity(t,r)||(0,k.Map)();let{schema:o}=getParameterSchema(s,{isOAS3:e.isOAS3()});const l=s.get("content",(0,k.Map)()).keySeq().first(),c=o?n.getSampleSchema(o.toJS(),l,{includeWriteOnly:!0}):null;if(s&&void 0===s.get("value")&&"body"!==s.get("in")){let r;if(e.isSwagger2())r=void 0!==s.get("x-example")?s.get("x-example"):void 0!==s.getIn(["schema","example"])?s.getIn(["schema","example"]):o&&o.getIn(["default"]);else if(e.isOAS3()){o=this.composeJsonSchema(o);const e=a.activeExamplesMember(...t,"parameters",this.getParamKey());r=void 0!==s.getIn(["examples",e,"value"])?s.getIn(["examples",e,"value"]):void 0!==s.getIn(["content",l,"example"])?s.getIn(["content",l,"example"]):void 0!==s.get("example")?s.get("example"):void 0!==(o&&o.get("example"))?o&&o.get("example"):void 0!==(o&&o.get("default"))?o&&o.get("default"):s.get("default")}void 0===r||k.List.isList(r)||(r=stringify(r));const i=n.getSchemaObjectType(o),p=n.getSchemaObjectType(o?.get("items"));void 0!==r?this.onChangeWrapper(r):"object"===i&&c&&!s.get("examples")?this.onChangeWrapper(k.List.isList(c)?c:stringify(c)):"array"===i&&"object"===p&&c&&!s.get("examples")&&this.onChangeWrapper(k.List.isList(c)?c:(0,k.List)(JSON.parse(c)))}};getParamKey(){const{param:e}=this.props;return e?`${e.get("name")}-${e.get("in")}`:null}composeJsonSchema(e){const{fn:t}=this.props,r=e.get("oneOf")?.get(0)?.toJS(),a=e.get("anyOf")?.get(0)?.toJS();return(0,k.fromJS)(t.mergeJsonSchema(e.toJS(),r??a??{}))}render(){let{param:e,rawParam:t,getComponent:r,getConfigs:a,isExecute:n,fn:s,onChangeConsumes:o,specSelectors:l,pathMethod:c,specPath:i,oas3Selectors:p}=this.props,m=l.isOAS3();const{showExtensions:u,showCommonExtensions:d}=a();if(e||(e=t),!t)return null;const h=r("JsonSchemaForm"),g=r("ParamBody");let y=e.get("in"),f="body"!==y?null:O().createElement(g,{getComponent:r,getConfigs:a,fn:s,param:e,consumes:l.consumesOptionsFor(c),consumesValue:l.contentTypeValues(c).get("requestContentType"),onChange:this.onChangeWrapper,onChangeConsumes:o,isExecute:n,specSelectors:l,pathMethod:c});const S=r("modelExample"),E=r("Markdown",!0),_=r("ParameterExt"),v=r("ParameterIncludeEmpty"),w=r("ExamplesSelectValueRetainer"),b=r("Example");let{schema:C}=getParameterSchema(e,{isOAS3:m}),x=l.parameterWithMetaByIdentity(c,t)||(0,k.Map)();m&&(C=this.composeJsonSchema(C));let N=C?C.get("format"):null,A="formData"===y,I="FormData"in K,j=e.get("required");const q=s.getSchemaObjectType(C),P=s.getSchemaObjectType(C?.get("items")),M=s.getSchemaObjectTypeLabel(C),T=!f&&"object"===q,R=!f&&"object"===P;let J,$,V,L,D=x?x.get("value"):"",U=d?getCommonExtensions(C):null,z=u?getExtensions(e):null,B=!1;void 0!==e&&C&&(J=C.get("items")),void 0!==J?($=J.get("enum"),V=J.get("default")):C&&($=C.get("enum")),$&&$.size&&$.size>0&&(B=!0),void 0!==e&&(C&&(V=C.get("default")),void 0===V&&(V=e.get("default")),L=e.get("example"),void 0===L&&(L=e.get("x-example")));const F=f?null:O().createElement(h,{fn:s,getComponent:r,value:D,required:j,disabled:!n,description:e.get("name"),onChange:this.onChangeWrapper,errors:x.get("errors"),schema:C});return O().createElement("tr",{"data-param-name":e.get("name"),"data-param-in":e.get("in")},O().createElement("td",{className:"parameters-col_name"},O().createElement("div",{className:j?"parameter__name required":"parameter__name"},e.get("name"),j?O().createElement("span",null," *"):null),O().createElement("div",{className:"parameter__type"},M,N&&O().createElement("span",{className:"prop-format"},"($",N,")")),O().createElement("div",{className:"parameter__deprecated"},m&&e.get("deprecated")?"deprecated":null),O().createElement("div",{className:"parameter__in"},"(",e.get("in"),")")),O().createElement("td",{className:"parameters-col_description"},e.get("description")?O().createElement(E,{source:e.get("description")}):null,!f&&n||!B?null:O().createElement(E,{className:"parameter__enum",source:"<i>Available values</i> : "+$.map((function(e){return e})).toArray().map(String).join(", ")}),!f&&n||void 0===V?null:O().createElement(E,{className:"parameter__default",source:"<i>Default value</i> : "+V}),!f&&n||void 0===L?null:O().createElement(E,{source:"<i>Example</i> : "+L}),A&&!I&&O().createElement("div",null,"Error: your browser does not support FormData"),m&&e.get("examples")?O().createElement("section",{className:"parameter-controls"},O().createElement(w,{examples:e.get("examples"),onSelect:this._onExampleSelect,updateValue:this.onChangeWrapper,getComponent:r,defaultToFirstExample:!0,currentKey:p.activeExamplesMember(...c,"parameters",this.getParamKey()),currentUserInputValue:D})):null,T||R?O().createElement(S,{getComponent:r,specPath:i.push("schema"),getConfigs:a,isExecute:n,specSelectors:l,schema:C,example:F}):F,f&&C?O().createElement(S,{getComponent:r,specPath:i.push("schema"),getConfigs:a,isExecute:n,specSelectors:l,schema:C,example:f,includeWriteOnly:!0}):null,!f&&n&&e.get("allowEmptyValue")?O().createElement(v,{onChange:this.onChangeIncludeEmpty,isIncluded:l.parameterInclusionSettingFor(c,e.get("name"),e.get("in")),isDisabled:!isEmptyValue(D)}):null,m&&e.get("examples")?O().createElement(b,{example:e.getIn(["examples",p.activeExamplesMember(...c,"parameters",this.getParamKey())]),getComponent:r,getConfigs:a}):null,d&&U.size?U.entrySeq().map((([e,t])=>O().createElement(_,{key:`${e}-${t}`,xKey:e,xVal:t}))):null,u&&z.size?z.entrySeq().map((([e,t])=>O().createElement(_,{key:`${e}-${t}`,xKey:e,xVal:t}))):null))}}class Execute extends x.Component{handleValidateParameters=()=>{let{specSelectors:e,specActions:t,path:r,method:a}=this.props;return t.validateParams([r,a]),e.validateBeforeExecute([r,a])};handleValidateRequestBody=()=>{let{path:e,method:t,specSelectors:r,oas3Selectors:a,oas3Actions:n}=this.props,s={missingBodyValue:!1,missingRequiredKeys:[]};n.clearRequestBodyValidateError({path:e,method:t});let o=r.getOAS3RequiredRequestBodyContentType([e,t]),l=a.requestBodyValue(e,t),c=a.validateBeforeExecute([e,t]),i=a.requestContentType(e,t);if(!c)return s.missingBodyValue=!0,n.setRequestBodyValidateError({path:e,method:t,validationErrors:s}),!1;if(!o)return!0;let p=a.validateShallowRequired({oas3RequiredRequestBodyContentType:o,oas3RequestContentType:i,oas3RequestBodyValue:l});return!p||p.length<1||(p.forEach((e=>{s.missingRequiredKeys.push(e)})),n.setRequestBodyValidateError({path:e,method:t,validationErrors:s}),!1)};handleValidationResultPass=()=>{let{specActions:e,operation:t,path:r,method:a}=this.props;this.props.onExecute&&this.props.onExecute(),e.execute({operation:t,path:r,method:a})};handleValidationResultFail=()=>{let{specActions:e,path:t,method:r}=this.props;e.clearValidateParams([t,r]),setTimeout((()=>{e.validateParams([t,r])}),40)};handleValidationResult=e=>{e?this.handleValidationResultPass():this.handleValidationResultFail()};onClick=()=>{let e=this.handleValidateParameters(),t=this.handleValidateRequestBody(),r=e&&t;this.handleValidationResult(r)};onChangeProducesWrapper=e=>this.props.specActions.changeProducesValue([this.props.path,this.props.method],e);render(){const{disabled:e}=this.props;return O().createElement("button",{className:"btn execute opblock-control__btn",onClick:this.onClick,disabled:e},"Execute")}}class headers_Headers extends O().Component{render(){let{headers:e,getComponent:t}=this.props;const r=t("Property"),a=t("Markdown",!0);return e&&e.size?O().createElement("div",{className:"headers-wrapper"},O().createElement("h4",{className:"headers__title"},"Headers:"),O().createElement("table",{className:"headers"},O().createElement("thead",null,O().createElement("tr",{className:"header-row"},O().createElement("th",{className:"header-col"},"Name"),O().createElement("th",{className:"header-col"},"Description"),O().createElement("th",{className:"header-col"},"Type"))),O().createElement("tbody",null,e.entrySeq().map((([e,t])=>{if(!A().Map.isMap(t))return null;const n=t.get("description"),s=t.getIn(["schema"])?t.getIn(["schema","type"]):t.getIn(["type"]),o=t.getIn(["schema","example"]);return O().createElement("tr",{key:e},O().createElement("td",{className:"header-col"},e),O().createElement("td",{className:"header-col"},n?O().createElement(a,{source:n}):null),O().createElement("td",{className:"header-col"},s," ",o?O().createElement(r,{propKey:"Example",propVal:o,propClass:"header-example"}):null))})).toArray()))):null}}class Errors extends O().Component{render(){let{editorActions:e,errSelectors:t,layoutSelectors:r,layoutActions:a,getComponent:n}=this.props;const s=n("Collapse");if(e&&e.jumpToLine)var o=e.jumpToLine;let l=t.allErrors().filter((e=>"thrown"===e.get("type")||"error"===e.get("level")));if(!l||l.count()<1)return null;let c=r.isShown(["errorPane"],!0),i=l.sortBy((e=>e.get("line")));return O().createElement("pre",{className:"errors-wrapper"},O().createElement("hgroup",{className:"error"},O().createElement("h4",{className:"errors__title"},"Errors"),O().createElement("button",{className:"btn errors__clear-btn",onClick:()=>a.show(["errorPane"],!c)},c?"Hide":"Show")),O().createElement(s,{isOpened:c,animated:!0},O().createElement("div",{className:"errors"},i.map(((e,t)=>{let r=e.get("type");return"thrown"===r||"auth"===r?O().createElement(ThrownErrorItem,{key:t,error:e.get("error")||e,jumpToLine:o}):"spec"===r?O().createElement(SpecErrorItem,{key:t,error:e,jumpToLine:o}):void 0})))))}}const ThrownErrorItem=({error:e,jumpToLine:t})=>{if(!e)return null;let r=e.get("line");return O().createElement("div",{className:"error-wrapper"},e?O().createElement("div",null,O().createElement("h4",null,e.get("source")&&e.get("level")?toTitleCase(e.get("source"))+" "+e.get("level"):"",e.get("path")?O().createElement("small",null," at ",e.get("path")):null),O().createElement("span",{className:"message thrown"},e.get("message")),O().createElement("div",{className:"error-line"},r&&t?O().createElement("a",{onClick:t.bind(null,r)},"Jump to line ",r):null)):null)},SpecErrorItem=({error:e,jumpToLine:t=null})=>{let r=null;return e.get("path")?r=k.List.isList(e.get("path"))?O().createElement("small",null,"at ",e.get("path").join(".")):O().createElement("small",null,"at ",e.get("path")):e.get("line")&&!t&&(r=O().createElement("small",null,"on line ",e.get("line"))),O().createElement("div",{className:"error-wrapper"},e?O().createElement("div",null,O().createElement("h4",null,toTitleCase(e.get("source"))+" "+e.get("level")," ",r),O().createElement("span",{className:"message"},e.get("message")),O().createElement("div",{className:"error-line"},t?O().createElement("a",{onClick:t.bind(null,e.get("line"))},"Jump to line ",e.get("line")):null)):null)};function toTitleCase(e){return(e||"").split(" ").map((e=>e[0].toUpperCase()+e.slice(1))).join(" ")}const content_type_noop=()=>{};class ContentType extends O().Component{static defaultProps={onChange:content_type_noop,value:null,contentTypes:(0,k.fromJS)(["application/json"])};componentDidMount(){this.props.contentTypes&&this.props.onChange(this.props.contentTypes.first())}UNSAFE_componentWillReceiveProps(e){e.contentTypes&&e.contentTypes.size&&(e.contentTypes.includes(e.value)||e.onChange(e.contentTypes.first()))}onChangeWrapper=e=>this.props.onChange(e.target.value);render(){let{ariaControls:e,ariaLabel:t,className:r,contentTypes:a,controlId:n,value:s}=this.props;return a&&a.size?O().createElement("div",{className:"content-type-wrapper "+(r||"")},O().createElement("select",{"aria-controls":e,"aria-label":t,className:"content-type",id:n,onChange:this.onChangeWrapper,value:s||""},a.map((e=>O().createElement("option",{key:e,value:e},e))).toArray())):null}}function xclass(...e){return e.filter((e=>!!e)).join(" ").trim()}class Container extends O().Component{render(){let{fullscreen:e,full:t,...r}=this.props;if(e)return O().createElement("section",r);let a="swagger-container"+(t?"-full":"");return O().createElement("section",Qe()({},r,{className:xclass(r.className,a)}))}}const nn={mobile:"",tablet:"-tablet",desktop:"-desktop",large:"-hd"};class Col extends O().Component{render(){const{hide:e,keepContents:t,mobile:r,tablet:a,desktop:n,large:s,...o}=this.props;if(e&&!t)return O().createElement("span",null);let l=[];for(let e in nn){if(!Object.prototype.hasOwnProperty.call(nn,e))continue;let t=nn[e];if(e in this.props){let r=this.props[e];if(r<1){l.push("none"+t);continue}l.push("block"+t),l.push("col-"+r+t)}}e&&l.push("hidden");let c=xclass(o.className,...l);return O().createElement("section",Qe()({},o,{className:c}))}}class Row extends O().Component{render(){return O().createElement("div",Qe()({},this.props,{className:xclass(this.props.className,"wrapper")}))}}class Button extends O().Component{static defaultProps={className:""};render(){return O().createElement("button",Qe()({},this.props,{className:xclass(this.props.className,"button")}))}}const TextArea=e=>O().createElement("textarea",e),Input=e=>O().createElement("input",e);class Select extends O().Component{static defaultProps={multiple:!1,allowEmptyValue:!0};constructor(e,t){let r;super(e,t),r=e.value?e.value:e.multiple?[""]:"",this.state={value:r}}onChange=e=>{let t,{onChange:r,multiple:a}=this.props,n=[].slice.call(e.target.options);t=a?n.filter((function(e){return e.selected})).map((function(e){return e.value})):e.target.value,this.setState({value:t}),r&&r(t)};UNSAFE_componentWillReceiveProps(e){e.value!==this.props.value&&this.setState({value:e.value})}render(){let{allowedValues:e,multiple:t,allowEmptyValue:r,disabled:a}=this.props,n=this.state.value?.toJS?.()||this.state.value;return O().createElement("select",{className:this.props.className,multiple:t,value:n,onChange:this.onChange,disabled:a},r?O().createElement("option",{value:""},"--"):null,e.map((function(e,t){return O().createElement("option",{key:t,value:String(e)},String(e))})))}}class Link extends O().Component{render(){return O().createElement("a",Qe()({},this.props,{rel:"noopener noreferrer",className:xclass(this.props.className,"link")}))}}const NoMargin=({children:e})=>O().createElement("div",{className:"no-margin"}," ",e," ");class Collapse extends O().Component{static defaultProps={isOpened:!1,animated:!1};renderNotAnimated(){return this.props.isOpened?O().createElement(NoMargin,null,this.props.children):O().createElement("noscript",null)}render(){let{animated:e,isOpened:t,children:r}=this.props;return e?(r=t?r:null,O().createElement(NoMargin,null,r)):this.renderNotAnimated()}}class Overview extends O().Component{constructor(...e){super(...e),this.setTagShown=this._setTagShown.bind(this)}_setTagShown(e,t){this.props.layoutActions.show(e,t)}showOp(e,t){let{layoutActions:r}=this.props;r.show(e,t)}render(){let{specSelectors:e,layoutSelectors:t,layoutActions:r,getComponent:a}=this.props,n=e.taggedOperations();const s=a("Collapse");return O().createElement("div",null,O().createElement("h4",{className:"overview-title"},"Overview"),n.map(((e,a)=>{let n=e.get("operations"),o=["overview-tags",a],l=t.isShown(o,!0);return O().createElement("div",{key:"overview-"+a},O().createElement("h4",{onClick:()=>r.show(o,!l),className:"link overview-tag"}," ",l?"-":"+",a),O().createElement(s,{isOpened:l,animated:!0},n.map((e=>{let{path:a,method:n,id:s}=e.toObject(),o="operations",l=s,c=t.isShown([o,l]);return O().createElement(OperationLink,{key:s,path:a,method:n,id:a+"-"+n,shown:c,showOpId:l,showOpIdPrefix:o,href:`#operation-${l}`,onClick:r.show})})).toArray()))})).toArray(),n.size<1&&O().createElement("h3",null," No operations defined in spec! "))}}class OperationLink extends O().Component{constructor(e){super(e),this.onClick=this._onClick.bind(this)}_onClick(){let{showOpId:e,showOpIdPrefix:t,onClick:r,shown:a}=this.props;r([t,e],!a)}render(){let{id:e,method:t,shown:r,href:a}=this.props;return O().createElement(Link,{href:a,onClick:this.onClick,className:"block opblock-link "+(r?"shown":"")},O().createElement("div",null,O().createElement("small",{className:`bold-label-${t}`},t.toUpperCase()),O().createElement("span",{className:"bold-label"},e)))}}class InitializedInput extends O().Component{componentDidMount(){this.props.initialValue&&(this.inputRef.value=this.props.initialValue)}render(){const{value:e,defaultValue:t,initialValue:r,...a}=this.props;return O().createElement("input",Qe()({},a,{ref:e=>this.inputRef=e}))}}class InfoBasePath extends O().Component{render(){const{host:e,basePath:t}=this.props;return O().createElement("pre",{className:"base-url"},"[ Base URL: ",e,t," ]")}}class InfoUrl extends O().PureComponent{render(){const{url:e,getComponent:t}=this.props,r=t("Link");return O().createElement(r,{target:"_blank",href:sanitizeUrl(e)},O().createElement("span",{className:"url"}," ",e))}}class Info extends O().Component{render(){const{info:e,url:t,host:r,basePath:a,getComponent:n,externalDocs:s,selectedServer:o,url:l}=this.props,c=e.get("version"),i=e.get("description"),p=e.get("title"),m=safeBuildUrl(e.get("termsOfService"),l,{selectedServer:o}),u=e.get("contact"),d=e.get("license"),h=safeBuildUrl(s&&s.get("url"),l,{selectedServer:o}),g=s&&s.get("description"),y=n("Markdown",!0),f=n("Link"),S=n("VersionStamp"),E=n("OpenAPIVersion"),_=n("InfoUrl"),v=n("InfoBasePath"),w=n("License"),b=n("Contact");return O().createElement("div",{className:"info"},O().createElement("hgroup",{className:"main"},O().createElement("h2",{className:"title"},p,O().createElement("span",null,c&&O().createElement(S,{version:c}),O().createElement(E,{oasVersion:"2.0"}))),r||a?O().createElement(v,{host:r,basePath:a}):null,t&&O().createElement(_,{getComponent:n,url:t})),O().createElement("div",{className:"description"},O().createElement(y,{source:i})),m&&O().createElement("div",{className:"info__tos"},O().createElement(f,{target:"_blank",href:sanitizeUrl(m)},"Terms of service")),u?.size>0&&O().createElement(b,{getComponent:n,data:u,selectedServer:o,url:t}),d?.size>0&&O().createElement(w,{getComponent:n,license:d,selectedServer:o,url:t}),h?O().createElement(f,{className:"info__extdocs",target:"_blank",href:sanitizeUrl(h)},g||h):null)}}const sn=Info;class InfoContainer extends O().Component{render(){const{specSelectors:e,getComponent:t,oas3Selectors:r}=this.props,a=e.info(),n=e.url(),s=e.basePath(),o=e.host(),l=e.externalDocs(),c=r.selectedServer(),i=t("info");return O().createElement("div",null,a&&a.count()?O().createElement(i,{info:a,url:n,host:o,basePath:s,externalDocs:l,getComponent:t,selectedServer:c}):null)}}class Contact extends O().Component{render(){const{data:e,getComponent:t,selectedServer:r,url:a}=this.props,n=e.get("name","the developer"),s=safeBuildUrl(e.get("url"),a,{selectedServer:r}),o=e.get("email"),l=t("Link");return O().createElement("div",{className:"info__contact"},s&&O().createElement("div",null,O().createElement(l,{href:sanitizeUrl(s),target:"_blank"},n," - Website")),o&&O().createElement(l,{href:sanitizeUrl(`mailto:${o}`)},s?`Send email to ${n}`:`Contact ${n}`))}}const on=Contact;class License extends O().Component{render(){const{license:e,getComponent:t,selectedServer:r,url:a}=this.props,n=e.get("name","License"),s=safeBuildUrl(e.get("url"),a,{selectedServer:r}),o=t("Link");return O().createElement("div",{className:"info__license"},s?O().createElement("div",{className:"info__license__url"},O().createElement(o,{target:"_blank",href:sanitizeUrl(s)},n)):O().createElement("span",null,n))}}const ln=License;class JumpToPath extends O().Component{render(){return null}}class CopyToClipboardBtn extends O().Component{render(){let{getComponent:e}=this.props;const t=e("CopyIcon");return O().createElement("div",{className:"view-line-link copy-to-clipboard",title:"Copy to clipboard"},O().createElement(mt.CopyToClipboard,{text:this.props.textToCopy},O().createElement(t,null)))}}class Footer extends O().Component{render(){return O().createElement("div",{className:"footer"})}}class FilterContainer extends O().Component{onFilterChange=e=>{const{target:{value:t}}=e;this.props.layoutActions.updateFilter(t)};render(){const{specSelectors:e,layoutSelectors:t,getComponent:r}=this.props,a=r("Col"),n="loading"===e.loadingStatus(),s="failed"===e.loadingStatus(),o=t.currentFilter(),l=["operation-filter-input"];return s&&l.push("failed"),n&&l.push("loading"),O().createElement("div",null,!1===o?null:O().createElement("div",{className:"filter-container"},O().createElement(a,{className:"filter wrapper",mobile:12},O().createElement("input",{className:l.join(" "),placeholder:"Filter by tag",type:"text",onChange:this.onFilterChange,value:"string"==typeof o?o:"",disabled:n}))))}}const cn=Function.prototype;class ParamBody extends x.PureComponent{static defaultProp={consumes:(0,k.fromJS)(["application/json"]),param:(0,k.fromJS)({}),onChange:cn,onChangeConsumes:cn};constructor(e,t){super(e,t),this.state={isEditBox:!1,value:""}}componentDidMount(){this.updateValues.call(this,this.props)}UNSAFE_componentWillReceiveProps(e){this.updateValues.call(this,e)}updateValues=e=>{let{param:t,isExecute:r,consumesValue:a=""}=e,n=/xml/i.test(a),s=/json/i.test(a),o=n?t.get("value_xml"):t.get("value");if(void 0!==o){let e=!o&&s?"{}":o;this.setState({value:e}),this.onChange(e,{isXml:n,isEditBox:r})}else n?this.onChange(this.sample("xml"),{isXml:n,isEditBox:r}):this.onChange(this.sample(),{isEditBox:r})};sample=e=>{let{param:t,fn:r}=this.props,a=r.inferSchema(t.toJS());return r.getSampleSchema(a,e,{includeWriteOnly:!0})};onChange=(e,{isEditBox:t,isXml:r})=>{this.setState({value:e,isEditBox:t}),this._onChange(e,r)};_onChange=(e,t)=>{(this.props.onChange||cn)(e,t)};handleOnChange=e=>{const{consumesValue:t}=this.props,r=/xml/i.test(t),a=e.target.value;this.onChange(a,{isXml:r,isEditBox:this.state.isEditBox})};toggleIsEditBox=()=>this.setState((e=>({isEditBox:!e.isEditBox})));render(){let{onChangeConsumes:e,param:t,isExecute:r,specSelectors:a,pathMethod:n,getComponent:s}=this.props;const o=s("Button"),l=s("TextArea"),c=s("HighlightCode",!0),i=s("contentType");let p=(a?a.parameterWithMetaByIdentity(n,t):t).get("errors",(0,k.List)()),m=a.contentTypeValues(n).get("requestContentType"),u=this.props.consumes&&this.props.consumes.size?this.props.consumes:ParamBody.defaultProp.consumes,{value:d,isEditBox:h}=this.state,g=null;getKnownSyntaxHighlighterLanguage(d)&&(g="json");const y=`${createHtmlReadyId(`${n[1]}${n[0]}_parameters`)}_select`;return O().createElement("div",{className:"body-param","data-param-name":t.get("name"),"data-param-in":t.get("in")},h&&r?O().createElement(l,{className:"body-param__text"+(p.count()?" invalid":""),value:d,onChange:this.handleOnChange}):d&&O().createElement(c,{className:"body-param__example",language:g},d),O().createElement("div",{className:"body-param-options"},r?O().createElement("div",{className:"body-param-edit"},O().createElement(o,{className:h?"btn cancel body-param__example-edit":"btn edit body-param__example-edit",onClick:this.toggleIsEditBox},h?"Cancel":"Edit")):null,O().createElement("label",{htmlFor:y},O().createElement("span",null,"Parameter content type"),O().createElement(i,{value:m,contentTypes:u,onChange:e,className:"body-param-content-type",ariaLabel:"Parameter content type",controlId:y}))))}}class Curl extends O().Component{render(){const{request:e,getComponent:t}=this.props,r=requestSnippetGenerator_curl_bash(e),a=t("SyntaxHighlighter",!0);return O().createElement("div",{className:"curl-command"},O().createElement("h4",null,"Curl"),O().createElement("div",{className:"copy-to-clipboard"},O().createElement(mt.CopyToClipboard,{text:r},O().createElement("button",null))),O().createElement("div",null,O().createElement(a,{language:"bash",className:"curl microlight",renderPlainText:({children:e,PlainTextViewer:t})=>O().createElement(t,{className:"curl"},e)},r)))}}const property=({propKey:e,propVal:t,propClass:r})=>O().createElement("span",{className:r},O().createElement("br",null),e,": ",String(t));class TryItOutButton extends O().Component{static defaultProps={onTryoutClick:Function.prototype,onCancelClick:Function.prototype,onResetClick:Function.prototype,enabled:!1,hasUserEditedBody:!1,isOAS3:!1};render(){const{onTryoutClick:e,onCancelClick:t,onResetClick:r,enabled:a,hasUserEditedBody:n,isOAS3:s}=this.props,o=s&&n;return O().createElement("div",{className:o?"try-out btn-group":"try-out"},a?O().createElement("button",{className:"btn try-out__btn cancel",onClick:t},"Cancel"):O().createElement("button",{className:"btn try-out__btn",onClick:e},"Try it out "),o&&O().createElement("button",{className:"btn try-out__btn reset",onClick:r},"Reset"))}}class VersionPragmaFilter extends O().PureComponent{static defaultProps={alsoShow:null,children:null,bypass:!1};render(){const{bypass:e,isSwagger2:t,isOAS3:r,alsoShow:a}=this.props;return e?O().createElement("div",null,this.props.children):t&&r?O().createElement("div",{className:"version-pragma"},a,O().createElement("div",{className:"version-pragma__message version-pragma__message--ambiguous"},O().createElement("div",null,O().createElement("h3",null,"Unable to render this definition"),O().createElement("p",null,O().createElement("code",null,"swagger")," and ",O().createElement("code",null,"openapi")," fields cannot be present in the same Swagger or OpenAPI definition. Please remove one of the fields."),O().createElement("p",null,"Supported version fields are ",O().createElement("code",null,"swagger: ",'"2.0"')," and those that match ",O().createElement("code",null,"openapi: 3.0.n")," (for example, ",O().createElement("code",null,"openapi: 3.0.4"),").")))):t||r?O().createElement("div",null,this.props.children):O().createElement("div",{className:"version-pragma"},a,O().createElement("div",{className:"version-pragma__message version-pragma__message--missing"},O().createElement("div",null,O().createElement("h3",null,"Unable to render this definition"),O().createElement("p",null,"The provided definition does not specify a valid version field."),O().createElement("p",null,"Please indicate a valid Swagger or OpenAPI version field. Supported version fields are ",O().createElement("code",null,"swagger: ",'"2.0"')," and those that match ",O().createElement("code",null,"openapi: 3.0.n")," (for example, ",O().createElement("code",null,"openapi: 3.0.4"),")."))))}}const version_stamp=({version:e})=>O().createElement("small",null,O().createElement("pre",{className:"version"}," ",e," ")),openapi_version=({oasVersion:e})=>O().createElement("small",{className:"version-stamp"},O().createElement("pre",{className:"version"},"OAS ",e)),deep_link=({enabled:e,path:t,text:r})=>O().createElement("a",{className:"nostyle",onClick:e?e=>e.preventDefault():null,href:e?`#/${t}`:null},O().createElement("span",null,r)),svg_assets=()=>O().createElement("div",null,O().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",xmlnsXlink:"http://www.w3.org/1999/xlink",className:"svg-assets"},O().createElement("defs",null,O().createElement("symbol",{viewBox:"0 0 20 20",id:"unlocked"},O().createElement("path",{d:"M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V6h2v-.801C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8z"})),O().createElement("symbol",{viewBox:"0 0 20 20",id:"locked"},O().createElement("path",{d:"M15.8 8H14V5.6C14 2.703 12.665 1 10 1 7.334 1 6 2.703 6 5.6V8H4c-.553 0-1 .646-1 1.199V17c0 .549.428 1.139.951 1.307l1.197.387C5.672 18.861 6.55 19 7.1 19h5.8c.549 0 1.428-.139 1.951-.307l1.196-.387c.524-.167.953-.757.953-1.306V9.199C17 8.646 16.352 8 15.8 8zM12 8H8V5.199C8 3.754 8.797 3 10 3c1.203 0 2 .754 2 2.199V8z"})),O().createElement("symbol",{viewBox:"0 0 20 20",id:"close"},O().createElement("path",{d:"M14.348 14.849c-.469.469-1.229.469-1.697 0L10 11.819l-2.651 3.029c-.469.469-1.229.469-1.697 0-.469-.469-.469-1.229 0-1.697l2.758-3.15-2.759-3.152c-.469-.469-.469-1.228 0-1.697.469-.469 1.228-.469 1.697 0L10 8.183l2.651-3.031c.469-.469 1.228-.469 1.697 0 .469.469.469 1.229 0 1.697l-2.758 3.152 2.758 3.15c.469.469.469 1.229 0 1.698z"})),O().createElement("symbol",{viewBox:"0 0 20 20",id:"large-arrow"},O().createElement("path",{d:"M13.25 10L6.109 2.58c-.268-.27-.268-.707 0-.979.268-.27.701-.27.969 0l7.83 7.908c.268.271.268.709 0 .979l-7.83 7.908c-.268.271-.701.27-.969 0-.268-.269-.268-.707 0-.979L13.25 10z"})),O().createElement("symbol",{viewBox:"0 0 20 20",id:"large-arrow-down"},O().createElement("path",{d:"M17.418 6.109c.272-.268.709-.268.979 0s.271.701 0 .969l-7.908 7.83c-.27.268-.707.268-.979 0l-7.908-7.83c-.27-.268-.27-.701 0-.969.271-.268.709-.268.979 0L10 13.25l7.418-7.141z"})),O().createElement("symbol",{viewBox:"0 0 20 20",id:"large-arrow-up"},O().createElement("path",{d:"M 17.418 14.908 C 17.69 15.176 18.127 15.176 18.397 14.908 C 18.667 14.64 18.668 14.207 18.397 13.939 L 10.489 6.109 C 10.219 5.841 9.782 5.841 9.51 6.109 L 1.602 13.939 C 1.332 14.207 1.332 14.64 1.602 14.908 C 1.873 15.176 2.311 15.176 2.581 14.908 L 10 7.767 L 17.418 14.908 Z"})),O().createElement("symbol",{viewBox:"0 0 24 24",id:"jump-to"},O().createElement("path",{d:"M19 7v4H5.83l3.58-3.59L8 6l-6 6 6 6 1.41-1.41L5.83 13H21V7z"})),O().createElement("symbol",{viewBox:"0 0 24 24",id:"expand"},O().createElement("path",{d:"M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"})),O().createElement("symbol",{viewBox:"0 0 15 16",id:"copy"},O().createElement("g",{transform:"translate(2, -1)"},O().createElement("path",{fill:"#ffffff",fillRule:"evenodd",d:"M2 13h4v1H2v-1zm5-6H2v1h5V7zm2 3V8l-3 3 3 3v-2h5v-2H9zM4.5 9H2v1h2.5V9zM2 12h2.5v-1H2v1zm9 1h1v2c-.02.28-.11.52-.3.7-.19.18-.42.28-.7.3H1c-.55 0-1-.45-1-1V4c0-.55.45-1 1-1h3c0-1.11.89-2 2-2 1.11 0 2 .89 2 2h3c.55 0 1 .45 1 1v5h-1V6H1v9h10v-2zM2 5h8c0-.55-.45-1-1-1H8c-.55 0-1-.45-1-1s-.45-1-1-1-1 .45-1 1-.45 1-1 1H3c-.55 0-1 .45-1 1z"})))))),pn=require("remarkable"),mn=require("remarkable/linkify"),un=require("dompurify");var dn=__webpack_require__.n(un);dn().addHook&&dn().addHook("beforeSanitizeElements",(function(e){return e.href&&e.setAttribute("rel","noopener noreferrer"),e}));const hn=function Markdown({source:e,className:t="",getConfigs:r=()=>({useUnsafeMarkdown:!1})}){if("string"!=typeof e)return null;const a=new pn.Remarkable({html:!0,typographer:!0,breaks:!0,linkTarget:"_blank"}).use(mn.linkify);a.core.ruler.disable(["replacements","smartquotes"]);const{useUnsafeMarkdown:n}=r(),s=a.render(e),o=sanitizer(s,{useUnsafeMarkdown:n});return e&&s&&o?O().createElement("div",{className:pt()(t,"markdown"),dangerouslySetInnerHTML:{__html:o}}):null};function sanitizer(e,{useUnsafeMarkdown:t=!1}={}){const r=t,a=t?[]:["style","class"];return t&&!sanitizer.hasWarnedAboutDeprecation&&(console.warn("useUnsafeMarkdown display configuration parameter is deprecated since >3.26.0 and will be removed in v4.0.0."),sanitizer.hasWarnedAboutDeprecation=!0),dn().sanitize(e,{ADD_ATTR:["target"],FORBID_TAGS:["style","form"],ALLOW_DATA_ATTR:r,FORBID_ATTR:a})}sanitizer.hasWarnedAboutDeprecation=!1;class BaseLayout extends O().Component{render(){const{errSelectors:e,specSelectors:t,getComponent:r}=this.props,a=r("SvgAssets"),n=r("InfoContainer",!0),s=r("VersionPragmaFilter"),o=r("operations",!0),l=r("Models",!0),c=r("Webhooks",!0),i=r("Row"),p=r("Col"),m=r("errors",!0),u=r("ServersContainer",!0),d=r("SchemesContainer",!0),h=r("AuthorizeBtnContainer",!0),g=r("FilterContainer",!0),y=t.isSwagger2(),f=t.isOAS3(),S=t.isOAS31(),E=!t.specStr(),_=t.loadingStatus();let v=null;if("loading"===_&&(v=O().createElement("div",{className:"info"},O().createElement("div",{className:"loading-container"},O().createElement("div",{className:"loading"})))),"failed"===_&&(v=O().createElement("div",{className:"info"},O().createElement("div",{className:"loading-container"},O().createElement("h4",{className:"title"},"Failed to load API definition."),O().createElement(m,null)))),"failedConfig"===_){const t=e.lastError(),r=t?t.get("message"):"";v=O().createElement("div",{className:"info failed-config"},O().createElement("div",{className:"loading-container"},O().createElement("h4",{className:"title"},"Failed to load remote configuration."),O().createElement("p",null,r)))}if(!v&&E&&(v=O().createElement("h4",null,"No API definition provided.")),v)return O().createElement("div",{className:"swagger-ui"},O().createElement("div",{className:"loading-container"},v));const w=t.servers(),b=t.schemes(),C=w&&w.size,x=b&&b.size,N=!!t.securityDefinitions();return O().createElement("div",{className:"swagger-ui"},O().createElement(a,null),O().createElement(s,{isSwagger2:y,isOAS3:f,alsoShow:O().createElement(m,null)},O().createElement(m,null),O().createElement(i,{className:"information-container"},O().createElement(p,{mobile:12},O().createElement(n,null))),C||x||N?O().createElement("div",{className:"scheme-container"},O().createElement(p,{className:"schemes wrapper",mobile:12},C||x?O().createElement("div",{className:"schemes-server-container"},C?O().createElement(u,null):null,x?O().createElement(d,null):null):null,N?O().createElement(h,null):null)):null,O().createElement(g,null),O().createElement(i,null,O().createElement(p,{mobile:12,desktop:12},O().createElement(o,null))),S&&O().createElement(i,{className:"webhooks-container"},O().createElement(p,{mobile:12,desktop:12},O().createElement(c,null))),O().createElement(i,null,O().createElement(p,{mobile:12,desktop:12},O().createElement(l,null)))))}}const core_components=()=>({components:{App:Ga,authorizationPopup:AuthorizationPopup,authorizeBtn:AuthorizeBtn,AuthorizeBtnContainer,authorizeOperationBtn:AuthorizeOperationBtn,auths:Auths,AuthItem:auth_item_Auths,authError:AuthError,oauth2:Oauth2,apiKeyAuth:ApiKeyAuth,basicAuth:BasicAuth,clear:Clear,liveResponse:LiveResponse,InitializedInput,info:sn,InfoContainer,InfoUrl,InfoBasePath,Contact:on,License:ln,JumpToPath,CopyToClipboardBtn,onlineValidatorBadge:OnlineValidatorBadge,operations:Operations,operation:Operation,OperationSummary,OperationSummaryMethod,OperationSummaryPath,responses:Responses,response:Response,ResponseExtension:response_extension,responseBody:ResponseBody,parameters:Parameters,parameterRow:ParameterRow,execute:Execute,headers:headers_Headers,errors:Errors,contentType:ContentType,overview:Overview,footer:Footer,FilterContainer,ParamBody,curl:Curl,Property:property,TryItOutButton,Markdown:hn,BaseLayout,VersionPragmaFilter,VersionStamp:version_stamp,OperationExt:operation_extensions,OperationExtRow:operation_extension_row,ParameterExt:parameter_extension,ParameterIncludeEmpty,OperationTag,OperationContainer,OpenAPIVersion:openapi_version,DeepLink:deep_link,SvgAssets:svg_assets,Example,ExamplesSelect,ExamplesSelectValueRetainer}}),form_components=()=>({components:{...E}}),base=()=>[configsPlugin,util,logs,view,view_legacy,plugins_spec,err,icons,plugins_layout,json_schema_5,json_schema_5_samples,core_components,form_components,swagger_client,auth,downloadUrlPlugin,deep_linking,filter,on_complete,plugins_request_snippets,syntax_highlighting,versions,safe_render()],gn=(0,k.Map)();function onlyOAS3(e){return(t,r)=>(...a)=>{if(r.getSystem().specSelectors.isOAS3()){const t=e(...a);return"function"==typeof t?t(r):t}return t(...a)}}const yn=onlyOAS3(Vt()(null)),fn=onlyOAS3(((e,t)=>e=>e.getSystem().specSelectors.findSchema(t))),Sn=onlyOAS3((()=>e=>{const t=e.getSystem().specSelectors.specJson().getIn(["components","schemas"]);return k.Map.isMap(t)?t:gn})),En=onlyOAS3((()=>e=>e.getSystem().specSelectors.specJson().hasIn(["servers",0]))),_n=onlyOAS3((0,_e.createSelector)(Ht,(e=>e.getIn(["components","securitySchemes"])||null))),wrap_selectors_validOperationMethods=(e,t)=>(r,...a)=>t.specSelectors.isOAS3()?t.oas3Selectors.validOperationMethods():e(...a),vn=yn,wn=yn,bn=yn,Cn=yn,xn=yn;const On=function wrap_selectors_onlyOAS3(e){return(t,r)=>(...a)=>{if(r.getSystem().specSelectors.isOAS3()){let t=r.getState().getIn(["spec","resolvedSubtrees","components","securitySchemes"]);return e(r,t,...a)}return t(...a)}}((0,_e.createSelector)((e=>e),(({specSelectors:e})=>e.securityDefinitions()),((e,t)=>{let r=(0,k.List)();return t?(t.entrySeq().forEach((([e,t])=>{const a=t?.get("type");if("oauth2"===a&&t.get("flows").entrySeq().forEach((([a,n])=>{let s=(0,k.fromJS)({flow:a,authorizationUrl:n.get("authorizationUrl"),tokenUrl:n.get("tokenUrl"),scopes:n.get("scopes"),type:t.get("type"),description:t.get("description")});r=r.push(new k.Map({[e]:s.filter((e=>void 0!==e))}))})),"http"!==a&&"apiKey"!==a||(r=r.push(new k.Map({[e]:t}))),"openIdConnect"===a&&t.get("openIdConnectData")){let a=t.get("openIdConnectData");(a.get("grant_types_supported")||["authorization_code","implicit"]).forEach((n=>{let s=a.get("scopes_supported")&&a.get("scopes_supported").reduce(((e,t)=>e.set(t,"")),new k.Map),o=(0,k.fromJS)({flow:n,authorizationUrl:a.get("authorization_endpoint"),tokenUrl:a.get("token_endpoint"),scopes:s,type:"oauth2",openIdConnectUrl:t.get("openIdConnectUrl")});r=r.push(new k.Map({[e]:o.filter((e=>void 0!==e))}))}))}})),r):r})));function OAS3ComponentWrapFactory(e){return(t,r)=>a=>"function"==typeof r.specSelectors?.isOAS3?r.specSelectors.isOAS3()?O().createElement(e,Qe()({},a,r,{Ori:t})):O().createElement(t,a):(console.warn("OAS3 wrapper: couldn't get spec"),null)}const Nn=(0,k.Map)(),selectors_isSwagger2=()=>e=>function isSwagger2(e){const t=e.get("swagger");return"string"==typeof t&&"2.0"===t}(e.getSystem().specSelectors.specJson()),selectors_isOAS30=()=>e=>function isOAS30(e){const t=e.get("openapi");return"string"==typeof t&&/^3\.0\.(?:[1-9]\d*|0)$/.test(t)}(e.getSystem().specSelectors.specJson()),selectors_isOAS3=()=>e=>e.getSystem().specSelectors.isOAS30();function selectors_onlyOAS3(e){return(t,...r)=>a=>{if(a.specSelectors.isOAS3()){const n=e(t,...r);return"function"==typeof n?n(a):n}return null}}const kn=selectors_onlyOAS3((()=>e=>e.specSelectors.specJson().get("servers",Nn))),findSchema=(e,t)=>{const r=e.getIn(["resolvedSubtrees","components","schemas",t],null),a=e.getIn(["json","components","schemas",t],null);return r||a||null},An=selectors_onlyOAS3(((e,{callbacks:t,specPath:r})=>e=>{const a=e.specSelectors.validOperationMethods();return k.Map.isMap(t)?t.reduce(((e,t,n)=>{if(!k.Map.isMap(t))return e;const s=t.reduce(((e,t,s)=>{if(!k.Map.isMap(t))return e;const o=t.entrySeq().filter((([e])=>a.includes(e))).map((([e,t])=>({operation:(0,k.Map)({operation:t}),method:e,path:s,callbackName:n,specPath:r.concat([n,s,e])})));return e.concat(o)}),(0,k.List)());return e.concat(s)}),(0,k.List)()).groupBy((e=>e.callbackName)).map((e=>e.toArray())).toObject():{}})),callbacks=({callbacks:e,specPath:t,specSelectors:r,getComponent:a})=>{const n=r.callbacksOperations({callbacks:e,specPath:t}),s=Object.keys(n),o=a("OperationContainer",!0);return 0===s.length?O().createElement("span",null,"No callbacks"):O().createElement("div",null,s.map((e=>O().createElement("div",{key:`${e}`},O().createElement("h2",null,e),n[e].map((t=>O().createElement(o,{key:`${e}-${t.path}-${t.method}`,op:t.operation,tag:"callbacks",method:t.method,path:t.path,specPath:t.specPath,allowTryItOut:!1})))))))},getDefaultRequestBodyValue=(e,t,r,a)=>{const n=e.getIn(["content",t])??(0,k.OrderedMap)(),s=n.get("schema",(0,k.OrderedMap)()).toJS(),o=void 0!==n.get("examples"),l=n.get("example"),c=o?n.getIn(["examples",r,"value"]):l;return stringify(a.getSampleSchema(s,t,{includeWriteOnly:!0},c))},request_body=({userHasEditedBody:e,requestBody:t,requestBodyValue:r,requestBodyInclusionSetting:a,requestBodyErrors:n,getComponent:s,getConfigs:o,specSelectors:l,fn:c,contentType:i,isExecute:p,specPath:m,onChange:u,onChangeIncludeEmpty:d,activeExamplesKey:h,updateActiveExamplesKey:g,setRetainRequestBodyValueFlag:y})=>{const handleFile=e=>{u(e.target.files[0])},setIsIncludedOptions=e=>{let t={key:e,shouldDispatchInit:!1,defaultValue:!0};return"no value"===a.get(e,"no value")&&(t.shouldDispatchInit=!0),t},f=s("Markdown",!0),S=s("modelExample"),E=s("RequestBodyEditor"),_=s("HighlightCode",!0),v=s("ExamplesSelectValueRetainer"),w=s("Example"),b=s("ParameterIncludeEmpty"),{showCommonExtensions:C}=o(),x=t?.get("description")??null,N=t?.get("content")??new k.OrderedMap;i=i||N.keySeq().first()||"";const A=N.get(i)??(0,k.OrderedMap)(),I=A.get("schema",(0,k.OrderedMap)()),j=A.get("examples",null),q=j?.map(((e,r)=>{const a=e?.get("value",null);return a&&(e=e.set("value",getDefaultRequestBodyValue(t,i,r,c),a)),e}));n=k.List.isList(n)?n:(0,k.List)();if(c.isFileUploadIntended(A?.get("schema"),i)){const e=s("Input");return p?O().createElement(e,{type:"file",onChange:handleFile}):O().createElement("i",null,"Example values are not available for ",O().createElement("code",null,i)," media types.")}if(!A.size)return null;if(c.hasSchemaType(A.get("schema"),"object")&&("application/x-www-form-urlencoded"===i||0===i.indexOf("multipart/"))&&I.get("properties",(0,k.OrderedMap)()).size>0){const e=s("JsonSchemaForm"),t=s("ParameterExt"),i=I.get("properties",(0,k.OrderedMap)());return r=k.Map.isMap(r)?r:(0,k.OrderedMap)(),O().createElement("div",{className:"table-container"},x&&O().createElement(f,{source:x}),O().createElement("table",null,O().createElement("tbody",null,k.Map.isMap(i)&&i.entrySeq().map((([i,h])=>{if(h.get("readOnly"))return;const g=h.get("oneOf")?.get(0)?.toJS(),y=h.get("anyOf")?.get(0)?.toJS();h=(0,k.fromJS)(c.mergeJsonSchema(h.toJS(),g??y??{}));let E=C?getCommonExtensions(h):null;const _=I.get("required",(0,k.List)()).includes(i),v=c.getSchemaObjectType(h),w=c.getSchemaObjectTypeLabel(h),x=c.getSchemaObjectType(h?.get("items")),N=h.get("format"),A=h.get("description"),j=r.getIn([i,"value"]),q=r.getIn([i,"errors"])||n,P=a.get(i)||!1;let M=c.getSampleSchema(h,!1,{includeWriteOnly:!0});!1===M&&(M="false"),0===M&&(M="0"),"string"!=typeof M&&"object"===v&&(M=stringify(M)),"string"==typeof M&&"array"===v&&(M=JSON.parse(M));const T=c.isFileUploadIntended(h),R=O().createElement(e,{fn:c,dispatchInitialValue:!T,schema:h,description:i,getComponent:s,value:void 0===j?M:j,required:_,errors:q,onChange:e=>{u(e,[i])}});return O().createElement("tr",{key:i,className:"parameters","data-property-name":i},O().createElement("td",{className:"parameters-col_name"},O().createElement("div",{className:_?"parameter__name required":"parameter__name"},i,_?O().createElement("span",null," *"):null),O().createElement("div",{className:"parameter__type"},w,N&&O().createElement("span",{className:"prop-format"},"($",N,")"),C&&E.size?E.entrySeq().map((([e,r])=>O().createElement(t,{key:`${e}-${r}`,xKey:e,xVal:r}))):null),O().createElement("div",{className:"parameter__deprecated"},h.get("deprecated")?"deprecated":null)),O().createElement("td",{className:"parameters-col_description"},O().createElement(f,{source:A}),p?O().createElement("div",null,"object"===v||"object"===x?O().createElement(S,{getComponent:s,specPath:m.push("schema"),getConfigs:o,isExecute:p,specSelectors:l,schema:h,example:R}):R,_?null:O().createElement(b,{onChange:e=>d(i,e),isIncluded:P,isIncludedOptions:setIsIncludedOptions(i),isDisabled:Array.isArray(j)?0!==j.length:!isEmptyValue(j)})):null))})))))}const P=getDefaultRequestBodyValue(t,i,h,c);let M=null;getKnownSyntaxHighlighterLanguage(P)&&(M="json");const T=p?O().createElement(E,{value:r,errors:n,defaultValue:P,onChange:u,getComponent:s}):O().createElement(_,{className:"body-param__example",language:M},stringify(r)||P);return O().createElement("div",null,x&&O().createElement(f,{source:x}),q?O().createElement(v,{userHasEditedBody:e,examples:q,currentKey:h,currentUserInputValue:r,onSelect:e=>{g(e)},updateValue:u,defaultToFirstExample:!0,getComponent:s,setRetainRequestBodyValueFlag:y}):null,O().createElement(S,{getComponent:s,getConfigs:o,specSelectors:l,expandDepth:1,isExecute:p,schema:A.get("schema"),specPath:m.push("content",i),example:T,includeWriteOnly:!0}),q?O().createElement(w,{example:q.get(h),getComponent:s,getConfigs:o}):null)};class operation_link_OperationLink extends x.Component{render(){const{link:e,name:t,getComponent:r}=this.props,a=r("Markdown",!0);let n=e.get("operationId")||e.get("operationRef"),s=e.get("parameters")&&e.get("parameters").toJS(),o=e.get("description");return O().createElement("div",{className:"operation-link"},O().createElement("div",{className:"description"},O().createElement("b",null,O().createElement("code",null,t)),o?O().createElement(a,{source:o}):null),O().createElement("pre",null,"Operation `",n,"`",O().createElement("br",null),O().createElement("br",null),"Parameters ",function padString(e,t){if("string"!=typeof t)return"";return t.split("\n").map(((t,r)=>r>0?Array(e+1).join(" ")+t:t)).join("\n")}(0,JSON.stringify(s,null,2))||"{}",O().createElement("br",null)))}}const In=operation_link_OperationLink,components_servers=({servers:e,currentServer:t,setSelectedServer:r,setServerVariableValue:a,getServerVariable:n,getEffectiveServerValue:s})=>{const o=(e.find((e=>e.get("url")===t))||(0,k.OrderedMap)()).get("variables")||(0,k.OrderedMap)(),l=0!==o.size;(0,x.useEffect)((()=>{t||r(e.first()?.get("url"))}),[]),(0,x.useEffect)((()=>{const n=e.find((e=>e.get("url")===t));if(!n)return void r(e.first().get("url"));(n.get("variables")||(0,k.OrderedMap)()).map(((e,r)=>{a({server:t,key:r,val:e.get("default")||""})}))}),[t,e]);const c=(0,x.useCallback)((e=>{r(e.target.value)}),[r]),i=(0,x.useCallback)((e=>{const r=e.target.getAttribute("data-variable"),n=e.target.value;a({server:t,key:r,val:n})}),[a,t]);return O().createElement("div",{className:"servers"},O().createElement("label",{htmlFor:"servers"},O().createElement("select",{onChange:c,value:t,id:"servers"},e.valueSeq().map((e=>O().createElement("option",{value:e.get("url"),key:e.get("url")},e.get("url"),e.get("description")&&` - ${e.get("description")}`))).toArray())),l&&O().createElement("div",null,O().createElement("div",{className:"computed-url"},"Computed URL:",O().createElement("code",null,s(t))),O().createElement("h4",null,"Server variables"),O().createElement("table",null,O().createElement("tbody",null,o.entrySeq().map((([e,r])=>O().createElement("tr",{key:e},O().createElement("td",null,e),O().createElement("td",null,r.get("enum")?O().createElement("select",{"data-variable":e,onChange:i},r.get("enum").map((r=>O().createElement("option",{selected:r===n(t,e),key:r,value:r},r)))):O().createElement("input",{type:"text",value:n(t,e)||"",onChange:i,"data-variable":e})))))))))};class ServersContainer extends O().Component{render(){const{specSelectors:e,oas3Selectors:t,oas3Actions:r,getComponent:a}=this.props,n=e.servers(),s=a("Servers");return n&&n.size?O().createElement("div",null,O().createElement("span",{className:"servers-title"},"Servers"),O().createElement(s,{servers:n,currentServer:t.selectedServer(),setSelectedServer:r.setSelectedServer,setServerVariableValue:r.setServerVariableValue,getServerVariable:t.serverVariableValue,getEffectiveServerValue:t.serverEffectiveValue})):null}}const jn=Function.prototype;class RequestBodyEditor extends x.PureComponent{static defaultProps={onChange:jn,userHasEditedBody:!1};constructor(e,t){super(e,t),this.state={value:stringify(e.value)||e.defaultValue},e.onChange(e.value)}applyDefaultValue=e=>{const{onChange:t,defaultValue:r}=e||this.props;return this.setState({value:r}),t(r)};onChange=e=>{this.props.onChange(stringify(e))};onDomChange=e=>{const t=e.target.value;this.setState({value:t},(()=>this.onChange(t)))};UNSAFE_componentWillReceiveProps(e){this.props.value!==e.value&&e.value!==this.state.value&&this.setState({value:stringify(e.value)}),!e.value&&e.defaultValue&&this.state.value&&this.applyDefaultValue(e)}render(){let{getComponent:e,errors:t}=this.props,{value:r}=this.state,a=t.size>0;const n=e("TextArea");return O().createElement("div",{className:"body-param"},O().createElement(n,{className:pt()("body-param__text",{invalid:a}),title:t.size?t.join(", "):"",value:r,onChange:this.onDomChange}))}}class HttpAuth extends O().Component{constructor(e,t){super(e,t);let{name:r,schema:a}=this.props,n=this.getValue();this.state={name:r,schema:a,value:n}}getValue(){let{name:e,authorized:t}=this.props;return t&&t.getIn([e,"value"])}onChange=e=>{let{onChange:t}=this.props,{value:r,name:a}=e.target,n=Object.assign({},this.state.value);a?n[a]=r:n=r,this.setState({value:n},(()=>t(this.state)))};render(){let{schema:e,getComponent:t,errSelectors:r,name:a,authSelectors:n}=this.props;const s=t("Input"),o=t("Row"),l=t("Col"),c=t("authError"),i=t("Markdown",!0),p=t("JumpToPath",!0),m=(e.get("scheme")||"").toLowerCase(),u=n.selectAuthPath(a);let d=this.getValue(),h=r.allErrors().filter((e=>e.get("authId")===a));if("basic"===m){let t=d?d.get("username"):null;return O().createElement("div",null,O().createElement("h4",null,O().createElement("code",null,a),"  (http, Basic)",O().createElement(p,{path:u})),t&&O().createElement("h6",null,"Authorized"),O().createElement(o,null,O().createElement(i,{source:e.get("description")})),O().createElement(o,null,O().createElement("label",{htmlFor:"auth-basic-username"},"Username:"),t?O().createElement("code",null," ",t," "):O().createElement(l,null,O().createElement(s,{id:"auth-basic-username",type:"text",required:"required",name:"username","aria-label":"auth-basic-username",onChange:this.onChange,autoFocus:!0}))),O().createElement(o,null,O().createElement("label",{htmlFor:"auth-basic-password"},"Password:"),t?O().createElement("code",null," ****** "):O().createElement(l,null,O().createElement(s,{id:"auth-basic-password",autoComplete:"new-password",name:"password",type:"password","aria-label":"auth-basic-password",onChange:this.onChange}))),h.valueSeq().map(((e,t)=>O().createElement(c,{error:e,key:t}))))}return"bearer"===m?O().createElement("div",null,O().createElement("h4",null,O().createElement("code",null,a),"  (http, Bearer)",O().createElement(p,{path:u})),d&&O().createElement("h6",null,"Authorized"),O().createElement(o,null,O().createElement(i,{source:e.get("description")})),O().createElement(o,null,O().createElement("label",{htmlFor:"auth-bearer-value"},"Value:"),d?O().createElement("code",null," ****** "):O().createElement(l,null,O().createElement(s,{id:"auth-bearer-value",type:"text","aria-label":"auth-bearer-value",onChange:this.onChange,autoFocus:!0}))),h.valueSeq().map(((e,t)=>O().createElement(c,{error:e,key:t})))):O().createElement("div",null,O().createElement("em",null,O().createElement("b",null,a)," HTTP authentication: unsupported scheme ",`'${m}'`))}}class OperationServers extends O().Component{setSelectedServer=e=>{const{path:t,method:r}=this.props;return this.forceUpdate(),this.props.setSelectedServer(e,`${t}:${r}`)};setServerVariableValue=e=>{const{path:t,method:r}=this.props;return this.forceUpdate(),this.props.setServerVariableValue({...e,namespace:`${t}:${r}`})};getSelectedServer=()=>{const{path:e,method:t}=this.props;return this.props.getSelectedServer(`${e}:${t}`)};getServerVariable=(e,t)=>{const{path:r,method:a}=this.props;return this.props.getServerVariable({namespace:`${r}:${a}`,server:e},t)};getEffectiveServerValue=e=>{const{path:t,method:r}=this.props;return this.props.getEffectiveServerValue({server:e,namespace:`${t}:${r}`})};render(){const{operationServers:e,pathServers:t,getComponent:r}=this.props;if(!e&&!t)return null;const a=r("Servers"),n=e||t,s=e?"operation":"path";return O().createElement("div",{className:"opblock-section operation-servers"},O().createElement("div",{className:"opblock-section-header"},O().createElement("div",{className:"tab-header"},O().createElement("h4",{className:"opblock-title"},"Servers"))),O().createElement("div",{className:"opblock-description-wrapper"},O().createElement("h4",{className:"message"},"These ",s,"-level options override the global server options."),O().createElement(a,{servers:n,currentServer:this.getSelectedServer(),setSelectedServer:this.setSelectedServer,setServerVariableValue:this.setServerVariableValue,getServerVariable:this.getServerVariable,getEffectiveServerValue:this.getEffectiveServerValue})))}}const qn={Callbacks:callbacks,HttpAuth,RequestBody:request_body,Servers:components_servers,ServersContainer,RequestBodyEditor,OperationServers,operationLink:In},Pn=new pn.Remarkable("commonmark");Pn.block.ruler.enable(["table"]),Pn.set({linkTarget:"_blank"});const Mn=OAS3ComponentWrapFactory((({source:e,className:t="",getConfigs:r=()=>({useUnsafeMarkdown:!1})})=>{if("string"!=typeof e)return null;if(e){const{useUnsafeMarkdown:a}=r(),n=sanitizer(Pn.render(e),{useUnsafeMarkdown:a});let s;return"string"==typeof n&&(s=n.trim()),O().createElement("div",{dangerouslySetInnerHTML:{__html:s},className:pt()(t,"renderedMarkdown")})}return null})),Tn=OAS3ComponentWrapFactory((({Ori:e,...t})=>{const{schema:r,getComponent:a,errSelectors:n,authorized:s,onAuthChange:o,name:l,authSelectors:c}=t,i=a("HttpAuth");return"http"===r.get("type")?O().createElement(i,{key:l,schema:r,name:l,errSelectors:n,authorized:s,getComponent:a,onChange:o,authSelectors:c}):O().createElement(e,t)})),Rn=OAS3ComponentWrapFactory(OnlineValidatorBadge);class ModelComponent extends x.Component{render(){let{getConfigs:e,schema:t,Ori:r}=this.props,a=["model-box"],n=null;return!0===t.get("deprecated")&&(a.push("deprecated"),n=O().createElement("span",{className:"model-deprecated-warning"},"Deprecated:")),O().createElement("div",{className:a.join(" ")},n,O().createElement(r,Qe()({},this.props,{getConfigs:e,depth:1,expandDepth:this.props.expandDepth||0})))}}const Jn=OAS3ComponentWrapFactory(ModelComponent),$n=OAS3ComponentWrapFactory((({Ori:e,...t})=>{const{schema:r,getComponent:a,errors:n,onChange:s,fn:o}=t,l=o.isFileUploadIntended(r),c=a("Input");return l?O().createElement(c,{type:"file",className:n.length?"invalid":"",title:n.length?n:"",onChange:e=>{s(e.target.files[0])},disabled:e.isDisabled}):O().createElement(e,t)})),Vn={Markdown:Mn,AuthItem:Tn,OpenAPIVersion:function OAS30ComponentWrapFactory(e){return(t,r)=>a=>"function"==typeof r.specSelectors?.isOAS30?r.specSelectors.isOAS30()?O().createElement(e,Qe()({},a,r,{Ori:t})):O().createElement(t,a):(console.warn("OAS30 wrapper: couldn't get spec"),null)}((e=>{const{Ori:t}=e;return O().createElement(t,{oasVersion:"3.0"})})),JsonSchema_string:$n,model:Jn,onlineValidatorBadge:Rn},Ln="oas3_set_servers",Dn="oas3_set_request_body_value",Un="oas3_set_request_body_retain_flag",Kn="oas3_set_request_body_inclusion",zn="oas3_set_active_examples_member",Bn="oas3_set_request_content_type",Fn="oas3_set_response_content_type",Wn="oas3_set_server_variable_value",Hn="oas3_set_request_body_validate_error",Xn="oas3_clear_request_body_validate_error",Gn="oas3_clear_request_body_value";function setSelectedServer(e,t){return{type:Ln,payload:{selectedServerUrl:e,namespace:t}}}function setRequestBodyValue({value:e,pathMethod:t}){return{type:Dn,payload:{value:e,pathMethod:t}}}const setRetainRequestBodyValueFlag=({value:e,pathMethod:t})=>({type:Un,payload:{value:e,pathMethod:t}});function setRequestBodyInclusion({value:e,pathMethod:t,name:r}){return{type:Kn,payload:{value:e,pathMethod:t,name:r}}}function setActiveExamplesMember({name:e,pathMethod:t,contextType:r,contextName:a}){return{type:zn,payload:{name:e,pathMethod:t,contextType:r,contextName:a}}}function setRequestContentType({value:e,pathMethod:t}){return{type:Bn,payload:{value:e,pathMethod:t}}}function setResponseContentType({value:e,path:t,method:r}){return{type:Fn,payload:{value:e,path:t,method:r}}}function setServerVariableValue({server:e,namespace:t,key:r,val:a}){return{type:Wn,payload:{server:e,namespace:t,key:r,val:a}}}const setRequestBodyValidateError=({path:e,method:t,validationErrors:r})=>({type:Hn,payload:{path:e,method:t,validationErrors:r}}),clearRequestBodyValidateError=({path:e,method:t})=>({type:Xn,payload:{path:e,method:t}}),initRequestBodyValidateError=({pathMethod:e})=>({type:Xn,payload:{path:e[0],method:e[1]}}),clearRequestBodyValue=({pathMethod:e})=>({type:Gn,payload:{pathMethod:e}}),Yn=require("lodash/escapeRegExp");var Qn=__webpack_require__.n(Yn);const oas3_selectors_onlyOAS3=e=>(t,...r)=>a=>{if(a.getSystem().specSelectors.isOAS3()){const n=e(t,...r);return"function"==typeof n?n(a):n}return null};const Zn=oas3_selectors_onlyOAS3(((e,t)=>{const r=t?[t,"selectedServer"]:["selectedServer"];return e.getIn(r)||""})),es=oas3_selectors_onlyOAS3(((e,t,r)=>e.getIn(["requestData",t,r,"bodyValue"])||null)),ts=oas3_selectors_onlyOAS3(((e,t,r)=>e.getIn(["requestData",t,r,"retainBodyValue"])||!1)),selectDefaultRequestBodyValue=(e,t,r)=>e=>{const{oas3Selectors:a,specSelectors:n,fn:s}=e.getSystem();if(n.isOAS3()){const e=a.requestContentType(t,r);if(e)return getDefaultRequestBodyValue(n.specResolvedSubtree(["paths",t,r,"requestBody"]),e,a.activeExamplesMember(t,r,"requestBody","requestBody"),s)}return null},rs=oas3_selectors_onlyOAS3(((e,t,r)=>e=>{const{oas3Selectors:a,specSelectors:n,fn:s}=e;let o=!1;const l=a.requestContentType(t,r);let c=a.requestBodyValue(t,r);const i=n.specResolvedSubtree(["paths",t,r,"requestBody"]);if(!i)return!1;if(k.Map.isMap(c)&&(c=stringify(c.mapEntries((e=>k.Map.isMap(e[1])?[e[0],e[1].get("value")]:e)).toJS())),k.List.isList(c)&&(c=stringify(c)),l){const e=getDefaultRequestBodyValue(i,l,a.activeExamplesMember(t,r,"requestBody","requestBody"),s);o=!!c&&c!==e}return o})),as=oas3_selectors_onlyOAS3(((e,t,r)=>e.getIn(["requestData",t,r,"bodyInclusion"])||(0,k.Map)())),ns=oas3_selectors_onlyOAS3(((e,t,r)=>e.getIn(["requestData",t,r,"errors"])||null)),ss=oas3_selectors_onlyOAS3(((e,t,r,a,n)=>e.getIn(["examples",t,r,a,n,"activeExample"])||null)),os=oas3_selectors_onlyOAS3(((e,t,r)=>e.getIn(["requestData",t,r,"requestContentType"])||null)),ls=oas3_selectors_onlyOAS3(((e,t,r)=>e.getIn(["requestData",t,r,"responseContentType"])||null)),cs=oas3_selectors_onlyOAS3(((e,t,r)=>{let a;if("string"!=typeof t){const{server:e,namespace:n}=t;a=n?[n,"serverVariableValues",e,r]:["serverVariableValues",e,r]}else{a=["serverVariableValues",t,r]}return e.getIn(a)||null})),is=oas3_selectors_onlyOAS3(((e,t)=>{let r;if("string"!=typeof t){const{server:e,namespace:a}=t;r=a?[a,"serverVariableValues",e]:["serverVariableValues",e]}else{r=["serverVariableValues",t]}return e.getIn(r)||(0,k.OrderedMap)()})),ps=oas3_selectors_onlyOAS3(((e,t)=>{var r,a;if("string"!=typeof t){const{server:n,namespace:s}=t;a=n,r=s?e.getIn([s,"serverVariableValues",a]):e.getIn(["serverVariableValues",a])}else a=t,r=e.getIn(["serverVariableValues",a]);r=r||(0,k.OrderedMap)();let n=a;return r.map(((e,t)=>{n=n.replace(new RegExp(`{${Qn()(t)}}`,"g"),e)})),n})),ms=function validateRequestBodyIsRequired(e){return(...t)=>r=>{const a=r.getSystem().specSelectors.specJson();let n=[...t][1]||[];return!a.getIn(["paths",...n,"requestBody","required"])||e(...t)}}(((e,t)=>((e,t)=>(t=t||[],!!e.getIn(["requestData",...t,"bodyValue"])))(e,t))),validateShallowRequired=(e,{oas3RequiredRequestBodyContentType:t,oas3RequestContentType:r,oas3RequestBodyValue:a})=>{let n=[];if(!k.Map.isMap(a))return n;let s=[];return Object.keys(t.requestContentType).forEach((e=>{if(e===r){t.requestContentType[e].forEach((e=>{s.indexOf(e)<0&&s.push(e)}))}})),s.forEach((e=>{a.getIn([e,"value"])||n.push(e)})),n},us=Vt()(["get","put","post","delete","options","head","patch","trace"]),ds={[Ln]:(e,{payload:{selectedServerUrl:t,namespace:r}})=>{const a=r?[r,"selectedServer"]:["selectedServer"];return e.setIn(a,t)},[Dn]:(e,{payload:{value:t,pathMethod:r}})=>{let[a,n]=r;if(!k.Map.isMap(t))return e.setIn(["requestData",a,n,"bodyValue"],t);let s=e.getIn(["requestData",a,n,"bodyValue"])||(0,k.Map)();k.Map.isMap(s)||(s=(0,k.Map)());let o=s;const[...l]=t.keys();return l.forEach((e=>{let r=t.getIn([e]);o.has(e)&&k.Map.isMap(r)||(o=o.setIn([e,"value"],r))})),e.setIn(["requestData",a,n,"bodyValue"],o)},[Un]:(e,{payload:{value:t,pathMethod:r}})=>{let[a,n]=r;return e.setIn(["requestData",a,n,"retainBodyValue"],t)},[Kn]:(e,{payload:{value:t,pathMethod:r,name:a}})=>{let[n,s]=r;return e.setIn(["requestData",n,s,"bodyInclusion",a],t)},[zn]:(e,{payload:{name:t,pathMethod:r,contextType:a,contextName:n}})=>{let[s,o]=r;return e.setIn(["examples",s,o,a,n,"activeExample"],t)},[Bn]:(e,{payload:{value:t,pathMethod:r}})=>{let[a,n]=r;return e.setIn(["requestData",a,n,"requestContentType"],t)},[Fn]:(e,{payload:{value:t,path:r,method:a}})=>e.setIn(["requestData",r,a,"responseContentType"],t),[Wn]:(e,{payload:{server:t,namespace:r,key:a,val:n}})=>{const s=r?[r,"serverVariableValues",t,a]:["serverVariableValues",t,a];return e.setIn(s,n)},[Hn]:(e,{payload:{path:t,method:r,validationErrors:a}})=>{let n=[];if(n.push("Required field is not provided"),a.missingBodyValue)return e.setIn(["requestData",t,r,"errors"],(0,k.fromJS)(n));if(a.missingRequiredKeys&&a.missingRequiredKeys.length>0){const{missingRequiredKeys:s}=a;return e.updateIn(["requestData",t,r,"bodyValue"],(0,k.fromJS)({}),(e=>s.reduce(((e,t)=>e.setIn([t,"errors"],(0,k.fromJS)(n))),e)))}return console.warn("unexpected result: SET_REQUEST_BODY_VALIDATE_ERROR"),e},[Xn]:(e,{payload:{path:t,method:r}})=>{const a=e.getIn(["requestData",t,r,"bodyValue"]);if(!k.Map.isMap(a))return e.setIn(["requestData",t,r,"errors"],(0,k.fromJS)([]));const[...n]=a.keys();return n?e.updateIn(["requestData",t,r,"bodyValue"],(0,k.fromJS)({}),(e=>n.reduce(((e,t)=>e.setIn([t,"errors"],(0,k.fromJS)([]))),e))):e},[Gn]:(e,{payload:{pathMethod:t}})=>{let[r,a]=t;const n=e.getIn(["requestData",r,a,"bodyValue"]);return n?k.Map.isMap(n)?e.setIn(["requestData",r,a,"bodyValue"],(0,k.Map)()):e.setIn(["requestData",r,a,"bodyValue"],""):e}};function oas3({getSystem:e}){const t=(e=>(t,r=null)=>{const{getConfigs:a,fn:n}=e(),{fileUploadMediaTypes:s}=a();if("string"==typeof r&&s.some((e=>r.startsWith(e))))return!0;const o=k.Map.isMap(t);if(!o&&!wt()(t))return!1;const l=o?t.get("format"):t.format;return n.hasSchemaType(t,"string")&&["binary","byte"].includes(l)})(e);return{components:qn,wrapComponents:Vn,statePlugins:{spec:{wrapSelectors:_,selectors:w},auth:{wrapSelectors:v},oas3:{actions:{...b},reducers:ds,selectors:{...C}}},fn:{isFileUploadIntended:t,isFileUploadIntendedOAS30:t}}}const webhooks=({specSelectors:e,getComponent:t})=>{const r=e.selectWebhooksOperations(),a=Object.keys(r),n=t("OperationContainer",!0);return 0===a.length?null:O().createElement("div",{className:"webhooks"},O().createElement("h2",null,"Webhooks"),a.map((e=>O().createElement("div",{key:`${e}-webhook`},r[e].map((t=>O().createElement(n,{key:`${e}-${t.method}-webhook`,op:t.operation,tag:"webhooks",method:t.method,path:e,specPath:(0,k.List)(t.specPath),allowTryItOut:!1})))))))},components_license=({getComponent:e,specSelectors:t})=>{const r=t.selectLicenseNameField(),a=t.selectLicenseUrl(),n=e("Link");return O().createElement("div",{className:"info__license"},a?O().createElement("div",{className:"info__license__url"},O().createElement(n,{target:"_blank",href:sanitizeUrl(a)},r)):O().createElement("span",null,r))},components_contact=({getComponent:e,specSelectors:t})=>{const r=t.selectContactNameField(),a=t.selectContactUrl(),n=t.selectContactEmailField(),s=e("Link");return O().createElement("div",{className:"info__contact"},a&&O().createElement("div",null,O().createElement(s,{href:sanitizeUrl(a),target:"_blank"},r," - Website")),n&&O().createElement(s,{href:sanitizeUrl(`mailto:${n}`)},a?`Send email to ${r}`:`Contact ${r}`))},oas31_components_info=({getComponent:e,specSelectors:t})=>{const r=t.version(),a=t.url(),n=t.basePath(),s=t.host(),o=t.selectInfoSummaryField(),l=t.selectInfoDescriptionField(),c=t.selectInfoTitleField(),i=t.selectInfoTermsOfServiceUrl(),p=t.selectExternalDocsUrl(),m=t.selectExternalDocsDescriptionField(),u=t.contact(),d=t.license(),h=e("Markdown",!0),g=e("Link"),y=e("VersionStamp"),f=e("OpenAPIVersion"),S=e("InfoUrl"),E=e("InfoBasePath"),_=e("License",!0),v=e("Contact",!0),w=e("JsonSchemaDialect",!0);return O().createElement("div",{className:"info"},O().createElement("hgroup",{className:"main"},O().createElement("h2",{className:"title"},c,O().createElement("span",null,r&&O().createElement(y,{version:r}),O().createElement(f,{oasVersion:"3.1"}))),(s||n)&&O().createElement(E,{host:s,basePath:n}),a&&O().createElement(S,{getComponent:e,url:a})),o&&O().createElement("p",{className:"info__summary"},o),O().createElement("div",{className:"info__description description"},O().createElement(h,{source:l})),i&&O().createElement("div",{className:"info__tos"},O().createElement(g,{target:"_blank",href:sanitizeUrl(i)},"Terms of service")),u.size>0&&O().createElement(v,null),d.size>0&&O().createElement(_,null),p&&O().createElement(g,{className:"info__extdocs",target:"_blank",href:sanitizeUrl(p)},m||p),O().createElement(w,null))},json_schema_dialect=({getComponent:e,specSelectors:t})=>{const r=t.selectJsonSchemaDialectField(),a=t.selectJsonSchemaDialectDefault(),n=e("Link");return O().createElement(O().Fragment,null,r&&r===a&&O().createElement("p",{className:"info__jsonschemadialect"},"JSON Schema dialect:"," ",O().createElement(n,{target:"_blank",href:sanitizeUrl(r)},r)),r&&r!==a&&O().createElement("div",{className:"error-wrapper"},O().createElement("div",{className:"no-margin"},O().createElement("div",{className:"errors"},O().createElement("div",{className:"errors-wrapper"},O().createElement("h4",{className:"center"},"Warning"),O().createElement("p",{className:"message"},O().createElement("strong",null,"OpenAPI.jsonSchemaDialect")," field contains a value different from the default value of"," ",O().createElement(n,{target:"_blank",href:a},a),". Values different from the default one are currently not supported. Please either omit the field or provide it with the default value."))))))},version_pragma_filter=({bypass:e,isSwagger2:t,isOAS3:r,isOAS31:a,alsoShow:n,children:s})=>e?O().createElement("div",null,s):t&&(r||a)?O().createElement("div",{className:"version-pragma"},n,O().createElement("div",{className:"version-pragma__message version-pragma__message--ambiguous"},O().createElement("div",null,O().createElement("h3",null,"Unable to render this definition"),O().createElement("p",null,O().createElement("code",null,"swagger")," and ",O().createElement("code",null,"openapi")," fields cannot be present in the same Swagger or OpenAPI definition. Please remove one of the fields."),O().createElement("p",null,"Supported version fields are ",O().createElement("code",null,'swagger: "2.0"')," and those that match ",O().createElement("code",null,"openapi: 3.x.y")," (for example,"," ",O().createElement("code",null,"openapi: 3.1.0"),").")))):t||r||a?O().createElement("div",null,s):O().createElement("div",{className:"version-pragma"},n,O().createElement("div",{className:"version-pragma__message version-pragma__message--missing"},O().createElement("div",null,O().createElement("h3",null,"Unable to render this definition"),O().createElement("p",null,"The provided definition does not specify a valid version field."),O().createElement("p",null,"Please indicate a valid Swagger or OpenAPI version field. Supported version fields are ",O().createElement("code",null,'swagger: "2.0"')," and those that match ",O().createElement("code",null,"openapi: 3.x.y")," (for example,"," ",O().createElement("code",null,"openapi: 3.1.0"),").")))),getModelName=e=>"string"==typeof e&&e.includes("#/components/schemas/")?(e=>{const t=e.replace(/~1/g,"/").replace(/~0/g,"~");try{return decodeURIComponent(t)}catch{return t}})(e.replace(/^.*#\/components\/schemas\//,"")):null,hs=(0,x.forwardRef)((({schema:e,getComponent:t,onToggle:r=()=>{},specPath:a},n)=>{const s=t("JSONSchema202012"),o=getModelName(e.get("$$ref")),l=(0,x.useCallback)(((e,t)=>{r(o,t)}),[o,r]);return O().createElement(s,{name:o,schema:e.toJS(),ref:n,onExpand:l,identifier:a.toJS().join("_")})})),gs=hs,models=({specActions:e,specSelectors:t,layoutSelectors:r,layoutActions:a,getComponent:n,getConfigs:s,fn:o})=>{const l=t.selectSchemas(),c=Object.keys(l).length>0,i=["components","schemas"],{docExpansion:p,defaultModelsExpandDepth:m}=s(),u=m>0&&"none"!==p,d=r.isShown(i,u),h=n("Collapse"),g=n("JSONSchema202012"),y=n("ArrowUpIcon"),f=n("ArrowDownIcon"),{getTitle:S}=o.jsonSchema202012.useFn();(0,x.useEffect)((()=>{const a=Object.entries(l).some((([e])=>r.isShown([...i,e],!1))),n=d&&(m>1||a),s=null!=t.specResolvedSubtree(i);n&&!s&&e.requestResolvedSubtree(i)}),[d,m]);const E=(0,x.useCallback)((()=>{a.show(i,!d)}),[d]),_=(0,x.useCallback)((e=>{null!==e&&a.readyToScroll(i,e)}),[]),handleJSONSchema202012Ref=e=>t=>{null!==t&&a.readyToScroll([...i,e],t)},handleJSONSchema202012Expand=r=>(n,s)=>{const o=[...i,r];if(s){null!=t.specResolvedSubtree(o)||e.requestResolvedSubtree([...i,r]),a.show(o,!0)}else a.show(o,!1)};return!c||m<0?null:O().createElement("section",{className:pt()("models",{"is-open":d}),ref:_},O().createElement("h4",null,O().createElement("button",{"aria-expanded":d,className:"models-control",onClick:E},O().createElement("span",null,"Schemas"),d?O().createElement(y,null):O().createElement(f,null))),O().createElement(h,{isOpened:d},Object.entries(l).map((([e,t])=>{const r=S(t,{lookup:"basic"})||e;return O().createElement(g,{key:e,ref:handleJSONSchema202012Ref(e),schema:t,name:r,onExpand:handleJSONSchema202012Expand(e)})}))))},mutual_tls_auth=({schema:e,getComponent:t,name:r,authSelectors:a})=>{const n=t("JumpToPath",!0),s=a.selectAuthPath(r);return O().createElement("div",null,O().createElement("h4",null,r," (mutualTLS) ",O().createElement(n,{path:s})),O().createElement("p",null,"Mutual TLS is required by this API/Operation. Certificates are managed via your Operating System and/or your browser."),O().createElement("p",null,e.get("description")))};class auths_Auths extends O().Component{constructor(e,t){super(e,t),this.state={}}onAuthChange=e=>{let{name:t}=e;this.setState({[t]:e})};submitAuth=e=>{e.preventDefault();let{authActions:t}=this.props;t.authorizeWithPersistOption(this.state)};logoutClick=e=>{e.preventDefault();let{authActions:t,definitions:r}=this.props,a=r.map(((e,t)=>t)).toArray();this.setState(a.reduce(((e,t)=>(e[t]="",e)),{})),t.logoutWithPersistOption(a)};close=e=>{e.preventDefault();let{authActions:t}=this.props;t.showDefinitions(!1)};render(){let{definitions:e,getComponent:t,authSelectors:r,errSelectors:a}=this.props;const n=t("AuthItem"),s=t("oauth2",!0),o=t("Button"),l=r.authorized(),c=e.filter(((e,t)=>!!l.get(t))),i=e.filter((e=>"oauth2"!==e.get("type")&&"mutualTLS"!==e.get("type"))),p=e.filter((e=>"oauth2"===e.get("type"))),m=e.filter((e=>"mutualTLS"===e.get("type")));return O().createElement("div",{className:"auth-container"},i.size>0&&O().createElement("form",{onSubmit:this.submitAuth},i.map(((e,s)=>O().createElement(n,{key:s,schema:e,name:s,getComponent:t,onAuthChange:this.onAuthChange,authorized:l,errSelectors:a,authSelectors:r}))).toArray(),O().createElement("div",{className:"auth-btn-wrapper"},i.size===c.size?O().createElement(o,{className:"btn modal-btn auth",onClick:this.logoutClick,"aria-label":"Remove authorization"},"Logout"):O().createElement(o,{type:"submit",className:"btn modal-btn auth authorize","aria-label":"Apply credentials"},"Authorize"),O().createElement(o,{className:"btn modal-btn auth btn-done",onClick:this.close},"Close"))),p.size>0?O().createElement("div",null,O().createElement("div",{className:"scope-def"},O().createElement("p",null,"Scopes are used to grant an application different levels of access to data on behalf of the end user. Each API may declare one or more scopes."),O().createElement("p",null,"API requires the following scopes. Select which ones you want to grant to Swagger UI.")),e.filter((e=>"oauth2"===e.get("type"))).map(((e,t)=>O().createElement("div",{key:t},O().createElement(s,{authorized:l,schema:e,name:t})))).toArray()):null,m.size>0&&O().createElement("div",null,m.map(((e,s)=>O().createElement(n,{key:s,schema:e,name:s,getComponent:t,onAuthChange:this.onAuthChange,authorized:l,errSelectors:a,authSelectors:r}))).toArray()))}}const ys=auths_Auths,isOAS31=e=>{const t=e.get("openapi");return"string"==typeof t&&/^3\.1\.(?:[1-9]\d*|0)$/.test(t)},fn_createOnlyOAS31Selector=e=>(t,...r)=>a=>{if(a.getSystem().specSelectors.isOAS31()){const n=e(t,...r);return"function"==typeof n?n(a):n}return null},createOnlyOAS31SelectorWrapper=e=>(t,r)=>(a,...n)=>{if(r.getSystem().specSelectors.isOAS31()){const s=e(a,...n);return"function"==typeof s?s(t,r):s}return t(...n)},fn_createSystemSelector=e=>(t,...r)=>a=>{const n=e(t,a,...r);return"function"==typeof n?n(a):n},createOnlyOAS31ComponentWrapper=e=>(t,r)=>a=>r.specSelectors.isOAS31()?O().createElement(e,Qe()({},a,{originalComponent:t,getSystem:r.getSystem})):O().createElement(t,a),wrapOAS31Fn=(e,t)=>{const{fn:r,specSelectors:a}=t;return Object.fromEntries(Object.entries(e).map((([e,t])=>{const n=r[e];return[e,(...e)=>a.isOAS31()?t(...e):"function"==typeof n?n(...e):void 0]})))},fs=createOnlyOAS31ComponentWrapper((({getSystem:e})=>{const t=e().getComponent("OAS31License",!0);return O().createElement(t,null)})),Ss=createOnlyOAS31ComponentWrapper((({getSystem:e})=>{const t=e().getComponent("OAS31Contact",!0);return O().createElement(t,null)})),Es=createOnlyOAS31ComponentWrapper((({getSystem:e})=>{const t=e().getComponent("OAS31Info",!0);return O().createElement(t,null)})),getProperties=(e,{includeReadOnly:t,includeWriteOnly:r})=>{if(!e?.properties)return{};const a=Object.entries(e.properties).filter((([,e])=>(!(!0===e?.readOnly)||t)&&(!(!0===e?.writeOnly)||r)));return Object.fromEntries(a)},makeGetSchemaKeywords=e=>{if("function"!=typeof e)return null;const t=e();return()=>[...t,"discriminator","xml","externalDocs","example","$$ref"]},_s=createOnlyOAS31ComponentWrapper((({getSystem:e,...t})=>{const r=e(),{getComponent:a,fn:n,getConfigs:s}=r,o=s(),l=a("OAS31Model"),c=a("withJSONSchema202012SystemContext");return _s.ModelWithJSONSchemaContext??=c(l,{config:{default$schema:"https://spec.openapis.org/oas/3.1/dialect/base",defaultExpandedLevels:o.defaultModelExpandDepth,includeReadOnly:t.includeReadOnly,includeWriteOnly:t.includeWriteOnly},fn:{getProperties:n.jsonSchema202012.getProperties,isExpandable:n.jsonSchema202012.isExpandable,getSchemaKeywords:makeGetSchemaKeywords(n.jsonSchema202012.getSchemaKeywords)}}),O().createElement(_s.ModelWithJSONSchemaContext,t)})),vs=_s,ws=createOnlyOAS31ComponentWrapper((({getSystem:e})=>{const{getComponent:t,fn:r,getConfigs:a}=e(),n=a();if(ws.ModelsWithJSONSchemaContext)return O().createElement(ws.ModelsWithJSONSchemaContext,null);const s=t("OAS31Models",!0),o=t("withJSONSchema202012SystemContext");return ws.ModelsWithJSONSchemaContext??=o(s,{config:{default$schema:"https://spec.openapis.org/oas/3.1/dialect/base",defaultExpandedLevels:n.defaultModelsExpandDepth-1,includeReadOnly:!0,includeWriteOnly:!0},fn:{getProperties:r.jsonSchema202012.getProperties,isExpandable:r.jsonSchema202012.isExpandable,getSchemaKeywords:makeGetSchemaKeywords(r.jsonSchema202012.getSchemaKeywords)}}),O().createElement(ws.ModelsWithJSONSchemaContext,null)}));ws.ModelsWithJSONSchemaContext=null;const bs=ws,wrap_components_version_pragma_filter=(e,t)=>e=>{const r=t.specSelectors.isOAS31(),a=t.getComponent("OAS31VersionPragmaFilter");return O().createElement(a,Qe()({isOAS31:r},e))},Cs=createOnlyOAS31ComponentWrapper((({originalComponent:e,...t})=>{const{getComponent:r,schema:a,name:n}=t,s=r("MutualTLSAuth",!0);return"mutualTLS"===a.get("type")?O().createElement(s,{schema:a,name:n}):O().createElement(e,t)})),xs=Cs,Os=createOnlyOAS31ComponentWrapper((({getSystem:e,...t})=>{const r=e().getComponent("OAS31Auths",!0);return O().createElement(r,t)})),Ns=(0,k.Map)(),ks=(0,_e.createSelector)(((e,t)=>t.specSelectors.specJson()),isOAS31),selectors_webhooks=()=>e=>{const t=e.specSelectors.specJson().get("webhooks");return k.Map.isMap(t)?t:Ns},As=(0,_e.createSelector)([(e,t)=>t.specSelectors.webhooks(),(e,t)=>t.specSelectors.validOperationMethods(),(e,t)=>t.specSelectors.specResolvedSubtree(["webhooks"])],((e,t)=>e.reduce(((e,r,a)=>{if(!k.Map.isMap(r))return e;const n=r.entrySeq().filter((([e])=>t.includes(e))).map((([e,t])=>({operation:(0,k.Map)({operation:t}),method:e,path:a,specPath:["webhooks",a,e]})));return e.concat(n)}),(0,k.List)()).groupBy((e=>e.path)).map((e=>e.toArray())).toObject())),selectors_license=()=>e=>{const t=e.specSelectors.info().get("license");return k.Map.isMap(t)?t:Ns},selectLicenseNameField=()=>e=>e.specSelectors.license().get("name","License"),selectLicenseUrlField=()=>e=>e.specSelectors.license().get("url"),Is=(0,_e.createSelector)([(e,t)=>t.specSelectors.url(),(e,t)=>t.oas3Selectors.selectedServer(),(e,t)=>t.specSelectors.selectLicenseUrlField()],((e,t,r)=>{if(r)return safeBuildUrl(r,e,{selectedServer:t})})),selectLicenseIdentifierField=()=>e=>e.specSelectors.license().get("identifier"),selectors_contact=()=>e=>{const t=e.specSelectors.info().get("contact");return k.Map.isMap(t)?t:Ns},selectContactNameField=()=>e=>e.specSelectors.contact().get("name","the developer"),selectContactEmailField=()=>e=>e.specSelectors.contact().get("email"),selectContactUrlField=()=>e=>e.specSelectors.contact().get("url"),js=(0,_e.createSelector)([(e,t)=>t.specSelectors.url(),(e,t)=>t.oas3Selectors.selectedServer(),(e,t)=>t.specSelectors.selectContactUrlField()],((e,t,r)=>{if(r)return safeBuildUrl(r,e,{selectedServer:t})})),selectInfoTitleField=()=>e=>e.specSelectors.info().get("title"),selectInfoSummaryField=()=>e=>e.specSelectors.info().get("summary"),selectInfoDescriptionField=()=>e=>e.specSelectors.info().get("description"),selectInfoTermsOfServiceField=()=>e=>e.specSelectors.info().get("termsOfService"),qs=(0,_e.createSelector)([(e,t)=>t.specSelectors.url(),(e,t)=>t.oas3Selectors.selectedServer(),(e,t)=>t.specSelectors.selectInfoTermsOfServiceField()],((e,t,r)=>{if(r)return safeBuildUrl(r,e,{selectedServer:t})})),selectExternalDocsDescriptionField=()=>e=>e.specSelectors.externalDocs().get("description"),selectExternalDocsUrlField=()=>e=>e.specSelectors.externalDocs().get("url"),Ps=(0,_e.createSelector)([(e,t)=>t.specSelectors.url(),(e,t)=>t.oas3Selectors.selectedServer(),(e,t)=>t.specSelectors.selectExternalDocsUrlField()],((e,t,r)=>{if(r)return safeBuildUrl(r,e,{selectedServer:t})})),selectJsonSchemaDialectField=()=>e=>e.specSelectors.specJson().get("jsonSchemaDialect"),selectJsonSchemaDialectDefault=()=>"https://spec.openapis.org/oas/3.1/dialect/base",Ms=(0,_e.createSelector)(((e,t)=>t.specSelectors.definitions()),((e,t)=>t.specSelectors.specResolvedSubtree(["components","schemas"])),((e,t)=>k.Map.isMap(e)?k.Map.isMap(t)?Object.entries(e.toJS()).reduce(((e,[r,a])=>{const n=t.get(r);return e[r]=n?.toJS()||a,e}),{}):e.toJS():{})),wrap_selectors_isOAS3=(e,t)=>(r,...a)=>t.specSelectors.isOAS31()||e(...a),Ts=createOnlyOAS31SelectorWrapper((()=>(e,t)=>t.oas31Selectors.selectLicenseUrl())),Rs=createOnlyOAS31SelectorWrapper((()=>(e,t)=>{const r=t.specSelectors.securityDefinitions();let a=e();return r?(r.entrySeq().forEach((([e,t])=>{const r=t?.get("type");"mutualTLS"===r&&(a=a.push(new k.Map({[e]:t})))})),a):a})),Js=(0,_e.createSelector)([(e,t)=>t.specSelectors.url(),(e,t)=>t.oas3Selectors.selectedServer(),(e,t)=>t.specSelectors.selectLicenseUrlField(),(e,t)=>t.specSelectors.selectLicenseIdentifierField()],((e,t,r,a)=>r?safeBuildUrl(r,e,{selectedServer:t}):a?`https://spdx.org/licenses/${a}.html`:void 0)),keywords_Example=({schema:e,getSystem:t})=>{const{fn:r,getComponent:a}=t(),{hasKeyword:n}=r.jsonSchema202012.useFn(),s=a("JSONSchema202012JSONViewer");return n(e,"example")?O().createElement(s,{name:"Example",value:e.example,className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--example"}):null},keywords_Xml=({schema:e,getSystem:t})=>{const r=e?.xml||{},{fn:a,getComponent:n,getConfigs:s}=t(),{showExtensions:o}=s(),{useComponent:l,useIsExpanded:c,usePath:i,useLevel:p}=a.jsonSchema202012,{path:m}=i("xml"),{isExpanded:u,setExpanded:d,setCollapsed:h}=c("xml"),[g,y]=p(),f=o?getExtensions(r):[],S=!!(r.name||r.namespace||r.prefix||f.length>0),E=l("Accordion"),_=l("ExpandDeepButton"),v=n("OpenAPI31Extensions"),w=n("JSONSchema202012PathContext")(),b=n("JSONSchema202012LevelContext")(),C=(0,x.useCallback)((()=>{u?h():d()}),[u,d,h]),N=(0,x.useCallback)(((e,t)=>{t?d({deep:!0}):h({deep:!0})}),[d,h]);return 0===Object.keys(r).length?null:O().createElement(w.Provider,{value:m},O().createElement(b.Provider,{value:y},O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--xml","data-json-schema-level":g},S?O().createElement(O().Fragment,null,O().createElement(E,{expanded:u,onChange:C},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"XML")),O().createElement(_,{expanded:u,onClick:N})):O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"XML"),!0===r.attribute&&O().createElement("span",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--muted"},"attribute"),!0===r.wrapped&&O().createElement("span",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--muted"},"wrapped"),O().createElement("strong",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--primary"},"object"),O().createElement("ul",{className:pt()("json-schema-2020-12-keyword__children",{"json-schema-2020-12-keyword__children--collapsed":!u})},u&&O().createElement(O().Fragment,null,r.name&&O().createElement("li",{className:"json-schema-2020-12-property"},O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword"},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"name"),O().createElement("span",{className:"json-schema-2020-12-keyword__value json-schema-2020-12-keyword__value--secondary"},r.name))),r.namespace&&O().createElement("li",{className:"json-schema-2020-12-property"},O().createElement("div",{className:"json-schema-2020-12-keyword"},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"namespace"),O().createElement("span",{className:"json-schema-2020-12-keyword__value json-schema-2020-12-keyword__value--secondary"},r.namespace))),r.prefix&&O().createElement("li",{className:"json-schema-2020-12-property"},O().createElement("div",{className:"json-schema-2020-12-keyword"},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"prefix"),O().createElement("span",{className:"json-schema-2020-12-keyword__value json-schema-2020-12-keyword__value--secondary"},r.prefix)))),f.length>0&&O().createElement(v,{openAPISpecObj:r,openAPIExtensions:f,getSystem:t})))))},Discriminator_DiscriminatorMapping=({discriminator:e})=>{const t=e?.mapping||{};return 0===Object.keys(t).length?null:Object.entries(t).map((([e,t])=>O().createElement("div",{key:`${e}-${t}`,className:"json-schema-2020-12-keyword"},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},e),O().createElement("span",{className:"json-schema-2020-12-keyword__value json-schema-2020-12-keyword__value--secondary"},t))))},Discriminator_Discriminator=({schema:e,getSystem:t})=>{const r=e?.discriminator||{},{fn:a,getComponent:n,getConfigs:s}=t(),{showExtensions:o}=s(),{useComponent:l,useIsExpanded:c,usePath:i,useLevel:p}=a.jsonSchema202012,m="discriminator",{path:u}=i(m),{isExpanded:d,setExpanded:h,setCollapsed:g}=c(m),[y,f]=p(),S=o?getExtensions(r):[],E=!!(r.mapping||S.length>0),_=l("Accordion"),v=l("ExpandDeepButton"),w=n("OpenAPI31Extensions"),b=n("JSONSchema202012PathContext")(),C=n("JSONSchema202012LevelContext")(),N=(0,x.useCallback)((()=>{d?g():h()}),[d,h,g]),k=(0,x.useCallback)(((e,t)=>{t?h({deep:!0}):g({deep:!0})}),[h,g]);return 0===Object.keys(r).length?null:O().createElement(b.Provider,{value:u},O().createElement(C.Provider,{value:f},O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--discriminator","data-json-schema-level":y},E?O().createElement(O().Fragment,null,O().createElement(_,{expanded:d,onChange:N},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"Discriminator")),O().createElement(v,{expanded:d,onClick:k})):O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"Discriminator"),r.propertyName&&O().createElement("span",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--muted"},r.propertyName),O().createElement("strong",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--primary"},"object"),O().createElement("ul",{className:pt()("json-schema-2020-12-keyword__children",{"json-schema-2020-12-keyword__children--collapsed":!d})},d&&O().createElement("li",{className:"json-schema-2020-12-property"},O().createElement(Discriminator_DiscriminatorMapping,{discriminator:r})),S.length>0&&O().createElement(w,{openAPISpecObj:r,openAPIExtensions:S,getSystem:t})))))},keywords_OpenAPIExtensions=({openAPISpecObj:e,getSystem:t,openAPIExtensions:r})=>{const{fn:a}=t(),{useComponent:n}=a.jsonSchema202012,s=n("JSONViewer");return r.map((t=>O().createElement(s,{key:t,name:t,value:e[t],className:"json-schema-2020-12-json-viewer-extension-keyword"})))},keywords_ExternalDocs=({schema:e,getSystem:t})=>{const r=e?.externalDocs||{},{fn:a,getComponent:n,getConfigs:s}=t(),{showExtensions:o}=s(),{useComponent:l,useIsExpanded:c,usePath:i,useLevel:p}=a.jsonSchema202012,m="externalDocs",{path:u}=i(m),{isExpanded:d,setExpanded:h,setCollapsed:g}=c(m),[y,f]=p(),S=o?getExtensions(r):[],E=!!(r.description||r.url||S.length>0),_=l("Accordion"),v=l("ExpandDeepButton"),w=n("JSONSchema202012KeywordDescription"),b=n("Link"),C=n("OpenAPI31Extensions"),N=n("JSONSchema202012PathContext")(),k=n("JSONSchema202012LevelContext")(),A=(0,x.useCallback)((()=>{d?g():h()}),[d,h,g]),I=(0,x.useCallback)(((e,t)=>{t?h({deep:!0}):g({deep:!0})}),[h,g]);return 0===Object.keys(r).length?null:O().createElement(N.Provider,{value:u},O().createElement(k.Provider,{value:f},O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--externalDocs","data-json-schema-level":y},E?O().createElement(O().Fragment,null,O().createElement(_,{expanded:d,onChange:A},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"External documentation")),O().createElement(v,{expanded:d,onClick:I})):O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"External documentation"),O().createElement("strong",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--primary"},"object"),O().createElement("ul",{className:pt()("json-schema-2020-12-keyword__children",{"json-schema-2020-12-keyword__children--collapsed":!d})},d&&O().createElement(O().Fragment,null,r.description&&O().createElement("li",{className:"json-schema-2020-12-property"},O().createElement(w,{schema:r,getSystem:t})),r.url&&O().createElement("li",{className:"json-schema-2020-12-property"},O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword"},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"url"),O().createElement("span",{className:"json-schema-2020-12-keyword__value json-schema-2020-12-keyword__value--secondary"},O().createElement(b,{target:"_blank",href:sanitizeUrl(r.url)},r.url))))),S.length>0&&O().createElement(C,{openAPISpecObj:r,openAPIExtensions:S,getSystem:t})))))},keywords_Description=({schema:e,getSystem:t})=>{if(!e?.description)return null;const{getComponent:r}=t(),a=r("Markdown");return O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--description"},O().createElement("div",{className:"json-schema-2020-12-core-keyword__value json-schema-2020-12-core-keyword__value--secondary"},O().createElement(a,{source:e.description})))},$s=createOnlyOAS31ComponentWrapper(keywords_Description),Vs=createOnlyOAS31ComponentWrapper((({schema:e,getSystem:t,originalComponent:r})=>{const{getComponent:a}=t(),n=a("JSONSchema202012KeywordDiscriminator"),s=a("JSONSchema202012KeywordXml"),o=a("JSONSchema202012KeywordExample"),l=a("JSONSchema202012KeywordExternalDocs");return O().createElement(O().Fragment,null,O().createElement(r,{schema:e}),O().createElement(n,{schema:e,getSystem:t}),O().createElement(s,{schema:e,getSystem:t}),O().createElement(l,{schema:e,getSystem:t}),O().createElement(o,{schema:e,getSystem:t}))})),Ls=Vs,keywords_Properties=({schema:e,getSystem:t})=>{const{fn:r,getComponent:a}=t(),{useComponent:n,usePath:s}=r.jsonSchema202012,{getDependentRequired:o,getProperties:l}=r.jsonSchema202012.useFn(),c=r.jsonSchema202012.useConfig(),i=Array.isArray(e?.required)?e.required:[],{path:p}=s("properties"),m=n("JSONSchema"),u=a("JSONSchema202012PathContext")(),d=l(e,c);return 0===Object.keys(d).length?null:O().createElement(u.Provider,{value:p},O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--properties"},O().createElement("ul",null,Object.entries(d).map((([t,r])=>{const a=i.includes(t),n=o(t,e);return O().createElement("li",{key:t,className:pt()("json-schema-2020-12-property",{"json-schema-2020-12-property--required":a})},O().createElement(m,{name:t,schema:r,dependentRequired:n}))})))))},Ds=createOnlyOAS31ComponentWrapper(keywords_Properties);const Us=function oas31_after_load_afterLoad({fn:e,getSystem:t}){if(e.jsonSchema202012){const r=((e,t)=>{const{fn:r}=t();if("function"!=typeof e)return null;const{hasKeyword:a}=r.jsonSchema202012;return t=>e(t)||a(t,"example")||t?.xml||t?.discriminator||t?.externalDocs})(e.jsonSchema202012.isExpandable,t);Object.assign(this.fn.jsonSchema202012,{isExpandable:r,getProperties})}if("function"==typeof e.sampleFromSchema&&e.jsonSchema202012){const r=wrapOAS31Fn({sampleFromSchema:e.jsonSchema202012.sampleFromSchema,sampleFromSchemaGeneric:e.jsonSchema202012.sampleFromSchemaGeneric,createXMLExample:e.jsonSchema202012.createXMLExample,memoizedSampleFromSchema:e.jsonSchema202012.memoizedSampleFromSchema,memoizedCreateXMLExample:e.jsonSchema202012.memoizedCreateXMLExample,getJsonSampleSchema:e.jsonSchema202012.getJsonSampleSchema,getYamlSampleSchema:e.jsonSchema202012.getYamlSampleSchema,getXmlSampleSchema:e.jsonSchema202012.getXmlSampleSchema,getSampleSchema:e.jsonSchema202012.getSampleSchema,mergeJsonSchema:e.jsonSchema202012.mergeJsonSchema,getSchemaObjectTypeLabel:t=>e.jsonSchema202012.getType(immutableToJS(t)),getSchemaObjectType:t=>e.jsonSchema202012.foldType(immutableToJS(t)?.type)},t());Object.assign(this.fn,r)}const r=(e=>(t,r=null)=>{const{fn:a}=e();if(a.isFileUploadIntendedOAS30(t,r))return!0;const n=k.Map.isMap(t);if(!n&&!wt()(t))return!1;const s=n?t.get("contentMediaType"):t.contentMediaType,o=n?t.get("contentEncoding"):t.contentEncoding;return"string"==typeof s&&""!==s||"string"==typeof o&&""!==o})(t),{isFileUploadIntended:a}=wrapOAS31Fn({isFileUploadIntended:r},t());if(this.fn.isFileUploadIntended=a,this.fn.isFileUploadIntendedOAS31=r,e.jsonSchema202012){const{hasSchemaType:r}=wrapOAS31Fn({hasSchemaType:e.jsonSchema202012.hasSchemaType},t());this.fn.hasSchemaType=r}},oas31=({fn:e})=>{const t=e.createSystemSelector||fn_createSystemSelector,r=e.createOnlyOAS31Selector||fn_createOnlyOAS31Selector;return{afterLoad:Us,fn:{isOAS31,createSystemSelector:fn_createSystemSelector,createOnlyOAS31Selector:fn_createOnlyOAS31Selector},components:{Webhooks:webhooks,JsonSchemaDialect:json_schema_dialect,MutualTLSAuth:mutual_tls_auth,OAS31Info:oas31_components_info,OAS31License:components_license,OAS31Contact:components_contact,OAS31VersionPragmaFilter:version_pragma_filter,OAS31Model:gs,OAS31Models:models,OAS31Auths:ys,JSONSchema202012KeywordExample:keywords_Example,JSONSchema202012KeywordXml:keywords_Xml,JSONSchema202012KeywordDiscriminator:Discriminator_Discriminator,JSONSchema202012KeywordExternalDocs:keywords_ExternalDocs,OpenAPI31Extensions:keywords_OpenAPIExtensions},wrapComponents:{InfoContainer:Es,License:fs,Contact:Ss,VersionPragmaFilter:wrap_components_version_pragma_filter,Model:vs,Models:bs,AuthItem:xs,auths:Os,JSONSchema202012KeywordDescription:$s,JSONSchema202012KeywordExamples:Ls,JSONSchema202012KeywordProperties:Ds},statePlugins:{auth:{wrapSelectors:{definitionsToAuthorize:Rs}},spec:{selectors:{isOAS31:t(ks),license:selectors_license,selectLicenseNameField,selectLicenseUrlField,selectLicenseIdentifierField:r(selectLicenseIdentifierField),selectLicenseUrl:t(Is),contact:selectors_contact,selectContactNameField,selectContactEmailField,selectContactUrlField,selectContactUrl:t(js),selectInfoTitleField,selectInfoSummaryField:r(selectInfoSummaryField),selectInfoDescriptionField,selectInfoTermsOfServiceField,selectInfoTermsOfServiceUrl:t(qs),selectExternalDocsDescriptionField,selectExternalDocsUrlField,selectExternalDocsUrl:t(Ps),webhooks:r(selectors_webhooks),selectWebhooksOperations:r(t(As)),selectJsonSchemaDialectField,selectJsonSchemaDialectDefault,selectSchemas:t(Ms)},wrapSelectors:{isOAS3:wrap_selectors_isOAS3,selectLicenseUrl:Ts}},oas31:{selectors:{selectLicenseUrl:r(t(Js))}}}}},Ks=Oe().object,zs=Oe().bool,Bs=(Oe().oneOfType([Ks,zs]),(0,x.createContext)(null));Bs.displayName="JSONSchemaContext";const Fs=(0,x.createContext)(0);Fs.displayName="JSONSchemaLevelContext";const Ws=(0,x.createContext)(new Set),Hs=(0,x.createContext)([]);class JSONSchemaIsExpandedState{static Collapsed="collapsed";static Expanded="expanded";static DeeplyExpanded="deeply-expanded"}const useConfig=()=>{const{config:e}=(0,x.useContext)(Bs);return e},useComponent=e=>{const{components:t}=(0,x.useContext)(Bs);return t[e]||null},useFn=(e=void 0)=>{const{fn:t}=(0,x.useContext)(Bs);return void 0!==e?t[e]:t},useJSONSchemaContextState=()=>{const[,e]=(0,x.useState)(null),{state:t}=(0,x.useContext)(Bs);return{state:t,setState:r=>{r(t),e({})}}},useLevel=()=>{const e=(0,x.useContext)(Fs);return[e,e+1]},usePath=e=>{const t=(0,x.useContext)(Hs),{setState:r}=useJSONSchemaContextState(),a="string"==typeof e?[...t,e]:t;return{path:a,pathMutator:(e,t={deep:!1})=>{const n=a.toString(),updateFn=t=>{t.paths[n]=e,e===JSONSchemaIsExpandedState.Collapsed&&Object.keys(t.paths).forEach((e=>{e.startsWith(n)&&t.paths[e]===JSONSchemaIsExpandedState.DeeplyExpanded&&(t.paths[e]=JSONSchemaIsExpandedState.Expanded)}))},updateDeepFn=t=>{Object.keys(t.paths).forEach((r=>{r.startsWith(n)&&(t.paths[r]=e)}))};t.deep?r(updateDeepFn):r(updateFn)}}},useIsExpanded=e=>{const[t]=useLevel(),{defaultExpandedLevels:r}=useConfig(),{path:a,pathMutator:n}=usePath(e),{path:s}=usePath(),{state:o}=useJSONSchemaContextState(),l=o.paths[a.toString()],c=o.paths[s.toString()]??o.paths[s.slice(0,-1).toString()],i=l??(r-t>0?JSONSchemaIsExpandedState.Expanded:JSONSchemaIsExpandedState.Collapsed),p=i!==JSONSchemaIsExpandedState.Collapsed;(0,x.useEffect)((()=>{n(c===JSONSchemaIsExpandedState.DeeplyExpanded?JSONSchemaIsExpandedState.DeeplyExpanded:i)}),[c]);return{isExpanded:p,setExpanded:(0,x.useCallback)(((e={deep:!1})=>{n(e.deep?JSONSchemaIsExpandedState.DeeplyExpanded:JSONSchemaIsExpandedState.Expanded)}),[]),setCollapsed:(0,x.useCallback)(((e={deep:!1})=>{n(JSONSchemaIsExpandedState.Collapsed,e)}),[])}},useRenderedSchemas=(e=void 0)=>{if(void 0===e)return(0,x.useContext)(Ws);const t=(0,x.useContext)(Ws);return new Set([...t,e])},Xs=(0,x.forwardRef)((({schema:e,name:t="",dependentRequired:r=[],onExpand:a=()=>{},identifier:n=""},s)=>{const o=useFn(),l=n||e?.$id||t,{path:c}=usePath(l),{isExpanded:i,setExpanded:p,setCollapsed:m}=useIsExpanded(l),[u,d]=useLevel(),h=(()=>{const[e]=useLevel();return e>0})(),g=o.isExpandable(e)||r.length>0,y=(e=>useRenderedSchemas().has(e))(e),f=useRenderedSchemas(e),S=o.stringifyConstraints(e),E=useComponent("Accordion"),_=useComponent("Keyword$schema"),v=useComponent("Keyword$vocabulary"),w=useComponent("Keyword$id"),b=useComponent("Keyword$anchor"),C=useComponent("Keyword$dynamicAnchor"),N=useComponent("Keyword$ref"),k=useComponent("Keyword$dynamicRef"),A=useComponent("Keyword$defs"),I=useComponent("Keyword$comment"),j=useComponent("KeywordAllOf"),q=useComponent("KeywordAnyOf"),P=useComponent("KeywordOneOf"),M=useComponent("KeywordNot"),T=useComponent("KeywordIf"),R=useComponent("KeywordThen"),J=useComponent("KeywordElse"),$=useComponent("KeywordDependentSchemas"),V=useComponent("KeywordPrefixItems"),L=useComponent("KeywordItems"),D=useComponent("KeywordContains"),U=useComponent("KeywordProperties"),K=useComponent("KeywordPatternProperties"),z=useComponent("KeywordAdditionalProperties"),B=useComponent("KeywordPropertyNames"),F=useComponent("KeywordUnevaluatedItems"),W=useComponent("KeywordUnevaluatedProperties"),H=useComponent("KeywordType"),X=useComponent("KeywordEnum"),G=useComponent("KeywordConst"),Y=useComponent("KeywordConstraint"),Q=useComponent("KeywordDependentRequired"),Z=useComponent("KeywordContentSchema"),ee=useComponent("KeywordTitle"),te=useComponent("KeywordDescription"),re=useComponent("KeywordDefault"),ae=useComponent("KeywordDeprecated"),ne=useComponent("KeywordReadOnly"),se=useComponent("KeywordWriteOnly"),oe=useComponent("KeywordExamples"),le=useComponent("ExtensionKeywords"),ce=useComponent("ExpandDeepButton"),ie=(0,x.useCallback)(((e,t)=>{t?p():m(),a(e,t,!1)}),[a,p,m]),pe=(0,x.useCallback)(((e,t)=>{t?p({deep:!0}):m({deep:!0}),a(e,t,!0)}),[a,p,m]);return O().createElement(Hs.Provider,{value:c},O().createElement(Fs.Provider,{value:d},O().createElement(Ws.Provider,{value:f},O().createElement("article",{ref:s,"data-json-schema-level":u,className:pt()("json-schema-2020-12",{"json-schema-2020-12--embedded":h,"json-schema-2020-12--circular":y})},O().createElement("div",{className:"json-schema-2020-12-head"},g&&!y?O().createElement(O().Fragment,null,O().createElement(E,{expanded:i,onChange:ie},O().createElement(ee,{title:t,schema:e})),O().createElement(ce,{expanded:i,onClick:pe})):O().createElement(ee,{title:t,schema:e}),O().createElement(ae,{schema:e}),O().createElement(ne,{schema:e}),O().createElement(se,{schema:e}),O().createElement(H,{schema:e,isCircular:y}),S.length>0&&S.map((e=>O().createElement(Y,{key:`${e.scope}-${e.value}`,constraint:e})))),O().createElement("div",{className:pt()("json-schema-2020-12-body",{"json-schema-2020-12-body--collapsed":!i})},i&&O().createElement(O().Fragment,null,O().createElement(te,{schema:e}),!y&&g&&O().createElement(O().Fragment,null,O().createElement(U,{schema:e}),O().createElement(K,{schema:e}),O().createElement(z,{schema:e}),O().createElement(W,{schema:e}),O().createElement(B,{schema:e}),O().createElement(j,{schema:e}),O().createElement(q,{schema:e}),O().createElement(P,{schema:e}),O().createElement(M,{schema:e}),O().createElement(T,{schema:e}),O().createElement(R,{schema:e}),O().createElement(J,{schema:e}),O().createElement($,{schema:e}),O().createElement(V,{schema:e}),O().createElement(L,{schema:e}),O().createElement(F,{schema:e}),O().createElement(D,{schema:e}),O().createElement(Z,{schema:e})),O().createElement(X,{schema:e}),O().createElement(G,{schema:e}),O().createElement(Q,{schema:e,dependentRequired:r}),O().createElement(re,{schema:e}),O().createElement(oe,{schema:e}),O().createElement(_,{schema:e}),O().createElement(v,{schema:e}),O().createElement(w,{schema:e}),O().createElement(b,{schema:e}),O().createElement(C,{schema:e}),O().createElement(N,{schema:e}),!y&&g&&O().createElement(A,{schema:e}),O().createElement(k,{schema:e}),O().createElement(I,{schema:e}),O().createElement(le,{schema:e})))))))})),Gs=Xs,keywords_$schema=({schema:e})=>e?.$schema?O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--$schema"},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"$schema"),O().createElement("span",{className:"json-schema-2020-12-keyword__value json-schema-2020-12-keyword__value--secondary"},e.$schema)):null,$vocabulary_$vocabulary=({schema:e})=>{const t="$vocabulary",{path:r}=usePath(t),{isExpanded:a,setExpanded:n,setCollapsed:s}=useIsExpanded(t),o=useComponent("Accordion"),l=(0,x.useCallback)((()=>{a?s():n()}),[a,n,s]);return e?.$vocabulary?"object"!=typeof e.$vocabulary?null:O().createElement(Hs.Provider,{value:r},O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--$vocabulary"},O().createElement(o,{expanded:a,onChange:l},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"$vocabulary")),O().createElement("strong",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--primary"},"object"),O().createElement("ul",null,a&&Object.entries(e.$vocabulary).map((([e,t])=>O().createElement("li",{key:e,className:pt()("json-schema-2020-12-$vocabulary-uri",{"json-schema-2020-12-$vocabulary-uri--disabled":!t})},O().createElement("span",{className:"json-schema-2020-12-keyword__value json-schema-2020-12-keyword__value--secondary"},e))))))):null},keywords_$id=({schema:e})=>e?.$id?O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--$id"},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"$id"),O().createElement("span",{className:"json-schema-2020-12-keyword__value json-schema-2020-12-keyword__value--secondary"},e.$id)):null,keywords_$anchor=({schema:e})=>e?.$anchor?O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--$anchor"},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"$anchor"),O().createElement("span",{className:"json-schema-2020-12-keyword__value json-schema-2020-12-keyword__value--secondary"},e.$anchor)):null,keywords_$dynamicAnchor=({schema:e})=>e?.$dynamicAnchor?O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--$dynamicAnchor"},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"$dynamicAnchor"),O().createElement("span",{className:"json-schema-2020-12-keyword__value json-schema-2020-12-keyword__value--secondary"},e.$dynamicAnchor)):null,keywords_$ref=({schema:e})=>e?.$ref?O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--$ref"},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"$ref"),O().createElement("span",{className:"json-schema-2020-12-keyword__value json-schema-2020-12-keyword__value--secondary"},e.$ref)):null,keywords_$dynamicRef=({schema:e})=>e?.$dynamicRef?O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--$dynamicRef"},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"$dynamicRef"),O().createElement("span",{className:"json-schema-2020-12-keyword__value json-schema-2020-12-keyword__value--secondary"},e.$dynamicRef)):null,keywords_$defs=({schema:e})=>{const t=e?.$defs||{},r="$defs",{path:a}=usePath(r),{isExpanded:n,setExpanded:s,setCollapsed:o}=useIsExpanded(r),[l,c]=useLevel(),i=useComponent("Accordion"),p=useComponent("ExpandDeepButton"),m=useComponent("JSONSchema"),u=(0,x.useCallback)((()=>{n?o():s()}),[n,s,o]),d=(0,x.useCallback)(((e,t)=>{t?s({deep:!0}):o({deep:!0})}),[s,o]);return 0===Object.keys(t).length?null:O().createElement(Hs.Provider,{value:a},O().createElement(Fs.Provider,{value:c},O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--$defs","data-json-schema-level":l},O().createElement(i,{expanded:n,onChange:u},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"$defs")),O().createElement(p,{expanded:n,onClick:d}),O().createElement("strong",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--primary"},"object"),O().createElement("ul",{className:pt()("json-schema-2020-12-keyword__children",{"json-schema-2020-12-keyword__children--collapsed":!n})},n&&O().createElement(O().Fragment,null,Object.entries(t).map((([e,t])=>O().createElement("li",{key:e,className:"json-schema-2020-12-property"},O().createElement(m,{name:e,schema:t})))))))))},keywords_$comment=({schema:e})=>e?.$comment?O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--$comment"},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--secondary"},"$comment"),O().createElement("span",{className:"json-schema-2020-12-keyword__value json-schema-2020-12-keyword__value--secondary"},e.$comment)):null,keywords_AllOf=({schema:e})=>{const t=e?.allOf||[],r=useFn(),a="allOf",{path:n}=usePath(a),{isExpanded:s,setExpanded:o,setCollapsed:l}=useIsExpanded(a),[c,i]=useLevel(),p=useComponent("Accordion"),m=useComponent("ExpandDeepButton"),u=useComponent("JSONSchema"),d=useComponent("KeywordType"),h=(0,x.useCallback)((()=>{s?l():o()}),[s,o,l]),g=(0,x.useCallback)(((e,t)=>{t?o({deep:!0}):l({deep:!0})}),[o,l]);return Array.isArray(t)&&0!==t.length?O().createElement(Hs.Provider,{value:n},O().createElement(Fs.Provider,{value:i},O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--allOf","data-json-schema-level":c},O().createElement(p,{expanded:s,onChange:h},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"All of")),O().createElement(m,{expanded:s,onClick:g}),O().createElement(d,{schema:{allOf:t}}),O().createElement("ul",{className:pt()("json-schema-2020-12-keyword__children",{"json-schema-2020-12-keyword__children--collapsed":!s})},s&&O().createElement(O().Fragment,null,t.map(((e,t)=>O().createElement("li",{key:`#${t}`,className:"json-schema-2020-12-property"},O().createElement(u,{name:`#${t} ${r.getTitle(e)}`,schema:e}))))))))):null},keywords_AnyOf=({schema:e})=>{const t=e?.anyOf||[],r=useFn(),a="anyOf",{path:n}=usePath(a),{isExpanded:s,setExpanded:o,setCollapsed:l}=useIsExpanded(a),[c,i]=useLevel(),p=useComponent("Accordion"),m=useComponent("ExpandDeepButton"),u=useComponent("JSONSchema"),d=useComponent("KeywordType"),h=(0,x.useCallback)((()=>{s?l():o()}),[s,o,l]),g=(0,x.useCallback)(((e,t)=>{t?o({deep:!0}):l({deep:!0})}),[o,l]);return Array.isArray(t)&&0!==t.length?O().createElement(Hs.Provider,{value:n},O().createElement(Fs.Provider,{value:i},O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--anyOf","data-json-schema-level":c},O().createElement(p,{expanded:s,onChange:h},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"Any of")),O().createElement(m,{expanded:s,onClick:g}),O().createElement(d,{schema:{anyOf:t}}),O().createElement("ul",{className:pt()("json-schema-2020-12-keyword__children",{"json-schema-2020-12-keyword__children--collapsed":!s})},s&&O().createElement(O().Fragment,null,t.map(((e,t)=>O().createElement("li",{key:`#${t}`,className:"json-schema-2020-12-property"},O().createElement(u,{name:`#${t} ${r.getTitle(e)}`,schema:e}))))))))):null},keywords_OneOf=({schema:e})=>{const t=e?.oneOf||[],r=useFn(),a="oneOf",{path:n}=usePath(a),{isExpanded:s,setExpanded:o,setCollapsed:l}=useIsExpanded(a),[c,i]=useLevel(),p=useComponent("Accordion"),m=useComponent("ExpandDeepButton"),u=useComponent("JSONSchema"),d=useComponent("KeywordType"),h=(0,x.useCallback)((()=>{s?l():o()}),[s,o,l]),g=(0,x.useCallback)(((e,t)=>{t?o({deep:!0}):l({deep:!0})}),[o,l]);return Array.isArray(t)&&0!==t.length?O().createElement(Hs.Provider,{value:n},O().createElement(Fs.Provider,{value:i},O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--oneOf","data-json-schema-level":c},O().createElement(p,{expanded:s,onChange:h},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"One of")),O().createElement(m,{expanded:s,onClick:g}),O().createElement(d,{schema:{oneOf:t}}),O().createElement("ul",{className:pt()("json-schema-2020-12-keyword__children",{"json-schema-2020-12-keyword__children--collapsed":!s})},s&&O().createElement(O().Fragment,null,t.map(((e,t)=>O().createElement("li",{key:`#${t}`,className:"json-schema-2020-12-property"},O().createElement(u,{name:`#${t} ${r.getTitle(e)}`,schema:e}))))))))):null},keywords_Not=({schema:e})=>{const t=useFn(),r=useComponent("JSONSchema");if(!t.hasKeyword(e,"not"))return null;const a=O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"Not");return O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--not"},O().createElement(r,{name:a,schema:e.not,identifier:"not"}))},keywords_If=({schema:e})=>{const t=useFn(),r=useComponent("JSONSchema");if(!t.hasKeyword(e,"if"))return null;const a=O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"If");return O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--if"},O().createElement(r,{name:a,schema:e.if,identifier:"if"}))},keywords_Then=({schema:e})=>{const t=useFn(),r=useComponent("JSONSchema");if(!t.hasKeyword(e,"then"))return null;const a=O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"Then");return O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--then"},O().createElement(r,{name:a,schema:e.then,identifier:"then"}))},keywords_Else=({schema:e})=>{const t=useFn(),r=useComponent("JSONSchema");if(!t.hasKeyword(e,"else"))return null;const a=O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"Else");return O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--if"},O().createElement(r,{name:a,schema:e.else,identifier:"else"}))},keywords_DependentSchemas=({schema:e})=>{const t=e?.dependentSchemas||[],r="dependentSchemas",{path:a}=usePath(r),{isExpanded:n,setExpanded:s,setCollapsed:o}=useIsExpanded(r),[l,c]=useLevel(),i=useComponent("Accordion"),p=useComponent("ExpandDeepButton"),m=useComponent("JSONSchema"),u=(0,x.useCallback)((()=>{n?o():s()}),[n,s,o]),d=(0,x.useCallback)(((e,t)=>{t?s({deep:!0}):o({deep:!0})}),[s,o]);return"object"!=typeof t||0===Object.keys(t).length?null:O().createElement(Hs.Provider,{value:a},O().createElement(Fs.Provider,{value:c},O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--dependentSchemas","data-json-schema-level":l},O().createElement(i,{expanded:n,onChange:u},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"Dependent schemas")),O().createElement(p,{expanded:n,onClick:d}),O().createElement("strong",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--primary"},"object"),O().createElement("ul",{className:pt()("json-schema-2020-12-keyword__children",{"json-schema-2020-12-keyword__children--collapsed":!n})},n&&O().createElement(O().Fragment,null,Object.entries(t).map((([e,t])=>O().createElement("li",{key:e,className:"json-schema-2020-12-property"},O().createElement(m,{name:e,schema:t})))))))))},keywords_PrefixItems=({schema:e})=>{const t=e?.prefixItems||[],r=useFn(),a="prefixItems",{path:n}=usePath(a),{isExpanded:s,setExpanded:o,setCollapsed:l}=useIsExpanded(a),[c,i]=useLevel(),p=useComponent("Accordion"),m=useComponent("ExpandDeepButton"),u=useComponent("JSONSchema"),d=useComponent("KeywordType"),h=(0,x.useCallback)((()=>{s?l():o()}),[s,o,l]),g=(0,x.useCallback)(((e,t)=>{t?o({deep:!0}):l({deep:!0})}),[o,l]);return Array.isArray(t)&&0!==t.length?O().createElement(Hs.Provider,{value:n},O().createElement(Fs.Provider,{value:i},O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--prefixItems","data-json-schema-level":c},O().createElement(p,{expanded:s,onChange:h},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"Prefix items")),O().createElement(m,{expanded:s,onClick:g}),O().createElement(d,{schema:{prefixItems:t}}),O().createElement("ul",{className:pt()("json-schema-2020-12-keyword__children",{"json-schema-2020-12-keyword__children--collapsed":!s})},s&&O().createElement(O().Fragment,null,t.map(((e,t)=>O().createElement("li",{key:`#${t}`,className:"json-schema-2020-12-property"},O().createElement(u,{name:`#${t} ${r.getTitle(e)}`,schema:e}))))))))):null},keywords_Items=({schema:e})=>{const t=useFn(),r=useComponent("JSONSchema");if(!t.hasKeyword(e,"items"))return null;const a=O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"Items");return O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--items"},O().createElement(r,{name:a,schema:e.items,identifier:"items"}))},keywords_Contains=({schema:e})=>{const t=useFn(),r=useComponent("JSONSchema");if(!t.hasKeyword(e,"contains"))return null;const a=O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"Contains");return O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--contains"},O().createElement(r,{name:a,schema:e.contains,identifier:"contains"}))},keywords_Properties_Properties=({schema:e})=>{const t=useFn(),r=e?.properties||{},a=Array.isArray(e?.required)?e.required:[],n=useComponent("JSONSchema"),{path:s}=usePath("properties");return 0===Object.keys(r).length?null:O().createElement(Hs.Provider,{value:s},O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--properties"},O().createElement("ul",null,Object.entries(r).map((([r,s])=>{const o=a.includes(r),l=t.getDependentRequired(r,e);return O().createElement("li",{key:r,className:pt()("json-schema-2020-12-property",{"json-schema-2020-12-property--required":o})},O().createElement(n,{name:r,schema:s,dependentRequired:l}))})))))},PatternProperties_PatternProperties=({schema:e})=>{const t=e?.patternProperties||{},r=useComponent("JSONSchema"),{path:a}=usePath("patternProperties");return 0===Object.keys(t).length?null:O().createElement(Hs.Provider,{value:a},O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--patternProperties"},O().createElement("ul",null,Object.entries(t).map((([e,t])=>O().createElement("li",{key:e,className:"json-schema-2020-12-property"},O().createElement(r,{name:e,schema:t})))))))},keywords_AdditionalProperties=({schema:e})=>{const t=useFn(),r=useComponent("JSONSchema");if(!t.hasKeyword(e,"additionalProperties"))return null;const a=O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"Additional properties");return O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--additionalProperties"},!0===e.additionalProperties?O().createElement(O().Fragment,null,a,O().createElement("span",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--primary"},"allowed")):!1===e.additionalProperties?O().createElement(O().Fragment,null,a,O().createElement("span",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--primary"},"forbidden")):O().createElement(r,{name:a,schema:e.additionalProperties,identifier:"additionalProperties"}))},keywords_PropertyNames=({schema:e})=>{const t=useFn(),r=useComponent("JSONSchema"),a=O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"Property names");return t.hasKeyword(e,"propertyNames")?O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--propertyNames"},O().createElement(r,{name:a,schema:e.propertyNames,identifier:"propertyNames"})):null},keywords_UnevaluatedItems=({schema:e})=>{const t=useFn(),r=useComponent("JSONSchema");if(!t.hasKeyword(e,"unevaluatedItems"))return null;const a=O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"Unevaluated items");return O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--unevaluatedItems"},O().createElement(r,{name:a,schema:e.unevaluatedItems,identifier:"unevaluatedItems"}))},keywords_UnevaluatedProperties=({schema:e})=>{const t=useFn(),r=useComponent("JSONSchema");if(!t.hasKeyword(e,"unevaluatedProperties"))return null;const a=O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"Unevaluated properties");return O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--unevaluatedProperties"},O().createElement(r,{name:a,schema:e.unevaluatedProperties,identifier:"unevaluatedProperties"}))},keywords_Type=({schema:e,isCircular:t=!1})=>{const r=useFn().getType(e),a=t?" [circular]":"";return O().createElement("strong",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--primary"},`${r}${a}`)},Enum_Enum=({schema:e})=>{const t=useComponent("JSONViewer");return Array.isArray(e?.enum)?O().createElement(t,{name:"Enum",value:e.enum,className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--enum"}):null},Const_Const=({schema:e})=>{const t=useFn(),r=useComponent("JSONViewer");return t.hasKeyword(e,"const")?O().createElement(r,{name:"Const",value:e.const,className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--const"}):null},fn_upperFirst=e=>"string"==typeof e?`${e.charAt(0).toUpperCase()}${e.slice(1)}`:e,makeGetTitle=e=>(t,{lookup:r="extended"}={})=>{const a=e();if(null!=t?.title)return a.upperFirst(String(t.title));if("extended"===r){if(null!=t?.$anchor)return a.upperFirst(String(t.$anchor));if(null!=t?.$id)return String(t.$id)}return""},makeGetType=e=>{const getType=(t,r=new WeakSet)=>{const a=e();if(null==t)return"any";if(a.isBooleanJSONSchema(t))return t?"any":"never";if("object"!=typeof t)return"any";if(r.has(t))return"any";r.add(t);const{type:n,prefixItems:s,items:o}=t,getArrayType=()=>{if(Array.isArray(s)){const e=s.map((e=>getType(e,r))),t=o?getType(o,r):"any";return`array<[${e.join(", ")}], ${t}>`}if(o){return`array<${getType(o,r)}>`}return"array<any>"};if(t.not&&"any"===getType(t.not))return"never";const handleCombiningKeywords=(e,a)=>{if(Array.isArray(t[e])){return`(${t[e].map((e=>getType(e,r))).join(a)})`}return null},l=[Array.isArray(n)?n.map((e=>"array"===e?getArrayType():e)).join(" | "):"array"===n?getArrayType():["null","boolean","object","array","number","integer","string"].includes(n)?n:(()=>{if(Object.hasOwn(t,"prefixItems")||Object.hasOwn(t,"items")||Object.hasOwn(t,"contains"))return getArrayType();if(Object.hasOwn(t,"properties")||Object.hasOwn(t,"additionalProperties")||Object.hasOwn(t,"patternProperties"))return"object";if(["int32","int64"].includes(t.format))return"integer";if(["float","double"].includes(t.format))return"number";if(Object.hasOwn(t,"minimum")||Object.hasOwn(t,"maximum")||Object.hasOwn(t,"exclusiveMinimum")||Object.hasOwn(t,"exclusiveMaximum")||Object.hasOwn(t,"multipleOf"))return"number | integer";if(Object.hasOwn(t,"pattern")||Object.hasOwn(t,"format")||Object.hasOwn(t,"minLength")||Object.hasOwn(t,"maxLength")||Object.hasOwn(t,"contentEncoding")||Object.hasOwn(t,"contentMediaType"))return"string";if(void 0!==t.const){if(null===t.const)return"null";if("boolean"==typeof t.const)return"boolean";if("number"==typeof t.const)return Number.isInteger(t.const)?"integer":"number";if("string"==typeof t.const)return"string";if(Array.isArray(t.const))return"array<any>";if("object"==typeof t.const)return"object"}return null})(),handleCombiningKeywords("oneOf"," | "),handleCombiningKeywords("anyOf"," | "),handleCombiningKeywords("allOf"," & ")].filter(Boolean).join(" | ");return r.delete(t),l||"any"};return getType},isBooleanJSONSchema=e=>"boolean"==typeof e,hasKeyword=(e,t)=>null!==e&&"object"==typeof e&&Object.hasOwn(e,t),fn_makeIsExpandable=e=>t=>{const r=e();return t?.$schema||t?.$vocabulary||t?.$id||t?.$anchor||t?.$dynamicAnchor||t?.$ref||t?.$dynamicRef||t?.$defs||t?.$comment||t?.allOf||t?.anyOf||t?.oneOf||r.hasKeyword(t,"not")||r.hasKeyword(t,"if")||r.hasKeyword(t,"then")||r.hasKeyword(t,"else")||t?.dependentSchemas||t?.prefixItems||r.hasKeyword(t,"items")||r.hasKeyword(t,"contains")||t?.properties||t?.patternProperties||r.hasKeyword(t,"additionalProperties")||r.hasKeyword(t,"propertyNames")||r.hasKeyword(t,"unevaluatedItems")||r.hasKeyword(t,"unevaluatedProperties")||t?.description||t?.enum||r.hasKeyword(t,"const")||r.hasKeyword(t,"contentSchema")||r.hasKeyword(t,"default")||t?.examples||r.getExtensionKeywords(t).length>0},fn_stringify=e=>null===e||["number","bigint","boolean"].includes(typeof e)?String(e):Array.isArray(e)?`[${e.map(fn_stringify).join(", ")}]`:JSON.stringify(e),stringifyConstraintRange=(e,t,r)=>{const a="number"==typeof t,n="number"==typeof r;return a&&n?t===r?`${t} ${e}`:`[${t}, ${r}] ${e}`:a?`≥ ${t} ${e}`:n?`≤ ${r} ${e}`:null},stringifyConstraints=e=>{const t=[],r=(e=>{if("number"!=typeof e?.multipleOf)return null;if(e.multipleOf<=0)return null;if(1===e.multipleOf)return null;const{multipleOf:t}=e;if(Number.isInteger(t))return`multiple of ${t}`;const r=10**t.toString().split(".")[1].length;return`multiple of ${t*r}/${r}`})(e);null!==r&&t.push({scope:"number",value:r});const a=(e=>{const t=e?.minimum,r=e?.maximum,a=e?.exclusiveMinimum,n=e?.exclusiveMaximum,s="number"==typeof t,o="number"==typeof r,l="number"==typeof a,c="number"==typeof n,i=l&&(!s||t<a),p=c&&(!o||r>n);if((s||l)&&(o||c))return`${i?"(":"["}${i?a:t}, ${p?n:r}${p?")":"]"}`;if(s||l)return`${i?">":"≥"} ${i?a:t}`;if(o||c)return`${p?"<":"≤"} ${p?n:r}`;return null})(e);null!==a&&t.push({scope:"number",value:a}),e?.format&&t.push({scope:"string",value:e.format});const n=stringifyConstraintRange("characters",e?.minLength,e?.maxLength);null!==n&&t.push({scope:"string",value:n}),e?.pattern&&t.push({scope:"string",value:`matches ${e?.pattern}`}),e?.contentMediaType&&t.push({scope:"string",value:`media type: ${e.contentMediaType}`}),e?.contentEncoding&&t.push({scope:"string",value:`encoding: ${e.contentEncoding}`});const s=stringifyConstraintRange(e?.uniqueItems?"unique items":"items",e?.minItems,e?.maxItems);null!==s&&t.push({scope:"array",value:s}),e?.uniqueItems&&!s&&t.push({scope:"array",value:"unique"});const o=stringifyConstraintRange("contained items",e?.minContains,e?.maxContains);null!==o&&t.push({scope:"array",value:o});const l=stringifyConstraintRange("properties",e?.minProperties,e?.maxProperties);return null!==l&&t.push({scope:"object",value:l}),t},getDependentRequired=(e,t)=>t?.dependentRequired?Array.from(Object.entries(t.dependentRequired).reduce(((t,[r,a])=>Array.isArray(a)&&a.includes(e)?(t.add(r),t):t),new Set)):[],isPlainObject=e=>"object"==typeof e&&null!==e&&!Array.isArray(e)&&(null===Object.getPrototypeOf(e)||Object.getPrototypeOf(e)===Object.prototype),getSchemaKeywords=()=>["$schema","$vocabulary","$id","$anchor","$dynamicAnchor","$dynamicRef","$ref","$defs","$comment","allOf","anyOf","oneOf","not","if","then","else","dependentSchemas","prefixItems","items","contains","properties","patternProperties","additionalProperties","propertyNames","unevaluatedItems","unevaluatedProperties","type","enum","const","multipleOf","maximum","exclusiveMaximum","minimum","exclusiveMinimum","maxLength","minLength","pattern","maxItems","minItems","uniqueItems","maxContains","minContains","maxProperties","minProperties","required","dependentRequired","title","description","default","deprecated","readOnly","writeOnly","examples","format","contentEncoding","contentMediaType","contentSchema"],makeGetExtensionKeywords=e=>t=>{const r=e().getSchemaKeywords();return isPlainObject(t)?((e,t)=>{const r=new Set(t);return e.filter((e=>!r.has(e)))})(Object.keys(t),r):[]},fn_hasSchemaType=(e,t)=>{const r=k.Map.isMap(e);if(!r&&!isPlainObject(e))return!1;const hasType=e=>t===e||Array.isArray(t)&&t.includes(e),a=r?e.get("type"):e.type;return k.List.isList(a)||Array.isArray(a)?a.some((e=>hasType(e))):hasType(a)},Constraint=({constraint:e})=>isPlainObject(e)&&"string"==typeof e.scope&&"string"==typeof e.value?O().createElement("span",{className:`json-schema-2020-12__constraint json-schema-2020-12__constraint--${e.scope}`},e.value):null,Ys=O().memo(Constraint),DependentRequired_DependentRequired=({dependentRequired:e})=>Array.isArray(e)&&0!==e.length?O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--dependentRequired"},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"Required when defined"),O().createElement("ul",null,e.map((e=>O().createElement("li",{key:e},O().createElement("span",{className:"json-schema-2020-12-keyword__value json-schema-2020-12-keyword__value--warning"},e)))))):null,keywords_ContentSchema=({schema:e})=>{const t=useFn(),r=useComponent("JSONSchema");if(!t.hasKeyword(e,"contentSchema"))return null;const a=O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--primary"},"Content schema");return O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--contentSchema"},O().createElement(r,{name:a,schema:e.contentSchema,identifier:"contentSchema"}))},Title_Title=({title:e="",schema:t})=>{const r=useFn(),a=e||r.getTitle(t);return a?O().createElement("div",{className:"json-schema-2020-12__title"},a):null},keywords_Description_Description=({schema:e})=>e?.description?O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--description"},O().createElement("div",{className:"json-schema-2020-12-core-keyword__value json-schema-2020-12-core-keyword__value--secondary"},e.description)):null,Default_Default=({schema:e})=>{const t=useFn(),r=useComponent("JSONViewer");return t.hasKeyword(e,"default")?O().createElement(r,{name:"Default",value:e.default,className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--default"}):null},keywords_Deprecated=({schema:e})=>!0!==e?.deprecated?null:O().createElement("span",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--warning"},"deprecated"),keywords_ReadOnly=({schema:e})=>!0!==e?.readOnly?null:O().createElement("span",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--muted"},"read-only"),keywords_WriteOnly=({schema:e})=>!0!==e?.writeOnly?null:O().createElement("span",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--muted"},"write-only"),keywords_Examples_Examples=({schema:e})=>{const t=e?.examples||[],r=useComponent("JSONViewer");return Array.isArray(t)&&0!==t.length?O().createElement(r,{name:"Examples",value:e.examples,className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--examples"}):null},ExtensionKeywords_ExtensionKeywords=({schema:e})=>{const t=useFn(),r="ExtensionKeywords",{path:a}=usePath(r),{isExpanded:n,setExpanded:s,setCollapsed:o}=useIsExpanded(r),[l,c]=useLevel(),i=useComponent("Accordion"),p=useComponent("ExpandDeepButton"),m=useComponent("JSONViewer"),{showExtensionKeywords:u}=useConfig(),d=t.getExtensionKeywords(e),h=(0,x.useCallback)((()=>{n?o():s()}),[n,s,o]),g=(0,x.useCallback)(((e,t)=>{t?s({deep:!0}):o({deep:!0})}),[s,o]);return u&&0!==d.length?O().createElement(Hs.Provider,{value:a},O().createElement(Fs.Provider,{value:c},O().createElement("div",{className:"json-schema-2020-12-keyword json-schema-2020-12-keyword--extension-keywords","data-json-schema-level":l},O().createElement(i,{expanded:n,onChange:h},O().createElement("span",{className:"json-schema-2020-12-keyword__name json-schema-2020-12-keyword__name--extension"},"Extension Keywords")),O().createElement(p,{expanded:n,onClick:g}),O().createElement("ul",{className:pt()("json-schema-2020-12-keyword__children",{"json-schema-2020-12-keyword__children--collapsed":!n})},n&&O().createElement(O().Fragment,null,d.map((t=>O().createElement(m,{key:t,name:t,value:e[t],className:"json-schema-2020-12-json-viewer-extension-keyword"})))))))):null},JSONViewer=({name:e,value:t,className:r})=>{const a=useFn(),{path:n}=usePath(e),{isExpanded:s,setExpanded:o,setCollapsed:l}=useIsExpanded(e),[c,i]=useLevel(),p=useComponent("Accordion"),m=useComponent("ExpandDeepButton"),u="string"==typeof t||"number"==typeof t||"bigint"==typeof t||"boolean"==typeof t||"symbol"==typeof t||null==t,d=(e=>isPlainObject(e)&&0===Object.keys(e).length)(t)||(e=>Array.isArray(e)&&0===e.length)(t),h=(0,x.useCallback)((()=>{s?l():o()}),[s,o,l]),g=(0,x.useCallback)(((e,t)=>{t?o({deep:!0}):l({deep:!0})}),[o,l]);return u?O().createElement("div",{className:pt()("json-schema-2020-12-json-viewer",r)},O().createElement("span",{className:"json-schema-2020-12-json-viewer__name json-schema-2020-12-json-viewer__name--secondary"},e),O().createElement("span",{className:"json-schema-2020-12-json-viewer__value json-schema-2020-12-json-viewer__value--secondary"},a.stringify(t))):d?O().createElement("div",{className:pt()("json-schema-2020-12-json-viewer",r)},O().createElement("span",{className:"json-schema-2020-12-json-viewer__name json-schema-2020-12-json-viewer__name--secondary"},e),O().createElement("strong",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--primary"},Array.isArray(t)?"empty array":"empty object")):O().createElement(Hs.Provider,{value:n},O().createElement(Fs.Provider,{value:i},O().createElement("div",{className:pt()("json-schema-2020-12-json-viewer",r),"data-json-schema-level":c},O().createElement(p,{expanded:s,onChange:h},O().createElement("span",{className:"json-schema-2020-12-json-viewer__name json-schema-2020-12-json-viewer__name--secondary"},e)),O().createElement(m,{expanded:s,onClick:g}),O().createElement("strong",{className:"json-schema-2020-12__attribute json-schema-2020-12__attribute--primary"},Array.isArray(t)?"array":"object"),O().createElement("ul",{className:pt()("json-schema-2020-12-json-viewer__children",{"json-schema-2020-12-json-viewer__children--collapsed":!s})},s&&O().createElement(O().Fragment,null,Array.isArray(t)?t.map(((e,t)=>O().createElement("li",{key:`#${t}`,className:"json-schema-2020-12-property"},O().createElement(JSONViewer,{name:`#${t}`,value:e,className:r})))):Object.entries(t).map((([e,t])=>O().createElement("li",{key:e,className:"json-schema-2020-12-property"},O().createElement(JSONViewer,{name:e,value:t,className:r})))))))))},Qs=JSONViewer,Accordion_Accordion=({expanded:e=!1,children:t,onChange:r})=>{const a=useComponent("ChevronRightIcon"),n=(0,x.useCallback)((t=>{r(t,!e)}),[e,r]);return O().createElement("button",{type:"button",className:"json-schema-2020-12-accordion",onClick:n},O().createElement("div",{className:"json-schema-2020-12-accordion__children"},t),O().createElement("span",{className:pt()("json-schema-2020-12-accordion__icon",{"json-schema-2020-12-accordion__icon--expanded":e,"json-schema-2020-12-accordion__icon--collapsed":!e})},O().createElement(a,null)))},ExpandDeepButton_ExpandDeepButton=({expanded:e,onClick:t})=>{const r=(0,x.useCallback)((r=>{t(r,!e)}),[e,t]);return O().createElement("button",{type:"button",className:"json-schema-2020-12-expand-deep-button",onClick:r},e?"Collapse all":"Expand all")},icons_ChevronRight=()=>O().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24"},O().createElement("path",{d:"M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"})),withJSONSchemaContext=(e,t={})=>{const r={components:{JSONSchema:Gs,Keyword$schema:keywords_$schema,Keyword$vocabulary:$vocabulary_$vocabulary,Keyword$id:keywords_$id,Keyword$anchor:keywords_$anchor,Keyword$dynamicAnchor:keywords_$dynamicAnchor,Keyword$ref:keywords_$ref,Keyword$dynamicRef:keywords_$dynamicRef,Keyword$defs:keywords_$defs,Keyword$comment:keywords_$comment,KeywordAllOf:keywords_AllOf,KeywordAnyOf:keywords_AnyOf,KeywordOneOf:keywords_OneOf,KeywordNot:keywords_Not,KeywordIf:keywords_If,KeywordThen:keywords_Then,KeywordElse:keywords_Else,KeywordDependentSchemas:keywords_DependentSchemas,KeywordPrefixItems:keywords_PrefixItems,KeywordItems:keywords_Items,KeywordContains:keywords_Contains,KeywordProperties:keywords_Properties_Properties,KeywordPatternProperties:PatternProperties_PatternProperties,KeywordAdditionalProperties:keywords_AdditionalProperties,KeywordPropertyNames:keywords_PropertyNames,KeywordUnevaluatedItems:keywords_UnevaluatedItems,KeywordUnevaluatedProperties:keywords_UnevaluatedProperties,KeywordType:keywords_Type,KeywordEnum:Enum_Enum,KeywordConst:Const_Const,KeywordConstraint:Ys,KeywordDependentRequired:DependentRequired_DependentRequired,KeywordContentSchema:keywords_ContentSchema,KeywordTitle:Title_Title,KeywordDescription:keywords_Description_Description,KeywordDefault:Default_Default,KeywordDeprecated:keywords_Deprecated,KeywordReadOnly:keywords_ReadOnly,KeywordWriteOnly:keywords_WriteOnly,KeywordExamples:keywords_Examples_Examples,ExtensionKeywords:ExtensionKeywords_ExtensionKeywords,JSONViewer:Qs,Accordion:Accordion_Accordion,ExpandDeepButton:ExpandDeepButton_ExpandDeepButton,ChevronRightIcon:icons_ChevronRight,...t.components},config:{default$schema:"https://json-schema.org/draft/2020-12/schema",defaultExpandedLevels:0,showExtensionKeywords:!0,...t.config},fn:{upperFirst:fn_upperFirst,getTitle:makeGetTitle(useFn),getType:makeGetType(useFn),isBooleanJSONSchema,hasKeyword,isExpandable:fn_makeIsExpandable(useFn),stringify:fn_stringify,stringifyConstraints,getDependentRequired,getSchemaKeywords,getExtensionKeywords:makeGetExtensionKeywords(useFn),...t.fn},state:{paths:{}}},HOC=t=>O().createElement(Bs.Provider,{value:r},O().createElement(e,t));return HOC.contexts={JSONSchemaContext:Bs},HOC.displayName=e.displayName,HOC},makeWithJSONSchemaSystemContext=({getSystem:e})=>(t,r={})=>{const{getComponent:a,getConfigs:n}=e(),s=n(),o=a("JSONSchema202012"),l=a("JSONSchema202012Keyword$schema"),c=a("JSONSchema202012Keyword$vocabulary"),i=a("JSONSchema202012Keyword$id"),p=a("JSONSchema202012Keyword$anchor"),m=a("JSONSchema202012Keyword$dynamicAnchor"),u=a("JSONSchema202012Keyword$ref"),d=a("JSONSchema202012Keyword$dynamicRef"),h=a("JSONSchema202012Keyword$defs"),g=a("JSONSchema202012Keyword$comment"),y=a("JSONSchema202012KeywordAllOf"),f=a("JSONSchema202012KeywordAnyOf"),S=a("JSONSchema202012KeywordOneOf"),E=a("JSONSchema202012KeywordNot"),_=a("JSONSchema202012KeywordIf"),v=a("JSONSchema202012KeywordThen"),w=a("JSONSchema202012KeywordElse"),b=a("JSONSchema202012KeywordDependentSchemas"),C=a("JSONSchema202012KeywordPrefixItems"),x=a("JSONSchema202012KeywordItems"),O=a("JSONSchema202012KeywordContains"),N=a("JSONSchema202012KeywordProperties"),k=a("JSONSchema202012KeywordPatternProperties"),A=a("JSONSchema202012KeywordAdditionalProperties"),I=a("JSONSchema202012KeywordPropertyNames"),j=a("JSONSchema202012KeywordUnevaluatedItems"),q=a("JSONSchema202012KeywordUnevaluatedProperties"),P=a("JSONSchema202012KeywordType"),M=a("JSONSchema202012KeywordEnum"),T=a("JSONSchema202012KeywordConst"),R=a("JSONSchema202012KeywordConstraint"),J=a("JSONSchema202012KeywordDependentRequired"),$=a("JSONSchema202012KeywordContentSchema"),V=a("JSONSchema202012KeywordTitle"),L=a("JSONSchema202012KeywordDescription"),D=a("JSONSchema202012KeywordDefault"),U=a("JSONSchema202012KeywordDeprecated"),K=a("JSONSchema202012KeywordReadOnly"),z=a("JSONSchema202012KeywordWriteOnly"),B=a("JSONSchema202012KeywordExamples"),F=a("JSONSchema202012ExtensionKeywords"),W=a("JSONSchema202012JSONViewer"),H=a("JSONSchema202012Accordion"),X=a("JSONSchema202012ExpandDeepButton"),G=a("JSONSchema202012ChevronRightIcon");return withJSONSchemaContext(t,{components:{JSONSchema:o,Keyword$schema:l,Keyword$vocabulary:c,Keyword$id:i,Keyword$anchor:p,Keyword$dynamicAnchor:m,Keyword$ref:u,Keyword$dynamicRef:d,Keyword$defs:h,Keyword$comment:g,KeywordAllOf:y,KeywordAnyOf:f,KeywordOneOf:S,KeywordNot:E,KeywordIf:_,KeywordThen:v,KeywordElse:w,KeywordDependentSchemas:b,KeywordPrefixItems:C,KeywordItems:x,KeywordContains:O,KeywordProperties:N,KeywordPatternProperties:k,KeywordAdditionalProperties:A,KeywordPropertyNames:I,KeywordUnevaluatedItems:j,KeywordUnevaluatedProperties:q,KeywordType:P,KeywordEnum:M,KeywordConst:T,KeywordConstraint:R,KeywordDependentRequired:J,KeywordContentSchema:$,KeywordTitle:V,KeywordDescription:L,KeywordDefault:D,KeywordDeprecated:U,KeywordReadOnly:K,KeywordWriteOnly:z,KeywordExamples:B,ExtensionKeywords:F,JSONViewer:W,Accordion:H,ExpandDeepButton:X,ChevronRightIcon:G,...r.components},config:{showExtensionKeywords:s.showExtensions,...r.config},fn:{...r.fn}})},json_schema_2020_12=({getSystem:e,fn:t})=>{const fnAccessor=()=>({upperFirst:t.upperFirst,...t.jsonSchema202012});return{components:{JSONSchema202012:Gs,JSONSchema202012Keyword$schema:keywords_$schema,JSONSchema202012Keyword$vocabulary:$vocabulary_$vocabulary,JSONSchema202012Keyword$id:keywords_$id,JSONSchema202012Keyword$anchor:keywords_$anchor,JSONSchema202012Keyword$dynamicAnchor:keywords_$dynamicAnchor,JSONSchema202012Keyword$ref:keywords_$ref,JSONSchema202012Keyword$dynamicRef:keywords_$dynamicRef,JSONSchema202012Keyword$defs:keywords_$defs,JSONSchema202012Keyword$comment:keywords_$comment,JSONSchema202012KeywordAllOf:keywords_AllOf,JSONSchema202012KeywordAnyOf:keywords_AnyOf,JSONSchema202012KeywordOneOf:keywords_OneOf,JSONSchema202012KeywordNot:keywords_Not,JSONSchema202012KeywordIf:keywords_If,JSONSchema202012KeywordThen:keywords_Then,JSONSchema202012KeywordElse:keywords_Else,JSONSchema202012KeywordDependentSchemas:keywords_DependentSchemas,JSONSchema202012KeywordPrefixItems:keywords_PrefixItems,JSONSchema202012KeywordItems:keywords_Items,JSONSchema202012KeywordContains:keywords_Contains,JSONSchema202012KeywordProperties:keywords_Properties_Properties,JSONSchema202012KeywordPatternProperties:PatternProperties_PatternProperties,JSONSchema202012KeywordAdditionalProperties:keywords_AdditionalProperties,JSONSchema202012KeywordPropertyNames:keywords_PropertyNames,JSONSchema202012KeywordUnevaluatedItems:keywords_UnevaluatedItems,JSONSchema202012KeywordUnevaluatedProperties:keywords_UnevaluatedProperties,JSONSchema202012KeywordType:keywords_Type,JSONSchema202012KeywordEnum:Enum_Enum,JSONSchema202012KeywordConst:Const_Const,JSONSchema202012KeywordConstraint:Ys,JSONSchema202012KeywordDependentRequired:DependentRequired_DependentRequired,JSONSchema202012KeywordContentSchema:keywords_ContentSchema,JSONSchema202012KeywordTitle:Title_Title,JSONSchema202012KeywordDescription:keywords_Description_Description,JSONSchema202012KeywordDefault:Default_Default,JSONSchema202012KeywordDeprecated:keywords_Deprecated,JSONSchema202012KeywordReadOnly:keywords_ReadOnly,JSONSchema202012KeywordWriteOnly:keywords_WriteOnly,JSONSchema202012KeywordExamples:keywords_Examples_Examples,JSONSchema202012ExtensionKeywords:ExtensionKeywords_ExtensionKeywords,JSONSchema202012JSONViewer:Qs,JSONSchema202012Accordion:Accordion_Accordion,JSONSchema202012ExpandDeepButton:ExpandDeepButton_ExpandDeepButton,JSONSchema202012ChevronRightIcon:icons_ChevronRight,withJSONSchema202012Context:withJSONSchemaContext,withJSONSchema202012SystemContext:makeWithJSONSchemaSystemContext(e()),JSONSchema202012PathContext:()=>Hs,JSONSchema202012LevelContext:()=>Fs},fn:{upperFirst:fn_upperFirst,jsonSchema202012:{getTitle:makeGetTitle(fnAccessor),getType:makeGetType(fnAccessor),isExpandable:fn_makeIsExpandable(fnAccessor),isBooleanJSONSchema,hasKeyword,useFn,useConfig,useComponent,useIsExpanded,usePath,useLevel,getSchemaKeywords,getExtensionKeywords:makeGetExtensionKeywords(fnAccessor),hasSchemaType:fn_hasSchemaType}}}},array=(e,{sample:t=[]}={})=>((e,t={})=>{const{minItems:r,maxItems:a,uniqueItems:n}=t,{contains:s,minContains:o,maxContains:l}=t;let c=[...e];if(null!=s&&"object"==typeof s){if(Number.isInteger(o)&&o>1){const e=c.at(0);for(let t=1;t<o;t+=1)c.unshift(e)}Number.isInteger(l)}if(Number.isInteger(a)&&a>0&&(c=e.slice(0,a)),Number.isInteger(r)&&r>0)for(let e=0;c.length<r;e+=1)c.push(c[e%c.length]);return!0===n&&(c=Array.from(new Set(c))),c})(t,e),object=()=>{throw new Error("Not implemented")},bytes=e=>ae()(e),pick=e=>e.at(0),predicates_isBooleanJSONSchema=e=>"boolean"==typeof e,isJSONSchemaObject=e=>wt()(e),isJSONSchema=e=>predicates_isBooleanJSONSchema(e)||isJSONSchemaObject(e);const Zs=class Registry{data={};register(e,t){this.data[e]=t}unregister(e){void 0===e?this.data={}:delete this.data[e]}get(e){return this.data[e]}},int32=()=>0,int64=()=>0,generators_float=()=>.1,generators_double=()=>.1,email=()=>"<EMAIL>",idn_email=()=>"실례@example.com",hostname=()=>"example.com",idn_hostname=()=>"실례.com",ipv4=()=>"*************",ipv6=()=>"2001:0db8:5b96:0000:0000:426f:8e17:642a",uri=()=>"https://example.com/",uri_reference=()=>"path/index.html",iri=()=>"https://실례.com/",iri_reference=()=>"path/실례.html",uuid=()=>"3fa85f64-5717-4562-b3fc-2c963f66afa6",uri_template=()=>"https://example.com/dictionary/{term:1}/{term}",json_pointer=()=>"/a/b/c",relative_json_pointer=()=>"1/0",date_time=()=>(new Date).toISOString(),date=()=>(new Date).toISOString().substring(0,10),time=()=>(new Date).toISOString().substring(11),duration=()=>"P3D",generators_password=()=>"********",regex=()=>"^[a-z]+$";const eo=new class FormatRegistry extends Zs{#e={int32,int64,float:generators_float,double:generators_double,email,"idn-email":idn_email,hostname,"idn-hostname":idn_hostname,ipv4,ipv6,uri,"uri-reference":uri_reference,iri,"iri-reference":iri_reference,uuid,"uri-template":uri_template,"json-pointer":json_pointer,"relative-json-pointer":relative_json_pointer,"date-time":date_time,date,time,duration,password:generators_password,regex};data={...this.#e};get defaults(){return{...this.#e}}},formatAPI=(e,t)=>"function"==typeof t?eo.register(e,t):null===t?eo.unregister(e):eo.get(e);formatAPI.getDefaults=()=>eo.defaults;const to=formatAPI;var ro=__webpack_require__(158).Buffer;const _7bit=e=>ro.from(e).toString("ascii");var ao=__webpack_require__(158).Buffer;const _8bit=e=>ao.from(e).toString("utf8");var no=__webpack_require__(158).Buffer;const binary=e=>no.from(e).toString("binary"),quoted_printable=e=>{let t="";for(let r=0;r<e.length;r++){const a=e.charCodeAt(r);if(61===a)t+="=3D";else if(a>=33&&a<=60||a>=62&&a<=126||9===a||32===a)t+=e.charAt(r);else if(13===a||10===a)t+="\r\n";else if(a>126){const a=unescape(encodeURIComponent(e.charAt(r)));for(let e=0;e<a.length;e++)t+="="+("0"+a.charCodeAt(e).toString(16)).slice(-2).toUpperCase()}else t+="="+("0"+a.toString(16)).slice(-2).toUpperCase()}return t};var so=__webpack_require__(158).Buffer;const base16=e=>so.from(e).toString("hex");var oo=__webpack_require__(158).Buffer;const base32=e=>{const t=oo.from(e).toString("utf8"),r="ABCDEFGHIJKLMNOPQRSTUVWXYZ234567";let a=0,n="",s=0,o=0;for(let e=0;e<t.length;e++)for(s=s<<8|t.charCodeAt(e),o+=8;o>=5;)n+=r.charAt(s>>>o-5&31),o-=5;o>0&&(n+=r.charAt(s<<5-o&31),a=(8-8*t.length%5)%5);for(let e=0;e<a;e++)n+="=";return n};var lo=__webpack_require__(158).Buffer;const base64=e=>lo.from(e).toString("base64");var co=__webpack_require__(158).Buffer;const base64url=e=>co.from(e).toString("base64url");const io=new class EncoderRegistry extends Zs{#e={"7bit":_7bit,"8bit":_8bit,binary,"quoted-printable":quoted_printable,base16,base32,base64,base64url};data={...this.#e};get defaults(){return{...this.#e}}},encoderAPI=(e,t)=>"function"==typeof t?io.register(e,t):null===t?io.unregister(e):io.get(e);encoderAPI.getDefaults=()=>io.defaults;const po=encoderAPI,mo={"text/plain":()=>"string","text/css":()=>".selector { border: 1px solid red }","text/csv":()=>"value1,value2,value3","text/html":()=>"<p>content</p>","text/calendar":()=>"BEGIN:VCALENDAR","text/javascript":()=>"console.dir('Hello world!');","text/xml":()=>'<person age="30">John Doe</person>',"text/*":()=>"string"},uo={"image/*":()=>bytes(25).toString("binary")},ho={"audio/*":()=>bytes(25).toString("binary")},go={"video/*":()=>bytes(25).toString("binary")},yo={"application/json":()=>'{"key":"value"}',"application/ld+json":()=>'{"name": "John Doe"}',"application/x-httpd-php":()=>"<?php echo '<p>Hello World!</p>'; ?>","application/rtf":()=>String.raw`{\rtf1\adeflang1025\ansi\ansicpg1252\uc1`,"application/x-sh":()=>'echo "Hello World!"',"application/xhtml+xml":()=>"<p>content</p>","application/*":()=>bytes(25).toString("binary")};const fo=new class MediaTypeRegistry extends Zs{#e={...mo,...uo,...ho,...go,...yo};data={...this.#e};get defaults(){return{...this.#e}}},mediaTypeAPI=(e,t)=>{if("function"==typeof t)return fo.register(e,t);if(null===t)return fo.unregister(e);const r=e.split(";").at(0),a=`${r.split("/").at(0)}/*`;return fo.get(e)||fo.get(r)||fo.get(a)};mediaTypeAPI.getDefaults=()=>fo.defaults;const So=mediaTypeAPI,applyStringConstraints=(e,t={})=>{const{maxLength:r,minLength:a}=t;let n=e;if(Number.isInteger(r)&&r>0&&(n=n.slice(0,r)),Number.isInteger(a)&&a>0){let e=0;for(;n.length<a;)n+=n[e++%n.length]}return n},types_string=(e,{sample:t}={})=>{const{contentEncoding:r,contentMediaType:a,contentSchema:n}=e,{pattern:s,format:o}=e,l=po(r)||ma();let c;return c="string"==typeof s?applyStringConstraints((e=>{try{const t=/(?<=(?<!\\)\{)(\d{3,})(?=\})|(?<=(?<!\\)\{\d*,)(\d{3,})(?=\})|(?<=(?<!\\)\{)(\d{3,})(?=,\d*\})/g,r=e.replace(t,"100"),a=new(Ot())(r);return a.max=100,a.gen()}catch{return"string"}})(s),e):"string"==typeof o?(e=>{const{format:t}=e,r=to(t);return"function"==typeof r?r(e):"string"})(e):isJSONSchema(n)&&"string"==typeof a&&void 0!==t?Array.isArray(t)||"object"==typeof t?JSON.stringify(t):applyStringConstraints(String(t),e):"string"==typeof a?(e=>{const{contentMediaType:t}=e,r=So(t);return"function"==typeof r?r(e):"string"})(e):applyStringConstraints("string",e),l(c)},applyNumberConstraints=(e,t={})=>{const{minimum:r,maximum:a,exclusiveMinimum:n,exclusiveMaximum:s}=t,{multipleOf:o}=t,l=Number.isInteger(e)?1:Number.EPSILON;let c="number"==typeof r?r:null,i="number"==typeof a?a:null,p=e;if("number"==typeof n&&(c=null!==c?Math.max(c,n+l):n+l),"number"==typeof s&&(i=null!==i?Math.min(i,s-l):s-l),p=c>i&&e||c||i||p,"number"==typeof o&&o>0){const e=p%o;p=0===e?p:p+o-e}return p},types_number=e=>{const{format:t}=e;let r;return r="string"==typeof t?(e=>{const{format:t}=e,r=to(t);return"function"==typeof r?r(e):0})(e):0,applyNumberConstraints(r,e)},types_integer=e=>{const{format:t}=e;let r;return r="string"==typeof t?(e=>{const{format:t}=e,r=to(t);if("function"==typeof r)return r(e);switch(t){case"int32":return int32();case"int64":return int64()}return 0})(e):0,applyNumberConstraints(r,e)},types_boolean=e=>"boolean"!=typeof e.default||e.default,Eo=new Proxy({array,object,string:types_string,number:types_number,integer:types_integer,boolean:types_boolean,null:()=>null},{get:(e,t)=>"string"==typeof t&&Object.hasOwn(e,t)?e[t]:()=>`Unknown Type: ${t}`}),_o=["array","object","number","integer","string","boolean","null"],hasExample=e=>{if(!isJSONSchemaObject(e))return!1;const{examples:t,example:r,default:a}=e;return!!(Array.isArray(t)&&t.length>=1)||(void 0!==a||void 0!==r)},extractExample=e=>{if(!isJSONSchemaObject(e))return null;const{examples:t,example:r,default:a}=e;return Array.isArray(t)&&t.length>=1?t.at(0):void 0!==a?a:void 0!==r?r:void 0},vo={array:["items","prefixItems","contains","maxContains","minContains","maxItems","minItems","uniqueItems","unevaluatedItems"],object:["properties","additionalProperties","patternProperties","propertyNames","minProperties","maxProperties","required","dependentSchemas","dependentRequired","unevaluatedProperties"],string:["pattern","format","minLength","maxLength","contentEncoding","contentMediaType","contentSchema"],integer:["minimum","maximum","exclusiveMinimum","exclusiveMaximum","multipleOf"]};vo.number=vo.integer;const wo="string",inferTypeFromValue=e=>void 0===e?null:null===e?"null":Array.isArray(e)?"array":Number.isInteger(e)?"integer":typeof e,foldType=e=>{if(Array.isArray(e)&&e.length>=1){if(e.includes("array"))return"array";if(e.includes("object"))return"object";{const t=e.filter((e=>"null"!==e)),r=pick(t.length>0?t:e);if(_o.includes(r))return r}}return _o.includes(e)?e:null},inferType=(e,t=new WeakSet)=>{if(!isJSONSchemaObject(e))return wo;if(t.has(e))return wo;t.add(e);let{type:r,const:a}=e;if(r=foldType(r),"string"!=typeof r){const t=Object.keys(vo);e:for(let a=0;a<t.length;a+=1){const n=t[a],s=vo[n];for(let t=0;t<s.length;t+=1){const a=s[t];if(Object.hasOwn(e,a)){r=n;break e}}}}if("string"!=typeof r&&void 0!==a){const e=inferTypeFromValue(a);r="string"==typeof e?e:r}if("string"!=typeof r){const combineTypes=r=>{if(Array.isArray(e[r])){const a=e[r].map((e=>inferType(e,t)));return foldType(a)}return null},a=combineTypes("allOf"),n=combineTypes("anyOf"),s=combineTypes("oneOf"),o=e.not?inferType(e.not,t):null;(a||n||s||o)&&(r=foldType([a,n,s,o].filter(Boolean)))}if("string"!=typeof r&&hasExample(e)){const t=extractExample(e),a=inferTypeFromValue(t);r="string"==typeof a?a:r}return t.delete(e),r||wo},type_getType=e=>inferType(e),typeCast=e=>predicates_isBooleanJSONSchema(e)?(e=>!1===e?{not:{}}:{})(e):isJSONSchemaObject(e)?e:{},merge=(e,t,r={})=>{if(predicates_isBooleanJSONSchema(e)&&!0===e)return!0;if(predicates_isBooleanJSONSchema(e)&&!1===e)return!1;if(predicates_isBooleanJSONSchema(t)&&!0===t)return!0;if(predicates_isBooleanJSONSchema(t)&&!1===t)return!1;if(!isJSONSchema(e))return t;if(!isJSONSchema(t))return e;const a={...t,...e};if(t.type&&e.type&&Array.isArray(t.type)&&"string"==typeof t.type){const r=normalizeArray(t.type).concat(e.type);a.type=Array.from(new Set(r))}if(Array.isArray(t.required)&&Array.isArray(e.required)&&(a.required=[...new Set([...e.required,...t.required])]),t.properties&&e.properties){const n=new Set([...Object.keys(t.properties),...Object.keys(e.properties)]);a.properties={};for(const s of n){const n=t.properties[s]||{},o=e.properties[s]||{};n.readOnly&&!r.includeReadOnly||n.writeOnly&&!r.includeWriteOnly?a.required=(a.required||[]).filter((e=>e!==s)):a.properties[s]=merge(o,n,r)}}return isJSONSchema(t.items)&&isJSONSchema(e.items)&&(a.items=merge(e.items,t.items,r)),isJSONSchema(t.contains)&&isJSONSchema(e.contains)&&(a.contains=merge(e.contains,t.contains,r)),isJSONSchema(t.contentSchema)&&isJSONSchema(e.contentSchema)&&(a.contentSchema=merge(e.contentSchema,t.contentSchema,r)),a},bo=merge,main_sampleFromSchemaGeneric=(e,t={},r=void 0,a=!1)=>{if(null==e&&void 0===r)return;"function"==typeof e?.toJS&&(e=e.toJS()),e=typeCast(e);let n=void 0!==r||hasExample(e);const s=!n&&Array.isArray(e.oneOf)&&e.oneOf.length>0,o=!n&&Array.isArray(e.anyOf)&&e.anyOf.length>0;if(!n&&(s||o)){const r=typeCast(pick(s?e.oneOf:e.anyOf));!(e=bo(e,r,t)).xml&&r.xml&&(e.xml=r.xml),hasExample(e)&&hasExample(r)&&(n=!0)}const l={};let{xml:c,properties:i,additionalProperties:p,items:m,contains:u}=e||{},d=type_getType(e),{includeReadOnly:h,includeWriteOnly:g}=t;c=c||{};let y,{name:f,prefix:S,namespace:E}=c,_={};if(Object.hasOwn(e,"type")||(e.type=d),a&&(f=f||"notagname",y=(S?`${S}:`:"")+f,E)){l[S?`xmlns:${S}`:"xmlns"]=E}a&&(_[y]=[]);const v=objectify(i);let w,b=0;const hasExceededMaxProperties=()=>Number.isInteger(e.maxProperties)&&e.maxProperties>0&&b>=e.maxProperties,canAddProperty=t=>!(Number.isInteger(e.maxProperties)&&e.maxProperties>0)||!hasExceededMaxProperties()&&(!(t=>!Array.isArray(e.required)||0===e.required.length||!e.required.includes(t))(t)||e.maxProperties-b-(()=>{if(!Array.isArray(e.required)||0===e.required.length)return 0;let t=0;return a?e.required.forEach((e=>t+=void 0===_[e]?0:1)):e.required.forEach((e=>{t+=void 0===_[y]?.find((t=>void 0!==t[e]))?0:1})),e.required.length-t})()>0);if(w=a?(r,n=void 0)=>{if(e&&v[r]){if(v[r].xml=v[r].xml||{},v[r].xml.attribute){const e=Array.isArray(v[r].enum)?pick(v[r].enum):void 0;if(hasExample(v[r]))l[v[r].xml.name||r]=extractExample(v[r]);else if(void 0!==e)l[v[r].xml.name||r]=e;else{const e=typeCast(v[r]),a=type_getType(e),s=v[r].xml.name||r;if("array"===a){const e=main_sampleFromSchemaGeneric(v[r],t,n,!1);l[s]=e.map((e=>wt()(e)?"UnknownTypeObject":Array.isArray(e)?"UnknownTypeArray":e)).join(" ")}else l[s]="object"===a?"UnknownTypeObject":Eo[a](e)}return}v[r].xml.name=v[r].xml.name||r}else v[r]||!1===p||(v[r]={xml:{name:r}});let s=main_sampleFromSchemaGeneric(v[r],t,n,a);canAddProperty(r)&&(b++,Array.isArray(s)?_[y]=_[y].concat(s):_[y].push(s))}:(r,n)=>{if(canAddProperty(r)){if(wt()(e.discriminator?.mapping)&&e.discriminator.propertyName===r&&"string"==typeof e.$$ref){for(const t in e.discriminator.mapping)if(-1!==e.$$ref.search(e.discriminator.mapping[t])){_[r]=t;break}}else _[r]=main_sampleFromSchemaGeneric(v[r],t,n,a);b++}},n){let n;if(n=void 0!==r?r:extractExample(e),!a){if("number"==typeof n&&"string"===d)return`${n}`;if("string"!=typeof n||"string"===d)return n;try{return JSON.parse(n)}catch{return n}}if("array"===d){if(!Array.isArray(n)){if("string"==typeof n)return n;n=[n]}let r=[];return isJSONSchemaObject(m)&&(m.xml=m.xml||c||{},m.xml.name=m.xml.name||c.name,r=n.map((e=>main_sampleFromSchemaGeneric(m,t,e,a)))),isJSONSchemaObject(u)&&(u.xml=u.xml||c||{},u.xml.name=u.xml.name||c.name,r=[main_sampleFromSchemaGeneric(u,t,void 0,a),...r]),r=Eo.array(e,{sample:r}),c.wrapped?(_[y]=r,kt()(l)||_[y].push({_attr:l})):_=r,_}if("object"===d){if("string"==typeof n)return n;for(const e in n)Object.hasOwn(n,e)&&(v[e]?.readOnly&&!h||v[e]?.writeOnly&&!g||(v[e]?.xml?.attribute?l[v[e].xml.name||e]=n[e]:w(e,n[e])));return kt()(l)||_[y].push({_attr:l}),_}return _[y]=kt()(l)?n:[{_attr:l},n],_}if("array"===d){let r=[];if(isJSONSchemaObject(u))if(a&&(u.xml=u.xml||e.xml||{},u.xml.name=u.xml.name||c.name),Array.isArray(u.anyOf)){const{anyOf:e,...n}=m;r.push(...u.anyOf.map((e=>main_sampleFromSchemaGeneric(bo(e,n,t),t,void 0,a))))}else if(Array.isArray(u.oneOf)){const{oneOf:e,...n}=m;r.push(...u.oneOf.map((e=>main_sampleFromSchemaGeneric(bo(e,n,t),t,void 0,a))))}else{if(!(!a||a&&c.wrapped))return main_sampleFromSchemaGeneric(u,t,void 0,a);r.push(main_sampleFromSchemaGeneric(u,t,void 0,a))}if(isJSONSchemaObject(m))if(a&&(m.xml=m.xml||e.xml||{},m.xml.name=m.xml.name||c.name),Array.isArray(m.anyOf)){const{anyOf:e,...n}=m;r.push(...m.anyOf.map((e=>main_sampleFromSchemaGeneric(bo(e,n,t),t,void 0,a))))}else if(Array.isArray(m.oneOf)){const{oneOf:e,...n}=m;r.push(...m.oneOf.map((e=>main_sampleFromSchemaGeneric(bo(e,n,t),t,void 0,a))))}else{if(!(!a||a&&c.wrapped))return main_sampleFromSchemaGeneric(m,t,void 0,a);r.push(main_sampleFromSchemaGeneric(m,t,void 0,a))}return r=Eo.array(e,{sample:r}),a&&c.wrapped?(_[y]=r,kt()(l)||_[y].push({_attr:l}),_):r}if("object"===d){for(let e in v)Object.hasOwn(v,e)&&(v[e]?.deprecated||v[e]?.readOnly&&!h||v[e]?.writeOnly&&!g||w(e));if(a&&l&&_[y].push({_attr:l}),hasExceededMaxProperties())return _;if(predicates_isBooleanJSONSchema(p)&&p)a?_[y].push({additionalProp:"Anything can be here"}):_.additionalProp1={},b++;else if(isJSONSchemaObject(p)){const r=p,n=main_sampleFromSchemaGeneric(r,t,void 0,a);if(a&&"string"==typeof r?.xml?.name&&"notagname"!==r?.xml?.name)_[y].push(n);else{const t=r?.["x-additionalPropertiesName"]||"additionalProp",s=Number.isInteger(e.minProperties)&&e.minProperties>0&&b<e.minProperties?e.minProperties-b:3;for(let e=1;e<=s;e++){if(hasExceededMaxProperties())return _;if(a){const r={};r[t+e]=n.notagname,_[y].push(r)}else _[t+e]=n;b++}}}return _}let C;if(void 0!==e.const)C=e.const;else if(e&&Array.isArray(e.enum))C=pick(normalizeArray(e.enum));else{const r=isJSONSchemaObject(e.contentSchema)?main_sampleFromSchemaGeneric(e.contentSchema,t,void 0,a):void 0;C=Eo[d](e,{sample:r})}return a?(_[y]=kt()(l)?C:[{_attr:l},C],_):C},main_createXMLExample=(e,t,r)=>{const a=main_sampleFromSchemaGeneric(e,t,r,!0);if(a)return"string"==typeof a?a:Ct()(a,{declaration:!0,indent:"\t"})},main_sampleFromSchema=(e,t,r)=>main_sampleFromSchemaGeneric(e,t,r,!1),main_resolver=(e,t,r)=>[e,JSON.stringify(t),JSON.stringify(r)],Co=utils_memoizeN(main_createXMLExample,main_resolver),xo=utils_memoizeN(main_sampleFromSchema,main_resolver);const Oo=new class OptionRegistry extends Zs{#e={};data={...this.#e};get defaults(){return{...this.#e}}},api_optionAPI=(e,t)=>(void 0!==t&&Oo.register(e,t),Oo.get(e)),No=[{when:/json/,shouldStringifyTypes:["string"]}],ko=["object"],fn_get_json_sample_schema=e=>(t,r,a,n)=>{const{fn:s}=e(),o=s.jsonSchema202012.memoizedSampleFromSchema(t,r,n),l=typeof o,c=No.reduce(((e,t)=>t.when.test(a)?[...e,...t.shouldStringifyTypes]:e),ko);return X()(c,(e=>e===l))?JSON.stringify(o,null,2):o},fn_get_yaml_sample_schema=e=>(t,r,a,n)=>{const{fn:s}=e(),o=s.jsonSchema202012.getJsonSampleSchema(t,r,a,n);let l;try{l=qe().dump(qe().load(o),{lineWidth:-1},{schema:je.JSON_SCHEMA}),"\n"===l[l.length-1]&&(l=l.slice(0,l.length-1))}catch(e){return console.error(e),"error: could not generate yaml example"}return l.replace(/\t/g,"  ")},fn_get_xml_sample_schema=e=>(t,r,a)=>{const{fn:n}=e();if(t&&!t.xml&&(t.xml={}),t&&!t.xml.name){if(!t.$$ref&&(t.type||t.items||t.properties||t.additionalProperties))return'<?xml version="1.0" encoding="UTF-8"?>\n\x3c!-- XML example cannot be generated; root element name is undefined --\x3e';if(t.$$ref){let e=t.$$ref.match(/\S*\/(\S+)$/);t.xml.name=e[1]}}return n.jsonSchema202012.memoizedCreateXMLExample(t,r,a)},fn_get_sample_schema=e=>(t,r="",a={},n=void 0)=>{const{fn:s}=e();return"function"==typeof t?.toJS&&(t=t.toJS()),"function"==typeof n?.toJS&&(n=n.toJS()),/xml/.test(r)?s.jsonSchema202012.getXmlSampleSchema(t,a,n):/(yaml|yml)/.test(r)?s.jsonSchema202012.getYamlSampleSchema(t,a,r,n):s.jsonSchema202012.getJsonSampleSchema(t,a,r,n)},json_schema_2020_12_samples=({getSystem:e})=>{const t=fn_get_json_sample_schema(e),r=fn_get_yaml_sample_schema(e),a=fn_get_xml_sample_schema(e),n=fn_get_sample_schema(e);return{fn:{jsonSchema202012:{sampleFromSchema:main_sampleFromSchema,sampleFromSchemaGeneric:main_sampleFromSchemaGeneric,sampleOptionAPI:api_optionAPI,sampleEncoderAPI:po,sampleFormatAPI:to,sampleMediaTypeAPI:So,createXMLExample:main_createXMLExample,memoizedSampleFromSchema:xo,memoizedCreateXMLExample:Co,getJsonSampleSchema:t,getYamlSampleSchema:r,getXmlSampleSchema:a,getSampleSchema:n,mergeJsonSchema:bo,foldType}}}};function PresetApis(){return[base,oas3,json_schema_2020_12,json_schema_2020_12_samples,oas31]}const inline_plugin=e=>()=>({fn:e.fn,components:e.components}),factorization_system=e=>{const t=j()({layout:{layout:e.layout,filter:e.filter},spec:{spec:"",url:e.url},requestSnippets:e.requestSnippets},e.initialState);if(e.initialState)for(const[r,a]of Object.entries(e.initialState))void 0===a&&delete t[r];return{system:{configs:e.configs},plugins:e.presets,state:t}},query=()=>e=>{const t=e.queryConfigEnabled?(()=>{const e=new URLSearchParams(K.location.search);return Object.fromEntries(e)})():{};return Object.entries(t).reduce(((e,[t,r])=>("config"===t?e.configUrl=r:"urls.primaryName"===t?e[t]=r:e=br()(e,t,r),e)),{})},sources_url=({url:e,system:t})=>async r=>{if(!e)return{};if("function"!=typeof t.configsActions?.getConfigByUrl)return{};const a=(()=>{const e={};return e.promise=new Promise(((t,r)=>{e.resolve=t,e.reject=r})),e})();return t.configsActions.getConfigByUrl({url:e,loadRemoteConfig:!0,requestInterceptor:r.requestInterceptor,responseInterceptor:r.responseInterceptor},(e=>{a.resolve(e)})),a.promise},runtime=()=>()=>{const e={};return globalThis.location&&(e.oauth2RedirectUrl=`${globalThis.location.protocol}//${globalThis.location.host}${globalThis.location.pathname.substring(0,globalThis.location.pathname.lastIndexOf("/"))}/oauth2-redirect.html`),e},Ao=Object.freeze({dom_id:null,domNode:null,spec:{},url:"",urls:null,configUrl:null,layout:"BaseLayout",docExpansion:"list",maxDisplayedTags:-1,filter:!1,validatorUrl:"https://validator.swagger.io/validator",oauth2RedirectUrl:void 0,persistAuthorization:!1,configs:{},displayOperationId:!1,displayRequestDuration:!1,deepLinking:!1,tryItOutEnabled:!1,requestInterceptor:e=>(e.curlOptions=[],e),responseInterceptor:e=>e,showMutatedRequest:!0,defaultModelRendering:"example",defaultModelExpandDepth:1,defaultModelsExpandDepth:1,showExtensions:!1,showCommonExtensions:!1,withCredentials:!1,requestSnippetsEnabled:!1,requestSnippets:{generators:{curl_bash:{title:"cURL (bash)",syntax:"bash"},curl_powershell:{title:"cURL (PowerShell)",syntax:"powershell"},curl_cmd:{title:"cURL (CMD)",syntax:"bash"}},defaultExpanded:!0,languages:null},supportedSubmitMethods:["get","put","post","delete","options","head","patch","trace"],queryConfigEnabled:!1,presets:[PresetApis],plugins:[],initialState:{},fn:{},components:{},syntaxHighlight:{activated:!0,theme:"agate"},operationsSorter:null,tagsSorter:null,onComplete:null,modelPropertyMacro:null,parameterMacro:null,fileUploadMediaTypes:["application/octet-stream","image/","audio/","video/"],uncaughtExceptionHandler:null}),Io=require("lodash/has");var jo=__webpack_require__.n(Io);const qo=require("lodash/fp/set");var Po=__webpack_require__.n(qo);const type_casters_array=(e,t=[])=>Array.isArray(e)?e:t,type_casters_boolean=(e,t=!1)=>!0===e||"true"===e||1===e||"1"===e||!1!==e&&"false"!==e&&0!==e&&"0"!==e&&t,dom_node=e=>null===e||"null"===e?null:e,type_casters_filter=e=>{const t=String(e);return type_casters_boolean(e,t)},type_casters_function=(e,t)=>"function"==typeof e?e:t,nullable_array=e=>Array.isArray(e)?e:null,nullable_function=e=>"function"==typeof e?e:null,nullable_string=e=>null===e||"null"===e?null:String(e),type_casters_number=(e,t=-1)=>{const r=parseInt(e,10);return Number.isNaN(r)?t:r},type_casters_object=(e,t={})=>wt()(e)?e:t,sorter=e=>"function"==typeof e||"string"==typeof e?e:null,type_casters_string=e=>String(e),syntax_highlight=(e,t)=>wt()(e)?e:!1===e||"false"===e||0===e||"0"===e?{activated:!1}:t,undefined_string=e=>void 0===e||"undefined"===e?void 0:String(e),Mo={components:{typeCaster:type_casters_object},configs:{typeCaster:type_casters_object},configUrl:{typeCaster:nullable_string},deepLinking:{typeCaster:type_casters_boolean,defaultValue:Ao.deepLinking},defaultModelExpandDepth:{typeCaster:type_casters_number,defaultValue:Ao.defaultModelExpandDepth},defaultModelRendering:{typeCaster:type_casters_string},defaultModelsExpandDepth:{typeCaster:type_casters_number,defaultValue:Ao.defaultModelsExpandDepth},displayOperationId:{typeCaster:type_casters_boolean,defaultValue:Ao.displayOperationId},displayRequestDuration:{typeCaster:type_casters_boolean,defaultValue:Ao.displayRequestDuration},docExpansion:{typeCaster:type_casters_string},dom_id:{typeCaster:nullable_string},domNode:{typeCaster:dom_node},fileUploadMediaTypes:{typeCaster:type_casters_array,defaultValue:Ao.fileUploadMediaTypes},filter:{typeCaster:type_casters_filter},fn:{typeCaster:type_casters_object},initialState:{typeCaster:type_casters_object},layout:{typeCaster:type_casters_string},maxDisplayedTags:{typeCaster:type_casters_number,defaultValue:Ao.maxDisplayedTags},modelPropertyMacro:{typeCaster:nullable_function},oauth2RedirectUrl:{typeCaster:undefined_string},onComplete:{typeCaster:nullable_function},operationsSorter:{typeCaster:sorter},paramaterMacro:{typeCaster:nullable_function},persistAuthorization:{typeCaster:type_casters_boolean,defaultValue:Ao.persistAuthorization},plugins:{typeCaster:type_casters_array,defaultValue:Ao.plugins},presets:{typeCaster:type_casters_array,defaultValue:Ao.presets},requestInterceptor:{typeCaster:type_casters_function,defaultValue:Ao.requestInterceptor},requestSnippets:{typeCaster:type_casters_object,defaultValue:Ao.requestSnippets},requestSnippetsEnabled:{typeCaster:type_casters_boolean,defaultValue:Ao.requestSnippetsEnabled},responseInterceptor:{typeCaster:type_casters_function,defaultValue:Ao.responseInterceptor},showCommonExtensions:{typeCaster:type_casters_boolean,defaultValue:Ao.showCommonExtensions},showExtensions:{typeCaster:type_casters_boolean,defaultValue:Ao.showExtensions},showMutatedRequest:{typeCaster:type_casters_boolean,defaultValue:Ao.showMutatedRequest},spec:{typeCaster:type_casters_object,defaultValue:Ao.spec},supportedSubmitMethods:{typeCaster:type_casters_array,defaultValue:Ao.supportedSubmitMethods},syntaxHighlight:{typeCaster:syntax_highlight,defaultValue:Ao.syntaxHighlight},"syntaxHighlight.activated":{typeCaster:type_casters_boolean,defaultValue:Ao.syntaxHighlight.activated},"syntaxHighlight.theme":{typeCaster:type_casters_string},tagsSorter:{typeCaster:sorter},tryItOutEnabled:{typeCaster:type_casters_boolean,defaultValue:Ao.tryItOutEnabled},url:{typeCaster:type_casters_string},urls:{typeCaster:nullable_array},"urls.primaryName":{typeCaster:type_casters_string},validatorUrl:{typeCaster:nullable_string},withCredentials:{typeCaster:type_casters_boolean,defaultValue:Ao.withCredentials},uncaughtExceptionHandler:{typeCaster:nullable_function}},type_cast=e=>Object.entries(Mo).reduce(((e,[t,{typeCaster:r,defaultValue:a}])=>{if(jo()(e,t)){const n=r(Fe()(e,t),a);e=Po()(t,n,e)}return e}),{...e}),config_merge=(e,...t)=>{let r=Symbol.for("domNode"),a=Symbol.for("primaryName");const n=[];for(const e of t){const t={...e};Object.hasOwn(t,"domNode")&&(r=t.domNode,delete t.domNode),Object.hasOwn(t,"urls.primaryName")?(a=t["urls.primaryName"],delete t["urls.primaryName"]):Array.isArray(t.urls)&&Object.hasOwn(t.urls,"primaryName")&&(a=t.urls.primaryName,delete t.urls.primaryName),n.push(t)}const s=j()(e,...n);return r!==Symbol.for("domNode")&&(s.domNode=r),a!==Symbol.for("primaryName")&&Array.isArray(s.urls)&&(s.urls.primaryName=a),type_cast(s)};function SwaggerUI(e){const t=query()(e),r=runtime()(),a=SwaggerUI.config.merge({},SwaggerUI.config.defaults,r,e,t),n=factorization_system(a),s=inline_plugin(a),o=new Store(n);o.register([a.plugins,s]);const l=o.getSystem(),persistConfigs=e=>{o.setConfigs(e),l.configsActions.loaded()},updateSpec=e=>{!t.url&&"object"==typeof e.spec&&Object.keys(e.spec).length>0?(l.specActions.updateUrl(""),l.specActions.updateLoadingStatus("success"),l.specActions.updateSpec(JSON.stringify(e.spec))):"function"==typeof l.specActions.download&&e.url&&!e.urls&&(l.specActions.updateUrl(e.url),l.specActions.download(e.url))},render=e=>{if(e.domNode)l.render(e.domNode,"App");else if(e.dom_id){const t=document.querySelector(e.dom_id);l.render(t,"App")}else null===e.dom_id||null===e.domNode||console.error("Skipped rendering: no `dom_id` or `domNode` was specified")};return a.configUrl?((async()=>{const{configUrl:e}=a,r=await sources_url({url:e,system:l})(a),n=SwaggerUI.config.merge({},a,r,t);persistConfigs(n),null!==r&&updateSpec(n),render(n)})(),l):(persistConfigs(a),updateSpec(a),render(a),l)}SwaggerUI.System=Store,SwaggerUI.config={defaults:Ao,merge:config_merge,typeCast:type_cast,typeCastMappings:Mo},SwaggerUI.presets={base,apis:PresetApis},SwaggerUI.plugins={Auth:auth,Configs:configsPlugin,DeepLining:deep_linking,Err:err,Filter:filter,Icons:icons,JSONSchema5:json_schema_5,JSONSchema5Samples:json_schema_5_samples,JSONSchema202012:json_schema_2020_12,JSONSchema202012Samples:json_schema_2020_12_samples,Layout:plugins_layout,Logs:logs,OpenAPI30:oas3,OpenAPI31:oas3,OnComplete:on_complete,RequestSnippets:plugins_request_snippets,Spec:plugins_spec,SwaggerClient:swagger_client,Util:util,View:view,ViewLegacy:view_legacy,DownloadUrl:downloadUrlPlugin,SyntaxHighlighting:syntax_highlighting,Versions:versions,SafeRender:safe_render};const To=SwaggerUI;return r=r.default})()));
//# sourceMappingURL=swagger-ui.js.map