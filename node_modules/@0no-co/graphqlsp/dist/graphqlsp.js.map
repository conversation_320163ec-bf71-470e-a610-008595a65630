{"version": 3, "file": "graphqlsp.js", "sources": ["../src/graphql/getSchema.ts", "../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/interface/autocompleteUtils.js", "../../../node_modules/.pnpm/vscode-languageserver-types@3.17.2/node_modules/vscode-languageserver-types/lib/esm/main.js", "../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/types.js", "../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/parser/types.js", "../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/interface/getAutocompleteSuggestions.js", "../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/interface/getHoverInformation.js", "../src/ast/cursor.ts", "../src/ast/token.ts", "../src/graphql/getFragmentSpreadSuggestions.ts", "../src/autoComplete.ts", "../src/index.ts", "../src/quickInfo.ts"], "sourcesContent": ["import type { Stats, PathLike } from 'node:fs';\nimport fs from 'node:fs/promises';\nimport path from 'path';\n\nimport type { IntrospectionQuery } from 'graphql';\n\nimport {\n  type SchemaLoaderResult,\n  type SchemaRef as _SchemaRef,\n  type GraphQLSPConfig,\n  loadRef,\n  minifyIntrospection,\n  outputIntrospectionFile,\n  resolveTypeScriptRootDir,\n} from '@gql.tada/internal';\n\nimport { ts } from '../ts';\nimport { Logger } from '../index';\n\nconst statFile = (\n  file: PathLike,\n  predicate: (stat: Stats) => boolean\n): Promise<boolean> => {\n  return fs\n    .stat(file)\n    .then(predicate)\n    .catch(() => false);\n};\n\nconst touchFile = async (file: PathLike): Promise<void> => {\n  try {\n    const now = new Date();\n    await fs.utimes(file, now, now);\n  } catch (_error) {}\n};\n\n/** Writes a file to a swapfile then moves it into place to prevent excess change events. */\nexport const swapWrite = async (\n  target: PathLike,\n  contents: string\n): Promise<void> => {\n  if (!(await statFile(target, stat => stat.isFile()))) {\n    // If the file doesn't exist, we can write directly, and not\n    // try-catch so the error falls through\n    await fs.writeFile(target, contents);\n  } else {\n    // If the file exists, we write to a swap-file, then rename (i.e. move)\n    // the file into place. No try-catch around `writeFile` for proper\n    // directory/permission errors\n    const tempTarget = target + '.tmp';\n    await fs.writeFile(tempTarget, contents);\n    try {\n      await fs.rename(tempTarget, target);\n    } catch (error) {\n      await fs.unlink(tempTarget);\n      throw error;\n    } finally {\n      // When we move the file into place, we also update its access and\n      // modification time manually, in case the rename doesn't trigger\n      // a change event\n      await touchFile(target);\n    }\n  }\n};\n\nasync function saveTadaIntrospection(\n  introspection: IntrospectionQuery,\n  tadaOutputLocation: string,\n  disablePreprocessing: boolean,\n  logger: Logger\n) {\n  const minified = minifyIntrospection(introspection);\n  const contents = outputIntrospectionFile(minified, {\n    fileType: tadaOutputLocation,\n    shouldPreprocess: !disablePreprocessing,\n  });\n\n  let output = tadaOutputLocation;\n  if (await statFile(output, stat => stat.isDirectory())) {\n    output = path.join(output, 'introspection.d.ts');\n  } else if (!(await statFile(output, p => !!p))) {\n    await fs.mkdir(path.dirname(output), { recursive: true });\n    if (await statFile(output, stat => stat.isDirectory())) {\n      output = path.join(output, 'introspection.d.ts');\n    }\n  }\n\n  try {\n    await swapWrite(output, contents);\n    logger(`Introspection saved to path @ ${output}`);\n  } catch (error) {\n    logger(`Failed to write introspection @ ${error}`);\n  }\n}\n\nexport type SchemaRef = _SchemaRef<SchemaLoaderResult | null>;\n\nexport const loadSchema = (\n  // TODO: abstract info away\n  info: ts.server.PluginCreateInfo,\n  origin: GraphQLSPConfig,\n  logger: Logger\n): _SchemaRef<SchemaLoaderResult | null> => {\n  const ref = loadRef(origin);\n\n  (async () => {\n    const rootPath =\n      (await resolveTypeScriptRootDir(info.project.getProjectName())) ||\n      path.dirname(info.project.getProjectName());\n\n    const tadaDisablePreprocessing =\n      info.config.tadaDisablePreprocessing ?? false;\n    const tadaOutputLocation =\n      info.config.tadaOutputLocation &&\n      path.resolve(rootPath, info.config.tadaOutputLocation);\n\n    logger('Got root-directory to resolve schema from: ' + rootPath);\n    logger('Resolving schema from \"schema\" config: ' + JSON.stringify(origin));\n\n    try {\n      logger(`Loading schema...`);\n      await ref.load({ rootPath });\n    } catch (error) {\n      logger(`Failed to load schema: ${error}`);\n    }\n\n    if (ref.current) {\n      if (ref.current && ref.current.tadaOutputLocation !== undefined) {\n        saveTadaIntrospection(\n          ref.current.introspection,\n          tadaOutputLocation,\n          tadaDisablePreprocessing,\n          logger\n        );\n      }\n    } else if (ref.multi) {\n      Object.values(ref.multi).forEach(value => {\n        if (!value) return;\n\n        if (value.tadaOutputLocation) {\n          saveTadaIntrospection(\n            value.introspection,\n            path.resolve(rootPath, value.tadaOutputLocation),\n            tadaDisablePreprocessing,\n            logger\n          );\n        }\n      });\n    }\n\n    ref.autoupdate({ rootPath }, (schemaRef, value) => {\n      if (!value) return;\n\n      if (value.tadaOutputLocation) {\n        const found = schemaRef.multi\n          ? schemaRef.multi[value.name as string]\n          : schemaRef.current;\n        if (!found) return;\n        saveTadaIntrospection(\n          found.introspection,\n          path.resolve(rootPath, value.tadaOutputLocation),\n          tadaDisablePreprocessing,\n          logger\n        );\n      }\n    });\n  })();\n\n  return ref as any;\n};\n", "import { isCompositeType, SchemaMetaFieldDef, TypeMetaFieldDef, TypeNameMetaFieldDef, } from 'graphql';\nexport function getDefinitionState(tokenState) {\n    let definitionState;\n    forEachState(tokenState, (state) => {\n        switch (state.kind) {\n            case 'Query':\n            case 'ShortQuery':\n            case 'Mutation':\n            case 'Subscription':\n            case 'FragmentDefinition':\n                definitionState = state;\n                break;\n        }\n    });\n    return definitionState;\n}\nexport function getFieldDef(schema, type, fieldName) {\n    if (fieldName === SchemaMetaFieldDef.name && schema.getQueryType() === type) {\n        return SchemaMetaFieldDef;\n    }\n    if (fieldName === TypeMetaFieldDef.name && schema.getQueryType() === type) {\n        return TypeMetaFieldDef;\n    }\n    if (fieldName === TypeNameMetaFieldDef.name && isCompositeType(type)) {\n        return TypeNameMetaFieldDef;\n    }\n    if ('getFields' in type) {\n        return type.getFields()[fieldName];\n    }\n    return null;\n}\nexport function forEachState(stack, fn) {\n    const reverseStateStack = [];\n    let state = stack;\n    while (state === null || state === void 0 ? void 0 : state.kind) {\n        reverseStateStack.push(state);\n        state = state.prevState;\n    }\n    for (let i = reverseStateStack.length - 1; i >= 0; i--) {\n        fn(reverseStateStack[i]);\n    }\n}\nexport function objectValues(object) {\n    const keys = Object.keys(object);\n    const len = keys.length;\n    const values = new Array(len);\n    for (let i = 0; i < len; ++i) {\n        values[i] = object[keys[i]];\n    }\n    return values;\n}\nexport function hintList(token, list) {\n    return filterAndSortList(list, normalizeText(token.string));\n}\nfunction filterAndSortList(list, text) {\n    if (!text) {\n        return filterNonEmpty(list, entry => !entry.isDeprecated);\n    }\n    const byProximity = list.map(entry => ({\n        proximity: getProximity(normalizeText(entry.label), text),\n        entry,\n    }));\n    return filterNonEmpty(filterNonEmpty(byProximity, pair => pair.proximity <= 2), pair => !pair.entry.isDeprecated)\n        .sort((a, b) => (a.entry.isDeprecated ? 1 : 0) - (b.entry.isDeprecated ? 1 : 0) ||\n        a.proximity - b.proximity ||\n        a.entry.label.length - b.entry.label.length)\n        .map(pair => pair.entry);\n}\nfunction filterNonEmpty(array, predicate) {\n    const filtered = array.filter(predicate);\n    return filtered.length === 0 ? array : filtered;\n}\nfunction normalizeText(text) {\n    return text.toLowerCase().replaceAll(/\\W/g, '');\n}\nfunction getProximity(suggestion, text) {\n    let proximity = lexicalDistance(text, suggestion);\n    if (suggestion.length > text.length) {\n        proximity -= suggestion.length - text.length - 1;\n        proximity += suggestion.indexOf(text) === 0 ? 0 : 0.5;\n    }\n    return proximity;\n}\nfunction lexicalDistance(a, b) {\n    let i;\n    let j;\n    const d = [];\n    const aLength = a.length;\n    const bLength = b.length;\n    for (i = 0; i <= aLength; i++) {\n        d[i] = [i];\n    }\n    for (j = 1; j <= bLength; j++) {\n        d[0][j] = j;\n    }\n    for (i = 1; i <= aLength; i++) {\n        for (j = 1; j <= bLength; j++) {\n            const cost = a[i - 1] === b[j - 1] ? 0 : 1;\n            d[i][j] = Math.min(d[i - 1][j] + 1, d[i][j - 1] + 1, d[i - 1][j - 1] + cost);\n            if (i > 1 && j > 1 && a[i - 1] === b[j - 2] && a[i - 2] === b[j - 1]) {\n                d[i][j] = Math.min(d[i][j], d[i - 2][j - 2] + cost);\n            }\n        }\n    }\n    return d[aLength][bLength];\n}\n//# sourceMappingURL=autocompleteUtils.js.map", "/* --------------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See License.txt in the project root for license information.\n * ------------------------------------------------------------------------------------------ */\n'use strict';\nexport var DocumentUri;\n(function (DocumentUri) {\n    function is(value) {\n        return typeof value === 'string';\n    }\n    DocumentUri.is = is;\n})(DocumentUri || (DocumentUri = {}));\nexport var URI;\n(function (URI) {\n    function is(value) {\n        return typeof value === 'string';\n    }\n    URI.is = is;\n})(URI || (URI = {}));\nexport var integer;\n(function (integer) {\n    integer.MIN_VALUE = -**********;\n    integer.MAX_VALUE = **********;\n    function is(value) {\n        return typeof value === 'number' && integer.MIN_VALUE <= value && value <= integer.MAX_VALUE;\n    }\n    integer.is = is;\n})(integer || (integer = {}));\nexport var uinteger;\n(function (uinteger) {\n    uinteger.MIN_VALUE = 0;\n    uinteger.MAX_VALUE = **********;\n    function is(value) {\n        return typeof value === 'number' && uinteger.MIN_VALUE <= value && value <= uinteger.MAX_VALUE;\n    }\n    uinteger.is = is;\n})(uinteger || (uinteger = {}));\n/**\n * The Position namespace provides helper functions to work with\n * [Position](#Position) literals.\n */\nexport var Position;\n(function (Position) {\n    /**\n     * Creates a new Position literal from the given line and character.\n     * @param line The position's line.\n     * @param character The position's character.\n     */\n    function create(line, character) {\n        if (line === Number.MAX_VALUE) {\n            line = uinteger.MAX_VALUE;\n        }\n        if (character === Number.MAX_VALUE) {\n            character = uinteger.MAX_VALUE;\n        }\n        return { line: line, character: character };\n    }\n    Position.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Position](#Position) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Is.uinteger(candidate.line) && Is.uinteger(candidate.character);\n    }\n    Position.is = is;\n})(Position || (Position = {}));\n/**\n * The Range namespace provides helper functions to work with\n * [Range](#Range) literals.\n */\nexport var Range;\n(function (Range) {\n    function create(one, two, three, four) {\n        if (Is.uinteger(one) && Is.uinteger(two) && Is.uinteger(three) && Is.uinteger(four)) {\n            return { start: Position.create(one, two), end: Position.create(three, four) };\n        }\n        else if (Position.is(one) && Position.is(two)) {\n            return { start: one, end: two };\n        }\n        else {\n            throw new Error(\"Range#create called with invalid arguments[\".concat(one, \", \").concat(two, \", \").concat(three, \", \").concat(four, \"]\"));\n        }\n    }\n    Range.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Range](#Range) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Position.is(candidate.start) && Position.is(candidate.end);\n    }\n    Range.is = is;\n})(Range || (Range = {}));\n/**\n * The Location namespace provides helper functions to work with\n * [Location](#Location) literals.\n */\nexport var Location;\n(function (Location) {\n    /**\n     * Creates a Location literal.\n     * @param uri The location's uri.\n     * @param range The location's range.\n     */\n    function create(uri, range) {\n        return { uri: uri, range: range };\n    }\n    Location.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Location](#Location) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Range.is(candidate.range) && (Is.string(candidate.uri) || Is.undefined(candidate.uri));\n    }\n    Location.is = is;\n})(Location || (Location = {}));\n/**\n * The LocationLink namespace provides helper functions to work with\n * [LocationLink](#LocationLink) literals.\n */\nexport var LocationLink;\n(function (LocationLink) {\n    /**\n     * Creates a LocationLink literal.\n     * @param targetUri The definition's uri.\n     * @param targetRange The full range of the definition.\n     * @param targetSelectionRange The span of the symbol definition at the target.\n     * @param originSelectionRange The span of the symbol being defined in the originating source file.\n     */\n    function create(targetUri, targetRange, targetSelectionRange, originSelectionRange) {\n        return { targetUri: targetUri, targetRange: targetRange, targetSelectionRange: targetSelectionRange, originSelectionRange: originSelectionRange };\n    }\n    LocationLink.create = create;\n    /**\n     * Checks whether the given literal conforms to the [LocationLink](#LocationLink) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Range.is(candidate.targetRange) && Is.string(candidate.targetUri)\n            && Range.is(candidate.targetSelectionRange)\n            && (Range.is(candidate.originSelectionRange) || Is.undefined(candidate.originSelectionRange));\n    }\n    LocationLink.is = is;\n})(LocationLink || (LocationLink = {}));\n/**\n * The Color namespace provides helper functions to work with\n * [Color](#Color) literals.\n */\nexport var Color;\n(function (Color) {\n    /**\n     * Creates a new Color literal.\n     */\n    function create(red, green, blue, alpha) {\n        return {\n            red: red,\n            green: green,\n            blue: blue,\n            alpha: alpha,\n        };\n    }\n    Color.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Color](#Color) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Is.numberRange(candidate.red, 0, 1)\n            && Is.numberRange(candidate.green, 0, 1)\n            && Is.numberRange(candidate.blue, 0, 1)\n            && Is.numberRange(candidate.alpha, 0, 1);\n    }\n    Color.is = is;\n})(Color || (Color = {}));\n/**\n * The ColorInformation namespace provides helper functions to work with\n * [ColorInformation](#ColorInformation) literals.\n */\nexport var ColorInformation;\n(function (ColorInformation) {\n    /**\n     * Creates a new ColorInformation literal.\n     */\n    function create(range, color) {\n        return {\n            range: range,\n            color: color,\n        };\n    }\n    ColorInformation.create = create;\n    /**\n     * Checks whether the given literal conforms to the [ColorInformation](#ColorInformation) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Range.is(candidate.range) && Color.is(candidate.color);\n    }\n    ColorInformation.is = is;\n})(ColorInformation || (ColorInformation = {}));\n/**\n * The Color namespace provides helper functions to work with\n * [ColorPresentation](#ColorPresentation) literals.\n */\nexport var ColorPresentation;\n(function (ColorPresentation) {\n    /**\n     * Creates a new ColorInformation literal.\n     */\n    function create(label, textEdit, additionalTextEdits) {\n        return {\n            label: label,\n            textEdit: textEdit,\n            additionalTextEdits: additionalTextEdits,\n        };\n    }\n    ColorPresentation.create = create;\n    /**\n     * Checks whether the given literal conforms to the [ColorInformation](#ColorInformation) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Is.string(candidate.label)\n            && (Is.undefined(candidate.textEdit) || TextEdit.is(candidate))\n            && (Is.undefined(candidate.additionalTextEdits) || Is.typedArray(candidate.additionalTextEdits, TextEdit.is));\n    }\n    ColorPresentation.is = is;\n})(ColorPresentation || (ColorPresentation = {}));\n/**\n * A set of predefined range kinds.\n */\nexport var FoldingRangeKind;\n(function (FoldingRangeKind) {\n    /**\n     * Folding range for a comment\n     */\n    FoldingRangeKind.Comment = 'comment';\n    /**\n     * Folding range for an import or include\n     */\n    FoldingRangeKind.Imports = 'imports';\n    /**\n     * Folding range for a region (e.g. `#region`)\n     */\n    FoldingRangeKind.Region = 'region';\n})(FoldingRangeKind || (FoldingRangeKind = {}));\n/**\n * The folding range namespace provides helper functions to work with\n * [FoldingRange](#FoldingRange) literals.\n */\nexport var FoldingRange;\n(function (FoldingRange) {\n    /**\n     * Creates a new FoldingRange literal.\n     */\n    function create(startLine, endLine, startCharacter, endCharacter, kind, collapsedText) {\n        var result = {\n            startLine: startLine,\n            endLine: endLine\n        };\n        if (Is.defined(startCharacter)) {\n            result.startCharacter = startCharacter;\n        }\n        if (Is.defined(endCharacter)) {\n            result.endCharacter = endCharacter;\n        }\n        if (Is.defined(kind)) {\n            result.kind = kind;\n        }\n        if (Is.defined(collapsedText)) {\n            result.collapsedText = collapsedText;\n        }\n        return result;\n    }\n    FoldingRange.create = create;\n    /**\n     * Checks whether the given literal conforms to the [FoldingRange](#FoldingRange) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Is.uinteger(candidate.startLine) && Is.uinteger(candidate.startLine)\n            && (Is.undefined(candidate.startCharacter) || Is.uinteger(candidate.startCharacter))\n            && (Is.undefined(candidate.endCharacter) || Is.uinteger(candidate.endCharacter))\n            && (Is.undefined(candidate.kind) || Is.string(candidate.kind));\n    }\n    FoldingRange.is = is;\n})(FoldingRange || (FoldingRange = {}));\n/**\n * The DiagnosticRelatedInformation namespace provides helper functions to work with\n * [DiagnosticRelatedInformation](#DiagnosticRelatedInformation) literals.\n */\nexport var DiagnosticRelatedInformation;\n(function (DiagnosticRelatedInformation) {\n    /**\n     * Creates a new DiagnosticRelatedInformation literal.\n     */\n    function create(location, message) {\n        return {\n            location: location,\n            message: message\n        };\n    }\n    DiagnosticRelatedInformation.create = create;\n    /**\n     * Checks whether the given literal conforms to the [DiagnosticRelatedInformation](#DiagnosticRelatedInformation) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Location.is(candidate.location) && Is.string(candidate.message);\n    }\n    DiagnosticRelatedInformation.is = is;\n})(DiagnosticRelatedInformation || (DiagnosticRelatedInformation = {}));\n/**\n * The diagnostic's severity.\n */\nexport var DiagnosticSeverity;\n(function (DiagnosticSeverity) {\n    /**\n     * Reports an error.\n     */\n    DiagnosticSeverity.Error = 1;\n    /**\n     * Reports a warning.\n     */\n    DiagnosticSeverity.Warning = 2;\n    /**\n     * Reports an information.\n     */\n    DiagnosticSeverity.Information = 3;\n    /**\n     * Reports a hint.\n     */\n    DiagnosticSeverity.Hint = 4;\n})(DiagnosticSeverity || (DiagnosticSeverity = {}));\n/**\n * The diagnostic tags.\n *\n * @since 3.15.0\n */\nexport var DiagnosticTag;\n(function (DiagnosticTag) {\n    /**\n     * Unused or unnecessary code.\n     *\n     * Clients are allowed to render diagnostics with this tag faded out instead of having\n     * an error squiggle.\n     */\n    DiagnosticTag.Unnecessary = 1;\n    /**\n     * Deprecated or obsolete code.\n     *\n     * Clients are allowed to rendered diagnostics with this tag strike through.\n     */\n    DiagnosticTag.Deprecated = 2;\n})(DiagnosticTag || (DiagnosticTag = {}));\n/**\n * The CodeDescription namespace provides functions to deal with descriptions for diagnostic codes.\n *\n * @since 3.16.0\n */\nexport var CodeDescription;\n(function (CodeDescription) {\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Is.string(candidate.href);\n    }\n    CodeDescription.is = is;\n})(CodeDescription || (CodeDescription = {}));\n/**\n * The Diagnostic namespace provides helper functions to work with\n * [Diagnostic](#Diagnostic) literals.\n */\nexport var Diagnostic;\n(function (Diagnostic) {\n    /**\n     * Creates a new Diagnostic literal.\n     */\n    function create(range, message, severity, code, source, relatedInformation) {\n        var result = { range: range, message: message };\n        if (Is.defined(severity)) {\n            result.severity = severity;\n        }\n        if (Is.defined(code)) {\n            result.code = code;\n        }\n        if (Is.defined(source)) {\n            result.source = source;\n        }\n        if (Is.defined(relatedInformation)) {\n            result.relatedInformation = relatedInformation;\n        }\n        return result;\n    }\n    Diagnostic.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Diagnostic](#Diagnostic) interface.\n     */\n    function is(value) {\n        var _a;\n        var candidate = value;\n        return Is.defined(candidate)\n            && Range.is(candidate.range)\n            && Is.string(candidate.message)\n            && (Is.number(candidate.severity) || Is.undefined(candidate.severity))\n            && (Is.integer(candidate.code) || Is.string(candidate.code) || Is.undefined(candidate.code))\n            && (Is.undefined(candidate.codeDescription) || (Is.string((_a = candidate.codeDescription) === null || _a === void 0 ? void 0 : _a.href)))\n            && (Is.string(candidate.source) || Is.undefined(candidate.source))\n            && (Is.undefined(candidate.relatedInformation) || Is.typedArray(candidate.relatedInformation, DiagnosticRelatedInformation.is));\n    }\n    Diagnostic.is = is;\n})(Diagnostic || (Diagnostic = {}));\n/**\n * The Command namespace provides helper functions to work with\n * [Command](#Command) literals.\n */\nexport var Command;\n(function (Command) {\n    /**\n     * Creates a new Command literal.\n     */\n    function create(title, command) {\n        var args = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            args[_i - 2] = arguments[_i];\n        }\n        var result = { title: title, command: command };\n        if (Is.defined(args) && args.length > 0) {\n            result.arguments = args;\n        }\n        return result;\n    }\n    Command.create = create;\n    /**\n     * Checks whether the given literal conforms to the [Command](#Command) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.title) && Is.string(candidate.command);\n    }\n    Command.is = is;\n})(Command || (Command = {}));\n/**\n * The TextEdit namespace provides helper function to create replace,\n * insert and delete edits more easily.\n */\nexport var TextEdit;\n(function (TextEdit) {\n    /**\n     * Creates a replace text edit.\n     * @param range The range of text to be replaced.\n     * @param newText The new text.\n     */\n    function replace(range, newText) {\n        return { range: range, newText: newText };\n    }\n    TextEdit.replace = replace;\n    /**\n     * Creates an insert text edit.\n     * @param position The position to insert the text at.\n     * @param newText The text to be inserted.\n     */\n    function insert(position, newText) {\n        return { range: { start: position, end: position }, newText: newText };\n    }\n    TextEdit.insert = insert;\n    /**\n     * Creates a delete text edit.\n     * @param range The range of text to be deleted.\n     */\n    function del(range) {\n        return { range: range, newText: '' };\n    }\n    TextEdit.del = del;\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate)\n            && Is.string(candidate.newText)\n            && Range.is(candidate.range);\n    }\n    TextEdit.is = is;\n})(TextEdit || (TextEdit = {}));\nexport var ChangeAnnotation;\n(function (ChangeAnnotation) {\n    function create(label, needsConfirmation, description) {\n        var result = { label: label };\n        if (needsConfirmation !== undefined) {\n            result.needsConfirmation = needsConfirmation;\n        }\n        if (description !== undefined) {\n            result.description = description;\n        }\n        return result;\n    }\n    ChangeAnnotation.create = create;\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Is.string(candidate.label) &&\n            (Is.boolean(candidate.needsConfirmation) || candidate.needsConfirmation === undefined) &&\n            (Is.string(candidate.description) || candidate.description === undefined);\n    }\n    ChangeAnnotation.is = is;\n})(ChangeAnnotation || (ChangeAnnotation = {}));\nexport var ChangeAnnotationIdentifier;\n(function (ChangeAnnotationIdentifier) {\n    function is(value) {\n        var candidate = value;\n        return Is.string(candidate);\n    }\n    ChangeAnnotationIdentifier.is = is;\n})(ChangeAnnotationIdentifier || (ChangeAnnotationIdentifier = {}));\nexport var AnnotatedTextEdit;\n(function (AnnotatedTextEdit) {\n    /**\n     * Creates an annotated replace text edit.\n     *\n     * @param range The range of text to be replaced.\n     * @param newText The new text.\n     * @param annotation The annotation.\n     */\n    function replace(range, newText, annotation) {\n        return { range: range, newText: newText, annotationId: annotation };\n    }\n    AnnotatedTextEdit.replace = replace;\n    /**\n     * Creates an annotated insert text edit.\n     *\n     * @param position The position to insert the text at.\n     * @param newText The text to be inserted.\n     * @param annotation The annotation.\n     */\n    function insert(position, newText, annotation) {\n        return { range: { start: position, end: position }, newText: newText, annotationId: annotation };\n    }\n    AnnotatedTextEdit.insert = insert;\n    /**\n     * Creates an annotated delete text edit.\n     *\n     * @param range The range of text to be deleted.\n     * @param annotation The annotation.\n     */\n    function del(range, annotation) {\n        return { range: range, newText: '', annotationId: annotation };\n    }\n    AnnotatedTextEdit.del = del;\n    function is(value) {\n        var candidate = value;\n        return TextEdit.is(candidate) && (ChangeAnnotation.is(candidate.annotationId) || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    AnnotatedTextEdit.is = is;\n})(AnnotatedTextEdit || (AnnotatedTextEdit = {}));\n/**\n * The TextDocumentEdit namespace provides helper function to create\n * an edit that manipulates a text document.\n */\nexport var TextDocumentEdit;\n(function (TextDocumentEdit) {\n    /**\n     * Creates a new `TextDocumentEdit`\n     */\n    function create(textDocument, edits) {\n        return { textDocument: textDocument, edits: edits };\n    }\n    TextDocumentEdit.create = create;\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate)\n            && OptionalVersionedTextDocumentIdentifier.is(candidate.textDocument)\n            && Array.isArray(candidate.edits);\n    }\n    TextDocumentEdit.is = is;\n})(TextDocumentEdit || (TextDocumentEdit = {}));\nexport var CreateFile;\n(function (CreateFile) {\n    function create(uri, options, annotation) {\n        var result = {\n            kind: 'create',\n            uri: uri\n        };\n        if (options !== undefined && (options.overwrite !== undefined || options.ignoreIfExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    CreateFile.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate && candidate.kind === 'create' && Is.string(candidate.uri) && (candidate.options === undefined ||\n            ((candidate.options.overwrite === undefined || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === undefined || Is.boolean(candidate.options.ignoreIfExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    CreateFile.is = is;\n})(CreateFile || (CreateFile = {}));\nexport var RenameFile;\n(function (RenameFile) {\n    function create(oldUri, newUri, options, annotation) {\n        var result = {\n            kind: 'rename',\n            oldUri: oldUri,\n            newUri: newUri\n        };\n        if (options !== undefined && (options.overwrite !== undefined || options.ignoreIfExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    RenameFile.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate && candidate.kind === 'rename' && Is.string(candidate.oldUri) && Is.string(candidate.newUri) && (candidate.options === undefined ||\n            ((candidate.options.overwrite === undefined || Is.boolean(candidate.options.overwrite)) && (candidate.options.ignoreIfExists === undefined || Is.boolean(candidate.options.ignoreIfExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    RenameFile.is = is;\n})(RenameFile || (RenameFile = {}));\nexport var DeleteFile;\n(function (DeleteFile) {\n    function create(uri, options, annotation) {\n        var result = {\n            kind: 'delete',\n            uri: uri\n        };\n        if (options !== undefined && (options.recursive !== undefined || options.ignoreIfNotExists !== undefined)) {\n            result.options = options;\n        }\n        if (annotation !== undefined) {\n            result.annotationId = annotation;\n        }\n        return result;\n    }\n    DeleteFile.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate && candidate.kind === 'delete' && Is.string(candidate.uri) && (candidate.options === undefined ||\n            ((candidate.options.recursive === undefined || Is.boolean(candidate.options.recursive)) && (candidate.options.ignoreIfNotExists === undefined || Is.boolean(candidate.options.ignoreIfNotExists)))) && (candidate.annotationId === undefined || ChangeAnnotationIdentifier.is(candidate.annotationId));\n    }\n    DeleteFile.is = is;\n})(DeleteFile || (DeleteFile = {}));\nexport var WorkspaceEdit;\n(function (WorkspaceEdit) {\n    function is(value) {\n        var candidate = value;\n        return candidate &&\n            (candidate.changes !== undefined || candidate.documentChanges !== undefined) &&\n            (candidate.documentChanges === undefined || candidate.documentChanges.every(function (change) {\n                if (Is.string(change.kind)) {\n                    return CreateFile.is(change) || RenameFile.is(change) || DeleteFile.is(change);\n                }\n                else {\n                    return TextDocumentEdit.is(change);\n                }\n            }));\n    }\n    WorkspaceEdit.is = is;\n})(WorkspaceEdit || (WorkspaceEdit = {}));\nvar TextEditChangeImpl = /** @class */ (function () {\n    function TextEditChangeImpl(edits, changeAnnotations) {\n        this.edits = edits;\n        this.changeAnnotations = changeAnnotations;\n    }\n    TextEditChangeImpl.prototype.insert = function (position, newText, annotation) {\n        var edit;\n        var id;\n        if (annotation === undefined) {\n            edit = TextEdit.insert(position, newText);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.insert(position, newText, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.insert(position, newText, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    TextEditChangeImpl.prototype.replace = function (range, newText, annotation) {\n        var edit;\n        var id;\n        if (annotation === undefined) {\n            edit = TextEdit.replace(range, newText);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.replace(range, newText, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.replace(range, newText, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    TextEditChangeImpl.prototype.delete = function (range, annotation) {\n        var edit;\n        var id;\n        if (annotation === undefined) {\n            edit = TextEdit.del(range);\n        }\n        else if (ChangeAnnotationIdentifier.is(annotation)) {\n            id = annotation;\n            edit = AnnotatedTextEdit.del(range, annotation);\n        }\n        else {\n            this.assertChangeAnnotations(this.changeAnnotations);\n            id = this.changeAnnotations.manage(annotation);\n            edit = AnnotatedTextEdit.del(range, id);\n        }\n        this.edits.push(edit);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    TextEditChangeImpl.prototype.add = function (edit) {\n        this.edits.push(edit);\n    };\n    TextEditChangeImpl.prototype.all = function () {\n        return this.edits;\n    };\n    TextEditChangeImpl.prototype.clear = function () {\n        this.edits.splice(0, this.edits.length);\n    };\n    TextEditChangeImpl.prototype.assertChangeAnnotations = function (value) {\n        if (value === undefined) {\n            throw new Error(\"Text edit change is not configured to manage change annotations.\");\n        }\n    };\n    return TextEditChangeImpl;\n}());\n/**\n * A helper class\n */\nvar ChangeAnnotations = /** @class */ (function () {\n    function ChangeAnnotations(annotations) {\n        this._annotations = annotations === undefined ? Object.create(null) : annotations;\n        this._counter = 0;\n        this._size = 0;\n    }\n    ChangeAnnotations.prototype.all = function () {\n        return this._annotations;\n    };\n    Object.defineProperty(ChangeAnnotations.prototype, \"size\", {\n        get: function () {\n            return this._size;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    ChangeAnnotations.prototype.manage = function (idOrAnnotation, annotation) {\n        var id;\n        if (ChangeAnnotationIdentifier.is(idOrAnnotation)) {\n            id = idOrAnnotation;\n        }\n        else {\n            id = this.nextId();\n            annotation = idOrAnnotation;\n        }\n        if (this._annotations[id] !== undefined) {\n            throw new Error(\"Id \".concat(id, \" is already in use.\"));\n        }\n        if (annotation === undefined) {\n            throw new Error(\"No annotation provided for id \".concat(id));\n        }\n        this._annotations[id] = annotation;\n        this._size++;\n        return id;\n    };\n    ChangeAnnotations.prototype.nextId = function () {\n        this._counter++;\n        return this._counter.toString();\n    };\n    return ChangeAnnotations;\n}());\n/**\n * A workspace change helps constructing changes to a workspace.\n */\nvar WorkspaceChange = /** @class */ (function () {\n    function WorkspaceChange(workspaceEdit) {\n        var _this = this;\n        this._textEditChanges = Object.create(null);\n        if (workspaceEdit !== undefined) {\n            this._workspaceEdit = workspaceEdit;\n            if (workspaceEdit.documentChanges) {\n                this._changeAnnotations = new ChangeAnnotations(workspaceEdit.changeAnnotations);\n                workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n                workspaceEdit.documentChanges.forEach(function (change) {\n                    if (TextDocumentEdit.is(change)) {\n                        var textEditChange = new TextEditChangeImpl(change.edits, _this._changeAnnotations);\n                        _this._textEditChanges[change.textDocument.uri] = textEditChange;\n                    }\n                });\n            }\n            else if (workspaceEdit.changes) {\n                Object.keys(workspaceEdit.changes).forEach(function (key) {\n                    var textEditChange = new TextEditChangeImpl(workspaceEdit.changes[key]);\n                    _this._textEditChanges[key] = textEditChange;\n                });\n            }\n        }\n        else {\n            this._workspaceEdit = {};\n        }\n    }\n    Object.defineProperty(WorkspaceChange.prototype, \"edit\", {\n        /**\n         * Returns the underlying [WorkspaceEdit](#WorkspaceEdit) literal\n         * use to be returned from a workspace edit operation like rename.\n         */\n        get: function () {\n            this.initDocumentChanges();\n            if (this._changeAnnotations !== undefined) {\n                if (this._changeAnnotations.size === 0) {\n                    this._workspaceEdit.changeAnnotations = undefined;\n                }\n                else {\n                    this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n                }\n            }\n            return this._workspaceEdit;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    WorkspaceChange.prototype.getTextEditChange = function (key) {\n        if (OptionalVersionedTextDocumentIdentifier.is(key)) {\n            this.initDocumentChanges();\n            if (this._workspaceEdit.documentChanges === undefined) {\n                throw new Error('Workspace edit is not configured for document changes.');\n            }\n            var textDocument = { uri: key.uri, version: key.version };\n            var result = this._textEditChanges[textDocument.uri];\n            if (!result) {\n                var edits = [];\n                var textDocumentEdit = {\n                    textDocument: textDocument,\n                    edits: edits\n                };\n                this._workspaceEdit.documentChanges.push(textDocumentEdit);\n                result = new TextEditChangeImpl(edits, this._changeAnnotations);\n                this._textEditChanges[textDocument.uri] = result;\n            }\n            return result;\n        }\n        else {\n            this.initChanges();\n            if (this._workspaceEdit.changes === undefined) {\n                throw new Error('Workspace edit is not configured for normal text edit changes.');\n            }\n            var result = this._textEditChanges[key];\n            if (!result) {\n                var edits = [];\n                this._workspaceEdit.changes[key] = edits;\n                result = new TextEditChangeImpl(edits);\n                this._textEditChanges[key] = result;\n            }\n            return result;\n        }\n    };\n    WorkspaceChange.prototype.initDocumentChanges = function () {\n        if (this._workspaceEdit.documentChanges === undefined && this._workspaceEdit.changes === undefined) {\n            this._changeAnnotations = new ChangeAnnotations();\n            this._workspaceEdit.documentChanges = [];\n            this._workspaceEdit.changeAnnotations = this._changeAnnotations.all();\n        }\n    };\n    WorkspaceChange.prototype.initChanges = function () {\n        if (this._workspaceEdit.documentChanges === undefined && this._workspaceEdit.changes === undefined) {\n            this._workspaceEdit.changes = Object.create(null);\n        }\n    };\n    WorkspaceChange.prototype.createFile = function (uri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        var annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        var operation;\n        var id;\n        if (annotation === undefined) {\n            operation = CreateFile.create(uri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = CreateFile.create(uri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    WorkspaceChange.prototype.renameFile = function (oldUri, newUri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        var annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        var operation;\n        var id;\n        if (annotation === undefined) {\n            operation = RenameFile.create(oldUri, newUri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = RenameFile.create(oldUri, newUri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    WorkspaceChange.prototype.deleteFile = function (uri, optionsOrAnnotation, options) {\n        this.initDocumentChanges();\n        if (this._workspaceEdit.documentChanges === undefined) {\n            throw new Error('Workspace edit is not configured for document changes.');\n        }\n        var annotation;\n        if (ChangeAnnotation.is(optionsOrAnnotation) || ChangeAnnotationIdentifier.is(optionsOrAnnotation)) {\n            annotation = optionsOrAnnotation;\n        }\n        else {\n            options = optionsOrAnnotation;\n        }\n        var operation;\n        var id;\n        if (annotation === undefined) {\n            operation = DeleteFile.create(uri, options);\n        }\n        else {\n            id = ChangeAnnotationIdentifier.is(annotation) ? annotation : this._changeAnnotations.manage(annotation);\n            operation = DeleteFile.create(uri, options, id);\n        }\n        this._workspaceEdit.documentChanges.push(operation);\n        if (id !== undefined) {\n            return id;\n        }\n    };\n    return WorkspaceChange;\n}());\nexport { WorkspaceChange };\n/**\n * The TextDocumentIdentifier namespace provides helper functions to work with\n * [TextDocumentIdentifier](#TextDocumentIdentifier) literals.\n */\nexport var TextDocumentIdentifier;\n(function (TextDocumentIdentifier) {\n    /**\n     * Creates a new TextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     */\n    function create(uri) {\n        return { uri: uri };\n    }\n    TextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the [TextDocumentIdentifier](#TextDocumentIdentifier) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri);\n    }\n    TextDocumentIdentifier.is = is;\n})(TextDocumentIdentifier || (TextDocumentIdentifier = {}));\n/**\n * The VersionedTextDocumentIdentifier namespace provides helper functions to work with\n * [VersionedTextDocumentIdentifier](#VersionedTextDocumentIdentifier) literals.\n */\nexport var VersionedTextDocumentIdentifier;\n(function (VersionedTextDocumentIdentifier) {\n    /**\n     * Creates a new VersionedTextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     * @param version The document's version.\n     */\n    function create(uri, version) {\n        return { uri: uri, version: version };\n    }\n    VersionedTextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the [VersionedTextDocumentIdentifier](#VersionedTextDocumentIdentifier) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && Is.integer(candidate.version);\n    }\n    VersionedTextDocumentIdentifier.is = is;\n})(VersionedTextDocumentIdentifier || (VersionedTextDocumentIdentifier = {}));\n/**\n * The OptionalVersionedTextDocumentIdentifier namespace provides helper functions to work with\n * [OptionalVersionedTextDocumentIdentifier](#OptionalVersionedTextDocumentIdentifier) literals.\n */\nexport var OptionalVersionedTextDocumentIdentifier;\n(function (OptionalVersionedTextDocumentIdentifier) {\n    /**\n     * Creates a new OptionalVersionedTextDocumentIdentifier literal.\n     * @param uri The document's uri.\n     * @param version The document's version.\n     */\n    function create(uri, version) {\n        return { uri: uri, version: version };\n    }\n    OptionalVersionedTextDocumentIdentifier.create = create;\n    /**\n     * Checks whether the given literal conforms to the [OptionalVersionedTextDocumentIdentifier](#OptionalVersionedTextDocumentIdentifier) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && (candidate.version === null || Is.integer(candidate.version));\n    }\n    OptionalVersionedTextDocumentIdentifier.is = is;\n})(OptionalVersionedTextDocumentIdentifier || (OptionalVersionedTextDocumentIdentifier = {}));\n/**\n * The TextDocumentItem namespace provides helper functions to work with\n * [TextDocumentItem](#TextDocumentItem) literals.\n */\nexport var TextDocumentItem;\n(function (TextDocumentItem) {\n    /**\n     * Creates a new TextDocumentItem literal.\n     * @param uri The document's uri.\n     * @param languageId The document's language identifier.\n     * @param version The document's version number.\n     * @param text The document's text.\n     */\n    function create(uri, languageId, version, text) {\n        return { uri: uri, languageId: languageId, version: version, text: text };\n    }\n    TextDocumentItem.create = create;\n    /**\n     * Checks whether the given literal conforms to the [TextDocumentItem](#TextDocumentItem) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && Is.string(candidate.languageId) && Is.integer(candidate.version) && Is.string(candidate.text);\n    }\n    TextDocumentItem.is = is;\n})(TextDocumentItem || (TextDocumentItem = {}));\n/**\n * Describes the content type that a client supports in various\n * result literals like `Hover`, `ParameterInfo` or `CompletionItem`.\n *\n * Please note that `MarkupKinds` must not start with a `$`. This kinds\n * are reserved for internal usage.\n */\nexport var MarkupKind;\n(function (MarkupKind) {\n    /**\n     * Plain text is supported as a content format\n     */\n    MarkupKind.PlainText = 'plaintext';\n    /**\n     * Markdown is supported as a content format\n     */\n    MarkupKind.Markdown = 'markdown';\n    /**\n     * Checks whether the given value is a value of the [MarkupKind](#MarkupKind) type.\n     */\n    function is(value) {\n        var candidate = value;\n        return candidate === MarkupKind.PlainText || candidate === MarkupKind.Markdown;\n    }\n    MarkupKind.is = is;\n})(MarkupKind || (MarkupKind = {}));\nexport var MarkupContent;\n(function (MarkupContent) {\n    /**\n     * Checks whether the given value conforms to the [MarkupContent](#MarkupContent) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(value) && MarkupKind.is(candidate.kind) && Is.string(candidate.value);\n    }\n    MarkupContent.is = is;\n})(MarkupContent || (MarkupContent = {}));\n/**\n * The kind of a completion entry.\n */\nexport var CompletionItemKind;\n(function (CompletionItemKind) {\n    CompletionItemKind.Text = 1;\n    CompletionItemKind.Method = 2;\n    CompletionItemKind.Function = 3;\n    CompletionItemKind.Constructor = 4;\n    CompletionItemKind.Field = 5;\n    CompletionItemKind.Variable = 6;\n    CompletionItemKind.Class = 7;\n    CompletionItemKind.Interface = 8;\n    CompletionItemKind.Module = 9;\n    CompletionItemKind.Property = 10;\n    CompletionItemKind.Unit = 11;\n    CompletionItemKind.Value = 12;\n    CompletionItemKind.Enum = 13;\n    CompletionItemKind.Keyword = 14;\n    CompletionItemKind.Snippet = 15;\n    CompletionItemKind.Color = 16;\n    CompletionItemKind.File = 17;\n    CompletionItemKind.Reference = 18;\n    CompletionItemKind.Folder = 19;\n    CompletionItemKind.EnumMember = 20;\n    CompletionItemKind.Constant = 21;\n    CompletionItemKind.Struct = 22;\n    CompletionItemKind.Event = 23;\n    CompletionItemKind.Operator = 24;\n    CompletionItemKind.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\n/**\n * Defines whether the insert text in a completion item should be interpreted as\n * plain text or a snippet.\n */\nexport var InsertTextFormat;\n(function (InsertTextFormat) {\n    /**\n     * The primary text to be inserted is treated as a plain string.\n     */\n    InsertTextFormat.PlainText = 1;\n    /**\n     * The primary text to be inserted is treated as a snippet.\n     *\n     * A snippet can define tab stops and placeholders with `$1`, `$2`\n     * and `${3:foo}`. `$0` defines the final tab stop, it defaults to\n     * the end of the snippet. Placeholders with equal identifiers are linked,\n     * that is typing in one will update others too.\n     *\n     * See also: https://microsoft.github.io/language-server-protocol/specifications/specification-current/#snippet_syntax\n     */\n    InsertTextFormat.Snippet = 2;\n})(InsertTextFormat || (InsertTextFormat = {}));\n/**\n * Completion item tags are extra annotations that tweak the rendering of a completion\n * item.\n *\n * @since 3.15.0\n */\nexport var CompletionItemTag;\n(function (CompletionItemTag) {\n    /**\n     * Render a completion as obsolete, usually using a strike-out.\n     */\n    CompletionItemTag.Deprecated = 1;\n})(CompletionItemTag || (CompletionItemTag = {}));\n/**\n * The InsertReplaceEdit namespace provides functions to deal with insert / replace edits.\n *\n * @since 3.16.0\n */\nexport var InsertReplaceEdit;\n(function (InsertReplaceEdit) {\n    /**\n     * Creates a new insert / replace edit\n     */\n    function create(newText, insert, replace) {\n        return { newText: newText, insert: insert, replace: replace };\n    }\n    InsertReplaceEdit.create = create;\n    /**\n     * Checks whether the given literal conforms to the [InsertReplaceEdit](#InsertReplaceEdit) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return candidate && Is.string(candidate.newText) && Range.is(candidate.insert) && Range.is(candidate.replace);\n    }\n    InsertReplaceEdit.is = is;\n})(InsertReplaceEdit || (InsertReplaceEdit = {}));\n/**\n * How whitespace and indentation is handled during completion\n * item insertion.\n *\n * @since 3.16.0\n */\nexport var InsertTextMode;\n(function (InsertTextMode) {\n    /**\n     * The insertion or replace strings is taken as it is. If the\n     * value is multi line the lines below the cursor will be\n     * inserted using the indentation defined in the string value.\n     * The client will not apply any kind of adjustments to the\n     * string.\n     */\n    InsertTextMode.asIs = 1;\n    /**\n     * The editor adjusts leading whitespace of new lines so that\n     * they match the indentation up to the cursor of the line for\n     * which the item is accepted.\n     *\n     * Consider a line like this: <2tabs><cursor><3tabs>foo. Accepting a\n     * multi line completion item is indented using 2 tabs and all\n     * following lines inserted will be indented using 2 tabs as well.\n     */\n    InsertTextMode.adjustIndentation = 2;\n})(InsertTextMode || (InsertTextMode = {}));\nexport var CompletionItemLabelDetails;\n(function (CompletionItemLabelDetails) {\n    function is(value) {\n        var candidate = value;\n        return candidate && (Is.string(candidate.detail) || candidate.detail === undefined) &&\n            (Is.string(candidate.description) || candidate.description === undefined);\n    }\n    CompletionItemLabelDetails.is = is;\n})(CompletionItemLabelDetails || (CompletionItemLabelDetails = {}));\n/**\n * The CompletionItem namespace provides functions to deal with\n * completion items.\n */\nexport var CompletionItem;\n(function (CompletionItem) {\n    /**\n     * Create a completion item and seed it with a label.\n     * @param label The completion item's label\n     */\n    function create(label) {\n        return { label: label };\n    }\n    CompletionItem.create = create;\n})(CompletionItem || (CompletionItem = {}));\n/**\n * The CompletionList namespace provides functions to deal with\n * completion lists.\n */\nexport var CompletionList;\n(function (CompletionList) {\n    /**\n     * Creates a new completion list.\n     *\n     * @param items The completion items.\n     * @param isIncomplete The list is not complete.\n     */\n    function create(items, isIncomplete) {\n        return { items: items ? items : [], isIncomplete: !!isIncomplete };\n    }\n    CompletionList.create = create;\n})(CompletionList || (CompletionList = {}));\nexport var MarkedString;\n(function (MarkedString) {\n    /**\n     * Creates a marked string from plain text.\n     *\n     * @param plainText The plain text.\n     */\n    function fromPlainText(plainText) {\n        return plainText.replace(/[\\\\`*_{}[\\]()#+\\-.!]/g, '\\\\$&'); // escape markdown syntax tokens: http://daringfireball.net/projects/markdown/syntax#backslash\n    }\n    MarkedString.fromPlainText = fromPlainText;\n    /**\n     * Checks whether the given value conforms to the [MarkedString](#MarkedString) type.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.string(candidate) || (Is.objectLiteral(candidate) && Is.string(candidate.language) && Is.string(candidate.value));\n    }\n    MarkedString.is = is;\n})(MarkedString || (MarkedString = {}));\nexport var Hover;\n(function (Hover) {\n    /**\n     * Checks whether the given value conforms to the [Hover](#Hover) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return !!candidate && Is.objectLiteral(candidate) && (MarkupContent.is(candidate.contents) ||\n            MarkedString.is(candidate.contents) ||\n            Is.typedArray(candidate.contents, MarkedString.is)) && (value.range === undefined || Range.is(value.range));\n    }\n    Hover.is = is;\n})(Hover || (Hover = {}));\n/**\n * The ParameterInformation namespace provides helper functions to work with\n * [ParameterInformation](#ParameterInformation) literals.\n */\nexport var ParameterInformation;\n(function (ParameterInformation) {\n    /**\n     * Creates a new parameter information literal.\n     *\n     * @param label A label string.\n     * @param documentation A doc string.\n     */\n    function create(label, documentation) {\n        return documentation ? { label: label, documentation: documentation } : { label: label };\n    }\n    ParameterInformation.create = create;\n})(ParameterInformation || (ParameterInformation = {}));\n/**\n * The SignatureInformation namespace provides helper functions to work with\n * [SignatureInformation](#SignatureInformation) literals.\n */\nexport var SignatureInformation;\n(function (SignatureInformation) {\n    function create(label, documentation) {\n        var parameters = [];\n        for (var _i = 2; _i < arguments.length; _i++) {\n            parameters[_i - 2] = arguments[_i];\n        }\n        var result = { label: label };\n        if (Is.defined(documentation)) {\n            result.documentation = documentation;\n        }\n        if (Is.defined(parameters)) {\n            result.parameters = parameters;\n        }\n        else {\n            result.parameters = [];\n        }\n        return result;\n    }\n    SignatureInformation.create = create;\n})(SignatureInformation || (SignatureInformation = {}));\n/**\n * A document highlight kind.\n */\nexport var DocumentHighlightKind;\n(function (DocumentHighlightKind) {\n    /**\n     * A textual occurrence.\n     */\n    DocumentHighlightKind.Text = 1;\n    /**\n     * Read-access of a symbol, like reading a variable.\n     */\n    DocumentHighlightKind.Read = 2;\n    /**\n     * Write-access of a symbol, like writing to a variable.\n     */\n    DocumentHighlightKind.Write = 3;\n})(DocumentHighlightKind || (DocumentHighlightKind = {}));\n/**\n * DocumentHighlight namespace to provide helper functions to work with\n * [DocumentHighlight](#DocumentHighlight) literals.\n */\nexport var DocumentHighlight;\n(function (DocumentHighlight) {\n    /**\n     * Create a DocumentHighlight object.\n     * @param range The range the highlight applies to.\n     * @param kind The highlight kind\n     */\n    function create(range, kind) {\n        var result = { range: range };\n        if (Is.number(kind)) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    DocumentHighlight.create = create;\n})(DocumentHighlight || (DocumentHighlight = {}));\n/**\n * A symbol kind.\n */\nexport var SymbolKind;\n(function (SymbolKind) {\n    SymbolKind.File = 1;\n    SymbolKind.Module = 2;\n    SymbolKind.Namespace = 3;\n    SymbolKind.Package = 4;\n    SymbolKind.Class = 5;\n    SymbolKind.Method = 6;\n    SymbolKind.Property = 7;\n    SymbolKind.Field = 8;\n    SymbolKind.Constructor = 9;\n    SymbolKind.Enum = 10;\n    SymbolKind.Interface = 11;\n    SymbolKind.Function = 12;\n    SymbolKind.Variable = 13;\n    SymbolKind.Constant = 14;\n    SymbolKind.String = 15;\n    SymbolKind.Number = 16;\n    SymbolKind.Boolean = 17;\n    SymbolKind.Array = 18;\n    SymbolKind.Object = 19;\n    SymbolKind.Key = 20;\n    SymbolKind.Null = 21;\n    SymbolKind.EnumMember = 22;\n    SymbolKind.Struct = 23;\n    SymbolKind.Event = 24;\n    SymbolKind.Operator = 25;\n    SymbolKind.TypeParameter = 26;\n})(SymbolKind || (SymbolKind = {}));\n/**\n * Symbol tags are extra annotations that tweak the rendering of a symbol.\n *\n * @since 3.16\n */\nexport var SymbolTag;\n(function (SymbolTag) {\n    /**\n     * Render a symbol as obsolete, usually using a strike-out.\n     */\n    SymbolTag.Deprecated = 1;\n})(SymbolTag || (SymbolTag = {}));\nexport var SymbolInformation;\n(function (SymbolInformation) {\n    /**\n     * Creates a new symbol information literal.\n     *\n     * @param name The name of the symbol.\n     * @param kind The kind of the symbol.\n     * @param range The range of the location of the symbol.\n     * @param uri The resource of the location of symbol.\n     * @param containerName The name of the symbol containing the symbol.\n     */\n    function create(name, kind, range, uri, containerName) {\n        var result = {\n            name: name,\n            kind: kind,\n            location: { uri: uri, range: range }\n        };\n        if (containerName) {\n            result.containerName = containerName;\n        }\n        return result;\n    }\n    SymbolInformation.create = create;\n})(SymbolInformation || (SymbolInformation = {}));\nexport var WorkspaceSymbol;\n(function (WorkspaceSymbol) {\n    /**\n     * Create a new workspace symbol.\n     *\n     * @param name The name of the symbol.\n     * @param kind The kind of the symbol.\n     * @param uri The resource of the location of the symbol.\n     * @param range An options range of the location.\n     * @returns A WorkspaceSymbol.\n     */\n    function create(name, kind, uri, range) {\n        return range !== undefined\n            ? { name: name, kind: kind, location: { uri: uri, range: range } }\n            : { name: name, kind: kind, location: { uri: uri } };\n    }\n    WorkspaceSymbol.create = create;\n})(WorkspaceSymbol || (WorkspaceSymbol = {}));\nexport var DocumentSymbol;\n(function (DocumentSymbol) {\n    /**\n     * Creates a new symbol information literal.\n     *\n     * @param name The name of the symbol.\n     * @param detail The detail of the symbol.\n     * @param kind The kind of the symbol.\n     * @param range The range of the symbol.\n     * @param selectionRange The selectionRange of the symbol.\n     * @param children Children of the symbol.\n     */\n    function create(name, detail, kind, range, selectionRange, children) {\n        var result = {\n            name: name,\n            detail: detail,\n            kind: kind,\n            range: range,\n            selectionRange: selectionRange\n        };\n        if (children !== undefined) {\n            result.children = children;\n        }\n        return result;\n    }\n    DocumentSymbol.create = create;\n    /**\n     * Checks whether the given literal conforms to the [DocumentSymbol](#DocumentSymbol) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return candidate &&\n            Is.string(candidate.name) && Is.number(candidate.kind) &&\n            Range.is(candidate.range) && Range.is(candidate.selectionRange) &&\n            (candidate.detail === undefined || Is.string(candidate.detail)) &&\n            (candidate.deprecated === undefined || Is.boolean(candidate.deprecated)) &&\n            (candidate.children === undefined || Array.isArray(candidate.children)) &&\n            (candidate.tags === undefined || Array.isArray(candidate.tags));\n    }\n    DocumentSymbol.is = is;\n})(DocumentSymbol || (DocumentSymbol = {}));\n/**\n * A set of predefined code action kinds\n */\nexport var CodeActionKind;\n(function (CodeActionKind) {\n    /**\n     * Empty kind.\n     */\n    CodeActionKind.Empty = '';\n    /**\n     * Base kind for quickfix actions: 'quickfix'\n     */\n    CodeActionKind.QuickFix = 'quickfix';\n    /**\n     * Base kind for refactoring actions: 'refactor'\n     */\n    CodeActionKind.Refactor = 'refactor';\n    /**\n     * Base kind for refactoring extraction actions: 'refactor.extract'\n     *\n     * Example extract actions:\n     *\n     * - Extract method\n     * - Extract function\n     * - Extract variable\n     * - Extract interface from class\n     * - ...\n     */\n    CodeActionKind.RefactorExtract = 'refactor.extract';\n    /**\n     * Base kind for refactoring inline actions: 'refactor.inline'\n     *\n     * Example inline actions:\n     *\n     * - Inline function\n     * - Inline variable\n     * - Inline constant\n     * - ...\n     */\n    CodeActionKind.RefactorInline = 'refactor.inline';\n    /**\n     * Base kind for refactoring rewrite actions: 'refactor.rewrite'\n     *\n     * Example rewrite actions:\n     *\n     * - Convert JavaScript function to class\n     * - Add or remove parameter\n     * - Encapsulate field\n     * - Make method static\n     * - Move method to base class\n     * - ...\n     */\n    CodeActionKind.RefactorRewrite = 'refactor.rewrite';\n    /**\n     * Base kind for source actions: `source`\n     *\n     * Source code actions apply to the entire file.\n     */\n    CodeActionKind.Source = 'source';\n    /**\n     * Base kind for an organize imports source action: `source.organizeImports`\n     */\n    CodeActionKind.SourceOrganizeImports = 'source.organizeImports';\n    /**\n     * Base kind for auto-fix source actions: `source.fixAll`.\n     *\n     * Fix all actions automatically fix errors that have a clear fix that do not require user input.\n     * They should not suppress errors or perform unsafe fixes such as generating new types or classes.\n     *\n     * @since 3.15.0\n     */\n    CodeActionKind.SourceFixAll = 'source.fixAll';\n})(CodeActionKind || (CodeActionKind = {}));\n/**\n * The reason why code actions were requested.\n *\n * @since 3.17.0\n */\nexport var CodeActionTriggerKind;\n(function (CodeActionTriggerKind) {\n    /**\n     * Code actions were explicitly requested by the user or by an extension.\n     */\n    CodeActionTriggerKind.Invoked = 1;\n    /**\n     * Code actions were requested automatically.\n     *\n     * This typically happens when current selection in a file changes, but can\n     * also be triggered when file content changes.\n     */\n    CodeActionTriggerKind.Automatic = 2;\n})(CodeActionTriggerKind || (CodeActionTriggerKind = {}));\n/**\n * The CodeActionContext namespace provides helper functions to work with\n * [CodeActionContext](#CodeActionContext) literals.\n */\nexport var CodeActionContext;\n(function (CodeActionContext) {\n    /**\n     * Creates a new CodeActionContext literal.\n     */\n    function create(diagnostics, only, triggerKind) {\n        var result = { diagnostics: diagnostics };\n        if (only !== undefined && only !== null) {\n            result.only = only;\n        }\n        if (triggerKind !== undefined && triggerKind !== null) {\n            result.triggerKind = triggerKind;\n        }\n        return result;\n    }\n    CodeActionContext.create = create;\n    /**\n     * Checks whether the given literal conforms to the [CodeActionContext](#CodeActionContext) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.typedArray(candidate.diagnostics, Diagnostic.is)\n            && (candidate.only === undefined || Is.typedArray(candidate.only, Is.string))\n            && (candidate.triggerKind === undefined || candidate.triggerKind === CodeActionTriggerKind.Invoked || candidate.triggerKind === CodeActionTriggerKind.Automatic);\n    }\n    CodeActionContext.is = is;\n})(CodeActionContext || (CodeActionContext = {}));\nexport var CodeAction;\n(function (CodeAction) {\n    function create(title, kindOrCommandOrEdit, kind) {\n        var result = { title: title };\n        var checkKind = true;\n        if (typeof kindOrCommandOrEdit === 'string') {\n            checkKind = false;\n            result.kind = kindOrCommandOrEdit;\n        }\n        else if (Command.is(kindOrCommandOrEdit)) {\n            result.command = kindOrCommandOrEdit;\n        }\n        else {\n            result.edit = kindOrCommandOrEdit;\n        }\n        if (checkKind && kind !== undefined) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    CodeAction.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate && Is.string(candidate.title) &&\n            (candidate.diagnostics === undefined || Is.typedArray(candidate.diagnostics, Diagnostic.is)) &&\n            (candidate.kind === undefined || Is.string(candidate.kind)) &&\n            (candidate.edit !== undefined || candidate.command !== undefined) &&\n            (candidate.command === undefined || Command.is(candidate.command)) &&\n            (candidate.isPreferred === undefined || Is.boolean(candidate.isPreferred)) &&\n            (candidate.edit === undefined || WorkspaceEdit.is(candidate.edit));\n    }\n    CodeAction.is = is;\n})(CodeAction || (CodeAction = {}));\n/**\n * The CodeLens namespace provides helper functions to work with\n * [CodeLens](#CodeLens) literals.\n */\nexport var CodeLens;\n(function (CodeLens) {\n    /**\n     * Creates a new CodeLens literal.\n     */\n    function create(range, data) {\n        var result = { range: range };\n        if (Is.defined(data)) {\n            result.data = data;\n        }\n        return result;\n    }\n    CodeLens.create = create;\n    /**\n     * Checks whether the given literal conforms to the [CodeLens](#CodeLens) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.command) || Command.is(candidate.command));\n    }\n    CodeLens.is = is;\n})(CodeLens || (CodeLens = {}));\n/**\n * The FormattingOptions namespace provides helper functions to work with\n * [FormattingOptions](#FormattingOptions) literals.\n */\nexport var FormattingOptions;\n(function (FormattingOptions) {\n    /**\n     * Creates a new FormattingOptions literal.\n     */\n    function create(tabSize, insertSpaces) {\n        return { tabSize: tabSize, insertSpaces: insertSpaces };\n    }\n    FormattingOptions.create = create;\n    /**\n     * Checks whether the given literal conforms to the [FormattingOptions](#FormattingOptions) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.uinteger(candidate.tabSize) && Is.boolean(candidate.insertSpaces);\n    }\n    FormattingOptions.is = is;\n})(FormattingOptions || (FormattingOptions = {}));\n/**\n * The DocumentLink namespace provides helper functions to work with\n * [DocumentLink](#DocumentLink) literals.\n */\nexport var DocumentLink;\n(function (DocumentLink) {\n    /**\n     * Creates a new DocumentLink literal.\n     */\n    function create(range, target, data) {\n        return { range: range, target: target, data: data };\n    }\n    DocumentLink.create = create;\n    /**\n     * Checks whether the given literal conforms to the [DocumentLink](#DocumentLink) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Range.is(candidate.range) && (Is.undefined(candidate.target) || Is.string(candidate.target));\n    }\n    DocumentLink.is = is;\n})(DocumentLink || (DocumentLink = {}));\n/**\n * The SelectionRange namespace provides helper function to work with\n * SelectionRange literals.\n */\nexport var SelectionRange;\n(function (SelectionRange) {\n    /**\n     * Creates a new SelectionRange\n     * @param range the range.\n     * @param parent an optional parent.\n     */\n    function create(range, parent) {\n        return { range: range, parent: parent };\n    }\n    SelectionRange.create = create;\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Range.is(candidate.range) && (candidate.parent === undefined || SelectionRange.is(candidate.parent));\n    }\n    SelectionRange.is = is;\n})(SelectionRange || (SelectionRange = {}));\n/**\n * A set of predefined token types. This set is not fixed\n * an clients can specify additional token types via the\n * corresponding client capabilities.\n *\n * @since 3.16.0\n */\nexport var SemanticTokenTypes;\n(function (SemanticTokenTypes) {\n    SemanticTokenTypes[\"namespace\"] = \"namespace\";\n    /**\n     * Represents a generic type. Acts as a fallback for types which can't be mapped to\n     * a specific type like class or enum.\n     */\n    SemanticTokenTypes[\"type\"] = \"type\";\n    SemanticTokenTypes[\"class\"] = \"class\";\n    SemanticTokenTypes[\"enum\"] = \"enum\";\n    SemanticTokenTypes[\"interface\"] = \"interface\";\n    SemanticTokenTypes[\"struct\"] = \"struct\";\n    SemanticTokenTypes[\"typeParameter\"] = \"typeParameter\";\n    SemanticTokenTypes[\"parameter\"] = \"parameter\";\n    SemanticTokenTypes[\"variable\"] = \"variable\";\n    SemanticTokenTypes[\"property\"] = \"property\";\n    SemanticTokenTypes[\"enumMember\"] = \"enumMember\";\n    SemanticTokenTypes[\"event\"] = \"event\";\n    SemanticTokenTypes[\"function\"] = \"function\";\n    SemanticTokenTypes[\"method\"] = \"method\";\n    SemanticTokenTypes[\"macro\"] = \"macro\";\n    SemanticTokenTypes[\"keyword\"] = \"keyword\";\n    SemanticTokenTypes[\"modifier\"] = \"modifier\";\n    SemanticTokenTypes[\"comment\"] = \"comment\";\n    SemanticTokenTypes[\"string\"] = \"string\";\n    SemanticTokenTypes[\"number\"] = \"number\";\n    SemanticTokenTypes[\"regexp\"] = \"regexp\";\n    SemanticTokenTypes[\"operator\"] = \"operator\";\n    /**\n     * @since 3.17.0\n     */\n    SemanticTokenTypes[\"decorator\"] = \"decorator\";\n})(SemanticTokenTypes || (SemanticTokenTypes = {}));\n/**\n * A set of predefined token modifiers. This set is not fixed\n * an clients can specify additional token types via the\n * corresponding client capabilities.\n *\n * @since 3.16.0\n */\nexport var SemanticTokenModifiers;\n(function (SemanticTokenModifiers) {\n    SemanticTokenModifiers[\"declaration\"] = \"declaration\";\n    SemanticTokenModifiers[\"definition\"] = \"definition\";\n    SemanticTokenModifiers[\"readonly\"] = \"readonly\";\n    SemanticTokenModifiers[\"static\"] = \"static\";\n    SemanticTokenModifiers[\"deprecated\"] = \"deprecated\";\n    SemanticTokenModifiers[\"abstract\"] = \"abstract\";\n    SemanticTokenModifiers[\"async\"] = \"async\";\n    SemanticTokenModifiers[\"modification\"] = \"modification\";\n    SemanticTokenModifiers[\"documentation\"] = \"documentation\";\n    SemanticTokenModifiers[\"defaultLibrary\"] = \"defaultLibrary\";\n})(SemanticTokenModifiers || (SemanticTokenModifiers = {}));\n/**\n * @since 3.16.0\n */\nexport var SemanticTokens;\n(function (SemanticTokens) {\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && (candidate.resultId === undefined || typeof candidate.resultId === 'string') &&\n            Array.isArray(candidate.data) && (candidate.data.length === 0 || typeof candidate.data[0] === 'number');\n    }\n    SemanticTokens.is = is;\n})(SemanticTokens || (SemanticTokens = {}));\n/**\n * The InlineValueText namespace provides functions to deal with InlineValueTexts.\n *\n * @since 3.17.0\n */\nexport var InlineValueText;\n(function (InlineValueText) {\n    /**\n     * Creates a new InlineValueText literal.\n     */\n    function create(range, text) {\n        return { range: range, text: text };\n    }\n    InlineValueText.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate !== undefined && candidate !== null && Range.is(candidate.range) && Is.string(candidate.text);\n    }\n    InlineValueText.is = is;\n})(InlineValueText || (InlineValueText = {}));\n/**\n * The InlineValueVariableLookup namespace provides functions to deal with InlineValueVariableLookups.\n *\n * @since 3.17.0\n */\nexport var InlineValueVariableLookup;\n(function (InlineValueVariableLookup) {\n    /**\n     * Creates a new InlineValueText literal.\n     */\n    function create(range, variableName, caseSensitiveLookup) {\n        return { range: range, variableName: variableName, caseSensitiveLookup: caseSensitiveLookup };\n    }\n    InlineValueVariableLookup.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate !== undefined && candidate !== null && Range.is(candidate.range) && Is.boolean(candidate.caseSensitiveLookup)\n            && (Is.string(candidate.variableName) || candidate.variableName === undefined);\n    }\n    InlineValueVariableLookup.is = is;\n})(InlineValueVariableLookup || (InlineValueVariableLookup = {}));\n/**\n * The InlineValueEvaluatableExpression namespace provides functions to deal with InlineValueEvaluatableExpression.\n *\n * @since 3.17.0\n */\nexport var InlineValueEvaluatableExpression;\n(function (InlineValueEvaluatableExpression) {\n    /**\n     * Creates a new InlineValueEvaluatableExpression literal.\n     */\n    function create(range, expression) {\n        return { range: range, expression: expression };\n    }\n    InlineValueEvaluatableExpression.create = create;\n    function is(value) {\n        var candidate = value;\n        return candidate !== undefined && candidate !== null && Range.is(candidate.range)\n            && (Is.string(candidate.expression) || candidate.expression === undefined);\n    }\n    InlineValueEvaluatableExpression.is = is;\n})(InlineValueEvaluatableExpression || (InlineValueEvaluatableExpression = {}));\n/**\n * The InlineValueContext namespace provides helper functions to work with\n * [InlineValueContext](#InlineValueContext) literals.\n *\n * @since 3.17.0\n */\nexport var InlineValueContext;\n(function (InlineValueContext) {\n    /**\n     * Creates a new InlineValueContext literal.\n     */\n    function create(frameId, stoppedLocation) {\n        return { frameId: frameId, stoppedLocation: stoppedLocation };\n    }\n    InlineValueContext.create = create;\n    /**\n     * Checks whether the given literal conforms to the [InlineValueContext](#InlineValueContext) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Range.is(value.stoppedLocation);\n    }\n    InlineValueContext.is = is;\n})(InlineValueContext || (InlineValueContext = {}));\n/**\n * Inlay hint kinds.\n *\n * @since 3.17.0\n */\nexport var InlayHintKind;\n(function (InlayHintKind) {\n    /**\n     * An inlay hint that for a type annotation.\n     */\n    InlayHintKind.Type = 1;\n    /**\n     * An inlay hint that is for a parameter.\n     */\n    InlayHintKind.Parameter = 2;\n    function is(value) {\n        return value === 1 || value === 2;\n    }\n    InlayHintKind.is = is;\n})(InlayHintKind || (InlayHintKind = {}));\nexport var InlayHintLabelPart;\n(function (InlayHintLabelPart) {\n    function create(value) {\n        return { value: value };\n    }\n    InlayHintLabelPart.create = create;\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate)\n            && (candidate.tooltip === undefined || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip))\n            && (candidate.location === undefined || Location.is(candidate.location))\n            && (candidate.command === undefined || Command.is(candidate.command));\n    }\n    InlayHintLabelPart.is = is;\n})(InlayHintLabelPart || (InlayHintLabelPart = {}));\nexport var InlayHint;\n(function (InlayHint) {\n    function create(position, label, kind) {\n        var result = { position: position, label: label };\n        if (kind !== undefined) {\n            result.kind = kind;\n        }\n        return result;\n    }\n    InlayHint.create = create;\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && Position.is(candidate.position)\n            && (Is.string(candidate.label) || Is.typedArray(candidate.label, InlayHintLabelPart.is))\n            && (candidate.kind === undefined || InlayHintKind.is(candidate.kind))\n            && (candidate.textEdits === undefined) || Is.typedArray(candidate.textEdits, TextEdit.is)\n            && (candidate.tooltip === undefined || Is.string(candidate.tooltip) || MarkupContent.is(candidate.tooltip))\n            && (candidate.paddingLeft === undefined || Is.boolean(candidate.paddingLeft))\n            && (candidate.paddingRight === undefined || Is.boolean(candidate.paddingRight));\n    }\n    InlayHint.is = is;\n})(InlayHint || (InlayHint = {}));\nexport var WorkspaceFolder;\n(function (WorkspaceFolder) {\n    function is(value) {\n        var candidate = value;\n        return Is.objectLiteral(candidate) && URI.is(candidate.uri) && Is.string(candidate.name);\n    }\n    WorkspaceFolder.is = is;\n})(WorkspaceFolder || (WorkspaceFolder = {}));\nexport var EOL = ['\\n', '\\r\\n', '\\r'];\n/**\n * @deprecated Use the text document from the new vscode-languageserver-textdocument package.\n */\nexport var TextDocument;\n(function (TextDocument) {\n    /**\n     * Creates a new ITextDocument literal from the given uri and content.\n     * @param uri The document's uri.\n     * @param languageId The document's language Id.\n     * @param version The document's version.\n     * @param content The document's content.\n     */\n    function create(uri, languageId, version, content) {\n        return new FullTextDocument(uri, languageId, version, content);\n    }\n    TextDocument.create = create;\n    /**\n     * Checks whether the given literal conforms to the [ITextDocument](#ITextDocument) interface.\n     */\n    function is(value) {\n        var candidate = value;\n        return Is.defined(candidate) && Is.string(candidate.uri) && (Is.undefined(candidate.languageId) || Is.string(candidate.languageId)) && Is.uinteger(candidate.lineCount)\n            && Is.func(candidate.getText) && Is.func(candidate.positionAt) && Is.func(candidate.offsetAt) ? true : false;\n    }\n    TextDocument.is = is;\n    function applyEdits(document, edits) {\n        var text = document.getText();\n        var sortedEdits = mergeSort(edits, function (a, b) {\n            var diff = a.range.start.line - b.range.start.line;\n            if (diff === 0) {\n                return a.range.start.character - b.range.start.character;\n            }\n            return diff;\n        });\n        var lastModifiedOffset = text.length;\n        for (var i = sortedEdits.length - 1; i >= 0; i--) {\n            var e = sortedEdits[i];\n            var startOffset = document.offsetAt(e.range.start);\n            var endOffset = document.offsetAt(e.range.end);\n            if (endOffset <= lastModifiedOffset) {\n                text = text.substring(0, startOffset) + e.newText + text.substring(endOffset, text.length);\n            }\n            else {\n                throw new Error('Overlapping edit');\n            }\n            lastModifiedOffset = startOffset;\n        }\n        return text;\n    }\n    TextDocument.applyEdits = applyEdits;\n    function mergeSort(data, compare) {\n        if (data.length <= 1) {\n            // sorted\n            return data;\n        }\n        var p = (data.length / 2) | 0;\n        var left = data.slice(0, p);\n        var right = data.slice(p);\n        mergeSort(left, compare);\n        mergeSort(right, compare);\n        var leftIdx = 0;\n        var rightIdx = 0;\n        var i = 0;\n        while (leftIdx < left.length && rightIdx < right.length) {\n            var ret = compare(left[leftIdx], right[rightIdx]);\n            if (ret <= 0) {\n                // smaller_equal -> take left to preserve order\n                data[i++] = left[leftIdx++];\n            }\n            else {\n                // greater -> take right\n                data[i++] = right[rightIdx++];\n            }\n        }\n        while (leftIdx < left.length) {\n            data[i++] = left[leftIdx++];\n        }\n        while (rightIdx < right.length) {\n            data[i++] = right[rightIdx++];\n        }\n        return data;\n    }\n})(TextDocument || (TextDocument = {}));\n/**\n * @deprecated Use the text document from the new vscode-languageserver-textdocument package.\n */\nvar FullTextDocument = /** @class */ (function () {\n    function FullTextDocument(uri, languageId, version, content) {\n        this._uri = uri;\n        this._languageId = languageId;\n        this._version = version;\n        this._content = content;\n        this._lineOffsets = undefined;\n    }\n    Object.defineProperty(FullTextDocument.prototype, \"uri\", {\n        get: function () {\n            return this._uri;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(FullTextDocument.prototype, \"languageId\", {\n        get: function () {\n            return this._languageId;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(FullTextDocument.prototype, \"version\", {\n        get: function () {\n            return this._version;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    FullTextDocument.prototype.getText = function (range) {\n        if (range) {\n            var start = this.offsetAt(range.start);\n            var end = this.offsetAt(range.end);\n            return this._content.substring(start, end);\n        }\n        return this._content;\n    };\n    FullTextDocument.prototype.update = function (event, version) {\n        this._content = event.text;\n        this._version = version;\n        this._lineOffsets = undefined;\n    };\n    FullTextDocument.prototype.getLineOffsets = function () {\n        if (this._lineOffsets === undefined) {\n            var lineOffsets = [];\n            var text = this._content;\n            var isLineStart = true;\n            for (var i = 0; i < text.length; i++) {\n                if (isLineStart) {\n                    lineOffsets.push(i);\n                    isLineStart = false;\n                }\n                var ch = text.charAt(i);\n                isLineStart = (ch === '\\r' || ch === '\\n');\n                if (ch === '\\r' && i + 1 < text.length && text.charAt(i + 1) === '\\n') {\n                    i++;\n                }\n            }\n            if (isLineStart && text.length > 0) {\n                lineOffsets.push(text.length);\n            }\n            this._lineOffsets = lineOffsets;\n        }\n        return this._lineOffsets;\n    };\n    FullTextDocument.prototype.positionAt = function (offset) {\n        offset = Math.max(Math.min(offset, this._content.length), 0);\n        var lineOffsets = this.getLineOffsets();\n        var low = 0, high = lineOffsets.length;\n        if (high === 0) {\n            return Position.create(0, offset);\n        }\n        while (low < high) {\n            var mid = Math.floor((low + high) / 2);\n            if (lineOffsets[mid] > offset) {\n                high = mid;\n            }\n            else {\n                low = mid + 1;\n            }\n        }\n        // low is the least x for which the line offset is larger than the current offset\n        // or array.length if no line offset is larger than the current offset\n        var line = low - 1;\n        return Position.create(line, offset - lineOffsets[line]);\n    };\n    FullTextDocument.prototype.offsetAt = function (position) {\n        var lineOffsets = this.getLineOffsets();\n        if (position.line >= lineOffsets.length) {\n            return this._content.length;\n        }\n        else if (position.line < 0) {\n            return 0;\n        }\n        var lineOffset = lineOffsets[position.line];\n        var nextLineOffset = (position.line + 1 < lineOffsets.length) ? lineOffsets[position.line + 1] : this._content.length;\n        return Math.max(Math.min(lineOffset + position.character, nextLineOffset), lineOffset);\n    };\n    Object.defineProperty(FullTextDocument.prototype, \"lineCount\", {\n        get: function () {\n            return this.getLineOffsets().length;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return FullTextDocument;\n}());\nvar Is;\n(function (Is) {\n    var toString = Object.prototype.toString;\n    function defined(value) {\n        return typeof value !== 'undefined';\n    }\n    Is.defined = defined;\n    function undefined(value) {\n        return typeof value === 'undefined';\n    }\n    Is.undefined = undefined;\n    function boolean(value) {\n        return value === true || value === false;\n    }\n    Is.boolean = boolean;\n    function string(value) {\n        return toString.call(value) === '[object String]';\n    }\n    Is.string = string;\n    function number(value) {\n        return toString.call(value) === '[object Number]';\n    }\n    Is.number = number;\n    function numberRange(value, min, max) {\n        return toString.call(value) === '[object Number]' && min <= value && value <= max;\n    }\n    Is.numberRange = numberRange;\n    function integer(value) {\n        return toString.call(value) === '[object Number]' && -********** <= value && value <= **********;\n    }\n    Is.integer = integer;\n    function uinteger(value) {\n        return toString.call(value) === '[object Number]' && 0 <= value && value <= **********;\n    }\n    Is.uinteger = uinteger;\n    function func(value) {\n        return toString.call(value) === '[object Function]';\n    }\n    Is.func = func;\n    function objectLiteral(value) {\n        // Strictly speaking class instances pass this check as well. Since the LSP\n        // doesn't use classes we ignore this for now. If we do we need to add something\n        // like this: `Object.getPrototypeOf(Object.getPrototypeOf(x)) === null`\n        return value !== null && typeof value === 'object';\n    }\n    Is.objectLiteral = objectLiteral;\n    function typedArray(value, check) {\n        return Array.isArray(value) && value.every(check);\n    }\n    Is.typedArray = typedArray;\n})(Is || (Is = {}));\n", "export { InsertTextFormat } from 'vscode-languageserver-types';\nexport const FileChangeTypeKind = {\n    Created: 1,\n    Changed: 2,\n    Deleted: 3,\n};\nexport var CompletionItemKind;\n(function (CompletionItemKind) {\n    CompletionItemKind.Text = 1;\n    CompletionItemKind.Method = 2;\n    CompletionItemKind.Function = 3;\n    CompletionItemKind.Constructor = 4;\n    CompletionItemKind.Field = 5;\n    CompletionItemKind.Variable = 6;\n    CompletionItemKind.Class = 7;\n    CompletionItemKind.Interface = 8;\n    CompletionItemKind.Module = 9;\n    CompletionItemKind.Property = 10;\n    CompletionItemKind.Unit = 11;\n    CompletionItemKind.Value = 12;\n    CompletionItemKind.Enum = 13;\n    CompletionItemKind.Keyword = 14;\n    CompletionItemKind.Snippet = 15;\n    CompletionItemKind.Color = 16;\n    CompletionItemKind.File = 17;\n    CompletionItemKind.Reference = 18;\n    CompletionItemKind.Folder = 19;\n    CompletionItemKind.EnumMember = 20;\n    CompletionItemKind.Constant = 21;\n    CompletionItemKind.Struct = 22;\n    CompletionItemKind.Event = 23;\n    CompletionItemKind.Operator = 24;\n    CompletionItemKind.TypeParameter = 25;\n})(CompletionItemKind || (CompletionItemKind = {}));\n//# sourceMappingURL=types.js.map", "import { Kind } from 'graphql';\nexport const AdditionalRuleKinds = {\n    ALIASED_FIELD: 'AliasedField',\n    ARGUMENTS: 'Arguments',\n    SHORT_QUERY: 'ShortQuery',\n    QUERY: 'Query',\n    MUTATION: 'Mutation',\n    SUBSCRIPTION: 'Subscription',\n    TYPE_CONDITION: 'TypeCondition',\n    INVALID: 'Invalid',\n    COMMENT: 'Comment',\n    SCHEMA_DEF: 'SchemaDef',\n    SCALAR_DEF: 'ScalarDef',\n    OBJECT_TYPE_DEF: 'ObjectTypeDef',\n    OBJECT_VALUE: 'ObjectValue',\n    LIST_VALUE: 'ListValue',\n    INTERFACE_DEF: 'InterfaceDef',\n    UNION_DEF: 'UnionDef',\n    ENUM_DEF: 'EnumDef',\n    ENUM_VALUE: 'EnumValue',\n    FIELD_DEF: 'FieldDef',\n    INPUT_DEF: 'InputDef',\n    INPUT_VALUE_DEF: 'InputValueDef',\n    ARGUMENTS_DEF: 'ArgumentsDef',\n    EXTEND_DEF: 'ExtendDef',\n    EXTENSION_DEFINITION: 'ExtensionDefinition',\n    DIRECTIVE_DEF: 'DirectiveDef',\n    IMPLEMENTS: 'Implements',\n    VARIABLE_DEFINITIONS: 'VariableDefinitions',\n    TYPE: 'Type',\n};\nexport const RuleKinds = Object.assign(Object.assign({}, Kind), AdditionalRuleKinds);\n//# sourceMappingURL=types.js.map", "import { isInterfaceType, GraphQLInterfaceType, GraphQLObjectType, Kind, DirectiveLocation, isListType, isNonNullType, isScalarType, isObjectType, isUnionType, isEnumType, isInputObjectType, isOutputType, GraphQLBoolean, GraphQLEnumType, GraphQLInputObjectType, GraphQLList, SchemaMetaFieldDef, TypeMetaFieldDef, TypeNameMetaFieldDef, assertAbstractType, doTypesOverlap, getNamedType, getNullableType, isAbstractType, isCompositeType, isInputType, visit, BREAK, parse, } from 'graphql';\nimport { CompletionItemKind, InsertTextFormat, } from '../types';\nimport { CharacterStream, onlineParser, RuleKinds, } from '../parser';\nimport { forEachState, getDefinitionState, getFieldDef, hintList, objectValues, } from './autocompleteUtils';\nexport const SuggestionCommand = {\n    command: 'editor.action.triggerSuggest',\n    title: 'Suggestions',\n};\nconst collectFragmentDefs = (op) => {\n    const externalFragments = [];\n    if (op) {\n        try {\n            visit(parse(op), {\n                FragmentDefinition(def) {\n                    externalFragments.push(def);\n                },\n            });\n        }\n        catch (_a) {\n            return [];\n        }\n    }\n    return externalFragments;\n};\nconst typeSystemKinds = [\n    Kind.SCHEMA_DEFINITION,\n    Kind.OPERATION_TYPE_DEFINITION,\n    Kind.SCALAR_TYPE_DEFINITION,\n    Kind.OBJECT_TYPE_DEFINITION,\n    Kind.INTERFACE_TYPE_DEFINITION,\n    Kind.UNION_TYPE_DEFINITION,\n    Kind.ENUM_TYPE_DEFINITION,\n    Kind.INPUT_OBJECT_TYPE_DEFINITION,\n    Kind.DIRECTIVE_DEFINITION,\n    Kind.SCHEMA_EXTENSION,\n    Kind.SCALAR_TYPE_EXTENSION,\n    Kind.OBJECT_TYPE_EXTENSION,\n    Kind.INTERFACE_TYPE_EXTENSION,\n    Kind.UNION_TYPE_EXTENSION,\n    Kind.ENUM_TYPE_EXTENSION,\n    Kind.INPUT_OBJECT_TYPE_EXTENSION,\n];\nconst hasTypeSystemDefinitions = (sdl) => {\n    let hasTypeSystemDef = false;\n    if (sdl) {\n        try {\n            visit(parse(sdl), {\n                enter(node) {\n                    if (node.kind === 'Document') {\n                        return;\n                    }\n                    if (typeSystemKinds.includes(node.kind)) {\n                        hasTypeSystemDef = true;\n                        return BREAK;\n                    }\n                    return false;\n                },\n            });\n        }\n        catch (_a) {\n            return hasTypeSystemDef;\n        }\n    }\n    return hasTypeSystemDef;\n};\nexport function getAutocompleteSuggestions(schema, queryText, cursor, contextToken, fragmentDefs, options) {\n    var _a;\n    const opts = Object.assign(Object.assign({}, options), { schema });\n    const token = contextToken || getTokenAtPosition(queryText, cursor, 1);\n    const state = token.state.kind === 'Invalid' ? token.state.prevState : token.state;\n    const mode = (options === null || options === void 0 ? void 0 : options.mode) || getDocumentMode(queryText, options === null || options === void 0 ? void 0 : options.uri);\n    if (!state) {\n        return [];\n    }\n    const { kind, step, prevState } = state;\n    const typeInfo = getTypeInfo(schema, token.state);\n    if (kind === RuleKinds.DOCUMENT) {\n        if (mode === GraphQLDocumentMode.TYPE_SYSTEM) {\n            return getSuggestionsForTypeSystemDefinitions(token);\n        }\n        return getSuggestionsForExecutableDefinitions(token);\n    }\n    if (kind === RuleKinds.EXTEND_DEF) {\n        return getSuggestionsForExtensionDefinitions(token);\n    }\n    if (((_a = prevState === null || prevState === void 0 ? void 0 : prevState.prevState) === null || _a === void 0 ? void 0 : _a.kind) === RuleKinds.EXTENSION_DEFINITION &&\n        state.name) {\n        return hintList(token, []);\n    }\n    if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.SCALAR_TYPE_EXTENSION) {\n        return hintList(token, Object.values(schema.getTypeMap())\n            .filter(isScalarType)\n            .map(type => ({\n            label: type.name,\n            kind: CompletionItemKind.Function,\n        })));\n    }\n    if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.OBJECT_TYPE_EXTENSION) {\n        return hintList(token, Object.values(schema.getTypeMap())\n            .filter(type => isObjectType(type) && !type.name.startsWith('__'))\n            .map(type => ({\n            label: type.name,\n            kind: CompletionItemKind.Function,\n        })));\n    }\n    if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.INTERFACE_TYPE_EXTENSION) {\n        return hintList(token, Object.values(schema.getTypeMap())\n            .filter(isInterfaceType)\n            .map(type => ({\n            label: type.name,\n            kind: CompletionItemKind.Function,\n        })));\n    }\n    if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.UNION_TYPE_EXTENSION) {\n        return hintList(token, Object.values(schema.getTypeMap())\n            .filter(isUnionType)\n            .map(type => ({\n            label: type.name,\n            kind: CompletionItemKind.Function,\n        })));\n    }\n    if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.ENUM_TYPE_EXTENSION) {\n        return hintList(token, Object.values(schema.getTypeMap())\n            .filter(type => isEnumType(type) && !type.name.startsWith('__'))\n            .map(type => ({\n            label: type.name,\n            kind: CompletionItemKind.Function,\n        })));\n    }\n    if ((prevState === null || prevState === void 0 ? void 0 : prevState.kind) === Kind.INPUT_OBJECT_TYPE_EXTENSION) {\n        return hintList(token, Object.values(schema.getTypeMap())\n            .filter(isInputObjectType)\n            .map(type => ({\n            label: type.name,\n            kind: CompletionItemKind.Function,\n        })));\n    }\n    if (kind === RuleKinds.IMPLEMENTS ||\n        (kind === RuleKinds.NAMED_TYPE && (prevState === null || prevState === void 0 ? void 0 : prevState.kind) === RuleKinds.IMPLEMENTS)) {\n        return getSuggestionsForImplements(token, state, schema, queryText, typeInfo);\n    }\n    if (kind === RuleKinds.SELECTION_SET ||\n        kind === RuleKinds.FIELD ||\n        kind === RuleKinds.ALIASED_FIELD) {\n        return getSuggestionsForFieldNames(token, typeInfo, opts);\n    }\n    if (kind === RuleKinds.ARGUMENTS ||\n        (kind === RuleKinds.ARGUMENT && step === 0)) {\n        const { argDefs } = typeInfo;\n        if (argDefs) {\n            return hintList(token, argDefs.map((argDef) => {\n                var _a;\n                return ({\n                    label: argDef.name,\n                    insertText: argDef.name + ': ',\n                    command: SuggestionCommand,\n                    detail: String(argDef.type),\n                    documentation: (_a = argDef.description) !== null && _a !== void 0 ? _a : undefined,\n                    kind: CompletionItemKind.Variable,\n                    type: argDef.type,\n                });\n            }));\n        }\n    }\n    if ((kind === RuleKinds.OBJECT_VALUE ||\n        (kind === RuleKinds.OBJECT_FIELD && step === 0)) &&\n        typeInfo.objectFieldDefs) {\n        const objectFields = objectValues(typeInfo.objectFieldDefs);\n        const completionKind = kind === RuleKinds.OBJECT_VALUE\n            ? CompletionItemKind.Value\n            : CompletionItemKind.Field;\n        return hintList(token, objectFields.map(field => {\n            var _a;\n            return ({\n                label: field.name,\n                detail: String(field.type),\n                documentation: (_a = field.description) !== null && _a !== void 0 ? _a : undefined,\n                kind: completionKind,\n                type: field.type,\n            });\n        }));\n    }\n    if (kind === RuleKinds.ENUM_VALUE ||\n        (kind === RuleKinds.LIST_VALUE && step === 1) ||\n        (kind === RuleKinds.OBJECT_FIELD && step === 2) ||\n        (kind === RuleKinds.ARGUMENT && step === 2)) {\n        return getSuggestionsForInputValues(token, typeInfo, queryText, schema);\n    }\n    if (kind === RuleKinds.VARIABLE && step === 1) {\n        const namedInputType = getNamedType(typeInfo.inputType);\n        const variableDefinitions = getVariableCompletions(queryText, schema, token);\n        return hintList(token, variableDefinitions.filter(v => v.detail === (namedInputType === null || namedInputType === void 0 ? void 0 : namedInputType.name)));\n    }\n    if ((kind === RuleKinds.TYPE_CONDITION && step === 1) ||\n        (kind === RuleKinds.NAMED_TYPE &&\n            prevState != null &&\n            prevState.kind === RuleKinds.TYPE_CONDITION)) {\n        return getSuggestionsForFragmentTypeConditions(token, typeInfo, schema, kind);\n    }\n    if (kind === RuleKinds.FRAGMENT_SPREAD && step === 1) {\n        return getSuggestionsForFragmentSpread(token, typeInfo, schema, queryText, Array.isArray(fragmentDefs)\n            ? fragmentDefs\n            : collectFragmentDefs(fragmentDefs));\n    }\n    const unwrappedState = unwrapType(state);\n    if ((mode === GraphQLDocumentMode.TYPE_SYSTEM &&\n        !unwrappedState.needsAdvance &&\n        kind === RuleKinds.NAMED_TYPE) ||\n        kind === RuleKinds.LIST_TYPE) {\n        if (unwrappedState.kind === RuleKinds.FIELD_DEF) {\n            return hintList(token, Object.values(schema.getTypeMap())\n                .filter(type => isOutputType(type) && !type.name.startsWith('__'))\n                .map(type => ({\n                label: type.name,\n                kind: CompletionItemKind.Function,\n            })));\n        }\n        if (unwrappedState.kind === RuleKinds.INPUT_VALUE_DEF) {\n            return hintList(token, Object.values(schema.getTypeMap())\n                .filter(type => isInputType(type) && !type.name.startsWith('__'))\n                .map(type => ({\n                label: type.name,\n                kind: CompletionItemKind.Function,\n            })));\n        }\n    }\n    if ((kind === RuleKinds.VARIABLE_DEFINITION && step === 2) ||\n        (kind === RuleKinds.LIST_TYPE && step === 1) ||\n        (kind === RuleKinds.NAMED_TYPE &&\n            prevState &&\n            (prevState.kind === RuleKinds.VARIABLE_DEFINITION ||\n                prevState.kind === RuleKinds.LIST_TYPE ||\n                prevState.kind === RuleKinds.NON_NULL_TYPE))) {\n        return getSuggestionsForVariableDefinition(token, schema, kind);\n    }\n    if (kind === RuleKinds.DIRECTIVE) {\n        return getSuggestionsForDirective(token, state, schema, kind);\n    }\n    return [];\n}\nconst insertSuffix = ' {\\n  $1\\n}';\nconst getInsertText = (field) => {\n    const { type } = field;\n    if (isCompositeType(type)) {\n        return insertSuffix;\n    }\n    if (isListType(type) && isCompositeType(type.ofType)) {\n        return insertSuffix;\n    }\n    if (isNonNullType(type)) {\n        if (isCompositeType(type.ofType)) {\n            return insertSuffix;\n        }\n        if (isListType(type.ofType) && isCompositeType(type.ofType.ofType)) {\n            return insertSuffix;\n        }\n    }\n    return null;\n};\nfunction getSuggestionsForTypeSystemDefinitions(token) {\n    return hintList(token, [\n        { label: 'extend', kind: CompletionItemKind.Function },\n        { label: 'type', kind: CompletionItemKind.Function },\n        { label: 'interface', kind: CompletionItemKind.Function },\n        { label: 'union', kind: CompletionItemKind.Function },\n        { label: 'input', kind: CompletionItemKind.Function },\n        { label: 'scalar', kind: CompletionItemKind.Function },\n        { label: 'schema', kind: CompletionItemKind.Function },\n    ]);\n}\nfunction getSuggestionsForExecutableDefinitions(token) {\n    return hintList(token, [\n        { label: 'query', kind: CompletionItemKind.Function },\n        { label: 'mutation', kind: CompletionItemKind.Function },\n        { label: 'subscription', kind: CompletionItemKind.Function },\n        { label: 'fragment', kind: CompletionItemKind.Function },\n        { label: '{', kind: CompletionItemKind.Constructor },\n    ]);\n}\nfunction getSuggestionsForExtensionDefinitions(token) {\n    return hintList(token, [\n        { label: 'type', kind: CompletionItemKind.Function },\n        { label: 'interface', kind: CompletionItemKind.Function },\n        { label: 'union', kind: CompletionItemKind.Function },\n        { label: 'input', kind: CompletionItemKind.Function },\n        { label: 'scalar', kind: CompletionItemKind.Function },\n        { label: 'schema', kind: CompletionItemKind.Function },\n    ]);\n}\nfunction getSuggestionsForFieldNames(token, typeInfo, options) {\n    var _a;\n    if (typeInfo.parentType) {\n        const { parentType } = typeInfo;\n        let fields = [];\n        if ('getFields' in parentType) {\n            fields = objectValues(parentType.getFields());\n        }\n        if (isCompositeType(parentType)) {\n            fields.push(TypeNameMetaFieldDef);\n        }\n        if (parentType === ((_a = options === null || options === void 0 ? void 0 : options.schema) === null || _a === void 0 ? void 0 : _a.getQueryType())) {\n            fields.push(SchemaMetaFieldDef, TypeMetaFieldDef);\n        }\n        return hintList(token, fields.map((field, index) => {\n            var _a;\n            const suggestion = {\n                sortText: String(index) + field.name,\n                label: field.name,\n                detail: String(field.type),\n                documentation: (_a = field.description) !== null && _a !== void 0 ? _a : undefined,\n                deprecated: Boolean(field.deprecationReason),\n                isDeprecated: Boolean(field.deprecationReason),\n                deprecationReason: field.deprecationReason,\n                kind: CompletionItemKind.Field,\n                type: field.type,\n            };\n            if (options === null || options === void 0 ? void 0 : options.fillLeafsOnComplete) {\n                const insertText = getInsertText(field);\n                if (insertText) {\n                    suggestion.insertText = field.name + insertText;\n                    suggestion.insertTextFormat = InsertTextFormat.Snippet;\n                    suggestion.command = SuggestionCommand;\n                }\n            }\n            return suggestion;\n        }));\n    }\n    return [];\n}\nfunction getSuggestionsForInputValues(token, typeInfo, queryText, schema) {\n    const namedInputType = getNamedType(typeInfo.inputType);\n    const queryVariables = getVariableCompletions(queryText, schema, token).filter(v => v.detail === namedInputType.name);\n    if (namedInputType instanceof GraphQLEnumType) {\n        const values = namedInputType.getValues();\n        return hintList(token, values\n            .map((value) => {\n            var _a;\n            return ({\n                label: value.name,\n                detail: String(namedInputType),\n                documentation: (_a = value.description) !== null && _a !== void 0 ? _a : undefined,\n                deprecated: Boolean(value.deprecationReason),\n                isDeprecated: Boolean(value.deprecationReason),\n                deprecationReason: value.deprecationReason,\n                kind: CompletionItemKind.EnumMember,\n                type: namedInputType,\n            });\n        })\n            .concat(queryVariables));\n    }\n    if (namedInputType === GraphQLBoolean) {\n        return hintList(token, queryVariables.concat([\n            {\n                label: 'true',\n                detail: String(GraphQLBoolean),\n                documentation: 'Not false.',\n                kind: CompletionItemKind.Variable,\n                type: GraphQLBoolean,\n            },\n            {\n                label: 'false',\n                detail: String(GraphQLBoolean),\n                documentation: 'Not true.',\n                kind: CompletionItemKind.Variable,\n                type: GraphQLBoolean,\n            },\n        ]));\n    }\n    return queryVariables;\n}\nfunction getSuggestionsForImplements(token, tokenState, schema, documentText, typeInfo) {\n    if (tokenState.needsSeparator) {\n        return [];\n    }\n    const typeMap = schema.getTypeMap();\n    const schemaInterfaces = objectValues(typeMap).filter(isInterfaceType);\n    const schemaInterfaceNames = schemaInterfaces.map(({ name }) => name);\n    const inlineInterfaces = new Set();\n    runOnlineParser(documentText, (_, state) => {\n        var _a, _b, _c, _d, _e;\n        if (state.name) {\n            if (state.kind === RuleKinds.INTERFACE_DEF &&\n                !schemaInterfaceNames.includes(state.name)) {\n                inlineInterfaces.add(state.name);\n            }\n            if (state.kind === RuleKinds.NAMED_TYPE &&\n                ((_a = state.prevState) === null || _a === void 0 ? void 0 : _a.kind) === RuleKinds.IMPLEMENTS) {\n                if (typeInfo.interfaceDef) {\n                    const existingType = (_b = typeInfo.interfaceDef) === null || _b === void 0 ? void 0 : _b.getInterfaces().find(({ name }) => name === state.name);\n                    if (existingType) {\n                        return;\n                    }\n                    const type = schema.getType(state.name);\n                    const interfaceConfig = (_c = typeInfo.interfaceDef) === null || _c === void 0 ? void 0 : _c.toConfig();\n                    typeInfo.interfaceDef = new GraphQLInterfaceType(Object.assign(Object.assign({}, interfaceConfig), { interfaces: [\n                            ...interfaceConfig.interfaces,\n                            type ||\n                                new GraphQLInterfaceType({ name: state.name, fields: {} }),\n                        ] }));\n                }\n                else if (typeInfo.objectTypeDef) {\n                    const existingType = (_d = typeInfo.objectTypeDef) === null || _d === void 0 ? void 0 : _d.getInterfaces().find(({ name }) => name === state.name);\n                    if (existingType) {\n                        return;\n                    }\n                    const type = schema.getType(state.name);\n                    const objectTypeConfig = (_e = typeInfo.objectTypeDef) === null || _e === void 0 ? void 0 : _e.toConfig();\n                    typeInfo.objectTypeDef = new GraphQLObjectType(Object.assign(Object.assign({}, objectTypeConfig), { interfaces: [\n                            ...objectTypeConfig.interfaces,\n                            type ||\n                                new GraphQLInterfaceType({ name: state.name, fields: {} }),\n                        ] }));\n                }\n            }\n        }\n    });\n    const currentTypeToExtend = typeInfo.interfaceDef || typeInfo.objectTypeDef;\n    const siblingInterfaces = (currentTypeToExtend === null || currentTypeToExtend === void 0 ? void 0 : currentTypeToExtend.getInterfaces()) || [];\n    const siblingInterfaceNames = siblingInterfaces.map(({ name }) => name);\n    const possibleInterfaces = schemaInterfaces\n        .concat([...inlineInterfaces].map(name => ({ name })))\n        .filter(({ name }) => name !== (currentTypeToExtend === null || currentTypeToExtend === void 0 ? void 0 : currentTypeToExtend.name) &&\n        !siblingInterfaceNames.includes(name));\n    return hintList(token, possibleInterfaces.map(type => {\n        const result = {\n            label: type.name,\n            kind: CompletionItemKind.Interface,\n            type,\n        };\n        if (type === null || type === void 0 ? void 0 : type.description) {\n            result.documentation = type.description;\n        }\n        return result;\n    }));\n}\nfunction getSuggestionsForFragmentTypeConditions(token, typeInfo, schema, _kind) {\n    let possibleTypes;\n    if (typeInfo.parentType) {\n        if (isAbstractType(typeInfo.parentType)) {\n            const abstractType = assertAbstractType(typeInfo.parentType);\n            const possibleObjTypes = schema.getPossibleTypes(abstractType);\n            const possibleIfaceMap = Object.create(null);\n            for (const type of possibleObjTypes) {\n                for (const iface of type.getInterfaces()) {\n                    possibleIfaceMap[iface.name] = iface;\n                }\n            }\n            possibleTypes = possibleObjTypes.concat(objectValues(possibleIfaceMap));\n        }\n        else {\n            possibleTypes = [typeInfo.parentType];\n        }\n    }\n    else {\n        const typeMap = schema.getTypeMap();\n        possibleTypes = objectValues(typeMap).filter(type => isCompositeType(type) && !type.name.startsWith('__'));\n    }\n    return hintList(token, possibleTypes.map(type => {\n        const namedType = getNamedType(type);\n        return {\n            label: String(type),\n            documentation: (namedType === null || namedType === void 0 ? void 0 : namedType.description) || '',\n            kind: CompletionItemKind.Field,\n        };\n    }));\n}\nfunction getSuggestionsForFragmentSpread(token, typeInfo, schema, queryText, fragmentDefs) {\n    if (!queryText) {\n        return [];\n    }\n    const typeMap = schema.getTypeMap();\n    const defState = getDefinitionState(token.state);\n    const fragments = getFragmentDefinitions(queryText);\n    if (fragmentDefs && fragmentDefs.length > 0) {\n        fragments.push(...fragmentDefs);\n    }\n    const relevantFrags = fragments.filter(frag => typeMap[frag.typeCondition.name.value] &&\n        !(defState &&\n            defState.kind === RuleKinds.FRAGMENT_DEFINITION &&\n            defState.name === frag.name.value) &&\n        isCompositeType(typeInfo.parentType) &&\n        isCompositeType(typeMap[frag.typeCondition.name.value]) &&\n        doTypesOverlap(schema, typeInfo.parentType, typeMap[frag.typeCondition.name.value]));\n    return hintList(token, relevantFrags.map(frag => ({\n        label: frag.name.value,\n        detail: String(typeMap[frag.typeCondition.name.value]),\n        documentation: `fragment ${frag.name.value} on ${frag.typeCondition.name.value}`,\n        kind: CompletionItemKind.Field,\n        type: typeMap[frag.typeCondition.name.value],\n    })));\n}\nconst getParentDefinition = (state, kind) => {\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;\n    if (((_a = state.prevState) === null || _a === void 0 ? void 0 : _a.kind) === kind) {\n        return state.prevState;\n    }\n    if (((_c = (_b = state.prevState) === null || _b === void 0 ? void 0 : _b.prevState) === null || _c === void 0 ? void 0 : _c.kind) === kind) {\n        return state.prevState.prevState;\n    }\n    if (((_f = (_e = (_d = state.prevState) === null || _d === void 0 ? void 0 : _d.prevState) === null || _e === void 0 ? void 0 : _e.prevState) === null || _f === void 0 ? void 0 : _f.kind) === kind) {\n        return state.prevState.prevState.prevState;\n    }\n    if (((_k = (_j = (_h = (_g = state.prevState) === null || _g === void 0 ? void 0 : _g.prevState) === null || _h === void 0 ? void 0 : _h.prevState) === null || _j === void 0 ? void 0 : _j.prevState) === null || _k === void 0 ? void 0 : _k.kind) === kind) {\n        return state.prevState.prevState.prevState.prevState;\n    }\n};\nexport function getVariableCompletions(queryText, schema, token) {\n    let variableName = null;\n    let variableType;\n    const definitions = Object.create({});\n    runOnlineParser(queryText, (_, state) => {\n        if ((state === null || state === void 0 ? void 0 : state.kind) === RuleKinds.VARIABLE && state.name) {\n            variableName = state.name;\n        }\n        if ((state === null || state === void 0 ? void 0 : state.kind) === RuleKinds.NAMED_TYPE && variableName) {\n            const parentDefinition = getParentDefinition(state, RuleKinds.TYPE);\n            if (parentDefinition === null || parentDefinition === void 0 ? void 0 : parentDefinition.type) {\n                variableType = schema.getType(parentDefinition === null || parentDefinition === void 0 ? void 0 : parentDefinition.type);\n            }\n        }\n        if (variableName && variableType && !definitions[variableName]) {\n            definitions[variableName] = {\n                detail: variableType.toString(),\n                insertText: token.string === '$' ? variableName : '$' + variableName,\n                label: variableName,\n                type: variableType,\n                kind: CompletionItemKind.Variable,\n            };\n            variableName = null;\n            variableType = null;\n        }\n    });\n    return objectValues(definitions);\n}\nexport function getFragmentDefinitions(queryText) {\n    const fragmentDefs = [];\n    runOnlineParser(queryText, (_, state) => {\n        if (state.kind === RuleKinds.FRAGMENT_DEFINITION &&\n            state.name &&\n            state.type) {\n            fragmentDefs.push({\n                kind: RuleKinds.FRAGMENT_DEFINITION,\n                name: {\n                    kind: Kind.NAME,\n                    value: state.name,\n                },\n                selectionSet: {\n                    kind: RuleKinds.SELECTION_SET,\n                    selections: [],\n                },\n                typeCondition: {\n                    kind: RuleKinds.NAMED_TYPE,\n                    name: {\n                        kind: Kind.NAME,\n                        value: state.type,\n                    },\n                },\n            });\n        }\n    });\n    return fragmentDefs;\n}\nfunction getSuggestionsForVariableDefinition(token, schema, _kind) {\n    const inputTypeMap = schema.getTypeMap();\n    const inputTypes = objectValues(inputTypeMap).filter(isInputType);\n    return hintList(token, inputTypes.map((type) => ({\n        label: type.name,\n        documentation: type.description,\n        kind: CompletionItemKind.Variable,\n    })));\n}\nfunction getSuggestionsForDirective(token, state, schema, _kind) {\n    var _a;\n    if ((_a = state.prevState) === null || _a === void 0 ? void 0 : _a.kind) {\n        const directives = schema\n            .getDirectives()\n            .filter(directive => canUseDirective(state.prevState, directive));\n        return hintList(token, directives.map(directive => ({\n            label: directive.name,\n            documentation: directive.description || '',\n            kind: CompletionItemKind.Function,\n        })));\n    }\n    return [];\n}\nexport function getTokenAtPosition(queryText, cursor, offset = 0) {\n    let styleAtCursor = null;\n    let stateAtCursor = null;\n    let stringAtCursor = null;\n    const token = runOnlineParser(queryText, (stream, state, style, index) => {\n        if (index !== cursor.line ||\n            stream.getCurrentPosition() + offset < cursor.character + 1) {\n            return;\n        }\n        styleAtCursor = style;\n        stateAtCursor = Object.assign({}, state);\n        stringAtCursor = stream.current();\n        return 'BREAK';\n    });\n    return {\n        start: token.start,\n        end: token.end,\n        string: stringAtCursor || token.string,\n        state: stateAtCursor || token.state,\n        style: styleAtCursor || token.style,\n    };\n}\nexport function runOnlineParser(queryText, callback) {\n    const lines = queryText.split('\\n');\n    const parser = onlineParser();\n    let state = parser.startState();\n    let style = '';\n    let stream = new CharacterStream('');\n    for (let i = 0; i < lines.length; i++) {\n        stream = new CharacterStream(lines[i]);\n        while (!stream.eol()) {\n            style = parser.token(stream, state);\n            const code = callback(stream, state, style, i);\n            if (code === 'BREAK') {\n                break;\n            }\n        }\n        callback(stream, state, style, i);\n        if (!state.kind) {\n            state = parser.startState();\n        }\n    }\n    return {\n        start: stream.getStartOfToken(),\n        end: stream.getCurrentPosition(),\n        string: stream.current(),\n        state,\n        style,\n    };\n}\nexport function canUseDirective(state, directive) {\n    if (!(state === null || state === void 0 ? void 0 : state.kind)) {\n        return false;\n    }\n    const { kind, prevState } = state;\n    const { locations } = directive;\n    switch (kind) {\n        case RuleKinds.QUERY:\n            return locations.includes(DirectiveLocation.QUERY);\n        case RuleKinds.MUTATION:\n            return locations.includes(DirectiveLocation.MUTATION);\n        case RuleKinds.SUBSCRIPTION:\n            return locations.includes(DirectiveLocation.SUBSCRIPTION);\n        case RuleKinds.FIELD:\n        case RuleKinds.ALIASED_FIELD:\n            return locations.includes(DirectiveLocation.FIELD);\n        case RuleKinds.FRAGMENT_DEFINITION:\n            return locations.includes(DirectiveLocation.FRAGMENT_DEFINITION);\n        case RuleKinds.FRAGMENT_SPREAD:\n            return locations.includes(DirectiveLocation.FRAGMENT_SPREAD);\n        case RuleKinds.INLINE_FRAGMENT:\n            return locations.includes(DirectiveLocation.INLINE_FRAGMENT);\n        case RuleKinds.SCHEMA_DEF:\n            return locations.includes(DirectiveLocation.SCHEMA);\n        case RuleKinds.SCALAR_DEF:\n            return locations.includes(DirectiveLocation.SCALAR);\n        case RuleKinds.OBJECT_TYPE_DEF:\n            return locations.includes(DirectiveLocation.OBJECT);\n        case RuleKinds.FIELD_DEF:\n            return locations.includes(DirectiveLocation.FIELD_DEFINITION);\n        case RuleKinds.INTERFACE_DEF:\n            return locations.includes(DirectiveLocation.INTERFACE);\n        case RuleKinds.UNION_DEF:\n            return locations.includes(DirectiveLocation.UNION);\n        case RuleKinds.ENUM_DEF:\n            return locations.includes(DirectiveLocation.ENUM);\n        case RuleKinds.ENUM_VALUE:\n            return locations.includes(DirectiveLocation.ENUM_VALUE);\n        case RuleKinds.INPUT_DEF:\n            return locations.includes(DirectiveLocation.INPUT_OBJECT);\n        case RuleKinds.INPUT_VALUE_DEF:\n            const prevStateKind = prevState === null || prevState === void 0 ? void 0 : prevState.kind;\n            switch (prevStateKind) {\n                case RuleKinds.ARGUMENTS_DEF:\n                    return locations.includes(DirectiveLocation.ARGUMENT_DEFINITION);\n                case RuleKinds.INPUT_DEF:\n                    return locations.includes(DirectiveLocation.INPUT_FIELD_DEFINITION);\n            }\n    }\n    return false;\n}\nexport function getTypeInfo(schema, tokenState) {\n    let argDef;\n    let argDefs;\n    let directiveDef;\n    let enumValue;\n    let fieldDef;\n    let inputType;\n    let objectTypeDef;\n    let objectFieldDefs;\n    let parentType;\n    let type;\n    let interfaceDef;\n    forEachState(tokenState, state => {\n        var _a;\n        switch (state.kind) {\n            case RuleKinds.QUERY:\n            case 'ShortQuery':\n                type = schema.getQueryType();\n                break;\n            case RuleKinds.MUTATION:\n                type = schema.getMutationType();\n                break;\n            case RuleKinds.SUBSCRIPTION:\n                type = schema.getSubscriptionType();\n                break;\n            case RuleKinds.INLINE_FRAGMENT:\n            case RuleKinds.FRAGMENT_DEFINITION:\n                if (state.type) {\n                    type = schema.getType(state.type);\n                }\n                break;\n            case RuleKinds.FIELD:\n            case RuleKinds.ALIASED_FIELD: {\n                if (!type || !state.name) {\n                    fieldDef = null;\n                }\n                else {\n                    fieldDef = parentType\n                        ? getFieldDef(schema, parentType, state.name)\n                        : null;\n                    type = fieldDef ? fieldDef.type : null;\n                }\n                break;\n            }\n            case RuleKinds.SELECTION_SET:\n                parentType = getNamedType(type);\n                break;\n            case RuleKinds.DIRECTIVE:\n                directiveDef = state.name ? schema.getDirective(state.name) : null;\n                break;\n            case RuleKinds.INTERFACE_DEF:\n                if (state.name) {\n                    objectTypeDef = null;\n                    interfaceDef = new GraphQLInterfaceType({\n                        name: state.name,\n                        interfaces: [],\n                        fields: {},\n                    });\n                }\n                break;\n            case RuleKinds.OBJECT_TYPE_DEF:\n                if (state.name) {\n                    interfaceDef = null;\n                    objectTypeDef = new GraphQLObjectType({\n                        name: state.name,\n                        interfaces: [],\n                        fields: {},\n                    });\n                }\n                break;\n            case RuleKinds.ARGUMENTS: {\n                if (state.prevState) {\n                    switch (state.prevState.kind) {\n                        case RuleKinds.FIELD:\n                            argDefs = fieldDef && fieldDef.args;\n                            break;\n                        case RuleKinds.DIRECTIVE:\n                            argDefs =\n                                directiveDef && directiveDef.args;\n                            break;\n                        case RuleKinds.ALIASED_FIELD: {\n                            const name = (_a = state.prevState) === null || _a === void 0 ? void 0 : _a.name;\n                            if (!name) {\n                                argDefs = null;\n                                break;\n                            }\n                            const field = parentType\n                                ? getFieldDef(schema, parentType, name)\n                                : null;\n                            if (!field) {\n                                argDefs = null;\n                                break;\n                            }\n                            argDefs = field.args;\n                            break;\n                        }\n                        default:\n                            argDefs = null;\n                            break;\n                    }\n                }\n                else {\n                    argDefs = null;\n                }\n                break;\n            }\n            case RuleKinds.ARGUMENT:\n                if (argDefs) {\n                    for (let i = 0; i < argDefs.length; i++) {\n                        if (argDefs[i].name === state.name) {\n                            argDef = argDefs[i];\n                            break;\n                        }\n                    }\n                }\n                inputType = argDef === null || argDef === void 0 ? void 0 : argDef.type;\n                break;\n            case RuleKinds.ENUM_VALUE:\n                const enumType = getNamedType(inputType);\n                enumValue =\n                    enumType instanceof GraphQLEnumType\n                        ? enumType\n                            .getValues()\n                            .find((val) => val.value === state.name)\n                        : null;\n                break;\n            case RuleKinds.LIST_VALUE:\n                const nullableType = getNullableType(inputType);\n                inputType =\n                    nullableType instanceof GraphQLList ? nullableType.ofType : null;\n                break;\n            case RuleKinds.OBJECT_VALUE:\n                const objectType = getNamedType(inputType);\n                objectFieldDefs =\n                    objectType instanceof GraphQLInputObjectType\n                        ? objectType.getFields()\n                        : null;\n                break;\n            case RuleKinds.OBJECT_FIELD:\n                const objectField = state.name && objectFieldDefs ? objectFieldDefs[state.name] : null;\n                inputType = objectField === null || objectField === void 0 ? void 0 : objectField.type;\n                break;\n            case RuleKinds.NAMED_TYPE:\n                if (state.name) {\n                    type = schema.getType(state.name);\n                }\n                break;\n        }\n    });\n    return {\n        argDef,\n        argDefs,\n        directiveDef,\n        enumValue,\n        fieldDef,\n        inputType,\n        objectFieldDefs,\n        parentType,\n        type,\n        interfaceDef,\n        objectTypeDef,\n    };\n}\nexport var GraphQLDocumentMode;\n(function (GraphQLDocumentMode) {\n    GraphQLDocumentMode[\"TYPE_SYSTEM\"] = \"TYPE_SYSTEM\";\n    GraphQLDocumentMode[\"EXECUTABLE\"] = \"EXECUTABLE\";\n})(GraphQLDocumentMode || (GraphQLDocumentMode = {}));\nfunction getDocumentMode(documentText, uri) {\n    if (uri === null || uri === void 0 ? void 0 : uri.endsWith('.graphqls')) {\n        return GraphQLDocumentMode.TYPE_SYSTEM;\n    }\n    return hasTypeSystemDefinitions(documentText)\n        ? GraphQLDocumentMode.TYPE_SYSTEM\n        : GraphQLDocumentMode.EXECUTABLE;\n}\nfunction unwrapType(state) {\n    if (state.prevState &&\n        state.kind &&\n        [\n            RuleKinds.NAMED_TYPE,\n            RuleKinds.LIST_TYPE,\n            RuleKinds.TYPE,\n            RuleKinds.NON_NULL_TYPE,\n        ].includes(state.kind)) {\n        return unwrapType(state.prevState);\n    }\n    return state;\n}\n//# sourceMappingURL=getAutocompleteSuggestions.js.map", "import { Graph<PERSON><PERSON>on<PERSON>ull, GraphQLList, } from 'graphql';\nimport { getTokenAtPosition, getTypeInfo } from './getAutocompleteSuggestions';\nexport function getHoverInformation(schema, queryText, cursor, contextToken, config) {\n    const token = contextToken || getTokenAtPosition(queryText, cursor);\n    if (!schema || !token || !token.state) {\n        return '';\n    }\n    const { kind, step } = token.state;\n    const typeInfo = getTypeInfo(schema, token.state);\n    const options = Object.assign(Object.assign({}, config), { schema });\n    if ((kind === 'Field' && step === 0 && typeInfo.fieldDef) ||\n        (kind === 'AliasedField' && step === 2 && typeInfo.fieldDef)) {\n        const into = [];\n        renderMdCodeStart(into, options);\n        renderField(into, typeInfo, options);\n        renderMdCodeEnd(into, options);\n        renderDescription(into, options, typeInfo.fieldDef);\n        return into.join('').trim();\n    }\n    if (kind === 'Directive' && step === 1 && typeInfo.directiveDef) {\n        const into = [];\n        renderMdCodeStart(into, options);\n        renderDirective(into, typeInfo, options);\n        renderMdCodeEnd(into, options);\n        renderDescription(into, options, typeInfo.directiveDef);\n        return into.join('').trim();\n    }\n    if (kind === 'Argument' && step === 0 && typeInfo.argDef) {\n        const into = [];\n        renderMdCodeStart(into, options);\n        renderArg(into, typeInfo, options);\n        renderMdCodeEnd(into, options);\n        renderDescription(into, options, typeInfo.argDef);\n        return into.join('').trim();\n    }\n    if (kind === 'EnumValue' &&\n        typeInfo.enumValue &&\n        'description' in typeInfo.enumValue) {\n        const into = [];\n        renderMdCodeStart(into, options);\n        renderEnumValue(into, typeInfo, options);\n        renderMdCodeEnd(into, options);\n        renderDescription(into, options, typeInfo.enumValue);\n        return into.join('').trim();\n    }\n    if (kind === 'NamedType' && typeInfo.type && 'description' in typeInfo.type) {\n        const into = [];\n        renderMdCodeStart(into, options);\n        renderType(into, typeInfo, options, typeInfo.type);\n        renderMdCodeEnd(into, options);\n        renderDescription(into, options, typeInfo.type);\n        return into.join('').trim();\n    }\n    return '';\n}\nfunction renderMdCodeStart(into, options) {\n    if (options.useMarkdown) {\n        text(into, '```graphql\\n');\n    }\n}\nfunction renderMdCodeEnd(into, options) {\n    if (options.useMarkdown) {\n        text(into, '\\n```');\n    }\n}\nfunction renderField(into, typeInfo, options) {\n    renderQualifiedField(into, typeInfo, options);\n    renderTypeAnnotation(into, typeInfo, options, typeInfo.type);\n}\nfunction renderQualifiedField(into, typeInfo, options) {\n    if (!typeInfo.fieldDef) {\n        return;\n    }\n    const fieldName = typeInfo.fieldDef.name;\n    if (fieldName.slice(0, 2) !== '__') {\n        renderType(into, typeInfo, options, typeInfo.parentType);\n        text(into, '.');\n    }\n    text(into, fieldName);\n}\nfunction renderDirective(into, typeInfo, _options) {\n    if (!typeInfo.directiveDef) {\n        return;\n    }\n    const name = '@' + typeInfo.directiveDef.name;\n    text(into, name);\n}\nfunction renderArg(into, typeInfo, options) {\n    if (typeInfo.directiveDef) {\n        renderDirective(into, typeInfo, options);\n    }\n    else if (typeInfo.fieldDef) {\n        renderQualifiedField(into, typeInfo, options);\n    }\n    if (!typeInfo.argDef) {\n        return;\n    }\n    const { name } = typeInfo.argDef;\n    text(into, '(');\n    text(into, name);\n    renderTypeAnnotation(into, typeInfo, options, typeInfo.inputType);\n    text(into, ')');\n}\nfunction renderTypeAnnotation(into, typeInfo, options, t) {\n    text(into, ': ');\n    renderType(into, typeInfo, options, t);\n}\nfunction renderEnumValue(into, typeInfo, options) {\n    if (!typeInfo.enumValue) {\n        return;\n    }\n    const { name } = typeInfo.enumValue;\n    renderType(into, typeInfo, options, typeInfo.inputType);\n    text(into, '.');\n    text(into, name);\n}\nfunction renderType(into, typeInfo, options, t) {\n    if (!t) {\n        return;\n    }\n    if (t instanceof GraphQLNonNull) {\n        renderType(into, typeInfo, options, t.ofType);\n        text(into, '!');\n    }\n    else if (t instanceof GraphQLList) {\n        text(into, '[');\n        renderType(into, typeInfo, options, t.ofType);\n        text(into, ']');\n    }\n    else {\n        text(into, t.name);\n    }\n}\nfunction renderDescription(into, options, def) {\n    if (!def) {\n        return;\n    }\n    const description = typeof def.description === 'string' ? def.description : null;\n    if (description) {\n        text(into, '\\n\\n');\n        text(into, description);\n    }\n    renderDeprecation(into, options, def);\n}\nfunction renderDeprecation(into, _options, def) {\n    if (!def) {\n        return;\n    }\n    const reason = def.deprecationReason || null;\n    if (!reason) {\n        return;\n    }\n    text(into, '\\n\\n');\n    text(into, 'Deprecated: ');\n    text(into, reason);\n}\nfunction text(into, content) {\n    into.push(content);\n}\n//# sourceMappingURL=getHoverInformation.js.map", "import { IPosition } from 'graphql-language-service';\n\nexport class Cursor implements IPosition {\n  line: number;\n  character: number;\n\n  constructor(line: number, char: number) {\n    this.line = line;\n    this.character = char;\n  }\n\n  setLine(line: number) {\n    this.line = line;\n  }\n\n  setCharacter(character: number) {\n    this.character = character;\n  }\n\n  lessThanOrEqualTo(position: IPosition) {\n    return (\n      this.line < position.line ||\n      (this.line === position.line && this.character <= position.character)\n    );\n  }\n}\n", "import { ts } from '../ts';\nimport { onlineParser, State, CharacterStream } from 'graphql-language-service';\n\nexport interface Token {\n  start: number;\n  end: number;\n  string: string;\n  tokenKind: string;\n  line: number;\n  state: State;\n}\n\nexport const getToken = (\n  template: ts.Expression,\n  cursorPosition: number\n): Token | undefined => {\n  if (!ts.isTemplateLiteral(template) && !ts.isStringLiteralLike(template)) {\n    return undefined;\n  }\n\n  const text = template.getText().slice(1, -1);\n  const input = text.split('\\n');\n  const parser = onlineParser();\n  const state = parser.startState();\n  let cPos = template.getStart() + 1;\n\n  let foundToken: Token | undefined = undefined;\n  let prevToken: Token | undefined = undefined;\n  for (let line = 0; line < input.length; line++) {\n    if (foundToken) continue;\n    const lPos = cPos - 1;\n    const stream = new CharacterStream(input[line] + '\\n');\n    while (!stream.eol()) {\n      const token = parser.token(stream, state);\n      const string = stream.current();\n\n      if (\n        lPos + stream.getStartOfToken() + 1 <= cursorPosition &&\n        lPos + stream.getCurrentPosition() >= cursorPosition\n      ) {\n        foundToken = prevToken\n          ? prevToken\n          : {\n              line,\n              start: stream.getStartOfToken() + 1,\n              end: stream.getCurrentPosition(),\n              string,\n              state,\n              tokenKind: token,\n            };\n        break;\n      } else if (string === 'on') {\n        prevToken = {\n          line,\n          start: stream.getStartOfToken() + 1,\n          end: stream.getCurrentPosition(),\n          string,\n          state,\n          tokenKind: token,\n        };\n      } else if (string === '.' || string === '..') {\n        prevToken = {\n          line,\n          start: stream.getStartOfToken() + 1,\n          end: stream.getCurrentPosition(),\n          string,\n          state,\n          tokenKind: token,\n        };\n      } else {\n        prevToken = undefined;\n      }\n    }\n\n    cPos += input[line]!.length + 1;\n  }\n\n  return foundToken;\n};\n", "import {\n  CompletionItem,\n  Completion<PERSON>temKind,\n  ContextToken,\n  ContextTokenUnion,\n  Maybe,\n  RuleKinds,\n  getDefinitionState,\n} from 'graphql-language-service';\nimport {\n  FragmentDefinitionNode,\n  GraphQLArgument,\n  GraphQLCompositeType,\n  GraphQLDirective,\n  GraphQLEnumValue,\n  GraphQLField,\n  GraphQLInputFieldMap,\n  GraphQLInterfaceType,\n  GraphQLObjectType,\n  GraphQLSchema,\n  GraphQLType,\n  doTypesOverlap,\n  isCompositeType,\n} from 'graphql';\n\n/**\n * This part is vendored from https://github.com/graphql/graphiql/blob/main/packages/graphql-language-service/src/interface/autocompleteUtils.ts#L97\n */\ntype CompletionItemBase = {\n  label: string;\n  isDeprecated?: boolean;\n};\n\n// Create the expected hint response given a possible list and a token\nfunction hintList<T extends CompletionItemBase>(\n  token: ContextTokenUnion,\n  list: Array<T>\n): Array<T> {\n  return filterAndSortList(list, normalizeText(token.string));\n}\n\n// Given a list of hint entries and currently typed text, sort and filter to\n// provide a concise list.\nfunction filterAndSortList<T extends CompletionItemBase>(\n  list: Array<T>,\n  text: string\n): Array<T> {\n  if (!text) {\n    return filterNonEmpty<T>(list, entry => !entry.isDeprecated);\n  }\n\n  const byProximity = list.map(entry => ({\n    proximity: getProximity(normalizeText(entry.label), text),\n    entry,\n  }));\n\n  return filterNonEmpty(\n    filterNonEmpty(byProximity, pair => pair.proximity <= 2),\n    pair => !pair.entry.isDeprecated\n  )\n    .sort(\n      (a, b) =>\n        (a.entry.isDeprecated ? 1 : 0) - (b.entry.isDeprecated ? 1 : 0) ||\n        a.proximity - b.proximity ||\n        a.entry.label.length - b.entry.label.length\n    )\n    .map(pair => pair.entry);\n}\n\n// Filters the array by the predicate, unless it results in an empty array,\n// in which case return the original array.\nfunction filterNonEmpty<T>(\n  array: Array<T>,\n  predicate: (entry: T) => boolean\n): Array<T> {\n  const filtered = array.filter(predicate);\n  return filtered.length === 0 ? array : filtered;\n}\n\nfunction normalizeText(text: string): string {\n  return text.toLowerCase().replace(/\\W/g, '');\n}\n\n// Determine a numeric proximity for a suggestion based on current text.\nfunction getProximity(suggestion: string, text: string): number {\n  // start with lexical distance\n  let proximity = lexicalDistance(text, suggestion);\n  if (suggestion.length > text.length) {\n    // do not penalize long suggestions.\n    proximity -= suggestion.length - text.length - 1;\n    // penalize suggestions not starting with this phrase\n    proximity += suggestion.indexOf(text) === 0 ? 0 : 0.5;\n  }\n  return proximity;\n}\n\n/**\n * Computes the lexical distance between strings A and B.\n *\n * The \"distance\" between two strings is given by counting the minimum number\n * of edits needed to transform string A into string B. An edit can be an\n * insertion, deletion, or substitution of a single character, or a swap of two\n * adjacent characters.\n *\n * This distance can be useful for detecting typos in input or sorting\n *\n * @param {string} a\n * @param {string} b\n * @return {int} distance in number of edits\n */\nfunction lexicalDistance(a: string, b: string): number {\n  let i;\n  let j;\n  const d = [];\n  const aLength = a.length;\n  const bLength = b.length;\n\n  for (i = 0; i <= aLength; i++) {\n    d[i] = [i];\n  }\n\n  for (j = 1; j <= bLength; j++) {\n    d[0]![j] = j;\n  }\n\n  for (i = 1; i <= aLength; i++) {\n    for (j = 1; j <= bLength; j++) {\n      const cost = a[i - 1] === b[j - 1] ? 0 : 1;\n\n      d[i]![j] = Math.min(\n        d[i - 1]![j]! + 1,\n        d[i]![j - 1]! + 1,\n        d[i - 1]![j - 1]! + cost\n      );\n\n      if (i > 1 && j > 1 && a[i - 1] === b[j - 2] && a[i - 2] === b[j - 1]) {\n        d[i]![j] = Math.min(d[i]![j]!, d[i - 2]![j - 2]! + cost);\n      }\n    }\n  }\n\n  return d[aLength]![bLength]!;\n}\n\nexport type AllTypeInfo = {\n  type: Maybe<GraphQLType>;\n  parentType: Maybe<GraphQLType>;\n  inputType: Maybe<GraphQLType>;\n  directiveDef: Maybe<GraphQLDirective>;\n  fieldDef: Maybe<GraphQLField<any, any>>;\n  enumValue: Maybe<GraphQLEnumValue>;\n  argDef: Maybe<GraphQLArgument>;\n  argDefs: Maybe<GraphQLArgument[]>;\n  objectFieldDefs: Maybe<GraphQLInputFieldMap>;\n  interfaceDef: Maybe<GraphQLInterfaceType>;\n  objectTypeDef: Maybe<GraphQLObjectType>;\n};\n\n/**\n * This is vendored from https://github.com/graphql/graphiql/blob/main/packages/graphql-language-service/src/interface/getAutocompleteSuggestions.ts#L779\n */\nexport function getSuggestionsForFragmentSpread(\n  token: ContextToken,\n  typeInfo: AllTypeInfo,\n  schema: GraphQLSchema,\n  queryText: string,\n  fragments: FragmentDefinitionNode[]\n): Array<CompletionItem> {\n  if (!queryText) {\n    return [];\n  }\n\n  const typeMap = schema.getTypeMap();\n  const defState = getDefinitionState(token.state);\n\n  // Filter down to only the fragments which may exist here.\n  const relevantFrags = fragments.filter(\n    frag =>\n      // Only include fragments with known types.\n      typeMap[frag.typeCondition.name.value] &&\n      // Only include fragments which are not cyclic.\n      !(\n        defState &&\n        defState.kind === RuleKinds.FRAGMENT_DEFINITION &&\n        defState.name === frag.name.value\n      ) &&\n      // Only include fragments which could possibly be spread here.\n      isCompositeType(typeInfo.parentType) &&\n      isCompositeType(typeMap[frag.typeCondition.name.value]) &&\n      doTypesOverlap(\n        schema,\n        typeInfo.parentType,\n        typeMap[frag.typeCondition.name.value] as GraphQLCompositeType\n      )\n  );\n\n  return hintList(\n    token,\n    relevantFrags.map(frag => ({\n      label: frag.name.value,\n      detail: String(typeMap[frag.typeCondition.name.value]),\n      documentation: `fragment ${frag.name.value} on ${frag.typeCondition.name.value}`,\n      kind: CompletionItemKind.Field,\n      type: typeMap[frag.typeCondition.name.value],\n    }))\n  );\n}\n", "import { ts } from './ts';\n\nimport {\n  getAutocompleteSuggestions,\n  getTokenAtPosition,\n  getTypeInfo,\n  RuleKinds,\n  State,\n  RuleKind,\n  CompletionItem,\n  onlineParser,\n  CharacterStream,\n  ContextToken,\n} from 'graphql-language-service';\nimport { FragmentDefinitionNode, GraphQLSchema, Kind, parse } from 'graphql';\nimport { print } from '@0no-co/graphql.web';\n\nimport * as checks from './ast/checks';\nimport {\n  bubbleUpCallExpression,\n  bubbleUpTemplate,\n  findNode,\n  getAllFragments,\n  getSource,\n} from './ast';\nimport { Cursor } from './ast/cursor';\nimport { resolveTemplate } from './ast/resolve';\nimport { getToken } from './ast/token';\nimport { getSuggestionsForFragmentSpread } from './graphql/getFragmentSpreadSuggestions';\nimport { SchemaRef } from './graphql/getSchema';\n\nexport function getGraphQLCompletions(\n  filename: string,\n  cursorPosition: number,\n  schema: SchemaRef,\n  info: ts.server.PluginCreateInfo\n): ts.WithMetadata<ts.CompletionInfo> | undefined {\n  const isCallExpression = info.config.templateIsCallExpression ?? true;\n  const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n  const source = getSource(info, filename);\n  if (!source) return undefined;\n\n  let node = findNode(source, cursorPosition);\n  if (!node) return undefined;\n\n  node = isCallExpression\n    ? bubbleUpCallExpression(node)\n    : bubbleUpTemplate(node);\n\n  let text, cursor, schemaToUse: GraphQLSchema | undefined;\n  if (isCallExpression && checks.isGraphQLCall(node, typeChecker)) {\n    const schemaName = checks.getSchemaName(node, typeChecker);\n\n    schemaToUse =\n      schemaName && schema.multi[schemaName]\n        ? schema.multi[schemaName]?.schema\n        : schema.current?.schema;\n\n    const foundToken = getToken(node.arguments[0], cursorPosition);\n    if (\n      !schemaToUse ||\n      !foundToken ||\n      foundToken.string === '.' ||\n      foundToken.string === '..'\n    )\n      return undefined;\n\n    const queryText = node.arguments[0].getText().slice(1, -1);\n    const fragments = getAllFragments(filename, node, info);\n\n    text = `${queryText}\\n${fragments.map(x => print(x)).join('\\n')}`;\n    cursor = new Cursor(foundToken.line, foundToken.start - 1);\n  } else if (!isCallExpression && checks.isGraphQLTag(node)) {\n    const foundToken = getToken(node.template, cursorPosition);\n    if (\n      !foundToken ||\n      !schema.current ||\n      foundToken.string === '.' ||\n      foundToken.string === '..'\n    )\n      return undefined;\n\n    const { combinedText, resolvedSpans } = resolveTemplate(\n      node,\n      filename,\n      info\n    );\n\n    const amountOfLines = resolvedSpans\n      .filter(\n        x =>\n          x.original.start < cursorPosition &&\n          x.original.start + x.original.length < cursorPosition\n      )\n      .reduce((acc, span) => acc + (span.lines - 1), 0);\n\n    foundToken.line = foundToken.line + amountOfLines;\n\n    text = combinedText;\n    cursor = new Cursor(foundToken.line, foundToken.start - 1);\n    schemaToUse = schema.current.schema;\n  } else {\n    return undefined;\n  }\n\n  const [suggestions, spreadSuggestions] = getSuggestionsInternal(\n    schemaToUse,\n    text,\n    cursor\n  );\n\n  return {\n    isGlobalCompletion: false,\n    isMemberCompletion: false,\n    isNewIdentifierLocation: false,\n    entries: [\n      ...suggestions.map(suggestion => ({\n        ...suggestion,\n        kind: ts.ScriptElementKind.variableElement,\n        name: suggestion.label,\n        kindModifiers: 'declare',\n        sortText: suggestion.sortText || '0',\n        labelDetails: {\n          detail: suggestion.type\n            ? ' ' + suggestion.type?.toString()\n            : undefined,\n          description: suggestion.documentation,\n        },\n      })),\n      ...spreadSuggestions.map(suggestion => ({\n        ...suggestion,\n        kind: ts.ScriptElementKind.variableElement,\n        name: suggestion.label,\n        insertText: '...' + suggestion.label,\n        kindModifiers: 'declare',\n        sortText: '0',\n        labelDetails: {\n          description: suggestion.documentation,\n        },\n      })),\n    ],\n  };\n}\n\nexport function getSuggestionsInternal(\n  schema: GraphQLSchema,\n  queryText: string,\n  cursor: Cursor\n): [CompletionItem[], CompletionItem[]] {\n  const token = getTokenAtPosition(queryText, cursor);\n\n  let fragments: Array<FragmentDefinitionNode> = [];\n  try {\n    const parsed = parse(queryText, { noLocation: true });\n    fragments = parsed.definitions.filter(\n      x => x.kind === Kind.FRAGMENT_DEFINITION\n    ) as Array<FragmentDefinitionNode>;\n  } catch (e) {}\n\n  const isOnTypeCondition =\n    token.string === 'on' && token.state.kind === 'TypeCondition';\n  let suggestions = getAutocompleteSuggestions(\n    schema,\n    queryText,\n    cursor,\n    isOnTypeCondition\n      ? {\n          ...token,\n          state: {\n            ...token.state,\n            step: 1,\n          },\n          type: null,\n        }\n      : undefined\n  );\n  let spreadSuggestions = !isOnTypeCondition\n    ? getSuggestionsForFragmentSpread(\n        token,\n        getTypeInfo(schema, token.state),\n        schema,\n        queryText,\n        fragments\n      )\n    : [];\n\n  const state =\n    token.state.kind === 'Invalid' ? token.state.prevState : token.state;\n  const parentName = getParentDefinition(token.state, RuleKinds.FIELD)?.name;\n\n  if (state && parentName) {\n    const { kind } = state;\n\n    // Argument names\n    if (kind === RuleKinds.ARGUMENTS || kind === RuleKinds.ARGUMENT) {\n      const usedArguments = new Set<String>();\n\n      runOnlineParser(queryText, (_, state) => {\n        if (state.kind === RuleKinds.ARGUMENT) {\n          const parentDefinition = getParentDefinition(state, RuleKinds.FIELD);\n          if (\n            parentName &&\n            state.name &&\n            parentDefinition?.name === parentName\n          ) {\n            usedArguments.add(state.name);\n          }\n        }\n      });\n\n      suggestions = suggestions.filter(\n        suggestion => !usedArguments.has(suggestion.label)\n      );\n    }\n\n    // Field names\n    if (\n      kind === RuleKinds.SELECTION_SET ||\n      kind === RuleKinds.FIELD ||\n      kind === RuleKinds.ALIASED_FIELD\n    ) {\n      const usedFields = new Set<string>();\n      const usedFragments = getUsedFragments(queryText, parentName);\n\n      runOnlineParser(queryText, (_, state) => {\n        if (\n          state.kind === RuleKinds.FIELD ||\n          state.kind === RuleKinds.ALIASED_FIELD\n        ) {\n          const parentDefinition = getParentDefinition(state, RuleKinds.FIELD);\n          if (\n            parentDefinition &&\n            parentDefinition.name === parentName &&\n            state.name\n          ) {\n            usedFields.add(state.name);\n          }\n        }\n      });\n\n      suggestions = suggestions.filter(\n        suggestion => !usedFields.has(suggestion.label)\n      );\n      spreadSuggestions = spreadSuggestions.filter(\n        suggestion => !usedFragments.has(suggestion.label)\n      );\n    }\n\n    // Fragment spread names\n    if (kind === RuleKinds.FRAGMENT_SPREAD) {\n      const usedFragments = getUsedFragments(queryText, parentName);\n      suggestions = suggestions.filter(\n        suggestion => !usedFragments.has(suggestion.label)\n      );\n      spreadSuggestions = spreadSuggestions.filter(\n        suggestion => !usedFragments.has(suggestion.label)\n      );\n    }\n  }\n\n  return [suggestions, spreadSuggestions];\n}\n\nfunction getUsedFragments(queryText: string, parentName: string | undefined) {\n  const usedFragments = new Set<string>();\n\n  runOnlineParser(queryText, (_, state) => {\n    if (state.kind === RuleKinds.FRAGMENT_SPREAD && state.name) {\n      const parentDefinition = getParentDefinition(state, RuleKinds.FIELD);\n      if (parentName && parentDefinition?.name === parentName) {\n        usedFragments.add(state.name);\n      }\n    }\n  });\n\n  return usedFragments;\n}\n\n/**\n * This is vendored from https://github.com/graphql/graphiql/blob/aeedf7614e422c783f5cfb5e226c5effa46318fd/packages/graphql-language-service/src/interface/getAutocompleteSuggestions.ts#L831\n */\nfunction getParentDefinition(state: State, kind: RuleKind) {\n  if (state.prevState?.kind === kind) {\n    return state.prevState;\n  }\n  if (state.prevState?.prevState?.kind === kind) {\n    return state.prevState.prevState;\n  }\n  if (state.prevState?.prevState?.prevState?.kind === kind) {\n    return state.prevState.prevState.prevState;\n  }\n  if (state.prevState?.prevState?.prevState?.prevState?.kind === kind) {\n    return state.prevState.prevState.prevState.prevState;\n  }\n}\n\nfunction runOnlineParser(\n  queryText: string,\n  callback: (\n    stream: CharacterStream,\n    state: State,\n    style: string,\n    index: number\n  ) => void | 'BREAK'\n): ContextToken {\n  const lines = queryText.split('\\n');\n  const parser = onlineParser();\n  let state = parser.startState();\n  let style = '';\n\n  let stream: CharacterStream = new CharacterStream('');\n\n  for (let i = 0; i < lines.length; i++) {\n    stream = new CharacterStream(lines[i]!);\n    while (!stream.eol()) {\n      style = parser.token(stream, state);\n      const code = callback(stream, state, style, i);\n      if (code === 'BREAK') {\n        break;\n      }\n    }\n\n    // Above while loop won't run if there is an empty line.\n    // Run the callback one more time to catch this.\n    callback(stream, state, style, i);\n\n    if (!state.kind) {\n      state = parser.startState();\n    }\n  }\n\n  return {\n    start: stream.getStartOfToken(),\n    end: stream.getCurrentPosition(),\n    string: stream.current(),\n    state,\n    style,\n  };\n}\n", "import type { SchemaOrigin } from '@gql.tada/internal';\n\nimport { ts, init as initTypeScript } from './ts';\nimport { loadSchema } from './graphql/getSchema';\nimport { getGraphQLCompletions } from './autoComplete';\nimport { getGraphQLQuickInfo } from './quickInfo';\nimport { ALL_DIAGNOSTICS, getGraphQLDiagnostics } from './diagnostics';\nimport { templates } from './ast/templates';\nimport { getPersistedCodeFixAtPosition } from './persisted';\n\nfunction createBasicDecorator(info: ts.server.PluginCreateInfo) {\n  const proxy: ts.LanguageService = Object.create(null);\n  for (let k of Object.keys(info.languageService) as Array<\n    keyof ts.LanguageService\n  >) {\n    const x = info.languageService[k]!;\n    // @ts-expect-error - JS runtime trickery which is tricky to type tersely\n    proxy[k] = (...args: Array<{}>) => x.apply(info.languageService, args);\n  }\n\n  return proxy;\n}\n\nexport type Logger = (msg: string) => void;\n\ninterface Config {\n  schema: SchemaOrigin;\n  schemas: SchemaOrigin[];\n  tadaDisablePreprocessing?: boolean;\n  templateIsCallExpression?: boolean;\n  shouldCheckForColocatedFragments?: boolean;\n  template?: string;\n  clientDirectives?: string[];\n  trackFieldUsage?: boolean;\n  tadaOutputLocation?: string;\n}\n\nfunction create(info: ts.server.PluginCreateInfo) {\n  const logger: Logger = (msg: string) =>\n    info.project.projectService.logger.info(`[GraphQLSP] ${msg}`);\n  const config: Config = info.config;\n\n  logger('config: ' + JSON.stringify(config));\n  if (!config.schema && !config.schemas) {\n    logger('Missing \"schema\" option in configuration.');\n    throw new Error('Please provide a GraphQL Schema!');\n  }\n\n  logger('Setting up the GraphQL Plugin');\n\n  if (config.template) {\n    templates.add(config.template);\n  }\n\n  const proxy = createBasicDecorator(info);\n\n  const schema = loadSchema(info, config, logger);\n\n  proxy.getSemanticDiagnostics = (filename: string): ts.Diagnostic[] => {\n    const originalDiagnostics =\n      info.languageService.getSemanticDiagnostics(filename);\n\n    const hasGraphQLDiagnostics = originalDiagnostics.some(x =>\n      ALL_DIAGNOSTICS.includes(x.code)\n    );\n    if (hasGraphQLDiagnostics) return originalDiagnostics;\n\n    const graphQLDiagnostics = getGraphQLDiagnostics(filename, schema, info);\n\n    return graphQLDiagnostics\n      ? [...graphQLDiagnostics, ...originalDiagnostics]\n      : originalDiagnostics;\n  };\n\n  proxy.getCompletionsAtPosition = (\n    filename: string,\n    cursorPosition: number,\n    options: any\n  ): ts.WithMetadata<ts.CompletionInfo> | undefined => {\n    const completions = getGraphQLCompletions(\n      filename,\n      cursorPosition,\n      schema,\n      info\n    );\n\n    if (completions && completions.entries.length) {\n      return completions;\n    } else {\n      return (\n        info.languageService.getCompletionsAtPosition(\n          filename,\n          cursorPosition,\n          options\n        ) || {\n          isGlobalCompletion: false,\n          isMemberCompletion: false,\n          isNewIdentifierLocation: false,\n          entries: [],\n        }\n      );\n    }\n  };\n\n  proxy.getEditsForRefactor = (\n    filename,\n    formatOptions,\n    positionOrRange,\n    refactorName,\n    actionName,\n    preferences,\n    interactive\n  ) => {\n    const original = info.languageService.getEditsForRefactor(\n      filename,\n      formatOptions,\n      positionOrRange,\n      refactorName,\n      actionName,\n      preferences,\n      interactive\n    );\n\n    const codefix = getPersistedCodeFixAtPosition(\n      filename,\n      typeof positionOrRange === 'number'\n        ? positionOrRange\n        : positionOrRange.pos,\n      info\n    );\n    if (!codefix) return original;\n    return {\n      edits: [\n        {\n          fileName: filename,\n          textChanges: [{ newText: codefix.replacement, span: codefix.span }],\n        },\n      ],\n    };\n  };\n\n  proxy.getApplicableRefactors = (\n    filename,\n    positionOrRange,\n    preferences,\n    reason,\n    kind,\n    includeInteractive\n  ) => {\n    const original = info.languageService.getApplicableRefactors(\n      filename,\n      positionOrRange,\n      preferences,\n      reason,\n      kind,\n      includeInteractive\n    );\n\n    const codefix = getPersistedCodeFixAtPosition(\n      filename,\n      typeof positionOrRange === 'number'\n        ? positionOrRange\n        : positionOrRange.pos,\n      info\n    );\n\n    if (codefix) {\n      return [\n        {\n          name: 'GraphQL',\n          description: 'Operations specific to gql.tada!',\n          actions: [\n            {\n              name: 'Insert document-id',\n              description:\n                'Generate a document-id for your persisted-operation, by default a SHA256 hash.',\n            },\n          ],\n          inlineable: true,\n        },\n        ...original,\n      ];\n    } else {\n      return original;\n    }\n  };\n\n  proxy.getQuickInfoAtPosition = (filename: string, cursorPosition: number) => {\n    const quickInfo = getGraphQLQuickInfo(\n      filename,\n      cursorPosition,\n      schema,\n      info\n    );\n\n    if (quickInfo) return quickInfo;\n\n    return info.languageService.getQuickInfoAtPosition(\n      filename,\n      cursorPosition\n    );\n  };\n\n  logger('proxy: ' + JSON.stringify(proxy));\n\n  return proxy;\n}\n\nconst init: ts.server.PluginModuleFactory = ts => {\n  initTypeScript(ts);\n  return { create };\n};\n\nexport default init;\n", "import { ts } from './ts';\nimport { getHoverInformation } from 'graphql-language-service';\nimport { GraphQLSchema } from 'graphql';\n\nimport {\n  bubbleUpCallExpression,\n  bubbleUpTemplate,\n  findNode,\n  getSchemaName,\n  getSource,\n} from './ast';\n\nimport * as checks from './ast/checks';\nimport { resolveTemplate } from './ast/resolve';\nimport { getToken } from './ast/token';\nimport { Cursor } from './ast/cursor';\nimport { SchemaRef } from './graphql/getSchema';\n\nexport function getGraphQLQuickInfo(\n  filename: string,\n  cursorPosition: number,\n  schema: SchemaRef,\n  info: ts.server.PluginCreateInfo\n): ts.QuickInfo | undefined {\n  const isCallExpression = info.config.templateIsCallExpression ?? true;\n  const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n\n  const source = getSource(info, filename);\n  if (!source) return undefined;\n\n  let node = findNode(source, cursorPosition);\n  if (!node) return undefined;\n\n  node = isCallExpression\n    ? bubbleUpCallExpression(node)\n    : bubbleUpTemplate(node);\n\n  let cursor, text, schemaToUse: GraphQLSchema | undefined;\n  if (isCallExpression && checks.isGraphQLCall(node, typeChecker)) {\n    const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n    const schemaName = getSchemaName(node, typeChecker);\n\n    schemaToUse =\n      schemaName && schema.multi[schemaName]\n        ? schema.multi[schemaName]?.schema\n        : schema.current?.schema;\n\n    const foundToken = getToken(node.arguments[0], cursorPosition);\n    if (!schemaToUse || !foundToken) return undefined;\n\n    text = node.arguments[0].getText();\n    cursor = new Cursor(foundToken.line, foundToken.start - 1);\n  } else if (!isCallExpression && checks.isGraphQLTag(node)) {\n    const foundToken = getToken(node.template, cursorPosition);\n    if (!foundToken || !schema.current) return undefined;\n\n    const { combinedText, resolvedSpans } = resolveTemplate(\n      node,\n      filename,\n      info\n    );\n\n    const amountOfLines = resolvedSpans\n      .filter(\n        x =>\n          x.original.start < cursorPosition &&\n          x.original.start + x.original.length < cursorPosition\n      )\n      .reduce((acc, span) => acc + (span.lines - 1), 0);\n\n    foundToken.line = foundToken.line + amountOfLines;\n    text = combinedText;\n    cursor = new Cursor(foundToken.line, foundToken.start - 1);\n    schemaToUse = schema.current.schema;\n  } else {\n    return undefined;\n  }\n\n  const hoverInfo = getHoverInformation(schemaToUse, text, cursor);\n\n  return {\n    kind: ts.ScriptElementKind.label,\n    textSpan: {\n      start: cursorPosition,\n      length: 1,\n    },\n    kindModifiers: 'text',\n    documentation: Array.isArray(hoverInfo)\n      ? hoverInfo.map(item => ({ kind: 'text', text: item as string }))\n      : [{ kind: 'text', text: hoverInfo as string }],\n  } as ts.QuickInfo;\n}\n"], "names": ["statFile", "file", "predicate", "fs", "stat", "then", "catch", "swapWrite", "async", "target", "contents", "isFile", "writeFile", "tempTarget", "rename", "error", "unlink", "now", "Date", "utimes", "_error", "touchFile", "saveTadaIntrospection", "introspection", "tadaOutputLocation", "disablePreprocessing", "logger", "minified", "minifyIntrospection", "outputIntrospectionFile", "fileType", "shouldPreprocess", "output", "isDirectory", "path", "join", "p", "mkdir", "dirname", "recursive", "getDefinitionState", "tokenState", "definitionState", "forEachState", "state", "kind", "getFieldDef", "schema", "type", "fieldName", "SchemaMetaFieldDef", "name", "getQueryType", "TypeMetaFieldDef", "TypeNameMetaFieldDef", "isCompositeType", "getFields", "stack", "fn", "reverseStateStack", "push", "prevState", "i", "length", "objectValues", "object", "keys", "Object", "len", "values", "Array", "hintList", "token", "list", "filterAndSortList", "text", "filterNonEmpty", "entry", "isDeprecated", "byProximity", "map", "proximity", "getProximity", "normalizeText", "label", "pair", "sort", "a", "b", "string", "array", "filtered", "filter", "toLowerCase", "replaceAll", "suggestion", "lexicalDistance", "j", "d", "a<PERSON><PERSON><PERSON>", "b<PERSON><PERSON><PERSON>", "cost", "Math", "min", "indexOf", "DocumentUri", "is", "value", "URI", "integer", "MIN_VALUE", "MAX_VALUE", "<PERSON><PERSON><PERSON><PERSON>", "Position", "create", "line", "character", "Number", "candidate", "Is", "objectLiteral", "Range", "one", "two", "three", "four", "start", "end", "Error", "concat", "Location", "uri", "range", "undefined", "LocationLink", "targetUri", "targetRange", "targetSelectionRange", "originSelectionRange", "Color", "red", "green", "blue", "alpha", "numberRange", "ColorInformation", "color", "ColorPresentation", "textEdit", "additionalTextEdits", "TextEdit", "typedArray", "FoldingRangeKind", "Comment", "Imports", "Region", "FoldingRange", "startLine", "endLine", "startCharacter", "endCharacter", "collapsedText", "result", "defined", "DiagnosticRelatedInformation", "location", "message", "DiagnosticSeverity", "Warning", "Information", "Hint", "DiagnosticTag", "Unnecessary", "Deprecated", "CodeDescription", "href", "Diagnostic", "severity", "code", "source", "relatedInformation", "_a", "number", "codeDescription", "Command", "title", "command", "args", "_i", "arguments", "replace", "newText", "insert", "position", "del", "ChangeAnnotation", "needsConfirmation", "description", "boolean", "ChangeAnnotationIdentifier", "AnnotatedTextEdit", "annotation", "annotationId", "TextDocumentEdit", "textDocument", "edits", "OptionalVersionedTextDocumentIdentifier", "isArray", "CreateFile", "options", "overwrite", "ignoreIfExists", "RenameFile", "old<PERSON><PERSON>", "newUri", "DeleteFile", "ignoreIfNotExists", "WorkspaceEdit", "changes", "documentChanges", "every", "change", "TextEditChangeImpl", "changeAnnotations", "this", "prototype", "edit", "id", "assertChangeAnnotations", "manage", "delete", "add", "all", "clear", "splice", "ChangeAnnotations", "annotations", "_annotations", "_counter", "_size", "defineProperty", "get", "enumerable", "configurable", "idOrAnnotation", "nextId", "toString", "WorkspaceChange", "workspaceEdit", "_this", "_textEditChanges", "_workspaceEdit", "_changeAnnotations", "for<PERSON>ach", "textEditChange", "key", "initDocumentChanges", "size", "getTextEditChange", "version", "initChanges", "createFile", "optionsOrAnnotation", "operation", "renameFile", "deleteFile", "TextDocumentIdentifier", "VersionedTextDocumentIdentifier", "TextDocumentItem", "languageId", "<PERSON><PERSON><PERSON><PERSON>", "PlainText", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CompletionItemKind", "Text", "Method", "Function", "<PERSON><PERSON><PERSON><PERSON>", "Field", "Variable", "Class", "Interface", "<PERSON><PERSON><PERSON>", "Property", "Unit", "Value", "Enum", "Keyword", "Snippet", "File", "Reference", "Folder", "EnumMember", "Constant", "Struct", "Event", "Operator", "TypeParameter", "InsertTextFormat", "CompletionItemTag", "InsertReplaceEdit", "InsertTextMode", "asIs", "adjustIndentation", "CompletionItemLabelDetails", "detail", "CompletionItem", "CompletionList", "items", "isIncomplete", "MarkedString", "fromPlainText", "plainText", "language", "Hover", "ParameterInformation", "documentation", "SignatureInformation", "parameters", "DocumentHighlightKind", "Read", "Write", "DocumentHighlight", "SymbolKind", "Namespace", "Package", "String", "Boolean", "Key", "<PERSON><PERSON>", "SymbolTag", "SymbolInformation", "containerName", "WorkspaceSymbol", "DocumentSymbol", "<PERSON><PERSON><PERSON><PERSON>", "children", "deprecated", "tags", "CodeActionKind", "Empty", "QuickFix", "Refa<PERSON>", "RefactorExtract", "RefactorInline", "RefactorRewrite", "Source", "SourceOrganizeImports", "SourceFixAll", "CodeActionTriggerKind", "Invoked", "Automatic", "CodeActionContext", "diagnostics", "only", "trigger<PERSON>ind", "CodeAction", "kindOrCommandOrEdit", "checkKind", "isPreferred", "CodeLens", "data", "FormattingOptions", "tabSize", "insertSpaces", "DocumentLink", "SelectionRange", "parent", "SemanticTokenTypes", "SemanticTokenModifiers", "SemanticTokens", "resultId", "InlineValueText", "InlineValueVariableLookup", "variableName", "caseSensitiveLookup", "InlineValueEvaluatableExpression", "expression", "InlineValueContext", "frameId", "stoppedLocation", "InlayHintKind", "Type", "Parameter", "InlayHintLabelPart", "tooltip", "InlayHint", "textEdits", "paddingLeft", "paddingRight", "WorkspaceFolder", "TextDocument", "content", "FullTextDocument", "lineCount", "func", "getText", "positionAt", "offsetAt", "applyEdits", "document", "sortedEdits", "mergeSort", "diff", "lastModifiedOffset", "e", "startOffset", "endOffset", "substring", "compare", "left", "slice", "right", "leftIdx", "rightIdx", "_uri", "_languageId", "_version", "_content", "_lineOffsets", "update", "event", "getLineOffsets", "lineOffsets", "isLineStart", "ch", "char<PERSON>t", "offset", "max", "low", "high", "mid", "floor", "lineOffset", "call", "check", "RuleKinds", "assign", "Kind", "ALIASED_FIELD", "ARGUMENTS", "SHORT_QUERY", "QUERY", "MUTATION", "SUBSCRIPTION", "TYPE_CONDITION", "INVALID", "COMMENT", "SCHEMA_DEF", "SCALAR_DEF", "OBJECT_TYPE_DEF", "OBJECT_VALUE", "LIST_VALUE", "INTERFACE_DEF", "UNION_DEF", "ENUM_DEF", "ENUM_VALUE", "FIELD_DEF", "INPUT_DEF", "INPUT_VALUE_DEF", "ARGUMENTS_DEF", "EXTEND_DEF", "EXTENSION_DEFINITION", "DIRECTIVE_DEF", "IMPLEMENTS", "VARIABLE_DEFINITIONS", "TYPE", "SuggestionCommand", "collectFragmentDefs", "op", "externalFragments", "visit", "parse", "FragmentDefinition", "def", "typeSystemKinds", "SCHEMA_DEFINITION", "OPERATION_TYPE_DEFINITION", "SCALAR_TYPE_DEFINITION", "OBJECT_TYPE_DEFINITION", "INTERFACE_TYPE_DEFINITION", "UNION_TYPE_DEFINITION", "ENUM_TYPE_DEFINITION", "INPUT_OBJECT_TYPE_DEFINITION", "DIRECTIVE_DEFINITION", "SCHEMA_EXTENSION", "SCALAR_TYPE_EXTENSION", "OBJECT_TYPE_EXTENSION", "INTERFACE_TYPE_EXTENSION", "UNION_TYPE_EXTENSION", "ENUM_TYPE_EXTENSION", "INPUT_OBJECT_TYPE_EXTENSION", "hasTypeSystemDefinitions", "sdl", "hasTypeSystemDef", "enter", "node", "includes", "BREAK", "getAutocompleteSuggestions", "queryText", "cursor", "contextToken", "fragmentDefs", "opts", "getTokenAtPosition", "mode", "getDocumentMode", "documentText", "endsWith", "GraphQLDocumentMode", "TYPE_SYSTEM", "EXECUTABLE", "step", "typeInfo", "getTypeInfo", "DOCUMENT", "getSuggestionsForTypeSystemDefinitions", "getSuggestionsForExecutableDefinitions", "getSuggestionsForExtensionDefinitions", "getTypeMap", "isScalarType", "isObjectType", "startsWith", "isInterfaceType", "isUnionType", "isEnumType", "isInputObjectType", "NAMED_TYPE", "getSuggestionsForImplements", "needsSeparator", "typeMap", "schemaInterfaces", "schemaInterfaceNames", "inlineInterfaces", "Set", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_", "interfaceDef", "_b", "getInterfaces", "find", "getType", "interfaceConfig", "_c", "toConfig", "GraphQLInterfaceType", "interfaces", "fields", "objectTypeDef", "_d", "objectTypeConfig", "_e", "GraphQLObjectType", "currentTypeToExtend", "siblingInterfaceNames", "possibleInterfaces", "SELECTION_SET", "FIELD", "getSuggestionsForFieldNames", "parentType", "field", "index", "sortText", "deprecationReason", "fillLeafsOnComplete", "insertText", "getInsertText", "insertTextFormat", "ARGUMENT", "argDefs", "argDef", "OBJECT_FIELD", "objectFieldDefs", "objectFields", "completionKind", "getSuggestionsForInputValues", "namedInputType", "getNamedType", "inputType", "queryVariables", "getVariableCompletions", "v", "GraphQLEnumType", "getV<PERSON>ues", "GraphQLBoolean", "VARIABLE", "getSuggestionsForFragmentTypeConditions", "_kind", "possibleTypes", "isAbstractType", "abstractType", "assertAbstractType", "possibleObjTypes", "getPossibleTypes", "possibleIfaceMap", "iface", "namedType", "FRAGMENT_SPREAD", "getSuggestionsForFragmentSpread", "defState", "fragments", "getFragmentDefinitions", "FRAGMENT_DEFINITION", "NAME", "selectionSet", "selections", "typeCondition", "relevantFrags", "frag", "doTypesOverlap", "unwrappedState", "unwrapType", "needsAdvance", "LIST_TYPE", "isOutputType", "isInputType", "VARIABLE_DEFINITION", "NON_NULL_TYPE", "getSuggestionsForVariableDefinition", "inputTypeMap", "inputTypes", "DIRECTIVE", "getSuggestionsForDirective", "directives", "getDirectives", "directive", "canUseDirective", "locations", "DirectiveLocation", "INLINE_FRAGMENT", "SCHEMA", "SCALAR", "OBJECT", "FIELD_DEFINITION", "INTERFACE", "UNION", "ENUM", "INPUT_OBJECT", "ARGUMENT_DEFINITION", "INPUT_FIELD_DEFINITION", "insertSuffix", "isListType", "ofType", "isNonNullType", "getParentDefinition", "_f", "_k", "_j", "_h", "_g", "variableType", "definitions", "parentDefinition", "styleAtCursor", "stateAtCursor", "stringAtCursor", "stream", "style", "getCurrentPosition", "current", "callback", "lines", "split", "parser", "onlineParser", "startState", "CharacterStream", "eol", "getStartOfToken", "directiveDef", "enumValue", "fieldDef", "getMutationType", "getSubscriptionType", "getDirective", "enumType", "val", "nullableType", "getNullableType", "GraphQLList", "objectType", "GraphQLInputObjectType", "objectField", "getHoverInformation", "config", "into", "renderMdCodeStart", "renderField", "renderQualifiedField", "renderTypeAnnotation", "renderMdCodeEnd", "renderDescription", "trim", "renderDirective", "renderArg", "renderEnumValue", "renderType", "useMarkdown", "_options", "t", "GraphQLNonNull", "renderDeprecation", "reason", "<PERSON><PERSON><PERSON>", "constructor", "char", "setLine", "<PERSON><PERSON><PERSON><PERSON>", "lessThanOrEqualTo", "getToken", "template", "cursorPosition", "ts", "isTemplateLiteral", "isStringLiteralLike", "input", "cPos", "getStart", "foundToken", "prevToken", "lPos", "tokenKind", "getGraphQLCompletions", "filename", "info", "isCallExpression", "templateIsCallExpression", "typeC<PERSON>cker", "languageService", "getProgram", "getType<PERSON><PERSON>cker", "getSource", "findNode", "bubbleUpCallExpression", "bubbleUpTemplate", "schemaToUse", "checks", "schemaName", "multi", "getAllFragments", "x", "print", "isGraphQLTag", "combinedText", "resolvedSpans", "resolveTemplate", "amountOfLines", "original", "reduce", "acc", "span", "suggestions", "spreadSuggestions", "getSuggestionsInternal", "noLocation", "isOnTypeCondition", "parentName", "usedArguments", "has", "usedFields", "usedFragments", "getUsedFragments", "isGlobalCompletion", "isMemberCompletion", "isNewIdentifierLocation", "entries", "ScriptElementKind", "variableElement", "kindModifiers", "labelDetails", "msg", "project", "projectService", "JSON", "stringify", "schemas", "templates", "proxy", "createBasicDecorator", "_loop", "k", "apply", "loadSchema", "origin", "ref", "loadRef", "rootPath", "resolveTypeScriptRootDir", "getProjectName", "tadaDisablePreprocessing", "resolve", "load", "autoupdate", "schemaRef", "found", "getSemanticDiagnostics", "originalDiagnostics", "some", "ALL_DIAGNOSTICS", "graphQLDiagnostics", "getGraphQLDiagnostics", "getCompletionsAtPosition", "completions", "getEditsForRefactor", "formatOptions", "position<PERSON>r<PERSON>ang<PERSON>", "refactorName", "actionName", "preferences", "interactive", "codefix", "getPersistedCodeFixAtPosition", "pos", "fileName", "textChanges", "replacement", "getApplicableRefactors", "includeInteractive", "actions", "inlineable", "getQuickInfoAtPosition", "quickInfo", "getGraphQLQuickInfo", "getSchemaName", "hoverInfo", "textSpan", "item", "initTypeScript", "init"], "mappings": ";;;;;;;;;;AAmBA,IAAMA,WAAWA,CACfC,GACAC,MAEOC,EACJC,KAAKH,GACLI,KAAKH,GACLI,OAAM,OAAM;;AAWV,IAAMC,YAAYC,OACvBC,GACAC;EAEA,WAAYV,SAASS,IAAQL,KAAQA,EAAKO;UAGlCR,EAAGS,UAAUH,GAAQC;SACtB;IAIL,IAAMG,IAAaJ,IAAS;UACtBN,EAAGS,UAAUC,GAAYH;IAC/B;YACQP,EAAGW,OAAOD,GAAYJ;AAC7B,MAAC,OAAOM;YACDZ,EAAGa,OAAOH;MAChB,MAAME;AACR,MAAU;YA3BIP;QAChB;UACE,IAAMS,IAAM,IAAIC;gBACVf,EAAGgB,OAAOlB,GAAMgB,GAAKA;AAC7B,UAAE,OAAOG,IAAS;AAAA,QA2BRC,CAAUZ;AAClB;AACF;AAAA;;AAGFD,eAAec,sBACbC,GACAC,GACAC,GACAC;EAEA,IAAMC,IAAWC,sBAAoBL;EACrC,IAAMb,IAAWmB,EAAuBA,wBAACF,GAAU;IACjDG,UAAUN;IACVO,mBAAmBN;;EAGrB,IAAIO,IAASR;EACb,UAAUxB,SAASgC,IAAQ5B,KAAQA,EAAK6B;IACtCD,IAASE,EAAKC,KAAKH,GAAQ;SACtB,WAAYhC,SAASgC,IAAQI,OAAOA,KAAK;UACxCjC,EAAGkC,MAAMH,EAAKI,QAAQN,IAAS;MAAEO,YAAW;;IAClD,UAAUvC,SAASgC,IAAQ5B,KAAQA,EAAK6B;MACtCD,IAASE,EAAKC,KAAKH,GAAQ;;AAE/B;EAEA;UACQzB,UAAUyB,GAAQtB;IACxBgB,EAAQ,iCAAgCM;AACzC,IAAC,OAAOjB;IACPW,EAAQ,mCAAkCX;AAC5C;AACF;;ACtEM,SAAUyB,mBACdC;EAEA,IAAIC;EAGJC,aAAaF,IAAaG;IACxB,QAAQA,EAAMC;KACZ,KAAK;KACL,KAAK;KACL,KAAK;KACL,KAAK;KACL,KAAK;MACHH,IAAkBE;;;EAKxB,OAAOF;AACT;;AAGM,SAAUI,YACdC,GACAC,GACAC;EAEA,IAAIA,MAAcC,EAAAA,mBAAmBC,QAAQJ,EAAOK,mBAAmBJ;IACrE,OAAOE;;EAET,IAAID,MAAcI,EAAAA,iBAAiBF,QAAQJ,EAAOK,mBAAmBJ;IACnE,OAAOK;;EAET,IAAIJ,MAAcK,EAAoBA,qBAACH,QAAQI,EAAeA,gBAACP;IAC7D,OAAOM;;EAET,IAAI,eAAeN;IACjB,OAAOA,EAAKQ,YAAYP;;EAG1B,OAAO;AACT;;AAGM,SAAUN,aACdc,GACAC;EAEA,IAAMC,IAAoB;EAC1B,IAAIf,IAAkCa;EACtC,OAAOb,qBAAAA,EAAOC,MAAM;IAClBc,EAAkBC,KAAKhB;IACvBA,IAAQA,EAAMiB;;EAEhB,KAAK,IAAIC,IAAIH,EAAkBI,SAAS,GAAGD,KAAK,GAAGA;IACjDJ,EAAGC,EAAkBG;;AAEzB;;AAEM,SAAUE,aAAgBC;EAC9B,IAAMC,IAAOC,OAAOD,KAAKD;EACzB,IAAMG,IAAMF,EAAKH;EACjB,IAAMM,IAAS,IAAIC,MAAMF;EACzB,KAAK,IAAIN,IAAI,GAAGA,IAAIM,KAAON;IACzBO,EAAOP,KAAKG,EAAOC,EAAKJ;;EAE1B,OAAOO;AACT;;AAGM,SAAUE,WACdC,GACAC;EAEA,OAKF,SAASC,oBACPD,GACAE;IAEA,KAAKA;MACH,OAAOC,iBAAkBH,IAAMI,MAAUA,EAAMC;;IAGjD,IAAMC,IAAcN,EAAKO,KAAIH,MAAU;MACrCI,WAAWC,eAAaC,gBAAcN,EAAMO,QAAQT;MACpDE;;IAGF,OAAOD,iBACLA,iBAAeG,IAAaM,KAAQA,EAAKJ,aAAa,MACtDI,MAASA,EAAKR,MAAMC,eAEnBQ,MACC,CAACC,GAAGC,OACDD,EAAEV,MAAMC,eAAe,IAAI,MAAMU,EAAEX,MAAMC,eAAe,IAAI,MAC7DS,EAAEN,YAAYO,EAAEP,aAChBM,EAAEV,MAAMO,MAAMrB,SAASyB,EAAEX,MAAMO,MAAMrB,SAExCiB,KAAIK,KAAQA,EAAKR;AACtB,GA7BSH,CAAkBD,GAAMU,gBAAcX,EAAMiB;AACrD;;AAgCA,SAASb,iBACPc,GACAxF;EAEA,IAAMyF,IAAWD,EAAME,OAAO1F;EAC9B,OAA2B,MAApByF,EAAS5B,SAAe2B,IAAQC;AACzC;;AAEA,SAASR,gBAAcR;EACrB,OAAOA,EAAKkB,cAAcC,WAAW,OAAO;AAC9C;;AAGA,SAASZ,eAAaa,GAAoBpB;EAExC,IAAIM,IAwBN,SAASe,kBAAgBT,GAAWC;IAClC,IAAI1B;IACJ,IAAImC;IACJ,IAAMC,IAAI;IACV,IAAMC,IAAUZ,EAAExB;IAClB,IAAMqC,IAAUZ,EAAEzB;IAElB,KAAKD,IAAI,GAAGA,KAAKqC,GAASrC;MACxBoC,EAAEpC,KAAK,EAACA;;IAGV,KAAKmC,IAAI,GAAGA,KAAKG,GAASH;MACxBC,EAAE,GAAGD,KAAKA;;IAGZ,KAAKnC,IAAI,GAAGA,KAAKqC,GAASrC;MACxB,KAAKmC,IAAI,GAAGA,KAAKG,GAASH,KAAK;QAC7B,IAAMI,IAAOd,EAAEzB,IAAI,OAAO0B,EAAES,IAAI,KAAK,IAAI;QAEzCC,EAAEpC,GAAGmC,KAAKK,KAAKC,IACbL,EAAEpC,IAAI,GAAGmC,KAAK,GACdC,EAAEpC,GAAGmC,IAAI,KAAK,GACdC,EAAEpC,IAAI,GAAGmC,IAAI,KAAKI;QAGpB,IAAIvC,IAAI,KAAKmC,IAAI,KAAKV,EAAEzB,IAAI,OAAO0B,EAAES,IAAI,MAAMV,EAAEzB,IAAI,OAAO0B,EAAES,IAAI;UAChEC,EAAEpC,GAAGmC,KAAKK,KAAKC,IAAIL,EAAEpC,GAAGmC,IAAIC,EAAEpC,IAAI,GAAGmC,IAAI,KAAKI;;;;IAKpD,OAAOH,EAAEC,GAASC;AACpB,GAxDkBJ,CAAgBrB,GAAMoB;EACtC,IAAIA,EAAWhC,SAASY,EAAKZ,QAAQ;IAEnCkB,KAAac,EAAWhC,SAASY,EAAKZ,SAAS;IAE/CkB,KAA0C,MAA7Bc,EAAWS,QAAQ7B,KAAc,IAAI;;EAEpD,OAAOM;AACT;;ACpJO,IAAIwB;;CACX,SAAWA;EAIPA,EAAYC,KAHZ,SAASA,GAAGC;IACR,OAAwB,mBAAVA;AAClB;AAEH,CALD,CAKGF,MAAgBA,IAAc,CAAE;;AAC5B,IAAIG;;CACX,SAAWA;EAIPA,EAAIF,KAHJ,SAASA,GAAGC;IACR,OAAwB,mBAAVA;AAClB;AAEH,CALD,CAKGC,MAAQA,IAAM,CAAE;;AACZ,IAAIC;;CACX,SAAWA;EACPA,EAAQC,aAAa;EACrBD,EAAQE,YAAY;EAIpBF,EAAQH,KAHR,SAASA,GAAGC;IACR,OAAwB,mBAAVA,KAAsBE,EAAQC,aAAaH,KAASA,KAASE,EAAQE;AACvF;AAEH,CAPD,CAOGF,MAAYA,IAAU,CAAE;;AACpB,IAAIG;;CACX,SAAWA;EACPA,EAASF,YAAY;EACrBE,EAASD,YAAY;EAIrBC,EAASN,KAHT,SAASA,GAAGC;IACR,OAAwB,mBAAVA,KAAsBK,EAASF,aAAaH,KAASA,KAASK,EAASD;AACzF;AAEH,CAPD,CAOGC,MAAaA,IAAW,CAAE;;AAKtB,IAAIC;;CACX,SAAWA;EAePA,EAASC,SATT,SAASA,OAAOC,GAAMC;IAClB,IAAID,MAASE,OAAON;MAChBI,IAAOH,EAASD;;IAEpB,IAAIK,MAAcC,OAAON;MACrBK,IAAYJ,EAASD;;IAEzB,OAAO;MAAEI,MAAMA;MAAMC,WAAWA;;AACpC;EASAH,EAASP,KAJT,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcC,GAAGP,SAASM,EAAUH,SAASI,GAAGP,SAASM,EAAUF;AAC/F;AAEH,CAxBD,CAwBGH,MAAaA,IAAW,CAAE;;AAKtB,IAAIQ;;CACX,SAAWA;EAYPA,EAAMP,SAXN,SAASA,OAAOQ,GAAKC,GAAKC,GAAOC;IAC7B,IAAIN,GAAGP,SAASU,MAAQH,GAAGP,SAASW,MAAQJ,GAAGP,SAASY,MAAUL,GAAGP,SAASa;MAC1E,OAAO;QAAEC,OAAOb,EAASC,OAAOQ,GAAKC;QAAMI,KAAKd,EAASC,OAAOU,GAAOC;;WAEtE,IAAIZ,EAASP,GAAGgB,MAAQT,EAASP,GAAGiB;MACrC,OAAO;QAAEG,OAAOJ;QAAKK,KAAKJ;;;MAG1B,MAAM,IAAIK,MAAM,8CAA8CC,OAAOP,GAAK,MAAMO,OAAON,GAAK,MAAMM,OAAOL,GAAO,MAAMK,OAAOJ,GAAM;;AAE3I;EASAJ,EAAMf,KAJN,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcL,EAASP,GAAGY,EAAUQ,UAAUb,EAASP,GAAGY,EAAUS;AAChG;AAEH,CArBD,CAqBGN,MAAUA,IAAQ,CAAE;;AAKhB,IAAIS;;CACX,SAAWA;EASPA,EAAShB,SAHT,SAASA,OAAOiB,GAAKC;IACjB,OAAO;MAAED,KAAKA;MAAKC,OAAOA;;AAC9B;EASAF,EAASxB,KAJT,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcG,EAAMf,GAAGY,EAAUc,WAAWb,GAAG9B,OAAO6B,EAAUa,QAAQZ,GAAGc,UAAUf,EAAUa;AAC3H;AAEH,CAlBD,CAkBGD,MAAaA,IAAW,CAAE;;AAKtB,IAAII;;CACX,SAAWA;EAWPA,EAAapB,SAHb,SAASA,OAAOqB,GAAWC,GAAaC,GAAsBC;IAC1D,OAAO;MAAEH,WAAWA;MAAWC,aAAaA;MAAaC,sBAAsBA;MAAsBC,sBAAsBA;;AAC/H;EAWAJ,EAAa5B,KANb,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcG,EAAMf,GAAGY,EAAUkB,gBAAgBjB,GAAG9B,OAAO6B,EAAUiB,cACtFd,EAAMf,GAAGY,EAAUmB,0BAClBhB,EAAMf,GAAGY,EAAUoB,yBAAyBnB,GAAGc,UAAUf,EAAUoB;AAC/E;AAEH,CAtBD,CAsBGJ,MAAiBA,IAAe,CAAE;;AAK9B,IAAIK;;CACX,SAAWA;EAYPA,EAAMzB,SARN,SAASA,OAAO0B,GAAKC,GAAOC,GAAMC;IAC9B,OAAO;MACHH,KAAKA;MACLC,OAAOA;MACPC,MAAMA;MACNC,OAAOA;;AAEf;EAYAJ,EAAMjC,KAPN,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcC,GAAGyB,YAAY1B,EAAUsB,KAAK,GAAG,MAChErB,GAAGyB,YAAY1B,EAAUuB,OAAO,GAAG,MACnCtB,GAAGyB,YAAY1B,EAAUwB,MAAM,GAAG,MAClCvB,GAAGyB,YAAY1B,EAAUyB,OAAO,GAAG;AAC9C;AAEH,CAxBD,CAwBGJ,MAAUA,IAAQ,CAAE;;AAKhB,IAAIM;;CACX,SAAWA;EAUPA,EAAiB/B,SANjB,SAASA,OAAOkB,GAAOc;IACnB,OAAO;MACHd,OAAOA;MACPc,OAAOA;;AAEf;EASAD,EAAiBvC,KAJjB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcG,EAAMf,GAAGY,EAAUc,UAAUO,EAAMjC,GAAGY,EAAU4B;AAC1F;AAEH,CAnBD,CAmBGD,MAAqBA,IAAmB,CAAE;;AAKtC,IAAIE;;CACX,SAAWA;EAWPA,EAAkBjC,SAPlB,SAASA,OAAO9B,GAAOgE,GAAUC;IAC7B,OAAO;MACHjE,OAAOA;MACPgE,UAAUA;MACVC,qBAAqBA;;AAE7B;EAWAF,EAAkBzC,KANlB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcC,GAAG9B,OAAO6B,EAAUlC,WAClDmC,GAAGc,UAAUf,EAAU8B,aAAaE,EAAS5C,GAAGY,QAChDC,GAAGc,UAAUf,EAAU+B,wBAAwB9B,GAAGgC,WAAWjC,EAAU+B,qBAAqBC,EAAS5C;AACjH;AAEH,CAtBD,CAsBGyC,MAAsBA,IAAoB,CAAE;;AAIxC,IAAIK;;CACX,SAAWA;EAIPA,EAAiBC,UAAU;EAI3BD,EAAiBE,UAAU;EAI3BF,EAAiBG,SAAS;AAC7B,CAbD,CAaGH,MAAqBA,IAAmB,CAAE;;AAKtC,IAAII;;CACX,SAAWA;EAuBPA,EAAa1C,SAnBb,SAASA,OAAO2C,GAAWC,GAASC,GAAgBC,GAAcnH,GAAMoH;IACpE,IAAIC,IAAS;MACTL,WAAWA;MACXC,SAASA;;IAEb,IAAIvC,GAAG4C,QAAQJ;MACXG,EAAOH,iBAAiBA;;IAE5B,IAAIxC,GAAG4C,QAAQH;MACXE,EAAOF,eAAeA;;IAE1B,IAAIzC,GAAG4C,QAAQtH;MACXqH,EAAOrH,OAAOA;;IAElB,IAAI0E,GAAG4C,QAAQF;MACXC,EAAOD,gBAAgBA;;IAE3B,OAAOC;AACX;EAYAN,EAAalD,KAPb,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcC,GAAGP,SAASM,EAAUuC,cAActC,GAAGP,SAASM,EAAUuC,eACxFtC,GAAGc,UAAUf,EAAUyC,mBAAmBxC,GAAGP,SAASM,EAAUyC,qBAChExC,GAAGc,UAAUf,EAAU0C,iBAAiBzC,GAAGP,SAASM,EAAU0C,mBAC9DzC,GAAGc,UAAUf,EAAUzE,SAAS0E,GAAG9B,OAAO6B,EAAUzE;AAChE;AAEH,CAnCD,CAmCG+G,MAAiBA,IAAe,CAAE;;AAK9B,IAAIQ;;CACX,SAAWA;EAUPA,EAA6BlD,SAN7B,SAASA,OAAOmD,GAAUC;IACtB,OAAO;MACHD,UAAUA;MACVC,SAASA;;AAEjB;EASAF,EAA6B1D,KAJ7B,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcY,EAASxB,GAAGY,EAAU+C,aAAa9C,GAAG9B,OAAO6B,EAAUgD;AAC3F;AAEH,CAnBD,CAmBGF,MAAiCA,IAA+B,CAAE;;AAI9D,IAAIG;;CACX,SAAWA;EAIPA,EAAmBvC,QAAQ;EAI3BuC,EAAmBC,UAAU;EAI7BD,EAAmBE,cAAc;EAIjCF,EAAmBG,OAAO;AAC7B,CAjBD,CAiBGH,MAAuBA,IAAqB,CAAE;;AAM1C,IAAII;;CACX,SAAWA;EAOPA,EAAcC,cAAc;EAM5BD,EAAcE,aAAa;AAC9B,CAdD,CAcGF,MAAkBA,IAAgB,CAAE;;AAMhC,IAAIG;;CACX,SAAWA;EAKPA,EAAgBpE,KAJhB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcC,GAAG9B,OAAO6B,EAAUyD;AAC9D;AAEH,CAND,CAMGD,MAAoBA,IAAkB,CAAE;;AAKpC,IAAIE;;CACX,SAAWA;EAoBPA,EAAW9D,SAhBX,SAASA,OAAOkB,GAAOkC,GAASW,GAAUC,GAAMC,GAAQC;IACpD,IAAIlB,IAAS;MAAE9B,OAAOA;MAAOkC,SAASA;;IACtC,IAAI/C,GAAG4C,QAAQc;MACXf,EAAOe,WAAWA;;IAEtB,IAAI1D,GAAG4C,QAAQe;MACXhB,EAAOgB,OAAOA;;IAElB,IAAI3D,GAAG4C,QAAQgB;MACXjB,EAAOiB,SAASA;;IAEpB,IAAI5D,GAAG4C,QAAQiB;MACXlB,EAAOkB,qBAAqBA;;IAEhC,OAAOlB;AACX;EAiBAc,EAAWtE,KAZX,SAASA,GAAGC;IACR,IAAI0E;IACJ,IAAI/D,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MACXG,EAAMf,GAAGY,EAAUc,UACnBb,GAAG9B,OAAO6B,EAAUgD,aACnB/C,GAAG+D,OAAOhE,EAAU2D,aAAa1D,GAAGc,UAAUf,EAAU2D,eACxD1D,GAAGV,QAAQS,EAAU4D,SAAS3D,GAAG9B,OAAO6B,EAAU4D,SAAS3D,GAAGc,UAAUf,EAAU4D,WAClF3D,GAAGc,UAAUf,EAAUiE,oBAAqBhE,GAAG9B,OAA4C,UAApC4F,IAAK/D,EAAUiE,yBAAoC,MAAPF,SAAgB,IAASA,EAAGN,WAC/HxD,GAAG9B,OAAO6B,EAAU6D,WAAW5D,GAAGc,UAAUf,EAAU6D,aACtD5D,GAAGc,UAAUf,EAAU8D,uBAAuB7D,GAAGgC,WAAWjC,EAAU8D,oBAAoBhB,EAA6B1D;AACnI;AAEH,CArCD,CAqCGsE,MAAeA,IAAa,CAAE;;AAK1B,IAAIQ;;CACX,SAAWA;EAePA,EAAQtE,SAXR,SAASA,OAAOuE,GAAOC;IACnB,IAAIC,IAAO;IACX,KAAK,IAAIC,IAAK,GAAGA,IAAKC,UAAU9H,QAAQ6H;MACpCD,EAAKC,IAAK,KAAKC,UAAUD;;IAE7B,IAAI1B,IAAS;MAAEuB,OAAOA;MAAOC,SAASA;;IACtC,IAAInE,GAAG4C,QAAQwB,MAASA,EAAK5H,SAAS;MAClCmG,EAAO2B,YAAYF;;IAEvB,OAAOzB;AACX;EASAsB,EAAQ9E,KAJR,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcC,GAAG9B,OAAO6B,EAAUmE,UAAUlE,GAAG9B,OAAO6B,EAAUoE;AACtF;AAEH,CAxBD,CAwBGF,MAAYA,IAAU,CAAE;;AAKpB,IAAIlC;;CACX,SAAWA;EASPA,EAASwC,UAHT,SAASA,QAAQ1D,GAAO2D;IACpB,OAAO;MAAE3D,OAAOA;MAAO2D,SAASA;;AACpC;EAUAzC,EAAS0C,SAHT,SAASA,OAAOC,GAAUF;IACtB,OAAO;MAAE3D,OAAO;QAAEN,OAAOmE;QAAUlE,KAAKkE;;MAAYF,SAASA;;AACjE;EASAzC,EAAS4C,MAHT,SAASA,IAAI9D;IACT,OAAO;MAAEA,OAAOA;MAAO2D,SAAS;;AACpC;EAQAzC,EAAS5C,KANT,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MACjBC,GAAG9B,OAAO6B,EAAUyE,YACpBtE,EAAMf,GAAGY,EAAUc;AAC9B;AAEH,CAlCD,CAkCGkB,MAAaA,IAAW,CAAE;;AACtB,IAAI6C;;CACX,SAAWA;EAWPA,EAAiBjF,SAVjB,SAASA,OAAO9B,GAAOgH,GAAmBC;IACtC,IAAInC,IAAS;MAAE9E,OAAOA;;IACtB,SAA0BiD,MAAtB+D;MACAlC,EAAOkC,oBAAoBA;;IAE/B,SAAoB/D,MAAhBgE;MACAnC,EAAOmC,cAAcA;;IAEzB,OAAOnC;AACX;EAQAiC,EAAiBzF,KANjB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcC,GAAG9B,OAAO6B,EAAUlC,WACrDmC,GAAG+E,QAAQhF,EAAU8E,2BAAsD/D,MAAhCf,EAAU8E,uBACrD7E,GAAG9B,OAAO6B,EAAU+E,qBAA0ChE,MAA1Bf,EAAU+E;AACvD;AAEH,CAnBD,CAmBGF,MAAqBA,IAAmB,CAAE;;AACtC,IAAII;;CACX,SAAWA;EAKPA,EAA2B7F,KAJ3B,SAASA,GAAGC;IAER,OAAOY,GAAG9B,OADMkB;AAEpB;AAEH,CAND,CAMG4F,MAA+BA,IAA6B,CAAE;;AAC1D,IAAIC;;CACX,SAAWA;EAWPA,EAAkBV,UAHlB,SAASA,QAAQ1D,GAAO2D,GAASU;IAC7B,OAAO;MAAErE,OAAOA;MAAO2D,SAASA;MAASW,cAAcD;;AAC3D;EAYAD,EAAkBR,SAHlB,SAASA,OAAOC,GAAUF,GAASU;IAC/B,OAAO;MAAErE,OAAO;QAAEN,OAAOmE;QAAUlE,KAAKkE;;MAAYF,SAASA;MAASW,cAAcD;;AACxF;EAWAD,EAAkBN,MAHlB,SAASA,IAAI9D,GAAOqE;IAChB,OAAO;MAAErE,OAAOA;MAAO2D,SAAS;MAAIW,cAAcD;;AACtD;EAMAD,EAAkB9F,KAJlB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAO2C,EAAS5C,GAAGY,OAAe6E,EAAiBzF,GAAGY,EAAUoF,iBAAiBH,EAA2B7F,GAAGY,EAAUoF;AAC7H;AAEH,CAtCD,CAsCGF,MAAsBA,IAAoB,CAAE;;AAKxC,IAAIG;;CACX,SAAWA;EAOPA,EAAiBzF,SAHjB,SAASA,OAAO0F,GAAcC;IAC1B,OAAO;MAAED,cAAcA;MAAcC,OAAOA;;AAChD;EAQAF,EAAiBjG,KANjB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MACXwF,EAAwCpG,GAAGY,EAAUsF,iBACrDtI,MAAMyI,QAAQzF,EAAUuF;AACnC;AAEH,CAfD,CAeGF,MAAqBA,IAAmB,CAAE;;AACtC,IAAIK;;CACX,SAAWA;EAcPA,EAAW9F,SAbX,SAASA,OAAOiB,GAAK8E,GAASR;IAC1B,IAAIvC,IAAS;MACTrH,MAAM;MACNsF,KAAKA;;IAET,SAAgBE,MAAZ4E,WAAgD5E,MAAtB4E,EAAQC,kBAAsD7E,MAA3B4E,EAAQE;MACrEjD,EAAO+C,UAAUA;;IAErB,SAAmB5E,MAAfoE;MACAvC,EAAOwC,eAAeD;;IAE1B,OAAOvC;AACX;EAOA8C,EAAWtG,KALX,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,KAAgC,aAAnBA,EAAUzE,QAAqB0E,GAAG9B,OAAO6B,EAAUa,cAA+BE,MAAtBf,EAAU2F,iBACpD5E,MAAhCf,EAAU2F,QAAQC,aAA2B3F,GAAG+E,QAAQhF,EAAU2F,QAAQC,qBAAqD7E,MAArCf,EAAU2F,QAAQE,kBAAgC5F,GAAG+E,QAAQhF,EAAU2F,QAAQE,2BAAkD9E,MAA3Bf,EAAUoF,gBAA8BH,EAA2B7F,GAAGY,EAAUoF;AAC1R;AAEH,CArBD,CAqBGM,MAAeA,IAAa,CAAE;;AAC1B,IAAII;;CACX,SAAWA;EAePA,EAAWlG,SAdX,SAASA,OAAOmG,GAAQC,GAAQL,GAASR;IACrC,IAAIvC,IAAS;MACTrH,MAAM;MACNwK,QAAQA;MACRC,QAAQA;;IAEZ,SAAgBjF,MAAZ4E,WAAgD5E,MAAtB4E,EAAQC,kBAAsD7E,MAA3B4E,EAAQE;MACrEjD,EAAO+C,UAAUA;;IAErB,SAAmB5E,MAAfoE;MACAvC,EAAOwC,eAAeD;;IAE1B,OAAOvC;AACX;EAOAkD,EAAW1G,KALX,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,KAAgC,aAAnBA,EAAUzE,QAAqB0E,GAAG9B,OAAO6B,EAAU+F,WAAW9F,GAAG9B,OAAO6B,EAAUgG,iBAAkCjF,MAAtBf,EAAU2F,iBACtF5E,MAAhCf,EAAU2F,QAAQC,aAA2B3F,GAAG+E,QAAQhF,EAAU2F,QAAQC,qBAAqD7E,MAArCf,EAAU2F,QAAQE,kBAAgC5F,GAAG+E,QAAQhF,EAAU2F,QAAQE,2BAAkD9E,MAA3Bf,EAAUoF,gBAA8BH,EAA2B7F,GAAGY,EAAUoF;AAC1R;AAEH,CAtBD,CAsBGU,MAAeA,IAAa,CAAE;;AAC1B,IAAIG;;CACX,SAAWA;EAcPA,EAAWrG,SAbX,SAASA,OAAOiB,GAAK8E,GAASR;IAC1B,IAAIvC,IAAS;MACTrH,MAAM;MACNsF,KAAKA;;IAET,SAAgBE,MAAZ4E,WAAgD5E,MAAtB4E,EAAQ1K,kBAAyD8F,MAA9B4E,EAAQO;MACrEtD,EAAO+C,UAAUA;;IAErB,SAAmB5E,MAAfoE;MACAvC,EAAOwC,eAAeD;;IAE1B,OAAOvC;AACX;EAOAqD,EAAW7G,KALX,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,KAAgC,aAAnBA,EAAUzE,QAAqB0E,GAAG9B,OAAO6B,EAAUa,cAA+BE,MAAtBf,EAAU2F,iBACpD5E,MAAhCf,EAAU2F,QAAQ1K,aAA2BgF,GAAG+E,QAAQhF,EAAU2F,QAAQ1K,qBAAwD8F,MAAxCf,EAAU2F,QAAQO,qBAAmCjG,GAAG+E,QAAQhF,EAAU2F,QAAQO,8BAAqDnF,MAA3Bf,EAAUoF,gBAA8BH,EAA2B7F,GAAGY,EAAUoF;AAChS;AAEH,CArBD,CAqBGa,MAAeA,IAAa,CAAE;;AAC1B,IAAIE;;CACX,SAAWA;EAcPA,EAAc/G,KAbd,SAASA,GAAGC;IAER,OADgBA,WAEW0B,MAFX1B,EAED+G,gBAAuDrF,MAFtD1B,EAEkCgH,0BACftF,MAHnB1B,EAGDgH,mBAHChH,EAG0CgH,gBAAgBC,OAAM,SAAUC;MAClF,IAAItG,GAAG9B,OAAOoI,EAAOhL;QACjB,OAAOmK,EAAWtG,GAAGmH,MAAWT,EAAW1G,GAAGmH,MAAWN,EAAW7G,GAAGmH;;QAGvE,OAAOlB,EAAiBjG,GAAGmH;;AAElC;AACT;AAEH,CAfD,CAeGJ,MAAkBA,IAAgB,CAAE;;AACvC,IAAIK,IAAoC;EACpC,SAASA,mBAAmBjB,GAAOkB;IAC/BC,KAAKnB,QAAQA;IACbmB,KAAKD,oBAAoBA;AAC7B;EACAD,mBAAmBG,UAAUjC,SAAS,SAAUC,GAAUF,GAASU;IAC/D,IAAIyB;IACJ,IAAIC;IACJ,SAAmB9F,MAAfoE;MACAyB,IAAO5E,EAAS0C,OAAOC,GAAUF;WAEhC,IAAIQ,EAA2B7F,GAAG+F,IAAa;MAChD0B,IAAK1B;MACLyB,IAAO1B,EAAkBR,OAAOC,GAAUF,GAASU;AACvD,WACK;MACDuB,KAAKI,wBAAwBJ,KAAKD;MAClCI,IAAKH,KAAKD,kBAAkBM,OAAO5B;MACnCyB,IAAO1B,EAAkBR,OAAOC,GAAUF,GAASoC;AACvD;IACAH,KAAKnB,MAAMjJ,KAAKsK;IAChB,SAAW7F,MAAP8F;MACA,OAAOA;;;EAGfL,mBAAmBG,UAAUnC,UAAU,SAAU1D,GAAO2D,GAASU;IAC7D,IAAIyB;IACJ,IAAIC;IACJ,SAAmB9F,MAAfoE;MACAyB,IAAO5E,EAASwC,QAAQ1D,GAAO2D;WAE9B,IAAIQ,EAA2B7F,GAAG+F,IAAa;MAChD0B,IAAK1B;MACLyB,IAAO1B,EAAkBV,QAAQ1D,GAAO2D,GAASU;AACrD,WACK;MACDuB,KAAKI,wBAAwBJ,KAAKD;MAClCI,IAAKH,KAAKD,kBAAkBM,OAAO5B;MACnCyB,IAAO1B,EAAkBV,QAAQ1D,GAAO2D,GAASoC;AACrD;IACAH,KAAKnB,MAAMjJ,KAAKsK;IAChB,SAAW7F,MAAP8F;MACA,OAAOA;;;EAGfL,mBAAmBG,UAAUK,SAAS,SAAUlG,GAAOqE;IACnD,IAAIyB;IACJ,IAAIC;IACJ,SAAmB9F,MAAfoE;MACAyB,IAAO5E,EAAS4C,IAAI9D;WAEnB,IAAImE,EAA2B7F,GAAG+F,IAAa;MAChD0B,IAAK1B;MACLyB,IAAO1B,EAAkBN,IAAI9D,GAAOqE;AACxC,WACK;MACDuB,KAAKI,wBAAwBJ,KAAKD;MAClCI,IAAKH,KAAKD,kBAAkBM,OAAO5B;MACnCyB,IAAO1B,EAAkBN,IAAI9D,GAAO+F;AACxC;IACAH,KAAKnB,MAAMjJ,KAAKsK;IAChB,SAAW7F,MAAP8F;MACA,OAAOA;;;EAGfL,mBAAmBG,UAAUM,MAAM,SAAUL;IACzCF,KAAKnB,MAAMjJ,KAAKsK;;EAEpBJ,mBAAmBG,UAAUO,MAAM;IAC/B,OAAOR,KAAKnB;;EAEhBiB,mBAAmBG,UAAUQ,QAAQ;IACjCT,KAAKnB,MAAM6B,OAAO,GAAGV,KAAKnB,MAAM9I;;EAEpC+J,mBAAmBG,UAAUG,0BAA0B,SAAUzH;IAC7D,SAAc0B,MAAV1B;MACA,MAAM,IAAIqB,MAAM;;;EAGxB,OAAO8F;AACX,CAhFwC;;AAoFxC,IAAIa,IAAmC;EACnC,SAASA,kBAAkBC;IACvBZ,KAAKa,oBAA+BxG,MAAhBuG,IAA4BzK,OAAO+C,OAAO,QAAQ0H;IACtEZ,KAAKc,WAAW;IAChBd,KAAKe,QAAQ;AACjB;EACAJ,kBAAkBV,UAAUO,MAAM;IAC9B,OAAOR,KAAKa;;EAEhB1K,OAAO6K,eAAeL,kBAAkBV,WAAW,QAAQ;IACvDgB,KAAK;MACD,OAAOjB,KAAKe;AACf;IACDG,aAAY;IACZC,eAAc;;EAElBR,kBAAkBV,UAAUI,SAAS,SAAUe,GAAgB3C;IAC3D,IAAI0B;IACJ,IAAI5B,EAA2B7F,GAAG0I;MAC9BjB,IAAKiB;WAEJ;MACDjB,IAAKH,KAAKqB;MACV5C,IAAa2C;AACjB;IACA,SAA8B/G,MAA1B2F,KAAKa,aAAaV;MAClB,MAAM,IAAInG,MAAM,MAAMC,OAAOkG,GAAI;;IAErC,SAAmB9F,MAAfoE;MACA,MAAM,IAAIzE,MAAM,iCAAiCC,OAAOkG;;IAE5DH,KAAKa,aAAaV,KAAM1B;IACxBuB,KAAKe;IACL,OAAOZ;;EAEXQ,kBAAkBV,UAAUoB,SAAS;IACjCrB,KAAKc;IACL,OAAOd,KAAKc,SAASQ;;EAEzB,OAAOX;AACX,CAxCuC;;CA4CjB;EAClB,SAASY,gBAAgBC;IACrB,IAAIC,IAAQzB;IACZA,KAAK0B,mBAAmBvL,OAAO+C,OAAO;IACtC,SAAsBmB,MAAlBmH,GAA6B;MAC7BxB,KAAK2B,iBAAiBH;MACtB,IAAIA,EAAc7B,iBAAiB;QAC/BK,KAAK4B,qBAAqB,IAAIjB,EAAkBa,EAAczB;QAC9DyB,EAAczB,oBAAoBC,KAAK4B,mBAAmBpB;QAC1DgB,EAAc7B,gBAAgBkC,SAAQ,SAAUhC;UAC5C,IAAIlB,EAAiBjG,GAAGmH,IAAS;YAC7B,IAAIiC,IAAiB,IAAIhC,EAAmBD,EAAOhB,OAAO4C,EAAMG;YAChEH,EAAMC,iBAAiB7B,EAAOjB,aAAazE,OAAO2H;AACtD;AACJ;AACJ,aACK,IAAIN,EAAc9B;QACnBvJ,OAAOD,KAAKsL,EAAc9B,SAASmC,SAAQ,SAAUE;UACjD,IAAID,IAAiB,IAAIhC,EAAmB0B,EAAc9B,QAAQqC;UAClEN,EAAMC,iBAAiBK,KAAOD;AAClC;;AAER;MAEI9B,KAAK2B,iBAAiB;;AAE9B;EACAxL,OAAO6K,eAAeO,gBAAgBtB,WAAW,QAAQ;IAKrDgB,KAAK;MACDjB,KAAKgC;MACL,SAAgC3H,MAA5B2F,KAAK4B;QACL,IAAqC,MAAjC5B,KAAK4B,mBAAmBK;UACxBjC,KAAK2B,eAAe5B,yBAAoB1F;;UAGxC2F,KAAK2B,eAAe5B,oBAAoBC,KAAK4B,mBAAmBpB;;;MAGxE,OAAOR,KAAK2B;AACf;IACDT,aAAY;IACZC,eAAc;;EAElBI,gBAAgBtB,UAAUiC,oBAAoB,SAAUH;IACpD,IAAIjD,EAAwCpG,GAAGqJ,IAAM;MACjD/B,KAAKgC;MACL,SAA4C3H,MAAxC2F,KAAK2B,eAAehC;QACpB,MAAM,IAAI3F,MAAM;;MAEpB,IAAI4E,IAAe;QAAEzE,KAAK4H,EAAI5H;QAAKgI,SAASJ,EAAII;;MAEhD,MADIjG,IAAS8D,KAAK0B,iBAAiB9C,EAAazE,OACnC;QAMT6F,KAAK2B,eAAehC,gBAAgB/J,KAJb;UACnBgJ,cAAcA;UACdC,OAHAA,IAAQ;;QAMZ3C,IAAS,IAAI4D,EAAmBjB,GAAOmB,KAAK4B;QAC5C5B,KAAK0B,iBAAiB9C,EAAazE,OAAO+B;AAC9C;MACA,OAAOA;AACX,WACK;MACD8D,KAAKoC;MACL,SAAoC/H,MAAhC2F,KAAK2B,eAAejC;QACpB,MAAM,IAAI1F,MAAM;;MAEpB,IAAIkC;MACJ,MADIA,IAAS8D,KAAK0B,iBAAiBK,KACtB;QACT,IAAIlD;QACJmB,KAAK2B,eAAejC,QAAQqC,KADxBlD,IAAQ;QAEZ3C,IAAS,IAAI4D,EAAmBjB;QAChCmB,KAAK0B,iBAAiBK,KAAO7F;AACjC;MACA,OAAOA;AACX;;EAEJqF,gBAAgBtB,UAAU+B,sBAAsB;IAC5C,SAA4C3H,MAAxC2F,KAAK2B,eAAehC,wBAAiEtF,MAAhC2F,KAAK2B,eAAejC,SAAuB;MAChGM,KAAK4B,qBAAqB,IAAIjB;MAC9BX,KAAK2B,eAAehC,kBAAkB;MACtCK,KAAK2B,eAAe5B,oBAAoBC,KAAK4B,mBAAmBpB;AACpE;;EAEJe,gBAAgBtB,UAAUmC,cAAc;IACpC,SAA4C/H,MAAxC2F,KAAK2B,eAAehC,wBAAiEtF,MAAhC2F,KAAK2B,eAAejC;MACzEM,KAAK2B,eAAejC,UAAUvJ,OAAO+C,OAAO;;;EAGpDqI,gBAAgBtB,UAAUoC,aAAa,SAAUlI,GAAKmI,GAAqBrD;IACvEe,KAAKgC;IACL,SAA4C3H,MAAxC2F,KAAK2B,eAAehC;MACpB,MAAM,IAAI3F,MAAM;;IAEpB,IAAIyE;IACJ,IAAIN,EAAiBzF,GAAG4J,MAAwB/D,EAA2B7F,GAAG4J;MAC1E7D,IAAa6D;;MAGbrD,IAAUqD;;IAEd,IAAIC;IACJ,IAAIpC;IACJ,SAAmB9F,MAAfoE;MACA8D,IAAYvD,EAAW9F,OAAOiB,GAAK8E;WAElC;MACDkB,IAAK5B,EAA2B7F,GAAG+F,KAAcA,IAAauB,KAAK4B,mBAAmBvB,OAAO5B;MAC7F8D,IAAYvD,EAAW9F,OAAOiB,GAAK8E,GAASkB;AAChD;IACAH,KAAK2B,eAAehC,gBAAgB/J,KAAK2M;IACzC,SAAWlI,MAAP8F;MACA,OAAOA;;;EAGfoB,gBAAgBtB,UAAUuC,aAAa,SAAUnD,GAAQC,GAAQgD,GAAqBrD;IAClFe,KAAKgC;IACL,SAA4C3H,MAAxC2F,KAAK2B,eAAehC;MACpB,MAAM,IAAI3F,MAAM;;IAEpB,IAAIyE;IACJ,IAAIN,EAAiBzF,GAAG4J,MAAwB/D,EAA2B7F,GAAG4J;MAC1E7D,IAAa6D;;MAGbrD,IAAUqD;;IAEd,IAAIC;IACJ,IAAIpC;IACJ,SAAmB9F,MAAfoE;MACA8D,IAAYnD,EAAWlG,OAAOmG,GAAQC,GAAQL;WAE7C;MACDkB,IAAK5B,EAA2B7F,GAAG+F,KAAcA,IAAauB,KAAK4B,mBAAmBvB,OAAO5B;MAC7F8D,IAAYnD,EAAWlG,OAAOmG,GAAQC,GAAQL,GAASkB;AAC3D;IACAH,KAAK2B,eAAehC,gBAAgB/J,KAAK2M;IACzC,SAAWlI,MAAP8F;MACA,OAAOA;;;EAGfoB,gBAAgBtB,UAAUwC,aAAa,SAAUtI,GAAKmI,GAAqBrD;IACvEe,KAAKgC;IACL,SAA4C3H,MAAxC2F,KAAK2B,eAAehC;MACpB,MAAM,IAAI3F,MAAM;;IAEpB,IAAIyE;IACJ,IAAIN,EAAiBzF,GAAG4J,MAAwB/D,EAA2B7F,GAAG4J;MAC1E7D,IAAa6D;;MAGbrD,IAAUqD;;IAEd,IAAIC;IACJ,IAAIpC;IACJ,SAAmB9F,MAAfoE;MACA8D,IAAYhD,EAAWrG,OAAOiB,GAAK8E;WAElC;MACDkB,IAAK5B,EAA2B7F,GAAG+F,KAAcA,IAAauB,KAAK4B,mBAAmBvB,OAAO5B;MAC7F8D,IAAYhD,EAAWrG,OAAOiB,GAAK8E,GAASkB;AAChD;IACAH,KAAK2B,eAAehC,gBAAgB/J,KAAK2M;IACzC,SAAWlI,MAAP8F;MACA,OAAOA;;;AAInB,CA7KsB;;AAmLf,IAAIuC;;CACX,SAAWA;EAQPA,EAAuBxJ,SAHvB,SAASA,OAAOiB;IACZ,OAAO;MAAEA,KAAKA;;AAClB;EASAuI,EAAuBhK,KAJvB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcC,GAAG9B,OAAO6B,EAAUa;AACxD;AAEH,CAjBD,CAiBGuI,MAA2BA,IAAyB,CAAE;;AAKlD,IAAIC;;CACX,SAAWA;EASPA,EAAgCzJ,SAHhC,SAASA,OAAOiB,GAAKgI;IACjB,OAAO;MAAEhI,KAAKA;MAAKgI,SAASA;;AAChC;EASAQ,EAAgCjK,KAJhC,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcC,GAAG9B,OAAO6B,EAAUa,QAAQZ,GAAGV,QAAQS,EAAU6I;AACrF;AAEH,CAlBD,CAkBGQ,MAAoCA,IAAkC,CAAE;;AAKpE,IAAI7D;;CACX,SAAWA;EASPA,EAAwC5F,SAHxC,SAASA,OAAOiB,GAAKgI;IACjB,OAAO;MAAEhI,KAAKA;MAAKgI,SAASA;;AAChC;EASArD,EAAwCpG,KAJxC,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcC,GAAG9B,OAAO6B,EAAUa,SAA+B,SAAtBb,EAAU6I,WAAoB5I,GAAGV,QAAQS,EAAU6I;AACpH;AAEH,CAlBD,CAkBGrD,MAA4CA,IAA0C,CAAE;;AAKpF,IAAI8D;;CACX,SAAWA;EAWPA,EAAiB1J,SAHjB,SAASA,OAAOiB,GAAK0I,GAAYV,GAASxL;IACtC,OAAO;MAAEwD,KAAKA;MAAK0I,YAAYA;MAAYV,SAASA;MAASxL,MAAMA;;AACvE;EASAiM,EAAiBlK,KAJjB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcC,GAAG9B,OAAO6B,EAAUa,QAAQZ,GAAG9B,OAAO6B,EAAUuJ,eAAetJ,GAAGV,QAAQS,EAAU6I,YAAY5I,GAAG9B,OAAO6B,EAAU3C;AACxJ;AAEH,CApBD,CAoBGiM,MAAqBA,IAAmB,CAAE;;AAQtC,IAAIE;;CACX,SAAWA;EAIPA,EAAWC,YAAY;EAIvBD,EAAWE,WAAW;EAQtBF,EAAWpK,KAJX,SAASA,GAAGC;IAER,OADgBA,MACKmK,EAAWC,aADhBpK,MAC2CmK,EAAWE;AAC1E;AAEH,CAjBD,CAiBGF,MAAeA,IAAa,CAAE;;AAC1B,IAAIG;;CACX,SAAWA;EAQPA,EAAcvK,KAJd,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcb,MAAUmK,EAAWpK,GAAGY,EAAUzE,SAAS0E,GAAG9B,OAAO6B,EAAUX;AAC3F;AAEH,CATD,CASGsK,MAAkBA,IAAgB,CAAE;;AAIhC,IAAIC;;CACX,SAAWA;EACPA,EAAmBC,OAAO;EAC1BD,EAAmBE,SAAS;EAC5BF,EAAmBG,WAAW;EAC9BH,EAAmBI,cAAc;EACjCJ,EAAmBK,QAAQ;EAC3BL,EAAmBM,WAAW;EAC9BN,EAAmBO,QAAQ;EAC3BP,EAAmBQ,YAAY;EAC/BR,EAAmBS,SAAS;EAC5BT,EAAmBU,WAAW;EAC9BV,EAAmBW,OAAO;EAC1BX,EAAmBY,QAAQ;EAC3BZ,EAAmBa,OAAO;EAC1Bb,EAAmBc,UAAU;EAC7Bd,EAAmBe,UAAU;EAC7Bf,EAAmBvI,QAAQ;EAC3BuI,EAAmBgB,OAAO;EAC1BhB,EAAmBiB,YAAY;EAC/BjB,EAAmBkB,SAAS;EAC5BlB,EAAmBmB,aAAa;EAChCnB,EAAmBoB,WAAW;EAC9BpB,EAAmBqB,SAAS;EAC5BrB,EAAmBsB,QAAQ;EAC3BtB,EAAmBuB,WAAW;EAC9BvB,EAAmBwB,gBAAgB;AACtC,CA1BD,CA0BGxB,MAAuBA,IAAqB,CAAE;;AAK1C,IAAIyB;;CACX,SAAWA;EAIPA,EAAiB5B,YAAY;EAW7B4B,EAAiBV,UAAU;AAC9B,CAhBD,CAgBGU,MAAqBA,IAAmB,CAAE;;AAOtC,IAAIC;;CACX,SAAWA;EAIPA,EAAkB/H,aAAa;AAClC,CALD,CAKG+H,MAAsBA,IAAoB,CAAE;;AAMxC,IAAIC;;CACX,SAAWA;EAOPA,EAAkB3L,SAHlB,SAASA,OAAO6E,GAASC,GAAQF;IAC7B,OAAO;MAAEC,SAASA;MAASC,QAAQA;MAAQF,SAASA;;AACxD;EASA+G,EAAkBnM,KAJlB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,KAAaC,GAAG9B,OAAO6B,EAAUyE,YAAYtE,EAAMf,GAAGY,EAAU0E,WAAWvE,EAAMf,GAAGY,EAAUwE;AACzG;AAEH,CAhBD,CAgBG+G,MAAsBA,IAAoB,CAAE;;AAOxC,IAAIC;;CACX,SAAWA;EAQPA,EAAeC,OAAO;EAUtBD,EAAeE,oBAAoB;AACtC,CAnBD,CAmBGF,MAAmBA,IAAiB,CAAE;;AAClC,IAAIG;;CACX,SAAWA;EAMPA,EAA2BvM,KAL3B,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,MAAcC,GAAG9B,OAAO6B,EAAU4L,gBAAgC7K,MAArBf,EAAU4L,YACzD3L,GAAG9B,OAAO6B,EAAU+E,qBAA0ChE,MAA1Bf,EAAU+E;AACvD;AAEH,CAPD,CAOG4G,MAA+BA,IAA6B,CAAE;;AAK1D,IAAIE;;CACX,SAAWA;EAQPA,EAAejM,SAHf,SAASA,OAAO9B;IACZ,OAAO;MAAEA,OAAOA;;AACpB;AAEH,CATD,CASG+N,MAAmBA,IAAiB,CAAE;;AAKlC,IAAIC;;CACX,SAAWA;EAUPA,EAAelM,SAHf,SAASA,OAAOmM,GAAOC;IACnB,OAAO;MAAED,OAAOA,IAAQA,IAAQ;MAAIC,gBAAgBA;;AACxD;AAEH,CAXD,CAWGF,MAAmBA,IAAiB,CAAE;;AAClC,IAAIG;;CACX,SAAWA;EASPA,EAAaC,gBAHb,SAASA,cAAcC;IACnB,OAAOA,EAAU3H,QAAQ,yBAAyB;AACtD;EASAyH,EAAa7M,KAJb,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG9B,OAAO6B,MAAeC,GAAGC,cAAcF,MAAcC,GAAG9B,OAAO6B,EAAUoM,aAAanM,GAAG9B,OAAO6B,EAAUX;AACxH;AAEH,CAlBD,CAkBG4M,MAAiBA,IAAe,CAAE;;AAC9B,IAAII;;CACX,SAAWA;EAUPA,EAAMjN,KANN,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,SAASW,KAAaC,GAAGC,cAAcF,OAAe2J,EAAcvK,GAAGY,EAAU5G,aAC7E6S,EAAa7M,GAAGY,EAAU5G,aAC1B6G,GAAGgC,WAAWjC,EAAU5G,UAAU6S,EAAa7M,cAAyB2B,MAAhB1B,EAAMyB,SAAuBX,EAAMf,GAAGC,EAAMyB;AAC5G;AAEH,CAXD,CAWGuL,MAAUA,IAAQ,CAAE;;AAKhB,IAAIC;;CACX,SAAWA;EAUPA,EAAqB1M,SAHrB,SAASA,OAAO9B,GAAOyO;IACnB,OAAOA,IAAgB;MAAEzO,OAAOA;MAAOyO,eAAeA;QAAkB;MAAEzO,OAAOA;;AACrF;AAEH,CAXD,CAWGwO,MAAyBA,IAAuB,CAAE;;AAK9C,IAAIE;;CACX,SAAWA;EAkBPA,EAAqB5M,SAjBrB,SAASA,OAAO9B,GAAOyO;IACnB,IAAIE,IAAa;IACjB,KAAK,IAAInI,IAAK,GAAGA,IAAKC,UAAU9H,QAAQ6H;MACpCmI,EAAWnI,IAAK,KAAKC,UAAUD;;IAEnC,IAAI1B,IAAS;MAAE9E,OAAOA;;IACtB,IAAImC,GAAG4C,QAAQ0J;MACX3J,EAAO2J,gBAAgBA;;IAE3B,IAAItM,GAAG4C,QAAQ4J;MACX7J,EAAO6J,aAAaA;;MAGpB7J,EAAO6J,aAAa;;IAExB,OAAO7J;AACX;AAEH,CAnBD,CAmBG4J,MAAyBA,IAAuB,CAAE;;AAI9C,IAAIE;;CACX,SAAWA;EAIPA,EAAsB7C,OAAO;EAI7B6C,EAAsBC,OAAO;EAI7BD,EAAsBE,QAAQ;AACjC,CAbD,CAaGF,MAA0BA,IAAwB,CAAE;;AAKhD,IAAIG;;CACX,SAAWA;EAaPA,EAAkBjN,SAPlB,SAASA,OAAOkB,GAAOvF;IACnB,IAAIqH,IAAS;MAAE9B,OAAOA;;IACtB,IAAIb,GAAG+D,OAAOzI;MACVqH,EAAOrH,OAAOA;;IAElB,OAAOqH;AACX;AAEH,CAdD,CAcGiK,OAAsBA,KAAoB,CAAE;;AAIxC,IAAIC;;CACX,SAAWA;EACPA,EAAWlC,OAAO;EAClBkC,EAAWzC,SAAS;EACpByC,EAAWC,YAAY;EACvBD,EAAWE,UAAU;EACrBF,EAAW3C,QAAQ;EACnB2C,EAAWhD,SAAS;EACpBgD,EAAWxC,WAAW;EACtBwC,EAAW7C,QAAQ;EACnB6C,EAAW9C,cAAc;EACzB8C,EAAWrC,OAAO;EAClBqC,EAAW1C,YAAY;EACvB0C,EAAW/C,WAAW;EACtB+C,EAAW5C,WAAW;EACtB4C,EAAW9B,WAAW;EACtB8B,EAAWG,SAAS;EACpBH,EAAW/M,SAAS;EACpB+M,EAAWI,UAAU;EACrBJ,EAAW9P,QAAQ;EACnB8P,EAAWjQ,SAAS;EACpBiQ,EAAWK,MAAM;EACjBL,EAAWM,OAAO;EAClBN,EAAW/B,aAAa;EACxB+B,EAAW7B,SAAS;EACpB6B,EAAW5B,QAAQ;EACnB4B,EAAW3B,WAAW;EACtB2B,EAAW1B,gBAAgB;AAC9B,CA3BD,CA2BG0B,OAAeA,KAAa,CAAE;;AAM1B,IAAIO;;CACX,SAAWA;EAIPA,EAAU9J,aAAa;AAC1B,CALD,CAKG8J,OAAcA,KAAY,CAAE;;AACxB,IAAIC;;CACX,SAAWA;EAqBPA,EAAkB1N,SAXlB,SAASA,OAAO/D,GAAMN,GAAMuF,GAAOD,GAAK0M;IACpC,IAAI3K,IAAS;MACT/G,MAAMA;MACNN,MAAMA;MACNwH,UAAU;QAAElC,KAAKA;QAAKC,OAAOA;;;IAEjC,IAAIyM;MACA3K,EAAO2K,gBAAgBA;;IAE3B,OAAO3K;AACX;AAEH,CAtBD,CAsBG0K,OAAsBA,KAAoB,CAAE;;AACxC,IAAIE;;CACX,SAAWA;EAePA,EAAgB5N,SALhB,SAASA,OAAO/D,GAAMN,GAAMsF,GAAKC;IAC7B,YAAiBC,MAAVD,IACD;MAAEjF,MAAMA;MAAMN,MAAMA;MAAMwH,UAAU;QAAElC,KAAKA;QAAKC,OAAOA;;QACvD;MAAEjF,MAAMA;MAAMN,MAAMA;MAAMwH,UAAU;QAAElC,KAAKA;;;AACrD;AAEH,CAhBD,CAgBG2M,OAAoBA,KAAkB,CAAE;;AACpC,IAAIC;;CACX,SAAWA;EAwBPA,EAAe7N,SAbf,SAASA,OAAO/D,GAAM+P,GAAQrQ,GAAMuF,GAAO4M,GAAgBC;IACvD,IAAI/K,IAAS;MACT/G,MAAMA;MACN+P,QAAQA;MACRrQ,MAAMA;MACNuF,OAAOA;MACP4M,gBAAgBA;;IAEpB,SAAiB3M,MAAb4M;MACA/K,EAAO+K,WAAWA;;IAEtB,OAAO/K;AACX;EAeA6K,EAAerO,KAVf,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,KACHC,GAAG9B,OAAO6B,EAAUnE,SAASoE,GAAG+D,OAAOhE,EAAUzE,SACjD4E,EAAMf,GAAGY,EAAUc,UAAUX,EAAMf,GAAGY,EAAU0N,yBAC1B3M,MAArBf,EAAU4L,UAAwB3L,GAAG9B,OAAO6B,EAAU4L,kBAC7B7K,MAAzBf,EAAU4N,cAA4B3N,GAAG+E,QAAQhF,EAAU4N,sBACpC7M,MAAvBf,EAAU2N,YAA0B3Q,MAAMyI,QAAQzF,EAAU2N,oBACzC5M,MAAnBf,EAAU6N,QAAsB7Q,MAAMyI,QAAQzF,EAAU6N;AACjE;AAEH,CAvCD,CAuCGJ,OAAmBA,KAAiB,CAAE;;AAIlC,IAAIK;;CACX,SAAWA;EAIPA,EAAeC,QAAQ;EAIvBD,EAAeE,WAAW;EAI1BF,EAAeG,WAAW;EAY1BH,EAAeI,kBAAkB;EAWjCJ,EAAeK,iBAAiB;EAahCL,EAAeM,kBAAkB;EAMjCN,EAAeO,SAAS;EAIxBP,EAAeQ,wBAAwB;EASvCR,EAAeS,eAAe;AACjC,CApED,CAoEGT,OAAmBA,KAAiB,CAAE;;AAMlC,IAAIU;;CACX,SAAWA;EAIPA,EAAsBC,UAAU;EAOhCD,EAAsBE,YAAY;AACrC,CAZD,CAYGF,OAA0BA,KAAwB,CAAE;;AAKhD,IAAIG;;CACX,SAAWA;EAcPA,EAAkB/O,SAVlB,SAASA,OAAOgP,GAAaC,GAAMC;IAC/B,IAAIlM,IAAS;MAAEgM,aAAaA;;IAC5B,IAAIC;MACAjM,EAAOiM,OAAOA;;IAElB,IAAIC;MACAlM,EAAOkM,cAAcA;;IAEzB,OAAOlM;AACX;EAWA+L,EAAkBvP,KANlB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcC,GAAGgC,WAAWjC,EAAU4O,aAAalL,EAAWtE,aACrD2B,MAAnBf,EAAU6O,QAAsB5O,GAAGgC,WAAWjC,EAAU6O,MAAM5O,GAAG9B,kBACvC4C,MAA1Bf,EAAU8O,eAA6B9O,EAAU8O,gBAAgBN,GAAsBC,WAAWzO,EAAU8O,gBAAgBN,GAAsBE;AAC9J;AAEH,CAzBD,CAyBGC,OAAsBA,KAAoB,CAAE;;AACxC,IAAII;;CACX,SAAWA;EAmBPA,EAAWnP,SAlBX,SAASA,OAAOuE,GAAO6K,GAAqBzT;IACxC,IAAIqH,IAAS;MAAEuB,OAAOA;;IACtB,IAAI8K,KAAY;IAChB,IAAmC,mBAAxBD,GAAkC;MACzCC,KAAY;MACZrM,EAAOrH,OAAOyT;AACjB,WACI,IAAI9K,EAAQ9E,GAAG4P;MAChBpM,EAAOwB,UAAU4K;;MAGjBpM,EAAOgE,OAAOoI;;IAElB,IAAIC,UAAsBlO,MAATxF;MACbqH,EAAOrH,OAAOA;;IAElB,OAAOqH;AACX;EAYAmM,EAAW3P,KAVX,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,KAAaC,GAAG9B,OAAO6B,EAAUmE,gBACTpD,MAA1Bf,EAAU4O,eAA6B3O,GAAGgC,WAAWjC,EAAU4O,aAAalL,EAAWtE,cACpE2B,MAAnBf,EAAUzE,QAAsB0E,GAAG9B,OAAO6B,EAAUzE,gBACjCwF,MAAnBf,EAAU4G,aAA4C7F,MAAtBf,EAAUoE,kBACpBrD,MAAtBf,EAAUoE,WAAyBF,EAAQ9E,GAAGY,EAAUoE,mBAC9BrD,MAA1Bf,EAAUkP,eAA6BjP,GAAG+E,QAAQhF,EAAUkP,uBACzCnO,MAAnBf,EAAU4G,QAAsBT,EAAc/G,GAAGY,EAAU4G;AACpE;AAEH,CA/BD,CA+BGmI,OAAeA,KAAa,CAAE;;AAK1B,IAAII;;CACX,SAAWA;EAWPA,EAASvP,SAPT,SAASA,OAAOkB,GAAOsO;IACnB,IAAIxM,IAAS;MAAE9B,OAAOA;;IACtB,IAAIb,GAAG4C,QAAQuM;MACXxM,EAAOwM,OAAOA;;IAElB,OAAOxM;AACX;EASAuM,EAAS/P,KAJT,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcG,EAAMf,GAAGY,EAAUc,WAAWb,GAAGc,UAAUf,EAAUoE,YAAYF,EAAQ9E,GAAGY,EAAUoE;AAC1H;AAEH,CApBD,CAoBG+K,OAAaA,KAAW,CAAE;;AAKtB,IAAIE;;CACX,SAAWA;EAOPA,EAAkBzP,SAHlB,SAASA,OAAO0P,GAASC;IACrB,OAAO;MAAED,SAASA;MAASC,cAAcA;;AAC7C;EASAF,EAAkBjQ,KAJlB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcC,GAAGP,SAASM,EAAUsP,YAAYrP,GAAG+E,QAAQhF,EAAUuP;AAC3F;AAEH,CAhBD,CAgBGF,OAAsBA,KAAoB,CAAE;;AAKxC,IAAIG;;CACX,SAAWA;EAOPA,EAAa5P,SAHb,SAASA,OAAOkB,GAAO3H,GAAQiW;IAC3B,OAAO;MAAEtO,OAAOA;MAAO3H,QAAQA;MAAQiW,MAAMA;;AACjD;EASAI,EAAapQ,KAJb,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcG,EAAMf,GAAGY,EAAUc,WAAWb,GAAGc,UAAUf,EAAU7G,WAAW8G,GAAG9B,OAAO6B,EAAU7G;AACxH;AAEH,CAhBD,CAgBGqW,OAAiBA,KAAe,CAAE;;AAK9B,IAAIC;;CACX,SAAWA;EASPA,EAAe7P,SAHf,SAASA,OAAOkB,GAAO4O;IACnB,OAAO;MAAE5O,OAAOA;MAAO4O,QAAQA;;AACnC;EAMAD,EAAerQ,KAJf,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcG,EAAMf,GAAGY,EAAUc,gBAAgCC,MAArBf,EAAU0P,UAAwBD,EAAerQ,GAAGY,EAAU0P;AACtI;AAEH,CAfD,CAeGD,OAAmBA,KAAiB,CAAE;;AAQlC,IAAIE;;CACX,SAAWA;EACPA,EAA8B,YAAI;EAKlCA,EAAyB,OAAI;EAC7BA,EAA0B,QAAI;EAC9BA,EAAyB,OAAI;EAC7BA,EAA8B,YAAI;EAClCA,EAA2B,SAAI;EAC/BA,EAAkC,gBAAI;EACtCA,EAA8B,YAAI;EAClCA,EAA6B,WAAI;EACjCA,EAA6B,WAAI;EACjCA,EAA+B,aAAI;EACnCA,EAA0B,QAAI;EAC9BA,EAA6B,WAAI;EACjCA,EAA2B,SAAI;EAC/BA,EAA0B,QAAI;EAC9BA,EAA4B,UAAI;EAChCA,EAA6B,WAAI;EACjCA,EAA4B,UAAI;EAChCA,EAA2B,SAAI;EAC/BA,EAA2B,SAAI;EAC/BA,EAA2B,SAAI;EAC/BA,EAA6B,WAAI;EAIjCA,EAA8B,YAAI;AACrC,CA/BD,CA+BGA,OAAuBA,KAAqB,CAAE;;AAQ1C,IAAIC;;CACX,SAAWA;EACPA,EAAoC,cAAI;EACxCA,EAAmC,aAAI;EACvCA,EAAiC,WAAI;EACrCA,EAA+B,SAAI;EACnCA,EAAmC,aAAI;EACvCA,EAAiC,WAAI;EACrCA,EAA8B,QAAI;EAClCA,EAAqC,eAAI;EACzCA,EAAsC,gBAAI;EAC1CA,EAAuC,iBAAI;AAC9C,CAXD,CAWGA,OAA2BA,KAAyB,CAAE;;AAIlD,IAAIC;;CACX,SAAWA;EAMPA,EAAezQ,KALf,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,YAAsCe,MAAvBf,EAAU8P,YAAwD,mBAAvB9P,EAAU8P,aACxF9S,MAAMyI,QAAQzF,EAAUoP,UAAoC,MAA1BpP,EAAUoP,KAAK3S,UAA6C,mBAAtBuD,EAAUoP,KAAK;AAC/F;AAEH,CAPD,CAOGS,OAAmBA,KAAiB,CAAE;;AAMlC,IAAIE;;CACX,SAAWA;EAOPA,EAAgBnQ,SAHhB,SAASA,OAAOkB,GAAOzD;IACnB,OAAO;MAAEyD,OAAOA;MAAOzD,MAAMA;;AACjC;EAMA0S,EAAgB3Q,KAJhB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,aAAiDG,EAAMf,GAAGY,EAAUc,UAAUb,GAAG9B,OAAO6B,EAAU3C;AAC7G;AAEH,CAbD,CAaG0S,OAAoBA,KAAkB,CAAE;;AAMpC,IAAIC;;CACX,SAAWA;EAOPA,EAA0BpQ,SAH1B,SAASA,OAAOkB,GAAOmP,GAAcC;IACjC,OAAO;MAAEpP,OAAOA;MAAOmP,cAAcA;MAAcC,qBAAqBA;;AAC5E;EAOAF,EAA0B5Q,KAL1B,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,aAAiDG,EAAMf,GAAGY,EAAUc,UAAUb,GAAG+E,QAAQhF,EAAUkQ,yBAClGjQ,GAAG9B,OAAO6B,EAAUiQ,sBAA4ClP,MAA3Bf,EAAUiQ;AAC3D;AAEH,CAdD,CAcGD,OAA8BA,KAA4B,CAAE;;AAMxD,IAAIG;;CACX,SAAWA;EAOPA,EAAiCvQ,SAHjC,SAASA,OAAOkB,GAAOsP;IACnB,OAAO;MAAEtP,OAAOA;MAAOsP,YAAYA;;AACvC;EAOAD,EAAiC/Q,KALjC,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOW,aAAiDG,EAAMf,GAAGY,EAAUc,WACnEb,GAAG9B,OAAO6B,EAAUoQ,oBAAwCrP,MAAzBf,EAAUoQ;AACzD;AAEH,CAdD,CAcGD,OAAqCA,KAAmC,CAAE;;AAOtE,IAAIE;;CACX,SAAWA;EAOPA,EAAmBzQ,SAHnB,SAASA,OAAO0Q,GAASC;IACrB,OAAO;MAAED,SAASA;MAASC,iBAAiBA;;AAChD;EASAF,EAAmBjR,KAJnB,SAASA,GAAGC;IAER,OAAOY,GAAG4C,QADMxD,MACgBc,EAAMf,GAAGC,EAAMkR;AACnD;AAEH,CAhBD,CAgBGF,OAAuBA,KAAqB,CAAE;;AAM1C,IAAIG;;CACX,SAAWA;EAIPA,EAAcC,OAAO;EAIrBD,EAAcE,YAAY;EAI1BF,EAAcpR,KAHd,SAASA,GAAGC;IACR,OAAiB,MAAVA,KAAyB,MAAVA;AAC1B;AAEH,CAbD,CAaGmR,OAAkBA,KAAgB,CAAE;;AAChC,IAAIG;;CACX,SAAWA;EAIPA,EAAmB/Q,SAHnB,SAASA,OAAOP;IACZ,OAAO;MAAEA,OAAOA;;AACpB;EASAsR,EAAmBvR,KAPnB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,YACMe,MAAtBf,EAAU4Q,WAAyB3Q,GAAG9B,OAAO6B,EAAU4Q,YAAYjH,EAAcvK,GAAGY,EAAU4Q,mBACvE7P,MAAvBf,EAAU+C,YAA0BnC,EAASxB,GAAGY,EAAU+C,oBACpChC,MAAtBf,EAAUoE,WAAyBF,EAAQ9E,GAAGY,EAAUoE;AACpE;AAEH,CAbD,CAaGuM,OAAuBA,KAAqB,CAAE;;AAC1C,IAAIE;;CACX,SAAWA;EAQPA,EAAUjR,SAPV,SAASA,OAAO+E,GAAU7G,GAAOvC;IAC7B,IAAIqH,IAAS;MAAE+B,UAAUA;MAAU7G,OAAOA;;IAC1C,SAAaiD,MAATxF;MACAqH,EAAOrH,OAAOA;;IAElB,OAAOqH;AACX;EAYAiO,EAAUzR,KAVV,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcL,EAASP,GAAGY,EAAU2E,cACpD1E,GAAG9B,OAAO6B,EAAUlC,UAAUmC,GAAGgC,WAAWjC,EAAUlC,OAAO6S,GAAmBvR,cAC7D2B,MAAnBf,EAAUzE,QAAsBiV,GAAcpR,GAAGY,EAAUzE,eACnCwF,MAAxBf,EAAU8Q,aAA4B7Q,GAAGgC,WAAWjC,EAAU8Q,WAAW9O,EAAS5C,aAC5D2B,MAAtBf,EAAU4Q,WAAyB3Q,GAAG9B,OAAO6B,EAAU4Q,YAAYjH,EAAcvK,GAAGY,EAAU4Q,mBACpE7P,MAA1Bf,EAAU+Q,eAA6B9Q,GAAG+E,QAAQhF,EAAU+Q,uBACjChQ,MAA3Bf,EAAUgR,gBAA8B/Q,GAAG+E,QAAQhF,EAAUgR;AACzE;AAEH,CApBD,CAoBGH,OAAcA,KAAY,CAAE;;AACxB,IAAII;;CACX,SAAWA;EAKPA,EAAgB7R,KAJhB,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAGC,cAAcF,MAAcV,EAAIF,GAAGY,EAAUa,QAAQZ,GAAG9B,OAAO6B,EAAUnE;AACvF;AAEH,CAND,CAMGoV,OAAoBA,KAAkB,CAAE;;AAKpC,IAAIC;;CACX,SAAWA;EAWPA,EAAatR,SAHb,SAASA,OAAOiB,GAAK0I,GAAYV,GAASsI;IACtC,OAAO,IAAIC,GAAiBvQ,GAAK0I,GAAYV,GAASsI;AAC1D;EAUAD,EAAa9R,KALb,SAASA,GAAGC;IACR,IAAIW,IAAYX;IAChB,OAAOY,GAAG4C,QAAQ7C,MAAcC,GAAG9B,OAAO6B,EAAUa,SAASZ,GAAGc,UAAUf,EAAUuJ,eAAetJ,GAAG9B,OAAO6B,EAAUuJ,gBAAgBtJ,GAAGP,SAASM,EAAUqR,cACtJpR,GAAGqR,KAAKtR,EAAUuR,YAAYtR,GAAGqR,KAAKtR,EAAUwR,eAAevR,GAAGqR,KAAKtR,EAAUyR,aAAY,KAAO;AAC/G;EA0BAP,EAAaQ,aAxBb,SAASA,WAAWC,GAAUpM;IAC1B,IAAIlI,IAAOsU,EAASJ;IACpB,IAAIK,IAAcC,UAAUtM,IAAO,SAAUtH,GAAGC;MAC5C,IAAI4T,IAAO7T,EAAE6C,MAAMN,MAAMX,OAAO3B,EAAE4C,MAAMN,MAAMX;MAC9C,IAAa,MAATiS;QACA,OAAO7T,EAAE6C,MAAMN,MAAMV,YAAY5B,EAAE4C,MAAMN,MAAMV;;MAEnD,OAAOgS;AACX;IACA,IAAIC,IAAqB1U,EAAKZ;IAC9B,KAAK,IAAID,IAAIoV,EAAYnV,SAAS,GAAGD,KAAK,GAAGA,KAAK;MAC9C,IAAIwV,IAAIJ,EAAYpV;MACpB,IAAIyV,IAAcN,EAASF,SAASO,EAAElR,MAAMN;MAC5C,IAAI0R,IAAYP,EAASF,SAASO,EAAElR,MAAML;MAC1C,IAAIyR,KAAaH;QACb1U,IAAOA,EAAK8U,UAAU,GAAGF,KAAeD,EAAEvN,UAAUpH,EAAK8U,UAAUD,GAAW7U,EAAKZ;;QAGnF,MAAM,IAAIiE,MAAM;;MAEpBqR,IAAqBE;AACzB;IACA,OAAO5U;AACX;EAEA,SAASwU,UAAUzC,GAAMgD;IACrB,IAAIhD,EAAK3S,UAAU;MAEf,OAAO2S;;IAEX,IAAItU,IAAKsU,EAAK3S,SAAS,IAAK;IAC5B,IAAI4V,IAAOjD,EAAKkD,MAAM,GAAGxX;IACzB,IAAIyX,IAAQnD,EAAKkD,MAAMxX;IACvB+W,UAAUQ,GAAMD;IAChBP,UAAUU,GAAOH;IACjB,IAAII,IAAU;IACd,IAAIC,IAAW;IACf,IAAIjW,IAAI;IACR,OAAOgW,IAAUH,EAAK5V,UAAUgW,IAAWF,EAAM9V,QAAQ;MAErD,IADU2V,EAAQC,EAAKG,IAAUD,EAAME,OAC5B;QAEPrD,EAAK5S,OAAO6V,EAAKG;;QAIjBpD,EAAK5S,OAAO+V,EAAME;;AAE1B;IACA,OAAOD,IAAUH,EAAK5V;MAClB2S,EAAK5S,OAAO6V,EAAKG;;IAErB,OAAOC,IAAWF,EAAM9V;MACpB2S,EAAK5S,OAAO+V,EAAME;;IAEtB,OAAOrD;AACX;AACH,CA9ED,CA8EG8B,OAAiBA,KAAe,CAAE;;AAIrC,IAAIE,KAAkC;EAClC,SAASA,iBAAiBvQ,GAAK0I,GAAYV,GAASsI;IAChDzK,KAAKgM,OAAO7R;IACZ6F,KAAKiM,cAAcpJ;IACnB7C,KAAKkM,WAAW/J;IAChBnC,KAAKmM,WAAW1B;IAChBzK,KAAKoM,oBAAe/R;AACxB;EACAlE,OAAO6K,eAAe0J,iBAAiBzK,WAAW,OAAO;IACrDgB,KAAK;MACD,OAAOjB,KAAKgM;AACf;IACD9K,aAAY;IACZC,eAAc;;EAElBhL,OAAO6K,eAAe0J,iBAAiBzK,WAAW,cAAc;IAC5DgB,KAAK;MACD,OAAOjB,KAAKiM;AACf;IACD/K,aAAY;IACZC,eAAc;;EAElBhL,OAAO6K,eAAe0J,iBAAiBzK,WAAW,WAAW;IACzDgB,KAAK;MACD,OAAOjB,KAAKkM;AACf;IACDhL,aAAY;IACZC,eAAc;;EAElBuJ,iBAAiBzK,UAAU4K,UAAU,SAAUzQ;IAC3C,IAAIA,GAAO;MACP,IAAIN,IAAQkG,KAAK+K,SAAS3Q,EAAMN;MAChC,IAAIC,IAAMiG,KAAK+K,SAAS3Q,EAAML;MAC9B,OAAOiG,KAAKmM,SAASV,UAAU3R,GAAOC;AAC1C;IACA,OAAOiG,KAAKmM;;EAEhBzB,iBAAiBzK,UAAUoM,SAAS,SAAUC,GAAOnK;IACjDnC,KAAKmM,WAAWG,EAAM3V;IACtBqJ,KAAKkM,WAAW/J;IAChBnC,KAAKoM,oBAAe/R;;EAExBqQ,iBAAiBzK,UAAUsM,iBAAiB;IACxC,SAA0BlS,MAAtB2F,KAAKoM,cAA4B;MACjC,IAAII,IAAc;MAClB,IAAI7V,IAAOqJ,KAAKmM;MAChB,IAAIM,KAAc;MAClB,KAAK,IAAI3W,IAAI,GAAGA,IAAIa,EAAKZ,QAAQD,KAAK;QAClC,IAAI2W,GAAa;UACbD,EAAY5W,KAAKE;UACjB2W,KAAc;AAClB;QACA,IAAIC,IAAK/V,EAAKgW,OAAO7W;QACrB2W,IAAsB,SAAPC,KAAsB,SAAPA;QAC9B,IAAW,SAAPA,KAAe5W,IAAI,IAAIa,EAAKZ,UAAiC,SAAvBY,EAAKgW,OAAO7W,IAAI;UACtDA;;AAER;MACA,IAAI2W,KAAe9V,EAAKZ,SAAS;QAC7ByW,EAAY5W,KAAKe,EAAKZ;;MAE1BiK,KAAKoM,eAAeI;AACxB;IACA,OAAOxM,KAAKoM;;EAEhB1B,iBAAiBzK,UAAU6K,aAAa,SAAU8B;IAC9CA,IAAStU,KAAKuU,IAAIvU,KAAKC,IAAIqU,GAAQ5M,KAAKmM,SAASpW,SAAS;IAC1D,IAAIyW,IAAcxM,KAAKuM;IACvB,IAAIO,IAAM,GAAGC,IAAOP,EAAYzW;IAChC,IAAa,MAATgX;MACA,OAAO9T,EAASC,OAAO,GAAG0T;;IAE9B,OAAOE,IAAMC,GAAM;MACf,IAAIC,IAAM1U,KAAK2U,OAAOH,IAAMC,KAAQ;MACpC,IAAIP,EAAYQ,KAAOJ;QACnBG,IAAOC;;QAGPF,IAAME,IAAM;;AAEpB;IAGA,IAAI7T,IAAO2T,IAAM;IACjB,OAAO7T,EAASC,OAAOC,GAAMyT,IAASJ,EAAYrT;;EAEtDuR,iBAAiBzK,UAAU8K,WAAW,SAAU9M;IAC5C,IAAIuO,IAAcxM,KAAKuM;IACvB,IAAItO,EAAS9E,QAAQqT,EAAYzW;MAC7B,OAAOiK,KAAKmM,SAASpW;WAEpB,IAAIkI,EAAS9E,OAAO;MACrB,OAAO;;IAEX,IAAI+T,IAAaV,EAAYvO,EAAS9E;IAEtC,OAAOb,KAAKuU,IAAIvU,KAAKC,IAAI2U,IAAajP,EAAS7E,WADzB6E,EAAS9E,OAAO,IAAIqT,EAAYzW,SAAUyW,EAAYvO,EAAS9E,OAAO,KAAK6G,KAAKmM,SAASpW,SACpCmX;;EAE/E/W,OAAO6K,eAAe0J,iBAAiBzK,WAAW,aAAa;IAC3DgB,KAAK;MACD,OAAOjB,KAAKuM,iBAAiBxW;AAChC;IACDmL,aAAY;IACZC,eAAc;;EAElB,OAAOuJ;AACX,CA1GsC;;AA2GtC,IAAInR;;CACJ,SAAWA;EACP,IAAI+H,IAAWnL,OAAO8J,UAAUqB;EAIhC/H,EAAG4C,UAHH,SAASA,QAAQxD;IACb,YAAwB,MAAVA;AAClB;EAKAY,EAAGc,YAHH,SAASA,YAAU1B;IACf,YAAwB,MAAVA;AAClB;EAKAY,EAAG+E,UAHH,SAASA,QAAQ3F;IACb,QAAiB,MAAVA,MAA4B,MAAVA;AAC7B;EAKAY,EAAG9B,SAHH,SAASA,OAAOkB;IACZ,OAAgC,sBAAzB2I,EAAS6L,KAAKxU;AACzB;EAKAY,EAAG+D,SAHH,SAASA,OAAO3E;IACZ,OAAgC,sBAAzB2I,EAAS6L,KAAKxU;AACzB;EAKAY,EAAGyB,cAHH,SAASA,YAAYrC,GAAOJ,GAAKsU;IAC7B,OAAgC,sBAAzBvL,EAAS6L,KAAKxU,MAAgCJ,KAAOI,KAASA,KAASkU;AAClF;EAKAtT,EAAGV,UAHH,SAASA,QAAQF;IACb,OAAgC,sBAAzB2I,EAAS6L,KAAKxU,OAAiC,cAAcA,KAASA,KAAS;AAC1F;EAKAY,EAAGP,WAHH,SAASA,SAASL;IACd,OAAgC,sBAAzB2I,EAAS6L,KAAKxU,MAAgC,KAAKA,KAASA,KAAS;AAChF;EAKAY,EAAGqR,OAHH,SAASA,KAAKjS;IACV,OAAgC,wBAAzB2I,EAAS6L,KAAKxU;AACzB;EAQAY,EAAGC,gBANH,SAASA,cAAcb;IAInB,OAAiB,SAAVA,KAAmC,mBAAVA;AACpC;EAKAY,EAAGgC,aAHH,SAASA,WAAW5C,GAAOyU;IACvB,OAAO9W,MAAMyI,QAAQpG,MAAUA,EAAMiH,MAAMwN;AAC/C;AAEH,CAjDD,CAiDG7T,OAAOA,KAAK,CAAA;;ACr5DT,IAAW2J;;CAAjB,SAAiBA;EACFA,EAAAC,OAAO;EACPD,EAAAE,SAAS;EACTF,EAAAG,WAAW;EACXH,EAAAI,cAAc;EACdJ,EAAAK,QAAQ;EACRL,EAAAM,WAAW;EACXN,EAAAO,QAAQ;EACRP,EAAAQ,YAAY;EACZR,EAAAS,SAAS;EACTT,EAAAU,WAAW;EACXV,EAAAW,OAAO;EACPX,EAAAY,QAAQ;EACRZ,EAAAa,OAAO;EACPb,EAAAc,UAAU;EACVd,EAAAe,UAAU;EACVf,EAAAvI,QAAQ;EACRuI,EAAAgB,OAAO;EACPhB,EAAAiB,YAAY;EACZjB,EAAAkB,SAAS;EACTlB,EAAAmB,aAAa;EACbnB,EAAAoB,WAAW;EACXpB,EAAAqB,SAAS;EACTrB,EAAAsB,QAAQ;EACRtB,EAAAuB,WAAW;EACXvB,EAAAwB,gBAAgB;AAC9B,CA1BD,CAAiBxB,OAAAA,KAAkB,CAAA;;ACnJ5B,IAAMmK,KAASlX,OAAAmX,OAAAnX,OAAAmX,OAAA,IACjBC,EAAAA,OA/DoD;EACvDC,eAAe;EACfC,WAAW;EACXC,aAAa;EACbC,OAAO;EACPC,UAAU;EACVC,cAAc;EACdC,gBAAgB;EAChBC,SAAS;EACTC,SAAS;EACTC,YAAY;EACZC,YAAY;EACZC,iBAAiB;EACjBC,cAAc;EACdC,YAAY;EACZC,eAAe;EACfC,WAAW;EACXC,UAAU;EACVC,YAAY;EACZC,WAAW;EACXC,WAAW;EACXC,iBAAiB;EACjBC,eAAe;EACfC,YAAY;EACZC,sBAAsB;EACtBC,eAAe;EACfC,YAAY;EACZC,sBAAsB;EACtBC,MAAM;;;ACPD,IAAMC,KAAoB;EAC/B1R,SAAS;EACTD,OAAO;;;AAGT,IAAM4R,sBAAuBC;EAC3B,IAAMC,IAA8C;EACpD,IAAID;IACF;MACEE,QAAMC,EAAAA,MAAMH,IAAK;QACfI,kBAAAA,CAAmBC;UACjBJ,EAAkB3Z,KAAK+Z;AACzB;;AAEH,MAAC,OAAAtS;MACA,OAAO;;;EAGX,OAAOkS;AAAiB;;AAG1B,IAAMK,KAA0B,EAE9BrC,EAAAA,KAAKsC,mBACLtC,EAAAA,KAAKuC,2BACLvC,EAAAA,KAAKwC,wBACLxC,EAAIA,KAACyC,wBACLzC,EAAIA,KAAC0C,2BACL1C,EAAAA,KAAK2C,uBACL3C,EAAAA,KAAK4C,sBACL5C,EAAIA,KAAC6C,8BACL7C,EAAIA,KAAC8C,sBAEL9C,OAAK+C,kBACL/C,EAAAA,KAAKgD,uBACLhD,EAAAA,KAAKiD,uBACLjD,EAAIA,KAACkD,0BACLlD,EAAIA,KAACmD,sBACLnD,EAAAA,KAAKoD,qBACLpD,EAAAA,KAAKqD;;AAGP,IAAMC,2BAA4BC;EAChC,IAAIC,KAAmB;EACvB,IAAID;IACF;MACEtB,QAAMC,EAAAA,MAAMqB,IAAM;QAChBE,KAAAA,CAAMC;UACJ,IAAkB,eAAdA,EAAKpc;YACP;;UAEF,IAAI+a,GAAgBsB,SAASD,EAAKpc,OAAO;YACvCkc,KAAmB;YACnB,OAAOI;;UAET,QAAO;AACT;;AAEH,MAAC,OAAA9T;MACA,OAAO0T;;;EAGX,OAAOA;AAAgB;;AAcnB,SAAUK,2BACdrc,GACAsc,GACAC,GACAC,GACAC,GACAvS;;EAEA,IAAMwS,IAAItb,OAAAmX,OAAAnX,OAAAmX,OAAA,CAAA,GACLrO,IAAO;IACVlK;;EAEF,IAAMyB,IACJ+a,KAAgBG,mBAAmBL,GAAWC,GAAQ;EAExD,IAAM1c,IACiB,cAArB4B,EAAM5B,MAAMC,OAAqB2B,EAAM5B,MAAMiB,YAAYW,EAAM5B;EAEjE,IAAM+c,KAAO1S,iBAAO,IAAPA,EAAS0S,SA+lCxB,SAASC,gBACPC,GACA1X;IAEA,IAAIA,iBAAAA,IAAAA,EAAK2X,SAAS;MAChB,OAAOC,GAAoBC;;IAE7B,OAAOnB,yBAAyBgB,KAC5BE,GAAoBC,cACpBD,GAAoBE;AAC1B,GAzmCgCL,CAAgBP,GAAWpS,iBAAAA,IAAAA,EAAS9E;EAGlE,KAAKvF;IACH,OAAO;;EAGT,KAAMC,MAAEA,GAAIqd,MAAEA,GAAIrc,WAAEA,KAAcjB;EAClC,IAAMud,IAAWC,YAAYrd,GAAQyB,EAAM5B;EAG3C,IAAIC,MAASwY,GAAUgF,UAAU;IAC/B,IAAIV,MAASI,GAAoBC;MAC/B,OAsSN,SAASM,uCAAuC9b;QAC9C,OAAOD,WAASC,GAAO,EACrB;UAAEY,OAAO;UAAUvC,MAAMqO,GAAmBG;WAC5C;UAAEjM,OAAO;UAAQvC,MAAMqO,GAAmBG;WAC1C;UAAEjM,OAAO;UAAavC,MAAMqO,GAAmBG;WAC/C;UAAEjM,OAAO;UAASvC,MAAMqO,GAAmBG;WAC3C;UAAEjM,OAAO;UAASvC,MAAMqO,GAAmBG;WAC3C;UAAEjM,OAAO;UAAUvC,MAAMqO,GAAmBG;WAC5C;UAAEjM,OAAO;UAAUvC,MAAMqO,GAAmBG;;AAEhD,OAhTaiP,CAAuC9b;;IAEhD,OAgTJ,SAAS+b,uCAAuC/b;MAC9C,OAAOD,WAASC,GAAO,EACrB;QAAEY,OAAO;QAASvC,MAAMqO,GAAmBG;SAC3C;QAAEjM,OAAO;QAAYvC,MAAMqO,GAAmBG;SAC9C;QAAEjM,OAAO;QAAgBvC,MAAMqO,GAAmBG;SAClD;QAAEjM,OAAO;QAAYvC,MAAMqO,GAAmBG;SAC9C;QAAEjM,OAAO;QAAKvC,MAAMqO,GAAmBI;;AAE3C,KAxTWiP,CAAuC/b;;EAGhD,IAAI3B,MAASwY,GAAUyB;IACrB,OAsTJ,SAAS0D,sCAAsChc;MAC7C,OAAOD,WAASC,GAAO,EACrB;QAAEY,OAAO;QAAQvC,MAAMqO,GAAmBG;SAC1C;QAAEjM,OAAO;QAAavC,MAAMqO,GAAmBG;SAC/C;QAAEjM,OAAO;QAASvC,MAAMqO,GAAmBG;SAC3C;QAAEjM,OAAO;QAASvC,MAAMqO,GAAmBG;SAC3C;QAAEjM,OAAO;QAAUvC,MAAMqO,GAAmBG;SAC5C;QAAEjM,OAAO;QAAUvC,MAAMqO,GAAmBG;;AAEhD,KA/TWmP,CAAsChc;;EAG/C,KACsB6G,UAApBA,IAAAxH,iBAAAA,IAAAA,EAAWA,yBAASwH,aAAAA,EAAExI,UAASwY,GAAU0B,wBACzCna,EAAMO;IAEN,OAAOoB,WAASC,GAAO;;EAIzB,KAAIX,iBAAAA,IAAAA,EAAWhB,UAAS0Y,EAAAA,KAAKgD;IAC3B,OAAOha,WACLC,GACAL,OAAOE,OAAOtB,EAAO0d,cAClB7a,OAAO8a,EAAAA,cACP1b,KAAIhC,MAAS;MACZoC,OAAOpC,EAAKG;MACZN,MAAMqO,GAAmBG;;;EAMjC,KAAIxN,iBAAAA,IAAAA,EAAWhB,UAAS0Y,EAAAA,KAAKiD;IAC3B,OAAOja,WACLC,GACAL,OAAOE,OAAOtB,EAAO0d,cAClB7a,QAAO5C,KAAQ2d,EAAYA,aAAC3d,OAAUA,EAAKG,KAAKyd,WAAW,QAC3D5b,KAAIhC,MAAS;MACZoC,OAAOpC,EAAKG;MACZN,MAAMqO,GAAmBG;;;EAMjC,KAAIxN,iBAAAA,IAAAA,EAAWhB,UAAS0Y,EAAAA,KAAKkD;IAC3B,OAAOla,WACLC,GACAL,OAAOE,OAAOtB,EAAO0d,cAClB7a,OAAOib,EAAAA,iBACP7b,KAAIhC,MAAS;MACZoC,OAAOpC,EAAKG;MACZN,MAAMqO,GAAmBG;;;EAMjC,KAAIxN,iBAAAA,IAAAA,EAAWhB,UAAS0Y,EAAAA,KAAKmD;IAC3B,OAAOna,WACLC,GACAL,OAAOE,OAAOtB,EAAO0d,cAClB7a,OAAOkb,EAAAA,aACP9b,KAAIhC,MAAS;MACZoC,OAAOpC,EAAKG;MACZN,MAAMqO,GAAmBG;;;EAMjC,KAAIxN,iBAAAA,IAAAA,EAAWhB,UAAS0Y,EAAAA,KAAKoD;IAC3B,OAAOpa,WACLC,GACAL,OAAOE,OAAOtB,EAAO0d,cAClB7a,QAAO5C,KAAQ+d,EAAUA,WAAC/d,OAAUA,EAAKG,KAAKyd,WAAW,QACzD5b,KAAIhC,MAAS;MACZoC,OAAOpC,EAAKG;MACZN,MAAMqO,GAAmBG;;;EAMjC,KAAIxN,iBAAAA,IAAAA,EAAWhB,UAAS0Y,EAAAA,KAAKqD;IAC3B,OAAOra,WACLC,GACAL,OAAOE,OAAOtB,EAAO0d,cAClB7a,OAAOob,EAAAA,mBACPhc,KAAIhC,MAAS;MACZoC,OAAOpC,EAAKG;MACZN,MAAMqO,GAAmBG;;;EAKjC,IACExO,MAASwY,GAAU4B,cAClBpa,MAASwY,GAAU4F,eAAcpd,iBAAS,IAATA,EAAWhB,UAASwY,GAAU4B;IAEhE,OAoVJ,SAASiE,4BACP1c,GACA/B,GACAM,GACA8c,GACAM;MAGA,IAAI1d,EAAW0e;QACb,OAAO;;MAET,IAAMC,IAAUre,EAAO0d;MAEvB,IAAMY,IAAmBrd,aAAaod,GAASxb,OAAOib,EAAeA;MACrE,IAAMS,IAAuBD,EAAiBrc,KAAI,EAAG7B,aAAWA;MAChE,IAAMoe,IAAgC,IAAIC;MAC1CC,kBAAgB5B,IAAc,CAAC6B,GAAG9e;;QAChC,IAAIA,EAAMO,MAAM;UAEd,IACEP,EAAMC,SAASwY,GAAUiB,kBACxBgF,EAAqBpC,SAAStc,EAAMO;YAErCoe,EAAiBhT,IAAI3L,EAAMO;;UAI7B,IACEP,EAAMC,SAASwY,GAAU4F,eACV,UAAf5V,IAAAzI,EAAMiB,mBAAS,MAAAwH,SAAA,IAAAA,EAAExI,UAASwY,GAAU4B;YAEpC,IAAIkD,EAASwB,cAAc;cAIzB,IAH0CC,UAArBA,IAAAzB,EAASwB,4BAAYC,aAAAA,EACtCC,gBACDC,MAAK,EAAG3e,aAAWA,MAASP,EAAMO;gBAEnC;;cAEF,IAAMH,IAAOD,EAAOgf,QAAQnf,EAAMO;cAClC,IAAM6e,IAAuC,UAArBC,IAAA9B,EAASwB,sBAAY,MAAAM,SAAA,IAAAA,EAAEC;cAC/C/B,EAASwB,eAAe,IAAIQ,EAAAA,qBAAoBhe,OAAAmX,OAAAnX,OAAAmX,WAC3C0G,IAAe;gBAClBI,YAAY,KACPJ,EAAgBI,YAClBpf,KACC,IAAImf,EAAAA,qBAAqB;kBAAEhf,MAAMP,EAAMO;kBAAMkf,QAAQ,CAAA;;;AAG5D,mBAAM,IAAIlC,EAASmC,eAAe;cAIjC,IAH2CC,UAAtBA,IAAApC,EAASmC,6BAAaC,aAAAA,EACvCV,gBACDC,MAAK,EAAG3e,aAAWA,MAASP,EAAMO;gBAEnC;;cAEF,IAAMH,IAAOD,EAAOgf,QAAQnf,EAAMO;cAClC,IAAMqf,IAAyC,UAAtBC,IAAAtC,EAASmC,uBAAa,MAAAG,SAAA,IAAAA,EAAEP;cACjD/B,EAASmC,gBAAgB,IAAII,EAAAA,kBAAiBve,OAAAmX,OAAAnX,OAAAmX,WACzCkH,IAAgB;gBACnBJ,YAAY,KACPI,EAAiBJ,YACnBpf,KACC,IAAImf,EAAAA,qBAAqB;kBAAEhf,MAAMP,EAAMO;kBAAMkf,QAAQ,CAAA;;;;;;;MAQnE,IAAMM,IAAsBxC,EAASwB,gBAAgBxB,EAASmC;MAG9D,IAAMM,MADoBD,iBAAAA,IAAAA,EAAqBd,oBAAmB,IAClB7c,KAAI,EAAG7B,aAAWA;MAGlE,IAAM0f,IAAqBxB,EACxBpZ,OACC,KAAIsZ,IAAkBvc,KAAI7B,MAAS;QAAEA;YAEtCyC,QACC,EAAGzC,aACDA,OAASwf,qBAAAA,EAAqBxf,UAC7Byf,EAAsB1D,SAAS/b;MAGtC,OAAOoB,WACLC,GACAqe,EAAmB7d,KAAIhC;QACrB,IAAMkH,IAAS;UACb9E,OAAOpC,EAAKG;UACZN,MAAMqO,GAAmBQ;UACzB1O;;QAEF,IAAIA,qBAAAA,EAAMqJ;UACRnC,EAAO2J,gBAAgB7Q,EAAKqJ;;QAW9B,OAAOnC;AAAM;AAGnB,KAjcWgX,CACL1c,GACA5B,GACAG,GACAsc,GACAc;;EAKJ,IACEtd,MAASwY,GAAUyH,iBACnBjgB,MAASwY,GAAU0H,SACnBlgB,MAASwY,GAAUG;IAEnB,OAsNJ,SAASwH,4BACPxe,GACA2b,GACAlT;;MAEA,IAAIkT,EAAS8C,YAAY;QACvB,KAAMA,YAAEA,KAAe9C;QACvB,IAAIkC,IAAqC;QACzC,IAAI,eAAeY;UACjBZ,IAASre,aAEPif,EAAWzf;;QAIf,IAAID,EAAAA,gBAAgB0f;UAClBZ,EAAOze,KAAKN,EAAAA;;QAEd,IAAI2f,iBAAe5X,IAAA4B,iBAAO,IAAPA,EAASlK,gBAAM,MAAAsI,SAAA,IAAAA,EAAEjI;UAClCif,EAAOze,KAAKV,sBAAoBG,EAAAA;;QAElC,OAAOkB,WACLC,GACA6d,EAAOrd,KAAoB,CAACke,GAAOC;;UACjC,IAAMpd,IAA6B;YAEjCqd,UAAU7O,OAAO4O,KAASD,EAAM/f;YAChCiC,OAAO8d,EAAM/f;YACb+P,QAAQqB,OAAO2O,EAAMlgB;YACrB6Q,eAAgC,UAAjBxI,IAAA6X,EAAM7W,qBAAW,MAAAhB,IAAAA,SAAIhD;YACpC6M,YAAYV,QAAQ0O,EAAMG;YAC1Bve,cAAc0P,QAAQ0O,EAAMG;YAC5BA,mBAAmBH,EAAMG;YACzBxgB,MAAMqO,GAAmBK;YACzBvO,MAAMkgB,EAAMlgB;;UAGd,IAAIiK,qBAAAA,EAASqW,qBAAqB;YAEhC,IAAMC,IAAaC,cAAcN;YACjC,IAAIK,GAAY;cACdxd,EAAWwd,aAAaL,EAAM/f,OAAOogB;cACrCxd,EAAW0d,mBAAmB9Q,EAAiBV;cAC/ClM,EAAW2F,UAAU0R;;;UAIzB,OAAOrX;AAAU;;MAIvB,OAAO;AACT,KA1QWid,CAA4Bxe,GAAO2b,GAAUV;;EAItD,IACE5c,MAASwY,GAAUI,aAClB5Y,MAASwY,GAAUqI,YAAqB,MAATxD,GAChC;IACA,KAAMyD,SAAEA,KAAYxD;IACpB,IAAIwD;MACF,OAAOpf,WACLC,GACAmf,EAAQ3e,KACL4e;;QAA4C,OAAC;UAC5Cxe,OAAOwe,EAAOzgB;UACdogB,YAAYK,EAAOzgB,OAAO;UAC1BuI,SAAS0R;UACTlK,QAAQqB,OAAOqP,EAAO5gB;UACtB6Q,eAAiC,UAAlBxI,IAAAuY,EAAOvX,qBAAW,MAAAhB,IAAAA,SAAIhD;UACrCxF,MAAMqO,GAAmBM;UACzBxO,MAAM4gB,EAAO5gB;;AACd;;;EAOT,KACGH,MAASwY,GAAUe,gBACjBvZ,MAASwY,GAAUwI,gBAAyB,MAAT3D,MACtCC,EAAS2D,iBACT;IACA,IAAMC,IAAe/f,aAAamc,EAAS2D;IAC3C,IAAME,IACJnhB,MAASwY,GAAUe,eACflL,GAAmBY,QACnBZ,GAAmBK;IACzB,OAAOhN,WACLC,GACAuf,EAAa/e,KAAIke;;MAAS,OAAC;QACzB9d,OAAO8d,EAAM/f;QACb+P,QAAQqB,OAAO2O,EAAMlgB;QACrB6Q,eAAgC,UAAjBxI,IAAA6X,EAAM7W,qBAAW,MAAAhB,IAAAA,SAAIhD;QACpCxF,MAAMmhB;QACNhhB,MAAMkgB,EAAMlgB;;AACb;;EAKL,IACEH,MAASwY,GAAUoB,cAClB5Z,MAASwY,GAAUgB,cAAuB,MAAT6D,KACjCrd,MAASwY,GAAUwI,gBAAyB,MAAT3D,KACnCrd,MAASwY,GAAUqI,YAAqB,MAATxD;IAEhC,OAmNJ,SAAS+D,6BACPzf,GACA2b,GACAd,GACAtc;MAEA,IAAMmhB,IAAiBC,EAAAA,aAAahE,EAASiE;MAE7C,IAAMC,IAAmCC,uBACvCjF,GACAtc,GACAyB,GACAoB,QAAO2e,KAAKA,EAAErR,WAAWgR,EAAe/gB;MAE1C,IAAI+gB,aAA0BM,EAAAA,iBAAiB;QAE7C,OAAOjgB,WACLC,GAFa0f,EAAeO,YAIzBzf,KAAqB2B;;UAA4B,OAAC;YACjDvB,OAAOuB,EAAMxD;YACb+P,QAAQqB,OAAO2P;YACfrQ,eAAgC,UAAjBxI,IAAA1E,EAAM0F,qBAAW,MAAAhB,IAAAA,SAAIhD;YACpC6M,YAAYV,QAAQ7N,EAAM0c;YAC1Bve,cAAc0P,QAAQ7N,EAAM0c;YAC5BA,mBAAmB1c,EAAM0c;YACzBxgB,MAAMqO,GAAmBmB;YACzBrP,MAAMkhB;;AACP,YACAjc,OAAOoc;;MAGd,IAAIH,MAAmBQ,EAAAA;QACrB,OAAOngB,WACLC,GACA6f,EAAepc,OAAO,EACpB;UACE7C,OAAO;UACP8N,QAAQqB,OAAOmQ;UACf7Q,eAAe;UACfhR,MAAMqO,GAAmBM;UACzBxO,MAAM0hB,EAAAA;WAER;UACEtf,OAAO;UACP8N,QAAQqB,OAAOmQ;UACf7Q,eAAe;UACfhR,MAAMqO,GAAmBM;UACzBxO,MAAM0hB,EAAAA;;;MAMd,OAAOL;AACT,KA1QWJ,CAA6Bzf,GAAO2b,GAAUd,GAAWtc;;EAGlE,IAAIF,MAASwY,GAAUsJ,YAAqB,MAATzE,GAAY;IAC7C,IAAMgE,IAAiBC,EAAAA,aAAahE,EAASiE;IAM7C,OAAO7f,WACLC,GAN0B8f,uBAC1BjF,GACAtc,GACAyB,GAIoBoB,QAAO2e,KAAKA,EAAErR,YAAWgR,qBAAAA,EAAgB/gB;;EAKjE,IACGN,MAASwY,GAAUS,kBAA2B,MAAToE,KACrCrd,MAASwY,GAAU4F,cACL,QAAbpd,KACAA,EAAUhB,SAASwY,GAAUS;IAE/B,OAoWJ,SAAS8I,wCACPpgB,GACA2b,GACApd,GACA8hB;MAEA,IAAIC;MACJ,IAAI3E,EAAS8C;QACX,IAAI8B,EAAcA,eAAC5E,EAAS8C,aAAa;UACvC,IAAM+B,IAAeC,EAAAA,mBAAmB9E,EAAS8C;UAGjD,IAAMiC,IAAmBniB,EAAOoiB,iBAAiBH;UACjD,IAAMI,IAAmBjhB,OAAO+C,OAAO;UACvC,KAAK,IAAMlE,KAAQkiB;YACjB,KAAK,IAAMG,KAASriB,EAAK6e;cACvBuD,EAAiBC,EAAMliB,QAAQkiB;;;UAGnCP,IAAgBI,EAAiBjd,OAAOjE,aAAaohB;;UAIrDN,IAAgB,EAAC3E,EAAS8C;;aAEvB;QAEL6B,IAAgB9gB,aADAjB,EAAO0d,cACe7a,QACpC5C,KAAQO,EAAeA,gBAACP,OAAUA,EAAKG,KAAKyd,WAAW;;MAG3D,OAAOrc,WACLC,GACAsgB,EAAc9f,KAAIhC;QAChB,IAAMsiB,IAAYnB,eAAanhB;QAC/B,OAAO;UACLoC,OAAOmP,OAAOvR;UACd6Q,gBAAeyR,qBAAAA,EAAWjZ,gBAAe;UACzCxJ,MAAMqO,GAAmBK;;AAC1B;AAGP,KA9YWqT,CACLpgB,GACA2b,GACApd;;EAMJ,IAAIF,MAASwY,GAAUkK,mBAA4B,MAATrF;IACxC,OAsYJ,SAASsF,kCACPhhB,GACA2b,GACApd,GACAsc,GACAG;MAEA,KAAKH;QACH,OAAO;;MAET,IAAM+B,IAAUre,EAAO0d;MACvB,IAAMgF,IAAWjjB,mBAAmBgC,EAAM5B;MAC1C,IAAM8iB,IAgGF,SAAUC,uBACdtG;QAEA,IAAMG,IAAyC;QAC/CiC,kBAAgBpC,IAAW,CAACqC,GAAG9e;UAC7B,IACEA,EAAMC,SAASwY,GAAUuK,uBACzBhjB,EAAMO,QACNP,EAAMI;YAENwc,EAAa5b,KAAK;cAChBf,MAAMwY,GAAUuK;cAChBziB,MAAM;gBACJN,MAAM0Y,EAAIA,KAACsK;gBACXlf,OAAO/D,EAAMO;;cAGf2iB,cAAc;gBACZjjB,MAAMwY,GAAUyH;gBAChBiD,YAAY;;cAGdC,eAAe;gBACbnjB,MAAMwY,GAAU4F;gBAChB9d,MAAM;kBACJN,MAAM0Y,EAAIA,KAACsK;kBACXlf,OAAO/D,EAAMI;;;;;;QAOvB,OAAOwc;AACT,OAlIoBmG,CAAuBtG;MAEzC,IAAIG,KAAgBA,EAAazb,SAAS;QACxC2hB,EAAU9hB,QAAQ4b;;MAIpB,IAAMyG,IAAgBP,EAAU9f,QAC9BsgB,KAEE9E,EAAQ8E,EAAKF,cAAc7iB,KAAKwD,YAG9B8e,KACAA,EAAS5iB,SAASwY,GAAUuK,uBAC5BH,EAAStiB,SAAS+iB,EAAK/iB,KAAKwD,UAG9BpD,EAAAA,gBAAgB4c,EAAS8C,eACzB1f,kBAAgB6d,EAAQ8E,EAAKF,cAAc7iB,KAAKwD,WAChDwf,EAAcA,eACZpjB,GACAod,EAAS8C,YACT7B,EAAQ8E,EAAKF,cAAc7iB,KAAKwD;MAItC,OAAOpC,WACLC,GACAyhB,EAAcjhB,KAAIkhB,MAAS;QACzB9gB,OAAO8gB,EAAK/iB,KAAKwD;QACjBuM,QAAQqB,OAAO6M,EAAQ8E,EAAKF,cAAc7iB,KAAKwD;QAC/CkN,eAAe,YAAYqS,EAAK/iB,KAAKwD,YAAYuf,EAAKF,cAAc7iB,KAAKwD;QACzE9D,MAAMqO,GAAmBK;QACzBvO,MAAMoe,EAAQ8E,EAAKF,cAAc7iB,KAAKwD;;AAG5C,KAvbW6e,CACLhhB,GACA2b,GACApd,GACAsc,GACA/a,MAAMyI,QAAQyS,KACVA,IACAnC,oBAAoBmC;;EAI5B,IAAM4G,IAAiBC,WAAWzjB;EAElC,IACG+c,MAASI,GAAoBC,gBAC3BoG,EAAeE,gBAChBzjB,MAASwY,GAAU4F,cACrBpe,MAASwY,GAAUkL,WACnB;IACA,IAAIH,EAAevjB,SAASwY,GAAUqB;MACpC,OAAOnY,WACLC,GACAL,OAAOE,OAAOtB,EAAO0d,cAClB7a,QAAO5C,KAAQwjB,EAAYA,aAACxjB,OAAUA,EAAKG,KAAKyd,WAAW,QAC3D5b,KAAIhC,MAAS;QACZoC,OAAOpC,EAAKG;QACZN,MAAMqO,GAAmBG;;;IAIjC,IAAI+U,EAAevjB,SAASwY,GAAUuB;MACpC,OAAOrY,WACLC,GACAL,OAAOE,OAAOtB,EAAO0d,cAClB7a,QAAO5C,KAAQyjB,EAAWA,YAACzjB,OAAUA,EAAKG,KAAKyd,WAAW,QAC1D5b,KAAIhC,MAAS;QACZoC,OAAOpC,EAAKG;QACZN,MAAMqO,GAAmBG;;;;EAOnC,IACGxO,MAASwY,GAAUqL,uBAAgC,MAATxG,KAC1Crd,MAASwY,GAAUkL,aAAsB,MAATrG,KAChCrd,MAASwY,GAAU4F,cAClBpd,MACCA,EAAUhB,SAASwY,GAAUqL,uBAC5B7iB,EAAUhB,SAASwY,GAAUkL,aAC7B1iB,EAAUhB,SAASwY,GAAUsL;IAEjC,OAieJ,SAASC,oCACPpiB,GACAzB,GACA8hB;MAEA,IAAMgC,IAAe9jB,EAAO0d;MAC5B,IAAMqG,IAAa9iB,aAAa6iB,GAAcjhB,OAAO6gB,EAAWA;MAChE,OAAOliB,WACLC,GAEAsiB,EAAW9hB,KAAKhC,MAA4B;QAC1CoC,OAAOpC,EAAKG;QACZ0Q,eAAe7Q,EAAKqJ;QACpBxJ,MAAMqO,GAAmBM;;AAG/B,KAjfWoV,CAAoCpiB,GAAOzB;;EAIpD,IAAIF,MAASwY,GAAU0L;IACrB,OA8eJ,SAASC,2BACPxiB,GACA5B,GACAG,GACA8hB;;MAEA,IAAmBxZ,UAAfA,IAAAzI,EAAMiB,mBAASwH,MAAAA,SAAAA,IAAAA,EAAExI,MAAM;QACzB,IAAMokB,IAAalkB,EAChBmkB,gBACAthB,QAAOuhB,KAkGR,SAAUC,gBACdxkB,GACAukB;UAEA,MAAKvkB,qBAAAA,EAAOC;YACV,QAAO;;UAET,KAAMA,MAAEA,GAAIgB,WAAEA,KAAcjB;UAC5B,KAAMykB,WAAEA,KAAcF;UACtB,QAAQtkB;WACN,KAAKwY,GAAUM;YACb,OAAO0L,EAAUnI,SAASoI,EAAiBA,kBAAC3L;;WAC9C,KAAKN,GAAUO;YACb,OAAOyL,EAAUnI,SAASoI,EAAiBA,kBAAC1L;;WAC9C,KAAKP,GAAUQ;YACb,OAAOwL,EAAUnI,SAASoI,EAAiBA,kBAACzL;;WAC9C,KAAKR,GAAU0H;WACf,KAAK1H,GAAUG;YACb,OAAO6L,EAAUnI,SAASoI,EAAiBA,kBAACvE;;WAC9C,KAAK1H,GAAUuK;YACb,OAAOyB,EAAUnI,SAASoI,EAAiBA,kBAAC1B;;WAC9C,KAAKvK,GAAUkK;YACb,OAAO8B,EAAUnI,SAASoI,EAAiBA,kBAAC/B;;WAC9C,KAAKlK,GAAUkM;YACb,OAAOF,EAAUnI,SAASoI,EAAiBA,kBAACC;;WAG9C,KAAKlM,GAAUY;YACb,OAAOoL,EAAUnI,SAASoI,EAAiBA,kBAACE;;WAC9C,KAAKnM,GAAUa;YACb,OAAOmL,EAAUnI,SAASoI,EAAiBA,kBAACG;;WAC9C,KAAKpM,GAAUc;YACb,OAAOkL,EAAUnI,SAASoI,EAAiBA,kBAACI;;WAC9C,KAAKrM,GAAUqB;YACb,OAAO2K,EAAUnI,SAASoI,EAAiBA,kBAACK;;WAC9C,KAAKtM,GAAUiB;YACb,OAAO+K,EAAUnI,SAASoI,EAAiBA,kBAACM;;WAC9C,KAAKvM,GAAUkB;YACb,OAAO8K,EAAUnI,SAASoI,EAAiBA,kBAACO;;WAC9C,KAAKxM,GAAUmB;YACb,OAAO6K,EAAUnI,SAASoI,EAAiBA,kBAACQ;;WAC9C,KAAKzM,GAAUoB;YACb,OAAO4K,EAAUnI,SAASoI,EAAiBA,kBAAC7K;;WAC9C,KAAKpB,GAAUsB;YACb,OAAO0K,EAAUnI,SAASoI,EAAiBA,kBAACS;;WAC9C,KAAK1M,GAAUuB;YAEb,QADsB/Y,iBAAAA,IAAAA,EAAWhB;aAE/B,KAAKwY,GAAUwB;cACb,OAAOwK,EAAUnI,SAASoI,EAAiBA,kBAACU;;aAC9C,KAAK3M,GAAUsB;cACb,OAAO0K,EAAUnI,SAASoI,EAAiBA,kBAACW;;;UAIpD,QAAO;AACT,SA1J2Bb,CAAgBxkB,EAAMiB,WAAWsjB;QACxD,OAAO5iB,WACLC,GACAyiB,EAAWjiB,KAAImiB,MAAc;UAC3B/hB,OAAO+hB,EAAUhkB;UACjB0Q,eAAesT,EAAU9a,eAAe;UACxCxJ,MAAMqO,GAAmBG;;;MAI/B,OAAO;AACT,KAlgBW2V,CAA2BxiB,GAAO5B,GAAOG;;EAGlD,OAAO;AACT;;AAEA,IAAMmlB,KAAe;;AAOrB,IAAM1E,gBAAiBN;EACrB,KAAMlgB,MAAEA,KAASkgB;EACjB,IAAI3f,EAAAA,gBAAgBP;IAClB,OAAOklB;;EAET,IAAIC,EAAAA,WAAWnlB,MAASO,EAAeA,gBAACP,EAAKolB;IAC3C,OAAOF;;EAET,IAAIG,EAAAA,cAAcrlB,IAAO;IACvB,IAAIO,EAAeA,gBAACP,EAAKolB;MACvB,OAAOF;;IAET,IAAIC,EAAUA,WAACnlB,EAAKolB,WAAW7kB,EAAeA,gBAACP,EAAKolB,OAAOA;MACzD,OAAOF;;;EAGX,OAAO;AAAI;;AAmWb,IAAMI,wBAAsBA,CAAC1lB,GAAcC;;EACzC,KAAmB,UAAfwI,IAAAzI,EAAMiB,mBAAS,MAAAwH,SAAA,IAAAA,EAAExI,UAASA;IAC5B,OAAOD,EAAMiB;;EAEf,eAAIoe,IAAe,UAAfL,IAAAhf,EAAMiB,mBAAS,MAAA+d,SAAA,IAAAA,EAAE/d,mBAAS,MAAAoe,SAAA,IAAAA,EAAEpf,UAASA;IACvC,OAAOD,EAAMiB,UAAUA;;EAEzB,KAAyC,UAArC0kB,IAA0B,UAA1B9F,IAAeF,UAAfA,IAAA3f,EAAMiB,yBAAS0e,aAAAA,EAAE1e,mBAAS,MAAA4e,SAAA,IAAAA,EAAE5e,mBAAS,MAAA0kB,SAAA,IAAAA,EAAE1lB,UAASA;IAClD,OAAOD,EAAMiB,UAAUA,UAAUA;;EAEnC,KAAoD,UAAhD2kB,IAAqCC,UAArCA,IAA0B,UAA1BC,IAAe,UAAfC,IAAA/lB,EAAMiB,mBAAS8kB,MAAAA,SAAAA,IAAAA,EAAE9kB,mBAAS,MAAA6kB,SAAA,IAAAA,EAAE7kB,yBAAS4kB,aAAAA,EAAE5kB,mBAAS2kB,MAAAA,SAAAA,IAAAA,EAAE3lB,UAASA;IAC7D,OAAOD,EAAMiB,UAAUA,UAAUA,UAAUA;;;;AAIzC,SAAUygB,uBACdjF,GACAtc,GACAyB;EAEA,IAAI+S,IAA8B;EAClC,IAAIqR;EACJ,IAAMC,IAAmC1kB,OAAO+C,OAAO,CAAE;EACzDua,kBAAgBpC,IAAW,CAACqC,GAAG9e;IAE7B,KAAIA,iBAAK,IAALA,EAAOC,UAASwY,GAAUsJ,YAAY/hB,EAAMO;MAC9CoU,IAAe3U,EAAMO;;IAEvB,KAAIP,qBAAAA,EAAOC,UAASwY,GAAU4F,cAAc1J,GAAc;MACxD,IAAMuR,IAAmBR,sBAAoB1lB,GAAOyY,GAAU8B;MAC9D,IAAI2L,qBAAAA,EAAkB9lB;QACpB4lB,IAAe7lB,EAAOgf,QACpB+G,iBAAAA,IAAAA,EAAkB9lB;;;IAKxB,IAAIuU,KAAgBqR,MAAiBC,EAAYtR,IAAe;MAG9DsR,EAAYtR,KAAgB;QAC1BrE,QAAQ0V,EAAatZ;QACrBiU,YAA6B,QAAjB/e,EAAMiB,SAAiB8R,IAAe,MAAMA;QACxDnS,OAAOmS;QACPvU,MAAM4lB;QACN/lB,MAAMqO,GAAmBM;;MAG3B+F,IAAe;MACfqR,IAAe;;;EAInB,OAAO5kB,aAAa6kB;AACtB;;AA8EM,SAAUnJ,mBACdL,GACAC,GACA1E,IAAS;EAET,IAAImO,IAAgB;EACpB,IAAIC,IAAgB;EACpB,IAAIC,IAAiB;EACrB,IAAMzkB,IAAQid,kBAAgBpC,IAAW,CAAC6J,GAAQtmB,GAAOumB,GAAOhG;IAC9D,IACEA,MAAU7D,EAAOnY,QACjB+hB,EAAOE,uBAAuBxO,IAAS0E,EAAOlY,YAAY;MAE1D;;IAEF2hB,IAAgBI;IAChBH,IAAa7kB,OAAAmX,OAAA,CAAA,GAAQ1Y;IACrBqmB,IAAiBC,EAAOG;IACxB,OAAO;AAAO;EAKhB,OAAO;IACLvhB,OAAOtD,EAAMsD;IACbC,KAAKvD,EAAMuD;IACXtC,QAAQwjB,KAAkBzkB,EAAMiB;IAChC7C,OAAOomB,KAAiBxkB,EAAM5B;IAC9BumB,OAAOJ,KAAiBvkB,EAAM2kB;;AAElC;;AAgBM,SAAU1H,kBACdpC,GACAiK;EAEA,IAAMC,IAAQlK,EAAUmK,MAAM;EAC9B,IAAMC,IAASC,EAAAA;EACf,IAAI9mB,IAAQ6mB,EAAOE;EACnB,IAAIR,IAAQ;EAEZ,IAAID,IAA0B,IAAIU,kBAAgB;EAElD,KAAK,IAAI9lB,IAAI,GAAGA,IAAIylB,EAAMxlB,QAAQD,KAAK;IACrColB,IAAS,IAAIU,EAAeA,gBAACL,EAAMzlB;IACnC,QAAQolB,EAAOW,OAAO;MAGpB,IAAa,YADAP,EAASJ,GAAQtmB,GAD9BumB,IAAQM,EAAOjlB,MAAM0kB,GAAQtmB,IACekB;QAE1C;;;IAMJwlB,EAASJ,GAAQtmB,GAAOumB,GAAOrlB;IAE/B,KAAKlB,EAAMC;MACTD,IAAQ6mB,EAAOE;;;EAInB,OAAO;IACL7hB,OAAOohB,EAAOY;IACd/hB,KAAKmhB,EAAOE;IACZ3jB,QAAQyjB,EAAOG;IACfzmB;IACAumB;;AAEJ;;AA8DM,SAAU/I,YACdrd,GACAN;EAEA,IAAImhB;EACJ,IAAID;EACJ,IAAIoG;EACJ,IAAIC;EACJ,IAAIC;EACJ,IAAI7F;EACJ,IAAI9B;EACJ,IAAIwB;EACJ,IAAIb;EACJ,IAAIjgB;EACJ,IAAI2e;EACJhf,aAAaF,IAAYG;;IACvB,QAAQA,EAAMC;KACZ,KAAKwY,GAAUM;KACf,KAAK;MACH3Y,IAAOD,EAAOK;MACd;;KACF,KAAKiY,GAAUO;MACb5Y,IAAOD,EAAOmnB;MACd;;KACF,KAAK7O,GAAUQ;MACb7Y,IAAOD,EAAOonB;MACd;;KACF,KAAK9O,GAAUkM;KACf,KAAKlM,GAAUuK;MACb,IAAIhjB,EAAMI;QACRA,IAAOD,EAAOgf,QAAQnf,EAAMI;;MAE9B;;KACF,KAAKqY,GAAU0H;KACf,KAAK1H,GAAUG;MACb,KAAKxY,MAASJ,EAAMO;QAClB8mB,IAAW;aACN;QACLA,IAAWhH,IACPngB,YAAYC,GAAQkgB,GAAYrgB,EAAMO,QACtC;QACJH,IAAOinB,IAAWA,EAASjnB,OAAO;;MAEpC;;KAEF,KAAKqY,GAAUyH;MACbG,IAAakB,EAAAA,aAAanhB;MAC1B;;KACF,KAAKqY,GAAU0L;MACbgD,IAAennB,EAAMO,OAAOJ,EAAOqnB,aAAaxnB,EAAMO,QAAQ;MAC9D;;KAEF,KAAKkY,GAAUiB;MACb,IAAI1Z,EAAMO,MAAM;QACdmf,IAAgB;QAChBX,IAAe,IAAIQ,EAAAA,qBAAqB;UACtChf,MAAMP,EAAMO;UACZif,YAAY;UACZC,QAAQ,CAAA;;;MAIZ;;KAEF,KAAKhH,GAAUc;MACb,IAAIvZ,EAAMO,MAAM;QACdwe,IAAe;QACfW,IAAgB,IAAII,EAAAA,kBAAkB;UACpCvf,MAAMP,EAAMO;UACZif,YAAY;UACZC,QAAQ,CAAA;;;MAIZ;;KACF,KAAKhH,GAAUI;MACb,IAAI7Y,EAAMiB;QACR,QAAQjB,EAAMiB,UAAUhB;SACtB,KAAKwY,GAAU0H;UACbY,IAAUsG,KAAaA,EAASte;UAChC;;SACF,KAAK0P,GAAU0L;UACbpD,IACEoG,KAAiBA,EAAape;UAChC;;SAEF,KAAK0P,GAAUG;UACb,IAAMrY,IAAsBkI,UAAfA,IAAAzI,EAAMiB,mBAASwH,MAAAA,SAAAA,IAAAA,EAAElI;UAC9B,KAAKA,GAAM;YACTwgB,IAAU;YACV;;UAEF,IAAMT,IAAQD,IACVngB,YAAYC,GAAQkgB,GAAY9f,KAChC;UACJ,KAAK+f,GAAO;YACVS,IAAU;YACV;;UAEFA,IAAUT,EAAMvX;UAChB;;SAEF;UACEgY,IAAU;;;QAIdA,IAAU;;MAEZ;;KAEF,KAAKtI,GAAUqI;MACb,IAAIC;QACF,KAAK,IAAI7f,IAAI,GAAGA,IAAI6f,EAAQ5f,QAAQD;UAClC,IAAI6f,EAAQ7f,GAAGX,SAASP,EAAMO,MAAM;YAClCygB,IAASD,EAAQ7f;YACjB;;;;MAINsgB,IAAYR,iBAAAA,IAAAA,EAAQ5gB;MACpB;;KAEF,KAAKqY,GAAUoB;MACb,IAAM4N,IAAWlG,eAAaC;MAC9B4F,IACEK,aAAoB7F,EAAAA,kBAChB6F,EACG5F,YACA3C,MAAMwI,KAA0BA,EAAI3jB,UAAU/D,EAAMO,SACvD;MACN;;KAEF,KAAKkY,GAAUgB;MACb,IAAMkO,IAAeC,kBAAgBpG;MACrCA,IACEmG,aAAwBE,EAAAA,cAAcF,EAAanC,SAAS;MAC9D;;KACF,KAAK/M,GAAUe;MACb,IAAMsO,IAAavG,eAAaC;MAChCN,IACE4G,aAAsBC,EAAsBA,yBACxCD,EAAWlnB,cACX;MACN;;KAEF,KAAK6X,GAAUwI;MACb,IAAM+G,IACJhoB,EAAMO,QAAQ2gB,IAAkBA,EAAgBlhB,EAAMO,QAAQ;MAChEihB,IAAYwG,iBAAAA,IAAAA,EAAa5nB;MAEzB;;KACF,KAAKqY,GAAU4F;MACb,IAAIre,EAAMO;QACRH,IAAOD,EAAOgf,QAAQnf,EAAMO;;;;EAUpC,OAAO;IACLygB;IACAD;IACAoG;IACAC;IACAC;IACA7F;IACAN;IACAb;IACAjgB;IACA2e;IACAW;;AAEJ;;AAEA,IAAYvC;;CAAZ,SAAYA;EACVA,EAA2B,cAAA;EAC3BA,EAAyB,aAAA;AAC1B,CAHD,CAAYA,OAAAA,KAAmB,CAAA;;AAiB/B,SAASsG,WAAWzjB;EAClB,IACEA,EAAMiB,aACNjB,EAAMC,QAEJ,EACEwY,GAAU4F,YACV5F,GAAUkL,WACVlL,GAAU8B,MACV9B,GAAUsL,gBAEZzH,SAAStc,EAAMC;IAEjB,OAAOwjB,WAAWzjB,EAAMiB;;EAE1B,OAAOjB;AACT;;ACzwCM,SAAUioB,oBACd9nB,GACAsc,GACAC,GACAC,GACAuL;EAEA,IAAMtmB,IAAQ+a,KAAgBG,mBAAmBL,GAAWC;EAE5D,KAAKvc,MAAWyB,MAAUA,EAAM5B;IAC9B,OAAO;;EAGT,KAAMC,MAAEA,GAAIqd,MAAEA,KAAS1b,EAAM5B;EAC7B,IAAMud,IAAWC,YAAYrd,GAAQyB,EAAM5B;EAC3C,IAAMqK,IAAO9I,OAAAmX,OAAAnX,OAAAmX,OAAA,CAAA,GAAQwP,IAAM;IAAE/nB;;EAK7B,IACY,YAATF,KAA6B,MAATqd,KAAcC,EAAS8J,YAClC,mBAATpnB,KAAoC,MAATqd,KAAcC,EAAS8J,UACnD;IACA,IAAMc,IAAiB;IACvBC,kBAAkBD,GAAM9d;KAwD5B,SAASge,YAAYF,GAAgB5K,GAAuBlT;MAC1Die,qBAAqBH,GAAM5K,GAAUlT;MACrCke,qBAAqBJ,GAAM5K,GAAUlT,GAASkT,EAASnd;AACzD,KA1DIioB,CAAYF,GAAM5K,GAAUlT;IAC5Bme,gBAAgBL,GAAM9d;IACtBoe,kBAAkBN,GAAM9d,GAASkT,EAAS8J;IAC1C,OAAOc,EAAK5oB,KAAK,IAAImpB;;EAEvB,IAAa,gBAATzoB,KAAiC,MAATqd,KAAcC,EAAS4J,cAAc;IAC/D,IAAMgB,IAAiB;IACvBC,kBAAkBD,GAAM9d;IACxBse,gBAAgBR,GAAM5K;IACtBiL,gBAAgBL,GAAM9d;IACtBoe,kBAAkBN,GAAM9d,GAASkT,EAAS4J;IAC1C,OAAOgB,EAAK5oB,KAAK,IAAImpB;;EAEvB,IAAa,eAATzoB,KAAgC,MAATqd,KAAcC,EAASyD,QAAQ;IACxD,IAAMmH,IAAiB;IACvBC,kBAAkBD,GAAM9d;KAqE5B,SAASue,UAAUT,GAAgB5K,GAAuBlT;MACxD,IAAIkT,EAAS4J;QACXwB,gBAAgBR,GAAM5K;aACjB,IAAIA,EAAS8J;QAClBiB,qBAAqBH,GAAM5K,GAAUlT;;MAGvC,KAAKkT,EAASyD;QACZ;;MAGF,KAAMzgB,MAAEA,KAASgd,EAASyD;MAC1Bjf,KAAKomB,GAAM;MACXpmB,KAAKomB,GAAM5nB;MACXgoB,qBAAqBJ,GAAM5K,GAAUlT,GAASkT,EAASiE;MACvDzf,KAAKomB,GAAM;AACb,KApFIS,CAAUT,GAAM5K,GAAUlT;IAC1Bme,gBAAgBL,GAAM9d;IACtBoe,kBAAkBN,GAAM9d,GAASkT,EAASyD;IAC1C,OAAOmH,EAAK5oB,KAAK,IAAImpB;;EAEvB,IACW,gBAATzoB,KACAsd,EAAS6J,aACT,iBAAiB7J,EAAS6J,WAC1B;IACA,IAAMe,IAAiB;IACvBC,kBAAkBD,GAAM9d;KAqF5B,SAASwe,gBAAgBV,GAAgB5K,GAAuBlT;MAC9D,KAAKkT,EAAS6J;QACZ;;MAEF,KAAM7mB,MAAEA,KAASgd,EAAS6J;MAC1B0B,WAAWX,GAAM5K,GAAUlT,GAASkT,EAASiE;MAC7Czf,KAAKomB,GAAM;MACXpmB,KAAKomB,GAAM5nB;AACb,KA5FIsoB,CAAgBV,GAAM5K,GAAUlT;IAChCme,gBAAgBL,GAAM9d;IACtBoe,kBAAkBN,GAAM9d,GAASkT,EAAS6J;IAC1C,OAAOe,EAAK5oB,KAAK,IAAImpB;;EAEvB,IAAa,gBAATzoB,KAAwBsd,EAASnd,QAAQ,iBAAiBmd,EAASnd,MAAM;IAC3E,IAAM+nB,IAAiB;IACvBC,kBAAkBD,GAAM9d;IACxBye,WAAWX,GAAM5K,GAAUlT,GAASkT,EAASnd;IAC7CooB,gBAAgBL,GAAM9d;IACtBoe,kBAAkBN,GAAM9d,GAASkT,EAASnd;IAC1C,OAAO+nB,EAAK5oB,KAAK,IAAImpB;;EAEvB,OAAO;AACT;;AAEA,SAASN,kBAAkBD,GAAgB9d;EACzC,IAAIA,EAAQ0e;IACVhnB,KAAKomB,GAAM;;AAEf;;AACA,SAASK,gBAAgBL,GAAgB9d;EACvC,IAAIA,EAAQ0e;IACVhnB,KAAKomB,GAAM;;AAEf;;AAOA,SAASG,qBACPH,GACA5K,GACAlT;EAEA,KAAKkT,EAAS8J;IACZ;;EAEF,IAAMhnB,IAAYkd,EAAS8J,SAAS9mB;EACpC,IAA8B,SAA1BF,EAAU2W,MAAM,GAAG,IAAa;IAClC8R,WAAWX,GAAM5K,GAAUlT,GAASkT,EAAS8C;IAC7Cte,KAAKomB,GAAM;;EAEbpmB,KAAKomB,GAAM9nB;AACb;;AAEA,SAASsoB,gBAAgBR,GAAgB5K,GAAuByL;EAC9D,KAAKzL,EAAS4J;IACZ;;EAGFplB,KAAKomB,GADQ,MAAM5K,EAAS4J,aAAa5mB;AAE3C;;AAoBA,SAASgoB,qBACPJ,GACA5K,GACAlT,GACA4e;EAEAlnB,KAAKomB,GAAM;EACXW,WAAWX,GAAM5K,GAAUlT,GAAS4e;AACtC;;AAYA,SAASH,WACPX,GACA5K,GACAlT,GACA4e;EAEA,KAAKA;IACH;;EAGF,IAAIA,aAAaC,EAAAA,gBAAgB;IAC/BJ,WAAWX,GAAM5K,GAAUlT,GAAS4e,EAAEzD;IACtCzjB,KAAKomB,GAAM;AACZ,SAAM,IAAIc,aAAapB,eAAa;IACnC9lB,KAAKomB,GAAM;IACXW,WAAWX,GAAM5K,GAAUlT,GAAS4e,EAAEzD;IACtCzjB,KAAKomB,GAAM;;IAEXpmB,KAAKomB,GAAMc,EAAE1oB;;AAEjB;;AAEA,SAASkoB,kBACPN,GACA9d,GAEA0Q;EAEA,KAAKA;IACH;;EAEF,IAAMtR,IACuB,mBAApBsR,EAAItR,cAA2BsR,EAAItR,cAAc;EAC1D,IAAIA,GAAa;IACf1H,KAAKomB,GAAM;IACXpmB,KAAKomB,GAAM1e;;GAKf,SAAS0f,kBACPhB,GACAa,GACAjO;IAEA,KAAKA;MACH;;IAGF,IAAMqO,IAASrO,EAAI0F,qBAAqB;IACxC,KAAK2I;MACH;;IAEFrnB,KAAKomB,GAAM;IACXpmB,KAAKomB,GAAM;IACXpmB,KAAKomB,GAAMiB;AACb,GAnBED,CAAkBhB,GAAM9d,GAAS0Q;AACnC;;AAoBA,SAAShZ,KAAKomB,GAAgBtS;EAC5BsS,EAAKnnB,KAAK6U;AACZ;;AC5OO,MAAMwT;EAIXC,WAAAA,CAAY/kB,GAAcglB;IACxBne,KAAK7G,OAAOA;IACZ6G,KAAK5G,YAAY+kB;AACnB;EAEAC,OAAAA,CAAQjlB;IACN6G,KAAK7G,OAAOA;AACd;EAEAklB,YAAAA,CAAajlB;IACX4G,KAAK5G,YAAYA;AACnB;EAEAklB,iBAAAA,CAAkBrgB;IAChB,OACE+B,KAAK7G,OAAO8E,EAAS9E,QACpB6G,KAAK7G,SAAS8E,EAAS9E,QAAQ6G,KAAK5G,aAAa6E,EAAS7E;AAE/D;;;ACZK,IAAMmlB,WAAWA,CACtBC,GACAC;EAEA,KAAKC,EAAAA,GAAGC,kBAAkBH,OAAcE,KAAGE,oBAAoBJ;IAC7D;;EAIF,IAAMK,IADOL,EAAS3T,UAAUe,MAAM,IAAI,GACvB4P,MAAM;EACzB,IAAMC,IAASC,EAAAA;EACf,IAAM9mB,IAAQ6mB,EAAOE;EACrB,IAAImD,IAAON,EAASO,aAAa;EAEjC,IAAIC,SAAgC3kB;EACpC,IAAI4kB,SAA+B5kB;EACnC,KAAK,IAAIlB,IAAO,GAAGA,IAAO0lB,EAAM9oB,QAAQoD,KAAQ;IAC9C,IAAI6lB;MAAY;;IAChB,IAAME,IAAOJ,IAAO;IACpB,IAAM5D,IAAS,IAAIU,EAAeA,gBAACiD,EAAM1lB,KAAQ;IACjD,QAAQ+hB,EAAOW,OAAO;MACpB,IAAMrlB,IAAQilB,EAAOjlB,MAAM0kB,GAAQtmB;MACnC,IAAM6C,IAASyjB,EAAOG;MAEtB,IACE6D,IAAOhE,EAAOY,oBAAoB,KAAK2C,KACvCS,IAAOhE,EAAOE,wBAAwBqD,GACtC;QACAO,IAAaC,IACTA,IACA;UACE9lB;UACAW,OAAOohB,EAAOY,oBAAoB;UAClC/hB,KAAKmhB,EAAOE;UACZ3jB;UACA7C;UACAuqB,WAAW3oB;;QAEjB;AACF,aAAO,IAAe,SAAXiB;QACTwnB,IAAY;UACV9lB;UACAW,OAAOohB,EAAOY,oBAAoB;UAClC/hB,KAAKmhB,EAAOE;UACZ3jB;UACA7C;UACAuqB,WAAW3oB;;aAER,IAAe,QAAXiB,KAA6B,SAAXA;QAC3BwnB,IAAY;UACV9lB;UACAW,OAAOohB,EAAOY,oBAAoB;UAClC/hB,KAAKmhB,EAAOE;UACZ3jB;UACA7C;UACAuqB,WAAW3oB;;;QAGbyoB,SAAY5kB;;AAEhB;IAEAykB,KAAQD,EAAM1lB,GAAOpD,SAAS;AAChC;EAEA,OAAOipB;AAAU;;AC3CnB,SAASzoB,SACPC,GACAC;EAEA,OAKF,SAASC,kBACPD,GACAE;IAEA,KAAKA;MACH,OAAOC,eAAkBH,IAAMI,MAAUA,EAAMC;;IAGjD,IAAMC,IAAcN,EAAKO,KAAIH,MAAU;MACrCI,WAAWC,aAAaC,cAAcN,EAAMO,QAAQT;MACpDE;;IAGF,OAAOD,eACLA,eAAeG,IAAaM,KAAQA,EAAKJ,aAAa,MACtDI,MAASA,EAAKR,MAAMC,eAEnBQ,MACC,CAACC,GAAGC,OACDD,EAAEV,MAAMC,eAAe,IAAI,MAAMU,EAAEX,MAAMC,eAAe,IAAI,MAC7DS,EAAEN,YAAYO,EAAEP,aAChBM,EAAEV,MAAMO,MAAMrB,SAASyB,EAAEX,MAAMO,MAAMrB,SAExCiB,KAAIK,KAAQA,EAAKR;AACtB,GA7BSH,CAAkBD,GAAMU,cAAcX,EAAMiB;AACrD;;AAgCA,SAASb,eACPc,GACAxF;EAEA,IAAMyF,IAAWD,EAAME,OAAO1F;EAC9B,OAA2B,MAApByF,EAAS5B,SAAe2B,IAAQC;AACzC;;AAEA,SAASR,cAAcR;EACrB,OAAOA,EAAKkB,cAAciG,QAAQ,OAAO;AAC3C;;AAGA,SAAS5G,aAAaa,GAAoBpB;EAExC,IAAIM,IAwBN,SAASe,gBAAgBT,GAAWC;IAClC,IAAI1B;IACJ,IAAImC;IACJ,IAAMC,IAAI;IACV,IAAMC,IAAUZ,EAAExB;IAClB,IAAMqC,IAAUZ,EAAEzB;IAElB,KAAKD,IAAI,GAAGA,KAAKqC,GAASrC;MACxBoC,EAAEpC,KAAK,EAACA;;IAGV,KAAKmC,IAAI,GAAGA,KAAKG,GAASH;MACxBC,EAAE,GAAID,KAAKA;;IAGb,KAAKnC,IAAI,GAAGA,KAAKqC,GAASrC;MACxB,KAAKmC,IAAI,GAAGA,KAAKG,GAASH,KAAK;QAC7B,IAAMI,IAAOd,EAAEzB,IAAI,OAAO0B,EAAES,IAAI,KAAK,IAAI;QAEzCC,EAAEpC,GAAImC,KAAKK,KAAKC,IACdL,EAAEpC,IAAI,GAAImC,KAAM,GAChBC,EAAEpC,GAAImC,IAAI,KAAM,GAChBC,EAAEpC,IAAI,GAAImC,IAAI,KAAMI;QAGtB,IAAIvC,IAAI,KAAKmC,IAAI,KAAKV,EAAEzB,IAAI,OAAO0B,EAAES,IAAI,MAAMV,EAAEzB,IAAI,OAAO0B,EAAES,IAAI;UAChEC,EAAEpC,GAAImC,KAAKK,KAAKC,IAAIL,EAAEpC,GAAImC,IAAKC,EAAEpC,IAAI,GAAImC,IAAI,KAAMI;;AAEvD;;IAGF,OAAOH,EAAEC,GAAUC;AACrB,GAxDkBJ,CAAgBrB,GAAMoB;EACtC,IAAIA,EAAWhC,SAASY,EAAKZ,QAAQ;IAEnCkB,KAAac,EAAWhC,SAASY,EAAKZ,SAAS;IAE/CkB,KAA0C,MAA7Bc,EAAWS,QAAQ7B,KAAc,IAAI;AACpD;EACA,OAAOM;AACT;;AC/DO,SAASmoB,sBACdC,GACAZ,GACA1pB,GACAuqB;EAEA,IAAMC,IAAmBD,EAAKxC,OAAO0C,6BAA4B;EACjE,IAAMC,IAAcH,EAAKI,gBAAgBC,cAAcC;EACvD,IAAMziB,IAAS0iB,EAAAA,UAAUP,GAAMD;EAC/B,KAAKliB;IAAQ;;EAEb,IAAI8T,IAAO6O,EAAAA,SAAS3iB,GAAQshB;EAC5B,KAAKxN;IAAM;;EAEXA,IAAOsO,IACHQ,EAAsBA,uBAAC9O,KACvB+O,EAAAA,iBAAiB/O;EAErB,IAAIta,GAAM2a,GAAQ2O;EAClB,IAAIV,KAAoBW,EAAAA,cAAqBjP,GAAMwO,IAAc;IAC/D,IAAMU,IAAaD,EAAAA,cAAqBjP,GAAMwO;IAE9CQ,IACEE,KAAcprB,EAAOqrB,MAAMD,KACvBprB,EAAOqrB,MAAMD,IAAaprB,SAC1BA,EAAOsmB,SAAStmB;IAEtB,IAAMiqB,IAAaT,SAAStN,EAAKpT,UAAU,IAAI4gB;IAC/C,KACGwB,MACAjB,KACqB,QAAtBA,EAAWvnB,UACW,SAAtBunB,EAAWvnB;MAEX;;IAKFd,IAAQ,GAHUsa,EAAKpT,UAAU,GAAGgN,UAAUe,MAAM,IAAI,OACtCyU,EAAeA,gBAAChB,GAAUpO,GAAMqO,GAEhBtoB,KAAIspB,KAAKC,EAAKA,MAACD,KAAInsB,KAAK;IAC1Dmd,IAAS,IAAI2M,OAAOe,EAAW7lB,MAAM6lB,EAAWllB,QAAQ;AACzD,SAAM,KAAKylB,KAAoBW,EAAmBM,aAACvP,IAAO;IACzD,IAAM+N,IAAaT,SAAStN,EAAKuN,UAAUC;IAC3C,KACGO,MACAjqB,EAAOsmB,WACc,QAAtB2D,EAAWvnB,UACW,SAAtBunB,EAAWvnB;MAEX;;IAEF,KAAMgpB,cAAEA,GAAYC,eAAEA,KAAkBC,kBACtC1P,GACAoO,GACAC;IAGF,IAAMsB,IAAgBF,EACnB9oB,QACC0oB,KACEA,EAAEO,SAAS/mB,QAAQ2kB,KACnB6B,EAAEO,SAAS/mB,QAAQwmB,EAAEO,SAAS9qB,SAAS0oB,IAE1CqC,QAAO,CAACC,GAAKC,MAASD,KAAOC,EAAKzF,QAAQ,KAAI;IAEjDyD,EAAW7lB,OAAO6lB,EAAW7lB,OAAOynB;IAEpCjqB,IAAO8pB;IACPnP,IAAS,IAAI2M,OAAOe,EAAW7lB,MAAM6lB,EAAWllB,QAAQ;IACxDmmB,IAAclrB,EAAOsmB,QAAQtmB;AAC/B;IACE;;EAGF,KAAOksB,GAAaC,KAuCf,SAASC,uBACdpsB,GACAsc,GACAC;IAEA,IAAM9a,IAAQkb,mBAAmBL,GAAWC;IAE5C,IAAIoG,IAA2C;IAC/C;MAEEA,IADejI,EAAKA,MAAC4B,GAAW;QAAE+P,aAAY;SAC3BvG,YAAYjjB,QAC7B0oB,KAAKA,EAAEzrB,SAAS0Y,OAAKqK;AAEzB,MAAE,OAAOtM,IAAI;IAEb,IAAM+V,IACa,SAAjB7qB,EAAMiB,UAAwC,oBAArBjB,EAAM5B,MAAMC;IACvC,IAAIosB,IAAc7P,2BAChBrc,GACAsc,GACAC,GACA+P,IACI;SACK7qB;MACH5B,OAAO;WACF4B,EAAM5B;QACTsd,MAAM;;MAERld,MAAM;aAERqF;IAEN,IAAI6mB,KAAqBG,IDfpB,SAAS7J,gCACdhhB,GACA2b,GACApd,GACAsc,GACAqG;MAEA,KAAKrG;QACH,OAAO;;MAGT,IAAM+B,IAAUre,EAAO0d;MACvB,IAAMgF,IAAWjjB,mBAAmBgC,EAAM5B;MAuB1C,OAAO2B,SACLC,GArBoBkhB,EAAU9f,QAC9BsgB,KAEE9E,EAAQ8E,EAAKF,cAAc7iB,KAAKwD,YAG9B8e,KACAA,EAAS5iB,SAASwY,GAAUuK,uBAC5BH,EAAStiB,SAAS+iB,EAAK/iB,KAAKwD,UAG9BpD,kBAAgB4c,EAAS8C,eACzB1f,EAAeA,gBAAC6d,EAAQ8E,EAAKF,cAAc7iB,KAAKwD,WAChDwf,EAAAA,eACEpjB,GACAod,EAAS8C,YACT7B,EAAQ8E,EAAKF,cAAc7iB,KAAKwD,UAMtB3B,KAAIkhB,MAAS;QACzB9gB,OAAO8gB,EAAK/iB,KAAKwD;QACjBuM,QAAQqB,OAAO6M,EAAQ8E,EAAKF,cAAc7iB,KAAKwD;QAC/CkN,eAAgB,YAAWqS,EAAK/iB,KAAKwD,YAAYuf,EAAKF,cAAc7iB,KAAKwD;QACzE9D,MAAMqO,GAAmBK;QACzBvO,MAAMoe,EAAQ8E,EAAKF,cAAc7iB,KAAKwD;;AAG5C,KC7BM6e,CACEhhB,GACA4b,YAAYrd,GAAQyB,EAAM5B,QAC1BG,GACAsc,GACAqG,KAEF;IAEJ,IAAM9iB,IACiB,cAArB4B,EAAM5B,MAAMC,OAAqB2B,EAAM5B,MAAMiB,YAAYW,EAAM5B;IACjE,IAAM0sB,IAAahH,oBAAoB9jB,EAAM5B,OAAOyY,GAAU0H,QAAQ5f;IAEtE,IAAIP,KAAS0sB,GAAY;MACvB,KAAMzsB,MAAEA,KAASD;MAGjB,IAAIC,MAASwY,GAAUI,aAAa5Y,MAASwY,GAAUqI,UAAU;QAC/D,IAAM6L,IAAgB,IAAI/N;QAE1BC,gBAAgBpC,IAAW,CAACqC,GAAG9e;UAC7B,IAAIA,EAAMC,SAASwY,GAAUqI,UAAU;YACrC,IAAMoF,IAAmBR,oBAAoB1lB,GAAOyY,GAAU0H;YAC9D,IACEuM,KACA1sB,EAAMO,QACN2lB,GAAkB3lB,SAASmsB;cAE3BC,EAAchhB,IAAI3L,EAAMO;;AAE5B;AAAA;QAGF8rB,IAAcA,EAAYrpB,QACxBG,MAAewpB,EAAcC,IAAIzpB,EAAWX;AAEhD;MAGA,IACEvC,MAASwY,GAAUyH,iBACnBjgB,MAASwY,GAAU0H,SACnBlgB,MAASwY,GAAUG,eACnB;QACA,IAAMiU,IAAa,IAAIjO;QACvB,IAAMkO,IAAgBC,iBAAiBtQ,GAAWiQ;QAElD7N,gBAAgBpC,IAAW,CAACqC,GAAG9e;UAC7B,IACEA,EAAMC,SAASwY,GAAU0H,SACzBngB,EAAMC,SAASwY,GAAUG,eACzB;YACA,IAAMsN,IAAmBR,oBAAoB1lB,GAAOyY,GAAU0H;YAC9D,IACE+F,KACAA,EAAiB3lB,SAASmsB,KAC1B1sB,EAAMO;cAENssB,EAAWlhB,IAAI3L,EAAMO;;AAEzB;AAAA;QAGF8rB,IAAcA,EAAYrpB,QACxBG,MAAe0pB,EAAWD,IAAIzpB,EAAWX;QAE3C8pB,IAAoBA,EAAkBtpB,QACpCG,MAAe2pB,EAAcF,IAAIzpB,EAAWX;AAEhD;MAGA,IAAIvC,MAASwY,GAAUkK,iBAAiB;QACtC,IAAMmK,IAAgBC,iBAAiBtQ,GAAWiQ;QAClDL,IAAcA,EAAYrpB,QACxBG,MAAe2pB,EAAcF,IAAIzpB,EAAWX;QAE9C8pB,IAAoBA,EAAkBtpB,QACpCG,MAAe2pB,EAAcF,IAAIzpB,EAAWX;AAEhD;AACF;IAEA,OAAO,EAAC6pB,GAAaC;AACvB,GA5J2CC,CACvClB,GACAtpB,GACA2a;EAGF,OAAO;IACLsQ,qBAAoB;IACpBC,qBAAoB;IACpBC,0BAAyB;IACzBC,SAAS,KACJd,EAAYjqB,KAAIe,MAAe;SAC7BA;MACHlD,MAAM6pB,EAAAA,GAAGsD,kBAAkBC;MAC3B9sB,MAAM4C,EAAWX;MACjB8qB,eAAe;MACf9M,UAAUrd,EAAWqd,YAAY;MACjC+M,cAAc;QACZjd,QAAQnN,EAAW/C,OACf,MAAM+C,EAAW/C,MAAMsM,kBACvBjH;QACJgE,aAAatG,EAAW8N;;aAGzBqb,EAAkBlqB,KAAIe,MAAe;SACnCA;MACHlD,MAAM6pB,EAAAA,GAAGsD,kBAAkBC;MAC3B9sB,MAAM4C,EAAWX;MACjBme,YAAY,QAAQxd,EAAWX;MAC/B8qB,eAAe;MACf9M,UAAU;MACV+M,cAAc;QACZ9jB,aAAatG,EAAW8N;;;;AAKlC;;AAyHA,SAAS8b,iBAAiBtQ,GAAmBiQ;EAC3C,IAAMI,IAAgB,IAAIlO;EAE1BC,gBAAgBpC,IAAW,CAACqC,GAAG9e;IAC7B,IAAIA,EAAMC,SAASwY,GAAUkK,mBAAmB3iB,EAAMO,MAAM;MAC1D,IAAM2lB,IAAmBR,oBAAoB1lB,GAAOyY,GAAU0H;MAC9D,IAAIuM,KAAcxG,GAAkB3lB,SAASmsB;QAC3CI,EAAcnhB,IAAI3L,EAAMO;;AAE5B;AAAA;EAGF,OAAOusB;AACT;;AAKA,SAASpH,oBAAoB1lB,GAAcC;EACzC,IAAID,EAAMiB,WAAWhB,SAASA;IAC5B,OAAOD,EAAMiB;;EAEf,IAAIjB,EAAMiB,WAAWA,WAAWhB,SAASA;IACvC,OAAOD,EAAMiB,UAAUA;;EAEzB,IAAIjB,EAAMiB,WAAWA,WAAWA,WAAWhB,SAASA;IAClD,OAAOD,EAAMiB,UAAUA,UAAUA;;EAEnC,IAAIjB,EAAMiB,WAAWA,WAAWA,WAAWA,WAAWhB,SAASA;IAC7D,OAAOD,EAAMiB,UAAUA,UAAUA,UAAUA;;AAE/C;;AAEA,SAAS4d,gBACPpC,GACAiK;EAOA,IAAMC,IAAQlK,EAAUmK,MAAM;EAC9B,IAAMC,IAASC,EAAAA;EACf,IAAI9mB,IAAQ6mB,EAAOE;EACnB,IAAIR,IAAQ;EAEZ,IAAID,IAA0B,IAAIU,kBAAgB;EAElD,KAAK,IAAI9lB,IAAI,GAAGA,IAAIylB,EAAMxlB,QAAQD,KAAK;IACrColB,IAAS,IAAIU,EAAeA,gBAACL,EAAMzlB;IACnC,QAAQolB,EAAOW,OAAO;MAGpB,IAAa,YADAP,EAASJ,GAAQtmB,GAD9BumB,IAAQM,EAAOjlB,MAAM0kB,GAAQtmB,IACekB;QAE1C;;AAEJ;IAIAwlB,EAASJ,GAAQtmB,GAAOumB,GAAOrlB;IAE/B,KAAKlB,EAAMC;MACTD,IAAQ6mB,EAAOE;;AAEnB;EAEA,OAAO;IACL7hB,OAAOohB,EAAOY;IACd/hB,KAAKmhB,EAAOE;IACZ3jB,QAAQyjB,EAAOG;IACfzmB;IACAumB;;AAEJ;;AC7SA,SAASjiB,OAAOomB;EACd,IAAM5rB,SAAkB0uB,KACtB9C,EAAK+C,QAAQC,eAAe5uB,OAAO4rB,KAAM,eAAc8C;EACzD,IAAMtF,IAAiBwC,EAAKxC;EAE5BppB,OAAO,aAAa6uB,KAAKC,UAAU1F;EACnC,KAAKA,EAAO/nB,WAAW+nB,EAAO2F,SAAS;IACrC/uB,OAAO;IACP,MAAM,IAAIsG,MAAM;AAClB;EAEAtG,OAAO;EAEP,IAAIopB,EAAO0B;IACTkE,EAAAA,UAAUniB,IAAIuc,EAAO0B;;EAGvB,IAAMmE,IA5CR,SAASC,qBAAqBtD;IAC5B,IAAMqD,IAA4BxsB,OAAO+C,OAAO;IAAM,IAAA2pB,QAAAA;MAIpD,IAAMvC,IAAIhB,EAAKI,gBAAgBoD;MAE/BH,EAAMG,KAAK,IAAInlB,MAAoB2iB,EAAEyC,MAAMzD,EAAKI,iBAAiB/hB;;IALnE,KAAK,IAAImlB,KAAK3sB,OAAOD,KAAKopB,EAAKI;MAAgBmD;;IAQ/C,OAAOF;AACT,GAiCgBC,CAAqBtD;EAEnC,IAAMvqB,IXyCkBiuB,EAExB1D,GACA2D,GACAvvB;IAEA,IAAMwvB,IAAMC,UAAQF;IAEpB;MACE,IAAMG,UACGC,EAAwBA,yBAAC/D,EAAK+C,QAAQiB,qBAC7CpvB,EAAKI,QAAQgrB,EAAK+C,QAAQiB;MAE5B,IAAMC,IACJjE,EAAKxC,OAAOyG,6BAA4B;MAC1C,IAAM/vB,IACJ8rB,EAAKxC,OAAOtpB,sBACZU,EAAKsvB,QAAQJ,GAAU9D,EAAKxC,OAAOtpB;MAErCE,EAAO,gDAAgD0vB;MACvD1vB,EAAO,4CAA4C6uB,KAAKC,UAAUS;MAElE;QACEvvB,EAAQ;cACFwvB,EAAIO,KAAK;UAAEL;;AAClB,QAAC,OAAOrwB;QACPW,EAAQ,0BAAyBX;AACnC;MAEA,IAAImwB,EAAI7H;QACN,IAAI6H,EAAI7H,gBAA8ChhB,MAAnC6oB,EAAI7H,QAAQ7nB;UAC7BF,sBACE4vB,EAAI7H,QAAQ9nB,eACZC,GACA+vB,GACA7vB;;aAGC,IAAIwvB,EAAI9C;QACbjqB,OAAOE,OAAO6sB,EAAI9C,OAAOve,SAAQlJ;UAC/B,KAAKA;YAAO;;UAEZ,IAAIA,EAAMnF;YACRF,sBACEqF,EAAMpF,eACNW,EAAKsvB,QAAQJ,GAAUzqB,EAAMnF,qBAC7B+vB,GACA7vB;;AAEJ;;MAIJwvB,EAAIQ,WAAW;QAAEN;UAAY,CAACO,GAAWhrB;QACvC,KAAKA;UAAO;;QAEZ,IAAIA,EAAMnF,oBAAoB;UAC5B,IAAMowB,IAAQD,EAAUvD,QACpBuD,EAAUvD,MAAMznB,EAAMxD,QACtBwuB,EAAUtI;UACd,KAAKuI;YAAO;;UACZtwB,sBACEswB,EAAMrwB,eACNW,EAAKsvB,QAAQJ,GAAUzqB,EAAMnF,qBAC7B+vB,GACA7vB;AAEJ;AAAA;AAEH,MA7DD;IA+DA,OAAOwvB;AAAG,IWhHKF,CAAW1D,GAAMxC,GAAQppB;EAExCivB,EAAMkB,yBAA0BxE;IAC9B,IAAMyE,IACJxE,EAAKI,gBAAgBmE,uBAAuBxE;IAK9C,IAH8ByE,EAAoBC,MAAKzD,KACrD0D,EAAeA,gBAAC9S,SAASoP,EAAEpjB;MAEF,OAAO4mB;;IAElC,IAAMG,IAAqBC,EAAqBA,sBAAC7E,GAAUtqB,GAAQuqB;IAEnE,OAAO2E,IACH,KAAIA,MAAuBH,MAC3BA;AAAmB;EAGzBnB,EAAMwB,2BAA2B,CAC/B9E,GACAZ,GACAxf;IAEA,IAAMmlB,IAAchF,sBAClBC,GACAZ,GACA1pB,GACAuqB;IAGF,IAAI8E,KAAeA,EAAYrC,QAAQhsB;MACrC,OAAOquB;;MAEP,OACE9E,EAAKI,gBAAgByE,yBACnB9E,GACAZ,GACAxf,MACG;QACH2iB,qBAAoB;QACpBC,qBAAoB;QACpBC,0BAAyB;QACzBC,SAAS;;;AAGf;EAGFY,EAAM0B,sBAAsB,CAC1BhF,GACAiF,GACAC,GACAC,GACAC,GACAC,GACAC;IAEA,IAAM9D,IAAWvB,EAAKI,gBAAgB2E,oBACpChF,GACAiF,GACAC,GACAC,GACAC,GACAC,GACAC;IAGF,IAAMC,IAAUC,EAAAA,8BACdxF,GAC2B,mBAApBkF,IACHA,IACAA,EAAgBO,KACpBxF;IAEF,KAAKsF;MAAS,OAAO/D;;IACrB,OAAO;MACLhiB,OAAO,EACL;QACEkmB,UAAU1F;QACV2F,aAAa,EAAC;UAAEjnB,SAAS6mB,EAAQK;UAAajE,MAAM4D,EAAQ5D;;;;AAGjE;EAGH2B,EAAMuC,yBAAyB,CAC7B7F,GACAkF,GACAG,GACA1G,GACAnpB,GACAswB;IAEA,IAAMtE,IAAWvB,EAAKI,gBAAgBwF,uBACpC7F,GACAkF,GACAG,GACA1G,GACAnpB,GACAswB;IAWF,IARgBN,EAAAA,8BACdxF,GAC2B,mBAApBkF,IACHA,IACAA,EAAgBO,KACpBxF;MAIA,OAAO,EACL;QACEnqB,MAAM;QACNkJ,aAAa;QACb+mB,SAAS,EACP;UACEjwB,MAAM;UACNkJ,aACE;;QAGNgnB,aAAY;YAEXxE;;MAGL,OAAOA;;AACT;EAGF8B,EAAM2C,yBAAyB,CAACjG,GAAkBZ;IAChD,IAAM8G,IC1KH,SAASC,oBACdnG,GACAZ,GACA1pB,GACAuqB;MAEA,IAAMC,IAAmBD,EAAKxC,OAAO0C,6BAA4B;MACjE,IAAMC,IAAcH,EAAKI,gBAAgBC,cAAcC;MAEvD,IAAMziB,IAAS0iB,EAAAA,UAAUP,GAAMD;MAC/B,KAAKliB;QAAQ;;MAEb,IAAI8T,IAAO6O,EAAAA,SAAS3iB,GAAQshB;MAC5B,KAAKxN;QAAM;;MAEXA,IAAOsO,IACHQ,EAAsBA,uBAAC9O,KACvB+O,EAAAA,iBAAiB/O;MAErB,IAAIK,GAAQ3a,GAAMspB;MAClB,IAAIV,KAAoBW,EAAAA,cAAqBjP,GAAMwO,IAAc;QAC/D,IAAMA,IAAcH,EAAKI,gBAAgBC,cAAcC;QACvD,IAAMO,IAAasF,EAAAA,cAAcxU,GAAMwO;QAEvCQ,IACEE,KAAcprB,EAAOqrB,MAAMD,KACvBprB,EAAOqrB,MAAMD,IAAaprB,SAC1BA,EAAOsmB,SAAStmB;QAEtB,IAAMiqB,IAAaT,SAAStN,EAAKpT,UAAU,IAAI4gB;QAC/C,KAAKwB,MAAgBjB;UAAY;;QAEjCroB,IAAOsa,EAAKpT,UAAU,GAAGgN;QACzByG,IAAS,IAAI2M,OAAOe,EAAW7lB,MAAM6lB,EAAWllB,QAAQ;AACzD,aAAM,KAAKylB,KAAoBW,EAAmBM,aAACvP,IAAO;QACzD,IAAM+N,IAAaT,SAAStN,EAAKuN,UAAUC;QAC3C,KAAKO,MAAejqB,EAAOsmB;UAAS;;QAEpC,KAAMoF,cAAEA,GAAYC,eAAEA,KAAkBC,kBACtC1P,GACAoO,GACAC;QAGF,IAAMsB,IAAgBF,EACnB9oB,QACC0oB,KACEA,EAAEO,SAAS/mB,QAAQ2kB,KACnB6B,EAAEO,SAAS/mB,QAAQwmB,EAAEO,SAAS9qB,SAAS0oB,IAE1CqC,QAAO,CAACC,GAAKC,MAASD,KAAOC,EAAKzF,QAAQ,KAAI;QAEjDyD,EAAW7lB,OAAO6lB,EAAW7lB,OAAOynB;QACpCjqB,IAAO8pB;QACPnP,IAAS,IAAI2M,OAAOe,EAAW7lB,MAAM6lB,EAAWllB,QAAQ;QACxDmmB,IAAclrB,EAAOsmB,QAAQtmB;AAC/B;QACE;;MAGF,IAAM2wB,IAAY7I,oBAAoBoD,GAAatpB,GAAM2a;MAEzD,OAAO;QACLzc,MAAM6pB,EAAAA,GAAGsD,kBAAkB5qB;QAC3BuuB,UAAU;UACR7rB,OAAO2kB;UACP1oB,QAAQ;;QAEVmsB,eAAe;QACfrc,eAAevP,MAAMyI,QAAQ2mB,KACzBA,EAAU1uB,KAAI4uB,MAAS;UAAE/wB,MAAM;UAAQ8B,MAAMivB;eAC7C,EAAC;UAAE/wB,MAAM;UAAQ8B,MAAM+uB;;;AAE/B,KDiGsBF,CAChBnG,GACAZ,GACA1pB,GACAuqB;IAGF,IAAIiG;MAAW,OAAOA;;IAEtB,OAAOjG,EAAKI,gBAAgB4F,uBAC1BjG,GACAZ;AACD;EAGH/qB,OAAO,YAAY6uB,KAAKC,UAAUG;EAElC,OAAOA;AACT;;iBAE4CjE;EAC1CmH,EAAcC,KAACpH;EACf,OAAO;IAAExlB;;AAAQ", "x_google_ignoreList": [1, 2, 3, 4, 5, 6]}