{"version": 3, "file": "api-chunk.mjs", "sources": ["../../src/ts/index.js", "../../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/parser/CharacterStream.js", "../../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/parser/RuleHelpers.js", "../../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/parser/Rules.js", "../../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/parser/onlineParser.js", "../../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/utils/Range.js", "../../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/utils/validateWithCustomRules.js", "../../../../node_modules/.pnpm/graphql-language-service@5.2.0_graphql@16.8.1/node_modules/graphql-language-service/esm/interface/getDiagnostics.js", "../../../../node_modules/.pnpm/@0no-co+graphql.web@1.0.4_graphql@16.8.1/node_modules/@0no-co/graphql.web/dist/graphql.web.mjs", "../../src/ast/templates.ts", "../../src/ast/checks.ts", "../../src/ast/declaration.ts", "../../src/ast/resolve.ts", "../../src/ast/index.ts", "../../../../node_modules/.pnpm/lru-cache@10.0.1/node_modules/lru-cache/dist/mjs/index.js", "../../../../node_modules/.pnpm/@sindresorhus+fnv1a@2.0.1/node_modules/@sindresorhus/fnv1a/index.js", "../../src/fieldUsage.ts", "../../src/checkImports.ts", "../../src/persisted.ts", "../../src/diagnostics.ts"], "sourcesContent": ["export var ts;\nexport function init(modules) {\n  ts = modules.typescript;\n}\n", "export default class CharacterStream {\n    constructor(sourceText) {\n        this._start = 0;\n        this._pos = 0;\n        this.getStartOfToken = () => this._start;\n        this.getCurrentPosition = () => this._pos;\n        this.eol = () => this._sourceText.length === this._pos;\n        this.sol = () => this._pos === 0;\n        this.peek = () => {\n            return this._sourceText.charAt(this._pos) || null;\n        };\n        this.next = () => {\n            const char = this._sourceText.charAt(this._pos);\n            this._pos++;\n            return char;\n        };\n        this.eat = (pattern) => {\n            const isMatched = this._testNextCharacter(pattern);\n            if (isMatched) {\n                this._start = this._pos;\n                this._pos++;\n                return this._sourceText.charAt(this._pos - 1);\n            }\n            return undefined;\n        };\n        this.eatWhile = (match) => {\n            let isMatched = this._testNextCharacter(match);\n            let didEat = false;\n            if (isMatched) {\n                didEat = isMatched;\n                this._start = this._pos;\n            }\n            while (isMatched) {\n                this._pos++;\n                isMatched = this._testNextCharacter(match);\n                didEat = true;\n            }\n            return didEat;\n        };\n        this.eatSpace = () => this.eatWhile(/[\\s\\u00a0]/);\n        this.skipToEnd = () => {\n            this._pos = this._sourceText.length;\n        };\n        this.skipTo = (position) => {\n            this._pos = position;\n        };\n        this.match = (pattern, consume = true, caseFold = false) => {\n            let token = null;\n            let match = null;\n            if (typeof pattern === 'string') {\n                const regex = new RegExp(pattern, caseFold ? 'i' : 'g');\n                match = regex.test(this._sourceText.slice(this._pos, this._pos + pattern.length));\n                token = pattern;\n            }\n            else if (pattern instanceof RegExp) {\n                match = this._sourceText.slice(this._pos).match(pattern);\n                token = match === null || match === void 0 ? void 0 : match[0];\n            }\n            if (match != null &&\n                (typeof pattern === 'string' ||\n                    (match instanceof Array &&\n                        this._sourceText.startsWith(match[0], this._pos)))) {\n                if (consume) {\n                    this._start = this._pos;\n                    if (token && token.length) {\n                        this._pos += token.length;\n                    }\n                }\n                return match;\n            }\n            return false;\n        };\n        this.backUp = (num) => {\n            this._pos -= num;\n        };\n        this.column = () => this._pos;\n        this.indentation = () => {\n            const match = this._sourceText.match(/\\s*/);\n            let indent = 0;\n            if (match && match.length !== 0) {\n                const whiteSpaces = match[0];\n                let pos = 0;\n                while (whiteSpaces.length > pos) {\n                    if (whiteSpaces.charCodeAt(pos) === 9) {\n                        indent += 2;\n                    }\n                    else {\n                        indent++;\n                    }\n                    pos++;\n                }\n            }\n            return indent;\n        };\n        this.current = () => this._sourceText.slice(this._start, this._pos);\n        this._sourceText = sourceText;\n    }\n    _testNextCharacter(pattern) {\n        const character = this._sourceText.charAt(this._pos);\n        let isMatched = false;\n        if (typeof pattern === 'string') {\n            isMatched = character === pattern;\n        }\n        else {\n            isMatched =\n                pattern instanceof RegExp\n                    ? pattern.test(character)\n                    : pattern(character);\n        }\n        return isMatched;\n    }\n}\n//# sourceMappingURL=CharacterStream.js.map", "export function opt(ofRule) {\n    return { ofRule };\n}\nexport function list(ofRule, separator) {\n    return { ofRule, isList: true, separator };\n}\nexport function butNot(rule, exclusions) {\n    const ruleMatch = rule.match;\n    rule.match = token => {\n        let check = false;\n        if (ruleMatch) {\n            check = ruleMatch(token);\n        }\n        return (check &&\n            exclusions.every(exclusion => exclusion.match && !exclusion.match(token)));\n    };\n    return rule;\n}\nexport function t(kind, style) {\n    return { style, match: (token) => token.kind === kind };\n}\nexport function p(value, style) {\n    return {\n        style: style || 'punctuation',\n        match: (token) => token.kind === 'Punctuation' && token.value === value,\n    };\n}\n//# sourceMappingURL=RuleHelpers.js.map", "import { opt, list, butNot, t, p } from './RuleHelpers';\nimport { Kind } from 'graphql';\nexport const isIgnored = (ch) => ch === ' ' ||\n    ch === '\\t' ||\n    ch === ',' ||\n    ch === '\\n' ||\n    ch === '\\r' ||\n    ch === '\\uFEFF' ||\n    ch === '\\u00A0';\nexport const LexRules = {\n    Name: /^[_A-Za-z][_0-9A-Za-z]*/,\n    Punctuation: /^(?:!|\\$|\\(|\\)|\\.\\.\\.|:|=|&|@|\\[|]|\\{|\\||\\})/,\n    Number: /^-?(?:0|(?:[1-9][0-9]*))(?:\\.[0-9]*)?(?:[eE][+-]?[0-9]+)?/,\n    String: /^(?:\"\"\"(?:\\\\\"\"\"|[^\"]|\"[^\"]|\"\"[^\"])*(?:\"\"\")?|\"(?:[^\"\\\\]|\\\\(?:\"|\\/|\\\\|b|f|n|r|t|u[0-9a-fA-F]{4}))*\"?)/,\n    Comment: /^#.*/,\n};\nexport const ParseRules = {\n    Document: [list('Definition')],\n    Definition(token) {\n        switch (token.value) {\n            case '{':\n                return 'ShortQuery';\n            case 'query':\n                return 'Query';\n            case 'mutation':\n                return 'Mutation';\n            case 'subscription':\n                return 'Subscription';\n            case 'fragment':\n                return Kind.FRAGMENT_DEFINITION;\n            case 'schema':\n                return 'SchemaDef';\n            case 'scalar':\n                return 'ScalarDef';\n            case 'type':\n                return 'ObjectTypeDef';\n            case 'interface':\n                return 'InterfaceDef';\n            case 'union':\n                return 'UnionDef';\n            case 'enum':\n                return 'EnumDef';\n            case 'input':\n                return 'InputDef';\n            case 'extend':\n                return 'ExtendDef';\n            case 'directive':\n                return 'DirectiveDef';\n        }\n    },\n    ShortQuery: ['SelectionSet'],\n    Query: [\n        word('query'),\n        opt(name('def')),\n        opt('VariableDefinitions'),\n        list('Directive'),\n        'SelectionSet',\n    ],\n    Mutation: [\n        word('mutation'),\n        opt(name('def')),\n        opt('VariableDefinitions'),\n        list('Directive'),\n        'SelectionSet',\n    ],\n    Subscription: [\n        word('subscription'),\n        opt(name('def')),\n        opt('VariableDefinitions'),\n        list('Directive'),\n        'SelectionSet',\n    ],\n    VariableDefinitions: [p('('), list('VariableDefinition'), p(')')],\n    VariableDefinition: ['Variable', p(':'), 'Type', opt('DefaultValue')],\n    Variable: [p('$', 'variable'), name('variable')],\n    DefaultValue: [p('='), 'Value'],\n    SelectionSet: [p('{'), list('Selection'), p('}')],\n    Selection(token, stream) {\n        return token.value === '...'\n            ? stream.match(/[\\s\\u00a0,]*(on\\b|@|{)/, false)\n                ? 'InlineFragment'\n                : 'FragmentSpread'\n            : stream.match(/[\\s\\u00a0,]*:/, false)\n                ? 'AliasedField'\n                : 'Field';\n    },\n    AliasedField: [\n        name('property'),\n        p(':'),\n        name('qualifier'),\n        opt('Arguments'),\n        list('Directive'),\n        opt('SelectionSet'),\n    ],\n    Field: [\n        name('property'),\n        opt('Arguments'),\n        list('Directive'),\n        opt('SelectionSet'),\n    ],\n    Arguments: [p('('), list('Argument'), p(')')],\n    Argument: [name('attribute'), p(':'), 'Value'],\n    FragmentSpread: [p('...'), name('def'), list('Directive')],\n    InlineFragment: [\n        p('...'),\n        opt('TypeCondition'),\n        list('Directive'),\n        'SelectionSet',\n    ],\n    FragmentDefinition: [\n        word('fragment'),\n        opt(butNot(name('def'), [word('on')])),\n        'TypeCondition',\n        list('Directive'),\n        'SelectionSet',\n    ],\n    TypeCondition: [word('on'), 'NamedType'],\n    Value(token) {\n        switch (token.kind) {\n            case 'Number':\n                return 'NumberValue';\n            case 'String':\n                return 'StringValue';\n            case 'Punctuation':\n                switch (token.value) {\n                    case '[':\n                        return 'ListValue';\n                    case '{':\n                        return 'ObjectValue';\n                    case '$':\n                        return 'Variable';\n                    case '&':\n                        return 'NamedType';\n                }\n                return null;\n            case 'Name':\n                switch (token.value) {\n                    case 'true':\n                    case 'false':\n                        return 'BooleanValue';\n                }\n                if (token.value === 'null') {\n                    return 'NullValue';\n                }\n                return 'EnumValue';\n        }\n    },\n    NumberValue: [t('Number', 'number')],\n    StringValue: [\n        {\n            style: 'string',\n            match: (token) => token.kind === 'String',\n            update(state, token) {\n                if (token.value.startsWith('\"\"\"')) {\n                    state.inBlockstring = !token.value.slice(3).endsWith('\"\"\"');\n                }\n            },\n        },\n    ],\n    BooleanValue: [t('Name', 'builtin')],\n    NullValue: [t('Name', 'keyword')],\n    EnumValue: [name('string-2')],\n    ListValue: [p('['), list('Value'), p(']')],\n    ObjectValue: [p('{'), list('ObjectField'), p('}')],\n    ObjectField: [name('attribute'), p(':'), 'Value'],\n    Type(token) {\n        return token.value === '[' ? 'ListType' : 'NonNullType';\n    },\n    ListType: [p('['), 'Type', p(']'), opt(p('!'))],\n    NonNullType: ['NamedType', opt(p('!'))],\n    NamedType: [type('atom')],\n    Directive: [p('@', 'meta'), name('meta'), opt('Arguments')],\n    DirectiveDef: [\n        word('directive'),\n        p('@', 'meta'),\n        name('meta'),\n        opt('ArgumentsDef'),\n        word('on'),\n        list('DirectiveLocation', p('|')),\n    ],\n    InterfaceDef: [\n        word('interface'),\n        name('atom'),\n        opt('Implements'),\n        list('Directive'),\n        p('{'),\n        list('FieldDef'),\n        p('}'),\n    ],\n    Implements: [word('implements'), list('NamedType', p('&'))],\n    DirectiveLocation: [name('string-2')],\n    SchemaDef: [\n        word('schema'),\n        list('Directive'),\n        p('{'),\n        list('OperationTypeDef'),\n        p('}'),\n    ],\n    OperationTypeDef: [name('keyword'), p(':'), name('atom')],\n    ScalarDef: [word('scalar'), name('atom'), list('Directive')],\n    ObjectTypeDef: [\n        word('type'),\n        name('atom'),\n        opt('Implements'),\n        list('Directive'),\n        p('{'),\n        list('FieldDef'),\n        p('}'),\n    ],\n    FieldDef: [\n        name('property'),\n        opt('ArgumentsDef'),\n        p(':'),\n        'Type',\n        list('Directive'),\n    ],\n    ArgumentsDef: [p('('), list('InputValueDef'), p(')')],\n    InputValueDef: [\n        name('attribute'),\n        p(':'),\n        'Type',\n        opt('DefaultValue'),\n        list('Directive'),\n    ],\n    UnionDef: [\n        word('union'),\n        name('atom'),\n        list('Directive'),\n        p('='),\n        list('UnionMember', p('|')),\n    ],\n    UnionMember: ['NamedType'],\n    EnumDef: [\n        word('enum'),\n        name('atom'),\n        list('Directive'),\n        p('{'),\n        list('EnumValueDef'),\n        p('}'),\n    ],\n    EnumValueDef: [name('string-2'), list('Directive')],\n    InputDef: [\n        word('input'),\n        name('atom'),\n        list('Directive'),\n        p('{'),\n        list('InputValueDef'),\n        p('}'),\n    ],\n    ExtendDef: [word('extend'), 'ExtensionDefinition'],\n    ExtensionDefinition(token) {\n        switch (token.value) {\n            case 'schema':\n                return Kind.SCHEMA_EXTENSION;\n            case 'scalar':\n                return Kind.SCALAR_TYPE_EXTENSION;\n            case 'type':\n                return Kind.OBJECT_TYPE_EXTENSION;\n            case 'interface':\n                return Kind.INTERFACE_TYPE_EXTENSION;\n            case 'union':\n                return Kind.UNION_TYPE_EXTENSION;\n            case 'enum':\n                return Kind.ENUM_TYPE_EXTENSION;\n            case 'input':\n                return Kind.INPUT_OBJECT_TYPE_EXTENSION;\n        }\n    },\n    [Kind.SCHEMA_EXTENSION]: ['SchemaDef'],\n    [Kind.SCALAR_TYPE_EXTENSION]: ['ScalarDef'],\n    [Kind.OBJECT_TYPE_EXTENSION]: ['ObjectTypeDef'],\n    [Kind.INTERFACE_TYPE_EXTENSION]: ['InterfaceDef'],\n    [Kind.UNION_TYPE_EXTENSION]: ['UnionDef'],\n    [Kind.ENUM_TYPE_EXTENSION]: ['EnumDef'],\n    [Kind.INPUT_OBJECT_TYPE_EXTENSION]: ['InputDef'],\n};\nfunction word(value) {\n    return {\n        style: 'keyword',\n        match: (token) => token.kind === 'Name' && token.value === value,\n    };\n}\nfunction name(style) {\n    return {\n        style,\n        match: (token) => token.kind === 'Name',\n        update(state, token) {\n            state.name = token.value;\n        },\n    };\n}\nfunction type(style) {\n    return {\n        style,\n        match: (token) => token.kind === 'Name',\n        update(state, token) {\n            var _a;\n            if ((_a = state.prevState) === null || _a === void 0 ? void 0 : _a.prevState) {\n                state.name = token.value;\n                state.prevState.prevState.type = token.value;\n            }\n        },\n    };\n}\n//# sourceMappingURL=Rules.js.map", "import { LexRules, ParseRules, isIgnored } from './Rules';\nimport { Kind } from 'graphql';\nexport default function onlineParser(options = {\n    eatWhitespace: stream => stream.eatWhile(isIgnored),\n    lexRules: LexRules,\n    parseRules: ParseRules,\n    editorConfig: {},\n}) {\n    return {\n        startState() {\n            const initialState = {\n                level: 0,\n                step: 0,\n                name: null,\n                kind: null,\n                type: null,\n                rule: null,\n                needsSeparator: false,\n                prevState: null,\n            };\n            pushRule(options.parseRules, initialState, Kind.DOCUMENT);\n            return initialState;\n        },\n        token(stream, state) {\n            return getToken(stream, state, options);\n        },\n    };\n}\nfunction getToken(stream, state, options) {\n    var _a;\n    if (state.inBlockstring) {\n        if (stream.match(/.*\"\"\"/)) {\n            state.inBlockstring = false;\n            return 'string';\n        }\n        stream.skipToEnd();\n        return 'string';\n    }\n    const { lexRules, parseRules, eatWhitespace, editorConfig } = options;\n    if (state.rule && state.rule.length === 0) {\n        popRule(state);\n    }\n    else if (state.needsAdvance) {\n        state.needsAdvance = false;\n        advanceRule(state, true);\n    }\n    if (stream.sol()) {\n        const tabSize = (editorConfig === null || editorConfig === void 0 ? void 0 : editorConfig.tabSize) || 2;\n        state.indentLevel = Math.floor(stream.indentation() / tabSize);\n    }\n    if (eatWhitespace(stream)) {\n        return 'ws';\n    }\n    const token = lex(lexRules, stream);\n    if (!token) {\n        const matchedSomething = stream.match(/\\S+/);\n        if (!matchedSomething) {\n            stream.match(/\\s/);\n        }\n        pushRule(SpecialParseRules, state, 'Invalid');\n        return 'invalidchar';\n    }\n    if (token.kind === 'Comment') {\n        pushRule(SpecialParseRules, state, 'Comment');\n        return 'comment';\n    }\n    const backupState = assign({}, state);\n    if (token.kind === 'Punctuation') {\n        if (/^[{([]/.test(token.value)) {\n            if (state.indentLevel !== undefined) {\n                state.levels = (state.levels || []).concat(state.indentLevel + 1);\n            }\n        }\n        else if (/^[})\\]]/.test(token.value)) {\n            const levels = (state.levels = (state.levels || []).slice(0, -1));\n            if (state.indentLevel &&\n                levels.length > 0 &&\n                levels.at(-1) < state.indentLevel) {\n                state.indentLevel = levels.at(-1);\n            }\n        }\n    }\n    while (state.rule) {\n        let expected = typeof state.rule === 'function'\n            ? state.step === 0\n                ? state.rule(token, stream)\n                : null\n            : state.rule[state.step];\n        if (state.needsSeparator) {\n            expected = expected === null || expected === void 0 ? void 0 : expected.separator;\n        }\n        if (expected) {\n            if (expected.ofRule) {\n                expected = expected.ofRule;\n            }\n            if (typeof expected === 'string') {\n                pushRule(parseRules, state, expected);\n                continue;\n            }\n            if ((_a = expected.match) === null || _a === void 0 ? void 0 : _a.call(expected, token)) {\n                if (expected.update) {\n                    expected.update(state, token);\n                }\n                if (token.kind === 'Punctuation') {\n                    advanceRule(state, true);\n                }\n                else {\n                    state.needsAdvance = true;\n                }\n                return expected.style;\n            }\n        }\n        unsuccessful(state);\n    }\n    assign(state, backupState);\n    pushRule(SpecialParseRules, state, 'Invalid');\n    return 'invalidchar';\n}\nfunction assign(to, from) {\n    const keys = Object.keys(from);\n    for (let i = 0; i < keys.length; i++) {\n        to[keys[i]] = from[keys[i]];\n    }\n    return to;\n}\nconst SpecialParseRules = {\n    Invalid: [],\n    Comment: [],\n};\nfunction pushRule(rules, state, ruleKind) {\n    if (!rules[ruleKind]) {\n        throw new TypeError('Unknown rule: ' + ruleKind);\n    }\n    state.prevState = Object.assign({}, state);\n    state.kind = ruleKind;\n    state.name = null;\n    state.type = null;\n    state.rule = rules[ruleKind];\n    state.step = 0;\n    state.needsSeparator = false;\n}\nfunction popRule(state) {\n    if (!state.prevState) {\n        return;\n    }\n    state.kind = state.prevState.kind;\n    state.name = state.prevState.name;\n    state.type = state.prevState.type;\n    state.rule = state.prevState.rule;\n    state.step = state.prevState.step;\n    state.needsSeparator = state.prevState.needsSeparator;\n    state.prevState = state.prevState.prevState;\n}\nfunction advanceRule(state, successful) {\n    var _a;\n    if (isList(state) && state.rule) {\n        const step = state.rule[state.step];\n        if (step.separator) {\n            const { separator } = step;\n            state.needsSeparator = !state.needsSeparator;\n            if (!state.needsSeparator && separator.ofRule) {\n                return;\n            }\n        }\n        if (successful) {\n            return;\n        }\n    }\n    state.needsSeparator = false;\n    state.step++;\n    while (state.rule &&\n        !(Array.isArray(state.rule) && state.step < state.rule.length)) {\n        popRule(state);\n        if (state.rule) {\n            if (isList(state)) {\n                if ((_a = state.rule) === null || _a === void 0 ? void 0 : _a[state.step].separator) {\n                    state.needsSeparator = !state.needsSeparator;\n                }\n            }\n            else {\n                state.needsSeparator = false;\n                state.step++;\n            }\n        }\n    }\n}\nfunction isList(state) {\n    const step = Array.isArray(state.rule) &&\n        typeof state.rule[state.step] !== 'string' &&\n        state.rule[state.step];\n    return step && step.isList;\n}\nfunction unsuccessful(state) {\n    while (state.rule &&\n        !(Array.isArray(state.rule) && state.rule[state.step].ofRule)) {\n        popRule(state);\n    }\n    if (state.rule) {\n        advanceRule(state, false);\n    }\n}\nfunction lex(lexRules, stream) {\n    const kinds = Object.keys(lexRules);\n    for (let i = 0; i < kinds.length; i++) {\n        const match = stream.match(lexRules[kinds[i]]);\n        if (match && match instanceof Array) {\n            return { kind: kinds[i], value: match[0] };\n        }\n    }\n}\n//# sourceMappingURL=onlineParser.js.map", "export class Range {\n    constructor(start, end) {\n        this.containsPosition = (position) => {\n            if (this.start.line === position.line) {\n                return this.start.character <= position.character;\n            }\n            if (this.end.line === position.line) {\n                return this.end.character >= position.character;\n            }\n            return this.start.line <= position.line && this.end.line >= position.line;\n        };\n        this.start = start;\n        this.end = end;\n    }\n    setStart(line, character) {\n        this.start = new Position(line, character);\n    }\n    setEnd(line, character) {\n        this.end = new Position(line, character);\n    }\n}\nexport class Position {\n    constructor(line, character) {\n        this.lessThanOrEqualTo = (position) => this.line < position.line ||\n            (this.line === position.line && this.character <= position.character);\n        this.line = line;\n        this.character = character;\n    }\n    setLine(line) {\n        this.line = line;\n    }\n    setCharacter(character) {\n        this.character = character;\n    }\n}\nexport function offsetToPosition(text, loc) {\n    const EOL = '\\n';\n    const buf = text.slice(0, loc);\n    const lines = buf.split(EOL).length - 1;\n    const lastLineIndex = buf.lastIndexOf(EOL);\n    return new Position(lines, loc - lastLineIndex - 1);\n}\nexport function locToRange(text, loc) {\n    const start = offsetToPosition(text, loc.start);\n    const end = offsetToPosition(text, loc.end);\n    return new Range(start, end);\n}\n//# sourceMappingURL=Range.js.map", "import { specifiedRules, validate, NoUnusedFragmentsRule, KnownFragmentNamesRule, Kind, ExecutableDefinitionsRule, LoneSchemaDefinitionRule, UniqueOperationTypesRule, UniqueTypeNamesRule, UniqueEnumValueNamesRule, UniqueFieldDefinitionNamesRule, UniqueDirectiveNamesRule, KnownTypeNamesRule, KnownDirectivesRule, UniqueDirectivesPerLocationRule, PossibleTypeExtensionsRule, UniqueArgumentNamesRule, UniqueInputFieldNamesRule, } from 'graphql';\nconst specifiedSDLRules = [\n    LoneSchemaDefinitionRule,\n    UniqueOperationTypesRule,\n    UniqueTypeNamesRule,\n    UniqueEnumValueNamesRule,\n    UniqueFieldDefinitionNamesRule,\n    UniqueDirectiveNamesRule,\n    KnownTypeNamesRule,\n    KnownDirectivesRule,\n    UniqueDirectivesPerLocationRule,\n    PossibleTypeExtensionsRule,\n    UniqueArgumentNamesRule,\n    UniqueInputFieldNamesRule,\n];\nexport function validateWithCustomRules(schema, ast, customRules, isRelayCompatMode, isSchemaDocument) {\n    const rules = specifiedRules.filter(rule => {\n        if (rule === NoUnusedFragmentsRule || rule === ExecutableDefinitionsRule) {\n            return false;\n        }\n        if (isRelayCompatMode && rule === KnownFragmentNamesRule) {\n            return false;\n        }\n        return true;\n    });\n    if (customRules) {\n        Array.prototype.push.apply(rules, customRules);\n    }\n    if (isSchemaDocument) {\n        Array.prototype.push.apply(rules, specifiedSDLRules);\n    }\n    const errors = validate(schema, ast, rules);\n    return errors.filter(error => {\n        if (error.message.includes('Unknown directive') && error.nodes) {\n            const node = error.nodes[0];\n            if (node && node.kind === Kind.DIRECTIVE) {\n                const name = node.name.value;\n                if (name === 'arguments' || name === 'argumentDefinitions') {\n                    return false;\n                }\n            }\n        }\n        return true;\n    });\n}\n//# sourceMappingURL=validateWithCustomRules.js.map", "import { GraphQLError, print, validate, NoDeprecatedCustomRule, parse, } from 'graphql';\nimport { CharacterStream, onlineParser } from '../parser';\nimport { Range, validateWithCustomRules, Position } from '../utils';\nexport const SEVERITY = {\n    Error: 'Error',\n    Warning: 'Warning',\n    Information: 'Information',\n    Hint: 'Hint',\n};\nexport const DIAGNOSTIC_SEVERITY = {\n    [SEVERITY.Error]: 1,\n    [SEVERITY.Warning]: 2,\n    [SEVERITY.Information]: 3,\n    [SEVERITY.Hint]: 4,\n};\nconst invariant = (condition, message) => {\n    if (!condition) {\n        throw new Error(message);\n    }\n};\nexport function getDiagnostics(query, schema = null, customRules, isRelayCompatMode, externalFragments) {\n    var _a, _b;\n    let ast = null;\n    let fragments = '';\n    if (externalFragments) {\n        fragments =\n            typeof externalFragments === 'string'\n                ? externalFragments\n                : externalFragments.reduce((acc, node) => acc + print(node) + '\\n\\n', '');\n    }\n    const enhancedQuery = fragments ? `${query}\\n\\n${fragments}` : query;\n    try {\n        ast = parse(enhancedQuery);\n    }\n    catch (error) {\n        if (error instanceof GraphQLError) {\n            const range = getRange((_b = (_a = error.locations) === null || _a === void 0 ? void 0 : _a[0]) !== null && _b !== void 0 ? _b : { line: 0, column: 0 }, enhancedQuery);\n            return [\n                {\n                    severity: DIAGNOSTIC_SEVERITY.Error,\n                    message: error.message,\n                    source: 'GraphQL: Syntax',\n                    range,\n                },\n            ];\n        }\n        throw error;\n    }\n    return validateQuery(ast, schema, customRules, isRelayCompatMode);\n}\nexport function validateQuery(ast, schema = null, customRules, isRelayCompatMode) {\n    if (!schema) {\n        return [];\n    }\n    const validationErrorAnnotations = validateWithCustomRules(schema, ast, customRules, isRelayCompatMode).flatMap(error => annotations(error, DIAGNOSTIC_SEVERITY.Error, 'Validation'));\n    const deprecationWarningAnnotations = validate(schema, ast, [\n        NoDeprecatedCustomRule,\n    ]).flatMap(error => annotations(error, DIAGNOSTIC_SEVERITY.Warning, 'Deprecation'));\n    return validationErrorAnnotations.concat(deprecationWarningAnnotations);\n}\nfunction annotations(error, severity, type) {\n    if (!error.nodes) {\n        return [];\n    }\n    const highlightedNodes = [];\n    for (const [i, node] of error.nodes.entries()) {\n        const highlightNode = node.kind !== 'Variable' && 'name' in node && node.name !== undefined\n            ? node.name\n            : 'variable' in node && node.variable !== undefined\n                ? node.variable\n                : node;\n        if (highlightNode) {\n            invariant(error.locations, 'GraphQL validation error requires locations.');\n            const loc = error.locations[i];\n            const highlightLoc = getLocation(highlightNode);\n            const end = loc.column + (highlightLoc.end - highlightLoc.start);\n            highlightedNodes.push({\n                source: `GraphQL: ${type}`,\n                message: error.message,\n                severity,\n                range: new Range(new Position(loc.line - 1, loc.column - 1), new Position(loc.line - 1, end)),\n            });\n        }\n    }\n    return highlightedNodes;\n}\nexport function getRange(location, queryText) {\n    const parser = onlineParser();\n    const state = parser.startState();\n    const lines = queryText.split('\\n');\n    invariant(lines.length >= location.line, 'Query text must have more lines than where the error happened');\n    let stream = null;\n    for (let i = 0; i < location.line; i++) {\n        stream = new CharacterStream(lines[i]);\n        while (!stream.eol()) {\n            const style = parser.token(stream, state);\n            if (style === 'invalidchar') {\n                break;\n            }\n        }\n    }\n    invariant(stream, 'Expected Parser stream to be available.');\n    const line = location.line - 1;\n    const start = stream.getStartOfToken();\n    const end = stream.getCurrentPosition();\n    return new Range(new Position(line, start), new Position(line, end));\n}\nfunction getLocation(node) {\n    const typeCastedNode = node;\n    const location = typeCastedNode.loc;\n    invariant(location, 'Expected ASTNode to have a location.');\n    return location;\n}\n//# sourceMappingURL=getDiagnostics.js.map", "var e = {\n  NAME: \"Name\",\n  DOCUMENT: \"Document\",\n  OPERATION_DEFINITION: \"OperationDefinition\",\n  VARIABLE_DEFINITION: \"VariableDefinition\",\n  SELECTION_SET: \"SelectionSet\",\n  FIELD: \"Field\",\n  ARGUMENT: \"Argument\",\n  FRAGMENT_SPREAD: \"FragmentSpread\",\n  INLINE_FRAGMENT: \"InlineFragment\",\n  FRAGMENT_DEFINITION: \"FragmentDefinition\",\n  VARIABLE: \"Variable\",\n  INT: \"IntValue\",\n  FLOAT: \"FloatValue\",\n  STRING: \"StringValue\",\n  BOOLEAN: \"BooleanValue\",\n  NULL: \"NullValue\",\n  ENUM: \"EnumValue\",\n  LIST: \"ListValue\",\n  OBJECT: \"ObjectValue\",\n  OBJECT_FIELD: \"ObjectField\",\n  DIRECTIVE: \"Directive\",\n  NAMED_TYPE: \"NamedType\",\n  LIST_TYPE: \"ListType\",\n  NON_NULL_TYPE: \"NonNullType\"\n};\n\nvar r = {\n  QUERY: \"query\",\n  MUTATION: \"mutation\",\n  SUBSCRIPTION: \"subscription\"\n};\n\nclass GraphQLError extends Error {\n  constructor(e, r, i, n, a, t, o) {\n    super(e);\n    this.name = \"GraphQLError\";\n    this.message = e;\n    if (a) {\n      this.path = a;\n    }\n    if (r) {\n      this.nodes = Array.isArray(r) ? r : [ r ];\n    }\n    if (i) {\n      this.source = i;\n    }\n    if (n) {\n      this.positions = n;\n    }\n    if (t) {\n      this.originalError = t;\n    }\n    var l = o;\n    if (!l && t) {\n      var u = t.extensions;\n      if (u && \"object\" == typeof u) {\n        l = u;\n      }\n    }\n    this.extensions = l || {};\n  }\n  toJSON() {\n    return {\n      ...this,\n      message: this.message\n    };\n  }\n  toString() {\n    return this.message;\n  }\n  get [Symbol.toStringTag]() {\n    return \"GraphQLError\";\n  }\n}\n\nvar i;\n\nvar n;\n\nfunction error(e) {\n  return new GraphQLError(`Syntax Error: Unexpected token at ${n} in ${e}`);\n}\n\nfunction advance(e) {\n  e.lastIndex = n;\n  if (e.test(i)) {\n    return i.slice(n, n = e.lastIndex);\n  }\n}\n\nvar a = / +(?=[^\\s])/y;\n\nfunction blockString(e) {\n  var r = e.split(\"\\n\");\n  var i = \"\";\n  var n = 0;\n  var t = 0;\n  var o = r.length - 1;\n  for (var l = 0; l < r.length; l++) {\n    a.lastIndex = 0;\n    if (a.test(r[l])) {\n      if (l && (!n || a.lastIndex < n)) {\n        n = a.lastIndex;\n      }\n      t = t || l;\n      o = l;\n    }\n  }\n  for (var u = t; u <= o; u++) {\n    if (u !== t) {\n      i += \"\\n\";\n    }\n    i += r[u].slice(n).replace(/\\\\\"\"\"/g, '\"\"\"');\n  }\n  return i;\n}\n\nfunction ignored() {\n  for (var e = 0 | i.charCodeAt(n++); 9 === e || 10 === e || 13 === e || 32 === e || 35 === e || 44 === e || 65279 === e; e = 0 | i.charCodeAt(n++)) {\n    if (35 === e) {\n      while (10 !== (e = i.charCodeAt(n++)) && 13 !== e) {}\n    }\n  }\n  n--;\n}\n\nvar t = /[_A-Za-z]\\w*/y;\n\nfunction name() {\n  var e;\n  if (e = advance(t)) {\n    return {\n      kind: \"Name\",\n      value: e\n    };\n  }\n}\n\nvar o = /(?:null|true|false)/y;\n\nvar l = /\\$[_A-Za-z]\\w*/y;\n\nvar u = /-?\\d+/y;\n\nvar v = /(?:\\.\\d+)?[eE][+-]?\\d+|\\.\\d+/y;\n\nvar d = /\\\\/g;\n\nvar s = /\"\"\"(?:\"\"\"|(?:[\\s\\S]*?[^\\\\])\"\"\")/y;\n\nvar c = /\"(?:\"|[^\\r\\n]*?[^\\\\]\")/y;\n\nfunction value(e) {\n  var r;\n  var a;\n  if (a = advance(o)) {\n    r = \"null\" === a ? {\n      kind: \"NullValue\"\n    } : {\n      kind: \"BooleanValue\",\n      value: \"true\" === a\n    };\n  } else if (!e && (a = advance(l))) {\n    r = {\n      kind: \"Variable\",\n      name: {\n        kind: \"Name\",\n        value: a.slice(1)\n      }\n    };\n  } else if (a = advance(u)) {\n    var f = a;\n    if (a = advance(v)) {\n      r = {\n        kind: \"FloatValue\",\n        value: f + a\n      };\n    } else {\n      r = {\n        kind: \"IntValue\",\n        value: f\n      };\n    }\n  } else if (a = advance(t)) {\n    r = {\n      kind: \"EnumValue\",\n      value: a\n    };\n  } else if (a = advance(s)) {\n    r = {\n      kind: \"StringValue\",\n      value: blockString(a.slice(3, -3)),\n      block: !0\n    };\n  } else if (a = advance(c)) {\n    r = {\n      kind: \"StringValue\",\n      value: d.test(a) ? JSON.parse(a) : a.slice(1, -1),\n      block: !1\n    };\n  } else if (r = function list(e) {\n    var r;\n    if (91 === i.charCodeAt(n)) {\n      n++;\n      ignored();\n      var a = [];\n      while (r = value(e)) {\n        a.push(r);\n      }\n      if (93 !== i.charCodeAt(n++)) {\n        throw error(\"ListValue\");\n      }\n      ignored();\n      return {\n        kind: \"ListValue\",\n        values: a\n      };\n    }\n  }(e) || function object(e) {\n    if (123 === i.charCodeAt(n)) {\n      n++;\n      ignored();\n      var r = [];\n      var a;\n      while (a = name()) {\n        ignored();\n        if (58 !== i.charCodeAt(n++)) {\n          throw error(\"ObjectField\");\n        }\n        ignored();\n        var t = value(e);\n        if (!t) {\n          throw error(\"ObjectField\");\n        }\n        r.push({\n          kind: \"ObjectField\",\n          name: a,\n          value: t\n        });\n      }\n      if (125 !== i.charCodeAt(n++)) {\n        throw error(\"ObjectValue\");\n      }\n      ignored();\n      return {\n        kind: \"ObjectValue\",\n        fields: r\n      };\n    }\n  }(e)) {\n    return r;\n  }\n  ignored();\n  return r;\n}\n\nfunction arguments_(e) {\n  var r = [];\n  ignored();\n  if (40 === i.charCodeAt(n)) {\n    n++;\n    ignored();\n    var a;\n    while (a = name()) {\n      ignored();\n      if (58 !== i.charCodeAt(n++)) {\n        throw error(\"Argument\");\n      }\n      ignored();\n      var t = value(e);\n      if (!t) {\n        throw error(\"Argument\");\n      }\n      r.push({\n        kind: \"Argument\",\n        name: a,\n        value: t\n      });\n    }\n    if (!r.length || 41 !== i.charCodeAt(n++)) {\n      throw error(\"Argument\");\n    }\n    ignored();\n  }\n  return r;\n}\n\nfunction directives(e) {\n  var r = [];\n  ignored();\n  while (64 === i.charCodeAt(n)) {\n    n++;\n    var a = name();\n    if (!a) {\n      throw error(\"Directive\");\n    }\n    ignored();\n    r.push({\n      kind: \"Directive\",\n      name: a,\n      arguments: arguments_(e)\n    });\n  }\n  return r;\n}\n\nfunction field() {\n  var e = name();\n  if (e) {\n    ignored();\n    var r;\n    if (58 === i.charCodeAt(n)) {\n      n++;\n      ignored();\n      r = e;\n      if (!(e = name())) {\n        throw error(\"Field\");\n      }\n      ignored();\n    }\n    return {\n      kind: \"Field\",\n      alias: r,\n      name: e,\n      arguments: arguments_(!1),\n      directives: directives(!1),\n      selectionSet: selectionSet()\n    };\n  }\n}\n\nfunction type() {\n  var e;\n  ignored();\n  if (91 === i.charCodeAt(n)) {\n    n++;\n    ignored();\n    var r = type();\n    if (!r || 93 !== i.charCodeAt(n++)) {\n      throw error(\"ListType\");\n    }\n    e = {\n      kind: \"ListType\",\n      type: r\n    };\n  } else if (e = name()) {\n    e = {\n      kind: \"NamedType\",\n      name: e\n    };\n  } else {\n    throw error(\"NamedType\");\n  }\n  ignored();\n  if (33 === i.charCodeAt(n)) {\n    n++;\n    ignored();\n    return {\n      kind: \"NonNullType\",\n      type: e\n    };\n  } else {\n    return e;\n  }\n}\n\nvar f = /on/y;\n\nfunction typeCondition() {\n  if (advance(f)) {\n    ignored();\n    var e = name();\n    if (!e) {\n      throw error(\"NamedType\");\n    }\n    ignored();\n    return {\n      kind: \"NamedType\",\n      name: e\n    };\n  }\n}\n\nvar p = /\\.\\.\\./y;\n\nfunction fragmentSpread() {\n  if (advance(p)) {\n    ignored();\n    var e = n;\n    var r;\n    if ((r = name()) && \"on\" !== r.value) {\n      return {\n        kind: \"FragmentSpread\",\n        name: r,\n        directives: directives(!1)\n      };\n    } else {\n      n = e;\n      var i = typeCondition();\n      var a = directives(!1);\n      var t = selectionSet();\n      if (!t) {\n        throw error(\"InlineFragment\");\n      }\n      return {\n        kind: \"InlineFragment\",\n        typeCondition: i,\n        directives: a,\n        selectionSet: t\n      };\n    }\n  }\n}\n\nfunction selectionSet() {\n  var e;\n  ignored();\n  if (123 === i.charCodeAt(n)) {\n    n++;\n    ignored();\n    var r = [];\n    while (e = fragmentSpread() || field()) {\n      r.push(e);\n    }\n    if (!r.length || 125 !== i.charCodeAt(n++)) {\n      throw error(\"SelectionSet\");\n    }\n    ignored();\n    return {\n      kind: \"SelectionSet\",\n      selections: r\n    };\n  }\n}\n\nvar m = /fragment/y;\n\nfunction fragmentDefinition() {\n  if (advance(m)) {\n    ignored();\n    var e = name();\n    if (!e) {\n      throw error(\"FragmentDefinition\");\n    }\n    ignored();\n    var r = typeCondition();\n    if (!r) {\n      throw error(\"FragmentDefinition\");\n    }\n    var i = directives(!1);\n    var n = selectionSet();\n    if (!n) {\n      throw error(\"FragmentDefinition\");\n    }\n    return {\n      kind: \"FragmentDefinition\",\n      name: e,\n      typeCondition: r,\n      directives: i,\n      selectionSet: n\n    };\n  }\n}\n\nvar g = /(?:query|mutation|subscription)/y;\n\nfunction operationDefinition() {\n  var e;\n  var r;\n  var a = [];\n  var t = [];\n  if (e = advance(g)) {\n    ignored();\n    r = name();\n    a = function variableDefinitions() {\n      var e;\n      var r = [];\n      ignored();\n      if (40 === i.charCodeAt(n)) {\n        n++;\n        ignored();\n        while (e = advance(l)) {\n          ignored();\n          if (58 !== i.charCodeAt(n++)) {\n            throw error(\"VariableDefinition\");\n          }\n          var a = type();\n          var t = void 0;\n          if (61 === i.charCodeAt(n)) {\n            n++;\n            ignored();\n            if (!(t = value(!0))) {\n              throw error(\"VariableDefinition\");\n            }\n          }\n          ignored();\n          r.push({\n            kind: \"VariableDefinition\",\n            variable: {\n              kind: \"Variable\",\n              name: {\n                kind: \"Name\",\n                value: e.slice(1)\n              }\n            },\n            type: a,\n            defaultValue: t,\n            directives: directives(!0)\n          });\n        }\n        if (41 !== i.charCodeAt(n++)) {\n          throw error(\"VariableDefinition\");\n        }\n        ignored();\n      }\n      return r;\n    }();\n    t = directives(!1);\n  }\n  var o = selectionSet();\n  if (o) {\n    return {\n      kind: \"OperationDefinition\",\n      operation: e || \"query\",\n      name: r,\n      variableDefinitions: a,\n      directives: t,\n      selectionSet: o\n    };\n  }\n}\n\nfunction parse(e, r) {\n  i = \"string\" == typeof e.body ? e.body : e;\n  n = 0;\n  return function document() {\n    var e;\n    ignored();\n    var r = [];\n    while (e = fragmentDefinition() || operationDefinition()) {\n      r.push(e);\n    }\n    return {\n      kind: \"Document\",\n      definitions: r\n    };\n  }();\n}\n\nfunction parseValue(e, r) {\n  i = \"string\" == typeof e.body ? e.body : e;\n  n = 0;\n  ignored();\n  var a = value(!1);\n  if (!a) {\n    throw error(\"ValueNode\");\n  }\n  return a;\n}\n\nfunction parseType(e, r) {\n  i = \"string\" == typeof e.body ? e.body : e;\n  n = 0;\n  return type();\n}\n\nvar h = {};\n\nfunction visit(e, r) {\n  var i = [];\n  var n = [];\n  try {\n    var a = function traverse(e, a, t) {\n      var o = !1;\n      var l = r[e.kind] && r[e.kind].enter || r[e.kind] || r.enter;\n      var u = l && l.call(r, e, a, t, n, i);\n      if (!1 === u) {\n        return e;\n      } else if (null === u) {\n        return null;\n      } else if (u === h) {\n        throw h;\n      } else if (u && \"string\" == typeof u.kind) {\n        o = u !== e;\n        e = u;\n      }\n      if (t) {\n        i.push(t);\n      }\n      var v;\n      var d = {\n        ...e\n      };\n      for (var s in e) {\n        n.push(s);\n        var c = e[s];\n        if (Array.isArray(c)) {\n          var f = [];\n          for (var p = 0; p < c.length; p++) {\n            if (null != c[p] && \"string\" == typeof c[p].kind) {\n              i.push(e);\n              n.push(p);\n              v = traverse(c[p], p, c);\n              n.pop();\n              i.pop();\n              if (null == v) {\n                o = !0;\n              } else {\n                o = o || v !== c[p];\n                f.push(v);\n              }\n            }\n          }\n          c = f;\n        } else if (null != c && \"string\" == typeof c.kind) {\n          if (void 0 !== (v = traverse(c, s, e))) {\n            o = o || c !== v;\n            c = v;\n          }\n        }\n        n.pop();\n        if (o) {\n          d[s] = c;\n        }\n      }\n      if (t) {\n        i.pop();\n      }\n      var m = r[e.kind] && r[e.kind].leave || r.leave;\n      var g = m && m.call(r, e, a, t, n, i);\n      if (g === h) {\n        throw h;\n      } else if (void 0 !== g) {\n        return g;\n      } else if (void 0 !== u) {\n        return o ? d : u;\n      } else {\n        return o ? d : e;\n      }\n    }(e);\n    return void 0 !== a && !1 !== a ? a : e;\n  } catch (r) {\n    if (r !== h) {\n      throw r;\n    }\n    return e;\n  }\n}\n\nfunction printString(e) {\n  return JSON.stringify(e);\n}\n\nfunction printBlockString(e) {\n  return '\"\"\"\\n' + e.replace(/\"\"\"/g, '\\\\\"\"\"') + '\\n\"\"\"';\n}\n\nvar hasItems = e => !(!e || !e.length);\n\nvar y = {\n  OperationDefinition(e) {\n    if (\"query\" === e.operation && !e.name && !hasItems(e.variableDefinitions) && !hasItems(e.directives)) {\n      return y.SelectionSet(e.selectionSet);\n    }\n    var r = e.operation;\n    if (e.name) {\n      r += \" \" + e.name.value;\n    }\n    if (hasItems(e.variableDefinitions)) {\n      if (!e.name) {\n        r += \" \";\n      }\n      r += \"(\" + e.variableDefinitions.map(y.VariableDefinition).join(\", \") + \")\";\n    }\n    if (hasItems(e.directives)) {\n      r += \" \" + e.directives.map(y.Directive).join(\" \");\n    }\n    return r + \" \" + y.SelectionSet(e.selectionSet);\n  },\n  VariableDefinition(e) {\n    var r = y.Variable(e.variable) + \": \" + print(e.type);\n    if (e.defaultValue) {\n      r += \" = \" + print(e.defaultValue);\n    }\n    if (hasItems(e.directives)) {\n      r += \" \" + e.directives.map(y.Directive).join(\" \");\n    }\n    return r;\n  },\n  Field(e) {\n    var r = (e.alias ? e.alias.value + \": \" : \"\") + e.name.value;\n    if (hasItems(e.arguments)) {\n      var i = e.arguments.map(y.Argument);\n      var n = r + \"(\" + i.join(\", \") + \")\";\n      r = n.length > 80 ? r + \"(\\n  \" + i.join(\"\\n\").replace(/\\n/g, \"\\n  \") + \"\\n)\" : n;\n    }\n    if (hasItems(e.directives)) {\n      r += \" \" + e.directives.map(y.Directive).join(\" \");\n    }\n    return e.selectionSet ? r + \" \" + y.SelectionSet(e.selectionSet) : r;\n  },\n  StringValue: e => e.block ? printBlockString(e.value) : printString(e.value),\n  BooleanValue: e => \"\" + e.value,\n  NullValue: e => \"null\",\n  IntValue: e => e.value,\n  FloatValue: e => e.value,\n  EnumValue: e => e.value,\n  Name: e => e.value,\n  Variable: e => \"$\" + e.name.value,\n  ListValue: e => \"[\" + e.values.map(print).join(\", \") + \"]\",\n  ObjectValue: e => \"{\" + e.fields.map(y.ObjectField).join(\", \") + \"}\",\n  ObjectField: e => e.name.value + \": \" + print(e.value),\n  Document: e => hasItems(e.definitions) ? e.definitions.map(print).join(\"\\n\\n\") : \"\",\n  SelectionSet: e => \"{\\n  \" + e.selections.map(print).join(\"\\n\").replace(/\\n/g, \"\\n  \") + \"\\n}\",\n  Argument: e => e.name.value + \": \" + print(e.value),\n  FragmentSpread(e) {\n    var r = \"...\" + e.name.value;\n    if (hasItems(e.directives)) {\n      r += \" \" + e.directives.map(y.Directive).join(\" \");\n    }\n    return r;\n  },\n  InlineFragment(e) {\n    var r = \"...\";\n    if (e.typeCondition) {\n      r += \" on \" + e.typeCondition.name.value;\n    }\n    if (hasItems(e.directives)) {\n      r += \" \" + e.directives.map(y.Directive).join(\" \");\n    }\n    return r + \" \" + print(e.selectionSet);\n  },\n  FragmentDefinition(e) {\n    var r = \"fragment \" + e.name.value;\n    r += \" on \" + e.typeCondition.name.value;\n    if (hasItems(e.directives)) {\n      r += \" \" + e.directives.map(y.Directive).join(\" \");\n    }\n    return r + \" \" + print(e.selectionSet);\n  },\n  Directive(e) {\n    var r = \"@\" + e.name.value;\n    if (hasItems(e.arguments)) {\n      r += \"(\" + e.arguments.map(y.Argument).join(\", \") + \")\";\n    }\n    return r;\n  },\n  NamedType: e => e.name.value,\n  ListType: e => \"[\" + print(e.type) + \"]\",\n  NonNullType: e => print(e.type) + \"!\"\n};\n\nfunction print(e) {\n  return y[e.kind] ? y[e.kind](e) : \"\";\n}\n\nfunction valueFromASTUntyped(e, r) {\n  switch (e.kind) {\n   case \"NullValue\":\n    return null;\n\n   case \"IntValue\":\n    return parseInt(e.value, 10);\n\n   case \"FloatValue\":\n    return parseFloat(e.value);\n\n   case \"StringValue\":\n   case \"EnumValue\":\n   case \"BooleanValue\":\n    return e.value;\n\n   case \"ListValue\":\n    var i = [];\n    for (var n = 0, a = e.values; n < a.length; n += 1) {\n      i.push(valueFromASTUntyped(a[n], r));\n    }\n    return i;\n\n   case \"ObjectValue\":\n    var t = Object.create(null);\n    for (var o = 0, l = e.fields; o < l.length; o += 1) {\n      var u = l[o];\n      t[u.name.value] = valueFromASTUntyped(u.value, r);\n    }\n    return t;\n\n   case \"Variable\":\n    return r && r[e.name.value];\n  }\n}\n\nfunction valueFromTypeNode(e, r, i) {\n  if (\"Variable\" === e.kind) {\n    return i ? valueFromTypeNode(i[e.name.value], r, i) : void 0;\n  } else if (\"NonNullType\" === r.kind) {\n    return \"NullValue\" !== e.kind ? valueFromTypeNode(e, r, i) : void 0;\n  } else if (\"NullValue\" === e.kind) {\n    return null;\n  } else if (\"ListType\" === r.kind) {\n    if (\"ListValue\" === e.kind) {\n      var n = [];\n      for (var a = 0, t = e.values; a < t.length; a += 1) {\n        var o = valueFromTypeNode(t[a], r.type, i);\n        if (void 0 === o) {\n          return;\n        } else {\n          n.push(o);\n        }\n      }\n      return n;\n    }\n  } else if (\"NamedType\" === r.kind) {\n    switch (r.name.value) {\n     case \"Int\":\n     case \"Float\":\n     case \"String\":\n     case \"Bool\":\n      return r.name.value + \"Value\" === e.kind ? valueFromASTUntyped(e, i) : void 0;\n\n     default:\n      return valueFromASTUntyped(e, i);\n    }\n  }\n}\n\nexport { h as BREAK, GraphQLError, e as Kind, r as OperationTypeNode, parse, parseType, parseValue, print, printBlockString, printString, valueFromASTUntyped, valueFromTypeNode, visit };\n//# sourceMappingURL=graphql.web.mjs.map\n", "export const templates = new Set(['gql', 'graphql']);\n", "import { ts } from '../ts';\nimport { templates } from './templates';\n\n/** Checks for an immediately-invoked function expression */\nexport const isIIFE = (node: ts.Node): boolean =>\n  ts.isCallExpression(node) &&\n  node.arguments.length === 0 &&\n  (ts.isFunctionExpression(node.expression) ||\n    ts.isArrowFunction(node.expression)) &&\n  !node.expression.asteriskToken &&\n  !node.expression.modifiers?.length;\n\n/** Checks if node is a known identifier of graphql functions ('graphql' or 'gql') */\nexport const isGraphQLFunctionIdentifier = (\n  node: ts.Node\n): node is ts.Identifier =>\n  ts.isIdentifier(node) && templates.has(node.escapedText as string);\n\n/** If `checker` is passed, checks if node (as identifier/expression) is a gql.tada graphql() function */\nexport const isTadaGraphQLFunction = (\n  node: ts.Node,\n  checker: ts.TypeChecker | undefined\n): node is ts.LeftHandSideExpression => {\n  if (!ts.isLeftHandSideExpression(node)) return false;\n  const type = checker?.getTypeAtLocation(node);\n  // Any function that has both a `scalar` and `persisted` property\n  // is automatically considered a gql.tada graphql() function.\n  return (\n    type != null &&\n    type.getProperty('scalar') != null &&\n    type.getProperty('persisted') != null\n  );\n};\n\n/** If `checker` is passed, checks if node is a gql.tada graphql() call */\nexport const isTadaGraphQLCall = (\n  node: ts.CallExpression,\n  checker: ts.TypeChecker | undefined\n): boolean => {\n  // We expect graphql() to be called with either a string literal\n  // or a string literal and an array of fragments\n  if (!ts.isCallExpression(node)) {\n    return false;\n  } else if (node.arguments.length < 1 || node.arguments.length > 2) {\n    return false;\n  } else if (!ts.isStringLiteralLike(node.arguments[0]!)) {\n    return false;\n  }\n  return checker ? isTadaGraphQLFunction(node.expression, checker) : false;\n};\n\n/** Checks if node is a gql.tada graphql.persisted() call */\nexport const isTadaPersistedCall = (\n  node: ts.Node | undefined,\n  checker: ts.TypeChecker | undefined\n): node is ts.CallExpression => {\n  if (!node) {\n    return false;\n  } else if (!ts.isCallExpression(node)) {\n    return false;\n  } else if (!ts.isPropertyAccessExpression(node.expression)) {\n    return false; // rejecting non property access calls: <expression>.<name>()\n  } else if (\n    !ts.isIdentifier(node.expression.name) ||\n    node.expression.name.escapedText !== 'persisted'\n  ) {\n    return false; // rejecting calls on anyting but 'persisted': <expression>.persisted()\n  } else if (isGraphQLFunctionIdentifier(node.expression.expression)) {\n    return true;\n  } else {\n    return isTadaGraphQLFunction(node.expression.expression, checker);\n  }\n};\n\n// As per check in `isGraphQLCall()` below, enforces arguments length\nexport type GraphQLCallNode = ts.CallExpression & {\n  arguments: [ts.Expression] | [ts.Expression, ts.Expression];\n};\n\n/** Checks if node is a gql.tada or regular graphql() call */\nexport const isGraphQLCall = (\n  node: ts.Node,\n  checker: ts.TypeChecker | undefined\n): node is GraphQLCallNode => {\n  return (\n    ts.isCallExpression(node) &&\n    node.arguments.length >= 1 &&\n    node.arguments.length <= 2 &&\n    (isGraphQLFunctionIdentifier(node.expression) ||\n      isTadaGraphQLCall(node, checker))\n  );\n};\n\n/** Checks if node is a gql/graphql tagged template literal */\nexport const isGraphQLTag = (\n  node: ts.Node\n): node is ts.TaggedTemplateExpression =>\n  ts.isTaggedTemplateExpression(node) && isGraphQLFunctionIdentifier(node.tag);\n\n/** Retrieves the `__name` branded tag from gql.tada `graphql()` or `graphql.persisted()` calls */\nexport const getSchemaName = (\n  node: ts.CallExpression,\n  typeChecker: ts.TypeChecker | undefined,\n  isTadaPersistedCall = false\n): string | null => {\n  if (!typeChecker) return null;\n  const type = typeChecker.getTypeAtLocation(\n    // When calling `graphql.persisted`, we need to access the `graphql` part of\n    // the expression; `node.expression` is the `.persisted` part\n    isTadaPersistedCall ? node.getChildAt(0).getChildAt(0) : node.expression\n  );\n  if (type) {\n    const brandTypeSymbol = type.getProperty('__name');\n    if (brandTypeSymbol) {\n      const brand = typeChecker.getTypeOfSymbol(brandTypeSymbol);\n      if (brand.isUnionOrIntersection()) {\n        const found = brand.types.find(x => x.isStringLiteral());\n        return found && found.isStringLiteral() ? found.value : null;\n      } else if (brand.isStringLiteral()) {\n        return brand.value;\n      }\n    }\n  }\n  return null;\n};\n", "import { ts } from '../ts';\n\nexport type ValueDeclaration =\n  | ts.BinaryExpression\n  | ts.ArrowFunction\n  | ts.BindingElement\n  | ts.ClassDeclaration\n  | ts.ClassExpression\n  | ts.ClassStaticBlockDeclaration\n  | ts.ConstructorDeclaration\n  | ts.EnumDeclaration\n  | ts.EnumMember\n  | ts.ExportAssignment\n  | ts.ExportSpecifier\n  | ts.FunctionDeclaration\n  | ts.FunctionExpression\n  | ts.GetAccessorDeclaration\n  | ts.JsxAttribute\n  | ts.MethodDeclaration\n  | ts.ModuleDeclaration\n  | ts.ParameterDeclaration\n  | ts.PropertyAssignment\n  | ts.PropertyDeclaration\n  | ts.SetAccessorDeclaration\n  | ts.ShorthandPropertyAssignment\n  | ts.VariableDeclaration;\n\nexport type ValueOfDeclaration =\n  | ts.ClassExpression\n  | ts.ClassDeclaration\n  | ts.ArrowFunction\n  | ts.ClassStaticBlockDeclaration\n  | ts.ConstructorDeclaration\n  | ts.EnumDeclaration\n  | ts.FunctionDeclaration\n  | ts.GetAccessorDeclaration\n  | ts.SetAccessorDeclaration\n  | ts.MethodDeclaration\n  | ts.Expression;\n\n/** Checks if a node is a `ts.Declaration` and a value.\n * @remarks\n * This checks if a given node is a value declaration only,\n * excluding import/export specifiers, type declarations, and\n * ambient declarations.\n * All declarations that aren't JS(x) nodes will be discarded.\n * This is based on `ts.isDeclarationKind`.\n */\nexport function isValueDeclaration(node: ts.Node): node is ValueDeclaration {\n  switch (node.kind) {\n    case ts.SyntaxKind.BinaryExpression:\n    case ts.SyntaxKind.ArrowFunction:\n    case ts.SyntaxKind.BindingElement:\n    case ts.SyntaxKind.ClassDeclaration:\n    case ts.SyntaxKind.ClassExpression:\n    case ts.SyntaxKind.ClassStaticBlockDeclaration:\n    case ts.SyntaxKind.Constructor:\n    case ts.SyntaxKind.EnumDeclaration:\n    case ts.SyntaxKind.EnumMember:\n    case ts.SyntaxKind.ExportAssignment:\n    case ts.SyntaxKind.FunctionDeclaration:\n    case ts.SyntaxKind.FunctionExpression:\n    case ts.SyntaxKind.GetAccessor:\n    case ts.SyntaxKind.JsxAttribute:\n    case ts.SyntaxKind.MethodDeclaration:\n    case ts.SyntaxKind.Parameter:\n    case ts.SyntaxKind.PropertyAssignment:\n    case ts.SyntaxKind.PropertyDeclaration:\n    case ts.SyntaxKind.SetAccessor:\n    case ts.SyntaxKind.ShorthandPropertyAssignment:\n    case ts.SyntaxKind.VariableDeclaration:\n      return true;\n    default:\n      return false;\n  }\n}\n\n/** Returns true if operator assigns a value unchanged */\nfunction isAssignmentOperator(token: ts.BinaryOperatorToken): boolean {\n  switch (token.kind) {\n    case ts.SyntaxKind.EqualsToken:\n    case ts.SyntaxKind.BarBarEqualsToken:\n    case ts.SyntaxKind.AmpersandAmpersandEqualsToken:\n    case ts.SyntaxKind.QuestionQuestionEqualsToken:\n      return true;\n    default:\n      return false;\n  }\n}\n\n/** Evaluates to the declaration's value initializer or itself if it declares a value */\nexport function getValueOfValueDeclaration(\n  node: ValueDeclaration\n): ValueOfDeclaration | undefined {\n  switch (node.kind) {\n    case ts.SyntaxKind.ClassExpression:\n    case ts.SyntaxKind.ClassDeclaration:\n    case ts.SyntaxKind.ArrowFunction:\n    case ts.SyntaxKind.ClassStaticBlockDeclaration:\n    case ts.SyntaxKind.Constructor:\n    case ts.SyntaxKind.EnumDeclaration:\n    case ts.SyntaxKind.FunctionDeclaration:\n    case ts.SyntaxKind.FunctionExpression:\n    case ts.SyntaxKind.GetAccessor:\n    case ts.SyntaxKind.SetAccessor:\n    case ts.SyntaxKind.MethodDeclaration:\n      return node;\n    case ts.SyntaxKind.BindingElement:\n    case ts.SyntaxKind.EnumMember:\n    case ts.SyntaxKind.JsxAttribute:\n    case ts.SyntaxKind.Parameter:\n    case ts.SyntaxKind.PropertyAssignment:\n    case ts.SyntaxKind.PropertyDeclaration:\n    case ts.SyntaxKind.VariableDeclaration:\n      return node.initializer;\n    case ts.SyntaxKind.ExportAssignment:\n      return node.expression;\n    case ts.SyntaxKind.BinaryExpression:\n      return isAssignmentOperator(node.operatorToken) ? node.right : undefined;\n    case ts.SyntaxKind.ShorthandPropertyAssignment:\n      return node.objectAssignmentInitializer;\n    default:\n      return undefined;\n  }\n}\n\n// See: https://github.com/microsoft/TypeScript/blob/a5eec24/src/services/utilities.ts#L652-L654\nfunction climbPastPropertyOrElementAccess(node: ts.Node): ts.Node {\n  if (\n    node.parent &&\n    ts.isPropertyAccessExpression(node.parent) &&\n    node.parent.name === node\n  ) {\n    return node.parent;\n  } else if (\n    node.parent &&\n    ts.isElementAccessExpression(node.parent) &&\n    node.parent.argumentExpression === node\n  ) {\n    return node.parent;\n  } else {\n    return node;\n  }\n}\n\n// See: https://github.com/microsoft/TypeScript/blob/a5eec24/src/services/utilities.ts#L602-L605\nfunction isNewExpressionTarget(node: ts.Node): node is ts.NewExpression {\n  const target = climbPastPropertyOrElementAccess(node).parent;\n  return ts.isNewExpression(target) && target.expression === node;\n}\n\n// See: https://github.com/microsoft/TypeScript/blob/a5eec24/src/services/utilities.ts#L607-L610\nfunction isCallOrNewExpressionTarget(\n  node: ts.Node\n): node is ts.CallExpression | ts.NewExpression {\n  const target = climbPastPropertyOrElementAccess(node).parent;\n  return ts.isCallOrNewExpression(target) && target.expression === node;\n}\n\n// See: https://github.com/microsoft/TypeScript/blob/a5eec24/src/services/utilities.ts#L716-L719\nfunction isNameOfFunctionDeclaration(node: ts.Node): boolean {\n  return (\n    ts.isIdentifier(node) &&\n    node.parent &&\n    ts.isFunctionLike(node.parent) &&\n    node.parent.name === node\n  );\n}\n\n// See: https://github.com/microsoft/TypeScript/blob/a5eec24/src/services/utilities.ts#L2441-L2447\nfunction getNameFromPropertyName(name: ts.PropertyName): string | undefined {\n  if (ts.isComputedPropertyName(name)) {\n    return ts.isStringLiteralLike(name.expression) ||\n      ts.isNumericLiteral(name.expression)\n      ? name.expression.text\n      : undefined;\n  } else if (ts.isPrivateIdentifier(name) || ts.isMemberName(name)) {\n    return ts.idText(name);\n  } else {\n    return name.text;\n  }\n}\n\n/** Resolves the declaration of an identifier.\n * @remarks\n * This returns the declaration node first found for an identifier by resolving an identifier's\n * symbol via the type checker.\n * @privateRemarks\n * This mirrors the implementation of `getDefinitionAtPosition` in TS' language service. However,\n * it removes all cases that aren't applicable to identifiers and removes the intermediary positional\n * data structure, instead returning raw AST nodes.\n */\nexport function getDeclarationOfIdentifier(\n  node: ts.Identifier,\n  checker: ts.TypeChecker\n): ValueDeclaration | undefined {\n  // See: https://github.com/microsoft/TypeScript/blob/a5eec24/src/services/goToDefinition.ts#L523-L540\n  let symbol = checker.getSymbolAtLocation(node);\n  if (\n    symbol?.declarations?.[0] &&\n    symbol.flags & ts.SymbolFlags.Alias &&\n    (node.parent === symbol?.declarations?.[0] ||\n      !ts.isNamespaceImport(symbol.declarations[0]))\n  ) {\n    // Resolve alias symbols, excluding self-referential symbols\n    const aliased = checker.getAliasedSymbol(symbol);\n    if (aliased.declarations) symbol = aliased;\n  }\n\n  if (symbol && ts.isShorthandPropertyAssignment(node.parent)) {\n    // See: https://github.com/microsoft/TypeScript/blob/a5eec24/src/services/goToDefinition.ts#L248-L257\n    // Resolve shorthand property assignments\n    const shorthandSymbol = checker.getShorthandAssignmentValueSymbol(\n      symbol.valueDeclaration\n    );\n    if (shorthandSymbol) symbol = shorthandSymbol;\n  } else if (\n    ts.isBindingElement(node.parent) &&\n    ts.isObjectBindingPattern(node.parent.parent) &&\n    node === (node.parent.propertyName || node.parent.name)\n  ) {\n    // See: https://github.com/microsoft/TypeScript/blob/a5eec24/src/services/goToDefinition.ts#L259-L280\n    // Resolve symbol of property in shorthand assignments\n    const name = getNameFromPropertyName(node);\n    const prop = name\n      ? checker.getTypeAtLocation(node.parent.parent).getProperty(name)\n      : undefined;\n    if (prop) symbol = prop;\n  } else if (\n    ts.isObjectLiteralElement(node.parent) &&\n    (ts.isObjectLiteralExpression(node.parent.parent) ||\n      ts.isJsxAttributes(node.parent.parent)) &&\n    node.parent.name === node\n  ) {\n    // See: https://github.com/microsoft/TypeScript/blob/a5eec24/src/services/goToDefinition.ts#L298-L316\n    // Resolve symbol of property in object literal destructre expressions\n    const name = getNameFromPropertyName(node);\n    const prop = name\n      ? checker.getContextualType(node.parent.parent)?.getProperty(name)\n      : undefined;\n    if (prop) symbol = prop;\n  }\n\n  if (symbol && symbol.declarations?.length) {\n    if (\n      symbol.flags & ts.SymbolFlags.Class &&\n      !(symbol.flags & (ts.SymbolFlags.Function | ts.SymbolFlags.Variable)) &&\n      isNewExpressionTarget(node)\n    ) {\n      // See: https://github.com/microsoft/TypeScript/blob/a5eec24/src/services/goToDefinition.ts#L603-L610\n      // Resolve first class-like declaration for new expressions\n      for (const declaration of symbol.declarations) {\n        if (ts.isClassLike(declaration)) return declaration;\n      }\n    } else if (\n      isCallOrNewExpressionTarget(node) ||\n      isNameOfFunctionDeclaration(node)\n    ) {\n      // See: https://github.com/microsoft/TypeScript/blob/a5eec24/src/services/goToDefinition.ts#L612-L616\n      // Resolve first function-like declaration for call expressions or named functions\n      for (const declaration of symbol.declarations) {\n        if (\n          ts.isFunctionLike(declaration) &&\n          !!(declaration as ts.FunctionLikeDeclaration).body &&\n          isValueDeclaration(declaration)\n        ) {\n          return declaration;\n        }\n      }\n    }\n\n    // Account for assignments to property access expressions\n    // This resolves property access expressions to binding element parents\n    if (\n      symbol.valueDeclaration &&\n      ts.isPropertyAccessExpression(symbol.valueDeclaration)\n    ) {\n      const parent = symbol.valueDeclaration.parent;\n      if (\n        parent &&\n        ts.isBinaryExpression(parent) &&\n        parent.left === symbol.valueDeclaration\n      ) {\n        return parent;\n      }\n    }\n\n    if (\n      symbol.valueDeclaration &&\n      isValueDeclaration(symbol.valueDeclaration)\n    ) {\n      // NOTE: We prefer value declarations, since the checker may have already applied conditions\n      // similar to `isValueDeclaration` and selected it beforehand\n      // Only use value declarations if they're not type/ambient declarations or imports/exports\n      return symbol.valueDeclaration;\n    }\n\n    // Selecting the first available result, if any\n    // NOTE: We left out `!isExpandoDeclaration` as a condition, since `valueDeclaration` above\n    // should handle some of these cases, and we don't have to care about this subtlety as much for identifiers\n    // See: https://github.com/microsoft/TypeScript/blob/a5eec24/src/services/goToDefinition.ts#L582-L590\n    for (const declaration of symbol.declarations) {\n      // Only use declarations if they're not type/ambient declarations or imports/exports\n      if (isValueDeclaration(declaration)) return declaration;\n    }\n  }\n\n  return undefined;\n}\n\n/** Loops {@link getDeclarationOfIdentifier} until a value of the identifier is found */\nexport function getValueOfIdentifier(\n  node: ts.Identifier,\n  checker: ts.TypeChecker\n): ValueOfDeclaration | undefined {\n  while (ts.isIdentifier(node)) {\n    const declaration = getDeclarationOfIdentifier(node, checker);\n    if (!declaration) {\n      return undefined;\n    } else {\n      const value = getValueOfValueDeclaration(declaration);\n      if (value && ts.isIdentifier(value) && value !== node) {\n        // If the resolved value is another identifiers, we continue searching, if the\n        // identifier isn't self-referential\n        node = value;\n      } else {\n        return value;\n      }\n    }\n  }\n}\n\n/** Resolves exressions that might not influence the target identifier */\nexport function getIdentifierOfChainExpression(\n  node: ts.Expression\n): ts.Identifier | undefined {\n  let target: ts.Expression | undefined = node;\n  while (target) {\n    if (ts.isPropertyAccessExpression(target)) {\n      target = target.name;\n    } else if (\n      ts.isAsExpression(target) ||\n      ts.isSatisfiesExpression(target) ||\n      ts.isNonNullExpression(target) ||\n      ts.isParenthesizedExpression(target) ||\n      ts.isExpressionWithTypeArguments(target)\n    ) {\n      target = target.expression;\n    } else if (ts.isCommaListExpression(target)) {\n      target = target.elements[target.elements.length - 1];\n    } else if (ts.isIdentifier(target)) {\n      return target;\n    } else {\n      return;\n    }\n  }\n}\n", "import { print } from '@0no-co/graphql.web';\nimport { ts } from '../ts';\nimport {\n  getDeclarationOfIdentifier,\n  getValueOfValueDeclaration,\n} from './declaration';\n\ntype TemplateResult = {\n  combinedText: string;\n  resolvedSpans: Array<{\n    lines: number;\n    identifier: string;\n    original: { start: number; length: number };\n    new: { start: number; length: number };\n  }>;\n};\n\nexport function resolveTemplate(\n  node: ts.TaggedTemplateExpression | ts.StringLiteralLike,\n  filename: string,\n  info: ts.server.PluginCreateInfo\n): TemplateResult {\n  if (ts.isStringLiteralLike(node)) {\n    return { combinedText: node.getText().slice(1, -1), resolvedSpans: [] };\n  }\n\n  let templateText = node.template.getText().slice(1, -1);\n  if (\n    ts.isNoSubstitutionTemplateLiteral(node.template) ||\n    node.template.templateSpans.length === 0\n  ) {\n    return { combinedText: templateText, resolvedSpans: [] };\n  }\n\n  let addedCharacters = 0;\n  const resolvedSpans = node.template.templateSpans\n    .map(span => {\n      if (ts.isIdentifier(span.expression)) {\n        const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n        if (!typeChecker) return;\n\n        const declaration = getDeclarationOfIdentifier(\n          span.expression,\n          typeChecker\n        );\n        if (!declaration) return;\n\n        const parent = declaration;\n        if (ts.isVariableDeclaration(parent)) {\n          const identifierName = span.expression.escapedText;\n          const value = getValueOfValueDeclaration(parent);\n          if (!value) return;\n\n          // we reduce by two to account for the \"${\"\n          const originalStart = span.expression.getStart() - 2;\n          const originalRange = {\n            start: originalStart,\n            // we add 1 to account for the \"}\"\n            length: span.expression.end - originalStart + 1,\n          };\n\n          if (ts.isTaggedTemplateExpression(value)) {\n            const text = resolveTemplate(\n              value,\n              parent.getSourceFile().fileName,\n              info\n            );\n            templateText = templateText.replace(\n              '${' + span.expression.escapedText + '}',\n              text.combinedText\n            );\n\n            const alteredSpan = {\n              lines: text.combinedText.split('\\n').length,\n              identifier: identifierName,\n              original: originalRange,\n              new: {\n                start: originalRange.start + addedCharacters,\n                length: text.combinedText.length,\n              },\n            };\n            addedCharacters += text.combinedText.length - originalRange.length;\n            return alteredSpan;\n          } else if (\n            ts.isAsExpression(value) &&\n            ts.isTaggedTemplateExpression(value.expression)\n          ) {\n            const text = resolveTemplate(\n              value.expression,\n              parent.getSourceFile().fileName,\n              info\n            );\n            templateText = templateText.replace(\n              '${' + span.expression.escapedText + '}',\n              text.combinedText\n            );\n            const alteredSpan = {\n              lines: text.combinedText.split('\\n').length,\n              identifier: identifierName,\n              original: originalRange,\n              new: {\n                start: originalRange.start + addedCharacters,\n                length: text.combinedText.length,\n              },\n            };\n            addedCharacters += text.combinedText.length - originalRange.length;\n            return alteredSpan;\n          } else if (\n            ts.isAsExpression(value) &&\n            ts.isAsExpression(value.expression) &&\n            ts.isObjectLiteralExpression(value.expression.expression)\n          ) {\n            const astObject = JSON.parse(value.expression.expression.getText());\n            const resolvedTemplate = print(astObject);\n            templateText = templateText.replace(\n              '${' + span.expression.escapedText + '}',\n              resolvedTemplate\n            );\n            const alteredSpan = {\n              lines: resolvedTemplate.split('\\n').length,\n              identifier: identifierName,\n              original: originalRange,\n              new: {\n                start: originalRange.start + addedCharacters,\n                length: resolvedTemplate.length,\n              },\n            };\n            addedCharacters += resolvedTemplate.length - originalRange.length;\n            return alteredSpan;\n          }\n\n          return undefined;\n        }\n      }\n\n      return undefined;\n    })\n    .filter(Boolean) as TemplateResult['resolvedSpans'];\n\n  return { combinedText: templateText, resolvedSpans };\n}\n\nexport const resolveTadaFragmentArray = (\n  node: ts.Expression | undefined\n): undefined | readonly ts.Identifier[] => {\n  if (!node) return undefined;\n  // NOTE: Remove `as T`, users may commonly use `as const` for no reason\n  while (ts.isAsExpression(node)) node = node.expression;\n  if (!ts.isArrayLiteralExpression(node)) return undefined;\n  // NOTE: Let's avoid the allocation of another array here if we can\n  if (node.elements.every(ts.isIdentifier)) return node.elements;\n  const identifiers: ts.Identifier[] = [];\n  for (let element of node.elements) {\n    while (ts.isPropertyAccessExpression(element)) element = element.name;\n    if (ts.isIdentifier(element)) identifiers.push(element);\n  }\n  return identifiers;\n};\n", "import { ts } from '../ts';\nimport { FragmentDefinitionNode, parse } from 'graphql';\nimport * as checks from './checks';\nimport { resolveTadaFragmentArray } from './resolve';\nimport {\n  getDeclarationOfIdentifier,\n  getValueOfIdentifier,\n  getIdentifierOfChainExpression,\n} from './declaration';\n\nexport { getSchemaName } from './checks';\n\nexport function getSource(info: ts.server.PluginCreateInfo, filename: string) {\n  const program = info.languageService.getProgram();\n  if (!program) return undefined;\n\n  const source = program.getSourceFile(filename);\n  if (!source) return undefined;\n\n  return source;\n}\n\nexport function findNode(\n  sourceFile: ts.SourceFile,\n  position: number\n): ts.Node | undefined {\n  function find(node: ts.Node): ts.Node | undefined {\n    if (position >= node.getStart() && position < node.getEnd()) {\n      return ts.forEachChild(node, find) || node;\n    }\n  }\n  return find(sourceFile);\n}\n\nexport function findAllTaggedTemplateNodes(\n  sourceFile: ts.SourceFile | ts.Node\n): Array<ts.TaggedTemplateExpression | ts.NoSubstitutionTemplateLiteral> {\n  const result: Array<\n    ts.TaggedTemplateExpression | ts.NoSubstitutionTemplateLiteral\n  > = [];\n  function find(node: ts.Node) {\n    if (\n      checks.isGraphQLTag(node) ||\n      (ts.isNoSubstitutionTemplateLiteral(node) &&\n        checks.isGraphQLTag(node.parent))\n    ) {\n      result.push(node);\n      return;\n    } else {\n      ts.forEachChild(node, find);\n    }\n  }\n  find(sourceFile);\n  return result;\n}\n\nfunction resolveIdentifierToGraphQLCall(\n  input: ts.Identifier,\n  info: ts.server.PluginCreateInfo,\n  checker: ts.TypeChecker | undefined\n): checks.GraphQLCallNode | null {\n  if (!checker) return null;\n\n  const value = getValueOfIdentifier(input, checker);\n  if (!value) return null;\n\n  // Check whether we've got a `graphql()` or `gql()` call\n  return checks.isGraphQLCall(value, checker) ? value : null;\n}\n\nfunction unrollFragment(\n  element: ts.Identifier,\n  info: ts.server.PluginCreateInfo,\n  checker: ts.TypeChecker | undefined\n): Array<FragmentDefinitionNode> {\n  const fragments: FragmentDefinitionNode[] = [];\n  const elements: ts.Identifier[] = [element];\n  const seen = new WeakSet<ts.Identifier>();\n\n  const _unrollElement = (element: ts.Identifier): void => {\n    if (seen.has(element)) return;\n    seen.add(element);\n\n    const node = resolveIdentifierToGraphQLCall(element, info, checker);\n    if (!node) return;\n\n    const fragmentRefs = resolveTadaFragmentArray(node.arguments[1]);\n    if (fragmentRefs) elements.push(...fragmentRefs);\n\n    try {\n      const text = node.arguments[0];\n      const parsed = parse(text.getText().slice(1, -1), { noLocation: true });\n      parsed.definitions.forEach(definition => {\n        if (definition.kind === 'FragmentDefinition') {\n          fragments.push(definition);\n        }\n      });\n    } catch (_error) {\n      // NOTE: Assume graphql.parse errors can be ignored\n    }\n  };\n\n  let nextElement: ts.Identifier | undefined;\n  while ((nextElement = elements.shift()) !== undefined)\n    _unrollElement(nextElement);\n  return fragments;\n}\n\nexport function unrollTadaFragments(\n  fragmentsArray: ts.ArrayLiteralExpression,\n  wip: FragmentDefinitionNode[],\n  info: ts.server.PluginCreateInfo\n): FragmentDefinitionNode[] {\n  const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n  fragmentsArray.elements.forEach(element => {\n    if (ts.isIdentifier(element)) {\n      wip.push(...unrollFragment(element, info, typeChecker));\n    } else if (ts.isPropertyAccessExpression(element)) {\n      let el = element;\n      while (ts.isPropertyAccessExpression(el.expression)) el = el.expression;\n      if (ts.isIdentifier(el.name)) {\n        wip.push(...unrollFragment(el.name, info, typeChecker));\n      }\n    }\n  });\n\n  return wip;\n}\n\nexport function findAllCallExpressions(\n  sourceFile: ts.SourceFile,\n  info: ts.server.PluginCreateInfo,\n  shouldSearchFragments: boolean = true\n): {\n  nodes: Array<{\n    node: ts.StringLiteralLike;\n    schema: string | null;\n  }>;\n  fragments: Array<FragmentDefinitionNode>;\n} {\n  const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n  const result: Array<{\n    node: ts.StringLiteralLike;\n    schema: string | null;\n  }> = [];\n  let fragments: Array<FragmentDefinitionNode> = [];\n  let hasTriedToFindFragments = shouldSearchFragments ? false : true;\n\n  function find(node: ts.Node): void {\n    if (!ts.isCallExpression(node) || checks.isIIFE(node)) {\n      return ts.forEachChild(node, find);\n    }\n\n    // Check whether we've got a `graphql()` or `gql()` call, by the\n    // call expression's identifier\n    if (!checks.isGraphQLCall(node, typeChecker)) {\n      return ts.forEachChild(node, find);\n    }\n\n    const name = checks.getSchemaName(node, typeChecker);\n    const text = node.arguments[0];\n    const fragmentRefs = resolveTadaFragmentArray(node.arguments[1]);\n\n    if (!hasTriedToFindFragments && !fragmentRefs) {\n      hasTriedToFindFragments = true;\n      fragments.push(...getAllFragments(sourceFile.fileName, node, info));\n    } else if (fragmentRefs) {\n      for (const identifier of fragmentRefs) {\n        fragments.push(...unrollFragment(identifier, info, typeChecker));\n      }\n    }\n\n    if (text && ts.isStringLiteralLike(text)) {\n      result.push({ node: text, schema: name });\n    }\n  }\n  find(sourceFile);\n  return { nodes: result, fragments };\n}\n\nexport function findAllPersistedCallExpressions(\n  sourceFile: ts.SourceFile\n): Array<ts.CallExpression>;\nexport function findAllPersistedCallExpressions(\n  sourceFile: ts.SourceFile,\n  info: ts.server.PluginCreateInfo\n): Array<{ node: ts.CallExpression; schema: string | null }>;\n\nexport function findAllPersistedCallExpressions(\n  sourceFile: ts.SourceFile,\n  info?: ts.server.PluginCreateInfo\n) {\n  const result: Array<\n    ts.CallExpression | { node: ts.CallExpression; schema: string | null }\n  > = [];\n  const typeChecker = info?.languageService.getProgram()?.getTypeChecker();\n  function find(node: ts.Node): void {\n    if (!ts.isCallExpression(node) || checks.isIIFE(node)) {\n      return ts.forEachChild(node, find);\n    }\n\n    if (!checks.isTadaPersistedCall(node, typeChecker)) {\n      return;\n    } else if (info) {\n      const name = checks.getSchemaName(node, typeChecker, true);\n      result.push({ node, schema: name });\n    } else {\n      result.push(node);\n    }\n  }\n  find(sourceFile);\n  return result;\n}\n\nexport function getAllFragments(\n  fileName: string,\n  node: ts.Node,\n  info: ts.server.PluginCreateInfo\n) {\n  let fragments: Array<FragmentDefinitionNode> = [];\n\n  const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n  if (!ts.isCallExpression(node)) {\n    return fragments;\n  }\n\n  const fragmentRefs = resolveTadaFragmentArray(node.arguments[1]);\n  if (fragmentRefs) {\n    const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n    for (const identifier of fragmentRefs) {\n      fragments.push(...unrollFragment(identifier, info, typeChecker));\n    }\n    return fragments;\n  } else if (checks.isTadaGraphQLCall(node, typeChecker)) {\n    return fragments;\n  }\n\n  if (!typeChecker) return fragments;\n\n  const identifier = getIdentifierOfChainExpression(node.expression);\n  if (!identifier) return fragments;\n\n  const declaration = getDeclarationOfIdentifier(identifier, typeChecker);\n  if (!declaration) return fragments;\n\n  const sourceFile = declaration.getSourceFile();\n  if (!sourceFile) return fragments;\n\n  const definitions = [\n    {\n      fileName: sourceFile.fileName,\n      textSpan: {\n        start: declaration.getStart(),\n        length: declaration.getWidth(),\n      },\n    },\n  ];\n  if (!definitions || !definitions.length) return fragments;\n\n  const def = definitions[0];\n  if (!def) return fragments;\n  const src = getSource(info, def.fileName);\n  if (!src) return fragments;\n\n  ts.forEachChild(src, node => {\n    if (\n      ts.isVariableStatement(node) &&\n      node.declarationList &&\n      node.declarationList.declarations[0] &&\n      node.declarationList.declarations[0].name.getText() === 'documents'\n    ) {\n      const [declaration] = node.declarationList.declarations;\n      if (\n        declaration.initializer &&\n        ts.isObjectLiteralExpression(declaration.initializer)\n      ) {\n        declaration.initializer.properties.forEach(property => {\n          if (\n            ts.isPropertyAssignment(property) &&\n            ts.isStringLiteral(property.name)\n          ) {\n            try {\n              const possibleFragment = JSON.parse(\n                `${property.name.getText().replace(/'/g, '\"')}`\n              );\n\n              if (\n                possibleFragment.includes('fragment ') &&\n                possibleFragment.includes(' on ')\n              ) {\n                const parsed = parse(possibleFragment, {\n                  noLocation: true,\n                });\n                parsed.definitions.forEach(definition => {\n                  if (definition.kind === 'FragmentDefinition') {\n                    fragments.push(definition);\n                  }\n                });\n              }\n            } catch (e: any) {}\n          }\n        });\n      }\n    }\n  });\n\n  return fragments;\n}\n\nexport function findAllImports(\n  sourceFile: ts.SourceFile\n): Array<ts.ImportDeclaration> {\n  return sourceFile.statements.filter(ts.isImportDeclaration);\n}\n\nexport function bubbleUpTemplate(node: ts.Node): ts.Node {\n  while (\n    ts.isNoSubstitutionTemplateLiteral(node) ||\n    ts.isToken(node) ||\n    ts.isTemplateExpression(node) ||\n    ts.isTemplateSpan(node)\n  ) {\n    node = node.parent;\n  }\n\n  return node;\n}\n\nexport function bubbleUpCallExpression(node: ts.Node): ts.Node {\n  while (\n    ts.isStringLiteralLike(node) ||\n    ts.isToken(node) ||\n    ts.isTemplateExpression(node) ||\n    ts.isTemplateSpan(node)\n  ) {\n    node = node.parent;\n  }\n\n  return node;\n}\n", "/**\n * @module LRUCache\n */\nconst perf = typeof performance === 'object' &&\n    performance &&\n    typeof performance.now === 'function'\n    ? performance\n    : Date;\nconst warned = new Set();\n/* c8 ignore start */\nconst PROCESS = (typeof process === 'object' && !!process ? process : {});\n/* c8 ignore start */\nconst emitWarning = (msg, type, code, fn) => {\n    typeof PROCESS.emitWarning === 'function'\n        ? PROCESS.emitWarning(msg, type, code, fn)\n        : console.error(`[${code}] ${type}: ${msg}`);\n};\nlet AC = globalThis.AbortController;\nlet AS = globalThis.AbortSignal;\n/* c8 ignore start */\nif (typeof AC === 'undefined') {\n    //@ts-ignore\n    AS = class AbortSignal {\n        onabort;\n        _onabort = [];\n        reason;\n        aborted = false;\n        addEventListener(_, fn) {\n            this._onabort.push(fn);\n        }\n    };\n    //@ts-ignore\n    AC = class AbortController {\n        constructor() {\n            warnACPolyfill();\n        }\n        signal = new AS();\n        abort(reason) {\n            if (this.signal.aborted)\n                return;\n            //@ts-ignore\n            this.signal.reason = reason;\n            //@ts-ignore\n            this.signal.aborted = true;\n            //@ts-ignore\n            for (const fn of this.signal._onabort) {\n                fn(reason);\n            }\n            this.signal.onabort?.(reason);\n        }\n    };\n    let printACPolyfillWarning = PROCESS.env?.LRU_CACHE_IGNORE_AC_WARNING !== '1';\n    const warnACPolyfill = () => {\n        if (!printACPolyfillWarning)\n            return;\n        printACPolyfillWarning = false;\n        emitWarning('AbortController is not defined. If using lru-cache in ' +\n            'node 14, load an AbortController polyfill from the ' +\n            '`node-abort-controller` package. A minimal polyfill is ' +\n            'provided for use by LRUCache.fetch(), but it should not be ' +\n            'relied upon in other contexts (eg, passing it to other APIs that ' +\n            'use AbortController/AbortSignal might have undesirable effects). ' +\n            'You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.', 'NO_ABORT_CONTROLLER', 'ENOTSUP', warnACPolyfill);\n    };\n}\n/* c8 ignore stop */\nconst shouldWarn = (code) => !warned.has(code);\nconst TYPE = Symbol('type');\nconst isPosInt = (n) => n && n === Math.floor(n) && n > 0 && isFinite(n);\n/* c8 ignore start */\n// This is a little bit ridiculous, tbh.\n// The maximum array length is 2^32-1 or thereabouts on most JS impls.\n// And well before that point, you're caching the entire world, I mean,\n// that's ~32GB of just integers for the next/prev links, plus whatever\n// else to hold that many keys and values.  Just filling the memory with\n// zeroes at init time is brutal when you get that big.\n// But why not be complete?\n// Maybe in the future, these limits will have expanded.\nconst getUintArray = (max) => !isPosInt(max)\n    ? null\n    : max <= Math.pow(2, 8)\n        ? Uint8Array\n        : max <= Math.pow(2, 16)\n            ? Uint16Array\n            : max <= Math.pow(2, 32)\n                ? Uint32Array\n                : max <= Number.MAX_SAFE_INTEGER\n                    ? ZeroArray\n                    : null;\n/* c8 ignore stop */\nclass ZeroArray extends Array {\n    constructor(size) {\n        super(size);\n        this.fill(0);\n    }\n}\nclass Stack {\n    heap;\n    length;\n    // private constructor\n    static #constructing = false;\n    static create(max) {\n        const HeapCls = getUintArray(max);\n        if (!HeapCls)\n            return [];\n        Stack.#constructing = true;\n        const s = new Stack(max, HeapCls);\n        Stack.#constructing = false;\n        return s;\n    }\n    constructor(max, HeapCls) {\n        /* c8 ignore start */\n        if (!Stack.#constructing) {\n            throw new TypeError('instantiate Stack using Stack.create(n)');\n        }\n        /* c8 ignore stop */\n        this.heap = new HeapCls(max);\n        this.length = 0;\n    }\n    push(n) {\n        this.heap[this.length++] = n;\n    }\n    pop() {\n        return this.heap[--this.length];\n    }\n}\n/**\n * Default export, the thing you're using this module to get.\n *\n * All properties from the options object (with the exception of\n * {@link OptionsBase.max} and {@link OptionsBase.maxSize}) are added as\n * normal public members. (`max` and `maxBase` are read-only getters.)\n * Changing any of these will alter the defaults for subsequent method calls,\n * but is otherwise safe.\n */\nexport class LRUCache {\n    // properties coming in from the options of these, only max and maxSize\n    // really *need* to be protected. The rest can be modified, as they just\n    // set defaults for various methods.\n    #max;\n    #maxSize;\n    #dispose;\n    #disposeAfter;\n    #fetchMethod;\n    /**\n     * {@link LRUCache.OptionsBase.ttl}\n     */\n    ttl;\n    /**\n     * {@link LRUCache.OptionsBase.ttlResolution}\n     */\n    ttlResolution;\n    /**\n     * {@link LRUCache.OptionsBase.ttlAutopurge}\n     */\n    ttlAutopurge;\n    /**\n     * {@link LRUCache.OptionsBase.updateAgeOnGet}\n     */\n    updateAgeOnGet;\n    /**\n     * {@link LRUCache.OptionsBase.updateAgeOnHas}\n     */\n    updateAgeOnHas;\n    /**\n     * {@link LRUCache.OptionsBase.allowStale}\n     */\n    allowStale;\n    /**\n     * {@link LRUCache.OptionsBase.noDisposeOnSet}\n     */\n    noDisposeOnSet;\n    /**\n     * {@link LRUCache.OptionsBase.noUpdateTTL}\n     */\n    noUpdateTTL;\n    /**\n     * {@link LRUCache.OptionsBase.maxEntrySize}\n     */\n    maxEntrySize;\n    /**\n     * {@link LRUCache.OptionsBase.sizeCalculation}\n     */\n    sizeCalculation;\n    /**\n     * {@link LRUCache.OptionsBase.noDeleteOnFetchRejection}\n     */\n    noDeleteOnFetchRejection;\n    /**\n     * {@link LRUCache.OptionsBase.noDeleteOnStaleGet}\n     */\n    noDeleteOnStaleGet;\n    /**\n     * {@link LRUCache.OptionsBase.allowStaleOnFetchAbort}\n     */\n    allowStaleOnFetchAbort;\n    /**\n     * {@link LRUCache.OptionsBase.allowStaleOnFetchRejection}\n     */\n    allowStaleOnFetchRejection;\n    /**\n     * {@link LRUCache.OptionsBase.ignoreFetchAbort}\n     */\n    ignoreFetchAbort;\n    // computed properties\n    #size;\n    #calculatedSize;\n    #keyMap;\n    #keyList;\n    #valList;\n    #next;\n    #prev;\n    #head;\n    #tail;\n    #free;\n    #disposed;\n    #sizes;\n    #starts;\n    #ttls;\n    #hasDispose;\n    #hasFetchMethod;\n    #hasDisposeAfter;\n    /**\n     * Do not call this method unless you need to inspect the\n     * inner workings of the cache.  If anything returned by this\n     * object is modified in any way, strange breakage may occur.\n     *\n     * These fields are private for a reason!\n     *\n     * @internal\n     */\n    static unsafeExposeInternals(c) {\n        return {\n            // properties\n            starts: c.#starts,\n            ttls: c.#ttls,\n            sizes: c.#sizes,\n            keyMap: c.#keyMap,\n            keyList: c.#keyList,\n            valList: c.#valList,\n            next: c.#next,\n            prev: c.#prev,\n            get head() {\n                return c.#head;\n            },\n            get tail() {\n                return c.#tail;\n            },\n            free: c.#free,\n            // methods\n            isBackgroundFetch: (p) => c.#isBackgroundFetch(p),\n            backgroundFetch: (k, index, options, context) => c.#backgroundFetch(k, index, options, context),\n            moveToTail: (index) => c.#moveToTail(index),\n            indexes: (options) => c.#indexes(options),\n            rindexes: (options) => c.#rindexes(options),\n            isStale: (index) => c.#isStale(index),\n        };\n    }\n    // Protected read-only members\n    /**\n     * {@link LRUCache.OptionsBase.max} (read-only)\n     */\n    get max() {\n        return this.#max;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.maxSize} (read-only)\n     */\n    get maxSize() {\n        return this.#maxSize;\n    }\n    /**\n     * The total computed size of items in the cache (read-only)\n     */\n    get calculatedSize() {\n        return this.#calculatedSize;\n    }\n    /**\n     * The number of items stored in the cache (read-only)\n     */\n    get size() {\n        return this.#size;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.fetchMethod} (read-only)\n     */\n    get fetchMethod() {\n        return this.#fetchMethod;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.dispose} (read-only)\n     */\n    get dispose() {\n        return this.#dispose;\n    }\n    /**\n     * {@link LRUCache.OptionsBase.disposeAfter} (read-only)\n     */\n    get disposeAfter() {\n        return this.#disposeAfter;\n    }\n    constructor(options) {\n        const { max = 0, ttl, ttlResolution = 1, ttlAutopurge, updateAgeOnGet, updateAgeOnHas, allowStale, dispose, disposeAfter, noDisposeOnSet, noUpdateTTL, maxSize = 0, maxEntrySize = 0, sizeCalculation, fetchMethod, noDeleteOnFetchRejection, noDeleteOnStaleGet, allowStaleOnFetchRejection, allowStaleOnFetchAbort, ignoreFetchAbort, } = options;\n        if (max !== 0 && !isPosInt(max)) {\n            throw new TypeError('max option must be a nonnegative integer');\n        }\n        const UintArray = max ? getUintArray(max) : Array;\n        if (!UintArray) {\n            throw new Error('invalid max value: ' + max);\n        }\n        this.#max = max;\n        this.#maxSize = maxSize;\n        this.maxEntrySize = maxEntrySize || this.#maxSize;\n        this.sizeCalculation = sizeCalculation;\n        if (this.sizeCalculation) {\n            if (!this.#maxSize && !this.maxEntrySize) {\n                throw new TypeError('cannot set sizeCalculation without setting maxSize or maxEntrySize');\n            }\n            if (typeof this.sizeCalculation !== 'function') {\n                throw new TypeError('sizeCalculation set to non-function');\n            }\n        }\n        if (fetchMethod !== undefined &&\n            typeof fetchMethod !== 'function') {\n            throw new TypeError('fetchMethod must be a function if specified');\n        }\n        this.#fetchMethod = fetchMethod;\n        this.#hasFetchMethod = !!fetchMethod;\n        this.#keyMap = new Map();\n        this.#keyList = new Array(max).fill(undefined);\n        this.#valList = new Array(max).fill(undefined);\n        this.#next = new UintArray(max);\n        this.#prev = new UintArray(max);\n        this.#head = 0;\n        this.#tail = 0;\n        this.#free = Stack.create(max);\n        this.#size = 0;\n        this.#calculatedSize = 0;\n        if (typeof dispose === 'function') {\n            this.#dispose = dispose;\n        }\n        if (typeof disposeAfter === 'function') {\n            this.#disposeAfter = disposeAfter;\n            this.#disposed = [];\n        }\n        else {\n            this.#disposeAfter = undefined;\n            this.#disposed = undefined;\n        }\n        this.#hasDispose = !!this.#dispose;\n        this.#hasDisposeAfter = !!this.#disposeAfter;\n        this.noDisposeOnSet = !!noDisposeOnSet;\n        this.noUpdateTTL = !!noUpdateTTL;\n        this.noDeleteOnFetchRejection = !!noDeleteOnFetchRejection;\n        this.allowStaleOnFetchRejection = !!allowStaleOnFetchRejection;\n        this.allowStaleOnFetchAbort = !!allowStaleOnFetchAbort;\n        this.ignoreFetchAbort = !!ignoreFetchAbort;\n        // NB: maxEntrySize is set to maxSize if it's set\n        if (this.maxEntrySize !== 0) {\n            if (this.#maxSize !== 0) {\n                if (!isPosInt(this.#maxSize)) {\n                    throw new TypeError('maxSize must be a positive integer if specified');\n                }\n            }\n            if (!isPosInt(this.maxEntrySize)) {\n                throw new TypeError('maxEntrySize must be a positive integer if specified');\n            }\n            this.#initializeSizeTracking();\n        }\n        this.allowStale = !!allowStale;\n        this.noDeleteOnStaleGet = !!noDeleteOnStaleGet;\n        this.updateAgeOnGet = !!updateAgeOnGet;\n        this.updateAgeOnHas = !!updateAgeOnHas;\n        this.ttlResolution =\n            isPosInt(ttlResolution) || ttlResolution === 0\n                ? ttlResolution\n                : 1;\n        this.ttlAutopurge = !!ttlAutopurge;\n        this.ttl = ttl || 0;\n        if (this.ttl) {\n            if (!isPosInt(this.ttl)) {\n                throw new TypeError('ttl must be a positive integer if specified');\n            }\n            this.#initializeTTLTracking();\n        }\n        // do not allow completely unbounded caches\n        if (this.#max === 0 && this.ttl === 0 && this.#maxSize === 0) {\n            throw new TypeError('At least one of max, maxSize, or ttl is required');\n        }\n        if (!this.ttlAutopurge && !this.#max && !this.#maxSize) {\n            const code = 'LRU_CACHE_UNBOUNDED';\n            if (shouldWarn(code)) {\n                warned.add(code);\n                const msg = 'TTL caching without ttlAutopurge, max, or maxSize can ' +\n                    'result in unbounded memory consumption.';\n                emitWarning(msg, 'UnboundedCacheWarning', code, LRUCache);\n            }\n        }\n    }\n    /**\n     * Return the remaining TTL time for a given entry key\n     */\n    getRemainingTTL(key) {\n        return this.#keyMap.has(key) ? Infinity : 0;\n    }\n    #initializeTTLTracking() {\n        const ttls = new ZeroArray(this.#max);\n        const starts = new ZeroArray(this.#max);\n        this.#ttls = ttls;\n        this.#starts = starts;\n        this.#setItemTTL = (index, ttl, start = perf.now()) => {\n            starts[index] = ttl !== 0 ? start : 0;\n            ttls[index] = ttl;\n            if (ttl !== 0 && this.ttlAutopurge) {\n                const t = setTimeout(() => {\n                    if (this.#isStale(index)) {\n                        this.delete(this.#keyList[index]);\n                    }\n                }, ttl + 1);\n                // unref() not supported on all platforms\n                /* c8 ignore start */\n                if (t.unref) {\n                    t.unref();\n                }\n                /* c8 ignore stop */\n            }\n        };\n        this.#updateItemAge = index => {\n            starts[index] = ttls[index] !== 0 ? perf.now() : 0;\n        };\n        this.#statusTTL = (status, index) => {\n            if (ttls[index]) {\n                const ttl = ttls[index];\n                const start = starts[index];\n                status.ttl = ttl;\n                status.start = start;\n                status.now = cachedNow || getNow();\n                const age = status.now - start;\n                status.remainingTTL = ttl - age;\n            }\n        };\n        // debounce calls to perf.now() to 1s so we're not hitting\n        // that costly call repeatedly.\n        let cachedNow = 0;\n        const getNow = () => {\n            const n = perf.now();\n            if (this.ttlResolution > 0) {\n                cachedNow = n;\n                const t = setTimeout(() => (cachedNow = 0), this.ttlResolution);\n                // not available on all platforms\n                /* c8 ignore start */\n                if (t.unref) {\n                    t.unref();\n                }\n                /* c8 ignore stop */\n            }\n            return n;\n        };\n        this.getRemainingTTL = key => {\n            const index = this.#keyMap.get(key);\n            if (index === undefined) {\n                return 0;\n            }\n            const ttl = ttls[index];\n            const start = starts[index];\n            if (ttl === 0 || start === 0) {\n                return Infinity;\n            }\n            const age = (cachedNow || getNow()) - start;\n            return ttl - age;\n        };\n        this.#isStale = index => {\n            return (ttls[index] !== 0 &&\n                starts[index] !== 0 &&\n                (cachedNow || getNow()) - starts[index] > ttls[index]);\n        };\n    }\n    // conditionally set private methods related to TTL\n    #updateItemAge = () => { };\n    #statusTTL = () => { };\n    #setItemTTL = () => { };\n    /* c8 ignore stop */\n    #isStale = () => false;\n    #initializeSizeTracking() {\n        const sizes = new ZeroArray(this.#max);\n        this.#calculatedSize = 0;\n        this.#sizes = sizes;\n        this.#removeItemSize = index => {\n            this.#calculatedSize -= sizes[index];\n            sizes[index] = 0;\n        };\n        this.#requireSize = (k, v, size, sizeCalculation) => {\n            // provisionally accept background fetches.\n            // actual value size will be checked when they return.\n            if (this.#isBackgroundFetch(v)) {\n                return 0;\n            }\n            if (!isPosInt(size)) {\n                if (sizeCalculation) {\n                    if (typeof sizeCalculation !== 'function') {\n                        throw new TypeError('sizeCalculation must be a function');\n                    }\n                    size = sizeCalculation(v, k);\n                    if (!isPosInt(size)) {\n                        throw new TypeError('sizeCalculation return invalid (expect positive integer)');\n                    }\n                }\n                else {\n                    throw new TypeError('invalid size value (must be positive integer). ' +\n                        'When maxSize or maxEntrySize is used, sizeCalculation ' +\n                        'or size must be set.');\n                }\n            }\n            return size;\n        };\n        this.#addItemSize = (index, size, status) => {\n            sizes[index] = size;\n            if (this.#maxSize) {\n                const maxSize = this.#maxSize - sizes[index];\n                while (this.#calculatedSize > maxSize) {\n                    this.#evict(true);\n                }\n            }\n            this.#calculatedSize += sizes[index];\n            if (status) {\n                status.entrySize = size;\n                status.totalCalculatedSize = this.#calculatedSize;\n            }\n        };\n    }\n    #removeItemSize = _i => { };\n    #addItemSize = (_i, _s, _st) => { };\n    #requireSize = (_k, _v, size, sizeCalculation) => {\n        if (size || sizeCalculation) {\n            throw new TypeError('cannot set size without setting maxSize or maxEntrySize on cache');\n        }\n        return 0;\n    };\n    *#indexes({ allowStale = this.allowStale } = {}) {\n        if (this.#size) {\n            for (let i = this.#tail; true;) {\n                if (!this.#isValidIndex(i)) {\n                    break;\n                }\n                if (allowStale || !this.#isStale(i)) {\n                    yield i;\n                }\n                if (i === this.#head) {\n                    break;\n                }\n                else {\n                    i = this.#prev[i];\n                }\n            }\n        }\n    }\n    *#rindexes({ allowStale = this.allowStale } = {}) {\n        if (this.#size) {\n            for (let i = this.#head; true;) {\n                if (!this.#isValidIndex(i)) {\n                    break;\n                }\n                if (allowStale || !this.#isStale(i)) {\n                    yield i;\n                }\n                if (i === this.#tail) {\n                    break;\n                }\n                else {\n                    i = this.#next[i];\n                }\n            }\n        }\n    }\n    #isValidIndex(index) {\n        return (index !== undefined &&\n            this.#keyMap.get(this.#keyList[index]) === index);\n    }\n    /**\n     * Return a generator yielding `[key, value]` pairs,\n     * in order from most recently used to least recently used.\n     */\n    *entries() {\n        for (const i of this.#indexes()) {\n            if (this.#valList[i] !== undefined &&\n                this.#keyList[i] !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield [this.#keyList[i], this.#valList[i]];\n            }\n        }\n    }\n    /**\n     * Inverse order version of {@link LRUCache.entries}\n     *\n     * Return a generator yielding `[key, value]` pairs,\n     * in order from least recently used to most recently used.\n     */\n    *rentries() {\n        for (const i of this.#rindexes()) {\n            if (this.#valList[i] !== undefined &&\n                this.#keyList[i] !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield [this.#keyList[i], this.#valList[i]];\n            }\n        }\n    }\n    /**\n     * Return a generator yielding the keys in the cache,\n     * in order from most recently used to least recently used.\n     */\n    *keys() {\n        for (const i of this.#indexes()) {\n            const k = this.#keyList[i];\n            if (k !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield k;\n            }\n        }\n    }\n    /**\n     * Inverse order version of {@link LRUCache.keys}\n     *\n     * Return a generator yielding the keys in the cache,\n     * in order from least recently used to most recently used.\n     */\n    *rkeys() {\n        for (const i of this.#rindexes()) {\n            const k = this.#keyList[i];\n            if (k !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield k;\n            }\n        }\n    }\n    /**\n     * Return a generator yielding the values in the cache,\n     * in order from most recently used to least recently used.\n     */\n    *values() {\n        for (const i of this.#indexes()) {\n            const v = this.#valList[i];\n            if (v !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield this.#valList[i];\n            }\n        }\n    }\n    /**\n     * Inverse order version of {@link LRUCache.values}\n     *\n     * Return a generator yielding the values in the cache,\n     * in order from least recently used to most recently used.\n     */\n    *rvalues() {\n        for (const i of this.#rindexes()) {\n            const v = this.#valList[i];\n            if (v !== undefined &&\n                !this.#isBackgroundFetch(this.#valList[i])) {\n                yield this.#valList[i];\n            }\n        }\n    }\n    /**\n     * Iterating over the cache itself yields the same results as\n     * {@link LRUCache.entries}\n     */\n    [Symbol.iterator]() {\n        return this.entries();\n    }\n    /**\n     * Find a value for which the supplied fn method returns a truthy value,\n     * similar to Array.find().  fn is called as fn(value, key, cache).\n     */\n    find(fn, getOptions = {}) {\n        for (const i of this.#indexes()) {\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined)\n                continue;\n            if (fn(value, this.#keyList[i], this)) {\n                return this.get(this.#keyList[i], getOptions);\n            }\n        }\n    }\n    /**\n     * Call the supplied function on each item in the cache, in order from\n     * most recently used to least recently used.  fn is called as\n     * fn(value, key, cache).  Does not update age or recenty of use.\n     * Does not iterate over stale values.\n     */\n    forEach(fn, thisp = this) {\n        for (const i of this.#indexes()) {\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined)\n                continue;\n            fn.call(thisp, value, this.#keyList[i], this);\n        }\n    }\n    /**\n     * The same as {@link LRUCache.forEach} but items are iterated over in\n     * reverse order.  (ie, less recently used items are iterated over first.)\n     */\n    rforEach(fn, thisp = this) {\n        for (const i of this.#rindexes()) {\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined)\n                continue;\n            fn.call(thisp, value, this.#keyList[i], this);\n        }\n    }\n    /**\n     * Delete any stale entries. Returns true if anything was removed,\n     * false otherwise.\n     */\n    purgeStale() {\n        let deleted = false;\n        for (const i of this.#rindexes({ allowStale: true })) {\n            if (this.#isStale(i)) {\n                this.delete(this.#keyList[i]);\n                deleted = true;\n            }\n        }\n        return deleted;\n    }\n    /**\n     * Return an array of [key, {@link LRUCache.Entry}] tuples which can be\n     * passed to cache.load()\n     */\n    dump() {\n        const arr = [];\n        for (const i of this.#indexes({ allowStale: true })) {\n            const key = this.#keyList[i];\n            const v = this.#valList[i];\n            const value = this.#isBackgroundFetch(v)\n                ? v.__staleWhileFetching\n                : v;\n            if (value === undefined || key === undefined)\n                continue;\n            const entry = { value };\n            if (this.#ttls && this.#starts) {\n                entry.ttl = this.#ttls[i];\n                // always dump the start relative to a portable timestamp\n                // it's ok for this to be a bit slow, it's a rare operation.\n                const age = perf.now() - this.#starts[i];\n                entry.start = Math.floor(Date.now() - age);\n            }\n            if (this.#sizes) {\n                entry.size = this.#sizes[i];\n            }\n            arr.unshift([key, entry]);\n        }\n        return arr;\n    }\n    /**\n     * Reset the cache and load in the items in entries in the order listed.\n     * Note that the shape of the resulting cache may be different if the\n     * same options are not used in both caches.\n     */\n    load(arr) {\n        this.clear();\n        for (const [key, entry] of arr) {\n            if (entry.start) {\n                // entry.start is a portable timestamp, but we may be using\n                // node's performance.now(), so calculate the offset, so that\n                // we get the intended remaining TTL, no matter how long it's\n                // been on ice.\n                //\n                // it's ok for this to be a bit slow, it's a rare operation.\n                const age = Date.now() - entry.start;\n                entry.start = perf.now() - age;\n            }\n            this.set(key, entry.value, entry);\n        }\n    }\n    /**\n     * Add a value to the cache.\n     *\n     * Note: if `undefined` is specified as a value, this is an alias for\n     * {@link LRUCache#delete}\n     */\n    set(k, v, setOptions = {}) {\n        if (v === undefined) {\n            this.delete(k);\n            return this;\n        }\n        const { ttl = this.ttl, start, noDisposeOnSet = this.noDisposeOnSet, sizeCalculation = this.sizeCalculation, status, } = setOptions;\n        let { noUpdateTTL = this.noUpdateTTL } = setOptions;\n        const size = this.#requireSize(k, v, setOptions.size || 0, sizeCalculation);\n        // if the item doesn't fit, don't do anything\n        // NB: maxEntrySize set to maxSize by default\n        if (this.maxEntrySize && size > this.maxEntrySize) {\n            if (status) {\n                status.set = 'miss';\n                status.maxEntrySizeExceeded = true;\n            }\n            // have to delete, in case something is there already.\n            this.delete(k);\n            return this;\n        }\n        let index = this.#size === 0 ? undefined : this.#keyMap.get(k);\n        if (index === undefined) {\n            // addition\n            index = (this.#size === 0\n                ? this.#tail\n                : this.#free.length !== 0\n                    ? this.#free.pop()\n                    : this.#size === this.#max\n                        ? this.#evict(false)\n                        : this.#size);\n            this.#keyList[index] = k;\n            this.#valList[index] = v;\n            this.#keyMap.set(k, index);\n            this.#next[this.#tail] = index;\n            this.#prev[index] = this.#tail;\n            this.#tail = index;\n            this.#size++;\n            this.#addItemSize(index, size, status);\n            if (status)\n                status.set = 'add';\n            noUpdateTTL = false;\n        }\n        else {\n            // update\n            this.#moveToTail(index);\n            const oldVal = this.#valList[index];\n            if (v !== oldVal) {\n                if (this.#hasFetchMethod && this.#isBackgroundFetch(oldVal)) {\n                    oldVal.__abortController.abort(new Error('replaced'));\n                    const { __staleWhileFetching: s } = oldVal;\n                    if (s !== undefined && !noDisposeOnSet) {\n                        if (this.#hasDispose) {\n                            this.#dispose?.(s, k, 'set');\n                        }\n                        if (this.#hasDisposeAfter) {\n                            this.#disposed?.push([s, k, 'set']);\n                        }\n                    }\n                }\n                else if (!noDisposeOnSet) {\n                    if (this.#hasDispose) {\n                        this.#dispose?.(oldVal, k, 'set');\n                    }\n                    if (this.#hasDisposeAfter) {\n                        this.#disposed?.push([oldVal, k, 'set']);\n                    }\n                }\n                this.#removeItemSize(index);\n                this.#addItemSize(index, size, status);\n                this.#valList[index] = v;\n                if (status) {\n                    status.set = 'replace';\n                    const oldValue = oldVal && this.#isBackgroundFetch(oldVal)\n                        ? oldVal.__staleWhileFetching\n                        : oldVal;\n                    if (oldValue !== undefined)\n                        status.oldValue = oldValue;\n                }\n            }\n            else if (status) {\n                status.set = 'update';\n            }\n        }\n        if (ttl !== 0 && !this.#ttls) {\n            this.#initializeTTLTracking();\n        }\n        if (this.#ttls) {\n            if (!noUpdateTTL) {\n                this.#setItemTTL(index, ttl, start);\n            }\n            if (status)\n                this.#statusTTL(status, index);\n        }\n        if (!noDisposeOnSet && this.#hasDisposeAfter && this.#disposed) {\n            const dt = this.#disposed;\n            let task;\n            while ((task = dt?.shift())) {\n                this.#disposeAfter?.(...task);\n            }\n        }\n        return this;\n    }\n    /**\n     * Evict the least recently used item, returning its value or\n     * `undefined` if cache is empty.\n     */\n    pop() {\n        try {\n            while (this.#size) {\n                const val = this.#valList[this.#head];\n                this.#evict(true);\n                if (this.#isBackgroundFetch(val)) {\n                    if (val.__staleWhileFetching) {\n                        return val.__staleWhileFetching;\n                    }\n                }\n                else if (val !== undefined) {\n                    return val;\n                }\n            }\n        }\n        finally {\n            if (this.#hasDisposeAfter && this.#disposed) {\n                const dt = this.#disposed;\n                let task;\n                while ((task = dt?.shift())) {\n                    this.#disposeAfter?.(...task);\n                }\n            }\n        }\n    }\n    #evict(free) {\n        const head = this.#head;\n        const k = this.#keyList[head];\n        const v = this.#valList[head];\n        if (this.#hasFetchMethod && this.#isBackgroundFetch(v)) {\n            v.__abortController.abort(new Error('evicted'));\n        }\n        else if (this.#hasDispose || this.#hasDisposeAfter) {\n            if (this.#hasDispose) {\n                this.#dispose?.(v, k, 'evict');\n            }\n            if (this.#hasDisposeAfter) {\n                this.#disposed?.push([v, k, 'evict']);\n            }\n        }\n        this.#removeItemSize(head);\n        // if we aren't about to use the index, then null these out\n        if (free) {\n            this.#keyList[head] = undefined;\n            this.#valList[head] = undefined;\n            this.#free.push(head);\n        }\n        if (this.#size === 1) {\n            this.#head = this.#tail = 0;\n            this.#free.length = 0;\n        }\n        else {\n            this.#head = this.#next[head];\n        }\n        this.#keyMap.delete(k);\n        this.#size--;\n        return head;\n    }\n    /**\n     * Check if a key is in the cache, without updating the recency of use.\n     * Will return false if the item is stale, even though it is technically\n     * in the cache.\n     *\n     * Will not update item age unless\n     * {@link LRUCache.OptionsBase.updateAgeOnHas} is set.\n     */\n    has(k, hasOptions = {}) {\n        const { updateAgeOnHas = this.updateAgeOnHas, status } = hasOptions;\n        const index = this.#keyMap.get(k);\n        if (index !== undefined) {\n            const v = this.#valList[index];\n            if (this.#isBackgroundFetch(v) &&\n                v.__staleWhileFetching === undefined) {\n                return false;\n            }\n            if (!this.#isStale(index)) {\n                if (updateAgeOnHas) {\n                    this.#updateItemAge(index);\n                }\n                if (status) {\n                    status.has = 'hit';\n                    this.#statusTTL(status, index);\n                }\n                return true;\n            }\n            else if (status) {\n                status.has = 'stale';\n                this.#statusTTL(status, index);\n            }\n        }\n        else if (status) {\n            status.has = 'miss';\n        }\n        return false;\n    }\n    /**\n     * Like {@link LRUCache#get} but doesn't update recency or delete stale\n     * items.\n     *\n     * Returns `undefined` if the item is stale, unless\n     * {@link LRUCache.OptionsBase.allowStale} is set.\n     */\n    peek(k, peekOptions = {}) {\n        const { allowStale = this.allowStale } = peekOptions;\n        const index = this.#keyMap.get(k);\n        if (index !== undefined &&\n            (allowStale || !this.#isStale(index))) {\n            const v = this.#valList[index];\n            // either stale and allowed, or forcing a refresh of non-stale value\n            return this.#isBackgroundFetch(v) ? v.__staleWhileFetching : v;\n        }\n    }\n    #backgroundFetch(k, index, options, context) {\n        const v = index === undefined ? undefined : this.#valList[index];\n        if (this.#isBackgroundFetch(v)) {\n            return v;\n        }\n        const ac = new AC();\n        const { signal } = options;\n        // when/if our AC signals, then stop listening to theirs.\n        signal?.addEventListener('abort', () => ac.abort(signal.reason), {\n            signal: ac.signal,\n        });\n        const fetchOpts = {\n            signal: ac.signal,\n            options,\n            context,\n        };\n        const cb = (v, updateCache = false) => {\n            const { aborted } = ac.signal;\n            const ignoreAbort = options.ignoreFetchAbort && v !== undefined;\n            if (options.status) {\n                if (aborted && !updateCache) {\n                    options.status.fetchAborted = true;\n                    options.status.fetchError = ac.signal.reason;\n                    if (ignoreAbort)\n                        options.status.fetchAbortIgnored = true;\n                }\n                else {\n                    options.status.fetchResolved = true;\n                }\n            }\n            if (aborted && !ignoreAbort && !updateCache) {\n                return fetchFail(ac.signal.reason);\n            }\n            // either we didn't abort, and are still here, or we did, and ignored\n            const bf = p;\n            if (this.#valList[index] === p) {\n                if (v === undefined) {\n                    if (bf.__staleWhileFetching) {\n                        this.#valList[index] = bf.__staleWhileFetching;\n                    }\n                    else {\n                        this.delete(k);\n                    }\n                }\n                else {\n                    if (options.status)\n                        options.status.fetchUpdated = true;\n                    this.set(k, v, fetchOpts.options);\n                }\n            }\n            return v;\n        };\n        const eb = (er) => {\n            if (options.status) {\n                options.status.fetchRejected = true;\n                options.status.fetchError = er;\n            }\n            return fetchFail(er);\n        };\n        const fetchFail = (er) => {\n            const { aborted } = ac.signal;\n            const allowStaleAborted = aborted && options.allowStaleOnFetchAbort;\n            const allowStale = allowStaleAborted || options.allowStaleOnFetchRejection;\n            const noDelete = allowStale || options.noDeleteOnFetchRejection;\n            const bf = p;\n            if (this.#valList[index] === p) {\n                // if we allow stale on fetch rejections, then we need to ensure that\n                // the stale value is not removed from the cache when the fetch fails.\n                const del = !noDelete || bf.__staleWhileFetching === undefined;\n                if (del) {\n                    this.delete(k);\n                }\n                else if (!allowStaleAborted) {\n                    // still replace the *promise* with the stale value,\n                    // since we are done with the promise at this point.\n                    // leave it untouched if we're still waiting for an\n                    // aborted background fetch that hasn't yet returned.\n                    this.#valList[index] = bf.__staleWhileFetching;\n                }\n            }\n            if (allowStale) {\n                if (options.status && bf.__staleWhileFetching !== undefined) {\n                    options.status.returnedStale = true;\n                }\n                return bf.__staleWhileFetching;\n            }\n            else if (bf.__returned === bf) {\n                throw er;\n            }\n        };\n        const pcall = (res, rej) => {\n            const fmp = this.#fetchMethod?.(k, v, fetchOpts);\n            if (fmp && fmp instanceof Promise) {\n                fmp.then(v => res(v === undefined ? undefined : v), rej);\n            }\n            // ignored, we go until we finish, regardless.\n            // defer check until we are actually aborting,\n            // so fetchMethod can override.\n            ac.signal.addEventListener('abort', () => {\n                if (!options.ignoreFetchAbort ||\n                    options.allowStaleOnFetchAbort) {\n                    res(undefined);\n                    // when it eventually resolves, update the cache.\n                    if (options.allowStaleOnFetchAbort) {\n                        res = v => cb(v, true);\n                    }\n                }\n            });\n        };\n        if (options.status)\n            options.status.fetchDispatched = true;\n        const p = new Promise(pcall).then(cb, eb);\n        const bf = Object.assign(p, {\n            __abortController: ac,\n            __staleWhileFetching: v,\n            __returned: undefined,\n        });\n        if (index === undefined) {\n            // internal, don't expose status.\n            this.set(k, bf, { ...fetchOpts.options, status: undefined });\n            index = this.#keyMap.get(k);\n        }\n        else {\n            this.#valList[index] = bf;\n        }\n        return bf;\n    }\n    #isBackgroundFetch(p) {\n        if (!this.#hasFetchMethod)\n            return false;\n        const b = p;\n        return (!!b &&\n            b instanceof Promise &&\n            b.hasOwnProperty('__staleWhileFetching') &&\n            b.__abortController instanceof AC);\n    }\n    async fetch(k, fetchOptions = {}) {\n        const { \n        // get options\n        allowStale = this.allowStale, updateAgeOnGet = this.updateAgeOnGet, noDeleteOnStaleGet = this.noDeleteOnStaleGet, \n        // set options\n        ttl = this.ttl, noDisposeOnSet = this.noDisposeOnSet, size = 0, sizeCalculation = this.sizeCalculation, noUpdateTTL = this.noUpdateTTL, \n        // fetch exclusive options\n        noDeleteOnFetchRejection = this.noDeleteOnFetchRejection, allowStaleOnFetchRejection = this.allowStaleOnFetchRejection, ignoreFetchAbort = this.ignoreFetchAbort, allowStaleOnFetchAbort = this.allowStaleOnFetchAbort, context, forceRefresh = false, status, signal, } = fetchOptions;\n        if (!this.#hasFetchMethod) {\n            if (status)\n                status.fetch = 'get';\n            return this.get(k, {\n                allowStale,\n                updateAgeOnGet,\n                noDeleteOnStaleGet,\n                status,\n            });\n        }\n        const options = {\n            allowStale,\n            updateAgeOnGet,\n            noDeleteOnStaleGet,\n            ttl,\n            noDisposeOnSet,\n            size,\n            sizeCalculation,\n            noUpdateTTL,\n            noDeleteOnFetchRejection,\n            allowStaleOnFetchRejection,\n            allowStaleOnFetchAbort,\n            ignoreFetchAbort,\n            status,\n            signal,\n        };\n        let index = this.#keyMap.get(k);\n        if (index === undefined) {\n            if (status)\n                status.fetch = 'miss';\n            const p = this.#backgroundFetch(k, index, options, context);\n            return (p.__returned = p);\n        }\n        else {\n            // in cache, maybe already fetching\n            const v = this.#valList[index];\n            if (this.#isBackgroundFetch(v)) {\n                const stale = allowStale && v.__staleWhileFetching !== undefined;\n                if (status) {\n                    status.fetch = 'inflight';\n                    if (stale)\n                        status.returnedStale = true;\n                }\n                return stale ? v.__staleWhileFetching : (v.__returned = v);\n            }\n            // if we force a refresh, that means do NOT serve the cached value,\n            // unless we are already in the process of refreshing the cache.\n            const isStale = this.#isStale(index);\n            if (!forceRefresh && !isStale) {\n                if (status)\n                    status.fetch = 'hit';\n                this.#moveToTail(index);\n                if (updateAgeOnGet) {\n                    this.#updateItemAge(index);\n                }\n                if (status)\n                    this.#statusTTL(status, index);\n                return v;\n            }\n            // ok, it is stale or a forced refresh, and not already fetching.\n            // refresh the cache.\n            const p = this.#backgroundFetch(k, index, options, context);\n            const hasStale = p.__staleWhileFetching !== undefined;\n            const staleVal = hasStale && allowStale;\n            if (status) {\n                status.fetch = isStale ? 'stale' : 'refresh';\n                if (staleVal && isStale)\n                    status.returnedStale = true;\n            }\n            return staleVal ? p.__staleWhileFetching : (p.__returned = p);\n        }\n    }\n    /**\n     * Return a value from the cache. Will update the recency of the cache\n     * entry found.\n     *\n     * If the key is not found, get() will return `undefined`.\n     */\n    get(k, getOptions = {}) {\n        const { allowStale = this.allowStale, updateAgeOnGet = this.updateAgeOnGet, noDeleteOnStaleGet = this.noDeleteOnStaleGet, status, } = getOptions;\n        const index = this.#keyMap.get(k);\n        if (index !== undefined) {\n            const value = this.#valList[index];\n            const fetching = this.#isBackgroundFetch(value);\n            if (status)\n                this.#statusTTL(status, index);\n            if (this.#isStale(index)) {\n                if (status)\n                    status.get = 'stale';\n                // delete only if not an in-flight background fetch\n                if (!fetching) {\n                    if (!noDeleteOnStaleGet) {\n                        this.delete(k);\n                    }\n                    if (status && allowStale)\n                        status.returnedStale = true;\n                    return allowStale ? value : undefined;\n                }\n                else {\n                    if (status &&\n                        allowStale &&\n                        value.__staleWhileFetching !== undefined) {\n                        status.returnedStale = true;\n                    }\n                    return allowStale ? value.__staleWhileFetching : undefined;\n                }\n            }\n            else {\n                if (status)\n                    status.get = 'hit';\n                // if we're currently fetching it, we don't actually have it yet\n                // it's not stale, which means this isn't a staleWhileRefetching.\n                // If it's not stale, and fetching, AND has a __staleWhileFetching\n                // value, then that means the user fetched with {forceRefresh:true},\n                // so it's safe to return that value.\n                if (fetching) {\n                    return value.__staleWhileFetching;\n                }\n                this.#moveToTail(index);\n                if (updateAgeOnGet) {\n                    this.#updateItemAge(index);\n                }\n                return value;\n            }\n        }\n        else if (status) {\n            status.get = 'miss';\n        }\n    }\n    #connect(p, n) {\n        this.#prev[n] = p;\n        this.#next[p] = n;\n    }\n    #moveToTail(index) {\n        // if tail already, nothing to do\n        // if head, move head to next[index]\n        // else\n        //   move next[prev[index]] to next[index] (head has no prev)\n        //   move prev[next[index]] to prev[index]\n        // prev[index] = tail\n        // next[tail] = index\n        // tail = index\n        if (index !== this.#tail) {\n            if (index === this.#head) {\n                this.#head = this.#next[index];\n            }\n            else {\n                this.#connect(this.#prev[index], this.#next[index]);\n            }\n            this.#connect(this.#tail, index);\n            this.#tail = index;\n        }\n    }\n    /**\n     * Deletes a key out of the cache.\n     * Returns true if the key was deleted, false otherwise.\n     */\n    delete(k) {\n        let deleted = false;\n        if (this.#size !== 0) {\n            const index = this.#keyMap.get(k);\n            if (index !== undefined) {\n                deleted = true;\n                if (this.#size === 1) {\n                    this.clear();\n                }\n                else {\n                    this.#removeItemSize(index);\n                    const v = this.#valList[index];\n                    if (this.#isBackgroundFetch(v)) {\n                        v.__abortController.abort(new Error('deleted'));\n                    }\n                    else if (this.#hasDispose || this.#hasDisposeAfter) {\n                        if (this.#hasDispose) {\n                            this.#dispose?.(v, k, 'delete');\n                        }\n                        if (this.#hasDisposeAfter) {\n                            this.#disposed?.push([v, k, 'delete']);\n                        }\n                    }\n                    this.#keyMap.delete(k);\n                    this.#keyList[index] = undefined;\n                    this.#valList[index] = undefined;\n                    if (index === this.#tail) {\n                        this.#tail = this.#prev[index];\n                    }\n                    else if (index === this.#head) {\n                        this.#head = this.#next[index];\n                    }\n                    else {\n                        this.#next[this.#prev[index]] = this.#next[index];\n                        this.#prev[this.#next[index]] = this.#prev[index];\n                    }\n                    this.#size--;\n                    this.#free.push(index);\n                }\n            }\n        }\n        if (this.#hasDisposeAfter && this.#disposed?.length) {\n            const dt = this.#disposed;\n            let task;\n            while ((task = dt?.shift())) {\n                this.#disposeAfter?.(...task);\n            }\n        }\n        return deleted;\n    }\n    /**\n     * Clear the cache entirely, throwing away all values.\n     */\n    clear() {\n        for (const index of this.#rindexes({ allowStale: true })) {\n            const v = this.#valList[index];\n            if (this.#isBackgroundFetch(v)) {\n                v.__abortController.abort(new Error('deleted'));\n            }\n            else {\n                const k = this.#keyList[index];\n                if (this.#hasDispose) {\n                    this.#dispose?.(v, k, 'delete');\n                }\n                if (this.#hasDisposeAfter) {\n                    this.#disposed?.push([v, k, 'delete']);\n                }\n            }\n        }\n        this.#keyMap.clear();\n        this.#valList.fill(undefined);\n        this.#keyList.fill(undefined);\n        if (this.#ttls && this.#starts) {\n            this.#ttls.fill(0);\n            this.#starts.fill(0);\n        }\n        if (this.#sizes) {\n            this.#sizes.fill(0);\n        }\n        this.#head = 0;\n        this.#tail = 0;\n        this.#free.length = 0;\n        this.#calculatedSize = 0;\n        this.#size = 0;\n        if (this.#hasDisposeAfter && this.#disposed) {\n            const dt = this.#disposed;\n            let task;\n            while ((task = dt?.shift())) {\n                this.#disposeAfter?.(...task);\n            }\n        }\n    }\n}\n//# sourceMappingURL=index.js.map", "'use strict';\n\n// FNV_PRIMES and FNV_OFFSETS from\n// http://www.isthe.com/chongo/tech/comp/fnv/index.html#FNV-param\n\nconst FNV_PRIMES = {\n\t32: 16777619n,\n\t64: 1099511628211n,\n\t128: 309485009821345068724781371n,\n\t256: 374144419156711147060143317175368453031918731002211n,\n\t512: 35835915874844867368919076489095108449946327955754392558399825615420669938882575126094039892345713852759n,\n\t1024: 5016456510113118655434598811035278955030765345404790744303017523831112055108147451509157692220295382716162651878526895249385292291816524375083746691371804094271873160484737966720260389217684476157468082573n\n};\n\nconst FNV_OFFSETS = {\n\t32: 2166136261n,\n\t64: 14695981039346656037n,\n\t128: 144066263297769815596495629667062367629n,\n\t256: 100029257958052580907070968620625704837092796014241193945225284501741471925557n,\n\t512: 9659303129496669498009435400716310466090418745672637896108374329434462657994582932197716438449813051892206539805784495328239340083876191928701583869517785n,\n\t1024: 14197795064947621068722070641403218320880622795441933960878474914617582723252296732303717722150864096521202355549365628174669108571814760471015076148029755969804077320157692458563003215304957150157403644460363550505412711285966361610267868082893823963790439336411086884584107735010676915n\n};\n\n// Legacy implementation for 32-bit + number types\nfunction fnv1a(string) {\n\t// Handle Unicode code points > 0x7f\n\tlet hash = Number(FNV_OFFSETS[32]);\n\tlet isUnicoded = false;\n\n\tfor (let i = 0; i < string.length; i++) {\n\t\tlet characterCode = string.charCodeAt(i);\n\n\t\t// Non-ASCII characters trigger the Unicode escape logic\n\t\tif (characterCode > 0x7F && !isUnicoded) {\n\t\t\tstring = unescape(encodeURIComponent(string));\n\t\t\tcharacterCode = string.charCodeAt(i);\n\t\t\tisUnicoded = true;\n\t\t}\n\n\t\thash ^= characterCode;\n\t\thash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);\n\t}\n\n\treturn hash >>> 0;\n}\n\nfunction bigInt(string, {size = 32} = {}) {\n\tif (!FNV_PRIMES[size]) {\n\t\tthrow new Error('The `size` option must be one of 32, 64, 128, 256, 512, or 1024');\n\t}\n\n\tlet hash = FNV_OFFSETS[size];\n\tconst fnvPrime = FNV_PRIMES[size];\n\n\t// Handle Unicode code points > 0x7f\n\tlet isUnicoded = false;\n\n\tfor (let i = 0; i < string.length; i++) {\n\t\tlet characterCode = string.charCodeAt(i);\n\n\t\t// Non-ASCII characters trigger the Unicode escape logic\n\t\tif (characterCode > 0x7F && !isUnicoded) {\n\t\t\tstring = unescape(encodeURIComponent(string));\n\t\t\tcharacterCode = string.charCodeAt(i);\n\t\t\tisUnicoded = true;\n\t\t}\n\n\t\thash ^= BigInt(characterCode);\n\t\thash = BigInt.asUintN(size, hash * fnvPrime);\n\t}\n\n\treturn hash;\n}\n\nmodule.exports = fnv1a;\nmodule.exports.bigInt = bigInt;\n", "import { ts } from './ts';\nimport { parse, visit } from 'graphql';\n\nimport { findNode } from './ast';\nimport { getValueOfIdentifier } from './ast/declaration';\n\nexport const UNUSED_FIELD_CODE = 52005;\n\nconst unwrapAbstractType = (type: ts.Type) => {\n  return type.isUnionOrIntersection()\n    ? type.types.find(type => type.flags & ts.TypeFlags.Object) || type\n    : type;\n};\n\nconst getVariableDeclaration = (\n  start: ts.Node\n): ts.VariableDeclaration | undefined => {\n  let node: ts.Node = start;\n  const seen = new Set();\n  while (node.parent && !seen.has(node)) {\n    seen.add(node);\n    if (ts.isBlock(node)) {\n      return; // NOTE: We never want to traverse up into a new function/module block\n    } else if (ts.isVariableDeclaration((node = node.parent))) {\n      return node;\n    }\n  }\n};\n\nconst traverseArrayDestructuring = (\n  node: ts.ArrayBindingPattern,\n  originalWip: Array<string>,\n  allFields: Array<string>,\n  source: ts.SourceFile,\n  info: ts.server.PluginCreateInfo\n): Array<string> => {\n  return node.elements.flatMap(element => {\n    if (ts.isOmittedExpression(element)) return [];\n\n    const wip = [...originalWip];\n    return ts.isIdentifier(element.name)\n      ? crawlScope(element.name, wip, allFields, source, info, false)\n      : ts.isObjectBindingPattern(element.name)\n      ? traverseDestructuring(element.name, wip, allFields, source, info)\n      : traverseArrayDestructuring(element.name, wip, allFields, source, info);\n  });\n};\n\nconst traverseDestructuring = (\n  node: ts.ObjectBindingPattern,\n  originalWip: Array<string>,\n  allFields: Array<string>,\n  source: ts.SourceFile,\n  info: ts.server.PluginCreateInfo\n): Array<string> => {\n  const results = [];\n  for (const binding of node.elements) {\n    if (ts.isObjectBindingPattern(binding.name)) {\n      const wip = [...originalWip];\n      if (\n        binding.propertyName &&\n        !originalWip.includes(binding.propertyName.getText())\n      ) {\n        const joined = [...wip, binding.propertyName.getText()].join('.');\n        if (allFields.find(x => x.startsWith(joined))) {\n          wip.push(binding.propertyName.getText());\n        }\n      }\n      const traverseResult = traverseDestructuring(\n        binding.name,\n        wip,\n        allFields,\n        source,\n        info\n      );\n\n      results.push(...traverseResult);\n    } else if (ts.isIdentifier(binding.name)) {\n      const wip = [...originalWip];\n      if (\n        binding.propertyName &&\n        !originalWip.includes(binding.propertyName.getText())\n      ) {\n        const joined = [...wip, binding.propertyName.getText()].join('.');\n        if (allFields.find(x => x.startsWith(joined))) {\n          wip.push(binding.propertyName.getText());\n        }\n      } else {\n        const joined = [...wip, binding.name.getText()].join('.');\n        if (allFields.find(x => x.startsWith(joined))) {\n          wip.push(binding.name.getText());\n        }\n      }\n\n      const crawlResult = crawlScope(\n        binding.name,\n        wip,\n        allFields,\n        source,\n        info,\n        false\n      );\n\n      results.push(...crawlResult);\n    }\n  }\n\n  return results;\n};\n\nconst arrayMethods = new Set([\n  'map',\n  'filter',\n  'forEach',\n  'reduce',\n  'every',\n  'some',\n  'find',\n  'flatMap',\n  'sort',\n]);\n\nconst crawlChainedExpressions = (\n  ref: ts.CallExpression,\n  pathParts: string[],\n  allFields: string[],\n  source: ts.SourceFile,\n  info: ts.server.PluginCreateInfo\n): string[] => {\n  const isChained =\n    ts.isPropertyAccessExpression(ref.expression) &&\n    arrayMethods.has(ref.expression.name.text);\n  if (isChained) {\n    const foundRef = ref.expression;\n    const isReduce = foundRef.name.text === 'reduce';\n    let func: ts.Expression | ts.FunctionDeclaration | undefined =\n      ref.arguments[0];\n\n    const res = [];\n    if (ts.isCallExpression(ref.parent.parent)) {\n      const nestedResult = crawlChainedExpressions(\n        ref.parent.parent,\n        pathParts,\n        allFields,\n        source,\n        info\n      );\n      if (nestedResult.length) {\n        res.push(...nestedResult);\n      }\n    }\n\n    if (func && ts.isIdentifier(func)) {\n      // TODO: Scope utilities in checkFieldUsageInFile to deduplicate\n      const checker = info.languageService.getProgram()!.getTypeChecker();\n\n      const value = getValueOfIdentifier(func, checker);\n      if (\n        value &&\n        (ts.isFunctionDeclaration(value) ||\n          ts.isFunctionExpression(value) ||\n          ts.isArrowFunction(value))\n      ) {\n        func = value;\n      }\n    }\n\n    if (\n      func &&\n      (ts.isFunctionDeclaration(func) ||\n        ts.isFunctionExpression(func) ||\n        ts.isArrowFunction(func))\n    ) {\n      const param = func.parameters[isReduce ? 1 : 0];\n      if (param) {\n        const scopedResult = crawlScope(\n          param.name,\n          pathParts,\n          allFields,\n          source,\n          info,\n          true\n        );\n\n        if (scopedResult.length) {\n          res.push(...scopedResult);\n        }\n      }\n    }\n\n    return res;\n  }\n\n  return [];\n};\n\nconst crawlScope = (\n  node: ts.BindingName,\n  originalWip: Array<string>,\n  allFields: Array<string>,\n  source: ts.SourceFile,\n  info: ts.server.PluginCreateInfo,\n  inArrayMethod: boolean\n): Array<string> => {\n  if (ts.isObjectBindingPattern(node)) {\n    return traverseDestructuring(node, originalWip, allFields, source, info);\n  } else if (ts.isArrayBindingPattern(node)) {\n    return traverseArrayDestructuring(\n      node,\n      originalWip,\n      allFields,\n      source,\n      info\n    );\n  }\n\n  let results: string[] = [];\n\n  const references = info.languageService.getReferencesAtPosition(\n    source.fileName,\n    node.getStart()\n  );\n\n  if (!references) return results;\n\n  // Go over all the references tied to the result of\n  // accessing our equery and collect them as fully\n  // qualified paths (ideally ending in a leaf-node)\n  results = references.flatMap(ref => {\n    // If we get a reference to a different file we can bail\n    if (ref.fileName !== source.fileName) return [];\n    // We don't want to end back at our document so we narrow\n    // the scope.\n    if (\n      node.getStart() <= ref.textSpan.start &&\n      node.getEnd() >= ref.textSpan.start + ref.textSpan.length\n    )\n      return [];\n\n    let foundRef = findNode(source, ref.textSpan.start);\n    if (!foundRef) return [];\n\n    const pathParts = [...originalWip];\n    // In here we'll start crawling all the accessors of result\n    // and try to determine the total path\n    // - result.data.pokemon.name --> pokemon.name this is the easy route and never accesses\n    //   any of the recursive functions\n    // - const pokemon = result.data.pokemon --> this initiates a new crawl with a renewed scope\n    // - const { pokemon } = result.data --> this initiates a destructuring traversal which will\n    //   either end up in more destructuring traversals or a scope crawl\n    while (\n      ts.isIdentifier(foundRef) ||\n      ts.isPropertyAccessExpression(foundRef) ||\n      ts.isElementAccessExpression(foundRef) ||\n      ts.isVariableDeclaration(foundRef) ||\n      ts.isBinaryExpression(foundRef) ||\n      ts.isReturnStatement(foundRef) ||\n      ts.isArrowFunction(foundRef)\n    ) {\n      if (\n        !inArrayMethod &&\n        (ts.isReturnStatement(foundRef) || ts.isArrowFunction(foundRef))\n      ) {\n        // When we are returning the ref or we are dealing with an implicit return\n        // we mark all its children as used (bail scenario)\n        const joined = pathParts.join('.');\n        const bailedFields = allFields.filter(x => x.startsWith(joined + '.'));\n        return bailedFields;\n      } else if (ts.isVariableDeclaration(foundRef)) {\n        return crawlScope(\n          foundRef.name,\n          pathParts,\n          allFields,\n          source,\n          info,\n          false\n        );\n      } else if (\n        ts.isIdentifier(foundRef) &&\n        !pathParts.includes(foundRef.text)\n      ) {\n        const joined = [...pathParts, foundRef.text].join('.');\n        if (allFields.find(x => x.startsWith(joined + '.'))) {\n          pathParts.push(foundRef.text);\n        }\n      } else if (\n        ts.isPropertyAccessExpression(foundRef) &&\n        foundRef.name.text === 'at' &&\n        ts.isCallExpression(foundRef.parent)\n      ) {\n        foundRef = foundRef.parent;\n      } else if (\n        ts.isPropertyAccessExpression(foundRef) &&\n        arrayMethods.has(foundRef.name.text) &&\n        ts.isCallExpression(foundRef.parent)\n      ) {\n        const callExpression = foundRef.parent;\n        const res = [];\n        const isSomeOrEvery =\n          foundRef.name.text === 'some' || foundRef.name.text === 'every';\n        const chainedResults = crawlChainedExpressions(\n          callExpression,\n          pathParts,\n          allFields,\n          source,\n          info\n        );\n        if (chainedResults.length) {\n          res.push(...chainedResults);\n        }\n\n        if (ts.isVariableDeclaration(callExpression.parent) && !isSomeOrEvery) {\n          const varRes = crawlScope(\n            callExpression.parent.name,\n            pathParts,\n            allFields,\n            source,\n            info,\n            true\n          );\n          res.push(...varRes);\n        }\n\n        return res;\n      } else if (\n        ts.isPropertyAccessExpression(foundRef) &&\n        !pathParts.includes(foundRef.name.text)\n      ) {\n        const joined = [...pathParts, foundRef.name.text].join('.');\n        if (allFields.find(x => x.startsWith(joined))) {\n          pathParts.push(foundRef.name.text);\n        }\n      } else if (\n        ts.isElementAccessExpression(foundRef) &&\n        ts.isStringLiteral(foundRef.argumentExpression) &&\n        !pathParts.includes(foundRef.argumentExpression.text)\n      ) {\n        const joined = [...pathParts, foundRef.argumentExpression.text].join(\n          '.'\n        );\n        if (allFields.find(x => x.startsWith(joined))) {\n          pathParts.push(foundRef.argumentExpression.text);\n        }\n      }\n\n      if (ts.isNonNullExpression(foundRef.parent)) {\n        foundRef = foundRef.parent.parent;\n      } else {\n        foundRef = foundRef.parent;\n      }\n    }\n\n    return pathParts.join('.');\n  });\n\n  return results;\n};\n\nexport const checkFieldUsageInFile = (\n  source: ts.SourceFile,\n  nodes: ts.NoSubstitutionTemplateLiteral[],\n  info: ts.server.PluginCreateInfo\n) => {\n  const diagnostics: ts.Diagnostic[] = [];\n  const shouldTrackFieldUsage = info.config.trackFieldUsage ?? true;\n  if (!shouldTrackFieldUsage) return diagnostics;\n\n  const defaultReservedKeys = ['id', '_id', '__typename'];\n  const additionalKeys = info.config.reservedKeys ?? [];\n  const reservedKeys = new Set([...defaultReservedKeys, ...additionalKeys]);\n  const checker = info.languageService.getProgram()?.getTypeChecker();\n  if (!checker) return;\n\n  try {\n    nodes.forEach(node => {\n      const nodeText = node.getText();\n      // Bailing for mutations/subscriptions as these could have small details\n      // for normalised cache interactions\n      if (nodeText.includes('mutation') || nodeText.includes('subscription'))\n        return;\n\n      const variableDeclaration = getVariableDeclaration(node);\n      if (!variableDeclaration) return;\n\n      let dataType: ts.Type | undefined;\n\n      const type = checker.getTypeAtLocation(node.parent) as\n        | ts.TypeReference\n        | ts.Type;\n      // Attempt to retrieve type from internally resolve type arguments\n      if ('target' in type) {\n        const typeArguments = (type as any)\n          .resolvedTypeArguments as readonly ts.Type[];\n        dataType =\n          typeArguments && typeArguments.length > 1\n            ? typeArguments[0]\n            : undefined;\n      }\n      // Fallback to resolving the type from scratch\n      if (!dataType) {\n        const apiTypeSymbol = type.getProperty('__apiType');\n        if (apiTypeSymbol) {\n          let apiType = checker.getTypeOfSymbol(apiTypeSymbol);\n          let callSignature: ts.Signature | undefined =\n            type.getCallSignatures()[0];\n          if (apiType.isUnionOrIntersection()) {\n            for (const type of apiType.types) {\n              callSignature = type.getCallSignatures()[0];\n              if (callSignature) {\n                dataType = callSignature.getReturnType();\n                break;\n              }\n            }\n          }\n          dataType = callSignature && callSignature.getReturnType();\n        }\n      }\n\n      const references = info.languageService.getReferencesAtPosition(\n        source.fileName,\n        variableDeclaration.name.getStart()\n      );\n\n      if (!references) return;\n\n      const allAccess: string[] = [];\n      const inProgress: string[] = [];\n      const allPaths: string[] = [];\n      const fieldToLoc = new Map<string, { start: number; length: number }>();\n      // This visitor gets all the leaf-paths in the document\n      // as well as all fields that are part of the document\n      // We need the leaf-paths to check usage and we need the\n      // fields to validate whether an access on a given reference\n      // is valid given the current document...\n      visit(parse(node.getText().slice(1, -1)), {\n        Field: {\n          enter(node) {\n            const alias = node.alias ? node.alias.value : node.name.value;\n            const path = inProgress.length\n              ? `${inProgress.join('.')}.${alias}`\n              : alias;\n\n            if (!node.selectionSet && !reservedKeys.has(node.name.value)) {\n              allPaths.push(path);\n              fieldToLoc.set(path, {\n                start: node.name.loc!.start,\n                length: node.name.loc!.end - node.name.loc!.start,\n              });\n            } else if (node.selectionSet) {\n              inProgress.push(alias);\n              fieldToLoc.set(path, {\n                start: node.name.loc!.start,\n                length: node.name.loc!.end - node.name.loc!.start,\n              });\n            }\n          },\n          leave(node) {\n            if (node.selectionSet) {\n              inProgress.pop();\n            }\n          },\n        },\n      });\n\n      references.forEach(ref => {\n        if (ref.fileName !== source.fileName) return;\n\n        const targetNode = findNode(source, ref.textSpan.start);\n        if (!targetNode) return;\n        // Skip declaration as reference of itself\n        if (targetNode.parent === variableDeclaration) return;\n\n        const scopeSymbols = checker.getSymbolsInScope(\n          targetNode,\n          ts.SymbolFlags.BlockScopedVariable\n        );\n\n        let scopeDataSymbol: ts.Symbol | undefined;\n        for (let scopeSymbol of scopeSymbols) {\n          if (!scopeSymbol.valueDeclaration) continue;\n          let typeOfScopeSymbol = unwrapAbstractType(\n            checker.getTypeOfSymbol(scopeSymbol)\n          );\n          if (dataType === typeOfScopeSymbol) {\n            scopeDataSymbol = scopeSymbol;\n            break;\n          }\n\n          // NOTE: This is an aggressive fallback for hooks where the return value isn't destructured\n          // This is a last resort solution for patterns like react-query, where the fallback that\n          // would otherwise happen below isn't sufficient\n          if (typeOfScopeSymbol.flags & ts.TypeFlags.Object) {\n            const tuplePropertySymbol = typeOfScopeSymbol.getProperty('0');\n            if (tuplePropertySymbol) {\n              typeOfScopeSymbol = checker.getTypeOfSymbol(tuplePropertySymbol);\n              if (dataType === typeOfScopeSymbol) {\n                scopeDataSymbol = scopeSymbol;\n                break;\n              }\n            }\n\n            const dataPropertySymbol = typeOfScopeSymbol.getProperty('data');\n            if (dataPropertySymbol) {\n              typeOfScopeSymbol = unwrapAbstractType(\n                checker.getTypeOfSymbol(dataPropertySymbol)\n              );\n              if (dataType === typeOfScopeSymbol) {\n                scopeDataSymbol = scopeSymbol;\n                break;\n              }\n            }\n          }\n        }\n\n        const valueDeclaration = scopeDataSymbol?.valueDeclaration;\n        let name: ts.BindingName | undefined;\n        if (\n          valueDeclaration &&\n          'name' in valueDeclaration &&\n          !!valueDeclaration.name &&\n          (ts.isIdentifier(valueDeclaration.name as any) ||\n            ts.isBindingName(valueDeclaration.name as any))\n        ) {\n          name = valueDeclaration.name as ts.BindingName;\n        } else {\n          // Fall back to looking at the variable declaration directly,\n          // if we are on one.\n          const variableDeclaration = getVariableDeclaration(targetNode);\n          if (variableDeclaration) name = variableDeclaration.name;\n        }\n\n        if (name) {\n          const result = crawlScope(name, [], allPaths, source, info, false);\n          allAccess.push(...result);\n        }\n      });\n\n      if (!allAccess.length) {\n        return;\n      }\n\n      const unused = allPaths.filter(x => !allAccess.includes(x));\n      const aggregatedUnusedFields = new Set<string>();\n      const unusedChildren: { [key: string]: Set<string> } = {};\n      const unusedFragmentLeaf = new Set<string>();\n      unused.forEach(unusedField => {\n        const split = unusedField.split('.');\n        split.pop();\n        const parentField = split.join('.');\n        const loc = fieldToLoc.get(parentField);\n\n        if (loc) {\n          aggregatedUnusedFields.add(parentField);\n          if (unusedChildren[parentField]) {\n            unusedChildren[parentField]!.add(unusedField);\n          } else {\n            unusedChildren[parentField] = new Set([unusedField]);\n          }\n        } else {\n          unusedFragmentLeaf.add(unusedField);\n        }\n      });\n\n      aggregatedUnusedFields.forEach(field => {\n        const loc = fieldToLoc.get(field)!;\n        const unusedFields = unusedChildren[field]!;\n        diagnostics.push({\n          file: source,\n          length: loc.length,\n          start: node.getStart() + loc.start + 1,\n          category: ts.DiagnosticCategory.Warning,\n          code: UNUSED_FIELD_CODE,\n          messageText: `Field(s) ${[...unusedFields]\n            .map(x => `'${x}'`)\n            .join(', ')} are not used.`,\n        });\n      });\n\n      unusedFragmentLeaf.forEach(field => {\n        const loc = fieldToLoc.get(field)!;\n        diagnostics.push({\n          file: source,\n          length: loc.length,\n          start: node.getStart() + loc.start + 1,\n          category: ts.DiagnosticCategory.Warning,\n          code: UNUSED_FIELD_CODE,\n          messageText: `Field ${field} is not used.`,\n        });\n      });\n    });\n  } catch (e: any) {\n    console.error('[GraphQLSP]: ', e.message, e.stack);\n  }\n\n  return diagnostics;\n};\n", "import { ts } from './ts';\nimport { FragmentDefinitionNode, Kind, parse } from 'graphql';\n\nimport { findAllCallExpressions, findAllImports } from './ast';\nimport { resolveTemplate } from './ast/resolve';\nimport { getDeclarationOfIdentifier } from './ast/declaration';\n\nexport const MISSING_FRAGMENT_CODE = 52003;\n\nexport const getColocatedFragmentNames = (\n  source: ts.SourceFile,\n  info: ts.server.PluginCreateInfo\n): Record<\n  string,\n  { start: number; length: number; fragments: Array<string> }\n> => {\n  const imports = findAllImports(source);\n  const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n\n  const importSpecifierToFragments: Record<\n    string,\n    { start: number; length: number; fragments: Array<string> }\n  > = {};\n\n  if (!typeChecker) return importSpecifierToFragments;\n\n  if (imports.length) {\n    imports.forEach(imp => {\n      if (!imp.importClause) return;\n\n      if (imp.importClause.name) {\n        const declaration = getDeclarationOfIdentifier(\n          imp.importClause.name,\n          typeChecker\n        );\n        if (declaration) {\n          const sourceFile = declaration.getSourceFile();\n          if (sourceFile.fileName.includes('node_modules')) return;\n\n          const externalSource = sourceFile;\n          if (!externalSource) return;\n\n          const fragmentsForImport = getFragmentsInSource(\n            externalSource,\n            typeChecker,\n            info\n          );\n\n          const names = fragmentsForImport.map(fragment => fragment.name.value);\n          const key = imp.moduleSpecifier.getText();\n          let fragmentsEntry = importSpecifierToFragments[key];\n          if (names.length && fragmentsEntry) {\n            fragmentsEntry.fragments = fragmentsEntry.fragments.concat(names);\n          } else if (names.length && !fragmentsEntry) {\n            importSpecifierToFragments[key] = fragmentsEntry = {\n              start: imp.moduleSpecifier.getStart(),\n              length: imp.moduleSpecifier.getText().length,\n              fragments: names,\n            };\n          }\n        }\n      }\n\n      if (\n        imp.importClause.namedBindings &&\n        ts.isNamespaceImport(imp.importClause.namedBindings)\n      ) {\n        const declaration = getDeclarationOfIdentifier(\n          imp.importClause.namedBindings.name,\n          typeChecker\n        );\n        if (declaration) {\n          const sourceFile = declaration.getSourceFile();\n          if (sourceFile.fileName.includes('node_modules')) return;\n\n          const externalSource = sourceFile;\n          if (!externalSource) return;\n\n          const fragmentsForImport = getFragmentsInSource(\n            externalSource,\n            typeChecker,\n            info\n          );\n          const names = fragmentsForImport.map(fragment => fragment.name.value);\n          const key = imp.moduleSpecifier.getText();\n          let fragmentsEntry = importSpecifierToFragments[key];\n          if (names.length && fragmentsEntry) {\n            fragmentsEntry.fragments = fragmentsEntry.fragments.concat(names);\n          } else if (names.length && !fragmentsEntry) {\n            importSpecifierToFragments[key] = fragmentsEntry = {\n              start: imp.moduleSpecifier.getStart(),\n              length: imp.moduleSpecifier.getText().length,\n              fragments: names,\n            };\n          }\n        }\n      } else if (\n        imp.importClause.namedBindings &&\n        ts.isNamedImportBindings(imp.importClause.namedBindings)\n      ) {\n        imp.importClause.namedBindings.elements.forEach(el => {\n          const identifier = el.name || el.propertyName;\n          if (!identifier) return;\n\n          const declaration = getDeclarationOfIdentifier(\n            identifier,\n            typeChecker\n          );\n          if (declaration) {\n            const sourceFile = declaration.getSourceFile();\n            if (sourceFile.fileName.includes('node_modules')) return;\n\n            const externalSource = sourceFile;\n            if (!externalSource) return;\n\n            const fragmentsForImport = getFragmentsInSource(\n              externalSource,\n              typeChecker,\n              info\n            );\n            const names = fragmentsForImport.map(\n              fragment => fragment.name.value\n            );\n            const key = imp.moduleSpecifier.getText();\n            let fragmentsEntry = importSpecifierToFragments[key];\n            if (names.length && fragmentsEntry) {\n              fragmentsEntry.fragments = fragmentsEntry.fragments.concat(names);\n            } else if (names.length && !fragmentsEntry) {\n              importSpecifierToFragments[key] = fragmentsEntry = {\n                start: imp.moduleSpecifier.getStart(),\n                length: imp.moduleSpecifier.getText().length,\n                fragments: names,\n              };\n            }\n          }\n        });\n      }\n    });\n  }\n\n  return importSpecifierToFragments;\n};\n\nfunction getFragmentsInSource(\n  src: ts.SourceFile,\n  typeChecker: ts.TypeChecker,\n  info: ts.server.PluginCreateInfo\n): Array<FragmentDefinitionNode> {\n  let fragments: Array<FragmentDefinitionNode> = [];\n  const callExpressions = findAllCallExpressions(src, info, false);\n\n  const symbol = typeChecker.getSymbolAtLocation(src);\n  if (!symbol) return [];\n\n  const exports = typeChecker.getExportsOfModule(symbol);\n  const exportedNames = exports.map(symb => symb.name);\n  const nodes = callExpressions.nodes.filter(x => {\n    let parent = x.node.parent;\n    while (\n      parent &&\n      !ts.isSourceFile(parent) &&\n      !ts.isVariableDeclaration(parent)\n    ) {\n      parent = parent.parent;\n    }\n\n    if (ts.isVariableDeclaration(parent)) {\n      return exportedNames.includes(parent.name.getText());\n    } else {\n      return false;\n    }\n  });\n\n  nodes.forEach(node => {\n    const text = resolveTemplate(node.node, src.fileName, info).combinedText;\n    try {\n      const parsed = parse(text, { noLocation: true });\n      if (parsed.definitions.every(x => x.kind === Kind.FRAGMENT_DEFINITION)) {\n        fragments = fragments.concat(parsed.definitions as any);\n      }\n    } catch (e) {\n      return;\n    }\n  });\n\n  return fragments;\n}\n", "import { ts } from './ts';\n\nimport { createHash } from 'crypto';\n\nimport * as checks from './ast/checks';\nimport {\n  findAllCallExpressions,\n  findNode,\n  getSource,\n  unrollTadaFragments,\n} from './ast';\nimport { resolveTemplate } from './ast/resolve';\nimport {\n  FragmentDefinitionNode,\n  Kind,\n  parse,\n  print,\n  visit,\n} from '@0no-co/graphql.web';\nimport {\n  getDeclarationOfIdentifier,\n  getValueOfIdentifier,\n} from './ast/declaration';\n\ntype PersistedAction = {\n  span: {\n    start: number;\n    length: number;\n  };\n  replacement: string;\n};\n\nexport function getPersistedCodeFixAtPosition(\n  filename: string,\n  position: number,\n  info: ts.server.PluginCreateInfo\n): PersistedAction | undefined {\n  const isCallExpression = info.config.templateIsCallExpression ?? true;\n  const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n  if (!isCallExpression) return undefined;\n\n  let source = getSource(info, filename);\n  if (!source) return undefined;\n\n  const node = findNode(source, position);\n  if (!node) return undefined;\n\n  let callExpression: ts.Node = node;\n  // We found a node and need to check where on the path we are\n  // we expect this to look a little bit like\n  // const persistedDoc = graphql.persisted<typeof x>()\n  // When we are on the left half of this statement we bubble down\n  // looking for the correct call-expression and on the right hand\n  // we bubble up.\n  if (ts.isVariableStatement(callExpression)) {\n    callExpression =\n      callExpression.declarationList.declarations.find(declaration => {\n        return (\n          ts.isVariableDeclaration(declaration) &&\n          declaration.initializer &&\n          ts.isCallExpression(declaration.initializer)\n        );\n      }) || node;\n  } else if (ts.isVariableDeclarationList(callExpression)) {\n    callExpression =\n      callExpression.declarations.find(declaration => {\n        return (\n          ts.isVariableDeclaration(declaration) &&\n          declaration.initializer &&\n          ts.isCallExpression(declaration.initializer)\n        );\n      }) || node;\n  } else if (\n    ts.isVariableDeclaration(callExpression) &&\n    callExpression.initializer &&\n    ts.isCallExpression(callExpression.initializer)\n  ) {\n    callExpression = callExpression.initializer;\n  } else {\n    while (callExpression && !ts.isCallExpression(callExpression)) {\n      callExpression = callExpression.parent;\n    }\n  }\n\n  // We want to ensure that we found a call-expression and that it looks\n  // like \"graphql.persisted\", in a future iteration when the API surface\n  // is more defined we will need to use the ts.Symbol to support re-exporting\n  // this function by means of \"export const peristed = graphql.persisted\".\n  if (!checks.isTadaPersistedCall(callExpression, typeChecker)) {\n    return undefined;\n  }\n\n  let foundNode,\n    foundFilename = filename;\n  if (callExpression.typeArguments) {\n    const [typeQuery] = callExpression.typeArguments;\n    if (!typeQuery || !ts.isTypeQueryNode(typeQuery)) return undefined;\n    const { node: found, filename: fileName } =\n      getDocumentReferenceFromTypeQuery(typeQuery, filename, info);\n    foundNode = found;\n    foundFilename = fileName;\n  } else if (callExpression.arguments[1]) {\n    if (\n      !ts.isIdentifier(callExpression.arguments[1]) &&\n      !ts.isCallExpression(callExpression.arguments[1])\n    )\n      return undefined;\n    const { node: found, filename: fileName } =\n      getDocumentReferenceFromDocumentNode(\n        callExpression.arguments[1],\n        filename,\n        info\n      );\n    foundNode = found;\n    foundFilename = fileName;\n  }\n\n  if (!foundNode) return undefined;\n\n  const initializer = foundNode;\n  if (\n    !initializer ||\n    !ts.isCallExpression(initializer) ||\n    !initializer.arguments[0] ||\n    !ts.isStringLiteralLike(initializer.arguments[0])\n  ) {\n    return undefined;\n  }\n\n  const hash = generateHashForDocument(\n    info,\n    initializer.arguments[0],\n    foundFilename,\n    initializer.arguments[1] &&\n      ts.isArrayLiteralExpression(initializer.arguments[1])\n      ? initializer.arguments[1]\n      : undefined\n  );\n  const existingHash = callExpression.arguments[0];\n  // We assume for now that this is either undefined or an existing string literal\n  if (!existingHash) {\n    // We have no persisted-identifier yet, suggest adding in a new one\n    return {\n      span: {\n        start: callExpression.arguments.pos,\n        length: 1,\n      },\n      replacement: `\"sha256:${hash}\")`,\n    };\n  } else if (\n    ts.isStringLiteral(existingHash) &&\n    existingHash.getText() !== `\"sha256:${hash}\"`\n  ) {\n    // We are out of sync, suggest replacing this with the updated hash\n    return {\n      span: {\n        start: existingHash.getStart(),\n        length: existingHash.end - existingHash.getStart(),\n      },\n      replacement: `\"sha256:${hash}\"`,\n    };\n  } else if (ts.isIdentifier(existingHash)) {\n    // Suggest replacing a reference with a static one\n    // this to make these easier to statically analyze\n    return {\n      span: {\n        start: existingHash.getStart(),\n        length: existingHash.end - existingHash.getStart(),\n      },\n      replacement: `\"sha256:${hash}\"`,\n    };\n  } else {\n    return undefined;\n  }\n}\n\nexport const generateHashForDocument = (\n  info: ts.server.PluginCreateInfo,\n  templateLiteral: ts.StringLiteralLike | ts.TaggedTemplateExpression,\n  foundFilename: string,\n  referencedFragments: ts.ArrayLiteralExpression | undefined\n): string | undefined => {\n  if (referencedFragments) {\n    const fragments: Array<FragmentDefinitionNode> = [];\n    unrollTadaFragments(referencedFragments, fragments, info);\n    let text = resolveTemplate(\n      templateLiteral,\n      foundFilename,\n      info\n    ).combinedText;\n    const parsed = parse(text);\n    const seen = new Set<unknown>();\n    for (const definition of parsed.definitions) {\n      if (\n        definition.kind === Kind.FRAGMENT_DEFINITION &&\n        !seen.has(definition)\n      ) {\n        stripUnmaskDirectivesFromDefinition(definition);\n      }\n    }\n\n    const deduplicatedFragments = fragments\n      .map(fragment => {\n        stripUnmaskDirectivesFromDefinition(fragment);\n        return print(fragment);\n      })\n      .filter((fragment, index, array) => array.indexOf(fragment) === index);\n\n    deduplicatedFragments.forEach(fragmentDefinition => {\n      text = `${text}\\n\\n${fragmentDefinition}`;\n    });\n    const fullText = print(parse(text));\n    return createHash('sha256').update(fullText).digest('hex');\n  } else {\n    const externalSource = getSource(info, foundFilename)!;\n    const { fragments } = findAllCallExpressions(externalSource, info);\n\n    const text = resolveTemplate(\n      templateLiteral,\n      foundFilename,\n      info\n    ).combinedText;\n\n    const parsed = parse(text);\n    const seen = new Set<unknown>();\n    for (const definition of parsed.definitions) {\n      if (\n        definition.kind === Kind.FRAGMENT_DEFINITION &&\n        !seen.has(definition)\n      ) {\n        stripUnmaskDirectivesFromDefinition(definition);\n      }\n    }\n\n    const spreads = new Set<string>();\n    visit(parsed, {\n      FragmentDefinition: node => {\n        fragments.push(node);\n      },\n      FragmentSpread: node => {\n        spreads.add(node.name.value);\n      },\n    });\n\n    let resolvedText = text;\n    const visited = new Set();\n    const traversedSpreads = [...spreads];\n\n    let spreadName: string | undefined;\n    while ((spreadName = traversedSpreads.shift())) {\n      visited.add(spreadName);\n      const fragmentDefinition = fragments.find(\n        x => x.name.value === spreadName\n      );\n      if (!fragmentDefinition) {\n        info.project.projectService.logger.info(\n          `[GraphQLSP] could not find fragment for spread ${spreadName}!`\n        );\n        return;\n      }\n\n      stripUnmaskDirectivesFromDefinition(fragmentDefinition);\n\n      visit(fragmentDefinition, {\n        FragmentSpread: node => {\n          if (!visited.has(node.name.value))\n            traversedSpreads.push(node.name.value);\n        },\n      });\n\n      resolvedText = `${resolvedText}\\n\\n${print(fragmentDefinition)}`;\n    }\n\n    return createHash('sha256')\n      .update(print(parse(resolvedText)))\n      .digest('hex');\n  }\n};\n\nexport const getDocumentReferenceFromTypeQuery = (\n  typeQuery: ts.TypeQueryNode,\n  filename: string,\n  info: ts.server.PluginCreateInfo\n): { node: ts.CallExpression | null; filename: string } => {\n  const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n  if (!typeChecker) return { node: null, filename };\n\n  // Handle EntityName (Identifier | QualifiedName)\n  let identifier: ts.Identifier | undefined;\n  if (ts.isIdentifier(typeQuery.exprName)) {\n    identifier = typeQuery.exprName;\n  } else if (ts.isQualifiedName(typeQuery.exprName)) {\n    // For qualified names like 'module.identifier', get the right-most identifier\n    identifier = typeQuery.exprName.right;\n  }\n\n  if (!identifier) return { node: null, filename };\n\n  const value = getValueOfIdentifier(identifier, typeChecker);\n  if (!value || !checks.isGraphQLCall(value, typeChecker)) {\n    return { node: null, filename };\n  }\n\n  return {\n    node: value as ts.CallExpression,\n    filename: value.getSourceFile().fileName,\n  };\n};\n\nexport const getDocumentReferenceFromDocumentNode = (\n  documentNodeArgument: ts.Identifier | ts.CallExpression,\n  filename: string,\n  info: ts.server.PluginCreateInfo\n): { node: ts.CallExpression | null; filename: string } => {\n  if (ts.isIdentifier(documentNodeArgument)) {\n    const typeChecker = info.languageService.getProgram()?.getTypeChecker();\n    if (!typeChecker) return { node: null, filename };\n\n    const value = getValueOfIdentifier(documentNodeArgument, typeChecker);\n    if (!value || !checks.isGraphQLCall(value, typeChecker)) {\n      return { node: null, filename };\n    }\n\n    return {\n      node: value as ts.CallExpression,\n      filename: value.getSourceFile().fileName,\n    };\n  } else {\n    return { node: documentNodeArgument, filename };\n  }\n};\n\ntype writable<T> = { -readonly [K in keyof T]: T[K] };\n\nconst stripUnmaskDirectivesFromDefinition = (\n  definition: FragmentDefinitionNode\n) => {\n  (definition as writable<FragmentDefinitionNode>).directives =\n    definition.directives?.filter(\n      directive => directive.name.value !== '_unmask'\n    );\n};\n", "import { ts } from './ts';\nimport { Diagnostic, getDiagnostics } from 'graphql-language-service';\nimport {\n  FragmentDefinitionNode,\n  Kind,\n  OperationDefinitionNode,\n  parse,\n  visit,\n} from 'graphql';\nimport { LRUCache } from 'lru-cache';\nimport fnv1a from '@sindresorhus/fnv1a';\nimport { print } from '@0no-co/graphql.web';\n\nimport {\n  findAllCallExpressions,\n  findAllPersistedCallExpressions,\n  findAllTaggedTemplateNodes,\n  getSource,\n} from './ast';\nimport { resolveTemplate } from './ast/resolve';\nimport { UNUSED_FIELD_CODE, checkFieldUsageInFile } from './fieldUsage';\nimport {\n  MISSING_FRAGMENT_CODE,\n  getColocatedFragmentNames,\n} from './checkImports';\nimport {\n  generateHashForDocument,\n  getDocumentReferenceFromDocumentNode,\n  getDocumentReferenceFromTypeQuery,\n} from './persisted';\nimport { SchemaRef } from './graphql/getSchema';\n\nconst BASE_CLIENT_DIRECTIVES = new Set([\n  'populate',\n  'client',\n  'unmask',\n  '_unmask',\n  '_optional',\n  '_relayPagination',\n  '_simplePagination',\n  '_required',\n  'optional',\n  'required',\n  'arguments',\n  'argumentDefinitions',\n  'connection',\n  'refetchable',\n  'relay',\n  'required',\n  'inline',\n]);\n\nexport const SEMANTIC_DIAGNOSTIC_CODE = 52001;\nexport const USING_DEPRECATED_FIELD_CODE = 52004;\nexport const MISSING_PERSISTED_TYPE_ARG = 520100;\nexport const MISSING_PERSISTED_CODE_ARG = 520101;\nexport const MISSING_PERSISTED_DOCUMENT = 520102;\nexport const MISSMATCH_HASH_TO_DOCUMENT = 520103;\nexport const ALL_DIAGNOSTICS = [\n  SEMANTIC_DIAGNOSTIC_CODE,\n  USING_DEPRECATED_FIELD_CODE,\n  MISSING_FRAGMENT_CODE,\n  UNUSED_FIELD_CODE,\n  MISSING_PERSISTED_TYPE_ARG,\n  MISSING_PERSISTED_CODE_ARG,\n  MISSING_PERSISTED_DOCUMENT,\n  MISSMATCH_HASH_TO_DOCUMENT,\n];\n\nconst cache = new LRUCache<number, ts.Diagnostic[]>({\n  // how long to live in ms\n  ttl: 1000 * 60 * 15,\n  max: 5000,\n});\n\nexport function getGraphQLDiagnostics(\n  filename: string,\n  schema: SchemaRef,\n  info: ts.server.PluginCreateInfo\n): ts.Diagnostic[] | undefined {\n  const isCallExpression = info.config.templateIsCallExpression ?? true;\n\n  let source = getSource(info, filename);\n  if (!source) return undefined;\n\n  let fragments: Array<FragmentDefinitionNode> = [],\n    nodes: {\n      node: ts.StringLiteralLike | ts.TaggedTemplateExpression;\n      schema: string | null;\n    }[];\n  if (isCallExpression) {\n    const result = findAllCallExpressions(source, info);\n    fragments = result.fragments;\n    nodes = result.nodes;\n  } else {\n    nodes = findAllTaggedTemplateNodes(source).map(x => ({\n      node: x,\n      schema: null,\n    }));\n  }\n\n  const texts = nodes.map(({ node }) => {\n    if (\n      (ts.isNoSubstitutionTemplateLiteral(node) ||\n        ts.isTemplateExpression(node)) &&\n      !isCallExpression\n    ) {\n      if (ts.isTaggedTemplateExpression(node.parent)) {\n        node = node.parent;\n      } else {\n        return undefined;\n      }\n    }\n\n    return resolveTemplate(node, filename, info).combinedText;\n  });\n\n  const cacheKey = fnv1a(\n    isCallExpression\n      ? source.getText() +\n          fragments.map(x => print(x)).join('-') +\n          schema.version\n      : texts.join('-') + schema.version\n  );\n\n  let tsDiagnostics: ts.Diagnostic[];\n  if (cache.has(cacheKey)) {\n    tsDiagnostics = cache.get(cacheKey)!;\n  } else {\n    tsDiagnostics = runDiagnostics(source, { nodes, fragments }, schema, info);\n    cache.set(cacheKey, tsDiagnostics);\n  }\n\n  const shouldCheckForColocatedFragments =\n    info.config.shouldCheckForColocatedFragments ?? true;\n  let fragmentDiagnostics: ts.Diagnostic[] = [];\n\n  if (isCallExpression) {\n    const persistedCalls = findAllPersistedCallExpressions(source, info);\n    // We need to check whether the user has correctly inserted a hash,\n    // by means of providing an argument to the function and that they\n    // are establishing a reference to the document by means of the generic.\n    const persistedDiagnostics = persistedCalls\n      .map<ts.Diagnostic | null>(found => {\n        const { node: callExpression } = found;\n        if (!callExpression.typeArguments && !callExpression.arguments[1]) {\n          return {\n            category: ts.DiagnosticCategory.Warning,\n            code: MISSING_PERSISTED_TYPE_ARG,\n            file: source,\n            messageText: 'Missing generic pointing at the GraphQL document.',\n            start: callExpression.getStart(),\n            length: callExpression.getEnd() - callExpression.getStart(),\n          };\n        }\n\n        let foundNode,\n          foundFilename = filename,\n          ref,\n          start,\n          length;\n        const typeQuery =\n          callExpression.typeArguments && callExpression.typeArguments[0];\n        if (typeQuery) {\n          start = typeQuery.getStart();\n          length = typeQuery.getEnd() - typeQuery.getStart();\n\n          if (!ts.isTypeQueryNode(typeQuery)) {\n            return {\n              category: ts.DiagnosticCategory.Warning,\n              code: MISSING_PERSISTED_TYPE_ARG,\n              file: source,\n              messageText:\n                'Provided generic should be a typeQueryNode in the shape of graphql.persisted<typeof document>.',\n              start,\n              length,\n            };\n          }\n          const { node: found, filename: fileName } =\n            getDocumentReferenceFromTypeQuery(typeQuery, filename, info);\n          foundNode = found;\n          foundFilename = fileName;\n          ref = typeQuery.getText();\n        } else if (callExpression.arguments[1]) {\n          start = callExpression.arguments[1].getStart();\n          length =\n            callExpression.arguments[1].getEnd() -\n            callExpression.arguments[1].getStart();\n          if (\n            !ts.isIdentifier(callExpression.arguments[1]) &&\n            !ts.isCallExpression(callExpression.arguments[1])\n          ) {\n            return {\n              category: ts.DiagnosticCategory.Warning,\n              code: MISSING_PERSISTED_TYPE_ARG,\n              file: source,\n              messageText:\n                'Provided argument should be an identifier or invocation of \"graphql\" in the shape of graphql.persisted(hash, document).',\n              start,\n              length,\n            };\n          }\n\n          const { node: found, filename: fileName } =\n            getDocumentReferenceFromDocumentNode(\n              callExpression.arguments[1],\n              filename,\n              info\n            );\n          foundNode = found;\n          foundFilename = fileName;\n          ref = callExpression.arguments[1].getText();\n        }\n\n        if (!foundNode) {\n          return {\n            category: ts.DiagnosticCategory.Warning,\n            code: MISSING_PERSISTED_DOCUMENT,\n            file: source,\n            messageText: `Can't find reference to \"${ref}\".`,\n            start,\n            length,\n          };\n        }\n\n        const initializer = foundNode;\n        if (\n          !initializer ||\n          !ts.isCallExpression(initializer) ||\n          !initializer.arguments[0] ||\n          !ts.isStringLiteralLike(initializer.arguments[0])\n        ) {\n          // TODO: we can make this check more stringent where we also parse and resolve\n          // the accompanying template.\n          return {\n            category: ts.DiagnosticCategory.Warning,\n            code: MISSING_PERSISTED_DOCUMENT,\n            file: source,\n            messageText: `Referenced type \"${ref}\" is not a GraphQL document.`,\n            start,\n            length,\n          };\n        }\n\n        if (!callExpression.arguments[0]) {\n          // TODO: this might be covered by the API enforcing the first\n          // argument so can possibly be removed.\n          return {\n            category: ts.DiagnosticCategory.Warning,\n            code: MISSING_PERSISTED_CODE_ARG,\n            file: source,\n            messageText: `The call-expression is missing a hash for the persisted argument.`,\n            start: callExpression.arguments.pos,\n            length: callExpression.arguments.end - callExpression.arguments.pos,\n          };\n        }\n\n        const hash = callExpression.arguments[0].getText().slice(1, -1);\n        if (hash.startsWith('sha256:')) {\n          const generatedHash = generateHashForDocument(\n            info,\n            initializer.arguments[0],\n            foundFilename,\n            initializer.arguments[1] &&\n              ts.isArrayLiteralExpression(initializer.arguments[1])\n              ? initializer.arguments[1]\n              : undefined\n          );\n          if (!generatedHash) return null;\n\n          const upToDateHash = `sha256:${generatedHash}`;\n          if (upToDateHash !== hash) {\n            return {\n              category: ts.DiagnosticCategory.Warning,\n              code: MISSMATCH_HASH_TO_DOCUMENT,\n              file: source,\n              messageText: `The persisted document's hash is outdated`,\n              start: callExpression.arguments.pos,\n              length:\n                callExpression.arguments.end - callExpression.arguments.pos,\n            };\n          }\n        }\n\n        return null;\n      })\n      .filter(Boolean);\n\n    tsDiagnostics.push(...(persistedDiagnostics as ts.Diagnostic[]));\n  }\n\n  if (isCallExpression && shouldCheckForColocatedFragments) {\n    const moduleSpecifierToFragments = getColocatedFragmentNames(source, info);\n\n    const usedFragments = new Set();\n    nodes.forEach(({ node }) => {\n      try {\n        const parsed = parse(node.getText().slice(1, -1), {\n          noLocation: true,\n        });\n        visit(parsed, {\n          FragmentSpread: node => {\n            usedFragments.add(node.name.value);\n          },\n        });\n      } catch (e) {}\n    });\n\n    Object.keys(moduleSpecifierToFragments).forEach(moduleSpecifier => {\n      const {\n        fragments: fragmentNames,\n        start,\n        length,\n      } = moduleSpecifierToFragments[moduleSpecifier]!;\n      const missingFragments = Array.from(\n        new Set(fragmentNames.filter(x => !usedFragments.has(x)))\n      );\n      if (missingFragments.length) {\n        fragmentDiagnostics.push({\n          file: source,\n          length,\n          start,\n          category: ts.DiagnosticCategory.Warning,\n          code: MISSING_FRAGMENT_CODE,\n          messageText: `Unused co-located fragment definition(s) \"${missingFragments.join(\n            ', '\n          )}\" in ${moduleSpecifier}`,\n        });\n      }\n    });\n\n    return [...tsDiagnostics, ...fragmentDiagnostics];\n  } else {\n    return tsDiagnostics;\n  }\n}\n\nconst runDiagnostics = (\n  source: ts.SourceFile,\n  {\n    nodes,\n    fragments,\n  }: {\n    nodes: {\n      node: ts.TaggedTemplateExpression | ts.StringLiteralLike;\n      schema: string | null;\n    }[];\n    fragments: FragmentDefinitionNode[];\n  },\n  schema: SchemaRef,\n  info: ts.server.PluginCreateInfo\n): ts.Diagnostic[] => {\n  const filename = source.fileName;\n  const isCallExpression = info.config.templateIsCallExpression ?? true;\n\n  const diagnostics = nodes\n    .map(originalNode => {\n      let node = originalNode.node;\n      if (\n        !isCallExpression &&\n        (ts.isNoSubstitutionTemplateLiteral(node) ||\n          ts.isTemplateExpression(node))\n      ) {\n        if (ts.isTaggedTemplateExpression(node.parent)) {\n          node = node.parent;\n        } else {\n          return undefined;\n        }\n      }\n\n      const { combinedText: text, resolvedSpans } = resolveTemplate(\n        node,\n        filename,\n        info\n      );\n      const lines = text.split('\\n');\n\n      let isExpression = false;\n      if (ts.isAsExpression(node.parent)) {\n        if (ts.isExpressionStatement(node.parent.parent)) {\n          isExpression = true;\n        }\n      } else if (ts.isExpressionStatement(node.parent)) {\n        isExpression = true;\n      }\n      // When we are dealing with a plain gql statement we have to add two these can be recognised\n      // by the fact that the parent is an expressionStatement\n\n      let startingPosition =\n        node.getStart() +\n        (isCallExpression\n          ? 0\n          : (node as ts.TaggedTemplateExpression).tag.getText().length +\n            (isExpression ? 2 : 0));\n      const endPosition = startingPosition + node.getText().length;\n      let docFragments = [...fragments];\n      if (isCallExpression) {\n        try {\n          const documentFragments = parse(text, {\n            noLocation: true,\n          }).definitions.filter(x => x.kind === Kind.FRAGMENT_DEFINITION);\n          docFragments = docFragments.filter(\n            x =>\n              !documentFragments.some(\n                y =>\n                  y.kind === Kind.FRAGMENT_DEFINITION &&\n                  y.name.value === x.name.value\n              )\n          );\n        } catch (e) {}\n      }\n\n      const schemaToUse =\n        originalNode.schema && schema.multi[originalNode.schema]\n          ? schema.multi[originalNode.schema]?.schema\n          : schema.current?.schema;\n\n      if (!schemaToUse) {\n        return undefined;\n      }\n\n      const clientDirectives = new Set([\n        ...BASE_CLIENT_DIRECTIVES,\n        ...(info.config.clientDirectives || []),\n      ]);\n\n      const graphQLDiagnostics = getDiagnostics(\n        text,\n        schemaToUse,\n        undefined,\n        undefined,\n        docFragments\n      )\n        .filter(diag => {\n          if (!diag.message.includes('Unknown directive')) return true;\n\n          const [message] = diag.message.split('(');\n          const matches =\n            message && /Unknown directive \"@([^)]+)\"/g.exec(message);\n          if (!matches) return true;\n          const directiveName = matches[1];\n          return directiveName && !clientDirectives.has(directiveName);\n        })\n        .map(x => {\n          const { start, end } = x.range;\n\n          // We add the start.line to account for newline characters which are\n          // split out\n          let startChar = startingPosition + start.line;\n          for (let i = 0; i <= start.line && i < lines.length; i++) {\n            if (i === start.line) startChar += start.character;\n            else if (lines[i]) startChar += lines[i]!.length;\n          }\n\n          let endChar = startingPosition + end.line;\n          for (let i = 0; i <= end.line && i < lines.length; i++) {\n            if (i === end.line) endChar += end.character;\n            else if (lines[i]) endChar += lines[i]!.length;\n          }\n\n          const locatedInFragment = resolvedSpans.find(x => {\n            const newEnd = x.new.start + x.new.length;\n            return startChar >= x.new.start && endChar <= newEnd;\n          });\n\n          if (!!locatedInFragment) {\n            return {\n              ...x,\n              start: locatedInFragment.original.start,\n              length: locatedInFragment.original.length,\n            };\n          } else {\n            if (startChar > endPosition) {\n              // we have to calculate the added length and fix this\n              const addedCharacters = resolvedSpans\n                .filter(x => x.new.start + x.new.length < startChar)\n                .reduce(\n                  (acc, span) => acc + (span.new.length - span.original.length),\n                  0\n                );\n              startChar = startChar - addedCharacters;\n              endChar = endChar - addedCharacters;\n              return {\n                ...x,\n                start: startChar + 1,\n                length: endChar - startChar,\n              };\n            } else {\n              return {\n                ...x,\n                start: startChar + 1,\n                length: endChar - startChar,\n              };\n            }\n          }\n        })\n        .filter(x => x.start + x.length <= endPosition);\n\n      return graphQLDiagnostics;\n    })\n    .flat()\n    .filter(Boolean) as Array<Diagnostic & { length: number; start: number }>;\n\n  const tsDiagnostics = diagnostics.map(\n    diag =>\n      ({\n        file: source,\n        length: diag.length,\n        start: diag.start,\n        category:\n          diag.severity === 2\n            ? ts.DiagnosticCategory.Warning\n            : ts.DiagnosticCategory.Error,\n        code:\n          typeof diag.code === 'number'\n            ? diag.code\n            : diag.severity === 2\n            ? USING_DEPRECATED_FIELD_CODE\n            : SEMANTIC_DIAGNOSTIC_CODE,\n        messageText: diag.message.split('\\n')[0],\n      } as ts.Diagnostic)\n  );\n\n  if (isCallExpression) {\n    const usageDiagnostics =\n      checkFieldUsageInFile(\n        source,\n        nodes.map(x => x.node) as ts.NoSubstitutionTemplateLiteral[],\n        info\n      ) || [];\n\n    if (!usageDiagnostics) return tsDiagnostics;\n\n    return [...tsDiagnostics, ...usageDiagnostics];\n  } else {\n    return tsDiagnostics;\n  }\n};\n"], "names": ["ts", "init", "modules", "typescript", "CharacterStream", "constructor", "sourceText", "this", "_start", "_pos", "getStartOfToken", "getCurrentPosition", "eol", "_sourceText", "length", "sol", "peek", "char<PERSON>t", "next", "char", "eat", "pattern", "_testNextCharacter", "eat<PERSON>hile", "match", "isMatched", "didEat", "eatSpace", "skipToEnd", "skip<PERSON>o", "position", "consume", "caseFold", "token", "RegExp", "test", "slice", "Array", "startsWith", "backUp", "num", "column", "indentation", "indent", "whiteSpaces", "pos", "charCodeAt", "current", "character", "opt", "ofRule", "list", "separator", "isList", "t", "kind", "style", "p", "value", "isIgnored", "ch", "LexRules", "Name", "Punctuation", "Number", "String", "Comment", "ParseRules", "Document", "Definition", "Kind", "FRAGMENT_DEFINITION", "<PERSON><PERSON><PERSON><PERSON>", "Query", "word", "name", "Mutation", "Subscription", "VariableDefinitions", "VariableDefinition", "Variable", "DefaultValue", "SelectionSet", "Selection", "stream", "Ali<PERSON>dF<PERSON>", "Field", "Arguments", "Argument", "FragmentSpread", "InlineFragment", "FragmentDefinition", "but<PERSON>ot", "rule", "exclusions", "ruleMatch", "check", "every", "exclusion", "TypeCondition", "Value", "NumberValue", "StringValue", "update", "state", "inBlockstring", "endsWith", "BooleanValue", "Null<PERSON><PERSON>ue", "EnumValue", "ListValue", "ObjectValue", "ObjectField", "Type", "ListType", "NonNullType", "NamedType", "type", "_a", "prevState", "Directive", "DirectiveDef", "InterfaceDef", "Implements", "DirectiveLocation", "SchemaDef", "OperationTypeDef", "ScalarDef", "ObjectTypeDef", "FieldDef", "ArgumentsDef", "InputValueDef", "UnionDef", "UnionMember", "EnumDef", "EnumValueDef", "InputDef", "ExtendDef", "ExtensionDefinition", "SCHEMA_EXTENSION", "SCALAR_TYPE_EXTENSION", "OBJECT_TYPE_EXTENSION", "INTERFACE_TYPE_EXTENSION", "UNION_TYPE_EXTENSION", "ENUM_TYPE_EXTENSION", "INPUT_OBJECT_TYPE_EXTENSION", "onlineParser", "options", "eatWhitespace", "lexRules", "parseRules", "editorConfig", "startState", "initialState", "level", "step", "needsSeparator", "pushRule", "DOCUMENT", "getToken", "popRule", "needsAdvance", "advanceRule", "tabSize", "indentLevel", "Math", "floor", "lex", "kinds", "Object", "keys", "i", "SpecialParseRules", "backupState", "assign", "undefined", "levels", "concat", "at", "expected", "call", "unsuccessful", "to", "from", "Invalid", "rules", "ruleKind", "TypeError", "successful", "isArray", "Range", "start", "end", "containsPosition", "line", "setStart", "Position", "setEnd", "lessThanOrEqualTo", "setLine", "<PERSON><PERSON><PERSON><PERSON>", "specifiedSDLRules", "LoneSchemaDefinitionRule", "UniqueOperationTypesRule", "UniqueTypeNamesRule", "UniqueEnumValueNamesRule", "UniqueFieldDefinitionNamesRule", "UniqueDirectiveNamesRule", "KnownTypeNamesRule", "KnownDirectivesRule", "UniqueDirectivesPerLocationRule", "PossibleTypeExtensionsRule", "UniqueArgumentNamesRule", "UniqueInputFieldNamesRule", "DIAGNOSTIC_SEVERITY", "invariant", "condition", "message", "Error", "getDiagnostics", "query", "schema", "customRules", "isRelayCompatMode", "externalFragments", "ast", "fragments", "reduce", "acc", "node", "print", "enhancedQuery", "parse", "error", "GraphQLError", "range", "getRange", "location", "queryText", "parser", "lines", "split", "_b", "locations", "severity", "source", "validate<PERSON><PERSON>y", "validationErrorAnnotations", "validateWithCustomRules", "isSchemaDocument", "specifiedRules", "filter", "NoUnusedFragmentsRule", "ExecutableDefinitionsRule", "KnownFragmentNamesRule", "prototype", "push", "apply", "validate", "includes", "nodes", "DIRECTIVE", "flatMap", "annotations", "deprecationWarningAnnotations", "NoDeprecatedCustomRule", "Warning", "highlightedNodes", "entries", "highlightNode", "variable", "loc", "highlightLoc", "getLocation", "e", "templates", "Set", "isIIFE", "isCallExpression", "arguments", "isFunctionExpression", "expression", "isArrowFunction", "asteriskToken", "modifiers", "isGraphQLFunctionIdentifier", "isIdentifier", "has", "escapedText", "isTadaGraphQLFunction", "checker", "isLeftHandSideExpression", "getTypeAtLocation", "getProperty", "isTadaGraphQLCall", "isStringLiteralLike", "isTadaPersistedCall", "isPropertyAccessExpression", "isGraphQLCall", "isGraphQLTag", "isTaggedTemplateExpression", "tag", "getSchemaName", "typeC<PERSON>cker", "getChildAt", "brandTypeSymbol", "brand", "getTypeOfSymbol", "isUnionOrIntersection", "found", "types", "find", "x", "isStringLiteral", "isValueDeclaration", "SyntaxKind", "BinaryExpression", "ArrowFunction", "BindingElement", "ClassDeclaration", "ClassExpression", "ClassStaticBlockDeclaration", "<PERSON><PERSON><PERSON><PERSON>", "EnumDeclaration", "EnumMember", "ExportAssignment", "FunctionDeclaration", "FunctionExpression", "GetAccessor", "JsxAttribute", "MethodDeclaration", "Parameter", "PropertyAssignment", "PropertyDeclaration", "SetAccessor", "ShorthandPropertyAssignment", "VariableDeclaration", "getValueOfValueDeclaration", "initializer", "isAssignmentOperator", "EqualsToken", "BarBarEqualsToken", "AmpersandAmpersandEqualsToken", "QuestionQuestionEqualsToken", "operatorToken", "right", "objectAssignmentInitializer", "climbPastPropertyOrElementAccess", "parent", "isElementAccessExpression", "argumentExpression", "getNameFromPropertyName", "isComputedPropertyName", "isNumericLiteral", "text", "isPrivateIdentifier", "isMemberName", "idText", "getDeclarationOfIdentifier", "symbol", "getSymbolAtLocation", "declarations", "flags", "SymbolFlags", "<PERSON><PERSON>", "isNamespaceImport", "aliased", "getAliasedSymbol", "isShorthandPropertyAssignment", "shorthandSymbol", "getShorthandAssignmentValueSymbol", "valueDeclaration", "isBindingElement", "isObjectBindingPattern", "propertyName", "prop", "isObjectLiteralElement", "isObjectLiteralExpression", "isJsxAttributes", "getContextualType", "Class", "Function", "isNewExpressionTarget", "target", "isNewExpression", "declaration", "isClassLike", "isCallOrNewExpressionTarget", "isCallOrNewExpression", "isNameOfFunctionDeclaration", "isFunctionLike", "body", "isBinaryExpression", "left", "getValueOfIdentifier", "resolveTemplate", "filename", "info", "combinedText", "getText", "resolvedSpans", "templateText", "template", "isNoSubstitutionTemplateLiteral", "templateSpans", "addedCharacters", "map", "span", "languageService", "getProgram", "getType<PERSON><PERSON>cker", "isVariableDeclaration", "identifierName", "originalStart", "getStart", "originalRange", "getSourceFile", "fileName", "replace", "alteredSpan", "identifier", "original", "new", "isAsExpression", "resolvedTemplate", "JSON", "Boolean", "resolveTadaFragmentArray", "isArrayLiteralExpression", "elements", "identifiers", "element", "getSource", "program", "findNode", "sourceFile", "getEnd", "for<PERSON><PERSON><PERSON><PERSON><PERSON>", "unrollFragment", "seen", "WeakSet", "_unrollElement", "add", "resolveIdentifierToGraphQLCall", "input", "checks", "fragmentRefs", "noLocation", "definitions", "for<PERSON>ach", "definition", "_error", "nextElement", "shift", "unrollTadaFragments", "fragmentsArray", "wip", "el", "findAllCallExpressions", "shouldSearchFragments", "result", "hasTriedToFindFragments", "getAllFragments", "findAllPersistedCallExpressions", "getIdentifierOfChainExpression", "isSatisfiesExpression", "isNonNullExpression", "isParenthesizedExpression", "isExpressionWithTypeArguments", "isCommaListExpression", "textSpan", "getWidth", "def", "src", "isVariableStatement", "declarationList", "properties", "property", "isPropertyAssignment", "possibleFragment", "bubbleUpTemplate", "isToken", "isTemplateExpression", "isTemplateSpan", "bubbleUpCallExpression", "perf", "performance", "now", "Date", "warned", "PROCESS", "process", "emitWarning", "msg", "code", "fn", "console", "AC", "globalThis", "AbortController", "AS", "AbortSignal", "_onabort", "aborted", "addEventListener", "_", "warnACPolyfill", "signal", "abort", "reason", "<PERSON>ab<PERSON>", "printACPolyfillWarning", "env", "LRU_CACHE_IGNORE_AC_WARNING", "isPosInt", "n", "isFinite", "getUintArray", "max", "pow", "Uint8Array", "Uint16Array", "Uint32Array", "MAX_SAFE_INTEGER", "ZeroArray", "size", "super", "fill", "<PERSON><PERSON>", "static", "create", "HeapCls", "constructing", "s", "heap", "pop", "L<PERSON><PERSON><PERSON>", "maxSize", "dispose", "disposeAfter", "fetch<PERSON><PERSON><PERSON>", "calculatedSize", "keyMap", "keyList", "valList", "prev", "head", "tail", "free", "disposed", "sizes", "starts", "ttls", "hasDispose", "hasFetchMethod", "hasDisposeAfter", "unsafeExposeInternals", "c", "isBackgroundFetch", "backgroundFetch", "k", "index", "context", "moveToTail", "indexes", "rindexes", "isStale", "ttl", "ttlResolution", "ttlAutopurge", "updateAgeOnGet", "updateAgeOnHas", "allowStale", "noDisposeOnSet", "noUpdateTTL", "maxEntrySize", "sizeCalculation", "noDeleteOnFetchRejection", "noDeleteOnStaleGet", "allowStaleOnFetchRejection", "allowStaleOnFetchAbort", "ignoreFetchAbort", "UintArray", "Map", "initializeSizeTracking", "initializeTTLTracking", "<PERSON><PERSON><PERSON><PERSON>", "getRemainingTTL", "key", "Infinity", "setItemTTL", "setTimeout", "delete", "unref", "updateItemAge", "statusTTL", "status", "cachedNow", "getNow", "remainingTTL", "get", "#updateItemAge", "#statusTTL", "#setItemTTL", "#isStale", "removeItemSize", "requireSize", "v", "addItemSize", "evict", "entrySize", "totalCalculatedSize", "_i", "#addItemSize", "_s", "_st", "#requireSize", "_k", "_v", "isValidIndex", "rentries", "rkeys", "values", "rvalues", "Symbol", "iterator", "getOptions", "__staleWhileFetching", "thisp", "rforEach", "purgeStale", "deleted", "dump", "arr", "entry", "age", "unshift", "load", "clear", "set", "setOptions", "maxEntrySizeExceeded", "oldVal", "__abortController", "oldValue", "dt", "task", "val", "hasOptions", "peekOptions", "ac", "fetchOpts", "cb", "updateCache", "ignoreAbort", "fetchAborted", "fetchError", "fetchAbortIgnored", "fetchResolved", "fetchFail", "fetchUpdated", "er", "allowStaleAborted", "bf", "returnedStale", "__returned", "fetchDispatched", "Promise", "pcall", "res", "rej", "fmp", "then", "fetchRejected", "b", "hasOwnProperty", "fetch", "fetchOptions", "forceRefresh", "stale", "staleVal", "fetching", "connect", "FNV_PRIMES", "FNV_OFFSETS", "fnv1aModule", "exports", "fnv1a", "string", "hash", "isUnicoded", "characterCode", "unescape", "encodeURIComponent", "bigInt", "fnvPrime", "BigInt", "asUintN", "UNUSED_FIELD_CODE", "unwrapAbstractType", "TypeFlags", "getVariableDeclaration", "isBlock", "traverseArrayDestructuring", "originalWip", "allFields", "isOmittedExpression", "crawlScope", "traverseDestructuring", "results", "_loop", "binding", "joined", "join", "traverseResult", "crawlResult", "arrayMethods", "crawlChainedExpressions", "ref", "pathParts", "isReduce", "func", "nested<PERSON><PERSON>ult", "isFunctionDeclaration", "param", "parameters", "scopedResult", "inArrayMethod", "isArrayBindingPattern", "references", "getReferencesAtPosition", "foundRef", "_ret", "_loop2", "isReturnStatement", "callExpression", "isSomeOrEvery", "chainedResults", "varRes", "getColocatedFragmentNames", "imports", "findAllImports", "statements", "isImportDeclaration", "importSpecifierToFragments", "imp", "importClause", "names", "getFragmentsInSource", "fragment", "moduleSpecifier", "fragmentsEntry", "<PERSON><PERSON><PERSON><PERSON>", "isNamedImportBindings", "callExpressions", "exportedNames", "getExportsOfModule", "symb", "isSourceFile", "parsed", "getPersistedCodeFixAtPosition", "config", "templateIsCallExpression", "isVariableDeclarationList", "foundNode", "foundFilename", "typeArguments", "typeQuery", "isTypeQueryNode", "getDocumentReferenceFromTypeQuery", "getDocumentReferenceFromDocumentNode", "generateHashForDocument", "existingHash", "replacement", "templateLiteral", "referencedFragments", "stripUnmaskDirectivesFromDefinition", "array", "indexOf", "fragmentDefinition", "fullText", "createHash", "digest", "externalSource", "spreads", "visit", "resolvedText", "visited", "traversedSpreads", "spreadName", "project", "projectService", "logger", "exprName", "isQualifiedName", "documentNodeArgument", "directives", "directive", "BASE_CLIENT_DIRECTIVES", "MISSING_PERSISTED_TYPE_ARG", "MISSING_PERSISTED_CODE_ARG", "MISSING_PERSISTED_DOCUMENT", "MISSMATCH_HASH_TO_DOCUMENT", "ALL_DIAGNOSTICS", "cache", "getGraphQLDiagnostics", "findAllTaggedTemplateNodes", "texts", "cache<PERSON>ey", "version", "tsDiagnostics", "runDiagnostics", "shouldCheckForColocatedFragments", "fragmentDiagnostics", "persistedDiagnostics", "category", "DiagnosticCategory", "file", "messageText", "generatedHash", "moduleSpecifierToFragments", "usedFragments", "fragmentNames", "missingFragments", "diagnostics", "originalNode", "isExpression", "isExpressionStatement", "startingPosition", "endPosition", "docFragments", "documentFragments", "some", "y", "schemaToUse", "multi", "clientDirectives", "graphQLDiagnostics", "diag", "matches", "exec", "directiveName", "startChar", "endChar", "locatedInFragment", "flat", "usageDiagnostics", "checkFieldUsageInFile", "trackFieldUsage", "reservedKeys", "nodeText", "variableDeclaration", "dataType", "resolvedTypeArguments", "apiTypeSymbol", "apiType", "callSignature", "getCallSignatures", "getReturnType", "allAccess", "inProgress", "allPaths", "fieldToLoc", "enter", "alias", "path", "selectionSet", "leave", "targetNode", "scopeSymbols", "getSymbolsInScope", "BlockScopedVariable", "scopeDataSymbol", "scopeSymbol", "typeOfScopeSymbol", "tuplePropertySymbol", "dataPropertySymbol", "isBindingName", "unused", "aggregatedUnusedFields", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unusedFragmentLeaf", "unusedField", "parentField", "field", "unusedFields", "stack"], "mappings": ";;;;IAAWA;;AACJ,SAASC,KAAKC;EACnBF,IAAKE,EAAQC;AACf;;ACkBc,MAAOC;EAKnBC,WAAAA,CAAYC;IAJJC,KAAAC,SAAS;IACTD,KAAAE,OAAO;IAORF,KAAAG,kBAAkB,MAAcH,KAAKC;IAErCD,KAAAI,qBAAqB,MAAcJ,KAAKE;IAgBxCF,KAAAK,MAAM,MAAeL,KAAKM,YAAYC,WAAWP,KAAKE;IAEtDF,KAAAQ,MAAM,MAA6B,MAAdR,KAAKE;IAE1BF,KAAAS,OAAO,MACLT,KAAKM,YAAYI,OAAOV,KAAKE,SAAS;IAGxCF,KAAAW,OAAO;MACZ,IAAMC,IAAOZ,KAAKM,YAAYI,OAAOV,KAAKE;MAC1CF,KAAKE;MACL,OAAOU;AAAI;IAGNZ,KAAAa,MAAOC;MAEZ,IADkBd,KAAKe,mBAAmBD,IAC3B;QACbd,KAAKC,SAASD,KAAKE;QACnBF,KAAKE;QACL,OAAOF,KAAKM,YAAYI,OAAOV,KAAKE,OAAO;;MAE7C;AAAgB;IAGXF,KAAAgB,WAAYC;MACjB,IAAIC,IAAYlB,KAAKe,mBAAmBE;MACxC,IAAIE,KAAS;MAGb,IAAID,GAAW;QACbC,IAASD;QACTlB,KAAKC,SAASD,KAAKE;;MAGrB,OAAOgB,GAAW;QAChBlB,KAAKE;QACLgB,IAAYlB,KAAKe,mBAAmBE;QACpCE,KAAS;;MAGX,OAAOA;AAAM;IAGRnB,KAAAoB,WAAW,MAAepB,KAAKgB,SAAS;IAExChB,KAAAqB,YAAY;MACjBrB,KAAKE,OAAOF,KAAKM,YAAYC;AAAM;IAG9BP,KAAAsB,SAAUC;MACfvB,KAAKE,OAAOqB;AAAQ;IAGfvB,KAAAiB,QAAQ,CACbH,GACAU,KAAsC,GACtCC,KAAuC;MAEvC,IAAIC,IAAQ;MACZ,IAAIT,IAAQ;MAEZ,IAAuB,mBAAZH,GAAsB;QAE/BG,IADc,IAAIU,OAAOb,GAASW,IAAW,MAAM,KACrCG,KACZ5B,KAAKM,YAAYuB,MAAM7B,KAAKE,MAAMF,KAAKE,OAAOY,EAAQP;QAExDmB,IAAQZ;AACT,aAAM,IAAIA,aAAmBa;QAE5BD,IAAQT,SADRA,IAAQjB,KAAKM,YAAYuB,MAAM7B,KAAKE,MAAMe,MAAMH,eACxCG,EAAQ;;MAGlB,IACW,QAATA,MACoB,mBAAZH,KACLG,aAAiBa,SAIhB9B,KAAKM,YAAYyB,WAAWd,EAAM,IAAIjB,KAAKE,QAC/C;QACA,IAAIsB,GAAS;UACXxB,KAAKC,SAASD,KAAKE;UAEnB,IAAIwB,KAASA,EAAMnB;YACjBP,KAAKE,QAAQwB,EAAMnB;;;QAGvB,OAAOU;;MAIT,QAAO;AAAK;IAGPjB,KAAAgC,SAAUC;MACfjC,KAAKE,QAAQ+B;AAAG;IAGXjC,KAAAkC,SAAS,MAAclC,KAAKE;IAE5BF,KAAAmC,cAAc;MACnB,IAAMlB,IAAQjB,KAAKM,YAAYW,MAAM;MACrC,IAAImB,IAAS;MACb,IAAInB,KAA0B,MAAjBA,EAAMV,QAAc;QAC/B,IAAM8B,IAAcpB,EAAM;QAC1B,IAAIqB,IAAM;QACV,OAAOD,EAAY9B,SAAS+B,GAAK;UAC/B,IAAoC,MAAhCD,EAAYE,WAAWD;YACzBF,KAAU;;YAEVA;;UAEFE;;;MAIJ,OAAOF;AAAM;IAGRpC,KAAAwC,UAAU,MAAcxC,KAAKM,YAAYuB,MAAM7B,KAAKC,QAAQD,KAAKE;IA7ItEF,KAAKM,cAAcP;AACrB;EAMQgB,kBAAAA,CAAmBD;IACzB,IAAM2B,IAAYzC,KAAKM,YAAYI,OAAOV,KAAKE;IAC/C,IAAIgB,KAAY;IAChB,IAAuB,mBAAZJ;MACTI,IAAYuB,MAAc3B;;MAE1BI,IACEJ,aAAmBa,SACfb,EAAQc,KAAKa,KACb3B,EAAQ2B;;IAEhB,OAAOvB;AACT;;;AChCI,SAAUwB,IAAIC;EAClB,OAAO;IAAEA;;AACX;;AAGM,SAAUC,KAAKD,GAAuBE;EAC1C,OAAO;IAAEF;IAAQG,SAAQ;IAAMD;;AACjC;;AAoBM,SAAUE,IAAEC,GAAcC;EAC9B,OAAO;IAAEA;IAAOhC,OAAQS,KAAiBA,EAAMsB,SAASA;;AAC1D;;AAGM,SAAUE,IAAEC,GAAeF;EAC/B,OAAO;IACLA,OAAOA,KAAS;IAChBhC,OAAQS,KACS,kBAAfA,EAAMsB,QAA0BtB,EAAMyB,UAAUA;;AAEtD;;ACnCO,IAAMC,YAAaC,KACjB,QAAPA,KACO,SAAPA,KACO,QAAPA,KACO,SAAPA,KACO,SAAPA,KACO,aAAPA,KACO,QAAPA;;AAKK,IAAMC,IAAW;EAEtBC,MAAM;EAGNC,aAAa;EAGbC,QAAQ;EAGRC,QACE;EAGFC,SAAS;;;AAQJ,IAAMC,IAA4C;EACvDC,UAAU,EAACjB,KAAK;EAChBkB,UAAAA,CAAWpC;IACT,QAAQA,EAAMyB;KACZ,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAOY,EAAKC;;KACd,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;AAEZ;EAEDC,YAAY,EAAC;EACbC,OAAO,EACLC,KAAK,UACLzB,IAAI0B,OAAK,SACT1B,IAAI,wBACJE,KAAK,cACL;EAGFyB,UAAU,EACRF,KAAK,aACLzB,IAAI0B,OAAK,SACT1B,IAAI,wBACJE,KAAK,cACL;EAGF0B,cAAc,EACZH,KAAK,iBACLzB,IAAI0B,OAAK,SACT1B,IAAI,wBACJE,KAAK,cACL;EAGF2B,qBAAqB,EAACrB,IAAE,MAAMN,KAAK,uBAAuBM,IAAE;EAC5DsB,oBAAoB,EAAC,YAAYtB,IAAE,MAAM,QAAQR,IAAI;EACrD+B,UAAU,EAACvB,IAAE,KAAK,aAAakB,OAAK;EACpCM,cAAc,EAACxB,IAAE,MAAM;EACvByB,cAAc,EAACzB,IAAE,MAAMN,KAAK,cAAcM,IAAE;EAC5C0B,WAASA,CAAClD,GAAcmD,MACC,UAAhBnD,EAAMyB,QACT0B,EAAO5D,MAAM,2BAA0B,KACrC,mBACA,mBACF4D,EAAO5D,MAAM,kBAAiB,KAC9B,iBACA;EAGN6D,cAAc,EACZV,OAAK,aACLlB,IAAE,MACFkB,OAAK,cACL1B,IAAI,cACJE,KAAK,cACLF,IAAI;EAGNqC,OAAO,EACLX,OAAK,aACL1B,IAAI,cACJE,KAAK,cACLF,IAAI;EAGNsC,WAAW,EAAC9B,IAAE,MAAMN,KAAK,aAAaM,IAAE;EACxC+B,UAAU,EAACb,OAAK,cAAclB,IAAE,MAAM;EACtCgC,gBAAgB,EAAChC,IAAE,QAAQkB,OAAK,QAAQxB,KAAK;EAC7CuC,gBAAgB,EACdjC,IAAE,QACFR,IAAI,kBACJE,KAAK,cACL;EAGFwC,oBAAoB,EAClBjB,KAAK,aACLzB,IDnIE,SAAU2C,OAAOC,GAAYC;IACjC,IAAMC,IAAYF,EAAKrE;IACvBqE,EAAKrE,QAAQS;MACX,IAAI+D,KAAQ;MACZ,IAAID;QACFC,IAAQD,EAAU9D;;MAEpB,OACE+D,KAEAF,EAAWG,OAAMC,KAAaA,EAAU1E,UAAU0E,EAAU1E,MAAMS;AAAO;IAG7E,OAAO4D;AACT,GCqHQD,CAAOjB,OAAK,QAAQ,EAACD,KAAK,WAC9B,iBACAvB,KAAK,cACL;EAGFgD,eAAe,EAACzB,KAAK,OAAO;EAE5B0B,KAAAA,CAAMnE;IACJ,QAAQA,EAAMsB;KACZ,KAAK;MACH,OAAO;;KACT,KAAK;MACH,OAAO;;KACT,KAAK;MACH,QAAQtB,EAAMyB;OACZ,KAAK;QACH,OAAO;;OACT,KAAK;QACH,OAAO;;OACT,KAAK;QACH,OAAO;;OACT,KAAK;QACH,OAAO;;MAGX,OAAO;;KACT,KAAK;MACH,QAAQzB,EAAMyB;OACZ,KAAK;OACL,KAAK;QACH,OAAO;;MAGX,IAAoB,WAAhBzB,EAAMyB;QACR,OAAO;;MAET,OAAO;;AAEZ;EACD2C,aAAa,EAAC/C,IAAE,UAAU;EAC1BgD,aAAa,EACX;IACE9C,OAAO;IACPhC,OAAQS,KAAgC,aAAfA,EAAMsB;IAC/BgD,MAAAA,CAAOC,GAAcvE;MACnB,IAAIA,EAAMyB,MAAMpB,WAAW;QACzBkE,EAAMC,iBAAiBxE,EAAMyB,MAAMtB,MAAM,GAAGsE,SAAS;;AAEzD;;EAGJC,cAAc,EAACrD,IAAE,QAAQ;EACzBsD,WAAW,EAACtD,IAAE,QAAQ;EACtBuD,WAAW,EAAClC,OAAK;EACjBmC,WAAW,EAACrD,IAAE,MAAMN,KAAK,UAAUM,IAAE;EACrCsD,aAAa,EAACtD,IAAE,MAAMN,KAAK,gBAAgBM,IAAE;EAC7CuD,aAAa,EAACrC,OAAK,cAAclB,IAAE,MAAM;EACzCwD,MAAKhF,KACoB,QAAhBA,EAAMyB,QAAgB,aAAa;EAG5CwD,UAAU,EAACzD,IAAE,MAAM,QAAQA,IAAE,MAAMR,IAAIQ,IAAE;EACzC0D,aAAa,EAAC,aAAalE,IAAIQ,IAAE;EACjC2D,WAAW,EAsIb,SAASC,OAAK7D;IACZ,OAAO;MACLA;MACAhC,OAAQS,KAAgC,WAAfA,EAAMsB;MAC/BgD,MAAAA,CAAOC,GAAcvE;;QACnB,IAAmBqF,UAAfA,IAAAd,EAAMe,mBAASD,MAAAA,SAAAA,IAAAA,EAAEC,WAAW;UAC9Bf,EAAM7B,OAAO1C,EAAMyB;UACnB8C,EAAMe,UAAUA,UAAUF,OAAOpF,EAAMyB;;AAE3C;;AAEJ,GAjJc2D,CAAK;EACjBG,WAAW,EAAC/D,IAAE,KAAK,SAASkB,OAAK,SAAS1B,IAAI;EAC9CwE,cAAc,EACZ/C,KAAK,cACLjB,IAAE,KAAK,SACPkB,OAAK,SACL1B,IAAI,iBACJyB,KAAK,OACLvB,KAAK,qBAAqBM,IAAE;EAE9BiE,cAAc,EACZhD,KAAK,cACLC,OAAK,SACL1B,IAAI,eACJE,KAAK,cACLM,IAAE,MACFN,KAAK,aACLM,IAAE;EAEJkE,YAAY,EAACjD,KAAK,eAAevB,KAAK,aAAaM,IAAE;EACrDmE,mBAAmB,EAACjD,OAAK;EAEzBkD,WAAW,EACTnD,KAAK,WACLvB,KAAK,cACLM,IAAE,MACFN,KAAK,qBACLM,IAAE;EAGJqE,kBAAkB,EAACnD,OAAK,YAAYlB,IAAE,MAAMkB,OAAK;EACjDoD,WAAW,EAACrD,KAAK,WAAWC,OAAK,SAASxB,KAAK;EAC/C6E,eAAe,EACbtD,KAAK,SACLC,OAAK,SACL1B,IAAI,eACJE,KAAK,cACLM,IAAE,MACFN,KAAK,aACLM,IAAE;EAGJwE,UAAU,EACRtD,OAAK,aACL1B,IAAI,iBACJQ,IAAE,MACF,QACAN,KAAK;EAGP+E,cAAc,EAACzE,IAAE,MAAMN,KAAK,kBAAkBM,IAAE;EAChD0E,eAAe,EACbxD,OAAK,cACLlB,IAAE,MACF,QACAR,IAAI,iBACJE,KAAK;EAGPiF,UAAU,EACR1D,KAAK,UACLC,OAAK,SACLxB,KAAK,cACLM,IAAE,MACFN,KAAK,eAAeM,IAAE;EAGxB4E,aAAa,EAAC;EACdC,SAAS,EACP5D,KAAK,SACLC,OAAK,SACLxB,KAAK,cACLM,IAAE,MACFN,KAAK,iBACLM,IAAE;EAGJ8E,cAAc,EAAC5D,OAAK,aAAaxB,KAAK;EACtCqF,UAAU,EACR9D,KAAK,UACLC,OAAK,SACLxB,KAAK,cACLM,IAAE,MACFN,KAAK,kBACLM,IAAE;EAEJgF,WAAW,EAAC/D,KAAK,WAAW;EAC5BgE,mBAAAA,CAAoBzG;IAClB,QAAQA,EAAMyB;KACZ,KAAK;MACH,OAAOY,EAAKqE;;KACd,KAAK;MACH,OAAOrE,EAAKsE;;KACd,KAAK;MACH,OAAOtE,EAAKuE;;KACd,KAAK;MACH,OAAOvE,EAAKwE;;KACd,KAAK;MACH,OAAOxE,EAAKyE;;KACd,KAAK;MACH,OAAOzE,EAAK0E;;KACd,KAAK;MACH,OAAO1E,EAAK2E;;AAEjB;EACD,CAAC3E,EAAKqE,mBAAmB,EAAC;EAC1B,CAACrE,EAAKsE,wBAAwB,EAAC;EAC/B,CAACtE,EAAKuE,wBAAwB,EAAC;EAC/B,CAACvE,EAAKwE,2BAA2B,EAAC;EAClC,CAACxE,EAAKyE,uBAAuB,EAAC;EAC9B,CAACzE,EAAK0E,sBAAsB,EAAC;EAC7B,CAAC1E,EAAK2E,8BAA8B,EAAC;;;AAIvC,SAASvE,KAAKhB;EACZ,OAAO;IACLF,OAAO;IACPhC,OAAQS,KAAgC,WAAfA,EAAMsB,QAAmBtB,EAAMyB,UAAUA;;AAEtE;;AAGA,SAASiB,OAAKnB;EACZ,OAAO;IACLA;IACAhC,OAAQS,KAAgC,WAAfA,EAAMsB;IAC/BgD,MAAAA,CAAOC,GAAcvE;MACnBuE,EAAM7B,OAAO1C,EAAMyB;AACrB;;AAEJ;;ACpTc,SAAUwF,aACtBC,IAAyB;EACvBC,eAAehE,KAAUA,EAAO7D,SAASoC;EACzC0F,UAAUxF;EACVyF,YAAYnF;EACZoF,cAAc,CAAA;;EAMhB,OAAO;IACLC,UAAAA;MACE,IAAMC,IAAe;QACnBC,OAAO;QACPC,MAAM;QACNhF,MAAM;QACNpB,MAAM;QACN8D,MAAM;QACNxB,MAAM;QACN+D,iBAAgB;QAChBrC,WAAW;;MAGbsC,SAASV,EAAQG,YAAYG,GAAcnF,EAAKwF;MAChD,OAAOL;AACR;IACDxH,OAAKA,CAACmD,GAAyBoB,MAMnC,SAASuD,SACP3E,GACAoB,GACA2C;;MAEA,IAAI3C,EAAMC,eAAe;QAEvB,IAAIrB,EAAO5D,MAAM,UAAU;UACzBgF,EAAMC,iBAAgB;UACtB,OAAO;;QAETrB,EAAOxD;QACP,OAAO;;MAGT,KAAMyH,UAAEA,GAAQC,YAAEA,GAAUF,eAAEA,GAAaG,cAAEA,KAAiBJ;MAE9D,IAAI3C,EAAMX,QAA8B,MAAtBW,EAAMX,KAAK/E;QAC3BkJ,QAAQxD;aACH,IAAIA,EAAMyD,cAAc;QAC7BzD,EAAMyD,gBAAe;QACrBC,YAAY1D,IAAO;;MAIrB,IAAIpB,EAAOrE,OAAO;QAChB,IAAMoJ,KAAUZ,iBAAY,IAAZA,EAAcY,YAAW;QACzC3D,EAAM4D,cAAcC,KAAKC,MAAMlF,EAAO1C,gBAAgByH;;MAIxD,IAAIf,EAAchE;QAChB,OAAO;;MAIT,IAAMnD,IAuOR,SAASsI,IACPlB,GACAjE;QAEA,IAAMoF,IAAQC,OAAOC,KAAKrB;QAC1B,KAAK,IAAIsB,IAAI,GAAGA,IAAIH,EAAM1J,QAAQ6J,KAAK;UAGrC,IAAMnJ,IAAQ4D,EAAO5D,MAAM6H,EAASmB,EAAMG;UAC1C,IAAInJ,KAASA,aAAiBa;YAC5B,OAAO;cAAEkB,MAAMiH,EAAMG;cAAIjH,OAAOlC,EAAM;;;;AAG5C,OApPgB+I,CAAIlB,GAAUjE;MAG5B,KAAKnD,GAAO;QAEV,KADyBmD,EAAO5D,MAAM;UAIpC4D,EAAO5D,MAAM;;QAEfqI,SAASe,GAAmBpE,GAAO;QACnC,OAAO;;MAIT,IAAmB,cAAfvE,EAAMsB,MAAoB;QAC5BsG,SAASe,GAAmBpE,GAAO;QACnC,OAAO;;MAIT,IAAMqE,IAAcC,OAAO,CAAE,GAAEtE;MAG/B,IAAmB,kBAAfvE,EAAMsB;QACR,IAAI,SAASpB,KAAKF,EAAMyB;UACtB,SAA0BqH,MAAtBvE,EAAM4D;YAER5D,EAAMwE,UAAUxE,EAAMwE,UAAU,IAAIC,OAAOzE,EAAM4D,cAAc;;eAE5D,IAAI,UAAUjI,KAAKF,EAAMyB,QAAQ;UAItC,IAAMsH,IAAUxE,EAAMwE,UAAUxE,EAAMwE,UAAU,IAAI5I,MAAM,IAAI;UAG9D,IACEoE,EAAM4D,eACNY,EAAOlK,SAAS,KAChBkK,EAAOE,IAAI,KAAM1E,EAAM4D;YAEvB5D,EAAM4D,cAAcY,EAAOE,IAAI;;;;MAKrC,OAAO1E,EAAMX,MAAM;QAGjB,IAAIsF,IACoB,qBAAf3E,EAAMX,OACM,MAAfW,EAAMmD,OACJnD,EAAMX,KAAK5D,GAAOmD,KAClB,OACFoB,EAAMX,KAAKW,EAAMmD;QAGvB,IAAInD,EAAMoD;UACRuB,IAAWA,iBAAAA,IAAAA,EAAU/H;;QAGvB,IAAI+H,GAAU;UAEZ,IAAIA,EAASjI;YACXiI,IAAWA,EAASjI;;UAItB,IAAwB,mBAAbiI,GAAuB;YAChCtB,SAASP,GAAY9C,GAAO2E;YAC5B;;UAIF,IAAkB7D,UAAdA,IAAA6D,EAAS3J,eAAK8F,MAAAA,SAAAA,IAAAA,EAAA8D,KAAAD,GAAGlJ,IAAQ;YAC3B,IAAIkJ,EAAS5E;cACX4E,EAAS5E,OAAOC,GAAOvE;;YAMzB,IAAmB,kBAAfA,EAAMsB;cACR2G,YAAY1D,IAAO;;cAEnBA,EAAMyD,gBAAe;;YAGvB,OAAOkB,EAAS3H;;;QAGpB6H,aAAa7E;;MAIfsE,OAAOtE,GAAOqE;MACdhB,SAASe,GAAmBpE,GAAO;MACnC,OAAO;AACT,KA5IauD,CAAS3E,GAAQoB,GAAO2C;;AAGrC;;AA4IA,SAAS2B,OAAOQ,GAAYC;EAC1B,IAAMb,IAAOD,OAAOC,KAAKa;EACzB,KAAK,IAAIZ,IAAI,GAAGA,IAAID,EAAK5J,QAAQ6J;IAG/BW,EAAGZ,EAAKC,MAAMY,EAAKb,EAAKC;;EAE1B,OAAOW;AACT;;AAGA,IAAMV,IAAoB;EACxBY,SAAS;EACTtH,SAAS;;;AAIX,SAAS2F,SACP4B,GACAjF,GACAkF;EAEA,KAAKD,EAAMC;IACT,MAAM,IAAIC,UAAU,mBAAmBD;;EAEzClF,EAAMe,YAASkD,OAAAK,OAAA,CAAA,GAAQtE;EACvBA,EAAMjD,OAAOmI;EACblF,EAAM7B,OAAO;EACb6B,EAAMa,OAAO;EACbb,EAAMX,OAAO4F,EAAMC;EACnBlF,EAAMmD,OAAO;EACbnD,EAAMoD,kBAAiB;AACzB;;AAGA,SAASI,QAAQxD;EAEf,KAAKA,EAAMe;IACT;;EAEFf,EAAMjD,OAAOiD,EAAMe,UAAUhE;EAC7BiD,EAAM7B,OAAO6B,EAAMe,UAAU5C;EAC7B6B,EAAMa,OAAOb,EAAMe,UAAUF;EAC7Bb,EAAMX,OAAOW,EAAMe,UAAU1B;EAC7BW,EAAMmD,OAAOnD,EAAMe,UAAUoC;EAC7BnD,EAAMoD,iBAAiBpD,EAAMe,UAAUqC;EACvCpD,EAAMe,YAAYf,EAAMe,UAAUA;AACpC;;AAGA,SAAS2C,YAAY1D,GAAcoF;;EAGjC,IAAIvI,OAAOmD,MAAUA,EAAMX,MAAM;IAG/B,IAAM8D,IAAOnD,EAAMX,KAAKW,EAAMmD;IAC9B,IAAIA,EAAKvG,WAAW;MAClB,KAAMA,WAAEA,KAAcuG;MACtBnD,EAAMoD,kBAAkBpD,EAAMoD;MAE9B,KAAKpD,EAAMoD,kBAAkBxG,EAAUF;QACrC;;;IAIJ,IAAI0I;MACF;;;EAMJpF,EAAMoD,kBAAiB;EACvBpD,EAAMmD;EAGN,OACEnD,EAAMX,UACJxD,MAAMwJ,QAAQrF,EAAMX,SAASW,EAAMmD,OAAOnD,EAAMX,KAAK/E,SACvD;IACAkJ,QAAQxD;IAER,IAAIA,EAAMX;MAER,IAAIxC,OAAOmD;QAGT,IAAcc,UAAVA,IAAAd,EAAMX,cAAIyB,MAAAA,SAAAA,IAAAA,EAAGd,EAAMmD,MAAMvG;UAC3BoD,EAAMoD,kBAAkBpD,EAAMoD;;aAE3B;QACLpD,EAAMoD,kBAAiB;QACvBpD,EAAMmD;;;;AAId;;AAEA,SAAStG,OAAOmD;EACd,IAAMmD,IACJtH,MAAMwJ,QAAQrF,EAAMX,SACc,mBAA3BW,EAAMX,KAAKW,EAAMmD,SACvBnD,EAAMX,KAAKW,EAAMmD;EAEpB,OAAOA,KAAQA,EAAKtG;AACtB;;AAGA,SAASgI,aAAa7E;EAGpB,OACEA,EAAMX,UAGJxD,MAAMwJ,QAAQrF,EAAMX,UAASW,EAAMX,KAAKW,EAAMmD,MAAMzG;IAEtD8G,QAAQxD;;EAKV,IAAIA,EAAMX;IACRqE,YAAY1D,IAAO;;AAEvB;;;;;;ACvUM,MAAOsF;EAGXzL,WAAAA,CAAY0L,GAAkBC;IAa9BzL,KAAA0L,mBAAoBnK;MAClB,IAAIvB,KAAKwL,MAAMG,SAASpK,EAASoK;QAC/B,OAAO3L,KAAKwL,MAAM/I,aAAalB,EAASkB;;MAE1C,IAAIzC,KAAKyL,IAAIE,SAASpK,EAASoK;QAC7B,OAAO3L,KAAKyL,IAAIhJ,aAAalB,EAASkB;;MAExC,OAAOzC,KAAKwL,MAAMG,QAAQpK,EAASoK,QAAQ3L,KAAKyL,IAAIE,QAAQpK,EAASoK;AAAI;IAnBzE3L,KAAKwL,QAAQA;IACbxL,KAAKyL,MAAMA;AACb;EAEAG,QAAAA,CAASD,GAAclJ;IACrBzC,KAAKwL,QAAQ,IAAIK,SAASF,GAAMlJ;AAClC;EAEAqJ,MAAAA,CAAOH,GAAclJ;IACnBzC,KAAKyL,MAAM,IAAII,SAASF,GAAMlJ;AAChC;;;AAaI,MAAOoJ;EAGX/L,WAAAA,CAAY6L,GAAclJ;IAa1BzC,KAAA+L,oBAAqBxK,KACnBvB,KAAK2L,OAAOpK,EAASoK,QACpB3L,KAAK2L,SAASpK,EAASoK,QAAQ3L,KAAKyC,aAAalB,EAASkB;IAd3DzC,KAAK2L,OAAOA;IACZ3L,KAAKyC,YAAYA;AACnB;EAEAuJ,OAAAA,CAAQL;IACN3L,KAAK2L,OAAOA;AACd;EAEAM,YAAAA,CAAaxJ;IACXzC,KAAKyC,YAAYA;AACnB;;;AChBF,IAAMyJ,IAAoB,EACxBC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GACAC,GAEAC,GACAC;;ACLK,IAAMC,IAAsB;EACjC,CAXO,UAWW;EAClB,CAXS,YAWW;EACpB,CAXa,gBAWW;EACxB,CAXM,SAWW;;;AAGnB,IAAMC,YAAYA,CAACC,GAAgBC;EACjC,KAAKD;IACH,MAAM,IAAIE,MAAMD;;;;AAId,SAAUE,eACdC,GACAC,IAA2C,MAC3CC,GACAC,GACAC;;EAEA,IAAIC,IAAM;EACV,IAAIC,IAAY;EAChB,IAAIF;IACFE,IAC+B,mBAAtBF,IACHA,IACAA,EAAkBG,QAChB,CAACC,GAAKC,MAASD,IAAME,EAAMD,KAAQ,SACnC;;EAGV,IAAME,IAAgBL,IAAY,GAAGN,QAAYM,MAAcN;EAE/D;IACEK,IAAMO,EAAMD;AACb,IAAC,OAAOE;IACP,IAAIA,aAAiBC,GAAc;MACjC,IAAMC,IA0FN,SAAUC,SAASC,GAA0BC;QACjD,IAAMC,IAAS7F;QACf,IAAM1C,IAAQuI,EAAOvF;QACrB,IAAMwF,IAAQF,EAAUG,MAAM;QAE9B1B,UACEyB,EAAMlO,UAAU+N,EAAS3C,MACzB;QAGF,IAAI9G,IAAS;QAEb,KAAK,IAAIuF,IAAI,GAAGA,IAAIkE,EAAS3C,MAAMvB,KAAK;UACtCvF,IAAS,IAAIhF,gBAAgB4O,EAAMrE;UACnC,QAAQvF,EAAOxE,OAAO;YAEpB,IAAc,kBADAmO,EAAO9M,MAAMmD,GAAQoB;cAEjC;;;;QAKN+G,UAAUnI,GAAQ;QAClB,IAAM8G,IAAO2C,EAAS3C,OAAO;QAG7B,IAAMH,IAAQ3G,EAAO1E;QAGrB,IAAMsL,IAAM5G,EAAOzE;QACnB,OAAO,IAAImL,MAAM,IAAIM,SAASF,GAAMH,IAAQ,IAAIK,SAASF,GAAMF;AACjE,OAzHoB4C,CACQM,UAApBA,IAAe5H,UAAfA,IAAAmH,EAAMU,mBAAS7H,MAAAA,SAAAA,IAAAA,EAAG,YAAE4H,MAAAA,IAAAA,IAAI;QAAEhD,MAAM;QAAGzJ,QAAQ;SAC3C8L;MAGF,OAAO,EACL;QACEa,UAAU9B,EAAoBI;QAC9BD,SAASgB,EAAMhB;QACf4B,QAAQ;QACRV;;;IAIN,MAAMF;;EAGR,OAGI,SAAUa,cACdrB,GACAJ,IAA2C,MAC3CC,GACAC;IAGA,KAAKF;MACH,OAAO;;IAGT,IAAM0B,IDxDF,SAAUC,wBACd3B,GACAI,GACAH,GACAC,GACA0B;MAEA,IAAMhE,IAAQiE,EAAeC,QAAO9J;QAIlC,IAAIA,MAAS+J,KAAyB/J,MAASgK;UAC7C,QAAO;;QAET,IAAI9B,KAAqBlI,MAASiK;UAChC,QAAO;;QAET,QAAO;AAAI;MAGb,IAAIhC;QACFzL,MAAM0N,UAAUC,KAAKC,MAAMxE,GAAOqC;;MAEpC,IAAI2B;QACFpN,MAAM0N,UAAUC,KAAKC,MAAMxE,GAAOgB;;MAGpC,OADeyD,EAASrC,GAAQI,GAAKxC,GACvBkE,QAAOlB;QACnB,IAAIA,EAAMhB,QAAQ0C,SAAS,wBAAwB1B,EAAM2B,OAAO;UAC9D,IAAM/B,IAAOI,EAAM2B,MAAM;UACzB,IAAI/B,KAAQA,EAAK9K,SAASe,EAAK+L,WAAW;YACxC,IAAM1L,IAAO0J,EAAK1J,KAAKjB;YACvB,IAAa,gBAATiB,KAAiC,0BAATA;cAC1B,QAAO;;;;QAIb,QAAO;AAAI;AAEf,KCiBqC6K,CACjC3B,GACAI,GACAH,GACAC,GACAuC,SAAQ7B,KACR8B,YAAY9B,GAAOnB,EAAoBI,OAAO;IAIhD,IAAM8C,IAAgCN,EAASrC,GAAQI,GAAK,EAC1DwC,KACCH,SAAQ7B,KACT8B,YAAY9B,GAAOnB,EAAoBoD,SAAS;IAElD,OAAOnB,EAA2BtE,OAAOuF;AAC3C,GA9BSlB,CAAcrB,GAAKJ,GAAQC,GAAaC;AACjD;;AA+BA,SAASwC,YACP9B,GACAW,GACA/H;EAEA,KAAKoH,EAAM2B;IACT,OAAO;;EAET,IAAMO,IAAiC;EACvC,KAAK,KAAOhG,GAAG0D,MAASI,EAAM2B,MAAMQ,WAAW;IAC7C,IAAMC,IACU,eAAdxC,EAAK9K,QAAuB,UAAU8K,UAAsBtD,MAAdsD,EAAK1J,OAC/C0J,EAAK1J,OACL,cAAc0J,UAA0BtD,MAAlBsD,EAAKyC,WAC3BzC,EAAKyC,WACLzC;IACN,IAAIwC,GAAe;MACjBtD,UACEkB,EAAMU,WACN;MAKF,IAAM4B,IAAMtC,EAAMU,UAAUxE;MAC5B,IAAMqG,IAAeC,YAAYJ;MACjC,IAAM7E,IAAM+E,EAAItO,UAAUuO,EAAahF,MAAMgF,EAAajF;MAC1D4E,EAAiBX,KAAK;QACpBX,QAAQ,YAAYhI;QACpBoG,SAASgB,EAAMhB;QACf2B;QACAT,OAAO,IAAI7C,MACT,IAAIM,SAAS2E,EAAI7E,OAAO,GAAG6E,EAAItO,SAAS,IACxC,IAAI2J,SAAS2E,EAAI7E,OAAO,GAAGF;;;;EAKnC,OAAO2E;AACT;;AA0CA,SAASM,YAAY5C;EAEnB,IAAMQ,IADiBR,EACS0C;EAChCxD,UAAUsB,GAAU;EAGpB,OAAOA;AACT;;AC3NA,IAAAqC,IAUA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACVO,IAAMC,IAAY,IAAIC,IAAI,EAAC,OAAO;;ACIlC,IAAMC,SAAUhD,KACrBrO,EAAGsR,iBAAiBjD,MACM,MAA1BA,EAAKkD,UAAUzQ,WACdd,EAAGwR,qBAAqBnD,EAAKoD,eAC5BzR,EAAG0R,gBAAgBrD,EAAKoD,iBACzBpD,EAAKoD,WAAWE,kBAChBtD,EAAKoD,WAAWG,WAAW9Q;;AAGvB,IAAM+Q,8BACXxD,KAEArO,EAAG8R,aAAazD,MAAS8C,EAAUY,IAAI1D,EAAK2D;;AAGvC,IAAMC,wBAAwBA,CACnC5D,GACA6D;EAEA,KAAKlS,EAAGmS,yBAAyB9D;IAAO,QAAO;;EAC/C,IAAMhH,IAAO6K,GAASE,kBAAkB/D;EAGxC,OACU,QAARhH,KAC8B,QAA9BA,EAAKgL,YAAY,aACgB,QAAjChL,EAAKgL,YAAY;AAAoB;;AAKlC,IAAMC,oBAAoBA,CAC/BjE,GACA6D;EAIA,KAAKlS,EAAGsR,iBAAiBjD;IACvB,QAAO;SACF,IAAIA,EAAKkD,UAAUzQ,SAAS,KAAKuN,EAAKkD,UAAUzQ,SAAS;IAC9D,QAAO;SACF,KAAKd,EAAGuS,oBAAoBlE,EAAKkD,UAAU;IAChD,QAAO;;EAET,OAAOW,IAAUD,sBAAsB5D,EAAKoD,YAAYS,MAAW;AAAK;;AAInE,IAAMM,sBAAsBA,CACjCnE,GACA6D;EAEA,KAAK7D;IACH,QAAO;SACF,KAAKrO,EAAGsR,iBAAiBjD;IAC9B,QAAO;SACF,KAAKrO,EAAGyS,2BAA2BpE,EAAKoD;IAC7C,QAAO;SACF,KACJzR,EAAG8R,aAAazD,EAAKoD,WAAW9M,SACI,gBAArC0J,EAAKoD,WAAW9M,KAAKqN;IAErB,QAAO;SACF,IAAIH,4BAA4BxD,EAAKoD,WAAWA;IACrD,QAAO;;IAEP,OAAOQ,sBAAsB5D,EAAKoD,WAAWA,YAAYS;;AAC3D;;IASWQ,gBAAgBA,CAC3BrE,GACA6D,MAGElS,EAAGsR,iBAAiBjD,MACpBA,EAAKkD,UAAUzQ,UAAU,KACzBuN,EAAKkD,UAAUzQ,UAAU,MACxB+Q,4BAA4BxD,EAAKoD,eAChCa,kBAAkBjE,GAAM6D;;IAKjBS,eACXtE,KAEArO,EAAG4S,2BAA2BvE,MAASwD,4BAA4BxD,EAAKwE;;AAGnE,IAAMC,gBAAgBA,CAC3BzE,GACA0E,GACAP,KAAsB;EAEtB,KAAKO;IAAa,OAAO;;EACzB,IAAM1L,IAAO0L,EAAYX,kBAGvBI,IAAsBnE,EAAK2E,WAAW,GAAGA,WAAW,KAAK3E,EAAKoD;EAEhE,IAAIpK,GAAM;IACR,IAAM4L,IAAkB5L,EAAKgL,YAAY;IACzC,IAAIY,GAAiB;MACnB,IAAMC,IAAQH,EAAYI,gBAAgBF;MAC1C,IAAIC,EAAME,yBAAyB;QACjC,IAAMC,IAAQH,EAAMI,MAAMC,MAAKC,KAAKA,EAAEC;QACtC,OAAOJ,KAASA,EAAMI,oBAAoBJ,EAAM3P,QAAQ;AAC1D,aAAO,IAAIwP,EAAMO;QACf,OAAOP,EAAMxP;;AAEjB;AACF;EACA,OAAO;AAAI;;AC3EN,SAASgQ,mBAAmBrF;EACjC,QAAQA,EAAK9K;GACX,KAAKvD,EAAG2T,WAAWC;GACnB,KAAK5T,EAAG2T,WAAWE;GACnB,KAAK7T,EAAG2T,WAAWG;GACnB,KAAK9T,EAAG2T,WAAWI;GACnB,KAAK/T,EAAG2T,WAAWK;GACnB,KAAKhU,EAAG2T,WAAWM;GACnB,KAAKjU,EAAG2T,WAAWO;GACnB,KAAKlU,EAAG2T,WAAWQ;GACnB,KAAKnU,EAAG2T,WAAWS;GACnB,KAAKpU,EAAG2T,WAAWU;GACnB,KAAKrU,EAAG2T,WAAWW;GACnB,KAAKtU,EAAG2T,WAAWY;GACnB,KAAKvU,EAAG2T,WAAWa;GACnB,KAAKxU,EAAG2T,WAAWc;GACnB,KAAKzU,EAAG2T,WAAWe;GACnB,KAAK1U,EAAG2T,WAAWgB;GACnB,KAAK3U,EAAG2T,WAAWiB;GACnB,KAAK5U,EAAG2T,WAAWkB;GACnB,KAAK7U,EAAG2T,WAAWmB;GACnB,KAAK9U,EAAG2T,WAAWoB;GACnB,KAAK/U,EAAG2T,WAAWqB;IACjB,QAAO;;GACT;IACE,QAAO;;AAEb;;AAgBO,SAASC,2BACd5G;EAEA,QAAQA,EAAK9K;GACX,KAAKvD,EAAG2T,WAAWK;GACnB,KAAKhU,EAAG2T,WAAWI;GACnB,KAAK/T,EAAG2T,WAAWE;GACnB,KAAK7T,EAAG2T,WAAWM;GACnB,KAAKjU,EAAG2T,WAAWO;GACnB,KAAKlU,EAAG2T,WAAWQ;GACnB,KAAKnU,EAAG2T,WAAWW;GACnB,KAAKtU,EAAG2T,WAAWY;GACnB,KAAKvU,EAAG2T,WAAWa;GACnB,KAAKxU,EAAG2T,WAAWmB;GACnB,KAAK9U,EAAG2T,WAAWe;IACjB,OAAOrG;;GACT,KAAKrO,EAAG2T,WAAWG;GACnB,KAAK9T,EAAG2T,WAAWS;GACnB,KAAKpU,EAAG2T,WAAWc;GACnB,KAAKzU,EAAG2T,WAAWgB;GACnB,KAAK3U,EAAG2T,WAAWiB;GACnB,KAAK5U,EAAG2T,WAAWkB;GACnB,KAAK7U,EAAG2T,WAAWqB;IACjB,OAAO3G,EAAK6G;;GACd,KAAKlV,EAAG2T,WAAWU;IACjB,OAAOhG,EAAKoD;;GACd,KAAKzR,EAAG2T,WAAWC;IACjB,OAxCN,SAASuB,qBAAqBlT;MAC5B,QAAQA,EAAMsB;OACZ,KAAKvD,EAAG2T,WAAWyB;OACnB,KAAKpV,EAAG2T,WAAW0B;OACnB,KAAKrV,EAAG2T,WAAW2B;OACnB,KAAKtV,EAAG2T,WAAW4B;QACjB,QAAO;;OACT;QACE,QAAO;;AAEb,KA8BaJ,CAAqB9G,EAAKmH,iBAAiBnH,EAAKoH,aAAQ1K;;GACjE,KAAK/K,EAAG2T,WAAWoB;IACjB,OAAO1G,EAAKqH;;GACd;IACE;;AAEN;;AAGA,SAASC,iCAAiCtH;EACxC,IACEA,EAAKuH,UACL5V,EAAGyS,2BAA2BpE,EAAKuH,WACnCvH,EAAKuH,OAAOjR,SAAS0J;IAErB,OAAOA,EAAKuH;SACP,IACLvH,EAAKuH,UACL5V,EAAG6V,0BAA0BxH,EAAKuH,WAClCvH,EAAKuH,OAAOE,uBAAuBzH;IAEnC,OAAOA,EAAKuH;;IAEZ,OAAOvH;;AAEX;;AA2BA,SAAS0H,wBAAwBpR;EAC/B,IAAI3E,EAAGgW,uBAAuBrR;IAC5B,OAAO3E,EAAGuS,oBAAoB5N,EAAK8M,eACjCzR,EAAGiW,iBAAiBtR,EAAK8M,cACvB9M,EAAK8M,WAAWyE,YAChBnL;SACC,IAAI/K,EAAGmW,oBAAoBxR,MAAS3E,EAAGoW,aAAazR;IACzD,OAAO3E,EAAGqW,OAAO1R;;IAEjB,OAAOA,EAAKuR;;AAEhB;;AAWO,SAASI,2BACdjI,GACA6D;EAGA,IAAIqE,IAASrE,EAAQsE,oBAAoBnI;EACzC,IACEkI,GAAQE,eAAe,MACvBF,EAAOG,QAAQ1W,EAAG2W,YAAYC,UAC7BvI,EAAKuH,WAAWW,GAAQE,eAAe,OACrCzW,EAAG6W,kBAAkBN,EAAOE,aAAa,MAC5C;IAEA,IAAMK,IAAU5E,EAAQ6E,iBAAiBR;IACzC,IAAIO,EAAQL;MAAcF,IAASO;;AACrC;EAEA,IAAIP,KAAUvW,EAAGgX,8BAA8B3I,EAAKuH,SAAS;IAG3D,IAAMqB,IAAkB/E,EAAQgF,kCAC9BX,EAAOY;IAET,IAAIF;MAAiBV,IAASU;;AAChC,SAAO,IACLjX,EAAGoX,iBAAiB/I,EAAKuH,WACzB5V,EAAGqX,uBAAuBhJ,EAAKuH,OAAOA,WACtCvH,OAAUA,EAAKuH,OAAO0B,gBAAgBjJ,EAAKuH,OAAOjR,OAClD;IAGA,IAAMA,IAAOoR,wBAAwB1H;IACrC,IAAMkJ,IAAO5S,IACTuN,EAAQE,kBAAkB/D,EAAKuH,OAAOA,QAAQvD,YAAY1N,UAC1DoG;IACJ,IAAIwM;MAAMhB,IAASgB;;AACrB,SAAO,IACLvX,EAAGwX,uBAAuBnJ,EAAKuH,YAC9B5V,EAAGyX,0BAA0BpJ,EAAKuH,OAAOA,WACxC5V,EAAG0X,gBAAgBrJ,EAAKuH,OAAOA,YACjCvH,EAAKuH,OAAOjR,SAAS0J,GACrB;IAGA,IAAM1J,IAAOoR,wBAAwB1H;IACrC,IAAMkJ,IAAO5S,IACTuN,EAAQyF,kBAAkBtJ,EAAKuH,OAAOA,SAASvD,YAAY1N,UAC3DoG;IACJ,IAAIwM;MAAMhB,IAASgB;;AACrB;EAEA,IAAIhB,KAAUA,EAAOE,cAAc3V,QAAQ;IACzC,IACEyV,EAAOG,QAAQ1W,EAAG2W,YAAYiB,WAC5BrB,EAAOG,SAAS1W,EAAG2W,YAAYkB,WAAW7X,EAAG2W,YAAY3R,cApGjE,SAAS8S,sBAAsBzJ;MAC7B,IAAM0J,IAASpC,iCAAiCtH,GAAMuH;MACtD,OAAO5V,EAAGgY,gBAAgBD,MAAWA,EAAOtG,eAAepD;AAC7D,KAkGMyJ,CAAsBzJ;MAItB,KAAK,IAAM4J,KAAe1B,EAAOE;QAC/B,IAAIzW,EAAGkY,YAAYD;UAAc,OAAOA;;;WAErC,IAtGX,SAASE,4BACP9J;MAEA,IAAM0J,IAASpC,iCAAiCtH,GAAMuH;MACtD,OAAO5V,EAAGoY,sBAAsBL,MAAWA,EAAOtG,eAAepD;AACnE,KAkGM8J,CAA4B9J,MA/FlC,SAASgK,4BAA4BhK;MACnC,OACErO,EAAG8R,aAAazD,MAChBA,EAAKuH,UACL5V,EAAGsY,eAAejK,EAAKuH,WACvBvH,EAAKuH,OAAOjR,SAAS0J;AAEzB,KAyFMgK,CAA4BhK;MAI5B,KAAK,IAAM4J,KAAe1B,EAAOE;QAC/B,IACEzW,EAAGsY,eAAeL,MACfA,EAA2CM,QAC9C7E,mBAAmBuE;UAEnB,OAAOA;;;;IAOb,IACE1B,EAAOY,oBACPnX,EAAGyS,2BAA2B8D,EAAOY,mBACrC;MACA,IAAMvB,IAASW,EAAOY,iBAAiBvB;MACvC,IACEA,KACA5V,EAAGwY,mBAAmB5C,MACtBA,EAAO6C,SAASlC,EAAOY;QAEvB,OAAOvB;;AAEX;IAEA,IACEW,EAAOY,oBACPzD,mBAAmB6C,EAAOY;MAK1B,OAAOZ,EAAOY;;IAOhB,KAAK,IAAMc,KAAe1B,EAAOE;MAE/B,IAAI/C,mBAAmBuE;QAAc,OAAOA;;;AAEhD;EAEA;AACF;;AAGO,SAASS,qBACdrK,GACA6D;EAEA,OAAOlS,EAAG8R,aAAazD,IAAO;IAC5B,IAAM4J,IAAc3B,2BAA2BjI,GAAM6D;IACrD,KAAK+F;MACH;WACK;MACL,IAAMvU,IAAQuR,2BAA2BgD;MACzC,IAAIvU,KAAS1D,EAAG8R,aAAapO,MAAUA,MAAU2K;QAG/CA,IAAO3K;;QAEP,OAAOA;;AAEX;AACF;AACF;;ACzTO,SAASiV,gBACdtK,GACAuK,GACAC;EAEA,IAAI7Y,EAAGuS,oBAAoBlE;IACzB,OAAO;MAAEyK,cAAczK,EAAK0K,UAAU3W,MAAM,IAAI;MAAI4W,eAAe;;;EAGrE,IAAIC,IAAe5K,EAAK6K,SAASH,UAAU3W,MAAM,IAAI;EACrD,IACEpC,EAAGmZ,gCAAgC9K,EAAK6K,aACD,MAAvC7K,EAAK6K,SAASE,cAActY;IAE5B,OAAO;MAAEgY,cAAcG;MAAcD,eAAe;;;EAGtD,IAAIK,IAAkB;EACtB,IAAML,IAAgB3K,EAAK6K,SAASE,cACjCE,KAAIC;IACH,IAAIvZ,EAAG8R,aAAayH,EAAK9H,aAAa;MACpC,IAAMsB,IAAc8F,EAAKW,gBAAgBC,cAAcC;MACvD,KAAK3G;QAAa;;MAElB,IAAMkF,IAAc3B,2BAClBiD,EAAK9H,YACLsB;MAEF,KAAKkF;QAAa;;MAElB,IAAMrC,IAASqC;MACf,IAAIjY,EAAG2Z,sBAAsB/D,IAAS;QACpC,IAAMgE,IAAiBL,EAAK9H,WAAWO;QACvC,IAAMtO,IAAQuR,2BAA2BW;QACzC,KAAKlS;UAAO;;QAGZ,IAAMmW,IAAgBN,EAAK9H,WAAWqI,aAAa;QACnD,IAAMC,IAAgB;UACpBhO,OAAO8N;UAEP/Y,QAAQyY,EAAK9H,WAAWzF,MAAM6N,IAAgB;;QAGhD,IAAI7Z,EAAG4S,2BAA2BlP,IAAQ;UACxC,IAAMwS,IAAOyC,gBACXjV,GACAkS,EAAOoE,gBAAgBC,UACvBpB;UAEFI,IAAeA,EAAaiB,QAC1B,OAAOX,EAAK9H,WAAWO,cAAc,KACrCkE,EAAK4C;UAGP,IAAMqB,IAAc;YAClBnL,OAAOkH,EAAK4C,aAAa7J,MAAM,MAAMnO;YACrCsZ,YAAYR;YACZS,UAAUN;YACVO,KAAK;cACHvO,OAAOgO,EAAchO,QAAQsN;cAC7BvY,QAAQoV,EAAK4C,aAAahY;;;UAG9BuY,KAAmBnD,EAAK4C,aAAahY,SAASiZ,EAAcjZ;UAC5D,OAAOqZ;AACT,eAAO,IACLna,EAAGua,eAAe7W,MAClB1D,EAAG4S,2BAA2BlP,EAAM+N,aACpC;UACA,IAAMyE,IAAOyC,gBACXjV,EAAM+N,YACNmE,EAAOoE,gBAAgBC,UACvBpB;UAEFI,IAAeA,EAAaiB,QAC1B,OAAOX,EAAK9H,WAAWO,cAAc,KACrCkE,EAAK4C;UAEP,IAAMqB,IAAc;YAClBnL,OAAOkH,EAAK4C,aAAa7J,MAAM,MAAMnO;YACrCsZ,YAAYR;YACZS,UAAUN;YACVO,KAAK;cACHvO,OAAOgO,EAAchO,QAAQsN;cAC7BvY,QAAQoV,EAAK4C,aAAahY;;;UAG9BuY,KAAmBnD,EAAK4C,aAAahY,SAASiZ,EAAcjZ;UAC5D,OAAOqZ;AACT,eAAO,IACLna,EAAGua,eAAe7W,MAClB1D,EAAGua,eAAe7W,EAAM+N,eACxBzR,EAAGyX,0BAA0B/T,EAAM+N,WAAWA,aAC9C;UAEA,IAAM+I,IAAmBlM,MADPmM,KAAKjM,MAAM9K,EAAM+N,WAAWA,WAAWsH;UAEzDE,IAAeA,EAAaiB,QAC1B,OAAOX,EAAK9H,WAAWO,cAAc,KACrCwI;UAEF,IAAML,IAAc;YAClBnL,OAAOwL,EAAiBvL,MAAM,MAAMnO;YACpCsZ,YAAYR;YACZS,UAAUN;YACVO,KAAK;cACHvO,OAAOgO,EAAchO,QAAQsN;cAC7BvY,QAAQ0Z,EAAiB1Z;;;UAG7BuY,KAAmBmB,EAAiB1Z,SAASiZ,EAAcjZ;UAC3D,OAAOqZ;AACT;QAEA;AACF;AACF;IAEA;AAAgB,MAEjBxK,OAAO+K;EAEV,OAAO;IAAE5B,cAAcG;IAAcD;;AACvC;;AAEO,IAAM2B,2BACXtM;EAEA,KAAKA;IAAM;;EAEX,OAAOrO,EAAGua,eAAelM;IAAOA,IAAOA,EAAKoD;;EAC5C,KAAKzR,EAAG4a,yBAAyBvM;IAAO;;EAExC,IAAIA,EAAKwM,SAAS5U,MAAMjG,EAAG8R;IAAe,OAAOzD,EAAKwM;;EACtD,IAAMC,IAA+B;EACrC,KAAK,IAAIC,KAAW1M,EAAKwM,UAAU;IACjC,OAAO7a,EAAGyS,2BAA2BsI;MAAUA,IAAUA,EAAQpW;;IACjE,IAAI3E,EAAG8R,aAAaiJ;MAAUD,EAAY9K,KAAK+K;;AACjD;EACA,OAAOD;AAAW;;AChJb,SAASE,UAAUnC,GAAkCD;EAC1D,IAAMqC,IAAUpC,EAAKW,gBAAgBC;EACrC,KAAKwB;IAAS;;EAEd,IAAM5L,IAAS4L,EAAQjB,cAAcpB;EACrC,KAAKvJ;IAAQ;;EAEb,OAAOA;AACT;;AAEO,SAAS6L,SACdC,GACArZ;EAOA,OALA,SAASyR,KAAKlF;IACZ,IAAIvM,KAAYuM,EAAKyL,cAAchY,IAAWuM,EAAK+M;MACjD,OAAOpb,EAAGqb,aAAahN,GAAMkF,SAASlF;;AAE1C,GACOkF,CAAK4H;AACd;;AAsCA,SAASG,eACPP,GACAlC,GACA3G;EAEA,IAAMhE,IAAsC;EAC5C,IAAM2M,IAA4B,EAACE;EACnC,IAAMQ,IAAO,IAAIC;EAEjB,IAAMC,iBAAkBV;IACtB,IAAIQ,EAAKxJ,IAAIgJ;MAAU;;IACvBQ,EAAKG,IAAIX;IAET,IAAM1M,IA3BV,SAASsN,+BACPC,GACA/C,GACA3G;MAEA,KAAKA;QAAS,OAAO;;MAErB,IAAMxO,IAAQgV,qBAAqBkD,GAAO1J;MAC1C,KAAKxO;QAAO,OAAO;;MAGnB,OAAOmY,cAAqBnY,GAAOwO,KAAWxO,IAAQ;AACxD,KAeiBiY,CAA+BZ,GAASlC,GAAM3G;IAC3D,KAAK7D;MAAM;;IAEX,IAAMyN,IAAenB,yBAAyBtM,EAAKkD,UAAU;IAC7D,IAAIuK;MAAcjB,EAAS7K,QAAQ8L;;IAEnC;MAEiBtN,EADFH,EAAKkD,UAAU,GACFwH,UAAU3W,MAAM,IAAI,IAAI;QAAE2Z,aAAY;SACzDC,YAAYC,SAAQC;QACzB,IAAwB,yBAApBA,EAAW3Y;UACb2K,EAAU8B,KAAKkM;;AACjB;AAEH,MAAC,OAAOC,IACP;AAAA;EAIJ,IAAIC;EACJ,YAA4CrR,OAApCqR,IAAcvB,EAASwB;IAC7BZ,eAAeW;;EACjB,OAAOlO;AACT;;AAEO,SAASoO,oBACdC,GACAC,GACA3D;EAEA,IAAM9F,IAAc8F,EAAKW,gBAAgBC,cAAcC;EACvD6C,EAAe1B,SAASoB,SAAQlB;IAC9B,IAAI/a,EAAG8R,aAAaiJ;MAClByB,EAAIxM,QAAQsL,eAAeP,GAASlC,GAAM9F;WACrC,IAAI/S,EAAGyS,2BAA2BsI,IAAU;MACjD,IAAI0B,IAAK1B;MACT,OAAO/a,EAAGyS,2BAA2BgK,EAAGhL;QAAagL,IAAKA,EAAGhL;;MAC7D,IAAIzR,EAAG8R,aAAa2K,EAAG9X;QACrB6X,EAAIxM,QAAQsL,eAAemB,EAAG9X,MAAMkU,GAAM9F;;AAE9C;AAAA;EAGF,OAAOyJ;AACT;;AAEO,SAASE,uBACdvB,GACAtC,GACA8D,KAAiC;EAQjC,IAAM5J,IAAc8F,EAAKW,gBAAgBC,cAAcC;EACvD,IAAMkD,IAGD;EACL,IAAI1O,IAA2C;EAC/C,IAAI2O,IAA0BF,KAAwB,KAAQ;GAE9D,SAASpJ,KAAKlF;IACZ,KAAKrO,EAAGsR,iBAAiBjD,MAASwN,OAAcxN;MAC9C,OAAOrO,EAAGqb,aAAahN,GAAMkF;;IAK/B,KAAKsI,cAAqBxN,GAAM0E;MAC9B,OAAO/S,EAAGqb,aAAahN,GAAMkF;;IAG/B,IAAM5O,IAAOkX,cAAqBxN,GAAM0E;IACxC,IAAMmD,IAAO7H,EAAKkD,UAAU;IAC5B,IAAMuK,IAAenB,yBAAyBtM,EAAKkD,UAAU;IAE7D,KAAKsL,MAA4Bf,GAAc;MAC7Ce,KAA0B;MAC1B3O,EAAU8B,QAAQ8M,gBAAgB3B,EAAWlB,UAAU5L,GAAMwK;AAC9D,WAAM,IAAIiD;MACT,KAAK,IAAM1B,KAAc0B;QACvB5N,EAAU8B,QAAQsL,eAAelB,GAAYvB,GAAM9F;;;IAIvD,IAAImD,KAAQlW,EAAGuS,oBAAoB2D;MACjC0G,EAAO5M,KAAK;QAAE3B,MAAM6H;QAAMrI,QAAQlJ;;;AAEtC,GACA4O,CAAK4H;EACL,OAAO;IAAE/K,OAAOwM;IAAQ1O;;AAC1B;;AAUO,SAAS6O,gCACd5B,GACAtC;EAEA,IAAM+D,IAEF;EACJ,IAAM7J,IAAc8F,GAAMW,gBAAgBC,cAAcC;GACxD,SAASnG,KAAKlF;IACZ,KAAKrO,EAAGsR,iBAAiBjD,MAASwN,OAAcxN;MAC9C,OAAOrO,EAAGqb,aAAahN,GAAMkF;;IAG/B,KAAKsI,oBAA2BxN,GAAM0E;MACpC;WACK,IAAI8F,GAAM;MACf,IAAMlU,IAAOkX,cAAqBxN,GAAM0E,IAAa;MACrD6J,EAAO5M,KAAK;QAAE3B;QAAMR,QAAQlJ;;AAC9B;MACEiY,EAAO5M,KAAK3B;;AAEhB,GACAkF,CAAK4H;EACL,OAAOyB;AACT;;AAEO,SAASE,gBACd7C,GACA5L,GACAwK;EAEA,IAAI3K,IAA2C;EAE/C,IAAM6E,IAAc8F,EAAKW,gBAAgBC,cAAcC;EACvD,KAAK1Z,EAAGsR,iBAAiBjD;IACvB,OAAOH;;EAGT,IAAM4N,IAAenB,yBAAyBtM,EAAKkD,UAAU;EAC7D,IAAIuK,GAAc;IAChB,IAAM/I,IAAc8F,EAAKW,gBAAgBC,cAAcC;IACvD,KAAK,IAAMU,KAAc0B;MACvB5N,EAAU8B,QAAQsL,eAAelB,GAAYvB,GAAM9F;;IAErD,OAAO7E;AACR,SAAM,IAAI2N,kBAAyBxN,GAAM0E;IACxC,OAAO7E;;EAGT,KAAK6E;IAAa,OAAO7E;;EAEzB,IAAMkM,IF8FD,SAAS4C,+BACd3O;IAEA,IAAI0J,IAAoC1J;IACxC,OAAO0J;MACL,IAAI/X,EAAGyS,2BAA2BsF;QAChCA,IAASA,EAAOpT;aACX,IACL3E,EAAGua,eAAexC,MAClB/X,EAAGid,sBAAsBlF,MACzB/X,EAAGkd,oBAAoBnF,MACvB/X,EAAGmd,0BAA0BpF,MAC7B/X,EAAGod,8BAA8BrF;QAEjCA,IAASA,EAAOtG;aACX,IAAIzR,EAAGqd,sBAAsBtF;QAClCA,IAASA,EAAO8C,SAAS9C,EAAO8C,SAAS/Z,SAAS;aAC7C,IAAId,EAAG8R,aAAaiG;QACzB,OAAOA;;QAEP;;;AAGN,GErHqBiF,CAA+B3O,EAAKoD;EACvD,KAAK2I;IAAY,OAAOlM;;EAExB,IAAM+J,IAAc3B,2BAA2B8D,GAAYrH;EAC3D,KAAKkF;IAAa,OAAO/J;;EAEzB,IAAMiN,IAAalD,EAAY+B;EAC/B,KAAKmB;IAAY,OAAOjN;;EAExB,IAAM8N,IAAc,EAClB;IACE/B,UAAUkB,EAAWlB;IACrBqD,UAAU;MACRvR,OAAOkM,EAAY6B;MACnBhZ,QAAQmX,EAAYsF;;;EAI1B,KAAKvB,MAAgBA,EAAYlb;IAAQ,OAAOoN;;EAEhD,IAAMsP,IAAMxB,EAAY;EACxB,KAAKwB;IAAK,OAAOtP;;EACjB,IAAMuP,IAAMzC,UAAUnC,GAAM2E,EAAIvD;EAChC,KAAKwD;IAAK,OAAOvP;;EAEjBlO,EAAGqb,aAAaoC,IAAKpP;IACnB,IACErO,EAAG0d,oBAAoBrP,MACvBA,EAAKsP,mBACLtP,EAAKsP,gBAAgBlH,aAAa,MACsB,gBAAxDpI,EAAKsP,gBAAgBlH,aAAa,GAAG9R,KAAKoU,WAC1C;MACA,KAAOd,KAAe5J,EAAKsP,gBAAgBlH;MAC3C,IACEwB,EAAY/C,eACZlV,EAAGyX,0BAA0BQ,EAAY/C;QAEzC+C,EAAY/C,YAAY0I,WAAW3B,SAAQ4B;UACzC,IACE7d,EAAG8d,qBAAqBD,MACxB7d,EAAGyT,gBAAgBoK,EAASlZ;YAE5B;cACE,IAAMoZ,IAAmBtD,KAAKjM,MAC3B,GAAEqP,EAASlZ,KAAKoU,UAAUmB,QAAQ,MAAM;cAG3C,IACE6D,EAAiB5N,SAAS,gBAC1B4N,EAAiB5N,SAAS,SAC1B;gBACe3B,EAAMuP,GAAkB;kBACrChC,aAAY;mBAEPC,YAAYC,SAAQC;kBACzB,IAAwB,yBAApBA,EAAW3Y;oBACb2K,EAAU8B,KAAKkM;;AACjB;AAEJ;AACF,cAAE,OAAOhL,IAAS;;AACpB;;AAGN;AAAA;EAGF,OAAOhD;AACT;;AAQO,SAAS8P,iBAAiB3P;EAC/B,OACErO,EAAGmZ,gCAAgC9K,MACnCrO,EAAGie,QAAQ5P,MACXrO,EAAGke,qBAAqB7P,MACxBrO,EAAGme,eAAe9P;IAElBA,IAAOA,EAAKuH;;EAGd,OAAOvH;AACT;;AAEO,SAAS+P,uBAAuB/P;EACrC,OACErO,EAAGuS,oBAAoBlE,MACvBrO,EAAGie,QAAQ5P,MACXrO,EAAGke,qBAAqB7P,MACxBrO,EAAGme,eAAe9P;IAElBA,IAAOA,EAAKuH;;EAGd,OAAOvH;AACT;;AC7UA,IAAMgQ,IACmB,mBAAhBC,eACPA,eAC2B,qBAApBA,YAAYC,MACfD,cACAE;;AAEN,IAAMC,IAAS,IAAIrN;;AAMnB,IAAMsN,IACe,mBAAZC,WAA0BA,UAAUA,UAAU;;AAIvD,IAAMC,cAAcA,CAClBC,GACAxX,GACAyX,GACAC;EAE+B,qBAAxBL,EAAQE,cACXF,EAAQE,YAAYC,GAAKxX,GAAMyX,GAAMC,KACrCC,QAAQvQ,MAAM,IAAIqQ,MAASzX,MAASwX;AAAM;;AAGhD,IAAII,IAAKC,WAAWC;;AACpB,IAAIC,IAAKF,WAAWG;;AAGpB,SAAkB,MAAPJ,GAAoB;EAE7BG,IAAK,MAAMC;IAETC,SAAqC;IAErCC,SAAmB;IACnBC,gBAAAA,CAAiBC,GAAWV;MAC1Bxe,KAAK+e,SAAStP,KAAK+O;AACrB;;EAGFE,IAAK,MAAME;IACT9e,WAAAA;MACEqf;AACF;IACAC,OAAS,IAAIP;IACbQ,KAAAA,CAAMC;MACJ,IAAItf,KAAKof,OAAOJ;QAAS;;MAEzBhf,KAAKof,OAAOE,SAASA;MAErBtf,KAAKof,OAAOJ,WAAU;MAEtB,KAAK,IAAMR,KAAMxe,KAAKof,OAAOL;QAC3BP,EAAGc;;MAELtf,KAAKof,OAAOG,UAAUD;AACxB;;EAEF,IAAIE,KAC2C,QAA7CrB,EAAQsB,KAAKC;EACf,IAAMP,iBAAiBA;IACrB,KAAKK;MAAwB;;IAC7BA,MAAyB;IACzBnB,YACE,oaAOA,uBACA,WACAc;AACD;;;AAWL,IAAMQ,WAAYC,KAChBA,KAAKA,MAAM9V,KAAKC,MAAM6V,MAAMA,IAAI,KAAKC,SAASD;;AAchD,IAAME,eAAgBC,MACnBJ,SAASI,KACN,OACAA,KAAOjW,KAAKkW,IAAI,GAAG,KACnBC,aACAF,KAAOjW,KAAKkW,IAAI,GAAG,MACnBE,cACAH,KAAOjW,KAAKkW,IAAI,GAAG,MACnBG,cACAJ,KAAOtc,OAAO2c,mBACdC,YACA;;AAGN,MAAMA,kBAAkBve;EACtBhC,WAAAA,CAAYwgB;IACVC,MAAMD;IACNtgB,KAAKwgB,KAAK;AACZ;;;AAIF,MAAMC;EAIJC,WAAgC;EAChC,aAAOC,CAAOZ;IACZ,IAAMa,IAAUd,aAAaC;IAC7B,KAAKa;MAAS,OAAO;;IACrBH,OAAMI,KAAgB;IACtB,IAAMC,IAAI,IAAIL,MAAMV,GAAKa;IACzBH,OAAMI,KAAgB;IACtB,OAAOC;AACT;EACAhhB,WAAAA,CACEigB,GACAa;IAGA,KAAKH,OAAMI;MACT,MAAM,IAAIzV,UAAU;;IAGtBpL,KAAK+gB,OAAO,IAAIH,EAAQb;IACxB/f,KAAKO,SAAS;AAChB;EACAkP,IAAAA,CAAKmQ;IACH5f,KAAK+gB,KAAK/gB,KAAKO,YAAYqf;AAC7B;EACAoB,GAAAA;IACE,OAAOhhB,KAAK+gB,OAAO/gB,KAAKO;AAC1B;;;AAwoBI,MAAO0gB;EAIFlB;EACAmB;EACAC;EACAC;EACAC;EAkETf;EACAgB;EACAC;EACAC;EACAC;EACA9gB;EACA+gB;EACAC;EACAC;EACAC;EACAC;EACAC;EACAC;EACAC;EAEAC;EACAC;EACAC;EAWA,4BAAOC,CAILC;IACA,OAAO;MAELN,QAAQM,GAAEN;MACVC,MAAMK,GAAEL;MACRF,OAAOO,GAAEP;MACTR,QAAQe,GAAEf;MACVC,SAASc,GAAEd;MACXC,SAASa,GAAEb;MACX9gB,MAAM2hB,GAAE3hB;MACR+gB,MAAMY,GAAEZ;MACR,QAAIC;QACF,OAAOW,GAAEX;AACV;MACD,QAAIC;QACF,OAAOU,GAAEV;AACV;MACDC,MAAMS,GAAET;MAERU,mBAAoBrf,KAAWof,GAAEC,EAAmBrf;MACpDsf,iBAAiBA,CACfC,GACAC,GACA9Z,GACA+Z,MAEAL,GAAEE,EACAC,GACAC,GACA9Z,GACA+Z;MAEJC,YAAaF,KACXJ,GAAEM,EAAYF;MAChBG,SAAUja,KACR0Z,GAAEO,EAASja;MACbka,UAAWla,KACT0Z,GAAEQ,EAAUla;MACdma,SAAUL,KACRJ,GAAES,EAASL;;AAEjB;EAOA,OAAI3C;IACF,OAAO/f,MAAK+f;AACd;EAIA,WAAImB;IACF,OAAOlhB,MAAKkhB;AACd;EAIA,kBAAII;IACF,OAAOthB,MAAKshB;AACd;EAIA,QAAIhB;IACF,OAAOtgB,MAAKsgB;AACd;EAIA,eAAIe;IACF,OAAOrhB,MAAKqhB;AACd;EAIA,WAAIF;IACF,OAAOnhB,MAAKmhB;AACd;EAIA,gBAAIC;IACF,OAAOphB,MAAKohB;AACd;EAEAthB,WAAAA,CACE8I;IAEA,KAAMmX,KACJA,IAAM,GAACiD,KACPA,GAAGC,eACHA,IAAgB,GAACC,cACjBA,GAAYC,gBACZA,GAAcC,gBACdA,GAAcC,YACdA,GAAUlC,SACVA,GAAOC,cACPA,GAAYkC,gBACZA,GAAcC,aACdA,GAAWrC,SACXA,IAAU,GAACsC,cACXA,IAAe,GAACC,iBAChBA,GAAepC,aACfA,GAAWqC,0BACXA,GAAwBC,oBACxBA,GAAkBC,4BAClBA,GAA0BC,wBAC1BA,GAAsBC,kBACtBA,KACElb;IAEJ,IAAY,MAARmX,MAAcJ,SAASI;MACzB,MAAM,IAAI3U,UAAU;;IAGtB,IAAM2Y,IAAYhE,IAAMD,aAAaC,KAAOje;IAC5C,KAAKiiB;MACH,MAAM,IAAI5W,MAAM,wBAAwB4S;;IAG1C/f,MAAK+f,IAAOA;IACZ/f,MAAKkhB,IAAWA;IAChBlhB,KAAKwjB,eAAeA,KAAgBxjB,MAAKkhB;IACzClhB,KAAKyjB,kBAAkBA;IACvB,IAAIzjB,KAAKyjB,iBAAiB;MACxB,KAAKzjB,MAAKkhB,MAAalhB,KAAKwjB;QAC1B,MAAM,IAAIpY,UACR;;MAGJ,IAAoC,qBAAzBpL,KAAKyjB;QACd,MAAM,IAAIrY,UAAU;;;IAIxB,SACkBZ,MAAhB6W,KACuB,qBAAhBA;MAEP,MAAM,IAAIjW,UACR;;IAGJpL,MAAKqhB,IAAeA;IACpBrhB,MAAKmiB,MAAoBd;IAEzBrhB,MAAKuhB,IAAU,IAAIyC;IACnBhkB,MAAKwhB,IAAW,IAAI1f,MAAMie,GAAKS,UAAKhW;IACpCxK,MAAKyhB,IAAW,IAAI3f,MAAMie,GAAKS,UAAKhW;IACpCxK,MAAKW,IAAQ,IAAIojB,EAAUhE;IAC3B/f,MAAK0hB,IAAQ,IAAIqC,EAAUhE;IAC3B/f,MAAK2hB,IAAQ;IACb3hB,MAAK4hB,IAAQ;IACb5hB,MAAK6hB,IAAQpB,MAAME,OAAOZ;IAC1B/f,MAAKsgB,IAAQ;IACbtgB,MAAKshB,IAAkB;IAEvB,IAAuB,qBAAZH;MACTnhB,MAAKmhB,IAAWA;;IAElB,IAA4B,qBAAjBC,GAA6B;MACtCphB,MAAKohB,IAAgBA;MACrBphB,MAAK8hB,IAAY;WACZ;MACL9hB,MAAKohB,SAAgB5W;MACrBxK,MAAK8hB,SAAYtX;;IAEnBxK,MAAKkiB,MAAgBliB,MAAKmhB;IAC1BnhB,MAAKoiB,MAAqBpiB,MAAKohB;IAE/BphB,KAAKsjB,mBAAmBA;IACxBtjB,KAAKujB,gBAAgBA;IACrBvjB,KAAK0jB,6BAA6BA;IAClC1jB,KAAK4jB,+BAA+BA;IACpC5jB,KAAK6jB,2BAA2BA;IAChC7jB,KAAK8jB,qBAAqBA;IAG1B,IAA0B,MAAtB9jB,KAAKwjB,cAAoB;MAC3B,IAAsB,MAAlBxjB,MAAKkhB;QACP,KAAKvB,SAAS3f,MAAKkhB;UACjB,MAAM,IAAI9V,UACR;;;MAIN,KAAKuU,SAAS3f,KAAKwjB;QACjB,MAAM,IAAIpY,UACR;;MAGJpL,MAAKikB;;IAGPjkB,KAAKqjB,eAAeA;IACpBrjB,KAAK2jB,uBAAuBA;IAC5B3jB,KAAKmjB,mBAAmBA;IACxBnjB,KAAKojB,mBAAmBA;IACxBpjB,KAAKijB,gBACHtD,SAASsD,MAAoC,MAAlBA,IACvBA,IACA;IACNjjB,KAAKkjB,iBAAiBA;IACtBljB,KAAKgjB,MAAMA,KAAO;IAClB,IAAIhjB,KAAKgjB,KAAK;MACZ,KAAKrD,SAAS3f,KAAKgjB;QACjB,MAAM,IAAI5X,UACR;;MAGJpL,MAAKkkB;;IAIP,IAAkB,MAAdlkB,MAAK+f,KAA2B,MAAb/f,KAAKgjB,OAA+B,MAAlBhjB,MAAKkhB;MAC5C,MAAM,IAAI9V,UACR;;IAGJ,KAAKpL,KAAKkjB,iBAAiBljB,MAAK+f,MAAS/f,MAAKkhB,GAAU;MACtD,IAAM3C,IAAO;MACb,IA3hCcA,OAAkBL,EAAO1M,IAAI+M,GA2hCvC4F,CAAW5F,IAAO;QACpBL,EAAO/C,IAAIoD;QAIXF,YAFE,iGAEe,yBAAyBE,GAAM0C;;;AAGtD;EAKAmD,eAAAA,CAAgBC;IACd,OAAOrkB,MAAKuhB,EAAQ/P,IAAI6S,KAAOC,QAAW;AAC5C;EAEA,EAAAJ;IACE,IAAMjC,IAAO,IAAI5B,UAAUrgB,MAAK+f;IAChC,IAAMiC,IAAS,IAAI3B,UAAUrgB,MAAK+f;IAClC/f,MAAKiiB,IAAQA;IACbjiB,MAAKgiB,IAAUA;IAEfhiB,MAAKukB,IAAc,CAAC7B,GAAOM,GAAKxX,IAAQsS,EAAKE;MAC3CgE,EAAOU,KAAiB,MAARM,IAAYxX,IAAQ;MACpCyW,EAAKS,KAASM;MACd,IAAY,MAARA,KAAahjB,KAAKkjB,cAAc;QAClC,IAAMngB,IAAIyhB,YAAW;UACnB,IAAIxkB,MAAK+iB,EAASL;YAChB1iB,KAAKykB,OAAOzkB,MAAKwhB,EAASkB;;YAE3BM,IAAM;QAGT,IAAIjgB,EAAE2hB;UACJ3hB,EAAE2hB;;;;IAMR1kB,MAAK2kB,IAAiBjC;MACpBV,EAAOU,KAAyB,MAAhBT,EAAKS,KAAe5E,EAAKE,QAAQ;AAAC;IAGpDhe,MAAK4kB,IAAa,CAACC,GAAQnC;MACzB,IAAIT,EAAKS,IAAQ;QACf,IAAMM,IAAMf,EAAKS;QACjB,IAAMlX,IAAQwW,EAAOU;QACrBmC,EAAO7B,MAAMA;QACb6B,EAAOrZ,QAAQA;QACfqZ,EAAO7G,MAAM8G,KAAaC;QAE1BF,EAAOG,eAAehC,KADV6B,EAAO7G,MAAMxS;;;IAO7B,IAAIsZ,IAAY;IAChB,IAAMC,SAASA;MACb,IAAMnF,IAAI9B,EAAKE;MACf,IAAIhe,KAAKijB,gBAAgB,GAAG;QAC1B6B,IAAYlF;QACZ,IAAM7c,IAAIyhB,YACR,MAAOM,IAAY,IACnB9kB,KAAKijB;QAIP,IAAIlgB,EAAE2hB;UACJ3hB,EAAE2hB;;;MAIN,OAAO9E;AAAC;IAGV5f,KAAKokB,kBAAkBC;MACrB,IAAM3B,IAAQ1iB,MAAKuhB,EAAQ0D,IAAIZ;MAC/B,SAAc7Z,MAAVkY;QACF,OAAO;;MAET,IAAMM,IAAMf,EAAKS;MACjB,IAAMlX,IAAQwW,EAAOU;MACrB,IAAY,MAARM,KAAuB,MAAVxX;QACf,OAAO8Y;;MAGT,OAAOtB,MADM8B,KAAaC,YAAYvZ;AACtB;IAGlBxL,MAAK+iB,IAAWL,KAEI,MAAhBT,EAAKS,MACa,MAAlBV,EAAOU,OACNoC,KAAaC,YAAY/C,EAAOU,KAAST,EAAKS;AAGrD;EAGAiC,GAAyCO;EACzCN,GACEO;EACFZ,GAMYa;EAGZrC,GAAsCsC,OAAM;EAE5C,EAAApB;IACE,IAAMlC,IAAQ,IAAI1B,UAAUrgB,MAAK+f;IACjC/f,MAAKshB,IAAkB;IACvBthB,MAAK+hB,IAASA;IACd/hB,MAAKslB,IAAkB5C;MACrB1iB,MAAKshB,KAAmBS,EAAMW;MAC9BX,EAAMW,KAAS;AAAC;IAElB1iB,MAAKulB,IAAe,CAAC9C,GAAG+C,GAAGlF,GAAMmD;MAG/B,IAAIzjB,MAAKuiB,EAAmBiD;QAC1B,OAAO;;MAET,KAAK7F,SAASW;QACZ,IAAImD,GAAiB;UACnB,IAA+B,qBAApBA;YACT,MAAM,IAAIrY,UAAU;;UAEtBkV,IAAOmD,EAAgB+B,GAAG/C;UAC1B,KAAK9C,SAASW;YACZ,MAAM,IAAIlV,UACR;;;UAIJ,MAAM,IAAIA,UACR;;;MAMN,OAAOkV;AAAI;IAEbtgB,MAAKylB,IAAe,CAClB/C,GACApC,GACAuE;MAEA9C,EAAMW,KAASpC;MACf,IAAItgB,MAAKkhB,GAAU;QACjB,IAAMA,IAAUlhB,MAAKkhB,IAAWa,EAAMW;QACtC,OAAO1iB,MAAKshB,IAAkBJ;UAC5BlhB,MAAK0lB,GAAO;;;MAGhB1lB,MAAKshB,KAAmBS,EAAMW;MAC9B,IAAImC,GAAQ;QACVA,EAAOc,YAAYrF;QACnBuE,EAAOe,sBAAsB5lB,MAAKshB;;;AAGxC;EAEAgE,GAA0CO;EAC1CJ,GAIYK,CAACD,GAAIE,GAAIC,OAATF;EACZP,GAKqBU,CACnBC,GACAC,GACA7F,GACAmD;IAEA,IAAInD,KAAQmD;MACV,MAAM,IAAIrY,UACR;;IAGJ,OAAO;AAAC;EAGV,IAACyX,EAASQ,YAAEA,IAAarjB,KAAKqjB,cAAe;IAC3C,IAAIrjB,MAAKsgB;MACP,KAAK,IAAIlW,IAAIpK,MAAK4hB,GAAO,KAAQ;QAC/B,KAAK5hB,MAAKomB,EAAchc;UACtB;;QAEF,IAAIiZ,MAAerjB,MAAK+iB,EAAS3Y;gBACzBA;;QAER,IAAIA,MAAMpK,MAAK2hB;UACb;;UAEAvX,IAAIpK,MAAK0hB,EAAMtX;;;;AAIvB;EAEA,IAAC0Y,EAAUO,YAAEA,IAAarjB,KAAKqjB,cAAe;IAC5C,IAAIrjB,MAAKsgB;MACP,KAAK,IAAIlW,IAAIpK,MAAK2hB,GAAO,KAAQ;QAC/B,KAAK3hB,MAAKomB,EAAchc;UACtB;;QAEF,IAAIiZ,MAAerjB,MAAK+iB,EAAS3Y;gBACzBA;;QAER,IAAIA,MAAMpK,MAAK4hB;UACb;;UAEAxX,IAAIpK,MAAKW,EAAMyJ;;;;AAIvB;EAEA,EAAAgc,CAAc1D;IACZ,YACYlY,MAAVkY,KACA1iB,MAAKuhB,EAAQ0D,IAAIjlB,MAAKwhB,EAASkB,QAAiBA;AAEpD;EAMA,SAACrS;IACC,KAAK,IAAMjG,KAAKpK,MAAK6iB;MACnB,SACuBrY,MAArBxK,MAAKyhB,EAASrX,WACOI,MAArBxK,MAAKwhB,EAASpX,OACbpK,MAAKuiB,EAAmBviB,MAAKyhB,EAASrX;cAEjC,EAACpK,MAAKwhB,EAASpX,IAAIpK,MAAKyhB,EAASrX;;;AAG7C;EAQA,UAACic;IACC,KAAK,IAAMjc,KAAKpK,MAAK8iB;MACnB,SACuBtY,MAArBxK,MAAKyhB,EAASrX,WACOI,MAArBxK,MAAKwhB,EAASpX,OACbpK,MAAKuiB,EAAmBviB,MAAKyhB,EAASrX;cAEjC,EAACpK,MAAKwhB,EAASpX,IAAIpK,MAAKyhB,EAASrX;;;AAG7C;EAMA,MAACD;IACC,KAAK,IAAMC,KAAKpK,MAAK6iB,KAAY;MAC/B,IAAMJ,IAAIziB,MAAKwhB,EAASpX;MACxB,SACQI,MAANiY,MACCziB,MAAKuiB,EAAmBviB,MAAKyhB,EAASrX;cAEjCqY;;;AAGZ;EAQA,OAAC6D;IACC,KAAK,IAAMlc,KAAKpK,MAAK8iB,KAAa;MAChC,IAAML,IAAIziB,MAAKwhB,EAASpX;MACxB,SACQI,MAANiY,MACCziB,MAAKuiB,EAAmBviB,MAAKyhB,EAASrX;cAEjCqY;;;AAGZ;EAMA,QAAC8D;IACC,KAAK,IAAMnc,KAAKpK,MAAK6iB,KAAY;MAE/B,SACQrY,MAFExK,MAAKyhB,EAASrX,OAGrBpK,MAAKuiB,EAAmBviB,MAAKyhB,EAASrX;cAEjCpK,MAAKyhB,EAASrX;;;AAG1B;EAQA,SAACoc;IACC,KAAK,IAAMpc,KAAKpK,MAAK8iB,KAAa;MAEhC,SACQtY,MAFExK,MAAKyhB,EAASrX,OAGrBpK,MAAKuiB,EAAmBviB,MAAKyhB,EAASrX;cAEjCpK,MAAKyhB,EAASrX;;;AAG1B;EAMA,CAACqc,OAAOC;IACN,OAAO1mB,KAAKqQ;AACd;EAMA2C,IAAAA,CACEwL,GACAmI,IAA4C;IAE5C,KAAK,IAAMvc,KAAKpK,MAAK6iB,KAAY;MAC/B,IAAM2C,IAAIxlB,MAAKyhB,EAASrX;MACxB,IAAMjH,IAAQnD,MAAKuiB,EAAmBiD,KAClCA,EAAEoB,uBACFpB;MACJ,SAAchb,MAAVrH;QAAqB;;MACzB,IAAIqb,EAAGrb,GAAOnD,MAAKwhB,EAASpX,IAASpK;QACnC,OAAOA,KAAKilB,IAAIjlB,MAAKwhB,EAASpX,IAASuc;;;AAG7C;EAQAjL,OAAAA,CACE8C,GACAqI,IAAa7mB;IAEb,KAAK,IAAMoK,KAAKpK,MAAK6iB,KAAY;MAC/B,IAAM2C,IAAIxlB,MAAKyhB,EAASrX;MACxB,IAAMjH,IAAQnD,MAAKuiB,EAAmBiD,KAClCA,EAAEoB,uBACFpB;MACJ,SAAchb,MAAVrH;QAAqB;;MACzBqb,EAAG3T,KAAKgc,GAAO1jB,GAAOnD,MAAKwhB,EAASpX,IAASpK;;AAEjD;EAMA8mB,QAAAA,CACEtI,GACAqI,IAAa7mB;IAEb,KAAK,IAAMoK,KAAKpK,MAAK8iB,KAAa;MAChC,IAAM0C,IAAIxlB,MAAKyhB,EAASrX;MACxB,IAAMjH,IAAQnD,MAAKuiB,EAAmBiD,KAClCA,EAAEoB,uBACFpB;MACJ,SAAchb,MAAVrH;QAAqB;;MACzBqb,EAAG3T,KAAKgc,GAAO1jB,GAAOnD,MAAKwhB,EAASpX,IAASpK;;AAEjD;EAMA+mB,UAAAA;IACE,IAAIC,KAAU;IACd,KAAK,IAAM5c,KAAKpK,MAAK8iB,EAAU;MAAEO,aAAY;;MAC3C,IAAIrjB,MAAK+iB,EAAS3Y,IAAI;QACpBpK,KAAKykB,OAAOzkB,MAAKwhB,EAASpX;QAC1B4c,KAAU;;;IAGd,OAAOA;AACT;EAMAC,IAAAA;IACE,IAAMC,IAAgC;IACtC,KAAK,IAAM9c,KAAKpK,MAAK6iB,EAAS;MAAEQ,aAAY;QAAS;MACnD,IAAMgB,IAAMrkB,MAAKwhB,EAASpX;MAC1B,IAAMob,IAAIxlB,MAAKyhB,EAASrX;MACxB,IAAMjH,IAAuBnD,MAAKuiB,EAAmBiD,KACjDA,EAAEoB,uBACFpB;MACJ,SAAchb,MAAVrH,UAA+BqH,MAAR6Z;QAAmB;;MAC9C,IAAM8C,IAA2B;QAAEhkB;;MACnC,IAAInD,MAAKiiB,KAASjiB,MAAKgiB,GAAS;QAC9BmF,EAAMnE,MAAMhjB,MAAKiiB,EAAM7X;QAGvB,IAAMgd,IAAMtJ,EAAKE,QAAQhe,MAAKgiB,EAAQ5X;QACtC+c,EAAM3b,QAAQ1B,KAAKC,MAAMkU,KAAKD,QAAQoJ;;MAExC,IAAIpnB,MAAK+hB;QACPoF,EAAM7G,OAAOtgB,MAAK+hB,EAAO3X;;MAE3B8c,EAAIG,QAAQ,EAAChD,GAAK8C;;IAEpB,OAAOD;AACT;EAOAI,IAAAA,CAAKJ;IACHlnB,KAAKunB;IACL,KAAK,KAAOlD,GAAK8C,MAAUD,GAAK;MAC9B,IAAIC,EAAM3b,OAAO;QAOf,IAAM4b,IAAMnJ,KAAKD,QAAQmJ,EAAM3b;QAC/B2b,EAAM3b,QAAQsS,EAAKE,QAAQoJ;;MAE7BpnB,KAAKwnB,IAAInD,GAAK8C,EAAMhkB,OAAOgkB;;AAE/B;EAQAK,GAAAA,CACE/E,GACA+C,GACAiC,IAA4C,CAAA;IAE5C,SAAUjd,MAANgb,GAAiB;MACnBxlB,KAAKykB,OAAOhC;MACZ,OAAOziB;;IAET,KAAMgjB,KACJA,IAAMhjB,KAAKgjB,KAAGxX,OACdA,GAAK8X,gBACLA,IAAiBtjB,KAAKsjB,gBAAcG,iBACpCA,IAAkBzjB,KAAKyjB,iBAAeoB,QACtCA,KACE4C;IACJ,KAAIlE,aAAEA,IAAcvjB,KAAKujB,eAAgBkE;IAEzC,IAAMnH,IAAOtgB,MAAKulB,EAChB9C,GACA+C,GACAiC,EAAWnH,QAAQ,GACnBmD;IAIF,IAAIzjB,KAAKwjB,gBAAgBlD,IAAOtgB,KAAKwjB,cAAc;MACjD,IAAIqB,GAAQ;QACVA,EAAO2C,MAAM;QACb3C,EAAO6C,wBAAuB;;MAGhC1nB,KAAKykB,OAAOhC;MACZ,OAAOziB;;IAET,IAAI0iB,IAAuB,MAAf1iB,MAAKsgB,SAAc9V,IAAYxK,MAAKuhB,EAAQ0D,IAAIxC;IAC5D,SAAcjY,MAAVkY,GAAqB;MAEvBA,IACiB,MAAf1iB,MAAKsgB,IACDtgB,MAAK4hB,IACiB,MAAtB5hB,MAAK6hB,EAAMthB,SACXP,MAAK6hB,EAAMb,QACXhhB,MAAKsgB,MAAUtgB,MAAK+f,IACpB/f,MAAK0lB,GAAO,KACZ1lB,MAAKsgB;MAEXtgB,MAAKwhB,EAASkB,KAASD;MACvBziB,MAAKyhB,EAASiB,KAAS8C;MACvBxlB,MAAKuhB,EAAQiG,IAAI/E,GAAGC;MACpB1iB,MAAKW,EAAMX,MAAK4hB,KAASc;MACzB1iB,MAAK0hB,EAAMgB,KAAS1iB,MAAK4hB;MACzB5hB,MAAK4hB,IAAQc;MACb1iB,MAAKsgB;MACLtgB,MAAKylB,EAAa/C,GAAOpC,GAAMuE;MAC/B,IAAIA;QAAQA,EAAO2C,MAAM;;MACzBjE,KAAc;WACT;MAELvjB,MAAK4iB,EAAYF;MACjB,IAAMiF,IAAS3nB,MAAKyhB,EAASiB;MAC7B,IAAI8C,MAAMmC,GAAQ;QAChB,IAAI3nB,MAAKmiB,KAAmBniB,MAAKuiB,EAAmBoF,IAAS;UAC3DA,EAAOC,kBAAkBvI,MAAM,IAAIlS,MAAM;UACzC,KAAQyZ,sBAAsB9F,KAAM6G;UACpC,SAAUnd,MAANsW,MAAoBwC,GAAgB;YACtC,IAAItjB,MAAKkiB;cACPliB,MAAKmhB,IAAWL,GAAQ2B,GAAG;;YAE7B,IAAIziB,MAAKoiB;cACPpiB,MAAK8hB,GAAWrS,KAAK,EAACqR,GAAQ2B,GAAG;;;eAGhC,KAAKa,GAAgB;UAC1B,IAAItjB,MAAKkiB;YACPliB,MAAKmhB,IAAWwG,GAAalF,GAAG;;UAElC,IAAIziB,MAAKoiB;YACPpiB,MAAK8hB,GAAWrS,KAAK,EAACkY,GAAalF,GAAG;;;QAG1CziB,MAAKslB,EAAgB5C;QACrB1iB,MAAKylB,EAAa/C,GAAOpC,GAAMuE;QAC/B7kB,MAAKyhB,EAASiB,KAAS8C;QACvB,IAAIX,GAAQ;UACVA,EAAO2C,MAAM;UACb,IAAMK,IACJF,KAAU3nB,MAAKuiB,EAAmBoF,KAC9BA,EAAOf,uBACPe;UACN,SAAiBnd,MAAbqd;YAAwBhD,EAAOgD,WAAWA;;;AAEjD,aAAM,IAAIhD;QACTA,EAAO2C,MAAM;;;IAGjB,IAAY,MAARxE,MAAchjB,MAAKiiB;MACrBjiB,MAAKkkB;;IAEP,IAAIlkB,MAAKiiB,GAAO;MACd,KAAKsB;QACHvjB,MAAKukB,EAAY7B,GAAOM,GAAKxX;;MAE/B,IAAIqZ;QAAQ7kB,MAAK4kB,EAAWC,GAAQnC;;;IAEtC,KAAKY,KAAkBtjB,MAAKoiB,KAAoBpiB,MAAK8hB,GAAW;MAC9D,IAAMgG,IAAK9nB,MAAK8hB;MAChB,IAAIiG;MACJ,OAAQA,IAAOD,GAAIhM;QACjB9b,MAAKohB,OAAmB2G;;;IAG5B,OAAO/nB;AACT;EAMAghB,GAAAA;IACE;MACE,OAAOhhB,MAAKsgB,GAAO;QACjB,IAAM0H,IAAMhoB,MAAKyhB,EAASzhB,MAAK2hB;QAC/B3hB,MAAK0lB,GAAO;QACZ,IAAI1lB,MAAKuiB,EAAmByF;UAC1B,IAAIA,EAAIpB;YACN,OAAOoB,EAAIpB;;eAER,SAAYpc,MAARwd;UACT,OAAOA;;;MAGH;MACR,IAAIhoB,MAAKoiB,KAAoBpiB,MAAK8hB,GAAW;QAC3C,IAAMgG,IAAK9nB,MAAK8hB;QAChB,IAAIiG;QACJ,OAAQA,IAAOD,GAAIhM;UACjB9b,MAAKohB,OAAmB2G;;;;AAIhC;EAEA,EAAArC,CAAO7D;IACL,IAAMF,IAAO3hB,MAAK2hB;IAClB,IAAMc,IAAIziB,MAAKwhB,EAASG;IACxB,IAAM6D,IAAIxlB,MAAKyhB,EAASE;IACxB,IAAI3hB,MAAKmiB,KAAmBniB,MAAKuiB,EAAmBiD;MAClDA,EAAEoC,kBAAkBvI,MAAM,IAAIlS,MAAM;WAC/B,IAAInN,MAAKkiB,KAAeliB,MAAKoiB,GAAkB;MACpD,IAAIpiB,MAAKkiB;QACPliB,MAAKmhB,IAAWqE,GAAG/C,GAAG;;MAExB,IAAIziB,MAAKoiB;QACPpiB,MAAK8hB,GAAWrS,KAAK,EAAC+V,GAAG/C,GAAG;;;IAGhCziB,MAAKslB,EAAgB3D;IAErB,IAAIE,GAAM;MACR7hB,MAAKwhB,EAASG,UAAQnX;MACtBxK,MAAKyhB,EAASE,UAAQnX;MACtBxK,MAAK6hB,EAAMpS,KAAKkS;;IAElB,IAAmB,MAAf3hB,MAAKsgB,GAAa;MACpBtgB,MAAK2hB,IAAQ3hB,MAAK4hB,IAAQ;MAC1B5hB,MAAK6hB,EAAMthB,SAAS;;MAEpBP,MAAK2hB,IAAQ3hB,MAAKW,EAAMghB;;IAE1B3hB,MAAKuhB,EAAQkD,OAAOhC;IACpBziB,MAAKsgB;IACL,OAAOqB;AACT;EAUAnQ,GAAAA,CAAIiR,GAAMwF,IAA4C;IACpD,KAAM7E,gBAAEA,IAAiBpjB,KAAKojB,gBAAcyB,QAAEA,KAC5CoD;IACF,IAAMvF,IAAQ1iB,MAAKuhB,EAAQ0D,IAAIxC;IAC/B,SAAcjY,MAAVkY,GAAqB;MACvB,IAAM8C,IAAIxlB,MAAKyhB,EAASiB;MACxB,IACE1iB,MAAKuiB,EAAmBiD,WACGhb,MAA3Bgb,EAAEoB;QAEF,QAAO;;MAET,KAAK5mB,MAAK+iB,EAASL,IAAQ;QACzB,IAAIU;UACFpjB,MAAK2kB,EAAejC;;QAEtB,IAAImC,GAAQ;UACVA,EAAOrT,MAAM;UACbxR,MAAK4kB,EAAWC,GAAQnC;;QAE1B,QAAO;AACR,aAAM,IAAImC,GAAQ;QACjBA,EAAOrT,MAAM;QACbxR,MAAK4kB,EAAWC,GAAQnC;;AAE3B,WAAM,IAAImC;MACTA,EAAOrT,MAAM;;IAEf,QAAO;AACT;EASA/Q,IAAAA,CAAKgiB,GAAMyF,IAA8C;IACvD,KAAM7E,YAAEA,IAAarjB,KAAKqjB,cAAe6E;IACzC,IAAMxF,IAAQ1iB,MAAKuhB,EAAQ0D,IAAIxC;IAC/B,SACYjY,MAAVkY,MACCW,MAAerjB,MAAK+iB,EAASL,KAC9B;MACA,IAAM8C,IAAIxlB,MAAKyhB,EAASiB;MAExB,OAAO1iB,MAAKuiB,EAAmBiD,KAAKA,EAAEoB,uBAAuBpB;;AAEjE;EAEA,EAAAhD,CACEC,GACAC,GACA9Z,GACA+Z;IAEA,IAAM6C,SAAchb,MAAVkY,SAAsBlY,IAAYxK,MAAKyhB,EAASiB;IAC1D,IAAI1iB,MAAKuiB,EAAmBiD;MAC1B,OAAOA;;IAGT,IAAM2C,IAAK,IAAIzJ;IACf,KAAMU,QAAEA,KAAWxW;IAEnBwW,GAAQH,iBAAiB,UAAS,MAAMkJ,EAAG9I,MAAMD,EAAOE,UAAS;MAC/DF,QAAQ+I,EAAG/I;;IAGb,IAAMgJ,IAAY;MAChBhJ,QAAQ+I,EAAG/I;MACXxW;MACA+Z;;IAGF,IAAM0F,KAAKA,CACT7C,GACA8C,KAAc;MAEd,KAAMtJ,SAAEA,KAAYmJ,EAAG/I;MACvB,IAAMmJ,IAAc3f,EAAQkb,yBAA0BtZ,MAANgb;MAChD,IAAI5c,EAAQic;QACV,IAAI7F,MAAYsJ,GAAa;UAC3B1f,EAAQic,OAAO2D,gBAAe;UAC9B5f,EAAQic,OAAO4D,aAAaN,EAAG/I,OAAOE;UACtC,IAAIiJ;YAAa3f,EAAQic,OAAO6D,qBAAoB;;;UAEpD9f,EAAQic,OAAO8D,iBAAgB;;;MAGnC,IAAI3J,MAAYuJ,MAAgBD;QAC9B,OAAOM,UAAUT,EAAG/I,OAAOE;;MAI7B,IAAItf,MAAKyhB,EAASiB,OAAoBxf;QACpC,SAAUsH,MAANgb;UACF,IAHOtiB,EAGA0jB;YACL5mB,MAAKyhB,EAASiB,KAJTxf,EAI8B0jB;;YAEnC5mB,KAAKykB,OAAOhC;;eAET;UACL,IAAI7Z,EAAQic;YAAQjc,EAAQic,OAAOgE,gBAAe;;UAClD7oB,KAAKwnB,IAAI/E,GAAG+C,GAAG4C,EAAUxf;;;MAG7B,OAAO4c;AAAC;IAWV,IAAMoD,YAAaE;MACjB,KAAM9J,SAAEA,KAAYmJ,EAAG/I;MACvB,IAAM2J,IACJ/J,KAAWpW,EAAQib;MACrB,IAAMR,IACJ0F,KAAqBngB,EAAQgb;MAE/B,IAAMoF,IAAK9lB;MACX,IAAIlD,MAAKyhB,EAASiB,OAAoBxf,GAAG;QAIvC,MANemgB,KAAcza,EAAQ8a,kCAKgBlZ,MAA5Bwe,EAAGpC;UAE1B5mB,KAAKykB,OAAOhC;eACP,KAAKsG;UAKV/oB,MAAKyhB,EAASiB,KAAkBsG,EAAGpC;;;MAGvC,IAAIvD,GAAY;QACd,IAAIza,EAAQic,eAAsCra,MAA5Bwe,EAAGpC;UACvBhe,EAAQic,OAAOoE,iBAAgB;;QAEjC,OAAOD,EAAGpC;AACX,aAAM,IAAIoC,EAAGE,eAAeF;QAC3B,MAAMF;;;IA6BV,IAAIlgB,EAAQic;MAAQjc,EAAQic,OAAOsE,mBAAkB;;IACrD,IAAMjmB,IAAI,IAAIkmB,SA1BAC,CACZC,GACAC;MAEA,IAAMC,IAAMxpB,MAAKqhB,IAAeoB,GAAG+C,GAAG4C;MACtC,IAAIoB,KAAOA,aAAeJ;QACxBI,EAAIC,MAAKjE,KAAK8D,OAAU9e,MAANgb,SAAkBhb,IAAYgb,KAAI+D;;MAKtDpB,EAAG/I,OAAOH,iBAAiB,UAAS;QAClC,KACGrW,EAAQkb,oBACTlb,EAAQib,wBACR;UACAyF,OAAI9e;UAEJ,IAAI5B,EAAQib;YACVyF,IAAM9D,KAAK6C,GAAG7C,IAAG;;;;AAGrB,QAIyBiE,KAAKpB,KAlEtBS;MACV,IAAIlgB,EAAQic,QAAQ;QAClBjc,EAAQic,OAAO6E,iBAAgB;QAC/B9gB,EAAQic,OAAO4D,aAAaK;;MAE9B,OAAOF,UAAUE;AAAG;IA8DtB,IAAME,IAAyB9e,OAAOK,OAAOrH,GAAG;MAC9C0kB,mBAAmBO;MACnBvB,sBAAsBpB;MACtB0D,iBAAY1e;;IAGd,SAAcA,MAAVkY,GAAqB;MAEvB1iB,KAAKwnB,IAAI/E,GAAGuG,GAAI;WAAKZ,EAAUxf;QAASic,aAAQra;;MAChDkY,IAAQ1iB,MAAKuhB,EAAQ0D,IAAIxC;;MAEzBziB,MAAKyhB,EAASiB,KAASsG;;IAEzB,OAAOA;AACT;EAEA,EAAAzG,CAAmBrf;IACjB,KAAKlD,MAAKmiB;MAAiB,QAAO;;IAClC,IAAMwH,IAAIzmB;IACV,SACIymB,KACFA,aAAaP,WACbO,EAAEC,eAAe,2BACjBD,EAAE/B,6BAA6BlJ;AAEnC;EAwCA,WAAMmL,CACJpH,GACAqH,IAAgD;IAEhD,KAAMzG,YAEJA,IAAarjB,KAAKqjB,YAAUF,gBAC5BA,IAAiBnjB,KAAKmjB,gBAAcQ,oBACpCA,IAAqB3jB,KAAK2jB,oBAAkBX,KAE5CA,IAAMhjB,KAAKgjB,KAAGM,gBACdA,IAAiBtjB,KAAKsjB,gBAAchD,MACpCA,IAAO,GAACmD,iBACRA,IAAkBzjB,KAAKyjB,iBAAeF,aACtCA,IAAcvjB,KAAKujB,aAAWG,0BAE9BA,IAA2B1jB,KAAK0jB,0BAAwBE,4BACxDA,IAA6B5jB,KAAK4jB,4BAA0BE,kBAC5DA,IAAmB9jB,KAAK8jB,kBAAgBD,wBACxCA,IAAyB7jB,KAAK6jB,wBAAsBlB,SACpDA,GAAOoH,cACPA,KAAe,GAAKlF,QACpBA,GAAMzF,QACNA,KACE0K;IAEJ,KAAK9pB,MAAKmiB,GAAiB;MACzB,IAAI0C;QAAQA,EAAOgF,QAAQ;;MAC3B,OAAO7pB,KAAKilB,IAAIxC,GAAG;QACjBY;QACAF;QACAQ;QACAkB;;;IAIJ,IAAMjc,IAAU;MACdya;MACAF;MACAQ;MACAX;MACAM;MACAhD;MACAmD;MACAF;MACAG;MACAE;MACAC;MACAC;MACAe;MACAzF;;IAGF,IAAIsD,IAAQ1iB,MAAKuhB,EAAQ0D,IAAIxC;IAC7B,SAAcjY,MAAVkY,GAAqB;MACvB,IAAImC;QAAQA,EAAOgF,QAAQ;;MAC3B,IAAM3mB,IAAIlD,MAAKwiB,EAAiBC,GAAGC,GAAO9Z,GAAS+Z;MACnD,OAAQzf,EAAEgmB,aAAahmB;WAClB;MAEL,IAAMsiB,IAAIxlB,MAAKyhB,EAASiB;MACxB,IAAI1iB,MAAKuiB,EAAmBiD,IAAI;QAC9B,IAAMwE,IACJ3G,UAAyC7Y,MAA3Bgb,EAAEoB;QAClB,IAAI/B,GAAQ;UACVA,EAAOgF,QAAQ;UACf,IAAIG;YAAOnF,EAAOoE,iBAAgB;;;QAEpC,OAAOe,IAAQxE,EAAEoB,uBAAwBpB,EAAE0D,aAAa1D;;MAK1D,IAAMzC,IAAU/iB,MAAK+iB,EAASL;MAC9B,KAAKqH,MAAiBhH,GAAS;QAC7B,IAAI8B;UAAQA,EAAOgF,QAAQ;;QAC3B7pB,MAAK4iB,EAAYF;QACjB,IAAIS;UACFnjB,MAAK2kB,EAAejC;;QAEtB,IAAImC;UAAQ7kB,MAAK4kB,EAAWC,GAAQnC;;QACpC,OAAO8C;;MAKT,IAAMtiB,IAAIlD,MAAKwiB,EAAiBC,GAAGC,GAAO9Z,GAAS+Z;MAEnD,IAAMsH,SADsCzf,MAA3BtH,EAAE0jB,wBACUvD;MAC7B,IAAIwB,GAAQ;QACVA,EAAOgF,QAAQ9G,IAAU,UAAU;QACnC,IAAIkH,KAAYlH;UAAS8B,EAAOoE,iBAAgB;;;MAElD,OAAOgB,IAAW/mB,EAAE0jB,uBAAwB1jB,EAAEgmB,aAAahmB;;AAE/D;EAQA+hB,GAAAA,CAAIxC,GAAMkE,IAA4C;IACpD,KAAMtD,YACJA,IAAarjB,KAAKqjB,YAAUF,gBAC5BA,IAAiBnjB,KAAKmjB,gBAAcQ,oBACpCA,IAAqB3jB,KAAK2jB,oBAAkBkB,QAC5CA,KACE8B;IACJ,IAAMjE,IAAQ1iB,MAAKuhB,EAAQ0D,IAAIxC;IAC/B,SAAcjY,MAAVkY,GAAqB;MACvB,IAAMvf,IAAQnD,MAAKyhB,EAASiB;MAC5B,IAAMwH,IAAWlqB,MAAKuiB,EAAmBpf;MACzC,IAAI0hB;QAAQ7kB,MAAK4kB,EAAWC,GAAQnC;;MACpC,IAAI1iB,MAAK+iB,EAASL,IAAQ;QACxB,IAAImC;UAAQA,EAAOI,MAAM;;QAEzB,KAAKiF,GAAU;UACb,KAAKvG;YACH3jB,KAAKykB,OAAOhC;;UAEd,IAAIoC,KAAUxB;YAAYwB,EAAOoE,iBAAgB;;UACjD,OAAO5F,IAAalgB,SAAQqH;eACvB;UACL,IACEqa,KACAxB,UAC+B7Y,MAA/BrH,EAAMyjB;YAEN/B,EAAOoE,iBAAgB;;UAEzB,OAAO5F,IAAalgB,EAAMyjB,4BAAuBpc;;aAE9C;QACL,IAAIqa;UAAQA,EAAOI,MAAM;;QAMzB,IAAIiF;UACF,OAAO/mB,EAAMyjB;;QAEf5mB,MAAK4iB,EAAYF;QACjB,IAAIS;UACFnjB,MAAK2kB,EAAejC;;QAEtB,OAAOvf;;AAEV,WAAM,IAAI0hB;MACTA,EAAOI,MAAM;;AAEjB;EAEA,EAAAkF,CAASjnB,GAAU0c;IACjB5f,MAAK0hB,EAAM9B,KAAK1c;IAChBlD,MAAKW,EAAMuC,KAAK0c;AAClB;EAEA,EAAAgD,CAAYF;IASV,IAAIA,MAAU1iB,MAAK4hB,GAAO;MACxB,IAAIc,MAAU1iB,MAAK2hB;QACjB3hB,MAAK2hB,IAAQ3hB,MAAKW,EAAM+hB;;QAExB1iB,MAAKmqB,EACHnqB,MAAK0hB,EAAMgB,IACX1iB,MAAKW,EAAM+hB;;MAGf1iB,MAAKmqB,EAASnqB,MAAK4hB,GAAOc;MAC1B1iB,MAAK4hB,IAAQc;;AAEjB;EAMA+B,OAAOhC;IACL,IAAIuE,KAAU;IACd,IAAmB,MAAfhnB,MAAKsgB,GAAa;MACpB,IAAMoC,IAAQ1iB,MAAKuhB,EAAQ0D,IAAIxC;MAC/B,SAAcjY,MAAVkY,GAAqB;QACvBsE,KAAU;QACV,IAAmB,MAAfhnB,MAAKsgB;UACPtgB,KAAKunB;eACA;UACLvnB,MAAKslB,EAAgB5C;UACrB,IAAM8C,IAAIxlB,MAAKyhB,EAASiB;UACxB,IAAI1iB,MAAKuiB,EAAmBiD;YAC1BA,EAAEoC,kBAAkBvI,MAAM,IAAIlS,MAAM;iBAC/B,IAAInN,MAAKkiB,KAAeliB,MAAKoiB,GAAkB;YACpD,IAAIpiB,MAAKkiB;cACPliB,MAAKmhB,IAAWqE,GAAQ/C,GAAG;;YAE7B,IAAIziB,MAAKoiB;cACPpiB,MAAK8hB,GAAWrS,KAAK,EAAC+V,GAAQ/C,GAAG;;;UAGrCziB,MAAKuhB,EAAQkD,OAAOhC;UACpBziB,MAAKwhB,EAASkB,UAASlY;UACvBxK,MAAKyhB,EAASiB,UAASlY;UACvB,IAAIkY,MAAU1iB,MAAK4hB;YACjB5hB,MAAK4hB,IAAQ5hB,MAAK0hB,EAAMgB;iBACnB,IAAIA,MAAU1iB,MAAK2hB;YACxB3hB,MAAK2hB,IAAQ3hB,MAAKW,EAAM+hB;iBACnB;YACL1iB,MAAKW,EAAMX,MAAK0hB,EAAMgB,MAAU1iB,MAAKW,EAAM+hB;YAC3C1iB,MAAK0hB,EAAM1hB,MAAKW,EAAM+hB,MAAU1iB,MAAK0hB,EAAMgB;;UAE7C1iB,MAAKsgB;UACLtgB,MAAK6hB,EAAMpS,KAAKiT;;;;IAItB,IAAI1iB,MAAKoiB,KAAoBpiB,MAAK8hB,GAAWvhB,QAAQ;MACnD,IAAMunB,IAAK9nB,MAAK8hB;MAChB,IAAIiG;MACJ,OAAQA,IAAOD,GAAIhM;QACjB9b,MAAKohB,OAAmB2G;;;IAG5B,OAAOf;AACT;EAKAO,KAAAA;IACE,KAAK,IAAM7E,KAAS1iB,MAAK8iB,EAAU;MAAEO,aAAY;QAAS;MACxD,IAAMmC,IAAIxlB,MAAKyhB,EAASiB;MACxB,IAAI1iB,MAAKuiB,EAAmBiD;QAC1BA,EAAEoC,kBAAkBvI,MAAM,IAAIlS,MAAM;aAC/B;QACL,IAAMsV,IAAIziB,MAAKwhB,EAASkB;QACxB,IAAI1iB,MAAKkiB;UACPliB,MAAKmhB,IAAWqE,GAAQ/C,GAAQ;;QAElC,IAAIziB,MAAKoiB;UACPpiB,MAAK8hB,GAAWrS,KAAK,EAAC+V,GAAQ/C,GAAQ;;;;IAK5CziB,MAAKuhB,EAAQgG;IACbvnB,MAAKyhB,EAASjB,UAAKhW;IACnBxK,MAAKwhB,EAAShB,UAAKhW;IACnB,IAAIxK,MAAKiiB,KAASjiB,MAAKgiB,GAAS;MAC9BhiB,MAAKiiB,EAAMzB,KAAK;MAChBxgB,MAAKgiB,EAAQxB,KAAK;;IAEpB,IAAIxgB,MAAK+hB;MACP/hB,MAAK+hB,EAAOvB,KAAK;;IAEnBxgB,MAAK2hB,IAAQ;IACb3hB,MAAK4hB,IAAQ;IACb5hB,MAAK6hB,EAAMthB,SAAS;IACpBP,MAAKshB,IAAkB;IACvBthB,MAAKsgB,IAAQ;IACb,IAAItgB,MAAKoiB,KAAoBpiB,MAAK8hB,GAAW;MAC3C,IAAMgG,IAAK9nB,MAAK8hB;MAChB,IAAIiG;MACJ,OAAQA,IAAOD,GAAIhM;QACjB9b,MAAKohB,OAAmB2G;;;AAG9B;;;;;;;AC3wEF,IAAMqC,KAAa;EAClB,IAAI;EACJ,IAAI;EACJ,KAAK;EACL,KAAK;EACL,KAAK;EACL,MAAM;;;AAGP,IAAMC,KAAc;EACnB,IAAI;EACJ,IAAI;EACJ,KAAK;EACL,KAAK;EACL,KAAK;EACL,MAAM;;;AAsDPC,GAAcC,UAlDd,SAASC,MAAMC;EAEd,IAAIC,IAAOjnB,OAAO4mB,GAAY;EAC9B,IAAIM,KAAa;EAEjB,KAAK,IAAIvgB,IAAI,GAAGA,IAAIqgB,EAAOlqB,QAAQ6J,KAAK;IACvC,IAAIwgB,IAAgBH,EAAOloB,WAAW6H;IAGtC,IAAIwgB,IAAgB,QAASD,GAAY;MAExCC,KADAH,IAASI,SAASC,mBAAmBL,KACdloB,WAAW6H;MAClCugB,KAAa;AACb;IAEDD,KAAQE;IACRF,MAASA,KAAQ,MAAMA,KAAQ,MAAMA,KAAQ,MAAMA,KAAQ,MAAMA,KAAQ;AACzE;EAED,OAAOA,MAAS;AACjB;;AA+BAJ,GAAAC,QAAAQ,SA7BA,SAASA,OAAON,IAAQnK,MAACA,IAAO,MAAM;EACrC,KAAK8J,GAAW9J;IACf,MAAM,IAAInT,MAAM;;EAGjB,IAAIud,IAAOL,GAAY/J;EACvB,IAAM0K,IAAWZ,GAAW9J;EAG5B,IAAIqK,KAAa;EAEjB,KAAK,IAAIvgB,IAAI,GAAGA,IAAIqgB,EAAOlqB,QAAQ6J,KAAK;IACvC,IAAIwgB,IAAgBH,EAAOloB,WAAW6H;IAGtC,IAAIwgB,IAAgB,QAASD,GAAY;MAExCC,KADAH,IAASI,SAASC,mBAAmBL,KACdloB,WAAW6H;MAClCugB,KAAa;AACb;IAEDD,KAAQO,OAAOL;IACfF,IAAOO,OAAOC,QAAQ5K,GAAMoK,IAAOM;AACnC;EAED,OAAON;AACR;;;;AClEO,IAAMS,KAAoB;;AAEjC,IAAMC,qBAAsBtkB,KACnBA,EAAK+L,0BACR/L,EAAKiM,MAAMC,MAAKlM,KAAQA,EAAKqP,QAAQ1W,EAAG4rB,UAAUnhB,YAAWpD,IAC7DA;;AAGN,IAAMwkB,yBACJ9f;EAEA,IAAIsC,IAAgBtC;EACpB,IAAMwP,IAAO,IAAInK;EACjB,OAAO/C,EAAKuH,WAAW2F,EAAKxJ,IAAI1D,IAAO;IACrCkN,EAAKG,IAAIrN;IACT,IAAIrO,EAAG8rB,QAAQzd;MACb;WACK,IAAIrO,EAAG2Z,sBAAuBtL,IAAOA,EAAKuH;MAC/C,OAAOvH;;AAEX;AAAA;;AAGF,IAAM0d,6BAA6BA,CACjC1d,GACA2d,GACAC,GACA5c,GACAwJ,MAEOxK,EAAKwM,SAASvK,SAAQyK;EAC3B,IAAI/a,EAAGksB,oBAAoBnR;IAAU,OAAO;;EAE5C,IAAMyB,IAAM,KAAIwP;EAChB,OAAOhsB,EAAG8R,aAAaiJ,EAAQpW,QAC3BwnB,WAAWpR,EAAQpW,MAAM6X,GAAKyP,GAAW5c,GAAQwJ,IAAM,KACvD7Y,EAAGqX,uBAAuB0D,EAAQpW,QAClCynB,sBAAsBrR,EAAQpW,MAAM6X,GAAKyP,GAAW5c,GAAQwJ,KAC5DkT,2BAA2BhR,EAAQpW,MAAM6X,GAAKyP,GAAW5c,GAAQwJ;AAAK;;AAI9E,IAAMuT,wBAAwBA,CAC5B/d,GACA2d,GACAC,GACA5c,GACAwJ;EAEA,IAAMwT,IAAU;EAAG,IAAAC,QAAAA;IAEjB,IAAItsB,EAAGqX,uBAAuBkV,EAAQ5nB,OAAO;MAC3C,IAAM6X,IAAM,KAAIwP;MAChB,IACEO,EAAQjV,iBACP0U,EAAY7b,SAASoc,EAAQjV,aAAayB,YAC3C;QACA,IAAMyT,IAAS,KAAIhQ,GAAK+P,EAAQjV,aAAayB,YAAW0T,KAAK;QAC7D,IAAIR,EAAU1Y,MAAKC,KAAKA,EAAElR,WAAWkqB;UACnChQ,EAAIxM,KAAKuc,EAAQjV,aAAayB;;AAElC;MACA,IAAM2T,IAAiBN,sBACrBG,EAAQ5nB,MACR6X,GACAyP,GACA5c,GACAwJ;MAGFwT,EAAQrc,QAAQ0c;AACjB,WAAM,IAAI1sB,EAAG8R,aAAaya,EAAQ5nB,OAAO;MACxC,IAAM6X,IAAM,KAAIwP;MAChB,IACEO,EAAQjV,iBACP0U,EAAY7b,SAASoc,EAAQjV,aAAayB,YAC3C;QACA,IAAMyT,IAAS,KAAIhQ,GAAK+P,EAAQjV,aAAayB,YAAW0T,KAAK;QAC7D,IAAIR,EAAU1Y,MAAKC,KAAKA,EAAElR,WAAWkqB;UACnChQ,EAAIxM,KAAKuc,EAAQjV,aAAayB;;AAElC,aAAO;QACL,IAAMyT,IAAS,KAAIhQ,GAAK+P,EAAQ5nB,KAAKoU,YAAW0T,KAAK;QACrD,IAAIR,EAAU1Y,MAAKC,KAAKA,EAAElR,WAAWkqB;UACnChQ,EAAIxM,KAAKuc,EAAQ5nB,KAAKoU;;AAE1B;MAEA,IAAM4T,IAAcR,WAClBI,EAAQ5nB,MACR6X,GACAyP,GACA5c,GACAwJ,IACA;MAGFwT,EAAQrc,QAAQ2c;AAClB;;EAhDF,KAAK,IAAMJ,KAAWle,EAAKwM;IAAQyR;;EAmDnC,OAAOD;AAAO;;AAGhB,IAAMO,KAAe,IAAIxb,IAAI,EAC3B,OACA,UACA,WACA,UACA,SACA,QACA,QACA,WACA;;AAGF,IAAMyb,0BAA0BA,CAC9BC,GACAC,GACAd,GACA5c,GACAwJ;EAKA,IAFE7Y,EAAGyS,2BAA2Bqa,EAAIrb,eAClCmb,GAAa7a,IAAI+a,EAAIrb,WAAW9M,KAAKuR,OACxB;IAEb,IAAM8W,IAAkC,aADvBF,EAAIrb,WACK9M,KAAKuR;IAC/B,IAAI+W,IACFH,EAAIvb,UAAU;IAEhB,IAAMsY,IAAM;IACZ,IAAI7pB,EAAGsR,iBAAiBwb,EAAIlX,OAAOA,SAAS;MAC1C,IAAMsX,IAAeL,wBACnBC,EAAIlX,OAAOA,QACXmX,GACAd,GACA5c,GACAwJ;MAEF,IAAIqU,EAAapsB;QACf+oB,EAAI7Z,QAAQkd;;AAEhB;IAEA,IAAID,KAAQjtB,EAAG8R,aAAamb,IAAO;MAIjC,IAAMvpB,IAAQgV,qBAAqBuU,GAFnBpU,EAAKW,gBAAgBC,aAAcC;MAGnD,IACEhW,MACC1D,EAAGmtB,sBAAsBzpB,MACxB1D,EAAGwR,qBAAqB9N,MACxB1D,EAAG0R,gBAAgBhO;QAErBupB,IAAOvpB;;AAEX;IAEA,IACEupB,MACCjtB,EAAGmtB,sBAAsBF,MACxBjtB,EAAGwR,qBAAqByb,MACxBjtB,EAAG0R,gBAAgBub,KACrB;MACA,IAAMG,IAAQH,EAAKI,WAAWL,IAAW,IAAI;MAC7C,IAAII,GAAO;QACT,IAAME,IAAenB,WACnBiB,EAAMzoB,MACNooB,GACAd,GACA5c,GACAwJ,IACA;QAGF,IAAIyU,EAAaxsB;UACf+oB,EAAI7Z,QAAQsd;;AAEhB;AACF;IAEA,OAAOzD;AACT;EAEA,OAAO;AAAE;;AAGX,IAAMsC,aAAaA,CACjB9d,GACA2d,GACAC,GACA5c,GACAwJ,GACA0U;EAEA,IAAIvtB,EAAGqX,uBAAuBhJ;IAC5B,OAAO+d,sBAAsB/d,GAAM2d,GAAaC,GAAW5c,GAAQwJ;SAC9D,IAAI7Y,EAAGwtB,sBAAsBnf;IAClC,OAAO0d,2BACL1d,GACA2d,GACAC,GACA5c,GACAwJ;;EAIJ,IAAIwT,IAAoB;EAExB,IAAMoB,IAAa5U,EAAKW,gBAAgBkU,wBACtCre,EAAO4K,UACP5L,EAAKyL;EAGP,KAAK2T;IAAY,OAAOpB;;EAoIxB,OA/HAA,IAAUoB,EAAWnd,SAAQwc;IAE3B,IAAIA,EAAI7S,aAAa5K,EAAO4K;MAAU,OAAO;;IAG7C,IACE5L,EAAKyL,cAAcgT,EAAIxP,SAASvR,SAChCsC,EAAK+M,YAAY0R,EAAIxP,SAASvR,QAAQ+gB,EAAIxP,SAASxc;MAEnD,OAAO;;IAET,IAAI6sB,IAAWzS,SAAS7L,GAAQyd,EAAIxP,SAASvR;IAC7C,KAAK4hB;MAAU,OAAO;;IAEtB,IAAMZ,IAAY,KAAIf;IAOtB,IAqGC4B,GArGDC,SAAAA;MAUE,KACGN,MACAvtB,EAAG8tB,kBAAkBH,MAAa3tB,EAAG0R,gBAAgBic,KACtD;QAGA,IAAMnB,IAASO,EAAUN,KAAK;QACyC,OAAA;UAAA1G,GAAlDkG,EAAUtc,QAAO6D,KAAKA,EAAElR,WAAWkqB,IAAS;;AAElE,aAAM,IAAIxsB,EAAG2Z,sBAAsBgU;QAAW,OAAA;UAAA5H,GACtCoG,WACLwB,EAAShpB,MACTooB,GACAd,GACA5c,GACAwJ,IACA;;aAEG,IACL7Y,EAAG8R,aAAa6b,OACfZ,EAAU5c,SAASwd,EAASzX,OAC7B;QACA,IAAMsW,IAAS,KAAIO,GAAWY,EAASzX,OAAMuW,KAAK;QAClD,IAAIR,EAAU1Y,MAAKC,KAAKA,EAAElR,WAAWkqB,IAAS;UAC5CO,EAAU/c,KAAK2d,EAASzX;;AAE3B,aAAM,IACLlW,EAAGyS,2BAA2Bkb,MACP,SAAvBA,EAAShpB,KAAKuR,QACdlW,EAAGsR,iBAAiBqc,EAAS/X;QAE7B+X,IAAWA,EAAS/X;aACf,IACL5V,EAAGyS,2BAA2Bkb,MAC9Bf,GAAa7a,IAAI4b,EAAShpB,KAAKuR,SAC/BlW,EAAGsR,iBAAiBqc,EAAS/X,SAC7B;QACA,IAAMmY,IAAiBJ,EAAS/X;QAChC,IAAMiU,IAAM;QACZ,IAAMmE,IACmB,WAAvBL,EAAShpB,KAAKuR,QAA0C,YAAvByX,EAAShpB,KAAKuR;QACjD,IAAM+X,IAAiBpB,wBACrBkB,GACAhB,GACAd,GACA5c,GACAwJ;QAEF,IAAIoV,EAAentB;UACjB+oB,EAAI7Z,QAAQie;;QAGd,IAAIjuB,EAAG2Z,sBAAsBoU,EAAenY,YAAYoY,GAAe;UACrE,IAAME,IAAS/B,WACb4B,EAAenY,OAAOjR,MACtBooB,GACAd,GACA5c,GACAwJ,IACA;UAEFgR,EAAI7Z,QAAQke;AACd;QAAC,OAAA;UAAAnI,GAEM8D;;AACR,aAAM,IACL7pB,EAAGyS,2BAA2Bkb,OAC7BZ,EAAU5c,SAASwd,EAAShpB,KAAKuR,OAClC;QACA,IAAMsW,IAAS,KAAIO,GAAWY,EAAShpB,KAAKuR,OAAMuW,KAAK;QACvD,IAAIR,EAAU1Y,MAAKC,KAAKA,EAAElR,WAAWkqB;UACnCO,EAAU/c,KAAK2d,EAAShpB,KAAKuR;;AAEjC,aAAO,IACLlW,EAAG6V,0BAA0B8X,MAC7B3tB,EAAGyT,gBAAgBka,EAAS7X,wBAC3BiX,EAAU5c,SAASwd,EAAS7X,mBAAmBI,OAChD;QACA,IAAMsW,IAAS,KAAIO,GAAWY,EAAS7X,mBAAmBI,OAAMuW,KAC9D;QAEF,IAAIR,EAAU1Y,MAAKC,KAAKA,EAAElR,WAAWkqB;UACnCO,EAAU/c,KAAK2d,EAAS7X,mBAAmBI;;AAE/C;MAEA,IAAIlW,EAAGkd,oBAAoByQ,EAAS/X;QAClC+X,IAAWA,EAAS/X,OAAOA;;QAE3B+X,IAAWA,EAAS/X;;AAEvB;IApGD,OACE5V,EAAG8R,aAAa6b,MAChB3tB,EAAGyS,2BAA2Bkb,MAC9B3tB,EAAG6V,0BAA0B8X,MAC7B3tB,EAAG2Z,sBAAsBgU,MACzB3tB,EAAGwY,mBAAmBmV,MACtB3tB,EAAG8tB,kBAAkBH,MACrB3tB,EAAG0R,gBAAgBic;MAAS,IAAAC,IAAAC;QAAA,OAAAD,EAAA7H;;;IA+F9B,OAAOgH,EAAUN,KAAK;AAAI;AAGd;;AC1VT,IAAM0B,4BAA4BA,CACvC9e,GACAwJ;EAKA,IAAMuV,IJqSD,SAASC,eACdlT;IAEA,OAAOA,EAAWmT,WAAW3e,OAAO3P,EAAGuuB;AACzC,GIzSkBF,CAAehf;EAC/B,IAAM0D,IAAc8F,EAAKW,gBAAgBC,cAAcC;EAEvD,IAAM8U,IAGF,CAAA;EAEJ,KAAKzb;IAAa,OAAOyb;;EAEzB,IAAIJ,EAAQttB;IACVstB,EAAQnS,SAAQwS;MACd,KAAKA,EAAIC;QAAc;;MAEvB,IAAID,EAAIC,aAAa/pB,MAAM;QACzB,IAAMsT,IAAc3B,2BAClBmY,EAAIC,aAAa/pB,MACjBoO;QAEF,IAAIkF,GAAa;UACf,IAAMkD,IAAalD,EAAY+B;UAC/B,IAAImB,EAAWlB,SAAS9J,SAAS;YAAiB;;UAGlD,KADuBgL;YACF;;UAQrB,IAAMwT,IANqBC,qBAHJzT,GAKrBpI,GACA8F,GAG+BS,KAAIuV,KAAYA,EAASlqB,KAAKjB;UAC/D,IAAMkhB,IAAM6J,EAAIK,gBAAgB/V;UAChC,IAAIgW,IAAiBP,EAA2B5J;UAChD,IAAI+J,EAAM7tB,UAAUiuB;YAClBA,EAAe7gB,YAAY6gB,EAAe7gB,UAAUjD,OAAO0jB;iBACtD,IAAIA,EAAM7tB,WAAWiuB;YAC1BP,EAA2B5J,KAAOmK,IAAiB;cACjDhjB,OAAO0iB,EAAIK,gBAAgBhV;cAC3BhZ,QAAQ2tB,EAAIK,gBAAgB/V,UAAUjY;cACtCoN,WAAWygB;;;AAGjB;AACF;MAEA,IACEF,EAAIC,aAAaM,iBACjBhvB,EAAG6W,kBAAkB4X,EAAIC,aAAaM,gBACtC;QACA,IAAM/W,IAAc3B,2BAClBmY,EAAIC,aAAaM,cAAcrqB,MAC/BoO;QAEF,IAAIkF,GAAa;UACf,IAAMkD,IAAalD,EAAY+B;UAC/B,IAAImB,EAAWlB,SAAS9J,SAAS;YAAiB;;UAGlD,KADuBgL;YACF;;UAOrB,IAAMwT,IALqBC,qBAHJzT,GAKrBpI,GACA8F,GAE+BS,KAAIuV,KAAYA,EAASlqB,KAAKjB;UAC/D,IAAMkhB,IAAM6J,EAAIK,gBAAgB/V;UAChC,IAAIgW,IAAiBP,EAA2B5J;UAChD,IAAI+J,EAAM7tB,UAAUiuB;YAClBA,EAAe7gB,YAAY6gB,EAAe7gB,UAAUjD,OAAO0jB;iBACtD,IAAIA,EAAM7tB,WAAWiuB;YAC1BP,EAA2B5J,KAAOmK,IAAiB;cACjDhjB,OAAO0iB,EAAIK,gBAAgBhV;cAC3BhZ,QAAQ2tB,EAAIK,gBAAgB/V,UAAUjY;cACtCoN,WAAWygB;;;AAGjB;AACF,aAAO,IACLF,EAAIC,aAAaM,iBACjBhvB,EAAGivB,sBAAsBR,EAAIC,aAAaM;QAE1CP,EAAIC,aAAaM,cAAcnU,SAASoB,SAAQQ;UAC9C,IAAMrC,IAAaqC,EAAG9X,QAAQ8X,EAAGnF;UACjC,KAAK8C;YAAY;;UAEjB,IAAMnC,IAAc3B,2BAClB8D,GACArH;UAEF,IAAIkF,GAAa;YACf,IAAMkD,IAAalD,EAAY+B;YAC/B,IAAImB,EAAWlB,SAAS9J,SAAS;cAAiB;;YAGlD,KADuBgL;cACF;;YAOrB,IAAMwT,IALqBC,qBAHJzT,GAKrBpI,GACA8F,GAE+BS,KAC/BuV,KAAYA,EAASlqB,KAAKjB;YAE5B,IAAMkhB,IAAM6J,EAAIK,gBAAgB/V;YAChC,IAAIgW,IAAiBP,EAA2B5J;YAChD,IAAI+J,EAAM7tB,UAAUiuB;cAClBA,EAAe7gB,YAAY6gB,EAAe7gB,UAAUjD,OAAO0jB;mBACtD,IAAIA,EAAM7tB,WAAWiuB;cAC1BP,EAA2B5J,KAAOmK,IAAiB;gBACjDhjB,OAAO0iB,EAAIK,gBAAgBhV;gBAC3BhZ,QAAQ2tB,EAAIK,gBAAgB/V,UAAUjY;gBACtCoN,WAAWygB;;;AAGjB;AAAA;;AAEJ;;EAIJ,OAAOH;AAA0B;;AAGnC,SAASI,qBACPnR,GACA1K,GACA8F;EAEA,IAAI3K,IAA2C;EAC/C,IAAMghB,IAAkBxS,uBAAuBe,GAAK5E,IAAM;EAE1D,IAAMtC,IAASxD,EAAYyD,oBAAoBiH;EAC/C,KAAKlH;IAAQ,OAAO;;EAGpB,IAAM4Y,IADUpc,EAAYqc,mBAAmB7Y,GACjB+C,KAAI+V,KAAQA,EAAK1qB;EACjCuqB,EAAgB9e,MAAMT,QAAO6D;IACzC,IAAIoC,IAASpC,EAAEnF,KAAKuH;IACpB,OACEA,MACC5V,EAAGsvB,aAAa1Z,OAChB5V,EAAG2Z,sBAAsB/D;MAE1BA,IAASA,EAAOA;;IAGlB,IAAI5V,EAAG2Z,sBAAsB/D;MAC3B,OAAOuZ,EAAchf,SAASyF,EAAOjR,KAAKoU;;MAE1C,QAAO;;AACT,MAGIkD,SAAQ5N;IACZ,IAAM6H,IAAOyC,gBAAgBtK,EAAKA,MAAMoP,GAAc5E,GAAMC;IAC5D;MACE,IAAMyW,IAAS/gB,EAAM0H,GAAM;QAAE6F,aAAY;;MACzC,IAAIwT,EAAOvT,YAAY/V,OAAMuN,KAAKA,EAAEjQ,SAASe,EAAKC;QAChD2J,IAAYA,EAAUjD,OAAOskB,EAAOvT;;AAEvC,MAAC,OAAO9K;MACP;AACF;AAAA;EAGF,OAAOhD;AACT;;AC1JO,SAASshB,8BACd5W,GACA9W,GACA+W;EAEA,IAAMvH,IAAmBuH,EAAK4W,OAAOC,6BAA4B;EACjE,IAAM3c,IAAc8F,EAAKW,gBAAgBC,cAAcC;EACvD,KAAKpI;IAAkB;;EAEvB,IAAIjC,IAAS2L,UAAUnC,GAAMD;EAC7B,KAAKvJ;IAAQ;;EAEb,IAAMhB,IAAO6M,SAAS7L,GAAQvN;EAC9B,KAAKuM;IAAM;;EAEX,IAAI0f,IAA0B1f;EAO9B,IAAIrO,EAAG0d,oBAAoBqQ;IACzBA,IACEA,EAAepQ,gBAAgBlH,aAAalD,MAAK0E,KAE7CjY,EAAG2Z,sBAAsB1B,MACzBA,EAAY/C,eACZlV,EAAGsR,iBAAiB2G,EAAY/C,kBAE9B7G;SACH,IAAIrO,EAAG2vB,0BAA0B5B;IACtCA,IACEA,EAAetX,aAAalD,MAAK0E,KAE7BjY,EAAG2Z,sBAAsB1B,MACzBA,EAAY/C,eACZlV,EAAGsR,iBAAiB2G,EAAY/C,kBAE9B7G;SACH,IACLrO,EAAG2Z,sBAAsBoU,MACzBA,EAAe7Y,eACflV,EAAGsR,iBAAiByc,EAAe7Y;IAEnC6Y,IAAiBA,EAAe7Y;;IAEhC,OAAO6Y,MAAmB/tB,EAAGsR,iBAAiByc;MAC5CA,IAAiBA,EAAenY;;;EAQpC,KAAKiG,oBAA2BkS,GAAgBhb;IAC9C;;EAGF,IAAI6c,GACFC,IAAgBjX;EAClB,IAAImV,EAAe+B,eAAe;IAChC,KAAOC,KAAahC,EAAe+B;IACnC,KAAKC,MAAc/vB,EAAGgwB,gBAAgBD;MAAY;;IAClD,KAAQ1hB,MAAMgF,GAAOuF,UAAUqB,KAC7BgW,kCAAkCF,GAAWnX,GAAUC;IACzD+W,IAAYvc;IACZwc,IAAgB5V;AACjB,SAAM,IAAI8T,EAAexc,UAAU,IAAI;IACtC,KACGvR,EAAG8R,aAAaic,EAAexc,UAAU,QACzCvR,EAAGsR,iBAAiByc,EAAexc,UAAU;MAE9C;;IACF,KAAQlD,MAAMgF,GAAOuF,UAAUqB,KAC7BiW,qCACEnC,EAAexc,UAAU,IACzBqH,GACAC;IAEJ+W,IAAYvc;IACZwc,IAAgB5V;AAClB;EAEA,KAAK2V;IAAW;;EAEhB,IAAM1a,IAAc0a;EACpB,MACG1a,KACAlV,EAAGsR,iBAAiB4D,MACpBA,EAAY3D,UAAU,MACtBvR,EAAGuS,oBAAoB2C,EAAY3D,UAAU;IAE9C;;EAGF,IAAM0Z,IAAOkF,wBACXtX,GACA3D,EAAY3D,UAAU,IACtBse,GACA3a,EAAY3D,UAAU,MACpBvR,EAAG4a,yBAAyB1F,EAAY3D,UAAU,MAChD2D,EAAY3D,UAAU,UACtBxG;EAEN,IAAMqlB,IAAerC,EAAexc,UAAU;EAE9C,KAAK6e;IAEH,OAAO;MACL7W,MAAM;QACJxN,OAAOgiB,EAAexc,UAAU1O;QAChC/B,QAAQ;;MAEVuvB,aAAc,WAAUpF;;SAErB,IACLjrB,EAAGyT,gBAAgB2c,MACnBA,EAAarX,cAAe,WAAUkS;IAGtC,OAAO;MACL1R,MAAM;QACJxN,OAAOqkB,EAAatW;QACpBhZ,QAAQsvB,EAAapkB,MAAMokB,EAAatW;;MAE1CuW,aAAc,WAAUpF;;SAErB,IAAIjrB,EAAG8R,aAAase;IAGzB,OAAO;MACL7W,MAAM;QACJxN,OAAOqkB,EAAatW;QACpBhZ,QAAQsvB,EAAapkB,MAAMokB,EAAatW;;MAE1CuW,aAAc,WAAUpF;;;IAG1B;;AAEJ;;AAEO,IAAMkF,0BAA0BA,CACrCtX,GACAyX,GACAT,GACAU;EAEA,IAAIA,GAAqB;IACvB,IAAMriB,IAA2C;IACjDoO,oBAAoBiU,GAAqBriB,GAAW2K;IACpD,IAAI3C,IAAOyC,gBACT2X,GACAT,GACAhX,GACAC;IACF,IAAMyW,IAAS/gB,MAAM0H;IACrB,IAAMqF,IAAO,IAAInK;IACjB,KAAK,IAAM8K,KAAcqT,EAAOvT;MAC9B,IACEE,EAAW3Y,SAASe,MACnBiX,EAAKxJ,IAAImK;QAEVsU,oCAAoCtU;;;IAIVhO,EAC3BoL,KAAIuV;MACH2B,oCAAoC3B;MACpC,OAAOvgB,MAAMugB;AAAS,QAEvBlf,QAAO,CAACkf,GAAU5L,GAAOwN,MAAUA,EAAMC,QAAQ7B,OAAc5L,IAE5ChH,SAAQ0U;MAC5Bza,IAAQ,GAAEA,QAAWya;AAAoB;IAE3C,IAAMC,IAAWtiB,MAAME,MAAM0H;IAC7B,OAAO2a,EAAW,UAAUtqB,OAAOqqB,GAAUE,OAAO;AACtD,SAAO;IACL,IAAMC,IAAiB/V,UAAUnC,GAAMgX;IACvC,KAAQ3hB,WAAAA,KAAcwO,uBAAuBqU,GAAgBlY;IAE7D,IAAM3C,IAAOyC,gBACX2X,GACAT,GACAhX,GACAC;IAEF,IAAMyW,IAAS/gB,MAAM0H;IACrB,IAAMqF,IAAO,IAAInK;IACjB,KAAK,IAAM8K,KAAcqT,EAAOvT;MAC9B,IACEE,EAAW3Y,SAASe,MACnBiX,EAAKxJ,IAAImK;QAEVsU,oCAAoCtU;;;IAIxC,IAAM8U,IAAU,IAAI5f;IACpB6f,MAAM1B,GAAQ;MACZ5pB,oBAAoB0I;QAClBH,EAAU8B,KAAK3B;AAAK;MAEtB5I,gBAAgB4I;QACd2iB,EAAQtV,IAAIrN,EAAK1J,KAAKjB;AAAM;;IAIhC,IAAIwtB,IAAehb;IACnB,IAAMib,IAAU,IAAI/f;IACpB,IAAMggB,IAAmB,KAAIJ;IAE7B,IAAIK;IACJ,OAAQA,IAAaD,EAAiB/U,SAAU;MAC9C8U,EAAQzV,IAAI2V;MACZ,IAAMV,IAAqBziB,EAAUqF,MACnCC,KAAKA,EAAE7O,KAAKjB,UAAU2tB;MAExB,KAAKV,GAAoB;QACvB9X,EAAKyY,QAAQC,eAAeC,OAAO3Y,KAChC,kDAAiDwY;QAEpD;AACF;MAEAb,oCAAoCG;MAEpCM,MAAMN,GAAoB;QACxBlrB,gBAAgB4I;UACd,KAAK8iB,EAAQpf,IAAI1D,EAAK1J,KAAKjB;YACzB0tB,EAAiBphB,KAAK3B,EAAK1J,KAAKjB;;AAAM;;MAI5CwtB,IAAgB,GAAEA,QAAmB5iB,MAAMqiB;AAC7C;IAEA,OAAOE,EAAW,UACftqB,OAAO+H,MAAME,MAAM0iB,KACnBJ,OAAO;AACZ;AAAA;;AAGK,IAAMb,oCAAoCA,CAC/CF,GACAnX,GACAC;EAEA,IAAM9F,IAAc8F,EAAKW,gBAAgBC,cAAcC;EACvD,KAAK3G;IAAa,OAAO;MAAE1E,MAAM;MAAMuK;;;EAGvC,IAAIwB;EACJ,IAAIpa,EAAG8R,aAAaie,EAAU0B;IAC5BrX,IAAa2V,EAAU0B;SAClB,IAAIzxB,EAAG0xB,gBAAgB3B,EAAU0B;IAEtCrX,IAAa2V,EAAU0B,SAAShc;;EAGlC,KAAK2E;IAAY,OAAO;MAAE/L,MAAM;MAAMuK;;;EAEtC,IAAMlV,IAAQgV,qBAAqB0B,GAAYrH;EAC/C,KAAKrP,MAAUmY,cAAqBnY,GAAOqP;IACzC,OAAO;MAAE1E,MAAM;MAAMuK;;;EAGvB,OAAO;IACLvK,MAAM3K;IACNkV,UAAUlV,EAAMsW,gBAAgBC;;AACjC;;AAGI,IAAMiW,uCAAuCA,CAClDyB,GACA/Y,GACAC;EAEA,IAAI7Y,EAAG8R,aAAa6f,IAAuB;IACzC,IAAM5e,IAAc8F,EAAKW,gBAAgBC,cAAcC;IACvD,KAAK3G;MAAa,OAAO;QAAE1E,MAAM;QAAMuK;;;IAEvC,IAAMlV,IAAQgV,qBAAqBiZ,GAAsB5e;IACzD,KAAKrP,MAAUmY,cAAqBnY,GAAOqP;MACzC,OAAO;QAAE1E,MAAM;QAAMuK;;;IAGvB,OAAO;MACLvK,MAAM3K;MACNkV,UAAUlV,EAAMsW,gBAAgBC;;AAEpC;IACE,OAAO;MAAE5L,MAAMsjB;MAAsB/Y;;;AACvC;;AAKF,IAAM4X,sCACJtU;EAECA,EAAgD0V,aAC/C1V,EAAW0V,YAAYjiB,QACrBkiB,KAAsC,cAAzBA,EAAUltB,KAAKjB;AAC7B;;ACpTL,IAAMouB,KAAyB,IAAI1gB,IAAI,EACrC,YACA,UACA,UACA,WACA,aACA,oBACA,qBACA,aACA,YACA,YACA,aACA,uBACA,cACA,eACA,SACA,YACA;;AAKK,IAAM2gB,KAA6B;;AACnC,IAAMC,KAA6B;;AACnC,IAAMC,KAA6B;;AACnC,IAAMC,KAA6B;;IAC7BC,KAAkB,EANS,OACG,OF9CN,OEuDnCzG,IACAqG,IACAC,IACAC,IACAC;;AAGF,IAAME,KAAQ,IAAI5Q,SAAkC;EAElD+B,KAAK;EACLjD,KAAK;;;AAGA,SAAS+R,sBACdzZ,GACA/K,GACAgL;EAEA,IAAMvH,IAAmBuH,EAAK4W,OAAOC,6BAA4B;EAEjE,IAAIrgB,IAAS2L,UAAUnC,GAAMD;EAC7B,KAAKvJ;IAAQ;;EAEb,IACEe,GADElC,IAA2C;EAK/C,IAAIoD,GAAkB;IACpB,IAAMsL,IAASF,uBAAuBrN,GAAQwJ;IAC9C3K,IAAY0O,EAAO1O;IACnBkC,IAAQwM,EAAOxM;AACjB;IACEA,IN7DG,SAASkiB,2BACdnX;MAEA,IAAMyB,IAEF;OACJ,SAASrJ,KAAKlF;QACZ,IACEwN,aAAoBxN,MACnBrO,EAAGmZ,gCAAgC9K,MAClCwN,aAAoBxN,EAAKuH,SAC3B;UACAgH,EAAO5M,KAAK3B;UACZ;AACF;UACErO,EAAGqb,aAAahN,GAAMkF;;AAE1B,OACAA,CAAK4H;MACL,OAAOyB;AACT,KMyCY0V,CAA2BjjB,GAAQiK,KAAI9F,MAAM;MACnDnF,MAAMmF;MACN3F,QAAQ;;;EAIZ,IAAM0kB,IAAQniB,EAAMkJ,KAAI,EAAGjL;IACzB,KACGrO,EAAGmZ,gCAAgC9K,MAClCrO,EAAGke,qBAAqB7P,QACzBiD;MAED,IAAItR,EAAG4S,2BAA2BvE,EAAKuH;QACrCvH,IAAOA,EAAKuH;;QAEZ;;;IAIJ,OAAO+C,gBAAgBtK,GAAMuK,GAAUC,GAAMC;AAAY;EAG3D,IAAM0Z,IAAWzH,GACfzZ,IACIjC,EAAO0J,YACL7K,EAAUoL,KAAI9F,KAAKlF,MAAMkF,KAAIiZ,KAAK,OAClC5e,EAAO4kB,UACTF,EAAM9F,KAAK,OAAO5e,EAAO4kB;EAG/B,IAAIC;EACJ,IAAIN,GAAMrgB,IAAIygB;IACZE,IAAgBN,GAAM5M,IAAIgN;SACrB;IACLE,IAAgBC,eAAetjB,GAAQ;MAAEe;MAAOlC;OAAaL,GAAQgL;IACrEuZ,GAAMrK,IAAIyK,GAAUE;AACtB;EAEA,IAAME,IACJ/Z,EAAK4W,OAAOmD,qCAAoC;EAClD,IAAIC,IAAuC;EAE3C,IAAIvhB,GAAkB;IAKpB,IAAMwhB,IAJiB/V,gCAAgC1N,GAAQwJ,GAK5DS,KAA0BjG;MACzB,KAAQhF,MAAM0f,KAAmB1a;MACjC,KAAK0a,EAAe+B,kBAAkB/B,EAAexc,UAAU;QAC7D,OAAO;UACLwhB,UAAU/yB,EAAGgzB,mBAAmBtiB;UAChCoO,MAAMiT;UACNkB,MAAM5jB;UACN6jB,aAAa;UACbnnB,OAAOgiB,EAAejU;UACtBhZ,QAAQitB,EAAe3S,WAAW2S,EAAejU;;;MAIrD,IAAI8V,GAEF9C,GACA/gB,GACAjL,GAHA+uB,IAAgBjX;MAIlB,IAAMmX,IACJhC,EAAe+B,iBAAiB/B,EAAe+B,cAAc;MAC/D,IAAIC,GAAW;QACbhkB,IAAQgkB,EAAUjW;QAClBhZ,IAASivB,EAAU3U,WAAW2U,EAAUjW;QAExC,KAAK9Z,EAAGgwB,gBAAgBD;UACtB,OAAO;YACLgD,UAAU/yB,EAAGgzB,mBAAmBtiB;YAChCoO,MAAMiT;YACNkB,MAAM5jB;YACN6jB,aACE;YACFnnB;YACAjL;;;QAGJ,KAAQuN,MAAMgF,GAAOuF,UAAUqB,KAC7BgW,kCAAkCF,GAAWnX,GAAUC;QACzD+W,IAAYvc;QACZwc,IAAgB5V;QAChB6S,IAAMiD,EAAUhX;AACjB,aAAM,IAAIgV,EAAexc,UAAU,IAAI;QACtCxF,IAAQgiB,EAAexc,UAAU,GAAGuI;QACpChZ,IACEitB,EAAexc,UAAU,GAAG6J,WAC5B2S,EAAexc,UAAU,GAAGuI;QAC9B,KACG9Z,EAAG8R,aAAaic,EAAexc,UAAU,QACzCvR,EAAGsR,iBAAiByc,EAAexc,UAAU;UAE9C,OAAO;YACLwhB,UAAU/yB,EAAGgzB,mBAAmBtiB;YAChCoO,MAAMiT;YACNkB,MAAM5jB;YACN6jB,aACE;YACFnnB;YACAjL;;;QAIJ,KAAQuN,MAAMgF,GAAOuF,UAAUqB,KAC7BiW,qCACEnC,EAAexc,UAAU,IACzBqH,GACAC;QAEJ+W,IAAYvc;QACZwc,IAAgB5V;QAChB6S,IAAMiB,EAAexc,UAAU,GAAGwH;AACpC;MAEA,KAAK6W;QACH,OAAO;UACLmD,UAAU/yB,EAAGgzB,mBAAmBtiB;UAChCoO,MAAMmT;UACNgB,MAAM5jB;UACN6jB,aAAc,4BAA2BpG;UACzC/gB;UACAjL;;;MAIJ,IAAMoU,IAAc0a;MACpB,MACG1a,KACAlV,EAAGsR,iBAAiB4D,MACpBA,EAAY3D,UAAU,MACtBvR,EAAGuS,oBAAoB2C,EAAY3D,UAAU;QAI9C,OAAO;UACLwhB,UAAU/yB,EAAGgzB,mBAAmBtiB;UAChCoO,MAAMmT;UACNgB,MAAM5jB;UACN6jB,aAAc,oBAAmBpG;UACjC/gB;UACAjL;;;MAIJ,KAAKitB,EAAexc,UAAU;QAG5B,OAAO;UACLwhB,UAAU/yB,EAAGgzB,mBAAmBtiB;UAChCoO,MAAMkT;UACNiB,MAAM5jB;UACN6jB,aAAc;UACdnnB,OAAOgiB,EAAexc,UAAU1O;UAChC/B,QAAQitB,EAAexc,UAAUvF,MAAM+hB,EAAexc,UAAU1O;;;MAIpE,IAAMooB,IAAO8C,EAAexc,UAAU,GAAGwH,UAAU3W,MAAM,IAAI;MAC7D,IAAI6oB,EAAK3oB,WAAW,YAAY;QAC9B,IAAM6wB,IAAgBhD,wBACpBtX,GACA3D,EAAY3D,UAAU,IACtBse,GACA3a,EAAY3D,UAAU,MACpBvR,EAAG4a,yBAAyB1F,EAAY3D,UAAU,MAChD2D,EAAY3D,UAAU,UACtBxG;QAEN,KAAKooB;UAAe,OAAO;;QAG3B,IADsB,UAASA,QACVlI;UACnB,OAAO;YACL8H,UAAU/yB,EAAGgzB,mBAAmBtiB;YAChCoO,MAAMoT;YACNe,MAAM5jB;YACN6jB,aAAc;YACdnnB,OAAOgiB,EAAexc,UAAU1O;YAChC/B,QACEitB,EAAexc,UAAUvF,MAAM+hB,EAAexc,UAAU1O;;;AAGhE;MAEA,OAAO;AAAI,QAEZ8M,OAAO+K;IAEVgY,EAAc1iB,QAAS8iB;AACzB;EAEA,IAAIxhB,KAAoBshB,GAAkC;IACxD,IAAMQ,IAA6BjF,0BAA0B9e,GAAQwJ;IAErE,IAAMwa,IAAgB,IAAIjiB;IAC1BhB,EAAM6L,SAAQ,EAAG5N;MACf;QACE,IAAMkhB,IAAS/gB,EAAMH,EAAK0K,UAAU3W,MAAM,IAAI,IAAI;UAChD2Z,aAAY;;QAEdkV,EAAM1B,GAAQ;UACZ9pB,gBAAgB4I;YACdglB,EAAc3X,IAAIrN,EAAK1J,KAAKjB;AAAM;;AAGxC,QAAE,OAAOwN,IAAI;AAAA;IAGfzG,OAAOC,KAAK0oB,GAA4BnX,SAAQ6S;MAC9C,KACE5gB,WAAWolB,GAAavnB,OACxBA,GAAKjL,QACLA,KACEsyB,EAA2BtE;MAC/B,IAAMyE,IAAmBlxB,MAAMkJ,KAC7B,IAAI6F,IAAIkiB,EAAc3jB,QAAO6D,MAAM6f,EAActhB,IAAIyB;MAEvD,IAAI+f,EAAiBzyB;QACnB+xB,EAAoB7iB,KAAK;UACvBijB,MAAM5jB;UACNvO;UACAiL;UACAgnB,UAAU/yB,EAAGgzB,mBAAmBtiB;UAChCoO,MF5T2B;UE6T3BoU,aAAc,6CAA4CK,EAAiB9G,KACzE,aACOqC;;;AAEb;IAGF,OAAO,KAAI4D,MAAkBG;AAC/B;IACE,OAAOH;;AAEX;;AAEA,IAAMC,iBAAiBA,CACrBtjB,IAEEe,UACAlC,eAQFL,GACAgL;EAGA,IAAMvH,IAAmBuH,EAAK4W,OAAOC,6BAA4B;EAEjE,IAAM8D,IAAcpjB,EACjBkJ,KAAIma;IACH,IAAIplB,IAAOolB,EAAaplB;IACxB,KACGiD,MACAtR,EAAGmZ,gCAAgC9K,MAClCrO,EAAGke,qBAAqB7P;MAE1B,IAAIrO,EAAG4S,2BAA2BvE,EAAKuH;QACrCvH,IAAOA,EAAKuH;;QAEZ;;;IAIJ,KAAQkD,cAAc5C,GAAI8C,eAAEA,KAAkBL,gBAC5CtK,GACAuK,GACAC;IAEF,IAAM7J,IAAQkH,EAAKjH,MAAM;IAEzB,IAAIykB,KAAe;IACnB,IAAI1zB,EAAGua,eAAelM,EAAKuH;MACzB,IAAI5V,EAAG2zB,sBAAsBtlB,EAAKuH,OAAOA;QACvC8d,KAAe;;WAEZ,IAAI1zB,EAAG2zB,sBAAsBtlB,EAAKuH;MACvC8d,KAAe;;IAKjB,IAAIE,IACFvlB,EAAKyL,cACJxI,IACG,IACCjD,EAAqCwE,IAAIkG,UAAUjY,UACnD4yB,IAAe,IAAI;IAC1B,IAAMG,IAAcD,IAAmBvlB,EAAK0K,UAAUjY;IACtD,IAAIgzB,IAAe,KAAI5lB;IACvB,IAAIoD;MACF;QACE,IAAMyiB,IAAoBvlB,EAAM0H,GAAM;UACpC6F,aAAY;WACXC,YAAYrM,QAAO6D,KAAKA,EAAEjQ,SAASe,EAAKC;QAC3CuvB,IAAeA,EAAankB,QAC1B6D,MACGugB,EAAkBC,MACjBC,KACEA,EAAE1wB,SAASe,EAAKC,uBAChB0vB,EAAEtvB,KAAKjB,UAAU8P,EAAE7O,KAAKjB;AAGlC,QAAE,OAAOwN,IAAI;;IAGf,IAAMgjB,IACJT,EAAa5lB,UAAUA,EAAOsmB,MAAMV,EAAa5lB,UAC7CA,EAAOsmB,MAAMV,EAAa5lB,SAASA,SACnCA,EAAO9K,SAAS8K;IAEtB,KAAKqmB;MACH;;IAGF,IAAME,IAAmB,IAAIhjB,IAAI,KAC5B0gB,OACCjZ,EAAK4W,OAAO2E,oBAAoB;IAGtC,IAAMC,IAAqB1mB,eACzBuI,GACAge,QACAnpB,QACAA,GACA+oB,GAECnkB,QAAO2kB;MACN,KAAKA,EAAK7mB,QAAQ0C,SAAS;QAAsB,QAAO;;MAExD,KAAO1C,KAAW6mB,EAAK7mB,QAAQwB,MAAM;MACrC,IAAMslB,IACJ9mB,KAAW,gCAAgC+mB,KAAK/mB;MAClD,KAAK8mB;QAAS,QAAO;;MACrB,IAAME,IAAgBF,EAAQ;MAC9B,OAAOE,MAAkBL,EAAiBriB,IAAI0iB;AAAc,QAE7Dnb,KAAI9F;MACH,KAAMzH,OAAEA,GAAKC,KAAEA,KAAQwH,EAAE7E;MAIzB,IAAI+lB,IAAYd,IAAmB7nB,EAAMG;MACzC,KAAK,IAAIvB,IAAI,GAAGA,KAAKoB,EAAMG,QAAQvB,IAAIqE,EAAMlO,QAAQ6J;QACnD,IAAIA,MAAMoB,EAAMG;UAAMwoB,KAAa3oB,EAAM/I;eACpC,IAAIgM,EAAMrE;UAAI+pB,KAAa1lB,EAAMrE,GAAI7J;;;MAG5C,IAAI6zB,IAAUf,IAAmB5nB,EAAIE;MACrC,KAAK,IAAIvB,IAAI,GAAGA,KAAKqB,EAAIE,QAAQvB,IAAIqE,EAAMlO,QAAQ6J;QACjD,IAAIA,MAAMqB,EAAIE;UAAMyoB,KAAW3oB,EAAIhJ;eAC9B,IAAIgM,EAAMrE;UAAIgqB,KAAW3lB,EAAMrE,GAAI7J;;;MAG1C,IAAM8zB,IAAoB5b,EAAczF,MAAKC,KAEpCkhB,KAAalhB,EAAE8G,IAAIvO,SAAS4oB,KADpBnhB,EAAE8G,IAAIvO,QAAQyH,EAAE8G,IAAIxZ;MAIrC,IAAM8zB;QACJ,OAAO;aACFphB;UACHzH,OAAO6oB,EAAkBva,SAAStO;UAClCjL,QAAQ8zB,EAAkBva,SAASvZ;;aAGrC,IAAI4zB,IAAYb,GAAa;QAE3B,IAAMxa,IAAkBL,EACrBrJ,QAAO6D,KAAKA,EAAE8G,IAAIvO,QAAQyH,EAAE8G,IAAIxZ,SAAS4zB,IACzCvmB,QACC,CAACC,GAAKmL,MAASnL,KAAOmL,EAAKe,IAAIxZ,SAASyY,EAAKc,SAASvZ,UACtD;QAEJ4zB,KAAwBrb;QACxBsb,KAAoBtb;QACpB,OAAO;aACF7F;UACHzH,OAAO2oB,IAAY;UACnB5zB,QAAQ6zB,IAAUD;;AAEtB;QACE,OAAO;aACFlhB;UACHzH,OAAO2oB,IAAY;UACnB5zB,QAAQ6zB,IAAUD;;;AAGxB,QAED/kB,QAAO6D,KAAKA,EAAEzH,QAAQyH,EAAE1S,UAAU+yB;IAErC,OAAOQ;AAAkB,MAE1BQ,OACAllB,OAAO+K;EAEV,IAAMgY,IAAgBc,EAAYla,KAChCgb,MACG;IACCrB,MAAM5jB;IACNvO,QAAQwzB,EAAKxzB;IACbiL,OAAOuoB,EAAKvoB;IACZgnB,UACoB,MAAlBuB,EAAKllB,WACDpP,EAAGgzB,mBAAmBtiB,UACtB1Q,EAAGgzB,mBAAmBtlB;IAC5BoR,MACuB,mBAAdwV,EAAKxV,OACRwV,EAAKxV,OACa,MAAlBwV,EAAKllB,WA/cwB,QADH;IAmdhC8jB,aAAaoB,EAAK7mB,QAAQwB,MAAM,MAAM;;EAI5C,IAAIqC,GAAkB;IACpB,IAAMwjB,IHtK2BC,EACnC1lB,GACAe,GACAyI;MAEA,IAAM2a,IAA+B;MAErC,MAD8B3a,EAAK4W,OAAOuF,mBAAmB;QACjC,OAAOxB;;MAInC,IAAMyB,IAAe,IAAI7jB,IAAI,EAFA,MAAM,OAAO,iBACnByH,EAAK4W,OAAOwF,gBAAgB;MAEnD,IAAM/iB,IAAU2G,EAAKW,gBAAgBC,cAAcC;MACnD,KAAKxH;QAAS;;MAEd;QACE9B,EAAM6L,SAAQ5N;UACZ,IAAM6mB,IAAW7mB,EAAK0K;UAGtB,IAAImc,EAAS/kB,SAAS,eAAe+kB,EAAS/kB,SAAS;YACrD;;UAEF,IAAMglB,IAAsBtJ,uBAAuBxd;UACnD,KAAK8mB;YAAqB;;UAE1B,IAAIC;UAEJ,IAAM/tB,IAAO6K,EAAQE,kBAAkB/D,EAAKuH;UAI5C,IAAI,YAAYvO,GAAM;YACpB,IAAMyoB,IAAiBzoB,EACpBguB;YACHD,IACEtF,KAAiBA,EAAchvB,SAAS,IACpCgvB,EAAc,UACd/kB;AACR;UAEA,KAAKqqB,GAAU;YACb,IAAME,IAAgBjuB,EAAKgL,YAAY;YACvC,IAAIijB,GAAe;cACjB,IAAIC,IAAUrjB,EAAQiB,gBAAgBmiB;cACtC,IAAIE,IACFnuB,EAAKouB,oBAAoB;cAC3B,IAAIF,EAAQniB;gBACV,KAAK,IAAM/L,KAAQkuB,EAAQjiB;kBAEzB,IADAkiB,IAAgBnuB,EAAKouB,oBAAoB,IACtB;oBACjBL,IAAWI,EAAcE;oBACzB;AACF;;;cAGJN,IAAWI,KAAiBA,EAAcE;AAC5C;AACF;UAEA,IAAMjI,IAAa5U,EAAKW,gBAAgBkU,wBACtCre,EAAO4K,UACPkb,EAAoBxwB,KAAKmV;UAG3B,KAAK2T;YAAY;;UAEjB,IAAMkI,IAAsB;UAC5B,IAAMC,IAAuB;UAC7B,IAAMC,IAAqB;UAC3B,IAAMC,IAAa,IAAIvR;UAMvB0M,EAAMziB,EAAMH,EAAK0K,UAAU3W,MAAM,IAAI,KAAK;YACxCkD,OAAO;cACLywB,KAAAA,CAAM1nB;gBACJ,IAAM2nB,IAAQ3nB,EAAK2nB,QAAQ3nB,EAAK2nB,MAAMtyB,QAAQ2K,EAAK1J,KAAKjB;gBACxD,IAAMuyB,IAAOL,EAAW90B,SACnB,GAAE80B,EAAWnJ,KAAK,QAAQuJ,MAC3BA;gBAEJ,KAAK3nB,EAAK6nB,iBAAiBjB,EAAaljB,IAAI1D,EAAK1J,KAAKjB,QAAQ;kBAC5DmyB,EAAS7lB,KAAKimB;kBACdH,EAAW/N,IAAIkO,GAAM;oBACnBlqB,OAAOsC,EAAK1J,KAAKoM,IAAKhF;oBACtBjL,QAAQuN,EAAK1J,KAAKoM,IAAK/E,MAAMqC,EAAK1J,KAAKoM,IAAKhF;;AAEhD,uBAAO,IAAIsC,EAAK6nB,cAAc;kBAC5BN,EAAW5lB,KAAKgmB;kBAChBF,EAAW/N,IAAIkO,GAAM;oBACnBlqB,OAAOsC,EAAK1J,KAAKoM,IAAKhF;oBACtBjL,QAAQuN,EAAK1J,KAAKoM,IAAK/E,MAAMqC,EAAK1J,KAAKoM,IAAKhF;;AAEhD;AACD;cACDoqB,KAAAA,CAAM9nB;gBACJ,IAAIA,EAAK6nB;kBACPN,EAAWrU;;AAEf;;;UAIJkM,EAAWxR,SAAQ6Q;YACjB,IAAIA,EAAI7S,aAAa5K,EAAO4K;cAAU;;YAEtC,IAAMmc,IAAalb,SAAS7L,GAAQyd,EAAIxP,SAASvR;YACjD,KAAKqqB;cAAY;;YAEjB,IAAIA,EAAWxgB,WAAWuf;cAAqB;;YAE/C,IAAMkB,IAAenkB,EAAQokB,kBAC3BF,GACAp2B,EAAG2W,YAAY4f;YAGjB,IAAIC;YACJ,KAAK,IAAIC,KAAeJ,GAAc;cACpC,KAAKI,EAAYtf;gBAAkB;;cACnC,IAAIuf,IAAoB/K,mBACtBzZ,EAAQiB,gBAAgBsjB;cAE1B,IAAIrB,MAAasB,GAAmB;gBAClCF,IAAkBC;gBAClB;AACF;cAKA,IAAIC,EAAkBhgB,QAAQ1W,EAAG4rB,UAAUnhB,QAAQ;gBACjD,IAAMksB,IAAsBD,EAAkBrkB,YAAY;gBAC1D,IAAIskB,GAAqB;kBACvBD,IAAoBxkB,EAAQiB,gBAAgBwjB;kBAC5C,IAAIvB,MAAasB,GAAmB;oBAClCF,IAAkBC;oBAClB;AACF;AACF;gBAEA,IAAMG,IAAqBF,EAAkBrkB,YAAY;gBACzD,IAAIukB,GAAoB;kBACtBF,IAAoB/K,mBAClBzZ,EAAQiB,gBAAgByjB;kBAE1B,IAAIxB,MAAasB,GAAmB;oBAClCF,IAAkBC;oBAClB;AACF;AACF;AACF;AACF;YAEA,IAAMtf,IAAmBqf,GAAiBrf;YAC1C,IAAIxS;YACJ,IACEwS,KACA,UAAUA,KACRA,EAAiBxS,SAClB3E,EAAG8R,aAAaqF,EAAiBxS,SAChC3E,EAAG62B,cAAc1f,EAAiBxS;cAEpCA,IAAOwS,EAAiBxS;mBACnB;cAGL,IAAMwwB,IAAsBtJ,uBAAuBuK;cACnD,IAAIjB;gBAAqBxwB,IAAOwwB,EAAoBxwB;;AACtD;YAEA,IAAIA,GAAM;cACR,IAAMiY,IAASuP,WAAWxnB,GAAM,IAAIkxB,GAAUxmB,GAAQwJ,IAAM;cAC5D8c,EAAU3lB,QAAQ4M;AACpB;AAAA;UAGF,KAAK+Y,EAAU70B;YACb;;UAGF,IAAMg2B,IAASjB,EAASlmB,QAAO6D,MAAMmiB,EAAUxlB,SAASqD;UACxD,IAAMujB,IAAyB,IAAI3lB;UACnC,IAAM4lB,IAAiD,CAAA;UACvD,IAAMC,IAAqB,IAAI7lB;UAC/B0lB,EAAO7a,SAAQib;YACb,IAAMjoB,IAAQioB,EAAYjoB,MAAM;YAChCA,EAAMsS;YACN,IAAM4V,IAAcloB,EAAMwd,KAAK;YAG/B,IAFYqJ,EAAWtQ,IAAI2R,IAElB;cACPJ,EAAuBrb,IAAIyb;cAC3B,IAAIH,EAAeG;gBACjBH,EAAeG,GAAczb,IAAIwb;;gBAEjCF,EAAeG,KAAe,IAAI/lB,IAAI,EAAC8lB;;AAE3C;cACED,EAAmBvb,IAAIwb;;AACzB;UAGFH,EAAuB9a,SAAQmb;YAC7B,IAAMrmB,IAAM+kB,EAAWtQ,IAAI4R;YAC3B,IAAMC,IAAeL,EAAeI;YACpC5D,EAAYxjB,KAAK;cACfijB,MAAM5jB;cACNvO,QAAQiQ,EAAIjQ;cACZiL,OAAOsC,EAAKyL,aAAa/I,EAAIhF,QAAQ;cACrCgnB,UAAU/yB,EAAGgzB,mBAAmBtiB;cAChCoO,MAAM4M;cACNwH,aAAc,YAAW,KAAImE,IAC1B/d,KAAI9F,KAAM,IAAGA,OACbiZ,KAAK;;AACR;UAGJwK,EAAmBhb,SAAQmb;YACzB,IAAMrmB,IAAM+kB,EAAWtQ,IAAI4R;YAC3B5D,EAAYxjB,KAAK;cACfijB,MAAM5jB;cACNvO,QAAQiQ,EAAIjQ;cACZiL,OAAOsC,EAAKyL,aAAa/I,EAAIhF,QAAQ;cACrCgnB,UAAU/yB,EAAGgzB,mBAAmBtiB;cAChCoO,MAAM4M;cACNwH,aAAc,SAAQkE;;AACtB;AACF;AAEL,QAAC,OAAOlmB;QACP8N,QAAQvQ,MAAM,iBAAiByC,EAAEzD,SAASyD,EAAEomB;AAC9C;MAEA,OAAO9D;AAAW,MGrEduB,CACE1lB,GACAe,EAAMkJ,KAAI9F,KAAKA,EAAEnF,QACjBwK,MACG;IAEP,KAAKic;MAAkB,OAAOpC;;IAE9B,OAAO,KAAIA,MAAkBoC;AAC/B;IACE,OAAOpC;;AACT;;", "x_google_ignoreList": [1, 2, 3, 4, 5, 6, 7, 8, 14, 15]}