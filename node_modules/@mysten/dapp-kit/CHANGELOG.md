# @mysten/dapp-kit

## 0.16.15

### Patch Changes

- Updated dependencies [1c4a82d]
- Updated dependencies [783bb9e]
- Updated dependencies [783bb9e]
- Updated dependencies [5cbbb21]
  - @mysten/slush-wallet@0.1.15
  - @mysten/utils@0.1.1
  - @mysten/sui@1.36.0
  - @mysten/wallet-standard@0.16.5

## 0.16.14

### Patch Changes

- Updated dependencies [888afe6]
  - @mysten/sui@1.35.0
  - @mysten/slush-wallet@0.1.14
  - @mysten/wallet-standard@0.16.4

## 0.16.13

### Patch Changes

- Updated dependencies [3fb7a83]
  - @mysten/sui@1.34.0
  - @mysten/slush-wallet@0.1.13
  - @mysten/wallet-standard@0.16.3

## 0.16.12

### Patch Changes

- Updated dependencies [a00522b]
- Updated dependencies [a00522b]
  - @mysten/sui@1.33.0
  - @mysten/utils@0.1.0
  - @mysten/slush-wallet@0.1.12
  - @mysten/wallet-standard@0.16.2

## 0.16.11

### Patch Changes

- Updated dependencies [6b7deb8]
  - @mysten/sui@1.32.0
  - @mysten/slush-wallet@0.1.11
  - @mysten/wallet-standard@0.16.1

## 0.16.10

### Patch Changes

- Updated dependencies [1ff4e57]
- Updated dependencies [550e2e3]
- Updated dependencies [d0a406a]
- Updated dependencies [550e2e3]
  - @mysten/sui@1.31.0
  - @mysten/slush-wallet@0.1.10
  - @mysten/wallet-standard@0.16.0

## 0.16.9

### Patch Changes

- Updated dependencies [5bd6ca3]
  - @mysten/sui@1.30.5
  - @mysten/slush-wallet@0.1.9
  - @mysten/wallet-standard@0.15.6

## 0.16.8

### Patch Changes

- Updated dependencies [5dce590]
- Updated dependencies [4a5aef6]
  - @mysten/sui@1.30.4
  - @mysten/slush-wallet@0.1.8
  - @mysten/wallet-standard@0.15.5

## 0.16.7

### Patch Changes

- bb7c03a: Update dependencies
- Updated dependencies [4457f10]
- Updated dependencies [bb7c03a]
  - @mysten/sui@1.30.3
  - @mysten/wallet-standard@0.15.4
  - @mysten/slush-wallet@0.1.7
  - @mysten/utils@0.0.1

## 0.16.6

### Patch Changes

- Updated dependencies [b265f7e]
  - @mysten/sui@1.30.2
  - @mysten/slush-wallet@0.1.6
  - @mysten/wallet-standard@0.15.3

## 0.16.5

### Patch Changes

- Updated dependencies [ec519fc]
  - @mysten/sui@1.30.1
  - @mysten/slush-wallet@0.1.5
  - @mysten/wallet-standard@0.15.2

## 0.16.4

### Patch Changes

- 4721f75: Update slush wallet to statically define metadata
- Updated dependencies [2456052]
- Updated dependencies [5264038]
- Updated dependencies [2456052]
- Updated dependencies [4721f75]
- Updated dependencies [2456052]
- Updated dependencies [2456052]
- Updated dependencies [2456052]
  - @mysten/sui@1.30.0
  - @mysten/slush-wallet@0.1.4
  - @mysten/wallet-standard@0.15.1

## 0.16.3

### Patch Changes

- Updated dependencies [afbbb80]
  - @mysten/wallet-standard@0.15.0
  - @mysten/slush-wallet@0.1.3

## 0.16.2

### Patch Changes

- Updated dependencies [3f87e73]
  - @mysten/slush-wallet@0.1.2
  - @mysten/sui@1.29.1
  - @mysten/wallet-standard@0.14.9

## 0.16.1

### Patch Changes

- Updated dependencies [7d66a32]
- Updated dependencies [eb91fba]
- Updated dependencies [19a8045]
  - @mysten/sui@1.29.0
  - @mysten/slush-wallet@0.1.1
  - @mysten/wallet-standard@0.14.8

## 0.16.0

### Minor Changes

- c5adcb8: Integrated @mysten/slush-wallet, swapped registerStashedWallet for registerSlushWallet

### Patch Changes

- Updated dependencies [91624e0]
- Updated dependencies [9a94aea]
- Updated dependencies [c5adcb8]
  - @mysten/slush-wallet@0.1.0
  - @mysten/sui@1.28.2
  - @mysten/wallet-standard@0.14.7

## 0.15.7

### Patch Changes

- Updated dependencies [3cd4e53]
  - @mysten/sui@1.28.1
  - @mysten/wallet-standard@0.14.6
  - @mysten/zksend@0.12.27

## 0.15.6

### Patch Changes

- Updated dependencies [2705dc8]
- Updated dependencies [3eb8990]
  - @mysten/sui@1.28.0
  - @mysten/zksend@0.12.26
  - @mysten/wallet-standard@0.14.5

## 0.15.5

### Patch Changes

- Updated dependencies [d914fd3]
  - @mysten/zksend@0.12.25

## 0.15.4

### Patch Changes

- Updated dependencies [5cea435]
- Updated dependencies [9cacba9]
  - @mysten/sui@1.27.1
  - @mysten/zksend@0.12.24
  - @mysten/wallet-standard@0.14.4

## 0.15.3

### Patch Changes

- Updated dependencies [4d13ef8]
- Updated dependencies [4d13ef8]
  - @mysten/sui@1.27.0
  - @mysten/wallet-standard@0.14.3
  - @mysten/zksend@0.12.23

## 0.15.2

### Patch Changes

- 7ba32a4: update dependencies
- Updated dependencies [7ba32a4]
  - @mysten/wallet-standard@0.14.2
  - @mysten/sui@1.26.1
  - @mysten/zksend@0.12.22

## 0.15.1

### Patch Changes

- Updated dependencies [2e4740b]
- Updated dependencies [906dd14]
  - @mysten/zksend@0.12.21
  - @mysten/sui@1.26.0
  - @mysten/wallet-standard@0.14.1

## 0.15.0

### Minor Changes

- 68a9ecd: Default the `chain` identifier to the active network for all signing operations
- 132e67d: Add a `walletFilter` prop to the ConnectButton/ConnectModal components

### Patch Changes

- Updated dependencies [68a9ecd]
- Updated dependencies [e8b5d04]
- Updated dependencies [68a9ecd]
  - @mysten/wallet-standard@0.14.0
  - @mysten/sui@1.25.0
  - @mysten/zksend@0.12.20

## 0.14.53

### Patch Changes

- Updated dependencies [cf3d12d]
  - @mysten/sui@1.24.0
  - @mysten/wallet-standard@0.13.29
  - @mysten/zksend@0.12.19

## 0.14.52

### Patch Changes

- Updated dependencies [8baac61]
- Updated dependencies [8baac61]
  - @mysten/sui@1.23.0
  - @mysten/wallet-standard@0.13.28
  - @mysten/zksend@0.12.18

## 0.14.51

### Patch Changes

- Updated dependencies [03975f4]
  - @mysten/sui@1.22.0
  - @mysten/wallet-standard@0.13.27
  - @mysten/zksend@0.12.17

## 0.14.50

### Patch Changes

- @mysten/sui@1.21.2
- @mysten/wallet-standard@0.13.26
- @mysten/zksend@0.12.16

## 0.14.49

### Patch Changes

- @mysten/sui@1.21.1
- @mysten/wallet-standard@0.13.25
- @mysten/zksend@0.12.15

## 0.14.48

### Patch Changes

- Updated dependencies [3d8a0d9]
- Updated dependencies [20a5aaa]
  - @mysten/sui@1.21.0
  - @mysten/wallet-standard@0.13.24
  - @mysten/zksend@0.12.14

## 0.14.47

### Patch Changes

- Updated dependencies [827a200]
  - @mysten/sui@1.20.0
  - @mysten/wallet-standard@0.13.23
  - @mysten/zksend@0.12.13

## 0.14.46

### Patch Changes

- Updated dependencies [c39f32f]
- Updated dependencies [539168a]
  - @mysten/sui@1.19.0
  - @mysten/wallet-standard@0.13.22
  - @mysten/zksend@0.12.12

## 0.14.45

### Patch Changes

- 7abd243: Update repo links
- Updated dependencies [7abd243]
  - @mysten/sui@1.18.1
  - @mysten/wallet-standard@0.13.21
  - @mysten/zksend@0.12.11

## 0.14.44

### Patch Changes

- Updated dependencies [4f012b9]
- Updated dependencies [85bd9e4]
- Updated dependencies [5e3709d]
- Updated dependencies [b2928a9]
- Updated dependencies [a872b97]
- Updated dependencies [dc0e21e]
- Updated dependencies [85bd9e4]
- Updated dependencies [a872b97]
  - @mysten/sui@1.18.0
  - @mysten/zksend@0.12.10
  - @mysten/wallet-standard@0.13.20

## 0.14.43

### Patch Changes

- Updated dependencies [20af12d]
  - @mysten/sui@1.17.0
  - @mysten/wallet-standard@0.13.19
  - @mysten/zksend@0.12.9

## 0.14.42

### Patch Changes

- Updated dependencies [100207f]
  - @mysten/sui@1.16.2
  - @mysten/wallet-standard@0.13.18
  - @mysten/zksend@0.12.8

## 0.14.41

### Patch Changes

- @mysten/sui@1.16.1
- @mysten/wallet-standard@0.13.17
- @mysten/zksend@0.12.7

## 0.14.40

### Patch Changes

- Updated dependencies [ec2dc7f]
- Updated dependencies [ec2dc7f]
  - @mysten/sui@1.16.0
  - @mysten/wallet-standard@0.13.16
  - @mysten/zksend@0.12.6

## 0.14.39

### Patch Changes

- @mysten/sui@1.15.1
- @mysten/wallet-standard@0.13.15
- @mysten/zksend@0.12.5

## 0.14.38

### Patch Changes

- Updated dependencies [6460e45]
  - @mysten/sui@1.15.0
  - @mysten/wallet-standard@0.13.14
  - @mysten/zksend@0.12.4

## 0.14.37

### Patch Changes

- Updated dependencies [f03e60f]
  - @mysten/zksend@0.12.3

## 0.14.36

### Patch Changes

- 67d4620: Revert dev dependency change from v0.14.35 due to breakage

## 0.14.35

### Patch Changes

- 6f44f4b: Mark vanilla-extract libraries as dev dependencies

## 0.14.34

### Patch Changes

- ec919a0: Ensure wallet-icon is a non-empty string before rendering
- Updated dependencies [4bac808]
- Updated dependencies [4bac808]
- Updated dependencies [938fb6e]
  - @mysten/zksend@0.12.2
  - @mysten/sui@1.14.4
  - @mysten/wallet-standard@0.13.13

## 0.14.33

### Patch Changes

- Updated dependencies [1765780]
  - @mysten/zksend@0.12.1

## 0.14.32

### Patch Changes

- Updated dependencies [60bdb62]
- Updated dependencies [364a93a]
- Updated dependencies [364a93a]
  - @mysten/zksend@0.12.0

## 0.14.31

### Patch Changes

- Updated dependencies [d5a23d7]
  - @mysten/sui@1.14.3
  - @mysten/wallet-standard@0.13.12
  - @mysten/zksend@0.11.12

## 0.14.30

### Patch Changes

- Updated dependencies [e7bc63e]
  - @mysten/sui@1.14.2
  - @mysten/wallet-standard@0.13.11
  - @mysten/zksend@0.11.11

## 0.14.29

### Patch Changes

- Updated dependencies [69ef100]
  - @mysten/sui@1.14.1
  - @mysten/wallet-standard@0.13.10
  - @mysten/zksend@0.11.10

## 0.14.28

### Patch Changes

- Updated dependencies [c24814b]
  - @mysten/sui@1.14.0
  - @mysten/wallet-standard@0.13.9
  - @mysten/zksend@0.11.9

## 0.14.27

### Patch Changes

- Updated dependencies [477d2a4]
  - @mysten/sui@1.13.0
  - @mysten/wallet-standard@0.13.8
  - @mysten/zksend@0.11.8

## 0.14.26

### Patch Changes

- Updated dependencies [af39b6a]
  - @mysten/zksend@0.11.7

## 0.14.25

### Patch Changes

- Updated dependencies [5436a90]
- Updated dependencies [5436a90]
  - @mysten/sui@1.12.0
  - @mysten/wallet-standard@0.13.7
  - @mysten/zksend@0.11.6

## 0.14.24

### Patch Changes

- Updated dependencies [489f421]
- Updated dependencies [489f421]
  - @mysten/sui@1.11.0
  - @mysten/wallet-standard@0.13.6
  - @mysten/zksend@0.11.5

## 0.14.23

### Patch Changes

- 640b757: Add `getSuiClientQuery` to get the `queryOptions` config for usage with the `QueryClient`
  outside of React hooks. Added `useSuiClientSuspenseQuery` to support suspense-based data fetching.

## 0.14.22

### Patch Changes

- Updated dependencies [830b8d8]
  - @mysten/sui@1.10.0
  - @mysten/wallet-standard@0.13.5
  - @mysten/zksend@0.11.4

## 0.14.21

### Patch Changes

- Updated dependencies [0db770a]
  - @mysten/zksend@0.11.3

## 0.14.20

### Patch Changes

- Updated dependencies [2c96b06]
- Updated dependencies [1fd22cc]
  - @mysten/sui@1.9.0
  - @mysten/wallet-standard@0.13.4
  - @mysten/zksend@0.11.2

## 0.14.19

### Patch Changes

- Updated dependencies [22844ae]
  - @mysten/zksend@0.11.1

## 0.14.18

### Patch Changes

- 012aefe: Support passing network param through to stashed wallet
- Updated dependencies [4bdef4a]
- Updated dependencies [569511a]
  - @mysten/zksend@0.11.0
  - @mysten/sui@1.8.0
  - @mysten/wallet-standard@0.13.3

## 0.14.17

### Patch Changes

- Updated dependencies [143cd9d]
- Updated dependencies [4357ac6]
- Updated dependencies [4019dd7]
- Updated dependencies [4019dd7]
- Updated dependencies [00a974d]
  - @mysten/sui@1.7.0
  - @mysten/wallet-standard@0.13.2
  - @mysten/zksend@0.10.6

## 0.14.16

### Patch Changes

- Updated dependencies [a3e32fe]
  - @mysten/sui@1.6.0
  - @mysten/wallet-standard@0.13.1
  - @mysten/zksend@0.10.5

## 0.14.15

### Patch Changes

- Updated dependencies [0851b31]
- Updated dependencies [f37b3c2]
  - @mysten/wallet-standard@0.13.0
  - @mysten/sui@1.5.0
  - @mysten/zksend@0.10.4

## 0.14.14

### Patch Changes

- Updated dependencies [4419234]
  - @mysten/sui@1.4.0
  - @mysten/wallet-standard@0.12.14
  - @mysten/zksend@0.10.3

## 0.14.13

### Patch Changes

- Updated dependencies [a45f461]
  - @mysten/sui@1.3.1
  - @mysten/wallet-standard@0.12.13
  - @mysten/zksend@0.10.2

## 0.14.12

### Patch Changes

- 0f27a97: Update dependencies
- Updated dependencies [7fc464a]
- Updated dependencies [086b2bc]
- Updated dependencies [0fb0628]
- Updated dependencies [cdedf69]
- Updated dependencies [0f27a97]
- Updated dependencies [beed646]
  - @mysten/sui@1.3.0
  - @mysten/wallet-standard@0.12.12
  - @mysten/zksend@0.10.1

## 0.14.11

### Patch Changes

- Updated dependencies [06a900c1ab]
- Updated dependencies [45877014d1]
- Updated dependencies [45877014d1]
- Updated dependencies [87d6f75403]
  - @mysten/sui@1.2.1
  - @mysten/zksend@0.10.0
  - @mysten/wallet-standard@0.12.11

## 0.14.10

### Patch Changes

- Updated dependencies [fef99d377f]
  - @mysten/sui@1.2.0
  - @mysten/wallet-standard@0.12.10
  - @mysten/zksend@0.9.10

## 0.14.9

### Patch Changes

- Updated dependencies [805ff4d4c2]
  - @mysten/wallet-standard@0.12.9
  - @mysten/zksend@0.9.9

## 0.14.8

### Patch Changes

- Updated dependencies [0dfff33b95]
  - @mysten/sui@1.1.2
  - @mysten/wallet-standard@0.12.8
  - @mysten/zksend@0.9.8

## 0.14.7

### Patch Changes

- Updated dependencies [101f1ff4b8]
  - @mysten/sui@1.1.1
  - @mysten/wallet-standard@0.12.7
  - @mysten/zksend@0.9.7

## 0.14.6

### Patch Changes

- Updated dependencies [bae8f9683c]
  - @mysten/sui@1.1.0
  - @mysten/wallet-standard@0.12.6
  - @mysten/zksend@0.9.6

## 0.14.5

### Patch Changes

- Updated dependencies [369b924343]
  - @mysten/sui@1.0.5
  - @mysten/wallet-standard@0.12.5
  - @mysten/zksend@0.9.5

## 0.14.4

### Patch Changes

- Updated dependencies [f1e828f557]
- Updated dependencies [8e0e8eb643]
  - @mysten/sui@1.0.4
  - @mysten/zksend@0.9.4
  - @mysten/wallet-standard@0.12.4

## 0.14.3

### Patch Changes

- Updated dependencies [1f20580841]
  - @mysten/sui@1.0.3
  - @mysten/wallet-standard@0.12.3
  - @mysten/zksend@0.9.3

## 0.14.2

### Patch Changes

- Updated dependencies [f0a839f874]
  - @mysten/sui@1.0.2
  - @mysten/wallet-standard@0.12.2
  - @mysten/zksend@0.9.2

## 0.14.1

### Patch Changes

- Updated dependencies [6fc6235984]
  - @mysten/sui@1.0.1
  - @mysten/wallet-standard@0.12.1
  - @mysten/zksend@0.9.1

## 0.14.0

### Minor Changes

- a92b03de42: The Typescript SDK has been renamed to `@mysten/sui` and includes many new features
  and breaking changes. See the
  [full migration guide](https://sdk.mystenlabs.com/typescript/migrations/sui-1.0) for details on
  how to upgrade.

### Patch Changes

- Updated dependencies [ebdfe7cf21]
- Updated dependencies [3e1d716642]
- Updated dependencies [a92b03de42]
  - @mysten/sui@1.0.0
  - @mysten/zksend@0.9.0
  - @mysten/wallet-standard@0.12.0

## 0.13.2

### Patch Changes

- 3f8b08dedc: Fix broken theme style tag in canary versions of React when the provider is placed
  outside of the body tag
- Updated dependencies [eeb19db837]
  - @mysten/zksend@0.8.2

## 0.13.1

### Patch Changes

- 2da78f77a3: Update stashed logo and fix detection of stashed wallet
- Updated dependencies [2da78f77a3]
  - @mysten/zksend@0.8.1

## 0.13.0

### Minor Changes

- 807262f394: The `zksend` property on the `WalletProvider` has been replaced with a `stashedWallet`
  option.

### Patch Changes

- Updated dependencies [807262f394]
  - @mysten/zksend@0.8.0

## 0.12.15

### Patch Changes

- Updated dependencies [99b112178c]
  - @mysten/sui.js@0.54.1
  - @mysten/wallet-standard@0.11.6
  - @mysten/zksend@0.7.2

## 0.12.14

### Patch Changes

- Updated dependencies [29d8e45b0e]
- Updated dependencies [b7f673dbd9]
- Updated dependencies [b7f673dbd9]
- Updated dependencies [123b42c75c]
  - @mysten/zksend@0.7.1
  - @mysten/sui.js@0.54.0
  - @mysten/wallet-standard@0.11.5

## 0.12.13

### Patch Changes

- Updated dependencies [774bfb41a8]
- Updated dependencies [879421a5ac]
  - @mysten/sui.js@0.53.0
  - @mysten/zksend@0.7.0
  - @mysten/wallet-standard@0.11.4

## 0.12.12

### Patch Changes

- Updated dependencies [9a9ff3cde1]
- Updated dependencies [905bc99e88]
  - @mysten/zksend@0.6.0

## 0.12.11

### Patch Changes

- Updated dependencies [44ba140be0]
  - @mysten/zksend@0.5.0

## 0.12.10

### Patch Changes

- Updated dependencies [929db4976a]
  - @mysten/sui.js@0.52.0
  - @mysten/wallet-standard@0.11.3
  - @mysten/zksend@0.4.4

## 0.12.9

### Patch Changes

- Updated dependencies [59168f73ff]
- Updated dependencies [b8f2a859ce]
  - @mysten/zksend@0.4.3

## 0.12.8

### Patch Changes

- Updated dependencies [b4ecdb5860]
  - @mysten/sui.js@0.51.2
  - @mysten/wallet-standard@0.11.2
  - @mysten/zksend@0.4.2

## 0.12.7

### Patch Changes

- Updated dependencies [6984dd1e38]
  - @mysten/sui.js@0.51.1
  - @mysten/wallet-standard@0.11.1
  - @mysten/zksend@0.4.1

## 0.12.6

### Patch Changes

- Updated dependencies [c05a4e8cb7]
- Updated dependencies [c05a4e8cb7]
- Updated dependencies [c05a4e8cb7]
- Updated dependencies [c05a4e8cb7]
- Updated dependencies [c05a4e8cb7]
  - @mysten/zksend@0.4.0

## 0.12.5

### Patch Changes

- Updated dependencies [b82832279b]
  - @mysten/zksend@0.3.1

## 0.12.4

### Patch Changes

- Updated dependencies [3b1da3967a]
  - @mysten/zksend@0.3.0

## 0.12.3

### Patch Changes

- Updated dependencies [0cafa94027]
- Updated dependencies [437f0ca2ef]
  - @mysten/sui.js@0.51.0
  - @mysten/wallet-standard@0.11.0
  - @mysten/zksend@0.2.3

## 0.12.2

### Patch Changes

- 4830361fa4: Updated typescript version
- Updated dependencies [4830361fa4]
- Updated dependencies [4fd676671b]
  - @mysten/wallet-standard@0.10.3
  - @mysten/sui.js@0.50.1
  - @mysten/zksend@0.2.2

## 0.12.1

### Patch Changes

- Updated dependencies [f069e3a13d]
  - @mysten/zksend@0.2.1

## 0.12.0

### Minor Changes

- 7b8d044603: Add support for zkSend wallet

### Patch Changes

- 33c7325ee1: Fix infinite queries never ending
- Updated dependencies [a34f1cb67d]
- Updated dependencies [c07aa19958]
- Updated dependencies [13e922d9b1]
- Updated dependencies [c859f41a1c]
- Updated dependencies [d21c01ed47]
- Updated dependencies [2814db6529]
- Updated dependencies [e81f49e8dc]
- Updated dependencies [e87d99734a]
- Updated dependencies [c08e3569ef]
- Updated dependencies [9a14e61db4]
- Updated dependencies [ba6fccd010]
- Updated dependencies [c6b3066069]
- Updated dependencies [66fbbc7faa]
- Updated dependencies [7b8d044603]
- Updated dependencies [13e922d9b1]
- Updated dependencies [c6b3066069]
- Updated dependencies [a2904e0075]
- Updated dependencies [ea2744b0c3]
- Updated dependencies [44a1f9ea0b]
- Updated dependencies [7cc09a7bb4]
- Updated dependencies [9a14e61db4]
- Updated dependencies [f041b10b9f]
- Updated dependencies [c1f6cfff47]
- Updated dependencies [7c9a8cc24b]
- Updated dependencies [a34f1cb67d]
- Updated dependencies [ae9ae17eea]
- Updated dependencies [220a766d86]
  - @mysten/sui.js@0.50.0
  - @mysten/zksend@0.2.0
  - @mysten/wallet-standard@0.10.2

## 0.11.1

### Patch Changes

- Updated dependencies [9ac0a4ec01]
  - @mysten/wallet-standard@0.10.1
  - @mysten/sui.js@0.49.1

## 0.11.0

### Minor Changes

- e5f9e3ba21: Replace tsup based build to fix issues with esm/cjs dual publishing

### Patch Changes

- 9ba167b0af: Default storage to memory storage when local storage isn't available during SSR
- Updated dependencies [e5f9e3ba21]
  - @mysten/wallet-standard@0.10.0
  - @mysten/sui.js@0.49.0

## 0.10.3

### Patch Changes

- 165ad6b21d: Support both `id` and `name` as unique wallet identifiers.
- dd362ec1d6: Update docs url to sdk.mystenlabs.com
- d55db38647: Set a high z-index for the account dropdown so that it doesn't get hidden
- Updated dependencies [dd362ec1d6]
- Updated dependencies [165ad6b21d]
  - @mysten/wallet-standard@0.9.0
  - @mysten/sui.js@0.48.1

## 0.10.2

### Patch Changes

- 2ae1a6a108: Early return on `useAutoConnectWallet` to improve account switching performance
- b30fecbbca: Fix missing export of useSuiClientQueries
- Updated dependencies [cdcfa76c43]
  - @mysten/sui.js@0.48.0
  - @mysten/wallet-standard@0.8.11

## 0.10.1

### Patch Changes

- 367779ea2: Improve the reliability of the `useAutoConnectWallet` hook.

## 0.10.0

### Minor Changes

- 47b137d03: Add new useSuiClientQueries hook to support variable client queries

## 0.9.1

### Patch Changes

- Updated dependencies [194c980cb]
- Updated dependencies [9ac7e2f3d]
- Updated dependencies [0259aec82]
- Updated dependencies [64d45ba27]
  - @mysten/sui.js@0.47.0
  - @mysten/wallet-standard@0.8.10

## 0.9.0

### Minor Changes

- c5d4db238: Have useSignPersonalMessage fall back to use sui:signMessage

### Patch Changes

- Updated dependencies [652bcdd92]
  - @mysten/sui.js@0.46.1
  - @mysten/wallet-standard@0.8.9

## 0.8.0

### Minor Changes

- 103ad29c0: Fix the connect button component not opening the modal

### Patch Changes

- Updated dependencies [28c2c3330]
- Updated dependencies [43444c58f]
- Updated dependencies [8d1e74e52]
- Updated dependencies [093554a0d]
- Updated dependencies [3718a230b]
  - @mysten/sui.js@0.46.0
  - @mysten/wallet-standard@0.8.8

## 0.7.0

### Minor Changes

- b359868e3: Fix how esm types are built
- 5d7055365: Fix bug where style resets were being applied to custom trigger buttons

### Patch Changes

- Updated dependencies [30b47b758]
  - @mysten/sui.js@0.45.1
  - @mysten/wallet-standard@0.8.7

## 0.6.0

### Minor Changes

- d4d9c9218: Upgrade dapp-kit and the scaffold applications to react-query v5
- fb0ce3485: Add global connection status info and change the hook interface of `useCurrentWallet`
  to return an object to encapsulate connection info together. To migrate:

  Before: const currentWallet = useCurrentWallet();

  After: const { currentWallet } = useCurrentWallet();

### Patch Changes

- 09c103002: fix pagination in useSuiClientInfiniteQuery
- e0578094b: Fix typing of data for useSuiClientInfiniteQuery
- Updated dependencies [b9afb5567]
  - @mysten/sui.js@0.45.0
  - @mysten/wallet-standard@0.8.6

## 0.5.0

### Minor Changes

- ade244c3d: Add a new createNetworkConfig helper for managing network specific state

## 0.4.0

### Minor Changes

- b7c304696: Show account label or suiNS domain in connect button

## 0.3.0

### Minor Changes

- 361818abc: execute transaction from dApp rather than wallet in useSignAndExecuteTransactionBlock
- 2b532bc37: Fix issue where CSS was being overridden by application code
- 0c5cdc049: Expose types related to theming
- c7e12c928: Infer the active chain when signing transactions and expose some more descriptive
  errors

### Patch Changes

- Updated dependencies [b48289346]
- Updated dependencies [11cf4e68b]
  - @mysten/wallet-standard@0.8.5
  - @mysten/sui.js@0.44.0

## 0.2.4

### Patch Changes

- Updated dependencies [004fb1991]
  - @mysten/sui.js@0.43.3
  - @mysten/wallet-standard@0.8.4

## 0.2.3

### Patch Changes

- Updated dependencies [9b052166d]
  - @mysten/sui.js@0.43.2
  - @mysten/wallet-standard@0.8.3

## 0.2.2

### Patch Changes

- 87ef14c70: Fix side effects

## 0.2.1

### Patch Changes

- Updated dependencies [faa13ded9]
- Updated dependencies [c5684bb52]
  - @mysten/sui.js@0.43.1
  - @mysten/wallet-standard@0.8.2

## 0.2.0

### Minor Changes

- b29f66f18: Add theme definitions for our UI components
- 1227ee1ce: Theme UI components based on provided theme and add design polish
- 8e9590a8c: Exclude non-Sui accounts from the accounts state when someone connects a multi-chain
  wallet

## 0.1.0

### Minor Changes

- c51db090a: update useSuiClientQuery hooks and remove generated RPC hooks
- ccfc810c0: Require wallets to have some default characteristics in order to be recognized

### Patch Changes

- Updated dependencies [781d073d9]
- Updated dependencies [3764c464f]
- Updated dependencies [e4484852b]
- Updated dependencies [71e0a3197]
- Updated dependencies [1bc430161]
  - @mysten/sui.js@0.43.0
  - @mysten/wallet-standard@0.8.1

## 0.0.5

### Patch Changes

- Updated dependencies [fd8589806]
- Updated dependencies [fd8589806]
- Updated dependencies [8b9e5f737]
  - @mysten/wallet-standard@0.8.0
  - @mysten/sui.js@0.42.0

## 0.0.4

### Patch Changes

- @mysten/sui.js@0.41.2
- @mysten/wallet-standard@0.7.2

## 0.0.3

### Patch Changes

- Updated dependencies [24c21e1f0]
  - @mysten/sui.js@0.41.1

## 0.0.2

### Patch Changes

- Updated dependencies [ba8e3b857]
- Updated dependencies [f4b7b3474]
  - @mysten/sui.js@0.41.0

## 0.0.1

### Patch Changes

- Updated dependencies [a503cad34]
- Updated dependencies [8281e3d25]
  - @mysten/sui.js@0.40.0
