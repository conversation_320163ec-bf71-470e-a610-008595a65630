import type { RecipeVariants } from '@vanilla-extract/recipes';
export declare const headingVariants: import("@vanilla-extract/recipes").RuntimeFn<{
    size: {
        sm: {
            fontSize: `var(--${string})` | `var(--${string}, ${string})`;
        };
        md: {
            fontSize: `var(--${string})` | `var(--${string}, ${string})`;
        };
        lg: {
            fontSize: `var(--${string})` | `var(--${string}, ${string})`;
        };
        xl: {
            fontSize: `var(--${string})` | `var(--${string}, ${string})`;
        };
    };
    weight: {
        normal: {
            fontWeight: `var(--${string})` | `var(--${string}, ${string})`;
        };
        bold: {
            fontWeight: `var(--${string})` | `var(--${string}, ${string})`;
        };
    };
    truncate: {
        true: {
            overflow: "hidden";
            textOverflow: "ellipsis";
            whiteSpace: "nowrap";
        };
    };
}>;
export type HeadingVariants = RecipeVariants<typeof headingVariants>;
