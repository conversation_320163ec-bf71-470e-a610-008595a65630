import type { RecipeVariants } from '@vanilla-extract/recipes';
export declare const buttonVariants: import("@vanilla-extract/recipes").RuntimeFn<{
    variant: {
        primary: {
            backgroundColor: `var(--${string})` | `var(--${string}, ${string})`;
            color: `var(--${string})` | `var(--${string}, ${string})`;
            boxShadow: `var(--${string})` | `var(--${string}, ${string})`;
            ':hover': {
                backgroundColor: `var(--${string})` | `var(--${string}, ${string})`;
            };
        };
        outline: {
            borderWidth: number;
            borderStyle: "solid";
            borderColor: `var(--${string})` | `var(--${string}, ${string})`;
            color: `var(--${string})` | `var(--${string}, ${string})`;
            ':hover': {
                backgroundColor: `var(--${string})` | `var(--${string}, ${string})`;
            };
        };
    };
    size: {
        md: {
            borderRadius: `var(--${string})` | `var(--${string}, ${string})`;
            padding: "8px 16px";
        };
        lg: {
            borderRadius: `var(--${string})` | `var(--${string}, ${string})`;
            padding: "16px 24px ";
        };
    };
}>;
export type ButtonVariants = RecipeVariants<typeof buttonVariants>;
