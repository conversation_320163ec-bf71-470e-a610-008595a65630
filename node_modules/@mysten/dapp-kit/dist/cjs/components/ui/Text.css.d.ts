import type { RecipeVariants } from '@vanilla-extract/recipes';
export declare const textVariants: import("@vanilla-extract/recipes").RuntimeFn<{
    size: {
        sm: {
            fontSize: `var(--${string})` | `var(--${string}, ${string})`;
        };
    };
    weight: {
        normal: {
            fontWeight: `var(--${string})` | `var(--${string}, ${string})`;
        };
        medium: {
            fontWeight: `var(--${string})` | `var(--${string}, ${string})`;
        };
        bold: {
            fontWeight: `var(--${string})` | `var(--${string}, ${string})`;
        };
    };
    color: {
        muted: {
            color: `var(--${string})` | `var(--${string}, ${string})`;
        };
        danger: {
            color: `var(--${string})` | `var(--${string}, ${string})`;
        };
    };
    mono: {
        true: {
            fontFamily: "ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace";
        };
    };
}>;
export type TextVariants = RecipeVariants<typeof textVariants>;
