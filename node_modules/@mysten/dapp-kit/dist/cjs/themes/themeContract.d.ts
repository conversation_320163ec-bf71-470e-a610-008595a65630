declare const themeContractValues: {
    blurs: {
        modalOverlay: string;
    };
    backgroundColors: {
        primaryButton: string;
        primaryButtonHover: string;
        outlineButtonHover: string;
        walletItemHover: string;
        walletItemSelected: string;
        modalOverlay: string;
        modalPrimary: string;
        modalSecondary: string;
        iconButton: string;
        iconButtonHover: string;
        dropdownMenu: string;
        dropdownMenuSeparator: string;
    };
    borderColors: {
        outlineButton: string;
    };
    colors: {
        primaryButton: string;
        outlineButton: string;
        body: string;
        bodyMuted: string;
        bodyDanger: string;
        iconButton: string;
    };
    radii: {
        small: string;
        medium: string;
        large: string;
        xlarge: string;
    };
    shadows: {
        primaryButton: string;
        walletItemSelected: string;
    };
    fontWeights: {
        normal: string;
        medium: string;
        bold: string;
    };
    fontSizes: {
        small: string;
        medium: string;
        large: string;
        xlarge: string;
    };
    typography: {
        fontFamily: string;
        fontStyle: string;
        lineHeight: string;
        letterSpacing: string;
    };
};
export type ThemeVars = typeof themeContractValues;
/**
 * A custom theme that is enabled when various conditions are
 */
export type DynamicTheme = {
    /**
     * An optional media query required for the given theme to be enabled. This is useful
     * when you want the theme of your application to automatically switch depending on
     * a media feature.
     *
     * @example '(prefers-color-scheme: dark)'
     */
    mediaQuery?: string;
    /**
     * An optional CSS selector required for the given theme to be enabled. This is useful
     * when you have a manual theme switcher on your application that sets a top-level
     * class name or data-attribute to control the current theme.
     *
     * @example '.data-dark'
     */
    selector?: string;
    /** The theme definitions that will be set when the selector and mediaQuery criteria are matched. */
    variables: ThemeVars;
};
export type Theme = ThemeVars | DynamicTheme[];
export declare const themeVars: {
    blurs: {
        modalOverlay: `var(--${string})` | `var(--${string}, ${string})`;
    };
    backgroundColors: {
        primaryButton: `var(--${string})` | `var(--${string}, ${string})`;
        primaryButtonHover: `var(--${string})` | `var(--${string}, ${string})`;
        outlineButtonHover: `var(--${string})` | `var(--${string}, ${string})`;
        walletItemHover: `var(--${string})` | `var(--${string}, ${string})`;
        walletItemSelected: `var(--${string})` | `var(--${string}, ${string})`;
        modalOverlay: `var(--${string})` | `var(--${string}, ${string})`;
        modalPrimary: `var(--${string})` | `var(--${string}, ${string})`;
        modalSecondary: `var(--${string})` | `var(--${string}, ${string})`;
        iconButton: `var(--${string})` | `var(--${string}, ${string})`;
        iconButtonHover: `var(--${string})` | `var(--${string}, ${string})`;
        dropdownMenu: `var(--${string})` | `var(--${string}, ${string})`;
        dropdownMenuSeparator: `var(--${string})` | `var(--${string}, ${string})`;
    };
    borderColors: {
        outlineButton: `var(--${string})` | `var(--${string}, ${string})`;
    };
    colors: {
        primaryButton: `var(--${string})` | `var(--${string}, ${string})`;
        outlineButton: `var(--${string})` | `var(--${string}, ${string})`;
        body: `var(--${string})` | `var(--${string}, ${string})`;
        bodyMuted: `var(--${string})` | `var(--${string}, ${string})`;
        bodyDanger: `var(--${string})` | `var(--${string}, ${string})`;
        iconButton: `var(--${string})` | `var(--${string}, ${string})`;
    };
    radii: {
        small: `var(--${string})` | `var(--${string}, ${string})`;
        medium: `var(--${string})` | `var(--${string}, ${string})`;
        large: `var(--${string})` | `var(--${string}, ${string})`;
        xlarge: `var(--${string})` | `var(--${string}, ${string})`;
    };
    shadows: {
        primaryButton: `var(--${string})` | `var(--${string}, ${string})`;
        walletItemSelected: `var(--${string})` | `var(--${string}, ${string})`;
    };
    fontWeights: {
        normal: `var(--${string})` | `var(--${string}, ${string})`;
        medium: `var(--${string})` | `var(--${string}, ${string})`;
        bold: `var(--${string})` | `var(--${string}, ${string})`;
    };
    fontSizes: {
        small: `var(--${string})` | `var(--${string}, ${string})`;
        medium: `var(--${string})` | `var(--${string}, ${string})`;
        large: `var(--${string})` | `var(--${string}, ${string})`;
        xlarge: `var(--${string})` | `var(--${string}, ${string})`;
    };
    typography: {
        fontFamily: `var(--${string})` | `var(--${string}, ${string})`;
        fontStyle: `var(--${string})` | `var(--${string}, ${string})`;
        lineHeight: `var(--${string})` | `var(--${string}, ${string})`;
        letterSpacing: `var(--${string})` | `var(--${string}, ${string})`;
    };
};
export {};
