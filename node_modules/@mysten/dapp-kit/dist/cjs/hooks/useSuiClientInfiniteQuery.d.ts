import type { SuiClient } from '@mysten/sui/client';
import type { InfiniteData, UseInfiniteQueryOptions, UseInfiniteQueryResult } from '@tanstack/react-query';
import type { PartialBy } from '../types/utilityTypes.js';
interface PaginatedResult {
    data?: unknown;
    nextCursor?: unknown;
    hasNextPage: boolean;
}
export type SuiRpcPaginatedMethodName = {
    [K in keyof SuiClient]: SuiClient[K] extends (input: any) => Promise<PaginatedResult> ? K : never;
}[keyof SuiClient];
export type SuiRpcPaginatedMethods = {
    [K in SuiRpcPaginatedMethodName]: SuiClient[K] extends (input: infer Params) => Promise<infer Result extends {
        hasNextPage?: boolean | null;
        nextCursor?: infer Cursor | null;
    }> ? {
        name: K;
        result: Result;
        params: Params;
        cursor: Cursor;
    } : never;
};
export type UseSuiClientInfiniteQueryOptions<T extends keyof SuiRpcPaginatedMethods, TData> = PartialBy<Omit<UseInfiniteQueryOptions<SuiRpcPaginatedMethods[T]['result'], Error, TData, unknown[]>, 'queryFn' | 'initialPageParam' | 'getNextPageParam'>, 'queryKey'>;
export declare function useSuiClientInfiniteQuery<T extends keyof SuiRpcPaginatedMethods, TData = InfiniteData<SuiRpcPaginatedMethods[T]['result']>>(method: T, params: SuiRpcPaginatedMethods[T]['params'], { queryKey, enabled, ...options }?: UseSuiClientInfiniteQueryOptions<T, TData>): UseInfiniteQueryResult<TData, Error>;
export {};
