{"version": 3, "sources": ["../../../../src/hooks/wallet/useSlushWallet.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { registerSlushWallet } from '@mysten/slush-wallet';\nimport { useLayoutEffect } from 'react';\n\nexport interface SlushWalletConfig {\n\tname: string;\n\torigin?: string;\n}\n\nexport function useSlushWallet(config?: SlushWalletConfig) {\n\tuseLayoutEffect(() => {\n\t\tif (!config?.name) {\n\t\t\treturn;\n\t\t}\n\n\t\tlet cleanup: (() => void) | undefined;\n\t\tlet isMounted = true;\n\n\t\ttry {\n\t\t\tconst result = registerSlushWallet(config.name, {\n\t\t\t\torigin: config.origin,\n\t\t\t});\n\n\t\t\tif (isMounted && result) {\n\t\t\t\tcleanup = result.unregister;\n\t\t\t} else if (result) {\n\t\t\t\tresult.unregister();\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error('Failed to register Slush wallet:', error);\n\t\t}\n\n\t\treturn () => {\n\t\t\tisMounted = false;\n\t\t\tif (cleanup) cleanup();\n\t\t};\n\t}, [config?.name, config?.origin]);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,0BAAoC;AACpC,mBAAgC;AAOzB,SAAS,eAAe,QAA4B;AAC1D,oCAAgB,MAAM;AACrB,QAAI,CAAC,QAAQ,MAAM;AAClB;AAAA,IACD;AAEA,QAAI;AACJ,QAAI,YAAY;AAEhB,QAAI;AACH,YAAM,aAAS,yCAAoB,OAAO,MAAM;AAAA,QAC/C,QAAQ,OAAO;AAAA,MAChB,CAAC;AAED,UAAI,aAAa,QAAQ;AACxB,kBAAU,OAAO;AAAA,MAClB,WAAW,QAAQ;AAClB,eAAO,WAAW;AAAA,MACnB;AAAA,IACD,SAAS,OAAO;AACf,cAAQ,MAAM,oCAAoC,KAAK;AAAA,IACxD;AAEA,WAAO,MAAM;AACZ,kBAAY;AACZ,UAAI,QAAS,SAAQ;AAAA,IACtB;AAAA,EACD,GAAG,CAAC,QAAQ,MAAM,QAAQ,MAAM,CAAC;AAClC;", "names": []}