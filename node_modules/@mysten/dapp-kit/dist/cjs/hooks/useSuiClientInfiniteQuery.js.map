{"version": 3, "sources": ["../../../src/hooks/useSuiClientInfiniteQuery.ts", "../../../src/hooks/useSuiClient.ts", "../../../src/components/SuiClientProvider.tsx"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SuiClient } from '@mysten/sui/client';\nimport type {\n\tInfiniteData,\n\tUseInfiniteQueryOptions,\n\tUseInfiniteQueryResult,\n} from '@tanstack/react-query';\nimport { useInfiniteQuery } from '@tanstack/react-query';\n\nimport type { PartialBy } from '../types/utilityTypes.js';\nimport { useSuiClientContext } from './useSuiClient.js';\n\ninterface PaginatedResult {\n\tdata?: unknown;\n\tnextCursor?: unknown;\n\thasNextPage: boolean;\n}\n\nexport type SuiRpcPaginatedMethodName = {\n\t[K in keyof SuiClient]: SuiClient[K] extends (input: any) => Promise<PaginatedResult> ? K : never;\n}[keyof SuiClient];\n\nexport type SuiRpcPaginatedMethods = {\n\t[K in SuiRpcPaginatedMethodName]: SuiClient[K] extends (\n\t\tinput: infer Params,\n\t) => Promise<\n\t\tinfer Result extends { hasNextPage?: boolean | null; nextCursor?: infer Cursor | null }\n\t>\n\t\t? {\n\t\t\t\tname: K;\n\t\t\t\tresult: Result;\n\t\t\t\tparams: Params;\n\t\t\t\tcursor: Cursor;\n\t\t\t}\n\t\t: never;\n};\n\nexport type UseSuiClientInfiniteQueryOptions<\n\tT extends keyof SuiRpcPaginatedMethods,\n\tTData,\n> = PartialBy<\n\tOmit<\n\t\tUseInfiniteQueryOptions<SuiRpcPaginatedMethods[T]['result'], Error, TData, unknown[]>,\n\t\t'queryFn' | 'initialPageParam' | 'getNextPageParam'\n\t>,\n\t'queryKey'\n>;\n\nexport function useSuiClientInfiniteQuery<\n\tT extends keyof SuiRpcPaginatedMethods,\n\tTData = InfiniteData<SuiRpcPaginatedMethods[T]['result']>,\n>(\n\tmethod: T,\n\tparams: SuiRpcPaginatedMethods[T]['params'],\n\t{\n\t\tqueryKey = [],\n\t\tenabled = !!params,\n\t\t...options\n\t}: UseSuiClientInfiniteQueryOptions<T, TData> = {},\n): UseInfiniteQueryResult<TData, Error> {\n\tconst suiContext = useSuiClientContext();\n\n\treturn useInfiniteQuery({\n\t\t...options,\n\t\tinitialPageParam: null,\n\t\tqueryKey: [suiContext.network, method, params, ...queryKey],\n\t\tenabled,\n\t\tqueryFn: ({ pageParam }) =>\n\t\t\tsuiContext.client[method]({\n\t\t\t\t...(params ?? {}),\n\t\t\t\tcursor: pageParam,\n\t\t\t} as never),\n\t\tgetNextPageParam: (lastPage) => (lastPage.hasNextPage ? (lastPage.nextCursor ?? null) : null),\n\t});\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SuiClient } from '@mysten/sui/client';\nimport { useContext } from 'react';\n\nimport { SuiClientContext } from '../components/SuiClientProvider.js';\n\nexport function useSuiClientContext() {\n\tconst suiClient = useContext(SuiClientContext);\n\n\tif (!suiClient) {\n\t\tthrow new Error(\n\t\t\t'Could not find SuiClientContext. Ensure that you have set up the SuiClientProvider',\n\t\t);\n\t}\n\n\treturn suiClient;\n}\n\nexport function useSuiClient(): SuiClient {\n\treturn useSuiClientContext().client;\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { getFullnodeUrl, isSuiClient, SuiClient } from '@mysten/sui/client';\nimport type { SuiClientOptions } from '@mysten/sui/client';\nimport { createContext, useMemo, useState } from 'react';\n\nimport type { NetworkConfig } from '../hooks/networkConfig.js';\n\ntype NetworkConfigs<T extends NetworkConfig | SuiClient = NetworkConfig | SuiClient> = Record<\n\tstring,\n\tT\n>;\n\nexport interface SuiClientProviderContext {\n\tclient: SuiClient;\n\tnetworks: NetworkConfigs;\n\tnetwork: string;\n\tconfig: NetworkConfig | null;\n\tselectNetwork: (network: string) => void;\n}\n\nexport const SuiClientContext = createContext<SuiClientProviderContext | null>(null);\n\nexport type SuiClientProviderProps<T extends NetworkConfigs> = {\n\tcreateClient?: (name: keyof T, config: T[keyof T]) => SuiClient;\n\tchildren: React.ReactNode;\n\tnetworks?: T;\n\tonNetworkChange?: (network: keyof T & string) => void;\n} & (\n\t| {\n\t\t\tdefaultNetwork?: keyof T & string;\n\t\t\tnetwork?: never;\n\t  }\n\t| {\n\t\t\tdefaultNetwork?: never;\n\t\t\tnetwork?: keyof T & string;\n\t  }\n);\n\nconst DEFAULT_NETWORKS = {\n\tlocalnet: { url: getFullnodeUrl('localnet') },\n};\n\nconst DEFAULT_CREATE_CLIENT = function createClient(\n\t_name: string,\n\tconfig: NetworkConfig | SuiClient,\n) {\n\tif (isSuiClient(config)) {\n\t\treturn config;\n\t}\n\n\treturn new SuiClient(config);\n};\n\nexport function SuiClientProvider<T extends NetworkConfigs>(props: SuiClientProviderProps<T>) {\n\tconst { onNetworkChange, network, children } = props;\n\tconst networks = (props.networks ?? DEFAULT_NETWORKS) as T;\n\tconst createClient =\n\t\t(props.createClient as typeof DEFAULT_CREATE_CLIENT) ?? DEFAULT_CREATE_CLIENT;\n\n\tconst [selectedNetwork, setSelectedNetwork] = useState<keyof T & string>(\n\t\tprops.network ?? props.defaultNetwork ?? (Object.keys(networks)[0] as keyof T & string),\n\t);\n\n\tconst currentNetwork = props.network ?? selectedNetwork;\n\n\tconst client = useMemo(() => {\n\t\treturn createClient(currentNetwork, networks[currentNetwork]);\n\t}, [createClient, currentNetwork, networks]);\n\n\tconst ctx = useMemo((): SuiClientProviderContext => {\n\t\treturn {\n\t\t\tclient,\n\t\t\tnetworks,\n\t\t\tnetwork: currentNetwork,\n\t\t\tconfig:\n\t\t\t\tnetworks[currentNetwork] instanceof SuiClient\n\t\t\t\t\t? null\n\t\t\t\t\t: (networks[currentNetwork] as SuiClientOptions),\n\t\t\tselectNetwork: (newNetwork) => {\n\t\t\t\tif (currentNetwork === newNetwork) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (!network && newNetwork !== selectedNetwork) {\n\t\t\t\t\tsetSelectedNetwork(newNetwork);\n\t\t\t\t}\n\n\t\t\t\tonNetworkChange?.(newNetwork);\n\t\t\t},\n\t\t};\n\t}, [client, networks, selectedNetwork, currentNetwork, network, onNetworkChange]);\n\n\treturn <SuiClientContext.Provider value={ctx}>{children}</SuiClientContext.Provider>;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AASA,yBAAiC;;;ACLjC,IAAAA,gBAA2B;;;ACD3B,oBAAuD;AAEvD,mBAAiD;AAyFzC;AAxED,IAAM,uBAAmB,4BAA+C,IAAI;AAkBnF,IAAM,mBAAmB;AAAA,EACxB,UAAU,EAAE,SAAK,8BAAe,UAAU,EAAE;AAC7C;;;ADlCO,SAAS,sBAAsB;AACrC,QAAM,gBAAY,0BAAW,gBAAgB;AAE7C,MAAI,CAAC,WAAW;AACf,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;;;ADgCO,SAAS,0BAIf,QACA,QACA;AAAA,EACC,WAAW,CAAC;AAAA,EACZ,UAAU,CAAC,CAAC;AAAA,EACZ,GAAG;AACJ,IAAgD,CAAC,GACV;AACvC,QAAM,aAAa,oBAAoB;AAEvC,aAAO,qCAAiB;AAAA,IACvB,GAAG;AAAA,IACH,kBAAkB;AAAA,IAClB,UAAU,CAAC,WAAW,SAAS,QAAQ,QAAQ,GAAG,QAAQ;AAAA,IAC1D;AAAA,IACA,SAAS,CAAC,EAAE,UAAU,MACrB,WAAW,OAAO,MAAM,EAAE;AAAA,MACzB,GAAI,UAAU,CAAC;AAAA,MACf,QAAQ;AAAA,IACT,CAAU;AAAA,IACX,kBAAkB,CAAC,aAAc,SAAS,cAAe,SAAS,cAAc,OAAQ;AAAA,EACzF,CAAC;AACF;", "names": ["import_react"]}