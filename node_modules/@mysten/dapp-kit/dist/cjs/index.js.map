{"version": 3, "sources": ["vanilla-extract-css-ns:src/components/styling/StyleMarker.css.ts.vanilla.css?source=OndoZXJlKCopIHsKICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogIGNvbG9yOiB2YXIoLS1kYXBwLWtpdC1jb2xvcnMtYm9keSk7CiAgZm9udC1mYW1pbHk6IHZhcigtLWRhcHAta2l0LXR5cG9ncmFwaHktZm9udEZhbWlseSk7CiAgZm9udC1zaXplOiB2YXIoLS1kYXBwLWtpdC1mb250V2VpZ2h0cy1ub3JtYWwpOwogIGZvbnQtc3R5bGU6IHZhcigtLWRhcHAta2l0LXR5cG9ncmFwaHktZm9udFN0eWxlKTsKICBmb250LXdlaWdodDogdmFyKC0tZGFwcC1raXQtZm9udFdlaWdodHMtbm9ybWFsKTsKICBsaW5lLWhlaWdodDogdmFyKC0tZGFwcC1raXQtdHlwb2dyYXBoeS1saW5lSGVpZ2h0KTsKICBsZXR0ZXItc3BhY2luZzogdmFyKC0tZGFwcC1raXQtdHlwb2dyYXBoeS1sZXR0ZXJTcGFjaW5nKTsKfQo6d2hlcmUoYnV0dG9uKSB7CiAgYXBwZWFyYW5jZTogbm9uZTsKICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDsKICBmb250LXNpemU6IGluaGVyaXQ7CiAgZm9udC1mYW1pbHk6IGluaGVyaXQ7CiAgbGluZS1oZWlnaHQ6IGluaGVyaXQ7CiAgbGV0dGVyLXNwYWNpbmc6IGluaGVyaXQ7CiAgY29sb3I6IGluaGVyaXQ7CiAgYm9yZGVyOiAwOwogIHBhZGRpbmc6IDA7CiAgbWFyZ2luOiAwOwp9Cjp3aGVyZShhKSB7CiAgdGV4dC1kZWNvcmF0aW9uOiBub25lOwogIGNvbG9yOiBpbmhlcml0OwogIG91dGxpbmU6IG5vbmU7Cn0KOndoZXJlKG9sLCB1bCkgewogIGxpc3Qtc3R5bGU6IG5vbmU7CiAgbWFyZ2luOiAwOwogIHBhZGRpbmc6IDA7Cn0KOndoZXJlKGgxLCBoMiwgaDMsIGg0LCBoNSwgaDYpIHsKICBmb250LXNpemU6IGluaGVyaXQ7CiAgZm9udC13ZWlnaHQ6IGluaGVyaXQ7CiAgbWFyZ2luOiAwOwp9", "../../src/components/styling/StyleMarker.css.ts", "../../src/index.ts", "../../src/components/connect-modal/ConnectModal.tsx", "../../src/constants/walletDefaults.ts", "../../src/utils/stateStorage.ts", "../../src/hooks/wallet/useConnectWallet.ts", "../../src/constants/walletMutationKeys.ts", "../../src/hooks/wallet/useWalletStore.ts", "../../src/contexts/walletContext.ts", "../../src/hooks/wallet/useWallets.ts", "../../src/utils/walletUtils.ts", "../../src/components/icons/BackIcon.tsx", "../../src/components/icons/CloseIcon.tsx", "../../src/components/styling/StyleMarker.tsx", "../../src/constants/styleDataAttribute.ts", "../../src/components/ui/Heading.tsx", "../../src/components/ui/Heading.css.ts", "../../src/components/ui/IconButton.tsx", "../../src/components/ui/IconButton.css.ts", "../../src/components/connect-modal/ConnectModal.css.ts", "../../src/components/ui/Button.tsx", "../../src/components/ui/Button.css.ts", "../../src/components/ui/Text.tsx", "../../src/components/ui/Text.css.ts", "../../src/components/connect-modal/views/ConnectionStatus.css.ts", "../../src/components/connect-modal/views/ConnectionStatus.tsx", "../../src/components/connect-modal/InfoSection.css.ts", "../../src/components/connect-modal/InfoSection.tsx", "../../src/components/connect-modal/views/GettingStarted.css.ts", "../../src/components/connect-modal/views/GettingStarted.tsx", "../../src/components/connect-modal/views/WhatIsAWallet.css.ts", "../../src/components/connect-modal/views/WhatIsAWallet.tsx", "../../src/components/icons/SuiIcon.tsx", "../../src/components/connect-modal/wallet-list/WalletList.css.ts", "../../src/components/connect-modal/wallet-list/WalletListItem.tsx", "../../src/components/connect-modal/wallet-list/WalletListItem.css.ts", "../../src/components/connect-modal/wallet-list/WalletList.tsx", "../../src/hooks/wallet/useCurrentAccount.ts", "../../src/components/AccountDropdownMenu.tsx", "../../src/hooks/useSuiClientQuery.ts", "../../src/hooks/useSuiClient.ts", "../../src/components/SuiClientProvider.tsx", "../../src/hooks/useResolveSuiNSNames.ts", "../../src/hooks/wallet/useAccounts.ts", "../../src/hooks/wallet/useDisconnectWallet.ts", "../../src/errors/walletErrors.ts", "../../src/hooks/wallet/useCurrentWallet.ts", "../../src/hooks/wallet/useSwitchAccount.ts", "../../src/components/AccountDropdownMenu.css.ts", "../../src/components/icons/CheckIcon.tsx", "../../src/components/icons/ChevronIcon.tsx", "../../src/components/ConnectButton.tsx", "../../src/components/WalletProvider.tsx", "../../src/hooks/wallet/useAutoConnectWallet.ts", "../../src/hooks/wallet/useSlushWallet.ts", "../../src/hooks/wallet/useUnsafeBurnerWallet.ts", "../../src/hooks/wallet/useWalletPropertiesChanged.ts", "../../src/hooks/wallet/useWalletsChanged.ts", "../../src/themes/lightTheme.ts", "../../src/walletStore.ts", "../../src/components/styling/InjectedThemeStyles.tsx", "../../src/themes/themeContract.ts", "../../src/hooks/networkConfig.ts", "../../src/hooks/useSuiClientInfiniteQuery.ts", "../../src/hooks/useSuiClientMutation.ts", "../../src/hooks/useSuiClientQueries.ts", "../../src/hooks/wallet/useSignAndExecuteTransaction.ts", "../../src/hooks/wallet/useReportTransactionEffects.ts", "../../src/hooks/wallet/useSignPersonalMessage.ts", "../../src/hooks/wallet/useSignTransaction.ts"], "sourcesContent": ["[data-dapp-kit]:where(*), [data-dapp-kit] :where(*) {\n  box-sizing: border-box;\n  color: var(--dapp-kit-colors-body);\n  font-family: var(--dapp-kit-typography-fontFamily);\n  font-size: var(--dapp-kit-fontWeights-normal);\n  font-style: var(--dapp-kit-typography-fontStyle);\n  font-weight: var(--dapp-kit-fontWeights-normal);\n  line-height: var(--dapp-kit-typography-lineHeight);\n  letter-spacing: var(--dapp-kit-typography-letterSpacing);\n}\n[data-dapp-kit]:where(button), [data-dapp-kit] :where(button) {\n  -webkit-appearance: none;\n     -moz-appearance: none;\n          appearance: none;\n  background-color: transparent;\n  font-size: inherit;\n  font-family: inherit;\n  line-height: inherit;\n  letter-spacing: inherit;\n  color: inherit;\n  border: 0;\n  padding: 0;\n  margin: 0;\n}\n[data-dapp-kit]:where(a), [data-dapp-kit] :where(a) {\n  text-decoration: none;\n  color: inherit;\n  outline: none;\n}\n[data-dapp-kit]:where(ol, ul), [data-dapp-kit] :where(ol, ul) {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n[data-dapp-kit]:where(h1, h2, h3, h4, h5, h6), [data-dapp-kit] :where(h1, h2, h3, h4, h5, h6) {\n  font-size: inherit;\n  font-weight: inherit;\n  margin: 0;\n}", "import 'src/components/styling/StyleMarker.css.ts.vanilla.css?source=OndoZXJlKCopIHsKICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogIGNvbG9yOiB2YXIoLS1kYXBwLWtpdC1jb2xvcnMtYm9keSk7CiAgZm9udC1mYW1pbHk6IHZhcigtLWRhcHAta2l0LXR5cG9ncmFwaHktZm9udEZhbWlseSk7CiAgZm9udC1zaXplOiB2YXIoLS1kYXBwLWtpdC1mb250V2VpZ2h0cy1ub3JtYWwpOwogIGZvbnQtc3R5bGU6IHZhcigtLWRhcHAta2l0LXR5cG9ncmFwaHktZm9udFN0eWxlKTsKICBmb250LXdlaWdodDogdmFyKC0tZGFwcC1raXQtZm9udFdlaWdodHMtbm9ybWFsKTsKICBsaW5lLWhlaWdodDogdmFyKC0tZGFwcC1raXQtdHlwb2dyYXBoeS1saW5lSGVpZ2h0KTsKICBsZXR0ZXItc3BhY2luZzogdmFyKC0tZGFwcC1raXQtdHlwb2dyYXBoeS1sZXR0ZXJTcGFjaW5nKTsKfQo6d2hlcmUoYnV0dG9uKSB7CiAgYXBwZWFyYW5jZTogbm9uZTsKICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDsKICBmb250LXNpemU6IGluaGVyaXQ7CiAgZm9udC1mYW1pbHk6IGluaGVyaXQ7CiAgbGluZS1oZWlnaHQ6IGluaGVyaXQ7CiAgbGV0dGVyLXNwYWNpbmc6IGluaGVyaXQ7CiAgY29sb3I6IGluaGVyaXQ7CiAgYm9yZGVyOiAwOwogIHBhZGRpbmc6IDA7CiAgbWFyZ2luOiAwOwp9Cjp3aGVyZShhKSB7CiAgdGV4dC1kZWNvcmF0aW9uOiBub25lOwogIGNvbG9yOiBpbmhlcml0OwogIG91dGxpbmU6IG5vbmU7Cn0KOndoZXJlKG9sLCB1bCkgewogIGxpc3Qtc3R5bGU6IG5vbmU7CiAgbWFyZ2luOiAwOwogIHBhZGRpbmc6IDA7Cn0KOndoZXJlKGgxLCBoMiwgaDMsIGg0LCBoNSwgaDYpIHsKICBmb250LXNpemU6IGluaGVyaXQ7CiAgZm9udC13ZWlnaHQ6IGluaGVyaXQ7CiAgbWFyZ2luOiAwOwp9';", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nexport * from './components/connect-modal/ConnectModal.js';\nexport * from './components/ConnectButton.js';\nexport * from './components/SuiClientProvider.js';\nexport * from './components/WalletProvider.js';\nexport * from './hooks/networkConfig.js';\nexport * from './hooks/useResolveSuiNSNames.js';\nexport * from './hooks/useSuiClient.js';\nexport * from './hooks/useSuiClientInfiniteQuery.js';\nexport * from './hooks/useSuiClientMutation.js';\nexport * from './hooks/useSuiClientQuery.js';\nexport * from './hooks/useSuiClientQueries.js';\nexport * from './hooks/wallet/useAccounts.js';\nexport * from './hooks/wallet/useAutoConnectWallet.js';\nexport * from './hooks/wallet/useConnectWallet.js';\nexport * from './hooks/wallet/useCurrentAccount.js';\nexport * from './hooks/wallet/useCurrentWallet.js';\nexport * from './hooks/wallet/useDisconnectWallet.js';\nexport * from './hooks/wallet/useSignAndExecuteTransaction.js';\nexport * from './hooks/wallet/useSignPersonalMessage.js';\nexport * from './hooks/wallet/useSignTransaction.js';\nexport * from './hooks/wallet/useReportTransactionEffects.js';\nexport * from './hooks/wallet/useSwitchAccount.js';\nexport * from './hooks/wallet/useWallets.js';\nexport * from './themes/lightTheme.js';\nexport * from './types.js';\n\nexport type { Theme, ThemeVars, DynamicTheme } from './themes/themeContract.js';\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { WalletWithRequiredFeatures } from '@mysten/wallet-standard';\nimport * as Dialog from '@radix-ui/react-dialog';\nimport clsx from 'clsx';\nimport { useState } from 'react';\nimport type { ReactNode } from 'react';\n\nimport { DEFAULT_WALLET_FILTER } from '../../constants/walletDefaults.js';\nimport { useConnectWallet } from '../../hooks/wallet/useConnectWallet.js';\nimport { useWallets } from '../../hooks/wallet/useWallets.js';\nimport { getWalletUniqueIdentifier } from '../../utils/walletUtils.js';\nimport { BackIcon } from '../icons/BackIcon.js';\nimport { CloseIcon } from '../icons/CloseIcon.js';\nimport { StyleMarker } from '../styling/StyleMarker.js';\nimport { Heading } from '../ui/Heading.js';\nimport { IconButton } from '../ui/IconButton.js';\nimport * as styles from './ConnectModal.css.js';\nimport { ConnectionStatus } from './views/ConnectionStatus.js';\nimport { GettingStarted } from './views/GettingStarted.js';\nimport { WhatIsAWallet } from './views/WhatIsAWallet.js';\nimport { WalletList } from './wallet-list/WalletList.js';\n\ntype ConnectModalView = 'getting-started' | 'what-is-a-wallet' | 'connection-status';\n\ntype ControlledModalProps = {\n\t/** The controlled open state of the dialog. */\n\topen: boolean;\n\n\t/** Event handler called when the open state of the dialog changes. */\n\tonOpenChange: (open: boolean) => void;\n\n\tdefaultOpen?: never;\n};\n\ntype UncontrolledModalProps = {\n\topen?: never;\n\n\tonOpenChange?: never;\n\n\t/** The open state of the dialog when it is initially rendered. Use when you do not need to control its open state. */\n\tdefaultOpen?: boolean;\n};\n\ntype ConnectModalProps = {\n\t/** The trigger button that opens the dialog. */\n\ttrigger: NonNullable<ReactNode>;\n\n\t/** Filter the wallets shown in the modal. */\n\twalletFilter?: (wallet: WalletWithRequiredFeatures) => boolean;\n} & (ControlledModalProps | UncontrolledModalProps);\n\nexport function ConnectModal({\n\ttrigger,\n\topen,\n\tdefaultOpen,\n\tonOpenChange,\n\twalletFilter = DEFAULT_WALLET_FILTER,\n}: ConnectModalProps) {\n\tconst [isModalOpen, setModalOpen] = useState(open ?? defaultOpen);\n\tconst [currentView, setCurrentView] = useState<ConnectModalView>();\n\tconst [selectedWallet, setSelectedWallet] = useState<WalletWithRequiredFeatures>();\n\n\tconst wallets = useWallets().filter(walletFilter);\n\tconst { mutate, isError } = useConnectWallet();\n\n\tconst resetSelection = () => {\n\t\tsetSelectedWallet(undefined);\n\t\tsetCurrentView(undefined);\n\t};\n\n\tconst handleOpenChange = (open: boolean) => {\n\t\tif (!open) {\n\t\t\tresetSelection();\n\t\t}\n\t\tsetModalOpen(open);\n\t\tonOpenChange?.(open);\n\t};\n\n\tconst connectWallet = (wallet: WalletWithRequiredFeatures) => {\n\t\tsetCurrentView('connection-status');\n\t\tmutate(\n\t\t\t{ wallet },\n\t\t\t{\n\t\t\t\tonSuccess: () => handleOpenChange(false),\n\t\t\t},\n\t\t);\n\t};\n\n\tlet modalContent: ReactNode | undefined;\n\tswitch (currentView) {\n\t\tcase 'what-is-a-wallet':\n\t\t\tmodalContent = <WhatIsAWallet />;\n\t\t\tbreak;\n\t\tcase 'getting-started':\n\t\t\tmodalContent = <GettingStarted />;\n\t\t\tbreak;\n\t\tcase 'connection-status':\n\t\t\tmodalContent = (\n\t\t\t\t<ConnectionStatus\n\t\t\t\t\tselectedWallet={selectedWallet!}\n\t\t\t\t\thadConnectionError={isError}\n\t\t\t\t\tonRetryConnection={connectWallet}\n\t\t\t\t/>\n\t\t\t);\n\t\t\tbreak;\n\t\tdefault:\n\t\t\tmodalContent = <WhatIsAWallet />;\n\t}\n\n\treturn (\n\t\t<Dialog.Root open={open ?? isModalOpen} onOpenChange={handleOpenChange}>\n\t\t\t<Dialog.Trigger asChild>{trigger}</Dialog.Trigger>\n\t\t\t<Dialog.Portal>\n\t\t\t\t<StyleMarker>\n\t\t\t\t\t<Dialog.Overlay className={styles.overlay}>\n\t\t\t\t\t\t<Dialog.Content className={styles.content} aria-describedby={undefined}>\n\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\tclassName={clsx(styles.walletListContainer, {\n\t\t\t\t\t\t\t\t\t[styles.walletListContainerWithViewSelected]: !!currentView,\n\t\t\t\t\t\t\t\t})}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<div className={styles.walletListContent}>\n\t\t\t\t\t\t\t\t\t<Dialog.Title className={styles.title} asChild>\n\t\t\t\t\t\t\t\t\t\t<Heading as=\"h2\">Connect a Wallet</Heading>\n\t\t\t\t\t\t\t\t\t</Dialog.Title>\n\t\t\t\t\t\t\t\t\t<WalletList\n\t\t\t\t\t\t\t\t\t\twallets={wallets}\n\t\t\t\t\t\t\t\t\t\tselectedWalletName={getWalletUniqueIdentifier(selectedWallet)}\n\t\t\t\t\t\t\t\t\t\tonPlaceholderClick={() => setCurrentView('getting-started')}\n\t\t\t\t\t\t\t\t\t\tonSelect={(wallet) => {\n\t\t\t\t\t\t\t\t\t\t\tif (\n\t\t\t\t\t\t\t\t\t\t\t\tgetWalletUniqueIdentifier(selectedWallet) !==\n\t\t\t\t\t\t\t\t\t\t\t\tgetWalletUniqueIdentifier(wallet)\n\t\t\t\t\t\t\t\t\t\t\t) {\n\t\t\t\t\t\t\t\t\t\t\t\tsetSelectedWallet(wallet);\n\t\t\t\t\t\t\t\t\t\t\t\tconnectWallet(wallet);\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}}\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\tclassName={styles.whatIsAWalletButton}\n\t\t\t\t\t\t\t\t\tonClick={() => setCurrentView('what-is-a-wallet')}\n\t\t\t\t\t\t\t\t\ttype=\"button\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\tWhat is a Wallet?\n\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\tclassName={clsx(styles.viewContainer, {\n\t\t\t\t\t\t\t\t\t[styles.selectedViewContainer]: !!currentView,\n\t\t\t\t\t\t\t\t})}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t<div className={styles.backButtonContainer}>\n\t\t\t\t\t\t\t\t\t<IconButton type=\"button\" aria-label=\"Back\" onClick={() => resetSelection()}>\n\t\t\t\t\t\t\t\t\t\t<BackIcon />\n\t\t\t\t\t\t\t\t\t</IconButton>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t{modalContent}\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<Dialog.Close className={styles.closeButtonContainer} asChild>\n\t\t\t\t\t\t\t\t<IconButton type=\"button\" aria-label=\"Close\">\n\t\t\t\t\t\t\t\t\t<CloseIcon />\n\t\t\t\t\t\t\t\t</IconButton>\n\t\t\t\t\t\t\t</Dialog.Close>\n\t\t\t\t\t\t</Dialog.Content>\n\t\t\t\t\t</Dialog.Overlay>\n\t\t\t\t</StyleMarker>\n\t\t\t</Dialog.Portal>\n\t\t</Dialog.Root>\n\t);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SuiWalletFeatures, WalletWithRequiredFeatures } from '@mysten/wallet-standard';\nimport { SLUSH_WALLET_NAME } from '@mysten/slush-wallet';\n\nimport { createInMemoryStore } from '../utils/stateStorage.js';\n\nexport const SUI_WALLET_NAME = 'Sui Wallet';\n\nexport const DEFAULT_STORAGE =\n\ttypeof window !== 'undefined' && window.localStorage ? localStorage : createInMemoryStore();\n\nexport const DEFAULT_STORAGE_KEY = 'sui-dapp-kit:wallet-connection-info';\n\nconst SIGN_FEATURES = [\n\t'sui:signTransaction',\n\t'sui:signTransactionBlock',\n] satisfies (keyof SuiWalletFeatures)[];\n\nexport const DEFAULT_WALLET_FILTER = (wallet: WalletWithRequiredFeatures) =>\n\tSIGN_FEATURES.some((feature) => wallet.features[feature]);\n\nexport const DEFAULT_PREFERRED_WALLETS = [SUI_WALLET_NAME, SLUSH_WALLET_NAME];\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { StateStorage } from 'zustand/middleware';\n\nexport function createInMemoryStore(): StateStorage {\n\tconst store = new Map();\n\treturn {\n\t\tgetItem(key: string) {\n\t\t\treturn store.get(key);\n\t\t},\n\t\tsetItem(key: string, value: string) {\n\t\t\tstore.set(key, value);\n\t\t},\n\t\tremoveItem(key: string) {\n\t\t\tstore.delete(key);\n\t\t},\n\t};\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type {\n\tStandardConnectInput,\n\tStandardConnectOutput,\n\tWalletAccount,\n\tWalletWithRequiredFeatures,\n} from '@mysten/wallet-standard';\nimport type { UseMutationOptions, UseMutationResult } from '@tanstack/react-query';\nimport { useMutation } from '@tanstack/react-query';\n\nimport { walletMutationKeys } from '../../constants/walletMutationKeys.js';\nimport { useWalletStore } from './useWalletStore.js';\n\ntype ConnectWalletArgs = {\n\t/** The wallet to connect to. */\n\twallet: WalletWithRequiredFeatures;\n\n\t/** An optional account address to connect to. Defaults to the first authorized account. */\n\taccountAddress?: string;\n} & StandardConnectInput;\n\ntype ConnectWalletResult = StandardConnectOutput;\n\ntype UseConnectWalletMutationOptions = Omit<\n\tUseMutationOptions<ConnectWalletResult, Error, ConnectWalletArgs, unknown>,\n\t'mutationFn'\n>;\n\n/**\n * Mutation hook for establishing a connection to a specific wallet.\n */\nexport function useConnectWallet({\n\tmutationKey,\n\t...mutationOptions\n}: UseConnectWalletMutationOptions = {}): UseMutationResult<\n\tConnectWalletResult,\n\tError,\n\tConnectWalletArgs,\n\tunknown\n> {\n\tconst setWalletConnected = useWalletStore((state) => state.setWalletConnected);\n\tconst setConnectionStatus = useWalletStore((state) => state.setConnectionStatus);\n\n\treturn useMutation({\n\t\tmutationKey: walletMutationKeys.connectWallet(mutationKey),\n\t\tmutationFn: async ({ wallet, accountAddress, ...connectArgs }) => {\n\t\t\ttry {\n\t\t\t\tsetConnectionStatus('connecting');\n\n\t\t\t\tconst connectResult = await wallet.features['standard:connect'].connect(connectArgs);\n\t\t\t\tconst connectedSuiAccounts = connectResult.accounts.filter((account) =>\n\t\t\t\t\taccount.chains.some((chain) => chain.split(':')[0] === 'sui'),\n\t\t\t\t);\n\t\t\t\tconst selectedAccount = getSelectedAccount(connectedSuiAccounts, accountAddress);\n\n\t\t\t\tsetWalletConnected(\n\t\t\t\t\twallet,\n\t\t\t\t\tconnectedSuiAccounts,\n\t\t\t\t\tselectedAccount,\n\t\t\t\t\tconnectResult.supportedIntents,\n\t\t\t\t);\n\n\t\t\t\treturn { accounts: connectedSuiAccounts };\n\t\t\t} catch (error) {\n\t\t\t\tsetConnectionStatus('disconnected');\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t},\n\t\t...mutationOptions,\n\t});\n}\n\nfunction getSelectedAccount(connectedAccounts: readonly WalletAccount[], accountAddress?: string) {\n\tif (connectedAccounts.length === 0) {\n\t\treturn null;\n\t}\n\n\tif (accountAddress) {\n\t\tconst selectedAccount = connectedAccounts.find((account) => account.address === accountAddress);\n\t\treturn selectedAccount ?? connectedAccounts[0];\n\t}\n\n\treturn connectedAccounts[0];\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { MutationKey } from '@tanstack/react-query';\n\nexport const walletMutationKeys = {\n\tall: { baseScope: 'wallet' },\n\tconnectWallet: formMutationKeyFn('connect-wallet'),\n\tautoconnectWallet: formMutationKeyFn('autoconnect-wallet'),\n\tdisconnectWallet: formMutationKeyFn('disconnect-wallet'),\n\tsignPersonalMessage: formMutationKeyFn('sign-personal-message'),\n\tsignTransaction: formMutationKeyFn('sign-transaction'),\n\tsignAndExecuteTransaction: formMutationKeyFn('sign-and-execute-transaction'),\n\tswitchAccount: formMutationKeyFn('switch-account'),\n\treportTransactionEffects: formMutationKeyFn('report-transaction-effects'),\n};\n\nfunction formMutationKeyFn(baseEntity: string) {\n\treturn function mutationKeyFn(additionalKeys: MutationKey = []) {\n\t\treturn [{ ...walletMutationKeys.all, baseEntity }, ...additionalKeys];\n\t};\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { useContext } from 'react';\nimport { useStore } from 'zustand';\n\nimport { WalletContext } from '../../contexts/walletContext.js';\nimport type { StoreState } from '../../walletStore.js';\n\nexport function useWalletStore<T>(selector: (state: StoreState) => T): T {\n\tconst store = useContext(WalletContext);\n\tif (!store) {\n\t\tthrow new Error(\n\t\t\t'Could not find WalletContext. Ensure that you have set up the WalletProvider.',\n\t\t);\n\t}\n\treturn useStore(store, selector);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { createContext } from 'react';\n\nimport type { WalletStore } from '../walletStore.js';\n\nexport const WalletContext = createContext<WalletStore | null>(null);\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { useWalletStore } from './useWalletStore.js';\n\n/**\n * Retrieves a list of registered wallets available to the dApp sorted by preference.\n */\nexport function useWallets() {\n\treturn useWalletStore((state) => state.wallets);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type {\n\tMinimallyRequiredFeatures,\n\tWallet,\n\tWalletWithFeatures,\n\tWalletWithRequiredFeatures,\n} from '@mysten/wallet-standard';\nimport { getWallets, isWalletWithRequiredFeatureSet } from '@mysten/wallet-standard';\n\nexport function getRegisteredWallets<AdditionalFeatures extends Wallet['features']>(\n\tpreferredWallets: string[],\n\twalletFilter?: (wallet: WalletWithRequiredFeatures) => boolean,\n) {\n\tconst walletsApi = getWallets();\n\tconst wallets = walletsApi.get();\n\n\tconst suiWallets = wallets.filter(\n\t\t(wallet): wallet is WalletWithFeatures<MinimallyRequiredFeatures & AdditionalFeatures> =>\n\t\t\tisWalletWithRequiredFeatureSet(wallet) && (!walletFilter || walletFilter(wallet)),\n\t);\n\n\treturn [\n\t\t// Preferred wallets, in order:\n\t\t...(preferredWallets\n\t\t\t.map((name) => suiWallets.find((wallet) => wallet.name === name))\n\t\t\t.filter(Boolean) as WalletWithFeatures<MinimallyRequiredFeatures & AdditionalFeatures>[]),\n\n\t\t// Wallets in default order:\n\t\t...suiWallets.filter((wallet) => !preferredWallets.includes(wallet.name)),\n\t];\n}\n\nexport function getWalletUniqueIdentifier(wallet?: Wallet) {\n\treturn wallet?.id ?? wallet?.name;\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { ComponentProps } from 'react';\n\n// FIXME: Replace this with a 10x10 icon to match the CheckIcon, or alternatively make the CheckIcon bigger\n// Right now, the icons don't align on mobile :(\nexport function BackIcon(props: ComponentProps<'svg'>) {\n\treturn (\n\t\t<svg width={24} height={24} fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" {...props}>\n\t\t\t<path\n\t\t\t\td=\"M7.57 12.262c0 .341.13.629.403.895l5.175 5.059c.204.205.45.307.751.307.609 0 1.101-.485 1.101-1.087 0-.293-.123-.574-.349-.8L10.14 12.27l4.511-4.375A1.13 1.13 0 0 0 15 7.087C15 6.485 14.508 6 13.9 6c-.295 0-.54.103-.752.308l-5.175 5.058c-.28.28-.404.56-.404.896Z\"\n\t\t\t\tfill=\"currentColor\"\n\t\t\t/>\n\t\t</svg>\n\t);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { ComponentProps } from 'react';\n\nexport function CloseIcon(props: ComponentProps<'svg'>) {\n\treturn (\n\t\t<svg width={10} height={10} fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" {...props}>\n\t\t\t<path\n\t\t\t\td=\"M9.708.292a.999.999 0 0 0-1.413 0l-3.289 3.29L1.717.291A.999.999 0 0 0 .305 1.705l3.289 3.289-3.29 3.289a.999.999 0 1 0 1.413 1.412l3.29-3.289 3.288 3.29a.999.999 0 0 0 1.413-1.413l-3.29-3.29 3.29-3.288a.999.999 0 0 0 0-1.413Z\"\n\t\t\t\tfill=\"currentColor\"\n\t\t\t/>\n\t\t</svg>\n\t);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Slot } from '@radix-ui/react-slot';\nimport type { ComponentPropsWithoutRef, ElementRef, ReactNode } from 'react';\nimport { forwardRef } from 'react';\n\nimport { styleDataAttribute } from '../../constants/styleDataAttribute.js';\n\nimport './StyleMarker.css.js';\n\ntype StyleMarker = {\n\tchildren: ReactNode;\n};\n\nexport const StyleMarker = forwardRef<\n\tElementRef<typeof Slot>,\n\tComponentPropsWithoutRef<typeof Slot>\n>(({ children, ...props }, forwardedRef) => (\n\t<Slot ref={forwardedRef} {...props} {...styleDataAttribute}>\n\t\t{children}\n\t</Slot>\n));\nStyleMarker.displayName = 'StyleMarker';\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nexport const styleDataAttributeName = 'data-dapp-kit';\n\nexport const styleDataAttributeSelector = `[${styleDataAttributeName}]`;\n\nexport const styleDataAttribute = { [styleDataAttributeName]: '' };\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Slot } from '@radix-ui/react-slot';\nimport clsx from 'clsx';\nimport { forwardRef } from 'react';\n\nimport { headingVariants } from './Heading.css.js';\nimport type { HeadingVariants } from './Heading.css.js';\n\ntype HeadingAsChildProps = {\n\tasChild?: boolean;\n\tas?: never;\n};\n\ntype HeadingAsProps = {\n\tas?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';\n\tasChild?: never;\n};\n\ntype HeadingProps = (HeadingAsChildProps | HeadingAsProps) &\n\tReact.HTMLAttributes<HTMLHeadingElement> &\n\tHeadingVariants;\n\nconst Heading = forwardRef<HTMLHeadingElement, HeadingProps>(\n\t(\n\t\t{\n\t\t\tchildren,\n\t\t\tclassName,\n\t\t\tasChild = false,\n\t\t\tas: Tag = 'h1',\n\t\t\tsize,\n\t\t\tweight,\n\t\t\ttruncate,\n\t\t\t...headingProps\n\t\t},\n\t\tforwardedRef,\n\t) => {\n\t\treturn (\n\t\t\t<Slot\n\t\t\t\t{...headingProps}\n\t\t\t\tref={forwardedRef}\n\t\t\t\tclassName={clsx(headingVariants({ size, weight, truncate }), className)}\n\t\t\t>\n\t\t\t\t{asChild ? children : <Tag>{children}</Tag>}\n\t\t\t</Slot>\n\t\t);\n\t},\n);\nHeading.displayName = 'Heading';\n\nexport { Heading };\n", "import 'src/components/ui/Heading.css.ts.vanilla.css?source=LkhlYWRpbmdfaGVhZGluZ1ZhcmlhbnRzX3NpemVfc21fXzFhYTgzNWsxIHsKICBmb250LXNpemU6IHZhcigtLWRhcHAta2l0LWZvbnRTaXplcy1zbWFsbCk7Cn0KLkhlYWRpbmdfaGVhZGluZ1ZhcmlhbnRzX3NpemVfbWRfXzFhYTgzNWsyIHsKICBmb250LXNpemU6IHZhcigtLWRhcHAta2l0LWZvbnRTaXplcy1tZWRpdW0pOwp9Ci5IZWFkaW5nX2hlYWRpbmdWYXJpYW50c19zaXplX2xnX18xYWE4MzVrMyB7CiAgZm9udC1zaXplOiB2YXIoLS1kYXBwLWtpdC1mb250U2l6ZXMtbGFyZ2UpOwp9Ci5IZWFkaW5nX2hlYWRpbmdWYXJpYW50c19zaXplX3hsX18xYWE4MzVrNCB7CiAgZm9udC1zaXplOiB2YXIoLS1kYXBwLWtpdC1mb250U2l6ZXMteGxhcmdlKTsKfQouSGVhZGluZ19oZWFkaW5nVmFyaWFudHNfd2VpZ2h0X25vcm1hbF9fMWFhODM1azUgewogIGZvbnQtd2VpZ2h0OiB2YXIoLS1kYXBwLWtpdC1mb250V2VpZ2h0cy1ub3JtYWwpOwp9Ci5IZWFkaW5nX2hlYWRpbmdWYXJpYW50c193ZWlnaHRfYm9sZF9fMWFhODM1azYgewogIGZvbnQtd2VpZ2h0OiB2YXIoLS1kYXBwLWtpdC1mb250V2VpZ2h0cy1ib2xkKTsKfQouSGVhZGluZ19oZWFkaW5nVmFyaWFudHNfdHJ1bmNhdGVfdHJ1ZV9fMWFhODM1azcgewogIG92ZXJmbG93OiBoaWRkZW47CiAgdGV4dC1vdmVyZmxvdzogZWxsaXBzaXM7CiAgd2hpdGUtc3BhY2U6IG5vd3JhcDsKfQ==';\nimport { createRuntimeFn as _7a468 } from '@vanilla-extract/recipes/createRuntimeFn';\nexport var headingVariants = _7a468({defaultClassName:'Heading__1aa835k0',variantClassNames:{size:{sm:'Heading_headingVariants_size_sm__1aa835k1',md:'Heading_headingVariants_size_md__1aa835k2',lg:'Heading_headingVariants_size_lg__1aa835k3',xl:'Heading_headingVariants_size_xl__1aa835k4'},weight:{normal:'Heading_headingVariants_weight_normal__1aa835k5',bold:'Heading_headingVariants_weight_bold__1aa835k6'},truncate:{true:'Heading_headingVariants_truncate_true__1aa835k7'}},defaultVariants:{size:'lg',weight:'bold'},compoundVariants:[]});", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Slot } from '@radix-ui/react-slot';\nimport clsx from 'clsx';\nimport type { ButtonHTMLAttributes } from 'react';\nimport { forwardRef } from 'react';\n\nimport * as styles from './IconButton.css.js';\n\ntype IconButtonProps = {\n\tasChild?: boolean;\n\t'aria-label': string;\n} & ButtonHTMLAttributes<HTMLButtonElement>;\n\nconst IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(\n\t({ className, asChild = false, ...props }, forwardedRef) => {\n\t\tconst Comp = asChild ? Slot : 'button';\n\t\treturn <Comp {...props} className={clsx(styles.container, className)} ref={forwardedRef} />;\n\t},\n);\nIconButton.displayName = 'Button';\n\nexport { IconButton };\n", "import 'src/components/ui/IconButton.css.ts.vanilla.css?source=Lkljb25CdXR0b25fY29udGFpbmVyX19zNm43YnEwIHsKICBib3JkZXItcmFkaXVzOiA5OTk5cHg7CiAgcGFkZGluZzogOHB4OwogIGNvbG9yOiB2YXIoLS1kYXBwLWtpdC1jb2xvcnMtaWNvbkJ1dHRvbik7CiAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tZGFwcC1raXQtYmFja2dyb3VuZENvbG9ycy1pY29uQnV0dG9uKTsKfQouSWNvbkJ1dHRvbl9jb250YWluZXJfX3M2bjdicTA6aG92ZXIgewogIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWRhcHAta2l0LWJhY2tncm91bmRDb2xvcnMtaWNvbkJ1dHRvbkhvdmVyKTsKfQ==';\nexport var container = 'IconButton_container__s6n7bq0';", "import 'src/components/connect-modal/ConnectModal.css.ts.vanilla.css?source=#H4sIAAAAAAAAA61UTW8aMRC98yt8iUSkOlrSkJDNpS2nSq1aKVJzjMx6dteN117Zs0BS8d8rfwAh6wAS4QB4vPNm5s17ezHVSkGBPzVn8lHPwUj2/PhYvUxebjPyb0DIjBVPldGd4rTQUpuczJkZUspZ29IngXT7wNTdW9o4rF8B6vwuQnCjW1oKiZBAkF0qrdVWoNAqJ6VYAnchoSxgTjL3/4UKxWGZk9v1526wGlzsDIQCJcRxRn6clnEuVEUllJiTSbvsJxVaISiMaZensPDbiIaZyII2HAw1jIvO9vJdWNClZKYC/3i6TBHAZ5q/y9FMI+omJ6NrNxwhYdL1yYiqfnXkwraSPeeklOAD7pdyYaAIsIWWXaPczd/OoiifaaQnJ7ZlBdAZ4ALAP+HkU0q9yEktOA+xRihaQyg6zua1j7HlJjYZb2MLwbHOyU2WpdayqBl+t18fmJSA3zpEreKKPp+yonsotOLrJUV5rAl628NcwGKqFTKhwMTqV776hkilFewgXWbtltnKOHpGfWQLEgoE/idRYbxbIazqLYCbLrDyNv06CH+jFDazWnbou0TdbjsMSrlMsl9IbSGNf3MIP6m93nr9Xn8Ii9Md/01Sw+/T6S7PhFTMjXgVWuht5WRj75/jNU+36UkO++rYWY9o5UFg7SR2H+UWW2MpDa8GXxrgghFbGABFmOJk6Py89un1pF2e+8zDL9D+FEYv7vxFhBtl2VkIeNGM16egys0RDVO21KbJw1/JEIZ0nJ19Iu7bu3jVa+nQyyNh4D7I++5PrLWffsihR/Ww3yYf9BY8VLav6j3r9RczZoV1Noy225Fu9ipiayPUU4wd1cdeSScXs/oP5e4DaPYIAAA=';\nexport var backButtonContainer = 'ConnectModal_backButtonContainer__gz8z96';\nexport var closeButtonContainer = 'ConnectModal_closeButtonContainer__gz8z97';\nexport var content = 'ConnectModal_content__gz8z92';\nexport var overlay = 'ConnectModal_overlay__gz8z90';\nexport var selectedViewContainer = 'ConnectModal_selectedViewContainer__gz8z95';\nexport var title = 'ConnectModal_title__gz8z91';\nexport var viewContainer = 'ConnectModal_viewContainer__gz8z94';\nexport var walletListContainer = 'ConnectModal_walletListContainer__gz8z99';\nexport var walletListContainerWithViewSelected = 'ConnectModal_walletListContainerWithViewSelected__gz8z9a';\nexport var walletListContent = 'ConnectModal_walletListContent__gz8z98';\nexport var whatIsAWalletButton = 'ConnectModal_whatIsAWalletButton__gz8z93';", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Slot } from '@radix-ui/react-slot';\nimport clsx from 'clsx';\nimport type { ButtonHTMLAttributes } from 'react';\nimport { forwardRef } from 'react';\n\nimport { buttonVariants } from './Button.css.js';\nimport type { ButtonVariants } from './Button.css.js';\n\ntype ButtonProps = {\n\tasChild?: boolean;\n} & ButtonHTMLAttributes<HTMLButtonElement> &\n\tButtonVariants;\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n\t({ className, variant, size, asChild = false, ...props }, forwardedRef) => {\n\t\tconst Comp = asChild ? Slot : 'button';\n\t\treturn (\n\t\t\t<Comp\n\t\t\t\t{...props}\n\t\t\t\tclassName={clsx(buttonVariants({ variant, size }), className)}\n\t\t\t\tref={forwardedRef}\n\t\t\t/>\n\t\t);\n\t},\n);\nButton.displayName = 'Button';\n\nexport { But<PERSON> };\n", "import 'src/components/ui/Button.css.ts.vanilla.css?source=#H4sIAAAAAAAAA62TwVLCMBCG732KPcohDEV0mPSmF59Aj520Ce1KmsQkhVaHd3fS0gGsIoyemtl/d79/N+n0ofZeqzTrPs/MIlPepWkTu2X8NoOPCICjM5K1FFBJVIKspGiSCIBJLBRBLypHIRfKCxvCr7XzuGpJrpUXyh9LK6082QosSk9hw+wNIZwZQ9boSdBeOsmRSnCsq0kS7aLpeYOUo2OZFLxzqg3L0bcUZtO7M8Wb/pAaixWz7dAs7npkLF8XVteKk1xLbUdGDwmPQXdk36ZnTcKc3xfmP6VnuiGuZFxvRzV9eFx09Wy01Bth/2PCp9DoEgu69uG9DBbmPVxbLizZIvclhdg0ySHofCsFBacl8qPwDyY7cW9wj7r0Cr6mXz3Kn7Z5Qv91mw7fRVrxAX17vEXLONZuRAxhPPxEAIZxjqqgsDQNxPdh6ed5shh4i4t5ktlCnOICCuYL00AS7T4BZ4uE22sEAAA=';\nimport { createRuntimeFn as _7a468 } from '@vanilla-extract/recipes/createRuntimeFn';\nexport var buttonVariants = _7a468({defaultClassName:'Button_buttonVariants__x1s81q0',variantClassNames:{variant:{primary:'Button_buttonVariants_variant_primary__x1s81q1',outline:'Button_buttonVariants_variant_outline__x1s81q2'},size:{md:'Button_buttonVariants_size_md__x1s81q3',lg:'Button_buttonVariants_size_lg__x1s81q4'}},defaultVariants:{variant:'primary',size:'md'},compoundVariants:[]});", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Slot } from '@radix-ui/react-slot';\nimport clsx from 'clsx';\nimport { forwardRef } from 'react';\n\nimport { textVariants } from './Text.css.js';\nimport type { TextVariants } from './Text.css.js';\n\ntype TextAsChildProps = {\n\tasChild?: boolean;\n\tas?: never;\n};\n\ntype TextDivProps = { as: 'div'; asChild?: never };\n\ntype TextProps = (TextAsChildProps | TextDivProps) &\n\tReact.HTMLAttributes<HTMLDivElement> &\n\tTextVariants;\n\nconst Text = forwardRef<HTMLDivElement, TextProps>(\n\t(\n\t\t{\n\t\t\tchildren,\n\t\t\tclassName,\n\t\t\tasChild = false,\n\t\t\tas: Tag = 'div',\n\t\t\tsize,\n\t\t\tweight,\n\t\t\tcolor,\n\t\t\tmono,\n\t\t\t...textProps\n\t\t},\n\t\tforwardedRef,\n\t) => {\n\t\treturn (\n\t\t\t<Slot\n\t\t\t\t{...textProps}\n\t\t\t\tref={forwardedRef}\n\t\t\t\tclassName={clsx(textVariants({ size, weight, color, mono }), className)}\n\t\t\t>\n\t\t\t\t{asChild ? children : <Tag>{children}</Tag>}\n\t\t\t</Slot>\n\t\t);\n\t},\n);\nText.displayName = 'Text';\n\nexport { Text };\n", "import 'src/components/ui/Text.css.ts.vanilla.css?source=LlRleHRfdGV4dFZhcmlhbnRzX3NpemVfc21fXzJidjF1cjEgewogIGZvbnQtc2l6ZTogdmFyKC0tZGFwcC1raXQtZm9udFNpemVzLXNtYWxsKTsKfQouVGV4dF90ZXh0VmFyaWFudHNfd2VpZ2h0X25vcm1hbF9fMmJ2MXVyMiB7CiAgZm9udC13ZWlnaHQ6IHZhcigtLWRhcHAta2l0LWZvbnRXZWlnaHRzLW5vcm1hbCk7Cn0KLlRleHRfdGV4dFZhcmlhbnRzX3dlaWdodF9tZWRpdW1fXzJidjF1cjMgewogIGZvbnQtd2VpZ2h0OiB2YXIoLS1kYXBwLWtpdC1mb250V2VpZ2h0cy1tZWRpdW0pOwp9Ci5UZXh0X3RleHRWYXJpYW50c193ZWlnaHRfYm9sZF9fMmJ2MXVyNCB7CiAgZm9udC13ZWlnaHQ6IHZhcigtLWRhcHAta2l0LWZvbnRXZWlnaHRzLWJvbGQpOwp9Ci5UZXh0X3RleHRWYXJpYW50c19jb2xvcl9tdXRlZF9fMmJ2MXVyNSB7CiAgY29sb3I6IHZhcigtLWRhcHAta2l0LWNvbG9ycy1ib2R5TXV0ZWQpOwp9Ci5UZXh0X3RleHRWYXJpYW50c19jb2xvcl9kYW5nZXJfXzJidjF1cjYgewogIGNvbG9yOiB2YXIoLS1kYXBwLWtpdC1jb2xvcnMtYm9keURhbmdlcik7Cn0KLlRleHRfdGV4dFZhcmlhbnRzX21vbm9fdHJ1ZV9fMmJ2MXVyNyB7CiAgZm9udC1mYW1pbHk6IHVpLW1vbm9zcGFjZSwgU0ZNb25vLVJlZ3VsYXIsIE1lbmxvLCBNb25hY28sIENvbnNvbGFzLCAiTGliZXJhdGlvbiBNb25vIiwgIkNvdXJpZXIgTmV3IiwgbW9ub3NwYWNlOwp9';\nimport { createRuntimeFn as _7a468 } from '@vanilla-extract/recipes/createRuntimeFn';\nexport var textVariants = _7a468({defaultClassName:'Text__2bv1ur0',variantClassNames:{size:{sm:'Text_textVariants_size_sm__2bv1ur1'},weight:{normal:'Text_textVariants_weight_normal__2bv1ur2',medium:'Text_textVariants_weight_medium__2bv1ur3',bold:'Text_textVariants_weight_bold__2bv1ur4'},color:{muted:'Text_textVariants_color_muted__2bv1ur5',danger:'Text_textVariants_color_danger__2bv1ur6'},mono:{true:'Text_textVariants_mono_true__2bv1ur7'}},defaultVariants:{size:'sm',weight:'normal'},compoundVariants:[]});", "import 'src/components/connect-modal/views/ConnectionStatus.css.ts.vanilla.css?source=LkNvbm5lY3Rpb25TdGF0dXNfY29udGFpbmVyX19uY2ttMmQwIHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICB3aWR0aDogMTAwJTsKfQouQ29ubmVjdGlvblN0YXR1c193YWxsZXRJY29uX19uY2ttMmQxIHsKICBvYmplY3QtZml0OiBjb3ZlcjsKICB3aWR0aDogNzJweDsKICBoZWlnaHQ6IDcycHg7CiAgYm9yZGVyLXJhZGl1czogdmFyKC0tZGFwcC1raXQtcmFkaWktbGFyZ2UpOwp9Ci5Db25uZWN0aW9uU3RhdHVzX3RpdGxlX19uY2ttMmQyIHsKICBtYXJnaW4tdG9wOiAxMnB4Owp9Ci5Db25uZWN0aW9uU3RhdHVzX2Nvbm5lY3Rpb25TdGF0dXNfX25ja20yZDMgewogIG1hcmdpbi10b3A6IDRweDsKfQouQ29ubmVjdGlvblN0YXR1c19yZXRyeUJ1dHRvbkNvbnRhaW5lcl9fbmNrbTJkNCB7CiAgcG9zaXRpb246IGFic29sdXRlOwogIGJvdHRvbTogMjBweDsKICByaWdodDogMjBweDsKfQ==';\nexport var connectionStatus = 'ConnectionStatus_connectionStatus__nckm2d3';\nexport var container = 'ConnectionStatus_container__nckm2d0';\nexport var retryButtonContainer = 'ConnectionStatus_retryButtonContainer__nckm2d4';\nexport var title = 'ConnectionStatus_title__nckm2d2';\nexport var walletIcon = 'ConnectionStatus_walletIcon__nckm2d1';", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { WalletWithRequiredFeatures } from '@mysten/wallet-standard';\n\nimport { Button } from '../../ui/Button.js';\nimport { Heading } from '../../ui/Heading.js';\nimport { Text } from '../../ui/Text.js';\nimport * as styles from './ConnectionStatus.css.js';\n\ntype ConnectionStatusProps = {\n\tselectedWallet: WalletWithRequiredFeatures;\n\thadConnectionError: boolean;\n\tonRetryConnection: (selectedWallet: WalletWithRequiredFeatures) => void;\n};\n\nexport function ConnectionStatus({\n\tselectedWallet,\n\thadConnectionError,\n\tonRetryConnection,\n}: ConnectionStatusProps) {\n\treturn (\n\t\t<div className={styles.container}>\n\t\t\t{selectedWallet.icon && (\n\t\t\t\t<img\n\t\t\t\t\tclassName={styles.walletIcon}\n\t\t\t\t\tsrc={selectedWallet.icon}\n\t\t\t\t\talt={`${selectedWallet.name} logo`}\n\t\t\t\t/>\n\t\t\t)}\n\t\t\t<div className={styles.title}>\n\t\t\t\t<Heading as=\"h2\" size=\"xl\">\n\t\t\t\t\tOpening {selectedWallet.name}\n\t\t\t\t</Heading>\n\t\t\t</div>\n\t\t\t<div className={styles.connectionStatus}>\n\t\t\t\t{hadConnectionError ? (\n\t\t\t\t\t<Text color=\"danger\">Connection failed</Text>\n\t\t\t\t) : (\n\t\t\t\t\t<Text color=\"muted\">Confirm connection in the wallet...</Text>\n\t\t\t\t)}\n\t\t\t</div>\n\t\t\t{hadConnectionError ? (\n\t\t\t\t<div className={styles.retryButtonContainer}>\n\t\t\t\t\t<Button type=\"button\" variant=\"outline\" onClick={() => onRetryConnection(selectedWallet)}>\n\t\t\t\t\t\tRetry Connection\n\t\t\t\t\t</Button>\n\t\t\t\t</div>\n\t\t\t) : null}\n\t\t</div>\n\t);\n}\n", "import 'src/components/connect-modal/InfoSection.css.ts.vanilla.css?source=LkluZm9TZWN0aW9uX2NvbnRhaW5lcl9fMXd0aW9pNzAgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBnYXA6IDRweDsKfQ==';\nexport var container = 'InfoSection_container__1wtioi70';", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Heading } from '../ui/Heading.js';\nimport { Text } from '../ui/Text.js';\nimport * as styles from './InfoSection.css.js';\n\ntype InfoSectionProps = {\n\ttitle: string;\n\tchildren: string;\n};\n\nexport function InfoSection({ title, children }: InfoSectionProps) {\n\treturn (\n\t\t<section className={styles.container}>\n\t\t\t<Heading as=\"h3\" size=\"sm\" weight=\"normal\">\n\t\t\t\t{title}\n\t\t\t</Heading>\n\t\t\t<Text weight=\"medium\" color=\"muted\">\n\t\t\t\t{children}\n\t\t\t</Text>\n\t\t</section>\n\t);\n}\n", "import 'src/components/connect-modal/views/GettingStarted.css.ts.vanilla.css?source=LkdldHRpbmdTdGFydGVkX2NvbnRhaW5lcl9fMWZwMDdlMTAgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwp9Ci5HZXR0aW5nU3RhcnRlZF9jb250ZW50X18xZnAwN2UxMSB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGp1c3RpZnktY29udGVudDogY2VudGVyOwogIGZsZXgtZ3JvdzogMTsKICBnYXA6IDIwcHg7CiAgcGFkZGluZzogNDBweDsKfQouR2V0dGluZ1N0YXJ0ZWRfaW5zdGFsbEJ1dHRvbkNvbnRhaW5lcl9fMWZwMDdlMTIgewogIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICBib3R0b206IDIwcHg7CiAgcmlnaHQ6IDIwcHg7Cn0=';\nexport var container = 'GettingStarted_container__1fp07e10';\nexport var content = 'GettingStarted_content__1fp07e11';\nexport var installButtonContainer = 'GettingStarted_installButtonContainer__1fp07e12';", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Button } from '../../ui/Button.js';\nimport { Heading } from '../../ui/Heading.js';\nimport { InfoSection } from '../InfoSection.js';\nimport * as styles from './GettingStarted.css.js';\n\nexport function GettingStarted() {\n\treturn (\n\t\t<div className={styles.container}>\n\t\t\t<Heading as=\"h2\">Get Started with Sui</Heading>\n\t\t\t<div className={styles.content}>\n\t\t\t\t<InfoSection title=\"Install the Sui Wallet Extension\">\n\t\t\t\t\tWe recommend pinning Sui Wallet to your taskbar for quicker access.\n\t\t\t\t</InfoSection>\n\t\t\t\t<InfoSection title=\"Create or Import a Wallet\">\n\t\t\t\t\tBe sure to back up your wallet using a secure method. Never share your secret phrase with\n\t\t\t\t\tanyone.\n\t\t\t\t</InfoSection>\n\t\t\t\t<InfoSection title=\"Refresh Your Browser\">\n\t\t\t\t\tOnce you set up your wallet, refresh this window browser to load up the extension.\n\t\t\t\t</InfoSection>\n\t\t\t\t<div className={styles.installButtonContainer}>\n\t\t\t\t\t<Button variant=\"outline\" asChild>\n\t\t\t\t\t\t<a\n\t\t\t\t\t\t\thref=\"https://chrome.google.com/webstore/detail/sui-wallet/opcgpfmipidbgpenhmajoajpbobppdil\"\n\t\t\t\t\t\t\ttarget=\"_blank\"\n\t\t\t\t\t\t\trel=\"noreferrer\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\tInstall Wallet Extension\n\t\t\t\t\t\t</a>\n\t\t\t\t\t</Button>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n", "import 'src/components/connect-modal/views/WhatIsAWallet.css.ts.vanilla.css?source=LldoYXRJc0FXYWxsZXRfY29udGFpbmVyX18xa3Rwa3E5MCB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KLldoYXRJc0FXYWxsZXRfY29udGVudF9fMWt0cGtxOTEgewogIGRpc3BsYXk6IGZsZXg7CiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsKICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsKICBmbGV4LWdyb3c6IDE7CiAgZ2FwOiAyMHB4OwogIHBhZGRpbmc6IDQwcHg7Cn0=';\nexport var container = 'WhatIsAWallet_container__1ktpkq90';\nexport var content = 'WhatIsAWallet_content__1ktpkq91';", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Heading } from '../../ui/Heading.js';\nimport { InfoSection } from '../InfoSection.js';\nimport * as styles from './WhatIsAWallet.css.js';\n\nexport function WhatIsAWallet() {\n\treturn (\n\t\t<div className={styles.container}>\n\t\t\t<Heading as=\"h2\">What is a Wallet</Heading>\n\t\t\t<div className={styles.content}>\n\t\t\t\t<InfoSection title=\"Easy Login\">\n\t\t\t\t\tNo need to create new accounts and passwords for every website. Just connect your wallet\n\t\t\t\t\tand get going.\n\t\t\t\t</InfoSection>\n\t\t\t\t<InfoSection title=\"Store your Digital Assets\">\n\t\t\t\t\tSend, receive, store, and display your digital assets like NFTs & coins.\n\t\t\t\t</InfoSection>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { ComponentProps } from 'react';\n\nexport function SuiIcon(props: ComponentProps<'svg'>) {\n\treturn (\n\t\t<svg width={28} height={28} fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\" {...props}>\n\t\t\t<rect width={28} height={28} rx={6} fill=\"#6FBCF0\" />\n\t\t\t<path\n\t\t\t\tfillRule=\"evenodd\"\n\t\t\t\tclipRule=\"evenodd\"\n\t\t\t\td=\"M7.942 20.527A6.875 6.875 0 0 0 13.957 24c2.51 0 4.759-1.298 6.015-3.473a6.875 6.875 0 0 0 0-6.945l-5.29-9.164a.837.837 0 0 0-1.45 0l-5.29 9.164a6.875 6.875 0 0 0 0 6.945Zm4.524-11.75 1.128-1.953a.418.418 0 0 1 .725 0l4.34 7.516a5.365 5.365 0 0 1 .449 4.442 4.675 4.675 0 0 0-.223-.73c-.599-1.512-1.954-2.68-4.029-3.47-1.426-.54-2.336-1.336-2.706-2.364-.476-1.326.021-2.77.316-3.44Zm-1.923 3.332L9.255 14.34a5.373 5.373 0 0 0 0 5.43 5.373 5.373 0 0 0 4.702 2.714 5.38 5.38 0 0 0 3.472-1.247c.125-.314.51-1.462.034-2.646-.44-1.093-1.5-1.965-3.15-2.594-1.864-.707-3.076-1.811-3.6-3.28a4.601 4.601 0 0 1-.17-.608Z\"\n\t\t\t\tfill=\"#fff\"\n\t\t\t/>\n\t\t</svg>\n\t);\n}\n", "import 'src/components/connect-modal/wallet-list/WalletList.css.ts.vanilla.css?source=LldhbGxldExpc3RfY29udGFpbmVyX18xdjJzNmN6MCB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGdhcDogNHB4Owp9';\nexport var container = 'WalletList_container__1v2s6cz0';", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { clsx } from 'clsx';\nimport type { ReactNode } from 'react';\n\nimport { Heading } from '../../ui/Heading.js';\nimport * as styles from './WalletListItem.css.js';\n\ntype WalletListItemProps = {\n\tname: string;\n\ticon: ReactNode;\n\tisSelected?: boolean;\n\tonClick: () => void;\n};\n\nexport function WalletListItem({ name, icon, onClick, isSelected = false }: WalletListItemProps) {\n\treturn (\n\t\t<li className={styles.container}>\n\t\t\t<button\n\t\t\t\tclassName={clsx(styles.walletItem, { [styles.selectedWalletItem]: isSelected })}\n\t\t\t\ttype=\"button\"\n\t\t\t\tonClick={onClick}\n\t\t\t>\n\t\t\t\t{icon && typeof icon === 'string' ? (\n\t\t\t\t\t<img className={styles.walletIcon} src={icon} alt={`${name} logo`} />\n\t\t\t\t) : (\n\t\t\t\t\ticon\n\t\t\t\t)}\n\t\t\t\t<Heading size=\"md\" truncate asChild>\n\t\t\t\t\t<div>{name}</div>\n\t\t\t\t</Heading>\n\t\t\t</button>\n\t\t</li>\n\t);\n}\n", "import 'src/components/connect-modal/wallet-list/WalletListItem.css.ts.vanilla.css?source=LldhbGxldExpc3RJdGVtX2NvbnRhaW5lcl9fMWRxcXRxczAgewogIGRpc3BsYXk6IGZsZXg7Cn0KLldhbGxldExpc3RJdGVtX3dhbGxldEl0ZW1fXzFkcXF0cXMxIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgZmxleC1ncm93OiAxOwogIHBhZGRpbmc6IDhweDsKICBnYXA6IDhweDsKICBib3JkZXItcmFkaXVzOiB2YXIoLS1kYXBwLWtpdC1yYWRpaS1sYXJnZSk7Cn0KLldhbGxldExpc3RJdGVtX3dhbGxldEl0ZW1fXzFkcXF0cXMxOmhvdmVyIHsKICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1kYXBwLWtpdC1iYWNrZ3JvdW5kQ29sb3JzLXdhbGxldEl0ZW1Ib3Zlcik7Cn0KLldhbGxldExpc3RJdGVtX3NlbGVjdGVkV2FsbGV0SXRlbV9fMWRxcXRxczIgewogIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWRhcHAta2l0LWJhY2tncm91bmRDb2xvcnMtd2FsbGV0SXRlbVNlbGVjdGVkKTsKICBib3gtc2hhZG93OiAwcHggMnB4IDZweCByZ2JhKDAsIDAsIDAsIDAuMDUpOwp9Ci5XYWxsZXRMaXN0SXRlbV93YWxsZXRJY29uX18xZHFxdHFzMyB7CiAgd2lkdGg6IDI4cHg7CiAgaGVpZ2h0OiAyOHB4OwogIGZsZXgtc2hyaW5rOiAwOwogIG9iamVjdC1maXQ6IGNvdmVyOwogIGJvcmRlci1yYWRpdXM6IHZhcigtLWRhcHAta2l0LXJhZGlpLXNtYWxsKTsKfQ==';\nexport var container = 'WalletListItem_container__1dqqtqs0';\nexport var selectedWalletItem = 'WalletListItem_selectedWalletItem__1dqqtqs2';\nexport var walletIcon = 'WalletListItem_walletIcon__1dqqtqs3';\nexport var walletItem = 'WalletListItem_walletItem__1dqqtqs1';", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { WalletWithRequiredFeatures } from '@mysten/wallet-standard';\n\nimport { getWalletUniqueIdentifier } from '../../../utils/walletUtils.js';\nimport { SuiIcon } from '../../icons/SuiIcon.js';\nimport * as styles from './WalletList.css.js';\nimport { WalletListItem } from './WalletListItem.js';\n\ntype WalletListProps = {\n\tselectedWalletName?: string;\n\tonPlaceholderClick: () => void;\n\tonSelect: (wallet: WalletWithRequiredFeatures) => void;\n\twallets: WalletWithRequiredFeatures[];\n};\n\nexport function WalletList({\n\tselectedWalletName,\n\tonPlaceholderClick,\n\tonSelect,\n\twallets,\n}: WalletListProps) {\n\treturn (\n\t\t<ul className={styles.container}>\n\t\t\t{wallets.length > 0 ? (\n\t\t\t\twallets.map((wallet) => (\n\t\t\t\t\t<WalletListItem\n\t\t\t\t\t\tkey={getWalletUniqueIdentifier(wallet)}\n\t\t\t\t\t\tname={wallet.name}\n\t\t\t\t\t\ticon={wallet.icon}\n\t\t\t\t\t\tisSelected={getWalletUniqueIdentifier(wallet) === selectedWalletName}\n\t\t\t\t\t\tonClick={() => onSelect(wallet)}\n\t\t\t\t\t/>\n\t\t\t\t))\n\t\t\t) : (\n\t\t\t\t<WalletListItem\n\t\t\t\t\tname=\"Sui Wallet\"\n\t\t\t\t\ticon={<SuiIcon />}\n\t\t\t\t\tonClick={onPlaceholderClick}\n\t\t\t\t\tisSelected\n\t\t\t\t/>\n\t\t\t)}\n\t\t</ul>\n\t);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { WalletAccount } from '@mysten/wallet-standard';\n\nimport { useWalletStore } from './useWalletStore.js';\n\n/**\n * Retrieves the wallet account that is currently selected, if one exists.\n */\nexport function useCurrentAccount(): WalletAccount | null {\n\treturn useWalletStore((state) => state.currentAccount);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { formatAddress } from '@mysten/sui/utils';\nimport type { WalletAccount } from '@mysten/wallet-standard';\nimport * as DropdownMenu from '@radix-ui/react-dropdown-menu';\nimport clsx from 'clsx';\n\nimport { useResolveSuiNSName } from '../hooks/useResolveSuiNSNames.js';\nimport { useAccounts } from '../hooks/wallet/useAccounts.js';\nimport { useDisconnectWallet } from '../hooks/wallet/useDisconnectWallet.js';\nimport { useSwitchAccount } from '../hooks/wallet/useSwitchAccount.js';\nimport * as styles from './AccountDropdownMenu.css.js';\nimport { CheckIcon } from './icons/CheckIcon.js';\nimport { ChevronIcon } from './icons/ChevronIcon.js';\nimport { StyleMarker } from './styling/StyleMarker.js';\nimport { Button } from './ui/Button.js';\nimport { Text } from './ui/Text.js';\n\ntype AccountDropdownMenuProps = {\n\tcurrentAccount: WalletAccount;\n};\n\nexport function AccountDropdownMenu({ currentAccount }: AccountDropdownMenuProps) {\n\tconst { mutate: disconnectWallet } = useDisconnectWallet();\n\n\tconst { data: domain } = useResolveSuiNSName(\n\t\tcurrentAccount.label ? null : currentAccount.address,\n\t);\n\tconst accounts = useAccounts();\n\n\treturn (\n\t\t<DropdownMenu.Root modal={false}>\n\t\t\t<StyleMarker>\n\t\t\t\t<DropdownMenu.Trigger asChild>\n\t\t\t\t\t<Button size=\"lg\" className={styles.connectedAccount}>\n\t\t\t\t\t\t<Text mono weight=\"bold\">\n\t\t\t\t\t\t\t{currentAccount.label ?? domain ?? formatAddress(currentAccount.address)}\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<ChevronIcon />\n\t\t\t\t\t</Button>\n\t\t\t\t</DropdownMenu.Trigger>\n\t\t\t</StyleMarker>\n\t\t\t<DropdownMenu.Portal>\n\t\t\t\t<StyleMarker className={styles.menuContainer}>\n\t\t\t\t\t<DropdownMenu.Content className={styles.menuContent}>\n\t\t\t\t\t\t{accounts.map((account) => (\n\t\t\t\t\t\t\t<AccountDropdownMenuItem\n\t\t\t\t\t\t\t\tkey={account.address}\n\t\t\t\t\t\t\t\taccount={account}\n\t\t\t\t\t\t\t\tactive={currentAccount.address === account.address}\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t))}\n\t\t\t\t\t\t<DropdownMenu.Separator className={styles.separator} />\n\t\t\t\t\t\t<DropdownMenu.Item\n\t\t\t\t\t\t\tclassName={clsx(styles.menuItem)}\n\t\t\t\t\t\t\tonSelect={() => disconnectWallet()}\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\tDisconnect\n\t\t\t\t\t\t</DropdownMenu.Item>\n\t\t\t\t\t</DropdownMenu.Content>\n\t\t\t\t</StyleMarker>\n\t\t\t</DropdownMenu.Portal>\n\t\t</DropdownMenu.Root>\n\t);\n}\n\nexport function AccountDropdownMenuItem({\n\taccount,\n\tactive,\n}: {\n\taccount: WalletAccount;\n\tactive?: boolean;\n}) {\n\tconst { mutate: switchAccount } = useSwitchAccount();\n\tconst { data: domain } = useResolveSuiNSName(account.label ? null : account.address);\n\n\treturn (\n\t\t<DropdownMenu.Item\n\t\t\tclassName={clsx(styles.menuItem, styles.switchAccountMenuItem)}\n\t\t\tonSelect={() => switchAccount({ account })}\n\t\t>\n\t\t\t<Text mono>{account.label ?? domain ?? formatAddress(account.address)}</Text>\n\t\t\t{active ? <CheckIcon /> : null}\n\t\t</DropdownMenu.Item>\n\t);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SuiClient } from '@mysten/sui/client';\nimport type {\n\tUndefinedInitialDataOptions,\n\tUseQueryOptions,\n\tUseQueryResult,\n} from '@tanstack/react-query';\nimport { queryOptions, useQuery, useSuspenseQuery } from '@tanstack/react-query';\nimport { useMemo } from 'react';\n\nimport type { PartialBy } from '../types/utilityTypes.js';\nimport { useSuiClientContext } from './useSuiClient.js';\n\nexport type SuiRpcMethodName = {\n\t[K in keyof SuiClient]: SuiClient[K] extends ((input: any) => Promise<any>) | (() => Promise<any>)\n\t\t? K\n\t\t: never;\n}[keyof SuiClient];\n\nexport type SuiRpcMethods = {\n\t[K in SuiRpcMethodName]: SuiClient[K] extends (input: infer P) => Promise<infer R>\n\t\t? {\n\t\t\t\tname: K;\n\t\t\t\tresult: R;\n\t\t\t\tparams: P;\n\t\t\t}\n\t\t: SuiClient[K] extends () => Promise<infer R>\n\t\t\t? {\n\t\t\t\t\tname: K;\n\t\t\t\t\tresult: R;\n\t\t\t\t\tparams: undefined | object;\n\t\t\t\t}\n\t\t\t: never;\n};\n\nexport type UseSuiClientQueryOptions<T extends keyof SuiRpcMethods, TData> = PartialBy<\n\tOmit<UseQueryOptions<SuiRpcMethods[T]['result'], Error, TData, unknown[]>, 'queryFn'>,\n\t'queryKey'\n>;\n\nexport type GetSuiClientQueryOptions<T extends keyof SuiRpcMethods> = {\n\tclient: SuiClient;\n\tnetwork: string;\n\tmethod: T;\n\toptions?: PartialBy<\n\t\tOmit<UndefinedInitialDataOptions<SuiRpcMethods[T]['result']>, 'queryFn'>,\n\t\t'queryKey'\n\t>;\n} & (undefined extends SuiRpcMethods[T]['params']\n\t? { params?: SuiRpcMethods[T]['params'] }\n\t: { params: SuiRpcMethods[T]['params'] });\n\nexport function getSuiClientQuery<T extends keyof SuiRpcMethods>({\n\tclient,\n\tnetwork,\n\tmethod,\n\tparams,\n\toptions,\n}: GetSuiClientQueryOptions<T>) {\n\treturn queryOptions<SuiRpcMethods[T]['result']>({\n\t\t...options,\n\t\tqueryKey: [network, method, params],\n\t\tqueryFn: async () => {\n\t\t\treturn await client[method](params as never);\n\t\t},\n\t});\n}\n\nexport function useSuiClientQuery<\n\tT extends keyof SuiRpcMethods,\n\tTData = SuiRpcMethods[T]['result'],\n>(\n\t...args: undefined extends SuiRpcMethods[T]['params']\n\t\t? [method: T, params?: SuiRpcMethods[T]['params'], options?: UseSuiClientQueryOptions<T, TData>]\n\t\t: [method: T, params: SuiRpcMethods[T]['params'], options?: UseSuiClientQueryOptions<T, TData>]\n): UseQueryResult<TData, Error> {\n\tconst [method, params, { queryKey = [], ...options } = {}] = args as [\n\t\tmethod: T,\n\t\tparams?: SuiRpcMethods[T]['params'],\n\t\toptions?: UseSuiClientQueryOptions<T, TData>,\n\t];\n\n\tconst suiContext = useSuiClientContext();\n\n\treturn useQuery({\n\t\t...options,\n\t\tqueryKey: [suiContext.network, method, params, ...queryKey],\n\t\tqueryFn: async () => {\n\t\t\treturn await suiContext.client[method](params as never);\n\t\t},\n\t});\n}\n\nexport function useSuiClientSuspenseQuery<\n\tT extends keyof SuiRpcMethods,\n\tTData = SuiRpcMethods[T]['result'],\n>(\n\t...args: undefined extends SuiRpcMethods[T]['params']\n\t\t? [method: T, params?: SuiRpcMethods[T]['params'], options?: UndefinedInitialDataOptions<TData>]\n\t\t: [method: T, params: SuiRpcMethods[T]['params'], options?: UndefinedInitialDataOptions<TData>]\n) {\n\tconst [method, params, options = {}] = args as [\n\t\tmethod: T,\n\t\tparams?: SuiRpcMethods[T]['params'],\n\t\toptions?: UndefinedInitialDataOptions<TData>,\n\t];\n\n\tconst suiContext = useSuiClientContext();\n\n\tconst query = useMemo(() => {\n\t\treturn getSuiClientQuery<T>({\n\t\t\tclient: suiContext.client,\n\t\t\tnetwork: suiContext.network,\n\t\t\tmethod,\n\t\t\tparams,\n\t\t\toptions,\n\t\t});\n\t}, [suiContext.client, suiContext.network, method, params, options]);\n\n\treturn useSuspenseQuery(query);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SuiClient } from '@mysten/sui/client';\nimport { useContext } from 'react';\n\nimport { SuiClientContext } from '../components/SuiClientProvider.js';\n\nexport function useSuiClientContext() {\n\tconst suiClient = useContext(SuiClientContext);\n\n\tif (!suiClient) {\n\t\tthrow new Error(\n\t\t\t'Could not find SuiClientContext. Ensure that you have set up the SuiClientProvider',\n\t\t);\n\t}\n\n\treturn suiClient;\n}\n\nexport function useSuiClient(): SuiClient {\n\treturn useSuiClientContext().client;\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { getFullnodeUrl, isSuiClient, SuiClient } from '@mysten/sui/client';\nimport type { SuiClientOptions } from '@mysten/sui/client';\nimport { createContext, useMemo, useState } from 'react';\n\nimport type { NetworkConfig } from '../hooks/networkConfig.js';\n\ntype NetworkConfigs<T extends NetworkConfig | SuiClient = NetworkConfig | SuiClient> = Record<\n\tstring,\n\tT\n>;\n\nexport interface SuiClientProviderContext {\n\tclient: SuiClient;\n\tnetworks: NetworkConfigs;\n\tnetwork: string;\n\tconfig: NetworkConfig | null;\n\tselectNetwork: (network: string) => void;\n}\n\nexport const SuiClientContext = createContext<SuiClientProviderContext | null>(null);\n\nexport type SuiClientProviderProps<T extends NetworkConfigs> = {\n\tcreateClient?: (name: keyof T, config: T[keyof T]) => SuiClient;\n\tchildren: React.ReactNode;\n\tnetworks?: T;\n\tonNetworkChange?: (network: keyof T & string) => void;\n} & (\n\t| {\n\t\t\tdefaultNetwork?: keyof T & string;\n\t\t\tnetwork?: never;\n\t  }\n\t| {\n\t\t\tdefaultNetwork?: never;\n\t\t\tnetwork?: keyof T & string;\n\t  }\n);\n\nconst DEFAULT_NETWORKS = {\n\tlocalnet: { url: getFullnodeUrl('localnet') },\n};\n\nconst DEFAULT_CREATE_CLIENT = function createClient(\n\t_name: string,\n\tconfig: NetworkConfig | SuiClient,\n) {\n\tif (isSuiClient(config)) {\n\t\treturn config;\n\t}\n\n\treturn new SuiClient(config);\n};\n\nexport function SuiClientProvider<T extends NetworkConfigs>(props: SuiClientProviderProps<T>) {\n\tconst { onNetworkChange, network, children } = props;\n\tconst networks = (props.networks ?? DEFAULT_NETWORKS) as T;\n\tconst createClient =\n\t\t(props.createClient as typeof DEFAULT_CREATE_CLIENT) ?? DEFAULT_CREATE_CLIENT;\n\n\tconst [selectedNetwork, setSelectedNetwork] = useState<keyof T & string>(\n\t\tprops.network ?? props.defaultNetwork ?? (Object.keys(networks)[0] as keyof T & string),\n\t);\n\n\tconst currentNetwork = props.network ?? selectedNetwork;\n\n\tconst client = useMemo(() => {\n\t\treturn createClient(currentNetwork, networks[currentNetwork]);\n\t}, [createClient, currentNetwork, networks]);\n\n\tconst ctx = useMemo((): SuiClientProviderContext => {\n\t\treturn {\n\t\t\tclient,\n\t\t\tnetworks,\n\t\t\tnetwork: currentNetwork,\n\t\t\tconfig:\n\t\t\t\tnetworks[currentNetwork] instanceof SuiClient\n\t\t\t\t\t? null\n\t\t\t\t\t: (networks[currentNetwork] as SuiClientOptions),\n\t\t\tselectNetwork: (newNetwork) => {\n\t\t\t\tif (currentNetwork === newNetwork) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (!network && newNetwork !== selectedNetwork) {\n\t\t\t\t\tsetSelectedNetwork(newNetwork);\n\t\t\t\t}\n\n\t\t\t\tonNetworkChange?.(newNetwork);\n\t\t\t},\n\t\t};\n\t}, [client, networks, selectedNetwork, currentNetwork, network, onNetworkChange]);\n\n\treturn <SuiClientContext.Provider value={ctx}>{children}</SuiClientContext.Provider>;\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { ResolvedNameServiceNames } from '@mysten/sui/client';\nimport type { UseQueryOptions, UseQueryResult } from '@tanstack/react-query';\n\nimport { useSuiClientQuery } from './useSuiClientQuery.js';\n\nexport function useResolveSuiNSName(\n\taddress?: string | null,\n\toptions?: Omit<\n\t\tUseQueryOptions<ResolvedNameServiceNames, Error, string | null, unknown[]>,\n\t\t'queryFn' | 'queryKey' | 'select'\n\t>,\n): UseQueryResult<string | null, Error> {\n\treturn useSuiClientQuery(\n\t\t'resolveNameServiceNames',\n\t\t{\n\t\t\taddress: address!,\n\t\t\tlimit: 1,\n\t\t},\n\t\t{\n\t\t\t...options,\n\t\t\trefetchOnWindowFocus: false,\n\t\t\tretry: false,\n\t\t\tselect: (data) => (data.data.length > 0 ? data.data[0] : null),\n\t\t\tenabled: !!address && options?.enabled !== false,\n\t\t},\n\t);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { WalletAccount } from '@mysten/wallet-standard';\n\nimport { useWalletStore } from './useWalletStore.js';\n\n/**\n * Retrieves a list of connected accounts authorized by the dApp.\n */\nexport function useAccounts(): readonly WalletAccount[] {\n\treturn useWalletStore((state) => state.accounts);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { UseMutationOptions, UseMutationResult } from '@tanstack/react-query';\nimport { useMutation } from '@tanstack/react-query';\n\nimport { walletMutationKeys } from '../../constants/walletMutationKeys.js';\nimport { WalletNotConnectedError } from '../../errors/walletErrors.js';\nimport { useCurrentWallet } from './useCurrentWallet.js';\nimport { useWalletStore } from './useWalletStore.js';\n\ntype UseDisconnectWalletError = WalletNotConnectedError | Error;\n\ntype UseDisconnectWalletMutationOptions = Omit<\n\tUseMutationOptions<void, UseDisconnectWalletError, void, unknown>,\n\t'mutationFn'\n>;\n\n/**\n * Mutation hook for disconnecting from an active wallet connection, if currently connected.\n */\nexport function useDisconnectWallet({\n\tmutationKey,\n\t...mutationOptions\n}: UseDisconnectWalletMutationOptions = {}): UseMutationResult<\n\tvoid,\n\tUseDisconnectWalletError,\n\tvoid\n> {\n\tconst { currentWallet } = useCurrentWallet();\n\tconst setWalletDisconnected = useWalletStore((state) => state.setWalletDisconnected);\n\n\treturn useMutation({\n\t\tmutationKey: walletMutationKeys.disconnectWallet(mutationKey),\n\t\tmutationFn: async () => {\n\t\t\tif (!currentWallet) {\n\t\t\t\tthrow new WalletNotConnectedError('No wallet is connected.');\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\t// Wallets aren't required to implement the disconnect feature, so we'll\n\t\t\t\t// optionally call the disconnect feature if it exists and reset the UI\n\t\t\t\t// state on the frontend at a minimum.\n\t\t\t\tawait currentWallet.features['standard:disconnect']?.disconnect();\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('Failed to disconnect the application from the current wallet.', error);\n\t\t\t}\n\n\t\t\tsetWalletDisconnected();\n\t\t},\n\t\t...mutationOptions,\n\t});\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\n/**\n * An error that is instantiated when someone attempts to perform an action that requires an active wallet connection.\n */\nexport class WalletNotConnectedError extends Error {}\n\n/**\n * An error that is instantiated when someone attempts to perform an action that requires a selected wallet account.\n * This is more of an edge case stemming from the fact that wallets don't technically require you to authorize any\n * accounts when connecting a wallet.\n */\nexport class WalletNoAccountSelectedError extends Error {}\n\n/**\n * An error that is instantiated when someone attempts to perform an action that isn't supported by a wallet.\n */\nexport class WalletFeatureNotSupportedError extends Error {}\n\n/**\n * An error that is instantiated when a wallet account can't be found for a specific wallet.\n */\nexport class WalletAccountNotFoundError extends Error {}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { useWalletStore } from './useWalletStore.js';\n\n/**\n * Retrieves the wallet that is currently connected to the dApp, if one exists.\n */\nexport function useCurrentWallet() {\n\tconst currentWallet = useWalletStore((state) => state.currentWallet);\n\tconst connectionStatus = useWalletStore((state) => state.connectionStatus);\n\tconst supportedIntents = useWalletStore((state) => state.supportedIntents);\n\n\tswitch (connectionStatus) {\n\t\tcase 'connecting':\n\t\t\treturn {\n\t\t\t\tconnectionStatus,\n\t\t\t\tcurrentWallet: null,\n\t\t\t\tisDisconnected: false,\n\t\t\t\tisConnecting: true,\n\t\t\t\tisConnected: false,\n\t\t\t\tsupportedIntents: [],\n\t\t\t} as const;\n\t\tcase 'disconnected':\n\t\t\treturn {\n\t\t\t\tconnectionStatus,\n\t\t\t\tcurrentWallet: null,\n\t\t\t\tisDisconnected: true,\n\t\t\t\tisConnecting: false,\n\t\t\t\tisConnected: false,\n\t\t\t\tsupportedIntents: [],\n\t\t\t} as const;\n\t\tcase 'connected': {\n\t\t\treturn {\n\t\t\t\tconnectionStatus,\n\t\t\t\tcurrentWallet: currentWallet!,\n\t\t\t\tisDisconnected: false,\n\t\t\t\tisConnecting: false,\n\t\t\t\tisConnected: true,\n\t\t\t\tsupportedIntents,\n\t\t\t} as const;\n\t\t}\n\t}\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { WalletAccount } from '@mysten/wallet-standard';\nimport type { UseMutationOptions, UseMutationResult } from '@tanstack/react-query';\nimport { useMutation } from '@tanstack/react-query';\n\nimport { walletMutationKeys } from '../../constants/walletMutationKeys.js';\nimport { WalletAccountNotFoundError, WalletNotConnectedError } from '../../errors/walletErrors.js';\nimport { useCurrentWallet } from './useCurrentWallet.js';\nimport { useWalletStore } from './useWalletStore.js';\n\ntype SwitchAccountArgs = {\n\taccount: WalletAccount;\n};\n\ntype SwitchAccountResult = void;\n\ntype UseSwitchAccountError = WalletNotConnectedError | WalletAccountNotFoundError | Error;\n\ntype UseSwitchAccountMutationOptions = Omit<\n\tUseMutationOptions<SwitchAccountResult, UseSwitchAccountError, SwitchAccountArgs, unknown>,\n\t'mutationFn'\n>;\n\n/**\n * Mutation hook for switching to a specific wallet account.\n */\nexport function useSwitchAccount({\n\tmutationKey,\n\t...mutationOptions\n}: UseSwitchAccountMutationOptions = {}): UseMutationResult<\n\tSwitchAccountResult,\n\tUseSwitchAccountError,\n\tSwitchAccountArgs\n> {\n\tconst { currentWallet } = useCurrentWallet();\n\tconst setAccountSwitched = useWalletStore((state) => state.setAccountSwitched);\n\n\treturn useMutation({\n\t\tmutationKey: walletMutationKeys.switchAccount(mutationKey),\n\t\tmutationFn: async ({ account }) => {\n\t\t\tif (!currentWallet) {\n\t\t\t\tthrow new WalletNotConnectedError('No wallet is connected.');\n\t\t\t}\n\n\t\t\tconst accountToSelect = currentWallet.accounts.find(\n\t\t\t\t(walletAccount) => walletAccount.address === account.address,\n\t\t\t);\n\t\t\tif (!accountToSelect) {\n\t\t\t\tthrow new WalletAccountNotFoundError(\n\t\t\t\t\t`No account with address ${account.address} is connected to ${currentWallet.name}.`,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tsetAccountSwitched(accountToSelect);\n\t\t},\n\t\t...mutationOptions,\n\t});\n}\n", "import 'src/components/AccountDropdownMenu.css.ts.vanilla.css?source=#H4sIAAAAAAAAA6VTTW8TMRC951fMkR5cJaFIxT1BuXDoiSNC0dSe7g7xjo09bhIQ/x2tm22qtqFU+GLpzdeb5+fTD87FKvopx+TjRq5I6spFEXJKfh9crTzfLn+EOfyaAXSYLJyn7cXs9+z0ufKBpF5GUWShPNUuWu1Pw+Jpa+H9dF7sQof5y9bDc0kBdxZuAm0vZtBu4zmTU45iwcVQBxkjG/baW1icz1PLHHBreuKuVwvL+T2YOxajMVk4u4MSes/S7dd8uDLAdcyessnouRYLt5jfGOMxJbNmbTCbgLmjk5aNbt3lWMUbF0PMTwoOCZdjvBj/QISTv6rzWWmYpHnbpHnMuxbKplAgpxYkCo1grBpY6AA8ERQDd2JYaSgWHIlSfu3m/0r7q0dF03PXh/FZyH9ri7xetpR5wLz7WFWjHCdQNqyu30euHrE5e95f32tRvtkZd+dGCyWhI3NNuiGSo4IdY0AJM2q8/xnv2tTJlot08HTpM8vawvz/nfRlmjpK8wcGoA4K9QMAAA==';\nexport var connectedAccount = 'AccountDropdownMenu_connectedAccount__div2ql0';\nexport var menuContainer = 'AccountDropdownMenu_menuContainer__div2ql1';\nexport var menuContent = 'AccountDropdownMenu_menuContent__div2ql2';\nexport var menuItem = 'AccountDropdownMenu_menuItem__div2ql3';\nexport var separator = 'AccountDropdownMenu_separator__div2ql5';\nexport var switchAccountMenuItem = 'AccountDropdownMenu_switchAccountMenuItem__div2ql4';", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { ComponentProps } from 'react';\n\nexport function CheckIcon(props: ComponentProps<'svg'>) {\n\treturn (\n\t\t<svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"none\" {...props}>\n\t\t\t<path\n\t\t\t\tfill=\"currentColor\"\n\t\t\t\td=\"m11.726 5.048-4.73 5.156-1.722-1.879a.72.72 0 0 0-.529-.23.722.722 0 0 0-.525.24.858.858 0 0 0-.22.573.86.86 0 0 0 .211.576l2.255 2.458c.14.153.332.24.53.24.2 0 .391-.087.532-.24l5.261-5.735A.86.86 0 0 0 13 5.63a.858.858 0 0 0-.22-.572.722.722 0 0 0-.525-.24.72.72 0 0 0-.529.23Z\"\n\t\t\t/>\n\t\t</svg>\n\t);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { ComponentProps } from 'react';\n\nexport function ChevronIcon(props: ComponentProps<'svg'>) {\n\treturn (\n\t\t<svg xmlns=\"http://www.w3.org/2000/svg\" width={16} height={16} fill=\"none\" {...props}>\n\t\t\t<path\n\t\t\t\tstroke=\"#A0B6C3\"\n\t\t\t\tstrokeLinecap=\"round\"\n\t\t\t\tstrokeLinejoin=\"round\"\n\t\t\t\tstrokeWidth={1.5}\n\t\t\t\td=\"m4 6 4 4 4-4\"\n\t\t\t/>\n\t\t</svg>\n\t);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { WalletWithRequiredFeatures } from '@mysten/wallet-standard';\nimport type { ButtonHTMLAttributes, ReactNode } from 'react';\n\nimport { useCurrentAccount } from '../hooks/wallet/useCurrentAccount.js';\nimport { AccountDropdownMenu } from './AccountDropdownMenu.js';\nimport { ConnectModal } from './connect-modal/ConnectModal.js';\nimport { StyleMarker } from './styling/StyleMarker.js';\nimport { Button } from './ui/Button.js';\n\ntype ConnectButtonProps = {\n\tconnectText?: ReactNode;\n\t/** Filter the wallets shown in the connect modal */\n\twalletFilter?: (wallet: WalletWithRequiredFeatures) => boolean;\n} & ButtonHTMLAttributes<HTMLButtonElement>;\n\nexport function ConnectButton({\n\tconnectText = 'Connect Wallet',\n\twalletFilter,\n\t...buttonProps\n}: ConnectButtonProps) {\n\tconst currentAccount = useCurrentAccount();\n\treturn currentAccount ? (\n\t\t<AccountDropdownMenu currentAccount={currentAccount} />\n\t) : (\n\t\t<ConnectModal\n\t\t\twalletFilter={walletFilter}\n\t\t\ttrigger={\n\t\t\t\t<StyleMarker>\n\t\t\t\t\t<Button {...buttonProps}>{connectText}</Button>\n\t\t\t\t</StyleMarker>\n\t\t\t}\n\t\t/>\n\t);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { WalletWithFeatures, WalletWithRequiredFeatures } from '@mysten/wallet-standard';\nimport type { ReactNode } from 'react';\nimport { useRef } from 'react';\nimport type { StateStorage } from 'zustand/middleware';\n\nimport {\n\tDEFAULT_PREFERRED_WALLETS,\n\tDEFAULT_STORAGE,\n\tDEFAULT_STORAGE_KEY,\n\tDEFAULT_WALLET_FILTER,\n} from '../constants/walletDefaults.js';\nimport { WalletContext } from '../contexts/walletContext.js';\nimport { useAutoConnectWallet } from '../hooks/wallet/useAutoConnectWallet.js';\nimport type { SlushWalletConfig } from '../hooks/wallet/useSlushWallet.js';\nimport { useSlushWallet } from '../hooks/wallet/useSlushWallet.js';\nimport { useUnsafeBurnerWallet } from '../hooks/wallet/useUnsafeBurnerWallet.js';\nimport { useWalletPropertiesChanged } from '../hooks/wallet/useWalletPropertiesChanged.js';\nimport { useWalletsChanged } from '../hooks/wallet/useWalletsChanged.js';\nimport { lightTheme } from '../themes/lightTheme.js';\nimport type { Theme } from '../themes/themeContract.js';\nimport { createInMemoryStore } from '../utils/stateStorage.js';\nimport { getRegisteredWallets } from '../utils/walletUtils.js';\nimport { createWalletStore } from '../walletStore.js';\nimport { InjectedThemeStyles } from './styling/InjectedThemeStyles.js';\n\nexport type WalletProviderProps = {\n\t/** A list of wallets that are sorted to the top of the wallet list, if they are available to connect to. By default, wallets are sorted by the order they are loaded in. */\n\tpreferredWallets?: string[];\n\n\t/** A filter function to select wallets that support features required for the dApp to function. This filters the list of wallets presented to users when selecting a wallet to connect from, ensuring that only wallets that meet the dApps requirements can connect. */\n\twalletFilter?: (wallet: WalletWithRequiredFeatures) => boolean;\n\n\t/** Enables the development-only unsafe burner wallet, which can be useful for testing. */\n\tenableUnsafeBurner?: boolean;\n\n\t/** Enables automatically reconnecting to the most recently used wallet account upon mounting. */\n\tautoConnect?: boolean;\n\n\t/** Enables the Slush wallet */\n\tslushWallet?: SlushWalletConfig;\n\n\t/** Configures how the most recently connected to wallet account is stored. Set to `null` to disable persisting state entirely. Defaults to using localStorage if it is available. */\n\tstorage?: StateStorage | null;\n\n\t/** The key to use to store the most recently connected wallet account. */\n\tstorageKey?: string;\n\n\t/** The theme to use for styling UI components. Defaults to using the light theme. */\n\ttheme?: Theme | null;\n\n\tchildren: ReactNode;\n};\n\nexport type { WalletWithFeatures };\n\nexport function WalletProvider({\n\tpreferredWallets = DEFAULT_PREFERRED_WALLETS,\n\twalletFilter = DEFAULT_WALLET_FILTER,\n\tstorage = DEFAULT_STORAGE,\n\tstorageKey = DEFAULT_STORAGE_KEY,\n\tenableUnsafeBurner = false,\n\tautoConnect = false,\n\tslushWallet,\n\ttheme = lightTheme,\n\tchildren,\n}: WalletProviderProps) {\n\tconst storeRef = useRef(\n\t\tcreateWalletStore({\n\t\t\tautoConnectEnabled: autoConnect,\n\t\t\twallets: getRegisteredWallets(preferredWallets, walletFilter),\n\t\t\tstorage: storage || createInMemoryStore(),\n\t\t\tstorageKey,\n\t\t}),\n\t);\n\n\treturn (\n\t\t<WalletContext.Provider value={storeRef.current}>\n\t\t\t<WalletConnectionManager\n\t\t\t\tpreferredWallets={preferredWallets}\n\t\t\t\twalletFilter={walletFilter}\n\t\t\t\tenableUnsafeBurner={enableUnsafeBurner}\n\t\t\t\tslushWallet={slushWallet}\n\t\t\t>\n\t\t\t\t{/* TODO: We ideally don't want to inject styles if people aren't using the UI components */}\n\t\t\t\t{theme ? <InjectedThemeStyles theme={theme} /> : null}\n\t\t\t\t{children}\n\t\t\t</WalletConnectionManager>\n\t\t</WalletContext.Provider>\n\t);\n}\n\ntype WalletConnectionManagerProps = Pick<\n\tWalletProviderProps,\n\t'preferredWallets' | 'walletFilter' | 'enableUnsafeBurner' | 'slushWallet' | 'children'\n>;\n\nfunction WalletConnectionManager({\n\tpreferredWallets = DEFAULT_PREFERRED_WALLETS,\n\twalletFilter = DEFAULT_WALLET_FILTER,\n\tenableUnsafeBurner = false,\n\tslushWallet,\n\tchildren,\n}: WalletConnectionManagerProps) {\n\tuseWalletsChanged(preferredWallets, walletFilter);\n\tuseWalletPropertiesChanged();\n\tuseSlushWallet(slushWallet);\n\tuseUnsafeBurnerWallet(enableUnsafeBurner);\n\tuseAutoConnectWallet();\n\n\treturn children;\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { useQuery } from '@tanstack/react-query';\nimport { useLayoutEffect, useState } from 'react';\n\nimport { getWalletUniqueIdentifier } from '../../utils/walletUtils.js';\nimport { useConnectWallet } from './useConnectWallet.js';\nimport { useCurrentWallet } from './useCurrentWallet.js';\nimport { useWallets } from './useWallets.js';\nimport { useWalletStore } from './useWalletStore.js';\n\nexport function useAutoConnectWallet(): 'disabled' | 'idle' | 'attempted' {\n\tconst { mutateAsync: connectWallet } = useConnectWallet();\n\tconst autoConnectEnabled = useWalletStore((state) => state.autoConnectEnabled);\n\tconst lastConnectedWalletName = useWalletStore((state) => state.lastConnectedWalletName);\n\tconst lastConnectedAccountAddress = useWalletStore((state) => state.lastConnectedAccountAddress);\n\tconst wallets = useWallets();\n\tconst { isConnected } = useCurrentWallet();\n\n\tconst [clientOnly, setClientOnly] = useState(false);\n\tuseLayoutEffect(() => {\n\t\tsetClientOnly(true);\n\t}, []);\n\n\tconst { data, isError } = useQuery({\n\t\tqueryKey: [\n\t\t\t'@mysten/dapp-kit',\n\t\t\t'autoconnect',\n\t\t\t{\n\t\t\t\tisConnected,\n\t\t\t\tautoConnectEnabled,\n\t\t\t\tlastConnectedWalletName,\n\t\t\t\tlastConnectedAccountAddress,\n\t\t\t\twalletCount: wallets.length,\n\t\t\t},\n\t\t],\n\t\tqueryFn: async () => {\n\t\t\tif (!autoConnectEnabled) {\n\t\t\t\treturn 'disabled';\n\t\t\t}\n\n\t\t\tif (!lastConnectedWalletName || !lastConnectedAccountAddress || isConnected) {\n\t\t\t\treturn 'attempted';\n\t\t\t}\n\n\t\t\tconst wallet = wallets.find(\n\t\t\t\t(wallet) => getWalletUniqueIdentifier(wallet) === lastConnectedWalletName,\n\t\t\t);\n\t\t\tif (wallet) {\n\t\t\t\tawait connectWallet({\n\t\t\t\t\twallet,\n\t\t\t\t\taccountAddress: lastConnectedAccountAddress,\n\t\t\t\t\tsilent: true,\n\t\t\t\t});\n\t\t\t}\n\n\t\t\treturn 'attempted';\n\t\t},\n\t\tenabled: autoConnectEnabled,\n\t\tpersister: undefined,\n\t\tgcTime: 0,\n\t\tstaleTime: 0,\n\t\tnetworkMode: 'always',\n\t\tretry: false,\n\t\tretryOnMount: false,\n\t\trefetchInterval: false,\n\t\trefetchIntervalInBackground: false,\n\t\trefetchOnMount: false,\n\t\trefetchOnReconnect: false,\n\t\trefetchOnWindowFocus: false,\n\t});\n\n\tif (!autoConnectEnabled) {\n\t\treturn 'disabled';\n\t}\n\n\t// We always initialize with \"idle\" so that in SSR environments, we guarantee that the initial render states always agree:\n\tif (!clientOnly) {\n\t\treturn 'idle';\n\t}\n\n\tif (isConnected) {\n\t\treturn 'attempted';\n\t}\n\n\tif (!lastConnectedWalletName) {\n\t\treturn 'attempted';\n\t}\n\n\treturn isError ? 'attempted' : (data ?? 'idle');\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { registerSlushWallet } from '@mysten/slush-wallet';\nimport { useLayoutEffect } from 'react';\n\nexport interface SlushWalletConfig {\n\tname: string;\n\torigin?: string;\n}\n\nexport function useSlushWallet(config?: SlushWalletConfig) {\n\tuseLayoutEffect(() => {\n\t\tif (!config?.name) {\n\t\t\treturn;\n\t\t}\n\n\t\tlet cleanup: (() => void) | undefined;\n\t\tlet isMounted = true;\n\n\t\ttry {\n\t\t\tconst result = registerSlushWallet(config.name, {\n\t\t\t\torigin: config.origin,\n\t\t\t});\n\n\t\t\tif (isMounted && result) {\n\t\t\t\tcleanup = result.unregister;\n\t\t\t} else if (result) {\n\t\t\t\tresult.unregister();\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error('Failed to register Slush wallet:', error);\n\t\t}\n\n\t\treturn () => {\n\t\t\tisMounted = false;\n\t\t\tif (cleanup) cleanup();\n\t\t};\n\t}, [config?.name, config?.origin]);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SuiClient } from '@mysten/sui/client';\nimport { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519';\nimport { Transaction } from '@mysten/sui/transactions';\nimport { toBase64 } from '@mysten/sui/utils';\nimport type {\n\tStandardConnectFeature,\n\tStandardConnectMethod,\n\tStandardEventsFeature,\n\tStandardEventsOnMethod,\n\tSuiFeatures,\n\tSuiSignAndExecuteTransactionBlockMethod,\n\tSuiSignAndExecuteTransactionMethod,\n\tSuiSignPersonalMessageMethod,\n\tSuiSignTransactionBlockMethod,\n\tSuiSignTransactionMethod,\n\tWallet,\n} from '@mysten/wallet-standard';\nimport { getWallets, ReadonlyWalletAccount, SUI_CHAINS } from '@mysten/wallet-standard';\nimport { useEffect } from 'react';\n\nimport { useSuiClient } from '../useSuiClient.js';\n\nconst WALLET_NAME = 'Unsafe Burner Wallet';\n\nexport function useUnsafeBurnerWallet(enabled: boolean) {\n\tconst suiClient = useSuiClient();\n\n\tuseEffect(() => {\n\t\tif (!enabled) {\n\t\t\treturn;\n\t\t}\n\t\tconst unregister = registerUnsafeBurnerWallet(suiClient);\n\t\treturn unregister;\n\t}, [enabled, suiClient]);\n}\n\nfunction registerUnsafeBurnerWallet(suiClient: SuiClient) {\n\tconst walletsApi = getWallets();\n\tconst registeredWallets = walletsApi.get();\n\n\tif (registeredWallets.find((wallet) => wallet.name === WALLET_NAME)) {\n\t\tconsole.warn(\n\t\t\t'registerUnsafeBurnerWallet: Unsafe Burner Wallet already registered, skipping duplicate registration.',\n\t\t);\n\t\treturn;\n\t}\n\n\tconsole.warn(\n\t\t'Your application is currently using the unsafe burner wallet. Make sure that this wallet is disabled in production.',\n\t);\n\n\tconst keypair = new Ed25519Keypair();\n\tconst account = new ReadonlyWalletAccount({\n\t\taddress: keypair.getPublicKey().toSuiAddress(),\n\t\tpublicKey: keypair.getPublicKey().toSuiBytes(),\n\t\tchains: ['sui:unknown'],\n\t\tfeatures: [\n\t\t\t'sui:signAndExecuteTransactionBlock',\n\t\t\t'sui:signTransactionBlock',\n\t\t\t'sui:signTransaction',\n\t\t\t'sui:signAndExecuteTransaction',\n\t\t],\n\t});\n\n\tclass UnsafeBurnerWallet implements Wallet {\n\t\tget version() {\n\t\t\treturn '1.0.0' as const;\n\t\t}\n\n\t\tget name() {\n\t\t\treturn WALLET_NAME;\n\t\t}\n\n\t\tget icon() {\n\t\t\treturn 'data:image/png;base64,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' as const;\n\t\t}\n\n\t\t// Return the Sui chains that your wallet supports.\n\t\tget chains() {\n\t\t\treturn SUI_CHAINS;\n\t\t}\n\n\t\tget accounts() {\n\t\t\treturn [account];\n\t\t}\n\n\t\tget features(): StandardConnectFeature & StandardEventsFeature & SuiFeatures {\n\t\t\treturn {\n\t\t\t\t'standard:connect': {\n\t\t\t\t\tversion: '1.0.0',\n\t\t\t\t\tconnect: this.#connect,\n\t\t\t\t},\n\t\t\t\t'standard:events': {\n\t\t\t\t\tversion: '1.0.0',\n\t\t\t\t\ton: this.#on,\n\t\t\t\t},\n\t\t\t\t'sui:signPersonalMessage': {\n\t\t\t\t\tversion: '1.1.0',\n\t\t\t\t\tsignPersonalMessage: this.#signPersonalMessage,\n\t\t\t\t},\n\t\t\t\t'sui:signTransactionBlock': {\n\t\t\t\t\tversion: '1.0.0',\n\t\t\t\t\tsignTransactionBlock: this.#signTransactionBlock,\n\t\t\t\t},\n\t\t\t\t'sui:signAndExecuteTransactionBlock': {\n\t\t\t\t\tversion: '1.0.0',\n\t\t\t\t\tsignAndExecuteTransactionBlock: this.#signAndExecuteTransactionBlock,\n\t\t\t\t},\n\t\t\t\t'sui:signTransaction': {\n\t\t\t\t\tversion: '2.0.0',\n\t\t\t\t\tsignTransaction: this.#signTransaction,\n\t\t\t\t},\n\t\t\t\t'sui:signAndExecuteTransaction': {\n\t\t\t\t\tversion: '2.0.0',\n\t\t\t\t\tsignAndExecuteTransaction: this.#signAndExecuteTransaction,\n\t\t\t\t},\n\t\t\t};\n\t\t}\n\n\t\t#on: StandardEventsOnMethod = () => {\n\t\t\treturn () => {};\n\t\t};\n\n\t\t#connect: StandardConnectMethod = async () => {\n\t\t\treturn { accounts: this.accounts };\n\t\t};\n\n\t\t#signPersonalMessage: SuiSignPersonalMessageMethod = async (messageInput) => {\n\t\t\tconst { bytes, signature } = await keypair.signPersonalMessage(messageInput.message);\n\t\t\treturn { bytes, signature };\n\t\t};\n\n\t\t#signTransactionBlock: SuiSignTransactionBlockMethod = async (transactionInput) => {\n\t\t\tconst { bytes, signature } = await transactionInput.transactionBlock.sign({\n\t\t\t\tclient: suiClient,\n\t\t\t\tsigner: keypair,\n\t\t\t});\n\n\t\t\treturn {\n\t\t\t\ttransactionBlockBytes: bytes,\n\t\t\t\tsignature: signature,\n\t\t\t};\n\t\t};\n\n\t\t#signTransaction: SuiSignTransactionMethod = async (transactionInput) => {\n\t\t\tconst { bytes, signature } = await Transaction.from(\n\t\t\t\tawait transactionInput.transaction.toJSON(),\n\t\t\t).sign({\n\t\t\t\tclient: suiClient,\n\t\t\t\tsigner: keypair,\n\t\t\t});\n\n\t\t\ttransactionInput.signal?.throwIfAborted();\n\n\t\t\treturn {\n\t\t\t\tbytes,\n\t\t\t\tsignature: signature,\n\t\t\t};\n\t\t};\n\n\t\t#signAndExecuteTransactionBlock: SuiSignAndExecuteTransactionBlockMethod = async (\n\t\t\ttransactionInput,\n\t\t) => {\n\t\t\tconst { bytes, signature } = await transactionInput.transactionBlock.sign({\n\t\t\t\tclient: suiClient,\n\t\t\t\tsigner: keypair,\n\t\t\t});\n\n\t\t\treturn suiClient.executeTransactionBlock({\n\t\t\t\tsignature,\n\t\t\t\ttransactionBlock: bytes,\n\t\t\t\toptions: transactionInput.options,\n\t\t\t});\n\t\t};\n\n\t\t#signAndExecuteTransaction: SuiSignAndExecuteTransactionMethod = async (transactionInput) => {\n\t\t\tconst { bytes, signature } = await Transaction.from(\n\t\t\t\tawait transactionInput.transaction.toJSON(),\n\t\t\t).sign({\n\t\t\t\tclient: suiClient,\n\t\t\t\tsigner: keypair,\n\t\t\t});\n\n\t\t\ttransactionInput.signal?.throwIfAborted();\n\n\t\t\tconst { rawEffects, digest } = await suiClient.executeTransactionBlock({\n\t\t\t\tsignature,\n\t\t\t\ttransactionBlock: bytes,\n\t\t\t\toptions: {\n\t\t\t\t\tshowRawEffects: true,\n\t\t\t\t},\n\t\t\t});\n\n\t\t\treturn {\n\t\t\t\tbytes,\n\t\t\t\tsignature,\n\t\t\t\tdigest,\n\t\t\t\teffects: toBase64(new Uint8Array(rawEffects!)),\n\t\t\t};\n\t\t};\n\t}\n\n\treturn walletsApi.register(new UnsafeBurnerWallet());\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { useEffect } from 'react';\n\nimport { useCurrentWallet } from './useCurrentWallet.js';\nimport { useWalletStore } from './useWalletStore.js';\n\n/**\n * Internal hook for easily handling various changes in properties for a wallet.\n */\nexport function useWalletPropertiesChanged() {\n\tconst { currentWallet } = useCurrentWallet();\n\tconst updateWalletAccounts = useWalletStore((state) => state.updateWalletAccounts);\n\n\tuseEffect(() => {\n\t\tconst unsubscribeFromEvents = currentWallet?.features['standard:events'].on(\n\t\t\t'change',\n\t\t\t({ accounts }) => {\n\t\t\t\t// TODO: We should handle features changing that might make the list of wallets\n\t\t\t\t// or even the current wallet incompatible with the dApp.\n\t\t\t\tif (accounts) {\n\t\t\t\t\tupdateWalletAccounts(accounts);\n\t\t\t\t}\n\t\t\t},\n\t\t);\n\t\treturn unsubscribeFromEvents;\n\t}, [currentWallet?.features, updateWalletAccounts]);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { WalletWithRequiredFeatures } from '@mysten/wallet-standard';\nimport { getWallets } from '@mysten/wallet-standard';\nimport { useEffect } from 'react';\n\nimport { getRegisteredWallets } from '../../utils/walletUtils.js';\nimport { useWalletStore } from './useWalletStore.js';\n\n/**\n * Internal hook for easily handling the addition and removal of new wallets.\n */\nexport function useWalletsChanged(\n\tpreferredWallets: string[],\n\twalletFilter?: (wallet: WalletWithRequiredFeatures) => boolean,\n) {\n\tconst setWalletRegistered = useWalletStore((state) => state.setWalletRegistered);\n\tconst setWalletUnregistered = useWalletStore((state) => state.setWalletUnregistered);\n\n\tuseEffect(() => {\n\t\tconst walletsApi = getWallets();\n\t\tsetWalletRegistered(getRegisteredWallets(preferredWallets, walletFilter));\n\n\t\tconst unsubscribeFromRegister = walletsApi.on('register', () => {\n\t\t\tsetWalletRegistered(getRegisteredWallets(preferredWallets, walletFilter));\n\t\t});\n\n\t\tconst unsubscribeFromUnregister = walletsApi.on('unregister', (unregisteredWallet) => {\n\t\t\tsetWalletUnregistered(\n\t\t\t\tgetRegisteredWallets(preferredWallets, walletFilter),\n\t\t\t\tunregisteredWallet,\n\t\t\t);\n\t\t});\n\n\t\treturn () => {\n\t\t\tunsubscribeFromRegister();\n\t\t\tunsubscribeFromUnregister();\n\t\t};\n\t}, [preferredWallets, walletFilter, setWalletRegistered, setWalletUnregistered]);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { ThemeVars } from './themeContract.js';\n\nexport const lightTheme: ThemeVars = {\n\tblurs: {\n\t\tmodalOverlay: 'blur(0)',\n\t},\n\tbackgroundColors: {\n\t\tprimaryButton: '#F6F7F9',\n\t\tprimaryButtonHover: '#F0F2F5',\n\t\toutlineButtonHover: '#F4F4F5',\n\t\tmodalOverlay: 'rgba(24 36 53 / 20%)',\n\t\tmodalPrimary: 'white',\n\t\tmodalSecondary: '#F7F8F8',\n\t\ticonButton: 'transparent',\n\t\ticonButtonHover: '#F0F1F2',\n\t\tdropdownMenu: '#FFFFFF',\n\t\tdropdownMenuSeparator: '#F3F6F8',\n\t\twalletItemSelected: 'white',\n\t\twalletItemHover: '#3C424226',\n\t},\n\tborderColors: {\n\t\toutlineButton: '#E4E4E7',\n\t},\n\tcolors: {\n\t\tprimaryButton: '#373737',\n\t\toutlineButton: '#373737',\n\t\ticonButton: '#000000',\n\t\tbody: '#182435',\n\t\tbodyMuted: '#767A81',\n\t\tbodyDanger: '#FF794B',\n\t},\n\tradii: {\n\t\tsmall: '6px',\n\t\tmedium: '8px',\n\t\tlarge: '12px',\n\t\txlarge: '16px',\n\t},\n\tshadows: {\n\t\tprimaryButton: '0px 4px 12px rgba(0, 0, 0, 0.1)',\n\t\twalletItemSelected: '0px 2px 6px rgba(0, 0, 0, 0.05)',\n\t},\n\tfontWeights: {\n\t\tnormal: '400',\n\t\tmedium: '500',\n\t\tbold: '600',\n\t},\n\tfontSizes: {\n\t\tsmall: '14px',\n\t\tmedium: '16px',\n\t\tlarge: '18px',\n\t\txlarge: '20px',\n\t},\n\ttypography: {\n\t\tfontFamily:\n\t\t\t'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"',\n\t\tfontStyle: 'normal',\n\t\tlineHeight: '1.3',\n\t\tletterSpacing: '1',\n\t},\n};\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { Wallet, WalletAccount, WalletWithRequiredFeatures } from '@mysten/wallet-standard';\nimport { createStore } from 'zustand';\nimport type { StateStorage } from 'zustand/middleware';\nimport { createJSONStorage, persist } from 'zustand/middleware';\n\nimport { getWalletUniqueIdentifier } from './utils/walletUtils.js';\n\ntype WalletConnectionStatus = 'disconnected' | 'connecting' | 'connected';\n\nexport type WalletActions = {\n\tsetAccountSwitched: (selectedAccount: WalletAccount) => void;\n\tsetConnectionStatus: (connectionStatus: WalletConnectionStatus) => void;\n\tsetWalletConnected: (\n\t\twallet: WalletWithRequiredFeatures,\n\t\tconnectedAccounts: readonly WalletAccount[],\n\t\tselectedAccount: WalletAccount | null,\n\t\tsupportedIntents?: string[],\n\t) => void;\n\tupdateWalletAccounts: (accounts: readonly WalletAccount[]) => void;\n\tsetWalletDisconnected: () => void;\n\tsetWalletRegistered: (updatedWallets: WalletWithRequiredFeatures[]) => void;\n\tsetWalletUnregistered: (\n\t\tupdatedWallets: WalletWithRequiredFeatures[],\n\t\tunregisteredWallet: Wallet,\n\t) => void;\n};\n\nexport type WalletStore = ReturnType<typeof createWalletStore>;\n\nexport type StoreState = {\n\tautoConnectEnabled: boolean;\n\twallets: WalletWithRequiredFeatures[];\n\taccounts: readonly WalletAccount[];\n\tcurrentWallet: WalletWithRequiredFeatures | null;\n\tcurrentAccount: WalletAccount | null;\n\tlastConnectedAccountAddress: string | null;\n\tlastConnectedWalletName: string | null;\n\tconnectionStatus: WalletConnectionStatus;\n\tsupportedIntents: string[];\n} & WalletActions;\n\ntype WalletConfiguration = {\n\tautoConnectEnabled: boolean;\n\twallets: WalletWithRequiredFeatures[];\n\tstorage: StateStorage;\n\tstorageKey: string;\n};\n\nexport function createWalletStore({\n\twallets,\n\tstorage,\n\tstorageKey,\n\tautoConnectEnabled,\n}: WalletConfiguration) {\n\treturn createStore<StoreState>()(\n\t\tpersist(\n\t\t\t(set, get) => ({\n\t\t\t\tautoConnectEnabled,\n\t\t\t\twallets,\n\t\t\t\taccounts: [] as WalletAccount[],\n\t\t\t\tcurrentWallet: null,\n\t\t\t\tcurrentAccount: null,\n\t\t\t\tlastConnectedAccountAddress: null,\n\t\t\t\tlastConnectedWalletName: null,\n\t\t\t\tconnectionStatus: 'disconnected',\n\t\t\t\tsupportedIntents: [],\n\t\t\t\tsetConnectionStatus(connectionStatus) {\n\t\t\t\t\tset(() => ({\n\t\t\t\t\t\tconnectionStatus,\n\t\t\t\t\t}));\n\t\t\t\t},\n\t\t\t\tsetWalletConnected(wallet, connectedAccounts, selectedAccount, supportedIntents = []) {\n\t\t\t\t\tset(() => ({\n\t\t\t\t\t\taccounts: connectedAccounts,\n\t\t\t\t\t\tcurrentWallet: wallet,\n\t\t\t\t\t\tcurrentAccount: selectedAccount,\n\t\t\t\t\t\tlastConnectedWalletName: getWalletUniqueIdentifier(wallet),\n\t\t\t\t\t\tlastConnectedAccountAddress: selectedAccount?.address,\n\t\t\t\t\t\tconnectionStatus: 'connected',\n\t\t\t\t\t\tsupportedIntents,\n\t\t\t\t\t}));\n\t\t\t\t},\n\t\t\t\tsetWalletDisconnected() {\n\t\t\t\t\tset(() => ({\n\t\t\t\t\t\taccounts: [],\n\t\t\t\t\t\tcurrentWallet: null,\n\t\t\t\t\t\tcurrentAccount: null,\n\t\t\t\t\t\tlastConnectedWalletName: null,\n\t\t\t\t\t\tlastConnectedAccountAddress: null,\n\t\t\t\t\t\tconnectionStatus: 'disconnected',\n\t\t\t\t\t\tsupportedIntents: [],\n\t\t\t\t\t}));\n\t\t\t\t},\n\t\t\t\tsetAccountSwitched(selectedAccount) {\n\t\t\t\t\tset(() => ({\n\t\t\t\t\t\tcurrentAccount: selectedAccount,\n\t\t\t\t\t\tlastConnectedAccountAddress: selectedAccount.address,\n\t\t\t\t\t}));\n\t\t\t\t},\n\t\t\t\tsetWalletRegistered(updatedWallets) {\n\t\t\t\t\tset(() => ({ wallets: updatedWallets }));\n\t\t\t\t},\n\t\t\t\tsetWalletUnregistered(updatedWallets, unregisteredWallet) {\n\t\t\t\t\tif (unregisteredWallet === get().currentWallet) {\n\t\t\t\t\t\tset(() => ({\n\t\t\t\t\t\t\twallets: updatedWallets,\n\t\t\t\t\t\t\taccounts: [],\n\t\t\t\t\t\t\tcurrentWallet: null,\n\t\t\t\t\t\t\tcurrentAccount: null,\n\t\t\t\t\t\t\tlastConnectedWalletName: null,\n\t\t\t\t\t\t\tlastConnectedAccountAddress: null,\n\t\t\t\t\t\t\tconnectionStatus: 'disconnected',\n\t\t\t\t\t\t\tsupportedIntents: [],\n\t\t\t\t\t\t}));\n\t\t\t\t\t} else {\n\t\t\t\t\t\tset(() => ({ wallets: updatedWallets }));\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tupdateWalletAccounts(accounts) {\n\t\t\t\t\tconst currentAccount = get().currentAccount;\n\n\t\t\t\t\tset(() => ({\n\t\t\t\t\t\taccounts,\n\t\t\t\t\t\tcurrentAccount:\n\t\t\t\t\t\t\t(currentAccount &&\n\t\t\t\t\t\t\t\taccounts.find(({ address }) => address === currentAccount.address)) ||\n\t\t\t\t\t\t\taccounts[0],\n\t\t\t\t\t}));\n\t\t\t\t},\n\t\t\t}),\n\t\t\t{\n\t\t\t\tname: storageKey,\n\t\t\t\tstorage: createJSONStorage(() => storage),\n\t\t\t\tpartialize: ({ lastConnectedWalletName, lastConnectedAccountAddress }) => ({\n\t\t\t\t\tlastConnectedWalletName,\n\t\t\t\t\tlastConnectedAccountAddress,\n\t\t\t\t}),\n\t\t\t},\n\t\t),\n\t);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { assignInlineVars } from '@vanilla-extract/dynamic';\n\nimport { styleDataAttributeSelector } from '../../constants/styleDataAttribute.js';\nimport { themeVars } from '../../themes/themeContract.js';\nimport type { DynamicTheme, Theme, ThemeVars } from '../../themes/themeContract.js';\n\ntype InjectedThemeStylesProps = {\n\ttheme: Theme;\n};\n\nexport function InjectedThemeStyles({ theme }: InjectedThemeStylesProps) {\n\tconst themeStyles = Array.isArray(theme)\n\t\t? getDynamicThemeStyles(theme)\n\t\t: getStaticThemeStyles(theme);\n\n\treturn (\n\t\t<style\n\t\t\t// @ts-expect-error The precedence prop hasn't made it to the stable release of React, but we\n\t\t\t// don't want this to break in frameworks like Next which use the latest canary build.\n\t\t\tprecedence=\"default\"\n\t\t\thref=\"mysten-dapp-kit-theme\"\n\t\t\tdangerouslySetInnerHTML={{\n\t\t\t\t__html: themeStyles,\n\t\t\t}}\n\t\t/>\n\t);\n}\n\nfunction getDynamicThemeStyles(themes: DynamicTheme[]) {\n\treturn themes\n\t\t.map(({ mediaQuery, selector, variables }) => {\n\t\t\tconst themeStyles = getStaticThemeStyles(variables);\n\t\t\tconst themeStylesWithSelectorPrefix = selector ? `${selector} ${themeStyles}` : themeStyles;\n\n\t\t\treturn mediaQuery\n\t\t\t\t? `@media ${mediaQuery}{${themeStylesWithSelectorPrefix}}`\n\t\t\t\t: themeStylesWithSelectorPrefix;\n\t\t})\n\t\t.join(' ');\n}\n\nfunction getStaticThemeStyles(theme: ThemeVars) {\n\treturn `${styleDataAttributeSelector} {${cssStringFromTheme(theme)}}`;\n}\n\nfunction cssStringFromTheme(theme: ThemeVars) {\n\treturn Object.entries(assignInlineVars(themeVars, theme))\n\t\t.map(([key, value]) => `${key}:${value};`)\n\t\t.join('');\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { createGlobalThemeContract } from '@vanilla-extract/css';\n\nconst themeContractValues = {\n\tblurs: {\n\t\tmodalOverlay: '',\n\t},\n\tbackgroundColors: {\n\t\tprimaryButton: '',\n\t\tprimaryButtonHover: '',\n\t\toutlineButtonHover: '',\n\t\twalletItemHover: '',\n\t\twalletItemSelected: '',\n\t\tmodalOverlay: '',\n\t\tmodalPrimary: '',\n\t\tmodalSecondary: '',\n\t\ticonButton: '',\n\t\ticonButtonHover: '',\n\t\tdropdownMenu: '',\n\t\tdropdownMenuSeparator: '',\n\t},\n\tborderColors: {\n\t\toutlineButton: '',\n\t},\n\tcolors: {\n\t\tprimaryButton: '',\n\t\toutlineButton: '',\n\t\tbody: '',\n\t\tbodyMuted: '',\n\t\tbodyDanger: '',\n\t\ticonButton: '',\n\t},\n\tradii: {\n\t\tsmall: '',\n\t\tmedium: '',\n\t\tlarge: '',\n\t\txlarge: '',\n\t},\n\tshadows: {\n\t\tprimaryButton: '',\n\t\twalletItemSelected: '',\n\t},\n\tfontWeights: {\n\t\tnormal: '',\n\t\tmedium: '',\n\t\tbold: '',\n\t},\n\tfontSizes: {\n\t\tsmall: '',\n\t\tmedium: '',\n\t\tlarge: '',\n\t\txlarge: '',\n\t},\n\ttypography: {\n\t\tfontFamily: '',\n\t\tfontStyle: '',\n\t\tlineHeight: '',\n\t\tletterSpacing: '',\n\t},\n};\n\nexport type ThemeVars = typeof themeContractValues;\n\n/**\n * A custom theme that is enabled when various conditions are\n */\nexport type DynamicTheme = {\n\t/**\n\t * An optional media query required for the given theme to be enabled. This is useful\n\t * when you want the theme of your application to automatically switch depending on\n\t * a media feature.\n\t *\n\t * @example '(prefers-color-scheme: dark)'\n\t */\n\tmediaQuery?: string;\n\n\t/**\n\t * An optional CSS selector required for the given theme to be enabled. This is useful\n\t * when you have a manual theme switcher on your application that sets a top-level\n\t * class name or data-attribute to control the current theme.\n\t *\n\t * @example '.data-dark'\n\t */\n\tselector?: string;\n\n\t/** The theme definitions that will be set when the selector and mediaQuery criteria are matched. */\n\tvariables: ThemeVars;\n};\n\nexport type Theme = ThemeVars | DynamicTheme[];\n\nexport const themeVars = createGlobalThemeContract(\n\tthemeContractValues,\n\t(_, path) => `dapp-kit-${path.join('-')}`,\n);\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SuiClientOptions } from '@mysten/sui/client';\n\nimport { useSuiClientContext } from './useSuiClient.js';\n\nexport type NetworkConfig<T extends object = object> = SuiClientOptions & {\n\tvariables?: T;\n};\n\nexport function createNetworkConfig<\n\tconst T extends Record<string, Config>,\n\tConfig extends NetworkConfig<Variables> = T[keyof T],\n\tVariables extends object = NonNullable<Config['variables']>,\n>(networkConfig: T) {\n\tfunction useNetworkConfig(): Config {\n\t\tconst { config } = useSuiClientContext();\n\n\t\tif (!config) {\n\t\t\tthrow new Error('No network config found');\n\t\t}\n\n\t\treturn config as T[keyof T];\n\t}\n\n\tfunction useNetworkVariables(): Variables {\n\t\tconst { variables } = useNetworkConfig();\n\n\t\treturn (variables ?? {}) as Variables;\n\t}\n\n\tfunction useNetworkVariable<K extends keyof Variables>(name: K): Variables[K] {\n\t\tconst variables = useNetworkVariables();\n\n\t\treturn variables[name];\n\t}\n\n\treturn {\n\t\tnetworkConfig,\n\t\tuseNetworkConfig,\n\t\tuseNetworkVariables,\n\t\tuseNetworkVariable,\n\t};\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SuiClient } from '@mysten/sui/client';\nimport type {\n\tInfiniteData,\n\tUseInfiniteQueryOptions,\n\tUseInfiniteQueryResult,\n} from '@tanstack/react-query';\nimport { useInfiniteQuery } from '@tanstack/react-query';\n\nimport type { PartialBy } from '../types/utilityTypes.js';\nimport { useSuiClientContext } from './useSuiClient.js';\n\ninterface PaginatedResult {\n\tdata?: unknown;\n\tnextCursor?: unknown;\n\thasNextPage: boolean;\n}\n\nexport type SuiRpcPaginatedMethodName = {\n\t[K in keyof SuiClient]: SuiClient[K] extends (input: any) => Promise<PaginatedResult> ? K : never;\n}[keyof SuiClient];\n\nexport type SuiRpcPaginatedMethods = {\n\t[K in SuiRpcPaginatedMethodName]: SuiClient[K] extends (\n\t\tinput: infer Params,\n\t) => Promise<\n\t\tinfer Result extends { hasNextPage?: boolean | null; nextCursor?: infer Cursor | null }\n\t>\n\t\t? {\n\t\t\t\tname: K;\n\t\t\t\tresult: Result;\n\t\t\t\tparams: Params;\n\t\t\t\tcursor: Cursor;\n\t\t\t}\n\t\t: never;\n};\n\nexport type UseSuiClientInfiniteQueryOptions<\n\tT extends keyof SuiRpcPaginatedMethods,\n\tTData,\n> = PartialBy<\n\tOmit<\n\t\tUseInfiniteQueryOptions<SuiRpcPaginatedMethods[T]['result'], Error, TData, unknown[]>,\n\t\t'queryFn' | 'initialPageParam' | 'getNextPageParam'\n\t>,\n\t'queryKey'\n>;\n\nexport function useSuiClientInfiniteQuery<\n\tT extends keyof SuiRpcPaginatedMethods,\n\tTData = InfiniteData<SuiRpcPaginatedMethods[T]['result']>,\n>(\n\tmethod: T,\n\tparams: SuiRpcPaginatedMethods[T]['params'],\n\t{\n\t\tqueryKey = [],\n\t\tenabled = !!params,\n\t\t...options\n\t}: UseSuiClientInfiniteQueryOptions<T, TData> = {},\n): UseInfiniteQueryResult<TData, Error> {\n\tconst suiContext = useSuiClientContext();\n\n\treturn useInfiniteQuery({\n\t\t...options,\n\t\tinitialPageParam: null,\n\t\tqueryKey: [suiContext.network, method, params, ...queryKey],\n\t\tenabled,\n\t\tqueryFn: ({ pageParam }) =>\n\t\t\tsuiContext.client[method]({\n\t\t\t\t...(params ?? {}),\n\t\t\t\tcursor: pageParam,\n\t\t\t} as never),\n\t\tgetNextPageParam: (lastPage) => (lastPage.hasNextPage ? (lastPage.nextCursor ?? null) : null),\n\t});\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { UseMutationOptions, UseMutationResult } from '@tanstack/react-query';\nimport { useMutation } from '@tanstack/react-query';\n\nimport { useSuiClientContext } from './useSuiClient.js';\nimport type { SuiRpcMethods } from './useSuiClientQuery.js';\n\nexport type UseSuiClientMutationOptions<T extends keyof SuiRpcMethods> = Omit<\n\tUseMutationOptions<SuiRpcMethods[T]['result'], Error, SuiRpcMethods[T]['params'], unknown[]>,\n\t'mutationFn'\n>;\n\nexport function useSuiClientMutation<T extends keyof SuiRpcMethods>(\n\tmethod: T,\n\toptions: UseSuiClientMutationOptions<T> = {},\n): UseMutationResult<SuiRpcMethods[T]['result'], Error, SuiRpcMethods[T]['params'], unknown[]> {\n\tconst suiContext = useSuiClientContext();\n\n\treturn useMutation({\n\t\t...options,\n\t\tmutationFn: async (params) => {\n\t\t\treturn await suiContext.client[method](params as never);\n\t\t},\n\t});\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { UseQueryResult } from '@tanstack/react-query';\nimport { useQueries } from '@tanstack/react-query';\n\nimport { useSuiClientContext } from './useSuiClient.js';\nimport type { SuiRpcMethods, UseSuiClientQueryOptions } from './useSuiClientQuery.js';\n\ntype SuiClientQueryOptions = SuiRpcMethods[keyof SuiRpcMethods] extends infer Method\n\t? Method extends {\n\t\t\tname: infer M extends keyof SuiRpcMethods;\n\t\t\tparams?: infer P;\n\t\t}\n\t\t? undefined extends P\n\t\t\t? {\n\t\t\t\t\tmethod: M;\n\t\t\t\t\tparams?: P;\n\t\t\t\t\toptions?: UseSuiClientQueryOptions<M, unknown>;\n\t\t\t\t}\n\t\t\t: {\n\t\t\t\t\tmethod: M;\n\t\t\t\t\tparams: P;\n\t\t\t\t\toptions?: UseSuiClientQueryOptions<M, unknown>;\n\t\t\t\t}\n\t\t: never\n\t: never;\n\nexport type UseSuiClientQueriesResults<Args extends readonly SuiClientQueryOptions[]> = {\n\t-readonly [K in keyof Args]: Args[K] extends {\n\t\tmethod: infer M extends keyof SuiRpcMethods;\n\t\treadonly options?:\n\t\t\t| {\n\t\t\t\t\tselect?: (...args: any[]) => infer R;\n\t\t\t  }\n\t\t\t| object;\n\t}\n\t\t? UseQueryResult<unknown extends R ? SuiRpcMethods[M]['result'] : R>\n\t\t: never;\n};\n\nexport function useSuiClientQueries<\n\tconst Queries extends readonly SuiClientQueryOptions[],\n\tResults = UseSuiClientQueriesResults<Queries>,\n>({\n\tqueries,\n\tcombine,\n}: {\n\tqueries: Queries;\n\tcombine?: (results: UseSuiClientQueriesResults<Queries>) => Results;\n}): Results {\n\tconst suiContext = useSuiClientContext();\n\n\treturn useQueries({\n\t\tcombine: combine as never,\n\t\tqueries: queries.map((query) => {\n\t\t\tconst { method, params, options: { queryKey = [], ...restOptions } = {} } = query;\n\n\t\t\treturn {\n\t\t\t\t...restOptions,\n\t\t\t\tqueryKey: [suiContext.network, method, params, ...queryKey],\n\t\t\t\tqueryFn: async () => {\n\t\t\t\t\treturn await suiContext.client[method](params as never);\n\t\t\t\t},\n\t\t\t};\n\t\t}) as [],\n\t});\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { Transaction } from '@mysten/sui/transactions';\nimport { toBase64 } from '@mysten/sui/utils';\nimport type {\n\tSuiSignAndExecuteTransactionInput,\n\tSuiSignAndExecuteTransactionOutput,\n} from '@mysten/wallet-standard';\nimport { signTransaction } from '@mysten/wallet-standard';\nimport type { UseMutationOptions, UseMutationResult } from '@tanstack/react-query';\nimport { useMutation } from '@tanstack/react-query';\n\nimport { walletMutationKeys } from '../../constants/walletMutationKeys.js';\nimport {\n\tWalletFeatureNotSupportedError,\n\tWalletNoAccountSelectedError,\n\tWalletNotConnectedError,\n} from '../../errors/walletErrors.js';\nimport type { PartialBy } from '../../types/utilityTypes.js';\nimport { useSuiClientContext } from '../useSuiClient.js';\nimport { useCurrentAccount } from './useCurrentAccount.js';\nimport { useCurrentWallet } from './useCurrentWallet.js';\nimport { useReportTransactionEffects } from './useReportTransactionEffects.js';\n\ntype UseSignAndExecuteTransactionArgs = PartialBy<\n\tOmit<SuiSignAndExecuteTransactionInput, 'transaction'>,\n\t'account' | 'chain'\n> & {\n\ttransaction: Transaction | string;\n};\n\ntype UseSignAndExecuteTransactionResult = SuiSignAndExecuteTransactionOutput;\n\ntype UseSignAndExecuteTransactionError =\n\t| WalletFeatureNotSupportedError\n\t| WalletNoAccountSelectedError\n\t| WalletNotConnectedError\n\t| Error;\n\ntype ExecuteTransactionResult =\n\t| {\n\t\t\tdigest: string;\n\t\t\trawEffects?: number[];\n\t  }\n\t| {\n\t\t\teffects?: {\n\t\t\t\tbcs?: string;\n\t\t\t};\n\t  };\n\ntype UseSignAndExecuteTransactionMutationOptions<Result extends ExecuteTransactionResult> = Omit<\n\tUseMutationOptions<\n\t\tResult,\n\t\tUseSignAndExecuteTransactionError,\n\t\tUseSignAndExecuteTransactionArgs,\n\t\tunknown\n\t>,\n\t'mutationFn'\n> & {\n\texecute?: ({ bytes, signature }: { bytes: string; signature: string }) => Promise<Result>;\n};\n\n/**\n * Mutation hook for prompting the user to sign and execute a transaction.\n */\nexport function useSignAndExecuteTransaction<\n\tResult extends ExecuteTransactionResult = UseSignAndExecuteTransactionResult,\n>({\n\tmutationKey,\n\texecute,\n\t...mutationOptions\n}: UseSignAndExecuteTransactionMutationOptions<Result> = {}): UseMutationResult<\n\tResult,\n\tUseSignAndExecuteTransactionError,\n\tUseSignAndExecuteTransactionArgs\n> {\n\tconst { currentWallet, supportedIntents } = useCurrentWallet();\n\tconst currentAccount = useCurrentAccount();\n\tconst { client, network } = useSuiClientContext();\n\tconst { mutate: reportTransactionEffects } = useReportTransactionEffects();\n\n\tconst executeTransaction: ({\n\t\tbytes,\n\t\tsignature,\n\t}: {\n\t\tbytes: string;\n\t\tsignature: string;\n\t}) => Promise<ExecuteTransactionResult> =\n\t\texecute ??\n\t\t(async ({ bytes, signature }) => {\n\t\t\tconst { digest, rawEffects } = await client.executeTransactionBlock({\n\t\t\t\ttransactionBlock: bytes,\n\t\t\t\tsignature,\n\t\t\t\toptions: {\n\t\t\t\t\tshowRawEffects: true,\n\t\t\t\t},\n\t\t\t});\n\n\t\t\treturn {\n\t\t\t\tdigest,\n\t\t\t\trawEffects,\n\t\t\t\teffects: toBase64(new Uint8Array(rawEffects!)),\n\t\t\t\tbytes,\n\t\t\t\tsignature,\n\t\t\t};\n\t\t});\n\n\treturn useMutation({\n\t\tmutationKey: walletMutationKeys.signAndExecuteTransaction(mutationKey),\n\t\tmutationFn: async ({ transaction, ...signTransactionArgs }): Promise<Result> => {\n\t\t\tif (!currentWallet) {\n\t\t\t\tthrow new WalletNotConnectedError('No wallet is connected.');\n\t\t\t}\n\n\t\t\tconst signerAccount = signTransactionArgs.account ?? currentAccount;\n\t\t\tif (!signerAccount) {\n\t\t\t\tthrow new WalletNoAccountSelectedError(\n\t\t\t\t\t'No wallet account is selected to sign the transaction with.',\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\t!currentWallet.features['sui:signTransaction'] &&\n\t\t\t\t!currentWallet.features['sui:signTransactionBlock']\n\t\t\t) {\n\t\t\t\tthrow new WalletFeatureNotSupportedError(\n\t\t\t\t\t\"This wallet doesn't support the `signTransaction` feature.\",\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tconst chain = signTransactionArgs.chain ?? `sui:${network}`;\n\t\t\tconst { signature, bytes } = await signTransaction(currentWallet, {\n\t\t\t\t...signTransactionArgs,\n\t\t\t\ttransaction: {\n\t\t\t\t\tasync toJSON() {\n\t\t\t\t\t\treturn typeof transaction === 'string'\n\t\t\t\t\t\t\t? transaction\n\t\t\t\t\t\t\t: await transaction.toJSON({\n\t\t\t\t\t\t\t\t\tsupportedIntents,\n\t\t\t\t\t\t\t\t\tclient,\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\taccount: signerAccount,\n\t\t\t\tchain,\n\t\t\t});\n\n\t\t\tconst result = await executeTransaction({ bytes, signature });\n\n\t\t\tlet effects: string;\n\n\t\t\tif ('effects' in result && result.effects?.bcs) {\n\t\t\t\teffects = result.effects.bcs;\n\t\t\t} else if ('rawEffects' in result) {\n\t\t\t\teffects = toBase64(new Uint8Array(result.rawEffects!));\n\t\t\t} else {\n\t\t\t\tthrow new Error('Could not parse effects from transaction result.');\n\t\t\t}\n\n\t\t\treportTransactionEffects({ effects, account: signerAccount, chain });\n\n\t\t\treturn result as Result;\n\t\t},\n\t\t...mutationOptions,\n\t});\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { toBase64 } from '@mysten/sui/utils';\nimport type { SuiReportTransactionEffectsInput } from '@mysten/wallet-standard';\nimport type { UseMutationOptions, UseMutationResult } from '@tanstack/react-query';\nimport { useMutation } from '@tanstack/react-query';\n\nimport { walletMutationKeys } from '../../constants/walletMutationKeys.js';\nimport type { WalletFeatureNotSupportedError } from '../../errors/walletErrors.js';\nimport {\n\tWalletNoAccountSelectedError,\n\tWalletNotConnectedError,\n} from '../../errors/walletErrors.js';\nimport type { PartialBy } from '../../types/utilityTypes.js';\nimport { useCurrentAccount } from './useCurrentAccount.js';\nimport { useCurrentWallet } from './useCurrentWallet.js';\n\ntype UseReportTransactionEffectsArgs = Omit<\n\tPartialBy<SuiReportTransactionEffectsInput, 'account' | 'chain'>,\n\t'effects'\n> & {\n\teffects: string | number[];\n};\n\ntype UseReportTransactionEffectsError =\n\t| WalletFeatureNotSupportedError\n\t| WalletNoAccountSelectedError\n\t| WalletNotConnectedError\n\t| Error;\n\ntype UseReportTransactionEffectsMutationOptions = Omit<\n\tUseMutationOptions<\n\t\tvoid,\n\t\tUseReportTransactionEffectsError,\n\t\tUseReportTransactionEffectsArgs,\n\t\tunknown\n\t>,\n\t'mutationFn'\n>;\n\n/**\n * Mutation hook for prompting the user to sign a message.\n */\nexport function useReportTransactionEffects({\n\tmutationKey,\n\t...mutationOptions\n}: UseReportTransactionEffectsMutationOptions = {}): UseMutationResult<\n\tvoid,\n\tUseReportTransactionEffectsError,\n\tUseReportTransactionEffectsArgs\n> {\n\tconst { currentWallet } = useCurrentWallet();\n\tconst currentAccount = useCurrentAccount();\n\n\treturn useMutation({\n\t\tmutationKey: walletMutationKeys.reportTransactionEffects(mutationKey),\n\t\tmutationFn: async ({ effects, chain = currentWallet?.chains[0], account = currentAccount }) => {\n\t\t\tif (!currentWallet) {\n\t\t\t\tthrow new WalletNotConnectedError('No wallet is connected.');\n\t\t\t}\n\n\t\t\tif (!account) {\n\t\t\t\tthrow new WalletNoAccountSelectedError(\n\t\t\t\t\t'No wallet account is selected to report transaction effects for',\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tconst reportTransactionEffectsFeature =\n\t\t\t\tcurrentWallet.features['sui:reportTransactionEffects'];\n\n\t\t\tif (reportTransactionEffectsFeature) {\n\t\t\t\treturn await reportTransactionEffectsFeature.reportTransactionEffects({\n\t\t\t\t\teffects: Array.isArray(effects) ? toBase64(new Uint8Array(effects)) : effects,\n\t\t\t\t\taccount,\n\t\t\t\t\tchain: chain ?? currentWallet?.chains[0],\n\t\t\t\t});\n\t\t\t}\n\t\t},\n\t\t...mutationOptions,\n\t});\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type {\n\tSuiSignPersonalMessageInput,\n\tSuiSignPersonalMessageOutput,\n} from '@mysten/wallet-standard';\nimport type { UseMutationOptions, UseMutationResult } from '@tanstack/react-query';\nimport { useMutation } from '@tanstack/react-query';\n\nimport {\n\tWalletFeatureNotSupportedError,\n\tWalletNoAccountSelectedError,\n\tWalletNotConnectedError,\n} from '../..//errors/walletErrors.js';\nimport { walletMutationKeys } from '../../constants/walletMutationKeys.js';\nimport type { PartialBy } from '../../types/utilityTypes.js';\nimport { useSuiClientContext } from '../useSuiClient.js';\nimport { useCurrentAccount } from './useCurrentAccount.js';\nimport { useCurrentWallet } from './useCurrentWallet.js';\n\ntype UseSignPersonalMessageArgs = PartialBy<SuiSignPersonalMessageInput, 'account' | 'chain'>;\n\ntype UseSignPersonalMessageResult = SuiSignPersonalMessageOutput;\n\ntype UseSignPersonalMessageError =\n\t| WalletFeatureNotSupportedError\n\t| WalletNoAccountSelectedError\n\t| WalletNotConnectedError\n\t| Error;\n\ntype UseSignPersonalMessageMutationOptions = Omit<\n\tUseMutationOptions<\n\t\tUseSignPersonalMessageResult,\n\t\tUseSignPersonalMessageError,\n\t\tUseSignPersonalMessageArgs,\n\t\tunknown\n\t>,\n\t'mutationFn'\n>;\n\n/**\n * Mutation hook for prompting the user to sign a message.\n */\nexport function useSignPersonalMessage({\n\tmutationKey,\n\t...mutationOptions\n}: UseSignPersonalMessageMutationOptions = {}): UseMutationResult<\n\tUseSignPersonalMessageResult,\n\tUseSignPersonalMessageError,\n\tUseSignPersonalMessageArgs\n> {\n\tconst { currentWallet } = useCurrentWallet();\n\tconst currentAccount = useCurrentAccount();\n\tconst { network } = useSuiClientContext();\n\n\treturn useMutation({\n\t\tmutationKey: walletMutationKeys.signPersonalMessage(mutationKey),\n\t\tmutationFn: async (signPersonalMessageArgs) => {\n\t\t\tif (!currentWallet) {\n\t\t\t\tthrow new WalletNotConnectedError('No wallet is connected.');\n\t\t\t}\n\n\t\t\tconst signerAccount = signPersonalMessageArgs.account ?? currentAccount;\n\t\t\tif (!signerAccount) {\n\t\t\t\tthrow new WalletNoAccountSelectedError(\n\t\t\t\t\t'No wallet account is selected to sign the personal message with.',\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tconst signPersonalMessageFeature = currentWallet.features['sui:signPersonalMessage'];\n\t\t\tif (signPersonalMessageFeature) {\n\t\t\t\treturn await signPersonalMessageFeature.signPersonalMessage({\n\t\t\t\t\t...signPersonalMessageArgs,\n\t\t\t\t\taccount: signerAccount,\n\t\t\t\t\tchain: signPersonalMessageArgs.chain ?? `sui:${network}`,\n\t\t\t\t});\n\t\t\t}\n\n\t\t\t// TODO: Remove this once we officially discontinue sui:signMessage in the wallet standard\n\t\t\tconst signMessageFeature = currentWallet.features['sui:signMessage'];\n\t\t\tif (signMessageFeature) {\n\t\t\t\tconsole.warn(\n\t\t\t\t\t\"This wallet doesn't support the `signPersonalMessage` feature... falling back to `signMessage`.\",\n\t\t\t\t);\n\n\t\t\t\tconst { messageBytes, signature } = await signMessageFeature.signMessage({\n\t\t\t\t\t...signPersonalMessageArgs,\n\t\t\t\t\taccount: signerAccount,\n\t\t\t\t});\n\t\t\t\treturn { bytes: messageBytes, signature };\n\t\t\t}\n\n\t\t\tthrow new WalletFeatureNotSupportedError(\n\t\t\t\t\"This wallet doesn't support the `signPersonalMessage` feature.\",\n\t\t\t);\n\t\t},\n\t\t...mutationOptions,\n\t});\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { Transaction } from '@mysten/sui/transactions';\nimport { signTransaction } from '@mysten/wallet-standard';\nimport type { SignedTransaction, SuiSignTransactionInput } from '@mysten/wallet-standard';\nimport type { UseMutationOptions, UseMutationResult } from '@tanstack/react-query';\nimport { useMutation } from '@tanstack/react-query';\n\nimport { walletMutationKeys } from '../../constants/walletMutationKeys.js';\nimport {\n\tWalletFeatureNotSupportedError,\n\tWalletNoAccountSelectedError,\n\tWalletNotConnectedError,\n} from '../../errors/walletErrors.js';\nimport type { PartialBy } from '../../types/utilityTypes.js';\nimport { useSuiClientContext } from '../useSuiClient.js';\nimport { useCurrentAccount } from './useCurrentAccount.js';\nimport { useCurrentWallet } from './useCurrentWallet.js';\nimport { useReportTransactionEffects } from './useReportTransactionEffects.js';\n\ntype UseSignTransactionArgs = PartialBy<\n\tOmit<SuiSignTransactionInput, 'transaction'>,\n\t'account' | 'chain'\n> & {\n\ttransaction: Transaction | string;\n};\n\ninterface UseSignTransactionResult extends SignedTransaction {\n\treportTransactionEffects: (effects: string) => void;\n}\n\ntype UseSignTransactionError =\n\t| WalletFeatureNotSupportedError\n\t| WalletNoAccountSelectedError\n\t| WalletNotConnectedError\n\t| Error;\n\ntype UseSignTransactionMutationOptions = Omit<\n\tUseMutationOptions<\n\t\tUseSignTransactionResult,\n\t\tUseSignTransactionError,\n\t\tUseSignTransactionArgs,\n\t\tunknown\n\t>,\n\t'mutationFn'\n>;\n\n/**\n * Mutation hook for prompting the user to sign a transaction.\n */\nexport function useSignTransaction({\n\tmutationKey,\n\t...mutationOptions\n}: UseSignTransactionMutationOptions = {}): UseMutationResult<\n\tUseSignTransactionResult,\n\tUseSignTransactionError,\n\tUseSignTransactionArgs\n> {\n\tconst { currentWallet } = useCurrentWallet();\n\tconst currentAccount = useCurrentAccount();\n\tconst { client, network } = useSuiClientContext();\n\n\tconst { mutate: reportTransactionEffects } = useReportTransactionEffects();\n\n\treturn useMutation({\n\t\tmutationKey: walletMutationKeys.signTransaction(mutationKey),\n\t\tmutationFn: async ({ transaction, ...signTransactionArgs }) => {\n\t\t\tif (!currentWallet) {\n\t\t\t\tthrow new WalletNotConnectedError('No wallet is connected.');\n\t\t\t}\n\n\t\t\tconst signerAccount = signTransactionArgs.account ?? currentAccount;\n\t\t\tif (!signerAccount) {\n\t\t\t\tthrow new WalletNoAccountSelectedError(\n\t\t\t\t\t'No wallet account is selected to sign the transaction with.',\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\t!currentWallet.features['sui:signTransaction'] &&\n\t\t\t\t!currentWallet.features['sui:signTransactionBlock']\n\t\t\t) {\n\t\t\t\tthrow new WalletFeatureNotSupportedError(\n\t\t\t\t\t\"This wallet doesn't support the `signTransaction` feature.\",\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tconst chain = signTransactionArgs.chain ?? `sui:${network}`;\n\t\t\tconst { bytes, signature } = await signTransaction(currentWallet, {\n\t\t\t\t...signTransactionArgs,\n\t\t\t\ttransaction: {\n\t\t\t\t\ttoJSON: async () => {\n\t\t\t\t\t\treturn typeof transaction === 'string'\n\t\t\t\t\t\t\t? transaction\n\t\t\t\t\t\t\t: await transaction.toJSON({\n\t\t\t\t\t\t\t\t\tsupportedIntents: [],\n\t\t\t\t\t\t\t\t\tclient,\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\taccount: signerAccount,\n\t\t\t\tchain,\n\t\t\t});\n\n\t\t\treturn {\n\t\t\t\tbytes,\n\t\t\t\tsignature,\n\t\t\t\treportTransactionEffects: (effects) => {\n\t\t\t\t\treportTransactionEffects({\n\t\t\t\t\t\teffects,\n\t\t\t\t\t\taccount: signerAccount,\n\t\t\t\t\t\tchain,\n\t\t\t\t\t});\n\t\t\t\t},\n\t\t\t};\n\t\t},\n\t\t...mutationOptions,\n\t});\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACIA,aAAwB;AACxB,IAAAA,eAAiB;AACjB,IAAAC,gBAAyB;;;ACFzB,0BAAkC;;;ACC3B,SAAS,sBAAoC;AACnD,QAAM,QAAQ,oBAAI,IAAI;AACtB,SAAO;AAAA,IACN,QAAQ,KAAa;AACpB,aAAO,MAAM,IAAI,GAAG;AAAA,IACrB;AAAA,IACA,QAAQ,KAAa,OAAe;AACnC,YAAM,IAAI,KAAK,KAAK;AAAA,IACrB;AAAA,IACA,WAAW,KAAa;AACvB,YAAM,OAAO,GAAG;AAAA,IACjB;AAAA,EACD;AACD;;;ADVO,IAAM,kBAAkB;AAExB,IAAM,kBACZ,OAAO,WAAW,eAAe,OAAO,eAAe,eAAe,oBAAoB;AAEpF,IAAM,sBAAsB;AAEnC,IAAM,gBAAgB;AAAA,EACrB;AAAA,EACA;AACD;AAEO,IAAM,wBAAwB,CAAC,WACrC,cAAc,KAAK,CAAC,YAAY,OAAO,SAAS,OAAO,CAAC;AAElD,IAAM,4BAA4B,CAAC,iBAAiB,qCAAiB;;;AEb5E,yBAA4B;;;ACLrB,IAAM,qBAAqB;AAAA,EACjC,KAAK,EAAE,WAAW,SAAS;AAAA,EAC3B,eAAe,kBAAkB,gBAAgB;AAAA,EACjD,mBAAmB,kBAAkB,oBAAoB;AAAA,EACzD,kBAAkB,kBAAkB,mBAAmB;AAAA,EACvD,qBAAqB,kBAAkB,uBAAuB;AAAA,EAC9D,iBAAiB,kBAAkB,kBAAkB;AAAA,EACrD,2BAA2B,kBAAkB,8BAA8B;AAAA,EAC3E,eAAe,kBAAkB,gBAAgB;AAAA,EACjD,0BAA0B,kBAAkB,4BAA4B;AACzE;AAEA,SAAS,kBAAkB,YAAoB;AAC9C,SAAO,SAAS,cAAc,iBAA8B,CAAC,GAAG;AAC/D,WAAO,CAAC,EAAE,GAAG,mBAAmB,KAAK,WAAW,GAAG,GAAG,cAAc;AAAA,EACrE;AACD;;;AClBA,IAAAC,gBAA2B;AAC3B,qBAAyB;;;ACDzB,mBAA8B;AAIvB,IAAM,oBAAgB,4BAAkC,IAAI;;;ADE5D,SAAS,eAAkB,UAAuC;AACxE,QAAM,YAAQ,0BAAW,aAAa;AACtC,MAAI,CAAC,OAAO;AACX,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AACA,aAAO,yBAAS,OAAO,QAAQ;AAChC;;;AFgBO,SAAS,iBAAiB;AAAA,EAChC;AAAA,EACA,GAAG;AACJ,IAAqC,CAAC,GAKpC;AACD,QAAM,qBAAqB,eAAe,CAAC,UAAU,MAAM,kBAAkB;AAC7E,QAAM,sBAAsB,eAAe,CAAC,UAAU,MAAM,mBAAmB;AAE/E,aAAO,gCAAY;AAAA,IAClB,aAAa,mBAAmB,cAAc,WAAW;AAAA,IACzD,YAAY,OAAO,EAAE,QAAQ,gBAAgB,GAAG,YAAY,MAAM;AACjE,UAAI;AACH,4BAAoB,YAAY;AAEhC,cAAM,gBAAgB,MAAM,OAAO,SAAS,kBAAkB,EAAE,QAAQ,WAAW;AACnF,cAAM,uBAAuB,cAAc,SAAS;AAAA,UAAO,CAAC,YAC3D,QAAQ,OAAO,KAAK,CAAC,UAAU,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,KAAK;AAAA,QAC7D;AACA,cAAM,kBAAkB,mBAAmB,sBAAsB,cAAc;AAE/E;AAAA,UACC;AAAA,UACA;AAAA,UACA;AAAA,UACA,cAAc;AAAA,QACf;AAEA,eAAO,EAAE,UAAU,qBAAqB;AAAA,MACzC,SAAS,OAAO;AACf,4BAAoB,cAAc;AAClC,cAAM;AAAA,MACP;AAAA,IACD;AAAA,IACA,GAAG;AAAA,EACJ,CAAC;AACF;AAEA,SAAS,mBAAmB,mBAA6C,gBAAyB;AACjG,MAAI,kBAAkB,WAAW,GAAG;AACnC,WAAO;AAAA,EACR;AAEA,MAAI,gBAAgB;AACnB,UAAM,kBAAkB,kBAAkB,KAAK,CAAC,YAAY,QAAQ,YAAY,cAAc;AAC9F,WAAO,mBAAmB,kBAAkB,CAAC;AAAA,EAC9C;AAEA,SAAO,kBAAkB,CAAC;AAC3B;;;AI7EO,SAAS,aAAa;AAC5B,SAAO,eAAe,CAAC,UAAU,MAAM,OAAO;AAC/C;;;ACDA,6BAA2D;AAEpD,SAAS,qBACf,kBACA,cACC;AACD,QAAM,iBAAa,mCAAW;AAC9B,QAAM,UAAU,WAAW,IAAI;AAE/B,QAAM,aAAa,QAAQ;AAAA,IAC1B,CAAC,eACA,uDAA+B,MAAM,MAAM,CAAC,gBAAgB,aAAa,MAAM;AAAA,EACjF;AAEA,SAAO;AAAA;AAAA,IAEN,GAAI,iBACF,IAAI,CAAC,SAAS,WAAW,KAAK,CAAC,WAAW,OAAO,SAAS,IAAI,CAAC,EAC/D,OAAO,OAAO;AAAA;AAAA,IAGhB,GAAG,WAAW,OAAO,CAAC,WAAW,CAAC,iBAAiB,SAAS,OAAO,IAAI,CAAC;AAAA,EACzE;AACD;AAEO,SAAS,0BAA0B,QAAiB;AAC1D,SAAO,QAAQ,MAAM,QAAQ;AAC9B;;;AC1BG;AAHI,SAAS,SAAS,OAA8B;AACtD,SACC,4CAAC,SAAI,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAO,OAAM,8BAA8B,GAAG,OAC9E;AAAA,IAAC;AAAA;AAAA,MACA,GAAE;AAAA,MACF,MAAK;AAAA;AAAA,EACN,GACD;AAEF;;;ACRG,IAAAC,sBAAA;AAHI,SAAS,UAAU,OAA8B;AACvD,SACC,6CAAC,SAAI,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAO,OAAM,8BAA8B,GAAG,OAC9E;AAAA,IAAC;AAAA;AAAA,MACA,GAAE;AAAA,MACF,MAAK;AAAA;AAAA,EACN,GACD;AAEF;;;ACXA,wBAAqB;AAErB,IAAAC,gBAA2B;;;ACFpB,IAAM,yBAAyB;AAE/B,IAAM,6BAA6B,IAAI,sBAAsB;AAE7D,IAAM,qBAAqB,EAAE,CAAC,sBAAsB,GAAG,GAAG;;;ADEjE,6BAAO;AAUN,IAAAC,sBAAA;AAJM,IAAM,kBAAc,0BAGzB,CAAC,EAAE,UAAU,GAAG,MAAM,GAAG,iBAC1B,6CAAC,0BAAK,KAAK,cAAe,GAAG,OAAQ,GAAG,oBACtC,UACF,CACA;AACD,YAAY,cAAc;;;AEpB1B,IAAAC,qBAAqB;AACrB,kBAAiB;AACjB,IAAAC,gBAA2B;;;ACJ3B,6BAA0C;AACnC,IAAI,sBAAkB,uBAAAC,iBAAO,EAAC,kBAAiB,qBAAoB,mBAAkB,EAAC,MAAK,EAAC,IAAG,6CAA4C,IAAG,6CAA4C,IAAG,6CAA4C,IAAG,4CAA2C,GAAE,QAAO,EAAC,QAAO,mDAAkD,MAAK,gDAA+C,GAAE,UAAS,EAAC,MAAK,kDAAiD,EAAC,GAAE,iBAAgB,EAAC,MAAK,MAAK,QAAO,OAAM,GAAE,kBAAiB,CAAC,EAAC,CAAC;;;AD0C9f,IAAAC,sBAAA;AApB1B,IAAM,cAAU;AAAA,EACf,CACC;AAAA,IACC;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,IAAI,MAAM;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACJ,GACA,iBACI;AACJ,WACC;AAAA,MAAC;AAAA;AAAA,QACC,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,eAAW,YAAAC,SAAK,gBAAgB,EAAE,MAAM,QAAQ,SAAS,CAAC,GAAG,SAAS;AAAA,QAErE,oBAAU,WAAW,6CAAC,OAAK,UAAS;AAAA;AAAA,IACtC;AAAA,EAEF;AACD;AACA,QAAQ,cAAc;;;AE9CtB,IAAAC,qBAAqB;AACrB,IAAAC,eAAiB;AAEjB,IAAAC,gBAA2B;;;ACLpB,IAAI,YAAY;;;ADiBd,IAAAC,sBAAA;AAHT,IAAM,iBAAa;AAAA,EAClB,CAAC,EAAE,WAAW,UAAU,OAAO,GAAG,MAAM,GAAG,iBAAiB;AAC3D,UAAM,OAAO,UAAU,0BAAO;AAC9B,WAAO,6CAAC,QAAM,GAAG,OAAO,eAAW,aAAAC,SAAY,WAAW,SAAS,GAAG,KAAK,cAAc;AAAA,EAC1F;AACD;AACA,WAAW,cAAc;;;AEpBlB,IAAI,sBAAsB;AAC1B,IAAI,uBAAuB;AAC3B,IAAI,UAAU;AACd,IAAI,UAAU;AACd,IAAI,wBAAwB;AAC5B,IAAI,QAAQ;AACZ,IAAI,gBAAgB;AACpB,IAAI,sBAAsB;AAC1B,IAAI,sCAAsC;AAC1C,IAAI,oBAAoB;AACxB,IAAI,sBAAsB;;;ACRjC,IAAAC,qBAAqB;AACrB,IAAAC,eAAiB;AAEjB,IAAAC,gBAA2B;;;ACL3B,IAAAC,0BAA0C;AACnC,IAAI,qBAAiB,wBAAAC,iBAAO,EAAC,kBAAiB,kCAAiC,mBAAkB,EAAC,SAAQ,EAAC,SAAQ,kDAAiD,SAAQ,iDAAgD,GAAE,MAAK,EAAC,IAAG,0CAAyC,IAAG,yCAAwC,EAAC,GAAE,iBAAgB,EAAC,SAAQ,WAAU,MAAK,KAAI,GAAE,kBAAiB,CAAC,EAAC,CAAC;;;ADkBpY,IAAAC,sBAAA;AAJH,IAAM,aAAS;AAAA,EACd,CAAC,EAAE,WAAW,SAAS,MAAM,UAAU,OAAO,GAAG,MAAM,GAAG,iBAAiB;AAC1E,UAAM,OAAO,UAAU,0BAAO;AAC9B,WACC;AAAA,MAAC;AAAA;AAAA,QACC,GAAG;AAAA,QACJ,eAAW,aAAAC,SAAK,eAAe,EAAE,SAAS,KAAK,CAAC,GAAG,SAAS;AAAA,QAC5D,KAAK;AAAA;AAAA,IACN;AAAA,EAEF;AACD;AACA,OAAO,cAAc;;;AEzBrB,IAAAC,qBAAqB;AACrB,IAAAC,eAAiB;AACjB,IAAAC,gBAA2B;;;ACJ3B,IAAAC,0BAA0C;AACnC,IAAI,mBAAe,wBAAAC,iBAAO,EAAC,kBAAiB,iBAAgB,mBAAkB,EAAC,MAAK,EAAC,IAAG,qCAAoC,GAAE,QAAO,EAAC,QAAO,4CAA2C,QAAO,4CAA2C,MAAK,yCAAwC,GAAE,OAAM,EAAC,OAAM,0CAAyC,QAAO,0CAAyC,GAAE,MAAK,EAAC,MAAK,uCAAsC,EAAC,GAAE,iBAAgB,EAAC,MAAK,MAAK,QAAO,SAAQ,GAAE,kBAAiB,CAAC,EAAC,CAAC;;;ADwCle,IAAAC,sBAAA;AArB1B,IAAM,WAAO;AAAA,EACZ,CACC;AAAA,IACC;AAAA,IACA;AAAA,IACA,UAAU;AAAA,IACV,IAAI,MAAM;AAAA,IACV;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACJ,GACA,iBACI;AACJ,WACC;AAAA,MAAC;AAAA;AAAA,QACC,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,eAAW,aAAAC,SAAK,aAAa,EAAE,MAAM,QAAQ,OAAO,KAAK,CAAC,GAAG,SAAS;AAAA,QAErE,oBAAU,WAAW,6CAAC,OAAK,UAAS;AAAA;AAAA,IACtC;AAAA,EAEF;AACD;AACA,KAAK,cAAc;;;AE9CZ,IAAI,mBAAmB;AACvB,IAAIC,aAAY;AAChB,IAAI,uBAAuB;AAC3B,IAAIC,SAAQ;AACZ,IAAI,aAAa;;;ACmBpB,IAAAC,sBAAA;AARG,SAAS,iBAAiB;AAAA,EAChC;AAAA,EACA;AAAA,EACA;AACD,GAA0B;AACzB,SACC,8CAAC,SAAI,WAAkBC,YACrB;AAAA,mBAAe,QACf;AAAA,MAAC;AAAA;AAAA,QACA,WAAkB;AAAA,QAClB,KAAK,eAAe;AAAA,QACpB,KAAK,GAAG,eAAe,IAAI;AAAA;AAAA,IAC5B;AAAA,IAED,6CAAC,SAAI,WAAkBC,QACtB,wDAAC,WAAQ,IAAG,MAAK,MAAK,MAAK;AAAA;AAAA,MACjB,eAAe;AAAA,OACzB,GACD;AAAA,IACA,6CAAC,SAAI,WAAkB,kBACrB,+BACA,6CAAC,QAAK,OAAM,UAAS,+BAAiB,IAEtC,6CAAC,QAAK,OAAM,SAAQ,iDAAmC,GAEzD;AAAA,IACC,qBACA,6CAAC,SAAI,WAAkB,sBACtB,uDAAC,UAAO,MAAK,UAAS,SAAQ,WAAU,SAAS,MAAM,kBAAkB,cAAc,GAAG,8BAE1F,GACD,IACG;AAAA,KACL;AAEF;;;AClDO,IAAIC,aAAY;;;ACarB,IAAAC,sBAAA;AAFK,SAAS,YAAY,EAAE,OAAAC,QAAO,SAAS,GAAqB;AAClE,SACC,8CAAC,aAAQ,WAAkBC,YAC1B;AAAA,iDAAC,WAAQ,IAAG,MAAK,MAAK,MAAK,QAAO,UAChC,UAAAD,QACF;AAAA,IACA,6CAAC,QAAK,QAAO,UAAS,OAAM,SAC1B,UACF;AAAA,KACD;AAEF;;;ACtBO,IAAIE,aAAY;AAChB,IAAIC,WAAU;AACd,IAAI,yBAAyB;;;ACQjC,IAAAC,uBAAA;AAHI,SAAS,iBAAiB;AAChC,SACC,+CAAC,SAAI,WAAkBC,YACtB;AAAA,kDAAC,WAAQ,IAAG,MAAK,kCAAoB;AAAA,IACrC,+CAAC,SAAI,WAAkBC,UACtB;AAAA,oDAAC,eAAY,OAAM,oCAAmC,iFAEtD;AAAA,MACA,8CAAC,eAAY,OAAM,6BAA4B,+GAG/C;AAAA,MACA,8CAAC,eAAY,OAAM,wBAAuB,gGAE1C;AAAA,MACA,8CAAC,SAAI,WAAkB,wBACtB,wDAAC,UAAO,SAAQ,WAAU,SAAO,MAChC;AAAA,QAAC;AAAA;AAAA,UACA,MAAK;AAAA,UACL,QAAO;AAAA,UACP,KAAI;AAAA,UACJ;AAAA;AAAA,MAED,GACD,GACD;AAAA,OACD;AAAA,KACD;AAEF;;;ACpCO,IAAIC,aAAY;AAChB,IAAIC,WAAU;;;ACQlB,IAAAC,uBAAA;AAHI,SAAS,gBAAgB;AAC/B,SACC,+CAAC,SAAI,WAAkBC,YACtB;AAAA,kDAAC,WAAQ,IAAG,MAAK,8BAAgB;AAAA,IACjC,+CAAC,SAAI,WAAkBC,UACtB;AAAA,oDAAC,eAAY,OAAM,cAAa,qHAGhC;AAAA,MACA,8CAAC,eAAY,OAAM,6BAA4B,sFAE/C;AAAA,OACD;AAAA,KACD;AAEF;;;ACfE,IAAAC,uBAAA;AAFK,SAAS,QAAQ,OAA8B;AACrD,SACC,+CAAC,SAAI,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAO,OAAM,8BAA8B,GAAG,OAC9E;AAAA,kDAAC,UAAK,OAAO,IAAI,QAAQ,IAAI,IAAI,GAAG,MAAK,WAAU;AAAA,IACnD;AAAA,MAAC;AAAA;AAAA,QACA,UAAS;AAAA,QACT,UAAS;AAAA,QACT,GAAE;AAAA,QACF,MAAK;AAAA;AAAA,IACN;AAAA,KACD;AAEF;;;AChBO,IAAIC,aAAY;;;ACEvB,IAAAC,eAAqB;;;ACFd,IAAIC,aAAY;AAChB,IAAI,qBAAqB;AACzB,IAAIC,cAAa;AACjB,IAAI,aAAa;;;ADerB,IAAAC,uBAAA;AAHI,SAAS,eAAe,EAAE,MAAM,MAAM,SAAS,aAAa,MAAM,GAAwB;AAChG,SACC,8CAAC,QAAG,WAAkBC,YACrB;AAAA,IAAC;AAAA;AAAA,MACA,eAAW,mBAAY,YAAY,EAAE,CAAQ,kBAAkB,GAAG,WAAW,CAAC;AAAA,MAC9E,MAAK;AAAA,MACL;AAAA,MAEC;AAAA,gBAAQ,OAAO,SAAS,WACxB,8CAAC,SAAI,WAAkBC,aAAY,KAAK,MAAM,KAAK,GAAG,IAAI,SAAS,IAEnE;AAAA,QAED,8CAAC,WAAQ,MAAK,MAAK,UAAQ,MAAC,SAAO,MAClC,wDAAC,SAAK,gBAAK,GACZ;AAAA;AAAA;AAAA,EACD,GACD;AAEF;;;AERK,IAAAC,uBAAA;AAVE,SAAS,WAAW;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,GAAoB;AACnB,SACC,8CAAC,QAAG,WAAkBC,YACpB,kBAAQ,SAAS,IACjB,QAAQ,IAAI,CAAC,WACZ;AAAA,IAAC;AAAA;AAAA,MAEA,MAAM,OAAO;AAAA,MACb,MAAM,OAAO;AAAA,MACb,YAAY,0BAA0B,MAAM,MAAM;AAAA,MAClD,SAAS,MAAM,SAAS,MAAM;AAAA;AAAA,IAJzB,0BAA0B,MAAM;AAAA,EAKtC,CACA,IAED;AAAA,IAAC;AAAA;AAAA,MACA,MAAK;AAAA,MACL,MAAM,8CAAC,WAAQ;AAAA,MACf,SAAS;AAAA,MACT,YAAU;AAAA;AAAA,EACX,GAEF;AAEF;;;AlCgDkB,IAAAC,uBAAA;AAxCX,SAAS,aAAa;AAAA,EAC5B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,eAAe;AAChB,GAAsB;AACrB,QAAM,CAAC,aAAa,YAAY,QAAI,wBAAS,QAAQ,WAAW;AAChE,QAAM,CAAC,aAAa,cAAc,QAAI,wBAA2B;AACjE,QAAM,CAAC,gBAAgB,iBAAiB,QAAI,wBAAqC;AAEjF,QAAM,UAAU,WAAW,EAAE,OAAO,YAAY;AAChD,QAAM,EAAE,QAAQ,QAAQ,IAAI,iBAAiB;AAE7C,QAAM,iBAAiB,MAAM;AAC5B,sBAAkB,MAAS;AAC3B,mBAAe,MAAS;AAAA,EACzB;AAEA,QAAM,mBAAmB,CAACC,UAAkB;AAC3C,QAAI,CAACA,OAAM;AACV,qBAAe;AAAA,IAChB;AACA,iBAAaA,KAAI;AACjB,mBAAeA,KAAI;AAAA,EACpB;AAEA,QAAM,gBAAgB,CAAC,WAAuC;AAC7D,mBAAe,mBAAmB;AAClC;AAAA,MACC,EAAE,OAAO;AAAA,MACT;AAAA,QACC,WAAW,MAAM,iBAAiB,KAAK;AAAA,MACxC;AAAA,IACD;AAAA,EACD;AAEA,MAAI;AACJ,UAAQ,aAAa;AAAA,IACpB,KAAK;AACJ,qBAAe,8CAAC,iBAAc;AAC9B;AAAA,IACD,KAAK;AACJ,qBAAe,8CAAC,kBAAe;AAC/B;AAAA,IACD,KAAK;AACJ,qBACC;AAAA,QAAC;AAAA;AAAA,UACA;AAAA,UACA,oBAAoB;AAAA,UACpB,mBAAmB;AAAA;AAAA,MACpB;AAED;AAAA,IACD;AACC,qBAAe,8CAAC,iBAAc;AAAA,EAChC;AAEA,SACC,+CAAQ,aAAP,EAAY,MAAM,QAAQ,aAAa,cAAc,kBACrD;AAAA,kDAAQ,gBAAP,EAAe,SAAO,MAAE,mBAAQ;AAAA,IACjC,8CAAQ,eAAP,EACA,wDAAC,eACA,wDAAQ,gBAAP,EAAe,WAAkB,SACjC,yDAAQ,gBAAP,EAAe,WAAkB,SAAS,oBAAkB,QAC5D;AAAA;AAAA,QAAC;AAAA;AAAA,UACA,eAAW,aAAAC,SAAY,qBAAqB;AAAA,YAC3C,CAAQ,mCAAmC,GAAG,CAAC,CAAC;AAAA,UACjD,CAAC;AAAA,UAED;AAAA,2DAAC,SAAI,WAAkB,mBACtB;AAAA,4DAAQ,cAAP,EAAa,WAAkB,OAAO,SAAO,MAC7C,wDAAC,WAAQ,IAAG,MAAK,8BAAgB,GAClC;AAAA,cACA;AAAA,gBAAC;AAAA;AAAA,kBACA;AAAA,kBACA,oBAAoB,0BAA0B,cAAc;AAAA,kBAC5D,oBAAoB,MAAM,eAAe,iBAAiB;AAAA,kBAC1D,UAAU,CAAC,WAAW;AACrB,wBACC,0BAA0B,cAAc,MACxC,0BAA0B,MAAM,GAC/B;AACD,wCAAkB,MAAM;AACxB,oCAAc,MAAM;AAAA,oBACrB;AAAA,kBACD;AAAA;AAAA,cACD;AAAA,eACD;AAAA,YACA;AAAA,cAAC;AAAA;AAAA,gBACA,WAAkB;AAAA,gBAClB,SAAS,MAAM,eAAe,kBAAkB;AAAA,gBAChD,MAAK;AAAA,gBACL;AAAA;AAAA,YAED;AAAA;AAAA;AAAA,MACD;AAAA,MACA;AAAA,QAAC;AAAA;AAAA,UACA,eAAW,aAAAA,SAAY,eAAe;AAAA,YACrC,CAAQ,qBAAqB,GAAG,CAAC,CAAC;AAAA,UACnC,CAAC;AAAA,UAED;AAAA,0DAAC,SAAI,WAAkB,qBACtB,wDAAC,cAAW,MAAK,UAAS,cAAW,QAAO,SAAS,MAAM,eAAe,GACzE,wDAAC,YAAS,GACX,GACD;AAAA,YACC;AAAA;AAAA;AAAA,MACF;AAAA,MACA,8CAAQ,cAAP,EAAa,WAAkB,sBAAsB,SAAO,MAC5D,wDAAC,cAAW,MAAK,UAAS,cAAW,SACpC,wDAAC,aAAU,GACZ,GACD;AAAA,OACD,GACD,GACD,GACD;AAAA,KACD;AAEF;;;AmCnKO,SAAS,oBAA0C;AACzD,SAAO,eAAe,CAAC,UAAU,MAAM,cAAc;AACtD;;;ACTA,mBAA8B;AAE9B,mBAA8B;AAC9B,IAAAC,eAAiB;;;ACGjB,IAAAC,sBAAyD;AACzD,IAAAC,iBAAwB;;;ACNxB,IAAAC,iBAA2B;;;ACD3B,oBAAuD;AAEvD,IAAAC,gBAAiD;AAyFzC,IAAAC,uBAAA;AAxED,IAAM,uBAAmB,6BAA+C,IAAI;AAkBnF,IAAM,mBAAmB;AAAA,EACxB,UAAU,EAAE,SAAK,8BAAe,UAAU,EAAE;AAC7C;AAEA,IAAM,wBAAwB,SAAS,aACtC,OACA,QACC;AACD,UAAI,2BAAY,MAAM,GAAG;AACxB,WAAO;AAAA,EACR;AAEA,SAAO,IAAI,wBAAU,MAAM;AAC5B;AAEO,SAAS,kBAA4C,OAAkC;AAC7F,QAAM,EAAE,iBAAiB,SAAS,SAAS,IAAI;AAC/C,QAAM,WAAY,MAAM,YAAY;AACpC,QAAMC,gBACJ,MAAM,gBAAiD;AAEzD,QAAM,CAAC,iBAAiB,kBAAkB,QAAI;AAAA,IAC7C,MAAM,WAAW,MAAM,kBAAmB,OAAO,KAAK,QAAQ,EAAE,CAAC;AAAA,EAClE;AAEA,QAAM,iBAAiB,MAAM,WAAW;AAExC,QAAM,aAAS,uBAAQ,MAAM;AAC5B,WAAOA,cAAa,gBAAgB,SAAS,cAAc,CAAC;AAAA,EAC7D,GAAG,CAACA,eAAc,gBAAgB,QAAQ,CAAC;AAE3C,QAAM,UAAM,uBAAQ,MAAgC;AACnD,WAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA,SAAS;AAAA,MACT,QACC,SAAS,cAAc,aAAa,0BACjC,OACC,SAAS,cAAc;AAAA,MAC5B,eAAe,CAAC,eAAe;AAC9B,YAAI,mBAAmB,YAAY;AAClC;AAAA,QACD;AAEA,YAAI,CAAC,WAAW,eAAe,iBAAiB;AAC/C,6BAAmB,UAAU;AAAA,QAC9B;AAEA,0BAAkB,UAAU;AAAA,MAC7B;AAAA,IACD;AAAA,EACD,GAAG,CAAC,QAAQ,UAAU,iBAAiB,gBAAgB,SAAS,eAAe,CAAC;AAEhF,SAAO,8CAAC,iBAAiB,UAAjB,EAA0B,OAAO,KAAM,UAAS;AACzD;;;ADvFO,SAAS,sBAAsB;AACrC,QAAM,gBAAY,2BAAW,gBAAgB;AAE7C,MAAI,CAAC,WAAW;AACf,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AAEO,SAAS,eAA0B;AACzC,SAAO,oBAAoB,EAAE;AAC9B;;;ADgCO,SAAS,kBAAiD;AAAA,EAChE;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,GAAgC;AAC/B,aAAO,kCAAyC;AAAA,IAC/C,GAAG;AAAA,IACH,UAAU,CAAC,SAAS,QAAQ,MAAM;AAAA,IAClC,SAAS,YAAY;AACpB,aAAO,MAAM,OAAO,MAAM,EAAE,MAAe;AAAA,IAC5C;AAAA,EACD,CAAC;AACF;AAEO,SAAS,qBAIZ,MAG4B;AAC/B,QAAM,CAAC,QAAQ,QAAQ,EAAE,WAAW,CAAC,GAAG,GAAG,QAAQ,IAAI,CAAC,CAAC,IAAI;AAM7D,QAAM,aAAa,oBAAoB;AAEvC,aAAO,8BAAS;AAAA,IACf,GAAG;AAAA,IACH,UAAU,CAAC,WAAW,SAAS,QAAQ,QAAQ,GAAG,QAAQ;AAAA,IAC1D,SAAS,YAAY;AACpB,aAAO,MAAM,WAAW,OAAO,MAAM,EAAE,MAAe;AAAA,IACvD;AAAA,EACD,CAAC;AACF;AAEO,SAAS,6BAIZ,MAGF;AACD,QAAM,CAAC,QAAQ,QAAQ,UAAU,CAAC,CAAC,IAAI;AAMvC,QAAM,aAAa,oBAAoB;AAEvC,QAAM,YAAQ,wBAAQ,MAAM;AAC3B,WAAO,kBAAqB;AAAA,MAC3B,QAAQ,WAAW;AAAA,MACnB,SAAS,WAAW;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACD,CAAC;AAAA,EACF,GAAG,CAAC,WAAW,QAAQ,WAAW,SAAS,QAAQ,QAAQ,OAAO,CAAC;AAEnE,aAAO,sCAAiB,KAAK;AAC9B;;;AGlHO,SAAS,oBACf,SACA,SAIuC;AACvC,SAAO;AAAA,IACN;AAAA,IACA;AAAA,MACC;AAAA,MACA,OAAO;AAAA,IACR;AAAA,IACA;AAAA,MACC,GAAG;AAAA,MACH,sBAAsB;AAAA,MACtB,OAAO;AAAA,MACP,QAAQ,CAAC,SAAU,KAAK,KAAK,SAAS,IAAI,KAAK,KAAK,CAAC,IAAI;AAAA,MACzD,SAAS,CAAC,CAAC,WAAW,SAAS,YAAY;AAAA,IAC5C;AAAA,EACD;AACD;;;ACnBO,SAAS,cAAwC;AACvD,SAAO,eAAe,CAAC,UAAU,MAAM,QAAQ;AAChD;;;ACRA,IAAAC,sBAA4B;;;ACErB,IAAM,0BAAN,cAAsC,MAAM;AAAC;AAO7C,IAAM,+BAAN,cAA2C,MAAM;AAAC;AAKlD,IAAM,iCAAN,cAA6C,MAAM;AAAC;AAKpD,IAAM,6BAAN,cAAyC,MAAM;AAAC;;;ACfhD,SAAS,mBAAmB;AAClC,QAAM,gBAAgB,eAAe,CAAC,UAAU,MAAM,aAAa;AACnE,QAAMC,oBAAmB,eAAe,CAAC,UAAU,MAAM,gBAAgB;AACzE,QAAM,mBAAmB,eAAe,CAAC,UAAU,MAAM,gBAAgB;AAEzE,UAAQA,mBAAkB;AAAA,IACzB,KAAK;AACJ,aAAO;AAAA,QACN,kBAAAA;AAAA,QACA,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,kBAAkB,CAAC;AAAA,MACpB;AAAA,IACD,KAAK;AACJ,aAAO;AAAA,QACN,kBAAAA;AAAA,QACA,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,kBAAkB,CAAC;AAAA,MACpB;AAAA,IACD,KAAK,aAAa;AACjB,aAAO;AAAA,QACN,kBAAAA;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,aAAa;AAAA,QACb;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;;;AFtBO,SAAS,oBAAoB;AAAA,EACnC;AAAA,EACA,GAAG;AACJ,IAAwC,CAAC,GAIvC;AACD,QAAM,EAAE,cAAc,IAAI,iBAAiB;AAC3C,QAAM,wBAAwB,eAAe,CAAC,UAAU,MAAM,qBAAqB;AAEnF,aAAO,iCAAY;AAAA,IAClB,aAAa,mBAAmB,iBAAiB,WAAW;AAAA,IAC5D,YAAY,YAAY;AACvB,UAAI,CAAC,eAAe;AACnB,cAAM,IAAI,wBAAwB,yBAAyB;AAAA,MAC5D;AAEA,UAAI;AAIH,cAAM,cAAc,SAAS,qBAAqB,GAAG,WAAW;AAAA,MACjE,SAAS,OAAO;AACf,gBAAQ,MAAM,iEAAiE,KAAK;AAAA,MACrF;AAEA,4BAAsB;AAAA,IACvB;AAAA,IACA,GAAG;AAAA,EACJ,CAAC;AACF;;;AG/CA,IAAAC,sBAA4B;AAuBrB,SAAS,iBAAiB;AAAA,EAChC;AAAA,EACA,GAAG;AACJ,IAAqC,CAAC,GAIpC;AACD,QAAM,EAAE,cAAc,IAAI,iBAAiB;AAC3C,QAAM,qBAAqB,eAAe,CAAC,UAAU,MAAM,kBAAkB;AAE7E,aAAO,iCAAY;AAAA,IAClB,aAAa,mBAAmB,cAAc,WAAW;AAAA,IACzD,YAAY,OAAO,EAAE,QAAQ,MAAM;AAClC,UAAI,CAAC,eAAe;AACnB,cAAM,IAAI,wBAAwB,yBAAyB;AAAA,MAC5D;AAEA,YAAM,kBAAkB,cAAc,SAAS;AAAA,QAC9C,CAAC,kBAAkB,cAAc,YAAY,QAAQ;AAAA,MACtD;AACA,UAAI,CAAC,iBAAiB;AACrB,cAAM,IAAI;AAAA,UACT,2BAA2B,QAAQ,OAAO,oBAAoB,cAAc,IAAI;AAAA,QACjF;AAAA,MACD;AAEA,yBAAmB,eAAe;AAAA,IACnC;AAAA,IACA,GAAG;AAAA,EACJ,CAAC;AACF;;;AC1DO,IAAI,mBAAmB;AACvB,IAAI,gBAAgB;AACpB,IAAI,cAAc;AAClB,IAAI,WAAW;AACf,IAAI,YAAY;AAChB,IAAI,wBAAwB;;;ACEhC,IAAAC,uBAAA;AAHI,SAAS,UAAU,OAA8B;AACvD,SACC,8CAAC,SAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAQ,GAAG,OAC9E;AAAA,IAAC;AAAA;AAAA,MACA,MAAK;AAAA,MACL,GAAE;AAAA;AAAA,EACH,GACD;AAEF;;;ACNG,IAAAC,uBAAA;AAHI,SAAS,YAAY,OAA8B;AACzD,SACC,8CAAC,SAAI,OAAM,8BAA6B,OAAO,IAAI,QAAQ,IAAI,MAAK,QAAQ,GAAG,OAC9E;AAAA,IAAC;AAAA;AAAA,MACA,QAAO;AAAA,MACP,eAAc;AAAA,MACd,gBAAe;AAAA,MACf,aAAa;AAAA,MACb,GAAE;AAAA;AAAA,EACH,GACD;AAEF;;;AZkBK,IAAAC,uBAAA;AAZE,SAAS,oBAAoB,EAAE,eAAe,GAA6B;AACjF,QAAM,EAAE,QAAQ,iBAAiB,IAAI,oBAAoB;AAEzD,QAAM,EAAE,MAAM,OAAO,IAAI;AAAA,IACxB,eAAe,QAAQ,OAAO,eAAe;AAAA,EAC9C;AACA,QAAM,WAAW,YAAY;AAE7B,SACC,+CAAc,mBAAb,EAAkB,OAAO,OACzB;AAAA,kDAAC,eACA,wDAAc,sBAAb,EAAqB,SAAO,MAC5B,yDAAC,UAAO,MAAK,MAAK,WAAkB,kBACnC;AAAA,oDAAC,QAAK,MAAI,MAAC,QAAO,QAChB,yBAAe,SAAS,cAAU,4BAAc,eAAe,OAAO,GACxE;AAAA,MACA,8CAAC,eAAY;AAAA,OACd,GACD,GACD;AAAA,IACA,8CAAc,qBAAb,EACA,wDAAC,eAAY,WAAkB,eAC9B,yDAAc,sBAAb,EAAqB,WAAkB,aACtC;AAAA,eAAS,IAAI,CAAC,YACd;AAAA,QAAC;AAAA;AAAA,UAEA;AAAA,UACA,QAAQ,eAAe,YAAY,QAAQ;AAAA;AAAA,QAFtC,QAAQ;AAAA,MAGd,CACA;AAAA,MACD,8CAAc,wBAAb,EAAuB,WAAkB,WAAW;AAAA,MACrD;AAAA,QAAc;AAAA,QAAb;AAAA,UACA,eAAW,aAAAC,SAAY,QAAQ;AAAA,UAC/B,UAAU,MAAM,iBAAiB;AAAA,UACjC;AAAA;AAAA,MAED;AAAA,OACD,GACD,GACD;AAAA,KACD;AAEF;AAEO,SAAS,wBAAwB;AAAA,EACvC;AAAA,EACA;AACD,GAGG;AACF,QAAM,EAAE,QAAQ,cAAc,IAAI,iBAAiB;AACnD,QAAM,EAAE,MAAM,OAAO,IAAI,oBAAoB,QAAQ,QAAQ,OAAO,QAAQ,OAAO;AAEnF,SACC;AAAA,IAAc;AAAA,IAAb;AAAA,MACA,eAAW,aAAAA,SAAY,UAAiB,qBAAqB;AAAA,MAC7D,UAAU,MAAM,cAAc,EAAE,QAAQ,CAAC;AAAA,MAEzC;AAAA,sDAAC,QAAK,MAAI,MAAE,kBAAQ,SAAS,cAAU,4BAAc,QAAQ,OAAO,GAAE;AAAA,QACrE,SAAS,8CAAC,aAAU,IAAK;AAAA;AAAA;AAAA,EAC3B;AAEF;;;Aa7DE,IAAAC,uBAAA;AAPK,SAAS,cAAc;AAAA,EAC7B,cAAc;AAAA,EACd;AAAA,EACA,GAAG;AACJ,GAAuB;AACtB,QAAM,iBAAiB,kBAAkB;AACzC,SAAO,iBACN,8CAAC,uBAAoB,gBAAgC,IAErD;AAAA,IAAC;AAAA;AAAA,MACA;AAAA,MACA,SACC,8CAAC,eACA,wDAAC,UAAQ,GAAG,aAAc,uBAAY,GACvC;AAAA;AAAA,EAEF;AAEF;;;AC/BA,IAAAC,iBAAuB;;;ACFvB,IAAAC,sBAAyB;AACzB,IAAAC,iBAA0C;AAQnC,SAAS,uBAA0D;AACzE,QAAM,EAAE,aAAa,cAAc,IAAI,iBAAiB;AACxD,QAAM,qBAAqB,eAAe,CAAC,UAAU,MAAM,kBAAkB;AAC7E,QAAM,0BAA0B,eAAe,CAAC,UAAU,MAAM,uBAAuB;AACvF,QAAM,8BAA8B,eAAe,CAAC,UAAU,MAAM,2BAA2B;AAC/F,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,YAAY,IAAI,iBAAiB;AAEzC,QAAM,CAAC,YAAY,aAAa,QAAI,yBAAS,KAAK;AAClD,sCAAgB,MAAM;AACrB,kBAAc,IAAI;AAAA,EACnB,GAAG,CAAC,CAAC;AAEL,QAAM,EAAE,MAAM,QAAQ,QAAI,8BAAS;AAAA,IAClC,UAAU;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,QACC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa,QAAQ;AAAA,MACtB;AAAA,IACD;AAAA,IACA,SAAS,YAAY;AACpB,UAAI,CAAC,oBAAoB;AACxB,eAAO;AAAA,MACR;AAEA,UAAI,CAAC,2BAA2B,CAAC,+BAA+B,aAAa;AAC5E,eAAO;AAAA,MACR;AAEA,YAAM,SAAS,QAAQ;AAAA,QACtB,CAACC,YAAW,0BAA0BA,OAAM,MAAM;AAAA,MACnD;AACA,UAAI,QAAQ;AACX,cAAM,cAAc;AAAA,UACnB;AAAA,UACA,gBAAgB;AAAA,UAChB,QAAQ;AAAA,QACT,CAAC;AAAA,MACF;AAEA,aAAO;AAAA,IACR;AAAA,IACA,SAAS;AAAA,IACT,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,IACb,OAAO;AAAA,IACP,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,6BAA6B;AAAA,IAC7B,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,EACvB,CAAC;AAED,MAAI,CAAC,oBAAoB;AACxB,WAAO;AAAA,EACR;AAGA,MAAI,CAAC,YAAY;AAChB,WAAO;AAAA,EACR;AAEA,MAAI,aAAa;AAChB,WAAO;AAAA,EACR;AAEA,MAAI,CAAC,yBAAyB;AAC7B,WAAO;AAAA,EACR;AAEA,SAAO,UAAU,cAAe,QAAQ;AACzC;;;ACxFA,IAAAC,uBAAoC;AACpC,IAAAC,iBAAgC;AAOzB,SAAS,eAAe,QAA4B;AAC1D,sCAAgB,MAAM;AACrB,QAAI,CAAC,QAAQ,MAAM;AAClB;AAAA,IACD;AAEA,QAAI;AACJ,QAAI,YAAY;AAEhB,QAAI;AACH,YAAM,aAAS,0CAAoB,OAAO,MAAM;AAAA,QAC/C,QAAQ,OAAO;AAAA,MAChB,CAAC;AAED,UAAI,aAAa,QAAQ;AACxB,kBAAU,OAAO;AAAA,MAClB,WAAW,QAAQ;AAClB,eAAO,WAAW;AAAA,MACnB;AAAA,IACD,SAAS,OAAO;AACf,cAAQ,MAAM,oCAAoC,KAAK;AAAA,IACxD;AAEA,WAAO,MAAM;AACZ,kBAAY;AACZ,UAAI,QAAS,SAAQ;AAAA,IACtB;AAAA,EACD,GAAG,CAAC,QAAQ,MAAM,QAAQ,MAAM,CAAC;AAClC;;;ACnCA,qBAA+B;AAC/B,0BAA4B;AAC5B,IAAAC,gBAAyB;AAczB,IAAAC,0BAA8D;AAC9D,IAAAC,iBAA0B;AAI1B,IAAM,cAAc;AAEb,SAAS,sBAAsB,SAAkB;AACvD,QAAM,YAAY,aAAa;AAE/B,gCAAU,MAAM;AACf,QAAI,CAAC,SAAS;AACb;AAAA,IACD;AACA,UAAM,aAAa,2BAA2B,SAAS;AACvD,WAAO;AAAA,EACR,GAAG,CAAC,SAAS,SAAS,CAAC;AACxB;AAEA,SAAS,2BAA2B,WAAsB;AAvC1D;AAwCC,QAAM,iBAAa,oCAAW;AAC9B,QAAM,oBAAoB,WAAW,IAAI;AAEzC,MAAI,kBAAkB,KAAK,CAAC,WAAW,OAAO,SAAS,WAAW,GAAG;AACpE,YAAQ;AAAA,MACP;AAAA,IACD;AACA;AAAA,EACD;AAEA,UAAQ;AAAA,IACP;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,8BAAe;AACnC,QAAM,UAAU,IAAI,8CAAsB;AAAA,IACzC,SAAS,QAAQ,aAAa,EAAE,aAAa;AAAA,IAC7C,WAAW,QAAQ,aAAa,EAAE,WAAW;AAAA,IAC7C,QAAQ,CAAC,aAAa;AAAA,IACtB,UAAU;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD,CAAC;AAAA,EAED,MAAM,mBAAqC;AAAA,IAA3C;AAuDC,8BAA8B,MAAM;AACnC,eAAO,MAAM;AAAA,QAAC;AAAA,MACf;AAEA,mCAAkC,YAAY;AAC7C,eAAO,EAAE,UAAU,KAAK,SAAS;AAAA,MAClC;AAEA,+CAAqD,OAAO,iBAAiB;AAC5E,cAAM,EAAE,OAAO,UAAU,IAAI,MAAM,QAAQ,oBAAoB,aAAa,OAAO;AACnF,eAAO,EAAE,OAAO,UAAU;AAAA,MAC3B;AAEA,gDAAuD,OAAO,qBAAqB;AAClF,cAAM,EAAE,OAAO,UAAU,IAAI,MAAM,iBAAiB,iBAAiB,KAAK;AAAA,UACzE,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT,CAAC;AAED,eAAO;AAAA,UACN,uBAAuB;AAAA,UACvB;AAAA,QACD;AAAA,MACD;AAEA,2CAA6C,OAAO,qBAAqB;AACxE,cAAM,EAAE,OAAO,UAAU,IAAI,MAAM,gCAAY;AAAA,UAC9C,MAAM,iBAAiB,YAAY,OAAO;AAAA,QAC3C,EAAE,KAAK;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT,CAAC;AAED,yBAAiB,QAAQ,eAAe;AAExC,eAAO;AAAA,UACN;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAEA,0DAA2E,OAC1E,qBACI;AACJ,cAAM,EAAE,OAAO,UAAU,IAAI,MAAM,iBAAiB,iBAAiB,KAAK;AAAA,UACzE,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT,CAAC;AAED,eAAO,UAAU,wBAAwB;AAAA,UACxC;AAAA,UACA,kBAAkB;AAAA,UAClB,SAAS,iBAAiB;AAAA,QAC3B,CAAC;AAAA,MACF;AAEA,qDAAiE,OAAO,qBAAqB;AAC5F,cAAM,EAAE,OAAO,UAAU,IAAI,MAAM,gCAAY;AAAA,UAC9C,MAAM,iBAAiB,YAAY,OAAO;AAAA,QAC3C,EAAE,KAAK;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT,CAAC;AAED,yBAAiB,QAAQ,eAAe;AAExC,cAAM,EAAE,YAAY,OAAO,IAAI,MAAM,UAAU,wBAAwB;AAAA,UACtE;AAAA,UACA,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR,gBAAgB;AAAA,UACjB;AAAA,QACD,CAAC;AAED,eAAO;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,UACA,aAAS,wBAAS,IAAI,WAAW,UAAW,CAAC;AAAA,QAC9C;AAAA,MACD;AAAA;AAAA,IAtIA,IAAI,UAAU;AACb,aAAO;AAAA,IACR;AAAA,IAEA,IAAI,OAAO;AACV,aAAO;AAAA,IACR;AAAA,IAEA,IAAI,OAAO;AACV,aAAO;AAAA,IACR;AAAA;AAAA,IAGA,IAAI,SAAS;AACZ,aAAO;AAAA,IACR;AAAA,IAEA,IAAI,WAAW;AACd,aAAO,CAAC,OAAO;AAAA,IAChB;AAAA,IAEA,IAAI,WAAyE;AAC5E,aAAO;AAAA,QACN,oBAAoB;AAAA,UACnB,SAAS;AAAA,UACT,SAAS,mBAAK;AAAA,QACf;AAAA,QACA,mBAAmB;AAAA,UAClB,SAAS;AAAA,UACT,IAAI,mBAAK;AAAA,QACV;AAAA,QACA,2BAA2B;AAAA,UAC1B,SAAS;AAAA,UACT,qBAAqB,mBAAK;AAAA,QAC3B;AAAA,QACA,4BAA4B;AAAA,UAC3B,SAAS;AAAA,UACT,sBAAsB,mBAAK;AAAA,QAC5B;AAAA,QACA,sCAAsC;AAAA,UACrC,SAAS;AAAA,UACT,gCAAgC,mBAAK;AAAA,QACtC;AAAA,QACA,uBAAuB;AAAA,UACtB,SAAS;AAAA,UACT,iBAAiB,mBAAK;AAAA,QACvB;AAAA,QACA,iCAAiC;AAAA,UAChC,SAAS;AAAA,UACT,2BAA2B,mBAAK;AAAA,QACjC;AAAA,MACD;AAAA,IACD;AAAA,EAmFD;AAjFC;AAIA;AAIA;AAKA;AAYA;AAgBA;AAeA;AA2BD,SAAO,WAAW,SAAS,IAAI,mBAAmB,CAAC;AACpD;;;AC3MA,IAAAC,iBAA0B;AAQnB,SAAS,6BAA6B;AAC5C,QAAM,EAAE,cAAc,IAAI,iBAAiB;AAC3C,QAAM,uBAAuB,eAAe,CAAC,UAAU,MAAM,oBAAoB;AAEjF,gCAAU,MAAM;AACf,UAAM,wBAAwB,eAAe,SAAS,iBAAiB,EAAE;AAAA,MACxE;AAAA,MACA,CAAC,EAAE,SAAS,MAAM;AAGjB,YAAI,UAAU;AACb,+BAAqB,QAAQ;AAAA,QAC9B;AAAA,MACD;AAAA,IACD;AACA,WAAO;AAAA,EACR,GAAG,CAAC,eAAe,UAAU,oBAAoB,CAAC;AACnD;;;ACxBA,IAAAC,0BAA2B;AAC3B,IAAAC,iBAA0B;AAQnB,SAAS,kBACf,kBACA,cACC;AACD,QAAM,sBAAsB,eAAe,CAAC,UAAU,MAAM,mBAAmB;AAC/E,QAAM,wBAAwB,eAAe,CAAC,UAAU,MAAM,qBAAqB;AAEnF,gCAAU,MAAM;AACf,UAAM,iBAAa,oCAAW;AAC9B,wBAAoB,qBAAqB,kBAAkB,YAAY,CAAC;AAExE,UAAM,0BAA0B,WAAW,GAAG,YAAY,MAAM;AAC/D,0BAAoB,qBAAqB,kBAAkB,YAAY,CAAC;AAAA,IACzE,CAAC;AAED,UAAM,4BAA4B,WAAW,GAAG,cAAc,CAAC,uBAAuB;AACrF;AAAA,QACC,qBAAqB,kBAAkB,YAAY;AAAA,QACnD;AAAA,MACD;AAAA,IACD,CAAC;AAED,WAAO,MAAM;AACZ,8BAAwB;AACxB,gCAA0B;AAAA,IAC3B;AAAA,EACD,GAAG,CAAC,kBAAkB,cAAc,qBAAqB,qBAAqB,CAAC;AAChF;;;ACnCO,IAAM,aAAwB;AAAA,EACpC,OAAO;AAAA,IACN,cAAc;AAAA,EACf;AAAA,EACA,kBAAkB;AAAA,IACjB,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,cAAc;AAAA,IACd,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,uBAAuB;AAAA,IACvB,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,EAClB;AAAA,EACA,cAAc;AAAA,IACb,eAAe;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACP,eAAe;AAAA,IACf,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACR,eAAe;AAAA,IACf,oBAAoB;AAAA,EACrB;AAAA,EACA,aAAa;AAAA,IACZ,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,EACP;AAAA,EACA,WAAW;AAAA,IACV,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACX,YACC;AAAA,IACD,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,EAChB;AACD;;;AC1DA,IAAAC,kBAA4B;AAE5B,wBAA2C;AA6CpC,SAAS,kBAAkB;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,GAAwB;AACvB,aAAO,6BAAwB;AAAA,QAC9B;AAAA,MACC,CAAC,KAAK,SAAS;AAAA,QACd;AAAA,QACA;AAAA,QACA,UAAU,CAAC;AAAA,QACX,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,6BAA6B;AAAA,QAC7B,yBAAyB;AAAA,QACzB,kBAAkB;AAAA,QAClB,kBAAkB,CAAC;AAAA,QACnB,oBAAoBC,mBAAkB;AACrC,cAAI,OAAO;AAAA,YACV,kBAAAA;AAAA,UACD,EAAE;AAAA,QACH;AAAA,QACA,mBAAmB,QAAQ,mBAAmB,iBAAiB,mBAAmB,CAAC,GAAG;AACrF,cAAI,OAAO;AAAA,YACV,UAAU;AAAA,YACV,eAAe;AAAA,YACf,gBAAgB;AAAA,YAChB,yBAAyB,0BAA0B,MAAM;AAAA,YACzD,6BAA6B,iBAAiB;AAAA,YAC9C,kBAAkB;AAAA,YAClB;AAAA,UACD,EAAE;AAAA,QACH;AAAA,QACA,wBAAwB;AACvB,cAAI,OAAO;AAAA,YACV,UAAU,CAAC;AAAA,YACX,eAAe;AAAA,YACf,gBAAgB;AAAA,YAChB,yBAAyB;AAAA,YACzB,6BAA6B;AAAA,YAC7B,kBAAkB;AAAA,YAClB,kBAAkB,CAAC;AAAA,UACpB,EAAE;AAAA,QACH;AAAA,QACA,mBAAmB,iBAAiB;AACnC,cAAI,OAAO;AAAA,YACV,gBAAgB;AAAA,YAChB,6BAA6B,gBAAgB;AAAA,UAC9C,EAAE;AAAA,QACH;AAAA,QACA,oBAAoB,gBAAgB;AACnC,cAAI,OAAO,EAAE,SAAS,eAAe,EAAE;AAAA,QACxC;AAAA,QACA,sBAAsB,gBAAgB,oBAAoB;AACzD,cAAI,uBAAuB,IAAI,EAAE,eAAe;AAC/C,gBAAI,OAAO;AAAA,cACV,SAAS;AAAA,cACT,UAAU,CAAC;AAAA,cACX,eAAe;AAAA,cACf,gBAAgB;AAAA,cAChB,yBAAyB;AAAA,cACzB,6BAA6B;AAAA,cAC7B,kBAAkB;AAAA,cAClB,kBAAkB,CAAC;AAAA,YACpB,EAAE;AAAA,UACH,OAAO;AACN,gBAAI,OAAO,EAAE,SAAS,eAAe,EAAE;AAAA,UACxC;AAAA,QACD;AAAA,QACA,qBAAqB,UAAU;AAC9B,gBAAM,iBAAiB,IAAI,EAAE;AAE7B,cAAI,OAAO;AAAA,YACV;AAAA,YACA,gBACE,kBACA,SAAS,KAAK,CAAC,EAAE,QAAQ,MAAM,YAAY,eAAe,OAAO,KAClE,SAAS,CAAC;AAAA,UACZ,EAAE;AAAA,QACH;AAAA,MACD;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,aAAS,qCAAkB,MAAM,OAAO;AAAA,QACxC,YAAY,CAAC,EAAE,yBAAyB,4BAA4B,OAAO;AAAA,UAC1E;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;;;AC5IA,qBAAiC;;;ACAjC,iBAA0C;AAE1C,IAAM,sBAAsB;AAAA,EAC3B,OAAO;AAAA,IACN,cAAc;AAAA,EACf;AAAA,EACA,kBAAkB;AAAA,IACjB,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,cAAc;AAAA,IACd,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,uBAAuB;AAAA,EACxB;AAAA,EACA,cAAc;AAAA,IACb,eAAe;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACP,eAAe;AAAA,IACf,eAAe;AAAA,IACf,MAAM;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACR,eAAe;AAAA,IACf,oBAAoB;AAAA,EACrB;AAAA,EACA,aAAa;AAAA,IACZ,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,EACP;AAAA,EACA,WAAW;AAAA,IACV,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,EAChB;AACD;AAgCO,IAAM,gBAAY;AAAA,EACxB;AAAA,EACA,CAAC,GAAG,SAAS,YAAY,KAAK,KAAK,GAAG,CAAC;AACxC;;;AD7EE,IAAAC,uBAAA;AANK,SAAS,oBAAoB,EAAE,MAAM,GAA6B;AACxE,QAAM,cAAc,MAAM,QAAQ,KAAK,IACpC,sBAAsB,KAAK,IAC3B,qBAAqB,KAAK;AAE7B,SACC;AAAA,IAAC;AAAA;AAAA,MAGA,YAAW;AAAA,MACX,MAAK;AAAA,MACL,yBAAyB;AAAA,QACxB,QAAQ;AAAA,MACT;AAAA;AAAA,EACD;AAEF;AAEA,SAAS,sBAAsB,QAAwB;AACtD,SAAO,OACL,IAAI,CAAC,EAAE,YAAY,UAAU,UAAU,MAAM;AAC7C,UAAM,cAAc,qBAAqB,SAAS;AAClD,UAAM,gCAAgC,WAAW,GAAG,QAAQ,IAAI,WAAW,KAAK;AAEhF,WAAO,aACJ,UAAU,UAAU,IAAI,6BAA6B,MACrD;AAAA,EACJ,CAAC,EACA,KAAK,GAAG;AACX;AAEA,SAAS,qBAAqB,OAAkB;AAC/C,SAAO,GAAG,0BAA0B,KAAK,mBAAmB,KAAK,CAAC;AACnE;AAEA,SAAS,mBAAmB,OAAkB;AAC7C,SAAO,OAAO,YAAQ,iCAAiB,WAAW,KAAK,CAAC,EACtD,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,KAAK,GAAG,EACxC,KAAK,EAAE;AACV;;;AR4BG,IAAAC,uBAAA;AAtBI,SAAS,eAAe;AAAA,EAC9B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,UAAU;AAAA,EACV,aAAa;AAAA,EACb,qBAAqB;AAAA,EACrB,cAAc;AAAA,EACd;AAAA,EACA,QAAQ;AAAA,EACR;AACD,GAAwB;AACvB,QAAM,eAAW;AAAA,IAChB,kBAAkB;AAAA,MACjB,oBAAoB;AAAA,MACpB,SAAS,qBAAqB,kBAAkB,YAAY;AAAA,MAC5D,SAAS,WAAW,oBAAoB;AAAA,MACxC;AAAA,IACD,CAAC;AAAA,EACF;AAEA,SACC,8CAAC,cAAc,UAAd,EAAuB,OAAO,SAAS,SACvC;AAAA,IAAC;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAGC;AAAA,gBAAQ,8CAAC,uBAAoB,OAAc,IAAK;AAAA,QAChD;AAAA;AAAA;AAAA,EACF,GACD;AAEF;AAOA,SAAS,wBAAwB;AAAA,EAChC,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,qBAAqB;AAAA,EACrB;AAAA,EACA;AACD,GAAiC;AAChC,oBAAkB,kBAAkB,YAAY;AAChD,6BAA2B;AAC3B,iBAAe,WAAW;AAC1B,wBAAsB,kBAAkB;AACxC,uBAAqB;AAErB,SAAO;AACR;;;AUtGO,SAAS,oBAId,eAAkB;AACnB,WAAS,mBAA2B;AACnC,UAAM,EAAE,OAAO,IAAI,oBAAoB;AAEvC,QAAI,CAAC,QAAQ;AACZ,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC1C;AAEA,WAAO;AAAA,EACR;AAEA,WAAS,sBAAiC;AACzC,UAAM,EAAE,UAAU,IAAI,iBAAiB;AAEvC,WAAQ,aAAa,CAAC;AAAA,EACvB;AAEA,WAAS,mBAA8C,MAAuB;AAC7E,UAAM,YAAY,oBAAoB;AAEtC,WAAO,UAAU,IAAI;AAAA,EACtB;AAEA,SAAO;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD;AACD;;;ACnCA,IAAAC,sBAAiC;AAyC1B,SAAS,0BAIf,QACA,QACA;AAAA,EACC,WAAW,CAAC;AAAA,EACZ,UAAU,CAAC,CAAC;AAAA,EACZ,GAAG;AACJ,IAAgD,CAAC,GACV;AACvC,QAAM,aAAa,oBAAoB;AAEvC,aAAO,sCAAiB;AAAA,IACvB,GAAG;AAAA,IACH,kBAAkB;AAAA,IAClB,UAAU,CAAC,WAAW,SAAS,QAAQ,QAAQ,GAAG,QAAQ;AAAA,IAC1D;AAAA,IACA,SAAS,CAAC,EAAE,UAAU,MACrB,WAAW,OAAO,MAAM,EAAE;AAAA,MACzB,GAAI,UAAU,CAAC;AAAA,MACf,QAAQ;AAAA,IACT,CAAU;AAAA,IACX,kBAAkB,CAAC,aAAc,SAAS,cAAe,SAAS,cAAc,OAAQ;AAAA,EACzF,CAAC;AACF;;;ACxEA,IAAAC,sBAA4B;AAUrB,SAAS,qBACf,QACA,UAA0C,CAAC,GACmD;AAC9F,QAAM,aAAa,oBAAoB;AAEvC,aAAO,iCAAY;AAAA,IAClB,GAAG;AAAA,IACH,YAAY,OAAO,WAAW;AAC7B,aAAO,MAAM,WAAW,OAAO,MAAM,EAAE,MAAe;AAAA,IACvD;AAAA,EACD,CAAC;AACF;;;ACtBA,IAAAC,sBAA2B;AAqCpB,SAAS,oBAGd;AAAA,EACD;AAAA,EACA;AACD,GAGY;AACX,QAAM,aAAa,oBAAoB;AAEvC,aAAO,gCAAW;AAAA,IACjB;AAAA,IACA,SAAS,QAAQ,IAAI,CAAC,UAAU;AAC/B,YAAM,EAAE,QAAQ,QAAQ,SAAS,EAAE,WAAW,CAAC,GAAG,GAAG,YAAY,IAAI,CAAC,EAAE,IAAI;AAE5E,aAAO;AAAA,QACN,GAAG;AAAA,QACH,UAAU,CAAC,WAAW,SAAS,QAAQ,QAAQ,GAAG,QAAQ;AAAA,QAC1D,SAAS,YAAY;AACpB,iBAAO,MAAM,WAAW,OAAO,MAAM,EAAE,MAAe;AAAA,QACvD;AAAA,MACD;AAAA,IACD,CAAC;AAAA,EACF,CAAC;AACF;;;AC/DA,IAAAC,gBAAyB;AAKzB,IAAAC,0BAAgC;AAEhC,IAAAC,uBAA4B;;;ACR5B,IAAAC,gBAAyB;AAGzB,IAAAC,sBAA4B;AAsCrB,SAAS,4BAA4B;AAAA,EAC3C;AAAA,EACA,GAAG;AACJ,IAAgD,CAAC,GAI/C;AACD,QAAM,EAAE,cAAc,IAAI,iBAAiB;AAC3C,QAAM,iBAAiB,kBAAkB;AAEzC,aAAO,iCAAY;AAAA,IAClB,aAAa,mBAAmB,yBAAyB,WAAW;AAAA,IACpE,YAAY,OAAO,EAAE,SAAS,QAAQ,eAAe,OAAO,CAAC,GAAG,UAAU,eAAe,MAAM;AAC9F,UAAI,CAAC,eAAe;AACnB,cAAM,IAAI,wBAAwB,yBAAyB;AAAA,MAC5D;AAEA,UAAI,CAAC,SAAS;AACb,cAAM,IAAI;AAAA,UACT;AAAA,QACD;AAAA,MACD;AAEA,YAAM,kCACL,cAAc,SAAS,8BAA8B;AAEtD,UAAI,iCAAiC;AACpC,eAAO,MAAM,gCAAgC,yBAAyB;AAAA,UACrE,SAAS,MAAM,QAAQ,OAAO,QAAI,wBAAS,IAAI,WAAW,OAAO,CAAC,IAAI;AAAA,UACtE;AAAA,UACA,OAAO,SAAS,eAAe,OAAO,CAAC;AAAA,QACxC,CAAC;AAAA,MACF;AAAA,IACD;AAAA,IACA,GAAG;AAAA,EACJ,CAAC;AACF;;;ADfO,SAAS,6BAEd;AAAA,EACD;AAAA,EACA;AAAA,EACA,GAAG;AACJ,IAAyD,CAAC,GAIxD;AACD,QAAM,EAAE,eAAe,iBAAiB,IAAI,iBAAiB;AAC7D,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,EAAE,QAAQ,QAAQ,IAAI,oBAAoB;AAChD,QAAM,EAAE,QAAQ,yBAAyB,IAAI,4BAA4B;AAEzE,QAAM,qBAOL,YACC,OAAO,EAAE,OAAO,UAAU,MAAM;AAChC,UAAM,EAAE,QAAQ,WAAW,IAAI,MAAM,OAAO,wBAAwB;AAAA,MACnE,kBAAkB;AAAA,MAClB;AAAA,MACA,SAAS;AAAA,QACR,gBAAgB;AAAA,MACjB;AAAA,IACD,CAAC;AAED,WAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA,aAAS,wBAAS,IAAI,WAAW,UAAW,CAAC;AAAA,MAC7C;AAAA,MACA;AAAA,IACD;AAAA,EACD;AAED,aAAO,kCAAY;AAAA,IAClB,aAAa,mBAAmB,0BAA0B,WAAW;AAAA,IACrE,YAAY,OAAO,EAAE,aAAa,GAAG,oBAAoB,MAAuB;AAC/E,UAAI,CAAC,eAAe;AACnB,cAAM,IAAI,wBAAwB,yBAAyB;AAAA,MAC5D;AAEA,YAAM,gBAAgB,oBAAoB,WAAW;AACrD,UAAI,CAAC,eAAe;AACnB,cAAM,IAAI;AAAA,UACT;AAAA,QACD;AAAA,MACD;AAEA,UACC,CAAC,cAAc,SAAS,qBAAqB,KAC7C,CAAC,cAAc,SAAS,0BAA0B,GACjD;AACD,cAAM,IAAI;AAAA,UACT;AAAA,QACD;AAAA,MACD;AAEA,YAAM,QAAQ,oBAAoB,SAAS,OAAO,OAAO;AACzD,YAAM,EAAE,WAAW,MAAM,IAAI,UAAM,yCAAgB,eAAe;AAAA,QACjE,GAAG;AAAA,QACH,aAAa;AAAA,UACZ,MAAM,SAAS;AACd,mBAAO,OAAO,gBAAgB,WAC3B,cACA,MAAM,YAAY,OAAO;AAAA,cACzB;AAAA,cACA;AAAA,YACD,CAAC;AAAA,UACJ;AAAA,QACD;AAAA,QACA,SAAS;AAAA,QACT;AAAA,MACD,CAAC;AAED,YAAM,SAAS,MAAM,mBAAmB,EAAE,OAAO,UAAU,CAAC;AAE5D,UAAI;AAEJ,UAAI,aAAa,UAAU,OAAO,SAAS,KAAK;AAC/C,kBAAU,OAAO,QAAQ;AAAA,MAC1B,WAAW,gBAAgB,QAAQ;AAClC,sBAAU,wBAAS,IAAI,WAAW,OAAO,UAAW,CAAC;AAAA,MACtD,OAAO;AACN,cAAM,IAAI,MAAM,kDAAkD;AAAA,MACnE;AAEA,+BAAyB,EAAE,SAAS,SAAS,eAAe,MAAM,CAAC;AAEnE,aAAO;AAAA,IACR;AAAA,IACA,GAAG;AAAA,EACJ,CAAC;AACF;;;AE9JA,IAAAC,uBAA4B;AAoCrB,SAAS,uBAAuB;AAAA,EACtC;AAAA,EACA,GAAG;AACJ,IAA2C,CAAC,GAI1C;AACD,QAAM,EAAE,cAAc,IAAI,iBAAiB;AAC3C,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,EAAE,QAAQ,IAAI,oBAAoB;AAExC,aAAO,kCAAY;AAAA,IAClB,aAAa,mBAAmB,oBAAoB,WAAW;AAAA,IAC/D,YAAY,OAAO,4BAA4B;AAC9C,UAAI,CAAC,eAAe;AACnB,cAAM,IAAI,wBAAwB,yBAAyB;AAAA,MAC5D;AAEA,YAAM,gBAAgB,wBAAwB,WAAW;AACzD,UAAI,CAAC,eAAe;AACnB,cAAM,IAAI;AAAA,UACT;AAAA,QACD;AAAA,MACD;AAEA,YAAM,6BAA6B,cAAc,SAAS,yBAAyB;AACnF,UAAI,4BAA4B;AAC/B,eAAO,MAAM,2BAA2B,oBAAoB;AAAA,UAC3D,GAAG;AAAA,UACH,SAAS;AAAA,UACT,OAAO,wBAAwB,SAAS,OAAO,OAAO;AAAA,QACvD,CAAC;AAAA,MACF;AAGA,YAAM,qBAAqB,cAAc,SAAS,iBAAiB;AACnE,UAAI,oBAAoB;AACvB,gBAAQ;AAAA,UACP;AAAA,QACD;AAEA,cAAM,EAAE,cAAc,UAAU,IAAI,MAAM,mBAAmB,YAAY;AAAA,UACxE,GAAG;AAAA,UACH,SAAS;AAAA,QACV,CAAC;AACD,eAAO,EAAE,OAAO,cAAc,UAAU;AAAA,MACzC;AAEA,YAAM,IAAI;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAAA,IACA,GAAG;AAAA,EACJ,CAAC;AACF;;;AC/FA,IAAAC,0BAAgC;AAGhC,IAAAC,uBAA4B;AA4CrB,SAAS,mBAAmB;AAAA,EAClC;AAAA,EACA,GAAG;AACJ,IAAuC,CAAC,GAItC;AACD,QAAM,EAAE,cAAc,IAAI,iBAAiB;AAC3C,QAAM,iBAAiB,kBAAkB;AACzC,QAAM,EAAE,QAAQ,QAAQ,IAAI,oBAAoB;AAEhD,QAAM,EAAE,QAAQ,yBAAyB,IAAI,4BAA4B;AAEzE,aAAO,kCAAY;AAAA,IAClB,aAAa,mBAAmB,gBAAgB,WAAW;AAAA,IAC3D,YAAY,OAAO,EAAE,aAAa,GAAG,oBAAoB,MAAM;AAC9D,UAAI,CAAC,eAAe;AACnB,cAAM,IAAI,wBAAwB,yBAAyB;AAAA,MAC5D;AAEA,YAAM,gBAAgB,oBAAoB,WAAW;AACrD,UAAI,CAAC,eAAe;AACnB,cAAM,IAAI;AAAA,UACT;AAAA,QACD;AAAA,MACD;AAEA,UACC,CAAC,cAAc,SAAS,qBAAqB,KAC7C,CAAC,cAAc,SAAS,0BAA0B,GACjD;AACD,cAAM,IAAI;AAAA,UACT;AAAA,QACD;AAAA,MACD;AAEA,YAAM,QAAQ,oBAAoB,SAAS,OAAO,OAAO;AACzD,YAAM,EAAE,OAAO,UAAU,IAAI,UAAM,yCAAgB,eAAe;AAAA,QACjE,GAAG;AAAA,QACH,aAAa;AAAA,UACZ,QAAQ,YAAY;AACnB,mBAAO,OAAO,gBAAgB,WAC3B,cACA,MAAM,YAAY,OAAO;AAAA,cACzB,kBAAkB,CAAC;AAAA,cACnB;AAAA,YACD,CAAC;AAAA,UACJ;AAAA,QACD;AAAA,QACA,SAAS;AAAA,QACT;AAAA,MACD,CAAC;AAED,aAAO;AAAA,QACN;AAAA,QACA;AAAA,QACA,0BAA0B,CAAC,YAAY;AACtC,mCAAyB;AAAA,YACxB;AAAA,YACA,SAAS;AAAA,YACT;AAAA,UACD,CAAC;AAAA,QACF;AAAA,MACD;AAAA,IACD;AAAA,IACA,GAAG;AAAA,EACJ,CAAC;AACF;", "names": ["import_clsx", "import_react", "import_react", "import_jsx_runtime", "import_react", "import_jsx_runtime", "import_react_slot", "import_react", "_7a468", "import_jsx_runtime", "clsx", "import_react_slot", "import_clsx", "import_react", "import_jsx_runtime", "clsx", "import_react_slot", "import_clsx", "import_react", "import_createRuntimeFn", "_7a468", "import_jsx_runtime", "clsx", "import_react_slot", "import_clsx", "import_react", "import_createRuntimeFn", "_7a468", "import_jsx_runtime", "clsx", "container", "title", "import_jsx_runtime", "container", "title", "container", "import_jsx_runtime", "title", "container", "container", "content", "import_jsx_runtime", "container", "content", "container", "content", "import_jsx_runtime", "container", "content", "import_jsx_runtime", "container", "import_clsx", "container", "walletIcon", "import_jsx_runtime", "container", "walletIcon", "import_jsx_runtime", "container", "import_jsx_runtime", "open", "clsx", "import_clsx", "import_react_query", "import_react", "import_react", "import_react", "import_jsx_runtime", "createClient", "import_react_query", "connectionStatus", "import_react_query", "import_jsx_runtime", "import_jsx_runtime", "import_jsx_runtime", "clsx", "import_jsx_runtime", "import_react", "import_react_query", "import_react", "wallet", "import_slush_wallet", "import_react", "import_utils", "import_wallet_standard", "import_react", "import_react", "import_wallet_standard", "import_react", "import_zustand", "connectionStatus", "import_jsx_runtime", "import_jsx_runtime", "import_react_query", "import_react_query", "import_react_query", "import_utils", "import_wallet_standard", "import_react_query", "import_utils", "import_react_query", "import_react_query", "import_wallet_standard", "import_react_query"]}