{"version": 3, "sources": ["../../../src/constants/walletDefaults.ts", "../../../src/utils/stateStorage.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SuiWalletFeatures, WalletWithRequiredFeatures } from '@mysten/wallet-standard';\nimport { SLUSH_WALLET_NAME } from '@mysten/slush-wallet';\n\nimport { createInMemoryStore } from '../utils/stateStorage.js';\n\nexport const SUI_WALLET_NAME = 'Sui Wallet';\n\nexport const DEFAULT_STORAGE =\n\ttypeof window !== 'undefined' && window.localStorage ? localStorage : createInMemoryStore();\n\nexport const DEFAULT_STORAGE_KEY = 'sui-dapp-kit:wallet-connection-info';\n\nconst SIGN_FEATURES = [\n\t'sui:signTransaction',\n\t'sui:signTransactionBlock',\n] satisfies (keyof SuiWalletFeatures)[];\n\nexport const DEFAULT_WALLET_FILTER = (wallet: WalletWithRequiredFeatures) =>\n\tSIGN_FEATURES.some((feature) => wallet.features[feature]);\n\nexport const DEFAULT_PREFERRED_WALLETS = [SUI_WALLET_NAME, SLUSH_WALLET_NAME];\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { StateStorage } from 'zustand/middleware';\n\nexport function createInMemoryStore(): StateStorage {\n\tconst store = new Map();\n\treturn {\n\t\tgetItem(key: string) {\n\t\t\treturn store.get(key);\n\t\t},\n\t\tsetItem(key: string, value: string) {\n\t\t\tstore.set(key, value);\n\t\t},\n\t\tremoveItem(key: string) {\n\t\t\tstore.delete(key);\n\t\t},\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,0BAAkC;;;ACC3B,SAAS,sBAAoC;AACnD,QAAM,QAAQ,oBAAI,IAAI;AACtB,SAAO;AAAA,IACN,QAAQ,KAAa;AACpB,aAAO,MAAM,IAAI,GAAG;AAAA,IACrB;AAAA,IACA,QAAQ,KAAa,OAAe;AACnC,YAAM,IAAI,KAAK,KAAK;AAAA,IACrB;AAAA,IACA,WAAW,KAAa;AACvB,YAAM,OAAO,GAAG;AAAA,IACjB;AAAA,EACD;AACD;;;ADVO,IAAM,kBAAkB;AAExB,IAAM,kBACZ,OAAO,WAAW,eAAe,OAAO,eAAe,eAAe,oBAAoB;AAEpF,IAAM,sBAAsB;AAEnC,IAAM,gBAAgB;AAAA,EACrB;AAAA,EACA;AACD;AAEO,IAAM,wBAAwB,CAAC,WACrC,cAAc,KAAK,CAAC,YAAY,OAAO,SAAS,OAAO,CAAC;AAElD,IAAM,4BAA4B,CAAC,iBAAiB,qCAAiB;", "names": []}