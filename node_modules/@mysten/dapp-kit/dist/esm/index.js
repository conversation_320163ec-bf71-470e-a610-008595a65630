var __create = Object.create;
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __getProtoOf = Object.getPrototypeOf;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __typeError = (msg) => {
  throw TypeError(msg);
};
var __esm = (fn, res) => function __init() {
  return fn && (res = (0, fn[__getOwnPropNames(fn)[0]])(fn = 0)), res;
};
var __commonJS = (cb, mod) => function __require() {
  return mod || (0, cb[__getOwnPropNames(cb)[0]])((mod = { exports: {} }).exports, mod), mod.exports;
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toESM = (mod, isNodeMode, target) => (target = mod != null ? __create(__getProtoOf(mod)) : {}, __copyProps(
  // If the importer is in node compatibility mode or this is not an ESM
  // file that has been converted to a CommonJS file using a Babel-
  // compatible transform (i.e. "__esModule" has not been set), then set
  // "default" to the CommonJS "module.exports" for node compatibility.
  isNodeMode || !mod || !mod.__esModule ? __defProp(target, "default", { value: mod, enumerable: true }) : target,
  mod
));
var __accessCheck = (obj, member, msg) => member.has(obj) || __typeError("Cannot " + msg);
var __privateGet = (obj, member, getter) => (__accessCheck(obj, member, "read from private field"), getter ? getter.call(obj) : member.get(obj));
var __privateAdd = (obj, member, value) => member.has(obj) ? __typeError("Cannot add the same private member more than once") : member instanceof WeakSet ? member.add(obj) : member.set(obj, value);

// vanilla-extract-css-ns:src/components/styling/StyleMarker.css.ts.vanilla.css?source=OndoZXJlKCopIHsKICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogIGNvbG9yOiB2YXIoLS1kYXBwLWtpdC1jb2xvcnMtYm9keSk7CiAgZm9udC1mYW1pbHk6IHZhcigtLWRhcHAta2l0LXR5cG9ncmFwaHktZm9udEZhbWlseSk7CiAgZm9udC1zaXplOiB2YXIoLS1kYXBwLWtpdC1mb250V2VpZ2h0cy1ub3JtYWwpOwogIGZvbnQtc3R5bGU6IHZhcigtLWRhcHAta2l0LXR5cG9ncmFwaHktZm9udFN0eWxlKTsKICBmb250LXdlaWdodDogdmFyKC0tZGFwcC1raXQtZm9udFdlaWdodHMtbm9ybWFsKTsKICBsaW5lLWhlaWdodDogdmFyKC0tZGFwcC1raXQtdHlwb2dyYXBoeS1saW5lSGVpZ2h0KTsKICBsZXR0ZXItc3BhY2luZzogdmFyKC0tZGFwcC1raXQtdHlwb2dyYXBoeS1sZXR0ZXJTcGFjaW5nKTsKfQo6d2hlcmUoYnV0dG9uKSB7CiAgYXBwZWFyYW5jZTogbm9uZTsKICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDsKICBmb250LXNpemU6IGluaGVyaXQ7CiAgZm9udC1mYW1pbHk6IGluaGVyaXQ7CiAgbGluZS1oZWlnaHQ6IGluaGVyaXQ7CiAgbGV0dGVyLXNwYWNpbmc6IGluaGVyaXQ7CiAgY29sb3I6IGluaGVyaXQ7CiAgYm9yZGVyOiAwOwogIHBhZGRpbmc6IDA7CiAgbWFyZ2luOiAwOwp9Cjp3aGVyZShhKSB7CiAgdGV4dC1kZWNvcmF0aW9uOiBub25lOwogIGNvbG9yOiBpbmhlcml0OwogIG91dGxpbmU6IG5vbmU7Cn0KOndoZXJlKG9sLCB1bCkgewogIGxpc3Qtc3R5bGU6IG5vbmU7CiAgbWFyZ2luOiAwOwogIHBhZGRpbmc6IDA7Cn0KOndoZXJlKGgxLCBoMiwgaDMsIGg0LCBoNSwgaDYpIHsKICBmb250LXNpemU6IGluaGVyaXQ7CiAgZm9udC13ZWlnaHQ6IGluaGVyaXQ7CiAgbWFyZ2luOiAwOwp9
var init_StyleMarker_css_ts_vanilla = __esm({
  "vanilla-extract-css-ns:src/components/styling/StyleMarker.css.ts.vanilla.css?source=OndoZXJlKCopIHsKICBib3gtc2l6aW5nOiBib3JkZXItYm94OwogIGNvbG9yOiB2YXIoLS1kYXBwLWtpdC1jb2xvcnMtYm9keSk7CiAgZm9udC1mYW1pbHk6IHZhcigtLWRhcHAta2l0LXR5cG9ncmFwaHktZm9udEZhbWlseSk7CiAgZm9udC1zaXplOiB2YXIoLS1kYXBwLWtpdC1mb250V2VpZ2h0cy1ub3JtYWwpOwogIGZvbnQtc3R5bGU6IHZhcigtLWRhcHAta2l0LXR5cG9ncmFwaHktZm9udFN0eWxlKTsKICBmb250LXdlaWdodDogdmFyKC0tZGFwcC1raXQtZm9udFdlaWdodHMtbm9ybWFsKTsKICBsaW5lLWhlaWdodDogdmFyKC0tZGFwcC1raXQtdHlwb2dyYXBoeS1saW5lSGVpZ2h0KTsKICBsZXR0ZXItc3BhY2luZzogdmFyKC0tZGFwcC1raXQtdHlwb2dyYXBoeS1sZXR0ZXJTcGFjaW5nKTsKfQo6d2hlcmUoYnV0dG9uKSB7CiAgYXBwZWFyYW5jZTogbm9uZTsKICBiYWNrZ3JvdW5kLWNvbG9yOiB0cmFuc3BhcmVudDsKICBmb250LXNpemU6IGluaGVyaXQ7CiAgZm9udC1mYW1pbHk6IGluaGVyaXQ7CiAgbGluZS1oZWlnaHQ6IGluaGVyaXQ7CiAgbGV0dGVyLXNwYWNpbmc6IGluaGVyaXQ7CiAgY29sb3I6IGluaGVyaXQ7CiAgYm9yZGVyOiAwOwogIHBhZGRpbmc6IDA7CiAgbWFyZ2luOiAwOwp9Cjp3aGVyZShhKSB7CiAgdGV4dC1kZWNvcmF0aW9uOiBub25lOwogIGNvbG9yOiBpbmhlcml0OwogIG91dGxpbmU6IG5vbmU7Cn0KOndoZXJlKG9sLCB1bCkgewogIGxpc3Qtc3R5bGU6IG5vbmU7CiAgbWFyZ2luOiAwOwogIHBhZGRpbmc6IDA7Cn0KOndoZXJlKGgxLCBoMiwgaDMsIGg0LCBoNSwgaDYpIHsKICBmb250LXNpemU6IGluaGVyaXQ7CiAgZm9udC13ZWlnaHQ6IGluaGVyaXQ7CiAgbWFyZ2luOiAwOwp9"() {
  }
});

// src/components/styling/StyleMarker.css.ts
var require_StyleMarker_css = __commonJS({
  "src/components/styling/StyleMarker.css.ts"() {
    "use strict";
    init_StyleMarker_css_ts_vanilla();
  }
});

// src/components/connect-modal/ConnectModal.tsx
import * as Dialog from "@radix-ui/react-dialog";
import clsx6 from "clsx";
import { useState } from "react";

// src/constants/walletDefaults.ts
import { SLUSH_WALLET_NAME } from "@mysten/slush-wallet";

// src/utils/stateStorage.ts
function createInMemoryStore() {
  const store = /* @__PURE__ */ new Map();
  return {
    getItem(key) {
      return store.get(key);
    },
    setItem(key, value) {
      store.set(key, value);
    },
    removeItem(key) {
      store.delete(key);
    }
  };
}

// src/constants/walletDefaults.ts
var SUI_WALLET_NAME = "Sui Wallet";
var DEFAULT_STORAGE = typeof window !== "undefined" && window.localStorage ? localStorage : createInMemoryStore();
var DEFAULT_STORAGE_KEY = "sui-dapp-kit:wallet-connection-info";
var SIGN_FEATURES = [
  "sui:signTransaction",
  "sui:signTransactionBlock"
];
var DEFAULT_WALLET_FILTER = (wallet) => SIGN_FEATURES.some((feature) => wallet.features[feature]);
var DEFAULT_PREFERRED_WALLETS = [SUI_WALLET_NAME, SLUSH_WALLET_NAME];

// src/hooks/wallet/useConnectWallet.ts
import { useMutation } from "@tanstack/react-query";

// src/constants/walletMutationKeys.ts
var walletMutationKeys = {
  all: { baseScope: "wallet" },
  connectWallet: formMutationKeyFn("connect-wallet"),
  autoconnectWallet: formMutationKeyFn("autoconnect-wallet"),
  disconnectWallet: formMutationKeyFn("disconnect-wallet"),
  signPersonalMessage: formMutationKeyFn("sign-personal-message"),
  signTransaction: formMutationKeyFn("sign-transaction"),
  signAndExecuteTransaction: formMutationKeyFn("sign-and-execute-transaction"),
  switchAccount: formMutationKeyFn("switch-account"),
  reportTransactionEffects: formMutationKeyFn("report-transaction-effects")
};
function formMutationKeyFn(baseEntity) {
  return function mutationKeyFn(additionalKeys = []) {
    return [{ ...walletMutationKeys.all, baseEntity }, ...additionalKeys];
  };
}

// src/hooks/wallet/useWalletStore.ts
import { useContext } from "react";
import { useStore } from "zustand";

// src/contexts/walletContext.ts
import { createContext } from "react";
var WalletContext = createContext(null);

// src/hooks/wallet/useWalletStore.ts
function useWalletStore(selector) {
  const store = useContext(WalletContext);
  if (!store) {
    throw new Error(
      "Could not find WalletContext. Ensure that you have set up the WalletProvider."
    );
  }
  return useStore(store, selector);
}

// src/hooks/wallet/useConnectWallet.ts
function useConnectWallet({
  mutationKey,
  ...mutationOptions
} = {}) {
  const setWalletConnected = useWalletStore((state) => state.setWalletConnected);
  const setConnectionStatus = useWalletStore((state) => state.setConnectionStatus);
  return useMutation({
    mutationKey: walletMutationKeys.connectWallet(mutationKey),
    mutationFn: async ({ wallet, accountAddress, ...connectArgs }) => {
      try {
        setConnectionStatus("connecting");
        const connectResult = await wallet.features["standard:connect"].connect(connectArgs);
        const connectedSuiAccounts = connectResult.accounts.filter(
          (account) => account.chains.some((chain) => chain.split(":")[0] === "sui")
        );
        const selectedAccount = getSelectedAccount(connectedSuiAccounts, accountAddress);
        setWalletConnected(
          wallet,
          connectedSuiAccounts,
          selectedAccount,
          connectResult.supportedIntents
        );
        return { accounts: connectedSuiAccounts };
      } catch (error) {
        setConnectionStatus("disconnected");
        throw error;
      }
    },
    ...mutationOptions
  });
}
function getSelectedAccount(connectedAccounts, accountAddress) {
  if (connectedAccounts.length === 0) {
    return null;
  }
  if (accountAddress) {
    const selectedAccount = connectedAccounts.find((account) => account.address === accountAddress);
    return selectedAccount ?? connectedAccounts[0];
  }
  return connectedAccounts[0];
}

// src/hooks/wallet/useWallets.ts
function useWallets() {
  return useWalletStore((state) => state.wallets);
}

// src/utils/walletUtils.ts
import { getWallets, isWalletWithRequiredFeatureSet } from "@mysten/wallet-standard";
function getRegisteredWallets(preferredWallets, walletFilter) {
  const walletsApi = getWallets();
  const wallets = walletsApi.get();
  const suiWallets = wallets.filter(
    (wallet) => isWalletWithRequiredFeatureSet(wallet) && (!walletFilter || walletFilter(wallet))
  );
  return [
    // Preferred wallets, in order:
    ...preferredWallets.map((name) => suiWallets.find((wallet) => wallet.name === name)).filter(Boolean),
    // Wallets in default order:
    ...suiWallets.filter((wallet) => !preferredWallets.includes(wallet.name))
  ];
}
function getWalletUniqueIdentifier(wallet) {
  return wallet?.id ?? wallet?.name;
}

// src/components/icons/BackIcon.tsx
import { jsx } from "react/jsx-runtime";
function BackIcon(props) {
  return /* @__PURE__ */ jsx("svg", { width: 24, height: 24, fill: "none", xmlns: "http://www.w3.org/2000/svg", ...props, children: /* @__PURE__ */ jsx(
    "path",
    {
      d: "M7.57 12.262c0 .341.13.629.403.895l5.175 5.059c.204.205.45.307.751.307.609 0 1.101-.485 1.101-1.087 0-.293-.123-.574-.349-.8L10.14 12.27l4.511-4.375A1.13 1.13 0 0 0 15 7.087C15 6.485 14.508 6 13.9 6c-.295 0-.54.103-.752.308l-5.175 5.058c-.28.28-.404.56-.404.896Z",
      fill: "currentColor"
    }
  ) });
}

// src/components/icons/CloseIcon.tsx
import { jsx as jsx2 } from "react/jsx-runtime";
function CloseIcon(props) {
  return /* @__PURE__ */ jsx2("svg", { width: 10, height: 10, fill: "none", xmlns: "http://www.w3.org/2000/svg", ...props, children: /* @__PURE__ */ jsx2(
    "path",
    {
      d: "M9.708.292a.999.999 0 0 0-1.413 0l-3.289 3.29L1.717.291A.999.999 0 0 0 .305 1.705l3.289 3.289-3.29 3.289a.999.999 0 1 0 1.413 1.412l3.29-3.289 3.288 3.29a.999.999 0 0 0 1.413-1.413l-3.29-3.29 3.29-3.288a.999.999 0 0 0 0-1.413Z",
      fill: "currentColor"
    }
  ) });
}

// src/components/styling/StyleMarker.tsx
import { Slot } from "@radix-ui/react-slot";
import { forwardRef } from "react";

// src/constants/styleDataAttribute.ts
var styleDataAttributeName = "data-dapp-kit";
var styleDataAttributeSelector = `[${styleDataAttributeName}]`;
var styleDataAttribute = { [styleDataAttributeName]: "" };

// src/components/styling/StyleMarker.tsx
var import_StyleMarker_css = __toESM(require_StyleMarker_css());
import { jsx as jsx3 } from "react/jsx-runtime";
var StyleMarker = forwardRef(({ children, ...props }, forwardedRef) => /* @__PURE__ */ jsx3(Slot, { ref: forwardedRef, ...props, ...styleDataAttribute, children }));
StyleMarker.displayName = "StyleMarker";

// src/components/ui/Heading.tsx
import { Slot as Slot2 } from "@radix-ui/react-slot";
import clsx from "clsx";
import { forwardRef as forwardRef2 } from "react";

// src/components/ui/Heading.css.ts
import { createRuntimeFn as _7a468 } from "@vanilla-extract/recipes/createRuntimeFn";
var headingVariants = _7a468({ defaultClassName: "Heading__1aa835k0", variantClassNames: { size: { sm: "Heading_headingVariants_size_sm__1aa835k1", md: "Heading_headingVariants_size_md__1aa835k2", lg: "Heading_headingVariants_size_lg__1aa835k3", xl: "Heading_headingVariants_size_xl__1aa835k4" }, weight: { normal: "Heading_headingVariants_weight_normal__1aa835k5", bold: "Heading_headingVariants_weight_bold__1aa835k6" }, truncate: { true: "Heading_headingVariants_truncate_true__1aa835k7" } }, defaultVariants: { size: "lg", weight: "bold" }, compoundVariants: [] });

// src/components/ui/Heading.tsx
import { jsx as jsx4 } from "react/jsx-runtime";
var Heading = forwardRef2(
  ({
    children,
    className,
    asChild = false,
    as: Tag = "h1",
    size,
    weight,
    truncate,
    ...headingProps
  }, forwardedRef) => {
    return /* @__PURE__ */ jsx4(
      Slot2,
      {
        ...headingProps,
        ref: forwardedRef,
        className: clsx(headingVariants({ size, weight, truncate }), className),
        children: asChild ? children : /* @__PURE__ */ jsx4(Tag, { children })
      }
    );
  }
);
Heading.displayName = "Heading";

// src/components/ui/IconButton.tsx
import { Slot as Slot3 } from "@radix-ui/react-slot";
import clsx2 from "clsx";
import { forwardRef as forwardRef3 } from "react";

// src/components/ui/IconButton.css.ts
var container = "IconButton_container__s6n7bq0";

// src/components/ui/IconButton.tsx
import { jsx as jsx5 } from "react/jsx-runtime";
var IconButton = forwardRef3(
  ({ className, asChild = false, ...props }, forwardedRef) => {
    const Comp = asChild ? Slot3 : "button";
    return /* @__PURE__ */ jsx5(Comp, { ...props, className: clsx2(container, className), ref: forwardedRef });
  }
);
IconButton.displayName = "Button";

// src/components/connect-modal/ConnectModal.css.ts
var backButtonContainer = "ConnectModal_backButtonContainer__gz8z96";
var closeButtonContainer = "ConnectModal_closeButtonContainer__gz8z97";
var content = "ConnectModal_content__gz8z92";
var overlay = "ConnectModal_overlay__gz8z90";
var selectedViewContainer = "ConnectModal_selectedViewContainer__gz8z95";
var title = "ConnectModal_title__gz8z91";
var viewContainer = "ConnectModal_viewContainer__gz8z94";
var walletListContainer = "ConnectModal_walletListContainer__gz8z99";
var walletListContainerWithViewSelected = "ConnectModal_walletListContainerWithViewSelected__gz8z9a";
var walletListContent = "ConnectModal_walletListContent__gz8z98";
var whatIsAWalletButton = "ConnectModal_whatIsAWalletButton__gz8z93";

// src/components/ui/Button.tsx
import { Slot as Slot4 } from "@radix-ui/react-slot";
import clsx3 from "clsx";
import { forwardRef as forwardRef4 } from "react";

// src/components/ui/Button.css.ts
import { createRuntimeFn as _7a4682 } from "@vanilla-extract/recipes/createRuntimeFn";
var buttonVariants = _7a4682({ defaultClassName: "Button_buttonVariants__x1s81q0", variantClassNames: { variant: { primary: "Button_buttonVariants_variant_primary__x1s81q1", outline: "Button_buttonVariants_variant_outline__x1s81q2" }, size: { md: "Button_buttonVariants_size_md__x1s81q3", lg: "Button_buttonVariants_size_lg__x1s81q4" } }, defaultVariants: { variant: "primary", size: "md" }, compoundVariants: [] });

// src/components/ui/Button.tsx
import { jsx as jsx6 } from "react/jsx-runtime";
var Button = forwardRef4(
  ({ className, variant, size, asChild = false, ...props }, forwardedRef) => {
    const Comp = asChild ? Slot4 : "button";
    return /* @__PURE__ */ jsx6(
      Comp,
      {
        ...props,
        className: clsx3(buttonVariants({ variant, size }), className),
        ref: forwardedRef
      }
    );
  }
);
Button.displayName = "Button";

// src/components/ui/Text.tsx
import { Slot as Slot5 } from "@radix-ui/react-slot";
import clsx4 from "clsx";
import { forwardRef as forwardRef5 } from "react";

// src/components/ui/Text.css.ts
import { createRuntimeFn as _7a4683 } from "@vanilla-extract/recipes/createRuntimeFn";
var textVariants = _7a4683({ defaultClassName: "Text__2bv1ur0", variantClassNames: { size: { sm: "Text_textVariants_size_sm__2bv1ur1" }, weight: { normal: "Text_textVariants_weight_normal__2bv1ur2", medium: "Text_textVariants_weight_medium__2bv1ur3", bold: "Text_textVariants_weight_bold__2bv1ur4" }, color: { muted: "Text_textVariants_color_muted__2bv1ur5", danger: "Text_textVariants_color_danger__2bv1ur6" }, mono: { true: "Text_textVariants_mono_true__2bv1ur7" } }, defaultVariants: { size: "sm", weight: "normal" }, compoundVariants: [] });

// src/components/ui/Text.tsx
import { jsx as jsx7 } from "react/jsx-runtime";
var Text = forwardRef5(
  ({
    children,
    className,
    asChild = false,
    as: Tag = "div",
    size,
    weight,
    color,
    mono,
    ...textProps
  }, forwardedRef) => {
    return /* @__PURE__ */ jsx7(
      Slot5,
      {
        ...textProps,
        ref: forwardedRef,
        className: clsx4(textVariants({ size, weight, color, mono }), className),
        children: asChild ? children : /* @__PURE__ */ jsx7(Tag, { children })
      }
    );
  }
);
Text.displayName = "Text";

// src/components/connect-modal/views/ConnectionStatus.css.ts
var connectionStatus = "ConnectionStatus_connectionStatus__nckm2d3";
var container2 = "ConnectionStatus_container__nckm2d0";
var retryButtonContainer = "ConnectionStatus_retryButtonContainer__nckm2d4";
var title2 = "ConnectionStatus_title__nckm2d2";
var walletIcon = "ConnectionStatus_walletIcon__nckm2d1";

// src/components/connect-modal/views/ConnectionStatus.tsx
import { jsx as jsx8, jsxs } from "react/jsx-runtime";
function ConnectionStatus({
  selectedWallet,
  hadConnectionError,
  onRetryConnection
}) {
  return /* @__PURE__ */ jsxs("div", { className: container2, children: [
    selectedWallet.icon && /* @__PURE__ */ jsx8(
      "img",
      {
        className: walletIcon,
        src: selectedWallet.icon,
        alt: `${selectedWallet.name} logo`
      }
    ),
    /* @__PURE__ */ jsx8("div", { className: title2, children: /* @__PURE__ */ jsxs(Heading, { as: "h2", size: "xl", children: [
      "Opening ",
      selectedWallet.name
    ] }) }),
    /* @__PURE__ */ jsx8("div", { className: connectionStatus, children: hadConnectionError ? /* @__PURE__ */ jsx8(Text, { color: "danger", children: "Connection failed" }) : /* @__PURE__ */ jsx8(Text, { color: "muted", children: "Confirm connection in the wallet..." }) }),
    hadConnectionError ? /* @__PURE__ */ jsx8("div", { className: retryButtonContainer, children: /* @__PURE__ */ jsx8(Button, { type: "button", variant: "outline", onClick: () => onRetryConnection(selectedWallet), children: "Retry Connection" }) }) : null
  ] });
}

// src/components/connect-modal/InfoSection.css.ts
var container3 = "InfoSection_container__1wtioi70";

// src/components/connect-modal/InfoSection.tsx
import { jsx as jsx9, jsxs as jsxs2 } from "react/jsx-runtime";
function InfoSection({ title: title3, children }) {
  return /* @__PURE__ */ jsxs2("section", { className: container3, children: [
    /* @__PURE__ */ jsx9(Heading, { as: "h3", size: "sm", weight: "normal", children: title3 }),
    /* @__PURE__ */ jsx9(Text, { weight: "medium", color: "muted", children })
  ] });
}

// src/components/connect-modal/views/GettingStarted.css.ts
var container4 = "GettingStarted_container__1fp07e10";
var content2 = "GettingStarted_content__1fp07e11";
var installButtonContainer = "GettingStarted_installButtonContainer__1fp07e12";

// src/components/connect-modal/views/GettingStarted.tsx
import { jsx as jsx10, jsxs as jsxs3 } from "react/jsx-runtime";
function GettingStarted() {
  return /* @__PURE__ */ jsxs3("div", { className: container4, children: [
    /* @__PURE__ */ jsx10(Heading, { as: "h2", children: "Get Started with Sui" }),
    /* @__PURE__ */ jsxs3("div", { className: content2, children: [
      /* @__PURE__ */ jsx10(InfoSection, { title: "Install the Sui Wallet Extension", children: "We recommend pinning Sui Wallet to your taskbar for quicker access." }),
      /* @__PURE__ */ jsx10(InfoSection, { title: "Create or Import a Wallet", children: "Be sure to back up your wallet using a secure method. Never share your secret phrase with anyone." }),
      /* @__PURE__ */ jsx10(InfoSection, { title: "Refresh Your Browser", children: "Once you set up your wallet, refresh this window browser to load up the extension." }),
      /* @__PURE__ */ jsx10("div", { className: installButtonContainer, children: /* @__PURE__ */ jsx10(Button, { variant: "outline", asChild: true, children: /* @__PURE__ */ jsx10(
        "a",
        {
          href: "https://chrome.google.com/webstore/detail/sui-wallet/opcgpfmipidbgpenhmajoajpbobppdil",
          target: "_blank",
          rel: "noreferrer",
          children: "Install Wallet Extension"
        }
      ) }) })
    ] })
  ] });
}

// src/components/connect-modal/views/WhatIsAWallet.css.ts
var container5 = "WhatIsAWallet_container__1ktpkq90";
var content3 = "WhatIsAWallet_content__1ktpkq91";

// src/components/connect-modal/views/WhatIsAWallet.tsx
import { jsx as jsx11, jsxs as jsxs4 } from "react/jsx-runtime";
function WhatIsAWallet() {
  return /* @__PURE__ */ jsxs4("div", { className: container5, children: [
    /* @__PURE__ */ jsx11(Heading, { as: "h2", children: "What is a Wallet" }),
    /* @__PURE__ */ jsxs4("div", { className: content3, children: [
      /* @__PURE__ */ jsx11(InfoSection, { title: "Easy Login", children: "No need to create new accounts and passwords for every website. Just connect your wallet and get going." }),
      /* @__PURE__ */ jsx11(InfoSection, { title: "Store your Digital Assets", children: "Send, receive, store, and display your digital assets like NFTs & coins." })
    ] })
  ] });
}

// src/components/icons/SuiIcon.tsx
import { jsx as jsx12, jsxs as jsxs5 } from "react/jsx-runtime";
function SuiIcon(props) {
  return /* @__PURE__ */ jsxs5("svg", { width: 28, height: 28, fill: "none", xmlns: "http://www.w3.org/2000/svg", ...props, children: [
    /* @__PURE__ */ jsx12("rect", { width: 28, height: 28, rx: 6, fill: "#6FBCF0" }),
    /* @__PURE__ */ jsx12(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M7.942 20.527A6.875 6.875 0 0 0 13.957 24c2.51 0 4.759-1.298 6.015-3.473a6.875 6.875 0 0 0 0-6.945l-5.29-9.164a.837.837 0 0 0-1.45 0l-5.29 9.164a6.875 6.875 0 0 0 0 6.945Zm4.524-11.75 1.128-1.953a.418.418 0 0 1 .725 0l4.34 7.516a5.365 5.365 0 0 1 .449 4.442 4.675 4.675 0 0 0-.223-.73c-.599-1.512-1.954-2.68-4.029-3.47-1.426-.54-2.336-1.336-2.706-2.364-.476-1.326.021-2.77.316-3.44Zm-1.923 3.332L9.255 14.34a5.373 5.373 0 0 0 0 5.43 5.373 5.373 0 0 0 4.702 2.714 5.38 5.38 0 0 0 3.472-1.247c.125-.314.51-1.462.034-2.646-.44-1.093-1.5-1.965-3.15-2.594-1.864-.707-3.076-1.811-3.6-3.28a4.601 4.601 0 0 1-.17-.608Z",
        fill: "#fff"
      }
    )
  ] });
}

// src/components/connect-modal/wallet-list/WalletList.css.ts
var container6 = "WalletList_container__1v2s6cz0";

// src/components/connect-modal/wallet-list/WalletListItem.tsx
import { clsx as clsx5 } from "clsx";

// src/components/connect-modal/wallet-list/WalletListItem.css.ts
var container7 = "WalletListItem_container__1dqqtqs0";
var selectedWalletItem = "WalletListItem_selectedWalletItem__1dqqtqs2";
var walletIcon2 = "WalletListItem_walletIcon__1dqqtqs3";
var walletItem = "WalletListItem_walletItem__1dqqtqs1";

// src/components/connect-modal/wallet-list/WalletListItem.tsx
import { jsx as jsx13, jsxs as jsxs6 } from "react/jsx-runtime";
function WalletListItem({ name, icon, onClick, isSelected = false }) {
  return /* @__PURE__ */ jsx13("li", { className: container7, children: /* @__PURE__ */ jsxs6(
    "button",
    {
      className: clsx5(walletItem, { [selectedWalletItem]: isSelected }),
      type: "button",
      onClick,
      children: [
        icon && typeof icon === "string" ? /* @__PURE__ */ jsx13("img", { className: walletIcon2, src: icon, alt: `${name} logo` }) : icon,
        /* @__PURE__ */ jsx13(Heading, { size: "md", truncate: true, asChild: true, children: /* @__PURE__ */ jsx13("div", { children: name }) })
      ]
    }
  ) });
}

// src/components/connect-modal/wallet-list/WalletList.tsx
import { jsx as jsx14 } from "react/jsx-runtime";
function WalletList({
  selectedWalletName,
  onPlaceholderClick,
  onSelect,
  wallets
}) {
  return /* @__PURE__ */ jsx14("ul", { className: container6, children: wallets.length > 0 ? wallets.map((wallet) => /* @__PURE__ */ jsx14(
    WalletListItem,
    {
      name: wallet.name,
      icon: wallet.icon,
      isSelected: getWalletUniqueIdentifier(wallet) === selectedWalletName,
      onClick: () => onSelect(wallet)
    },
    getWalletUniqueIdentifier(wallet)
  )) : /* @__PURE__ */ jsx14(
    WalletListItem,
    {
      name: "Sui Wallet",
      icon: /* @__PURE__ */ jsx14(SuiIcon, {}),
      onClick: onPlaceholderClick,
      isSelected: true
    }
  ) });
}

// src/components/connect-modal/ConnectModal.tsx
import { jsx as jsx15, jsxs as jsxs7 } from "react/jsx-runtime";
function ConnectModal({
  trigger,
  open,
  defaultOpen,
  onOpenChange,
  walletFilter = DEFAULT_WALLET_FILTER
}) {
  const [isModalOpen, setModalOpen] = useState(open ?? defaultOpen);
  const [currentView, setCurrentView] = useState();
  const [selectedWallet, setSelectedWallet] = useState();
  const wallets = useWallets().filter(walletFilter);
  const { mutate, isError } = useConnectWallet();
  const resetSelection = () => {
    setSelectedWallet(void 0);
    setCurrentView(void 0);
  };
  const handleOpenChange = (open2) => {
    if (!open2) {
      resetSelection();
    }
    setModalOpen(open2);
    onOpenChange?.(open2);
  };
  const connectWallet = (wallet) => {
    setCurrentView("connection-status");
    mutate(
      { wallet },
      {
        onSuccess: () => handleOpenChange(false)
      }
    );
  };
  let modalContent;
  switch (currentView) {
    case "what-is-a-wallet":
      modalContent = /* @__PURE__ */ jsx15(WhatIsAWallet, {});
      break;
    case "getting-started":
      modalContent = /* @__PURE__ */ jsx15(GettingStarted, {});
      break;
    case "connection-status":
      modalContent = /* @__PURE__ */ jsx15(
        ConnectionStatus,
        {
          selectedWallet,
          hadConnectionError: isError,
          onRetryConnection: connectWallet
        }
      );
      break;
    default:
      modalContent = /* @__PURE__ */ jsx15(WhatIsAWallet, {});
  }
  return /* @__PURE__ */ jsxs7(Dialog.Root, { open: open ?? isModalOpen, onOpenChange: handleOpenChange, children: [
    /* @__PURE__ */ jsx15(Dialog.Trigger, { asChild: true, children: trigger }),
    /* @__PURE__ */ jsx15(Dialog.Portal, { children: /* @__PURE__ */ jsx15(StyleMarker, { children: /* @__PURE__ */ jsx15(Dialog.Overlay, { className: overlay, children: /* @__PURE__ */ jsxs7(Dialog.Content, { className: content, "aria-describedby": void 0, children: [
      /* @__PURE__ */ jsxs7(
        "div",
        {
          className: clsx6(walletListContainer, {
            [walletListContainerWithViewSelected]: !!currentView
          }),
          children: [
            /* @__PURE__ */ jsxs7("div", { className: walletListContent, children: [
              /* @__PURE__ */ jsx15(Dialog.Title, { className: title, asChild: true, children: /* @__PURE__ */ jsx15(Heading, { as: "h2", children: "Connect a Wallet" }) }),
              /* @__PURE__ */ jsx15(
                WalletList,
                {
                  wallets,
                  selectedWalletName: getWalletUniqueIdentifier(selectedWallet),
                  onPlaceholderClick: () => setCurrentView("getting-started"),
                  onSelect: (wallet) => {
                    if (getWalletUniqueIdentifier(selectedWallet) !== getWalletUniqueIdentifier(wallet)) {
                      setSelectedWallet(wallet);
                      connectWallet(wallet);
                    }
                  }
                }
              )
            ] }),
            /* @__PURE__ */ jsx15(
              "button",
              {
                className: whatIsAWalletButton,
                onClick: () => setCurrentView("what-is-a-wallet"),
                type: "button",
                children: "What is a Wallet?"
              }
            )
          ]
        }
      ),
      /* @__PURE__ */ jsxs7(
        "div",
        {
          className: clsx6(viewContainer, {
            [selectedViewContainer]: !!currentView
          }),
          children: [
            /* @__PURE__ */ jsx15("div", { className: backButtonContainer, children: /* @__PURE__ */ jsx15(IconButton, { type: "button", "aria-label": "Back", onClick: () => resetSelection(), children: /* @__PURE__ */ jsx15(BackIcon, {}) }) }),
            modalContent
          ]
        }
      ),
      /* @__PURE__ */ jsx15(Dialog.Close, { className: closeButtonContainer, asChild: true, children: /* @__PURE__ */ jsx15(IconButton, { type: "button", "aria-label": "Close", children: /* @__PURE__ */ jsx15(CloseIcon, {}) }) })
    ] }) }) }) })
  ] });
}

// src/hooks/wallet/useCurrentAccount.ts
function useCurrentAccount() {
  return useWalletStore((state) => state.currentAccount);
}

// src/components/AccountDropdownMenu.tsx
import { formatAddress } from "@mysten/sui/utils";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import clsx7 from "clsx";

// src/hooks/useSuiClientQuery.ts
import { queryOptions, useQuery, useSuspenseQuery } from "@tanstack/react-query";
import { useMemo as useMemo2 } from "react";

// src/hooks/useSuiClient.ts
import { useContext as useContext2 } from "react";

// src/components/SuiClientProvider.tsx
import { getFullnodeUrl, isSuiClient, SuiClient } from "@mysten/sui/client";
import { createContext as createContext2, useMemo, useState as useState2 } from "react";
import { jsx as jsx16 } from "react/jsx-runtime";
var SuiClientContext = createContext2(null);
var DEFAULT_NETWORKS = {
  localnet: { url: getFullnodeUrl("localnet") }
};
var DEFAULT_CREATE_CLIENT = function createClient(_name, config) {
  if (isSuiClient(config)) {
    return config;
  }
  return new SuiClient(config);
};
function SuiClientProvider(props) {
  const { onNetworkChange, network, children } = props;
  const networks = props.networks ?? DEFAULT_NETWORKS;
  const createClient2 = props.createClient ?? DEFAULT_CREATE_CLIENT;
  const [selectedNetwork, setSelectedNetwork] = useState2(
    props.network ?? props.defaultNetwork ?? Object.keys(networks)[0]
  );
  const currentNetwork = props.network ?? selectedNetwork;
  const client = useMemo(() => {
    return createClient2(currentNetwork, networks[currentNetwork]);
  }, [createClient2, currentNetwork, networks]);
  const ctx = useMemo(() => {
    return {
      client,
      networks,
      network: currentNetwork,
      config: networks[currentNetwork] instanceof SuiClient ? null : networks[currentNetwork],
      selectNetwork: (newNetwork) => {
        if (currentNetwork === newNetwork) {
          return;
        }
        if (!network && newNetwork !== selectedNetwork) {
          setSelectedNetwork(newNetwork);
        }
        onNetworkChange?.(newNetwork);
      }
    };
  }, [client, networks, selectedNetwork, currentNetwork, network, onNetworkChange]);
  return /* @__PURE__ */ jsx16(SuiClientContext.Provider, { value: ctx, children });
}

// src/hooks/useSuiClient.ts
function useSuiClientContext() {
  const suiClient = useContext2(SuiClientContext);
  if (!suiClient) {
    throw new Error(
      "Could not find SuiClientContext. Ensure that you have set up the SuiClientProvider"
    );
  }
  return suiClient;
}
function useSuiClient() {
  return useSuiClientContext().client;
}

// src/hooks/useSuiClientQuery.ts
function getSuiClientQuery({
  client,
  network,
  method,
  params,
  options
}) {
  return queryOptions({
    ...options,
    queryKey: [network, method, params],
    queryFn: async () => {
      return await client[method](params);
    }
  });
}
function useSuiClientQuery(...args) {
  const [method, params, { queryKey = [], ...options } = {}] = args;
  const suiContext = useSuiClientContext();
  return useQuery({
    ...options,
    queryKey: [suiContext.network, method, params, ...queryKey],
    queryFn: async () => {
      return await suiContext.client[method](params);
    }
  });
}
function useSuiClientSuspenseQuery(...args) {
  const [method, params, options = {}] = args;
  const suiContext = useSuiClientContext();
  const query = useMemo2(() => {
    return getSuiClientQuery({
      client: suiContext.client,
      network: suiContext.network,
      method,
      params,
      options
    });
  }, [suiContext.client, suiContext.network, method, params, options]);
  return useSuspenseQuery(query);
}

// src/hooks/useResolveSuiNSNames.ts
function useResolveSuiNSName(address, options) {
  return useSuiClientQuery(
    "resolveNameServiceNames",
    {
      address,
      limit: 1
    },
    {
      ...options,
      refetchOnWindowFocus: false,
      retry: false,
      select: (data) => data.data.length > 0 ? data.data[0] : null,
      enabled: !!address && options?.enabled !== false
    }
  );
}

// src/hooks/wallet/useAccounts.ts
function useAccounts() {
  return useWalletStore((state) => state.accounts);
}

// src/hooks/wallet/useDisconnectWallet.ts
import { useMutation as useMutation2 } from "@tanstack/react-query";

// src/errors/walletErrors.ts
var WalletNotConnectedError = class extends Error {
};
var WalletNoAccountSelectedError = class extends Error {
};
var WalletFeatureNotSupportedError = class extends Error {
};
var WalletAccountNotFoundError = class extends Error {
};

// src/hooks/wallet/useCurrentWallet.ts
function useCurrentWallet() {
  const currentWallet = useWalletStore((state) => state.currentWallet);
  const connectionStatus2 = useWalletStore((state) => state.connectionStatus);
  const supportedIntents = useWalletStore((state) => state.supportedIntents);
  switch (connectionStatus2) {
    case "connecting":
      return {
        connectionStatus: connectionStatus2,
        currentWallet: null,
        isDisconnected: false,
        isConnecting: true,
        isConnected: false,
        supportedIntents: []
      };
    case "disconnected":
      return {
        connectionStatus: connectionStatus2,
        currentWallet: null,
        isDisconnected: true,
        isConnecting: false,
        isConnected: false,
        supportedIntents: []
      };
    case "connected": {
      return {
        connectionStatus: connectionStatus2,
        currentWallet,
        isDisconnected: false,
        isConnecting: false,
        isConnected: true,
        supportedIntents
      };
    }
  }
}

// src/hooks/wallet/useDisconnectWallet.ts
function useDisconnectWallet({
  mutationKey,
  ...mutationOptions
} = {}) {
  const { currentWallet } = useCurrentWallet();
  const setWalletDisconnected = useWalletStore((state) => state.setWalletDisconnected);
  return useMutation2({
    mutationKey: walletMutationKeys.disconnectWallet(mutationKey),
    mutationFn: async () => {
      if (!currentWallet) {
        throw new WalletNotConnectedError("No wallet is connected.");
      }
      try {
        await currentWallet.features["standard:disconnect"]?.disconnect();
      } catch (error) {
        console.error("Failed to disconnect the application from the current wallet.", error);
      }
      setWalletDisconnected();
    },
    ...mutationOptions
  });
}

// src/hooks/wallet/useSwitchAccount.ts
import { useMutation as useMutation3 } from "@tanstack/react-query";
function useSwitchAccount({
  mutationKey,
  ...mutationOptions
} = {}) {
  const { currentWallet } = useCurrentWallet();
  const setAccountSwitched = useWalletStore((state) => state.setAccountSwitched);
  return useMutation3({
    mutationKey: walletMutationKeys.switchAccount(mutationKey),
    mutationFn: async ({ account }) => {
      if (!currentWallet) {
        throw new WalletNotConnectedError("No wallet is connected.");
      }
      const accountToSelect = currentWallet.accounts.find(
        (walletAccount) => walletAccount.address === account.address
      );
      if (!accountToSelect) {
        throw new WalletAccountNotFoundError(
          `No account with address ${account.address} is connected to ${currentWallet.name}.`
        );
      }
      setAccountSwitched(accountToSelect);
    },
    ...mutationOptions
  });
}

// src/components/AccountDropdownMenu.css.ts
var connectedAccount = "AccountDropdownMenu_connectedAccount__div2ql0";
var menuContainer = "AccountDropdownMenu_menuContainer__div2ql1";
var menuContent = "AccountDropdownMenu_menuContent__div2ql2";
var menuItem = "AccountDropdownMenu_menuItem__div2ql3";
var separator = "AccountDropdownMenu_separator__div2ql5";
var switchAccountMenuItem = "AccountDropdownMenu_switchAccountMenuItem__div2ql4";

// src/components/icons/CheckIcon.tsx
import { jsx as jsx17 } from "react/jsx-runtime";
function CheckIcon(props) {
  return /* @__PURE__ */ jsx17("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "none", ...props, children: /* @__PURE__ */ jsx17(
    "path",
    {
      fill: "currentColor",
      d: "m11.726 5.048-4.73 5.156-1.722-1.879a.72.72 0 0 0-.529-.23.722.722 0 0 0-.525.24.858.858 0 0 0-.22.573.86.86 0 0 0 .211.576l2.255 2.458c.14.153.332.24.53.24.2 0 .391-.087.532-.24l5.261-5.735A.86.86 0 0 0 13 5.63a.858.858 0 0 0-.22-.572.722.722 0 0 0-.525-.24.72.72 0 0 0-.529.23Z"
    }
  ) });
}

// src/components/icons/ChevronIcon.tsx
import { jsx as jsx18 } from "react/jsx-runtime";
function ChevronIcon(props) {
  return /* @__PURE__ */ jsx18("svg", { xmlns: "http://www.w3.org/2000/svg", width: 16, height: 16, fill: "none", ...props, children: /* @__PURE__ */ jsx18(
    "path",
    {
      stroke: "#A0B6C3",
      strokeLinecap: "round",
      strokeLinejoin: "round",
      strokeWidth: 1.5,
      d: "m4 6 4 4 4-4"
    }
  ) });
}

// src/components/AccountDropdownMenu.tsx
import { jsx as jsx19, jsxs as jsxs8 } from "react/jsx-runtime";
function AccountDropdownMenu({ currentAccount }) {
  const { mutate: disconnectWallet } = useDisconnectWallet();
  const { data: domain } = useResolveSuiNSName(
    currentAccount.label ? null : currentAccount.address
  );
  const accounts = useAccounts();
  return /* @__PURE__ */ jsxs8(DropdownMenu.Root, { modal: false, children: [
    /* @__PURE__ */ jsx19(StyleMarker, { children: /* @__PURE__ */ jsx19(DropdownMenu.Trigger, { asChild: true, children: /* @__PURE__ */ jsxs8(Button, { size: "lg", className: connectedAccount, children: [
      /* @__PURE__ */ jsx19(Text, { mono: true, weight: "bold", children: currentAccount.label ?? domain ?? formatAddress(currentAccount.address) }),
      /* @__PURE__ */ jsx19(ChevronIcon, {})
    ] }) }) }),
    /* @__PURE__ */ jsx19(DropdownMenu.Portal, { children: /* @__PURE__ */ jsx19(StyleMarker, { className: menuContainer, children: /* @__PURE__ */ jsxs8(DropdownMenu.Content, { className: menuContent, children: [
      accounts.map((account) => /* @__PURE__ */ jsx19(
        AccountDropdownMenuItem,
        {
          account,
          active: currentAccount.address === account.address
        },
        account.address
      )),
      /* @__PURE__ */ jsx19(DropdownMenu.Separator, { className: separator }),
      /* @__PURE__ */ jsx19(
        DropdownMenu.Item,
        {
          className: clsx7(menuItem),
          onSelect: () => disconnectWallet(),
          children: "Disconnect"
        }
      )
    ] }) }) })
  ] });
}
function AccountDropdownMenuItem({
  account,
  active
}) {
  const { mutate: switchAccount } = useSwitchAccount();
  const { data: domain } = useResolveSuiNSName(account.label ? null : account.address);
  return /* @__PURE__ */ jsxs8(
    DropdownMenu.Item,
    {
      className: clsx7(menuItem, switchAccountMenuItem),
      onSelect: () => switchAccount({ account }),
      children: [
        /* @__PURE__ */ jsx19(Text, { mono: true, children: account.label ?? domain ?? formatAddress(account.address) }),
        active ? /* @__PURE__ */ jsx19(CheckIcon, {}) : null
      ]
    }
  );
}

// src/components/ConnectButton.tsx
import { jsx as jsx20 } from "react/jsx-runtime";
function ConnectButton({
  connectText = "Connect Wallet",
  walletFilter,
  ...buttonProps
}) {
  const currentAccount = useCurrentAccount();
  return currentAccount ? /* @__PURE__ */ jsx20(AccountDropdownMenu, { currentAccount }) : /* @__PURE__ */ jsx20(
    ConnectModal,
    {
      walletFilter,
      trigger: /* @__PURE__ */ jsx20(StyleMarker, { children: /* @__PURE__ */ jsx20(Button, { ...buttonProps, children: connectText }) })
    }
  );
}

// src/components/WalletProvider.tsx
import { useRef } from "react";

// src/hooks/wallet/useAutoConnectWallet.ts
import { useQuery as useQuery2 } from "@tanstack/react-query";
import { useLayoutEffect, useState as useState3 } from "react";
function useAutoConnectWallet() {
  const { mutateAsync: connectWallet } = useConnectWallet();
  const autoConnectEnabled = useWalletStore((state) => state.autoConnectEnabled);
  const lastConnectedWalletName = useWalletStore((state) => state.lastConnectedWalletName);
  const lastConnectedAccountAddress = useWalletStore((state) => state.lastConnectedAccountAddress);
  const wallets = useWallets();
  const { isConnected } = useCurrentWallet();
  const [clientOnly, setClientOnly] = useState3(false);
  useLayoutEffect(() => {
    setClientOnly(true);
  }, []);
  const { data, isError } = useQuery2({
    queryKey: [
      "@mysten/dapp-kit",
      "autoconnect",
      {
        isConnected,
        autoConnectEnabled,
        lastConnectedWalletName,
        lastConnectedAccountAddress,
        walletCount: wallets.length
      }
    ],
    queryFn: async () => {
      if (!autoConnectEnabled) {
        return "disabled";
      }
      if (!lastConnectedWalletName || !lastConnectedAccountAddress || isConnected) {
        return "attempted";
      }
      const wallet = wallets.find(
        (wallet2) => getWalletUniqueIdentifier(wallet2) === lastConnectedWalletName
      );
      if (wallet) {
        await connectWallet({
          wallet,
          accountAddress: lastConnectedAccountAddress,
          silent: true
        });
      }
      return "attempted";
    },
    enabled: autoConnectEnabled,
    persister: void 0,
    gcTime: 0,
    staleTime: 0,
    networkMode: "always",
    retry: false,
    retryOnMount: false,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false
  });
  if (!autoConnectEnabled) {
    return "disabled";
  }
  if (!clientOnly) {
    return "idle";
  }
  if (isConnected) {
    return "attempted";
  }
  if (!lastConnectedWalletName) {
    return "attempted";
  }
  return isError ? "attempted" : data ?? "idle";
}

// src/hooks/wallet/useSlushWallet.ts
import { registerSlushWallet } from "@mysten/slush-wallet";
import { useLayoutEffect as useLayoutEffect2 } from "react";
function useSlushWallet(config) {
  useLayoutEffect2(() => {
    if (!config?.name) {
      return;
    }
    let cleanup;
    let isMounted = true;
    try {
      const result = registerSlushWallet(config.name, {
        origin: config.origin
      });
      if (isMounted && result) {
        cleanup = result.unregister;
      } else if (result) {
        result.unregister();
      }
    } catch (error) {
      console.error("Failed to register Slush wallet:", error);
    }
    return () => {
      isMounted = false;
      if (cleanup) cleanup();
    };
  }, [config?.name, config?.origin]);
}

// src/hooks/wallet/useUnsafeBurnerWallet.ts
import { Ed25519Keypair } from "@mysten/sui/keypairs/ed25519";
import { Transaction } from "@mysten/sui/transactions";
import { toBase64 } from "@mysten/sui/utils";
import { getWallets as getWallets2, ReadonlyWalletAccount, SUI_CHAINS } from "@mysten/wallet-standard";
import { useEffect } from "react";
var WALLET_NAME = "Unsafe Burner Wallet";
function useUnsafeBurnerWallet(enabled) {
  const suiClient = useSuiClient();
  useEffect(() => {
    if (!enabled) {
      return;
    }
    const unregister = registerUnsafeBurnerWallet(suiClient);
    return unregister;
  }, [enabled, suiClient]);
}
function registerUnsafeBurnerWallet(suiClient) {
  var _on, _connect, _signPersonalMessage, _signTransactionBlock, _signTransaction, _signAndExecuteTransactionBlock, _signAndExecuteTransaction;
  const walletsApi = getWallets2();
  const registeredWallets = walletsApi.get();
  if (registeredWallets.find((wallet) => wallet.name === WALLET_NAME)) {
    console.warn(
      "registerUnsafeBurnerWallet: Unsafe Burner Wallet already registered, skipping duplicate registration."
    );
    return;
  }
  console.warn(
    "Your application is currently using the unsafe burner wallet. Make sure that this wallet is disabled in production."
  );
  const keypair = new Ed25519Keypair();
  const account = new ReadonlyWalletAccount({
    address: keypair.getPublicKey().toSuiAddress(),
    publicKey: keypair.getPublicKey().toSuiBytes(),
    chains: ["sui:unknown"],
    features: [
      "sui:signAndExecuteTransactionBlock",
      "sui:signTransactionBlock",
      "sui:signTransaction",
      "sui:signAndExecuteTransaction"
    ]
  });
  class UnsafeBurnerWallet {
    constructor() {
      __privateAdd(this, _on, () => {
        return () => {
        };
      });
      __privateAdd(this, _connect, async () => {
        return { accounts: this.accounts };
      });
      __privateAdd(this, _signPersonalMessage, async (messageInput) => {
        const { bytes, signature } = await keypair.signPersonalMessage(messageInput.message);
        return { bytes, signature };
      });
      __privateAdd(this, _signTransactionBlock, async (transactionInput) => {
        const { bytes, signature } = await transactionInput.transactionBlock.sign({
          client: suiClient,
          signer: keypair
        });
        return {
          transactionBlockBytes: bytes,
          signature
        };
      });
      __privateAdd(this, _signTransaction, async (transactionInput) => {
        const { bytes, signature } = await Transaction.from(
          await transactionInput.transaction.toJSON()
        ).sign({
          client: suiClient,
          signer: keypair
        });
        transactionInput.signal?.throwIfAborted();
        return {
          bytes,
          signature
        };
      });
      __privateAdd(this, _signAndExecuteTransactionBlock, async (transactionInput) => {
        const { bytes, signature } = await transactionInput.transactionBlock.sign({
          client: suiClient,
          signer: keypair
        });
        return suiClient.executeTransactionBlock({
          signature,
          transactionBlock: bytes,
          options: transactionInput.options
        });
      });
      __privateAdd(this, _signAndExecuteTransaction, async (transactionInput) => {
        const { bytes, signature } = await Transaction.from(
          await transactionInput.transaction.toJSON()
        ).sign({
          client: suiClient,
          signer: keypair
        });
        transactionInput.signal?.throwIfAborted();
        const { rawEffects, digest } = await suiClient.executeTransactionBlock({
          signature,
          transactionBlock: bytes,
          options: {
            showRawEffects: true
          }
        });
        return {
          bytes,
          signature,
          digest,
          effects: toBase64(new Uint8Array(rawEffects))
        };
      });
    }
    get version() {
      return "1.0.0";
    }
    get name() {
      return WALLET_NAME;
    }
    get icon() {
      return "data:image/png;base64,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";
    }
    // Return the Sui chains that your wallet supports.
    get chains() {
      return SUI_CHAINS;
    }
    get accounts() {
      return [account];
    }
    get features() {
      return {
        "standard:connect": {
          version: "1.0.0",
          connect: __privateGet(this, _connect)
        },
        "standard:events": {
          version: "1.0.0",
          on: __privateGet(this, _on)
        },
        "sui:signPersonalMessage": {
          version: "1.1.0",
          signPersonalMessage: __privateGet(this, _signPersonalMessage)
        },
        "sui:signTransactionBlock": {
          version: "1.0.0",
          signTransactionBlock: __privateGet(this, _signTransactionBlock)
        },
        "sui:signAndExecuteTransactionBlock": {
          version: "1.0.0",
          signAndExecuteTransactionBlock: __privateGet(this, _signAndExecuteTransactionBlock)
        },
        "sui:signTransaction": {
          version: "2.0.0",
          signTransaction: __privateGet(this, _signTransaction)
        },
        "sui:signAndExecuteTransaction": {
          version: "2.0.0",
          signAndExecuteTransaction: __privateGet(this, _signAndExecuteTransaction)
        }
      };
    }
  }
  _on = new WeakMap();
  _connect = new WeakMap();
  _signPersonalMessage = new WeakMap();
  _signTransactionBlock = new WeakMap();
  _signTransaction = new WeakMap();
  _signAndExecuteTransactionBlock = new WeakMap();
  _signAndExecuteTransaction = new WeakMap();
  return walletsApi.register(new UnsafeBurnerWallet());
}

// src/hooks/wallet/useWalletPropertiesChanged.ts
import { useEffect as useEffect2 } from "react";
function useWalletPropertiesChanged() {
  const { currentWallet } = useCurrentWallet();
  const updateWalletAccounts = useWalletStore((state) => state.updateWalletAccounts);
  useEffect2(() => {
    const unsubscribeFromEvents = currentWallet?.features["standard:events"].on(
      "change",
      ({ accounts }) => {
        if (accounts) {
          updateWalletAccounts(accounts);
        }
      }
    );
    return unsubscribeFromEvents;
  }, [currentWallet?.features, updateWalletAccounts]);
}

// src/hooks/wallet/useWalletsChanged.ts
import { getWallets as getWallets3 } from "@mysten/wallet-standard";
import { useEffect as useEffect3 } from "react";
function useWalletsChanged(preferredWallets, walletFilter) {
  const setWalletRegistered = useWalletStore((state) => state.setWalletRegistered);
  const setWalletUnregistered = useWalletStore((state) => state.setWalletUnregistered);
  useEffect3(() => {
    const walletsApi = getWallets3();
    setWalletRegistered(getRegisteredWallets(preferredWallets, walletFilter));
    const unsubscribeFromRegister = walletsApi.on("register", () => {
      setWalletRegistered(getRegisteredWallets(preferredWallets, walletFilter));
    });
    const unsubscribeFromUnregister = walletsApi.on("unregister", (unregisteredWallet) => {
      setWalletUnregistered(
        getRegisteredWallets(preferredWallets, walletFilter),
        unregisteredWallet
      );
    });
    return () => {
      unsubscribeFromRegister();
      unsubscribeFromUnregister();
    };
  }, [preferredWallets, walletFilter, setWalletRegistered, setWalletUnregistered]);
}

// src/themes/lightTheme.ts
var lightTheme = {
  blurs: {
    modalOverlay: "blur(0)"
  },
  backgroundColors: {
    primaryButton: "#F6F7F9",
    primaryButtonHover: "#F0F2F5",
    outlineButtonHover: "#F4F4F5",
    modalOverlay: "rgba(24 36 53 / 20%)",
    modalPrimary: "white",
    modalSecondary: "#F7F8F8",
    iconButton: "transparent",
    iconButtonHover: "#F0F1F2",
    dropdownMenu: "#FFFFFF",
    dropdownMenuSeparator: "#F3F6F8",
    walletItemSelected: "white",
    walletItemHover: "#3C424226"
  },
  borderColors: {
    outlineButton: "#E4E4E7"
  },
  colors: {
    primaryButton: "#373737",
    outlineButton: "#373737",
    iconButton: "#000000",
    body: "#182435",
    bodyMuted: "#767A81",
    bodyDanger: "#FF794B"
  },
  radii: {
    small: "6px",
    medium: "8px",
    large: "12px",
    xlarge: "16px"
  },
  shadows: {
    primaryButton: "0px 4px 12px rgba(0, 0, 0, 0.1)",
    walletItemSelected: "0px 2px 6px rgba(0, 0, 0, 0.05)"
  },
  fontWeights: {
    normal: "400",
    medium: "500",
    bold: "600"
  },
  fontSizes: {
    small: "14px",
    medium: "16px",
    large: "18px",
    xlarge: "20px"
  },
  typography: {
    fontFamily: 'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"',
    fontStyle: "normal",
    lineHeight: "1.3",
    letterSpacing: "1"
  }
};

// src/walletStore.ts
import { createStore } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";
function createWalletStore({
  wallets,
  storage,
  storageKey,
  autoConnectEnabled
}) {
  return createStore()(
    persist(
      (set, get) => ({
        autoConnectEnabled,
        wallets,
        accounts: [],
        currentWallet: null,
        currentAccount: null,
        lastConnectedAccountAddress: null,
        lastConnectedWalletName: null,
        connectionStatus: "disconnected",
        supportedIntents: [],
        setConnectionStatus(connectionStatus2) {
          set(() => ({
            connectionStatus: connectionStatus2
          }));
        },
        setWalletConnected(wallet, connectedAccounts, selectedAccount, supportedIntents = []) {
          set(() => ({
            accounts: connectedAccounts,
            currentWallet: wallet,
            currentAccount: selectedAccount,
            lastConnectedWalletName: getWalletUniqueIdentifier(wallet),
            lastConnectedAccountAddress: selectedAccount?.address,
            connectionStatus: "connected",
            supportedIntents
          }));
        },
        setWalletDisconnected() {
          set(() => ({
            accounts: [],
            currentWallet: null,
            currentAccount: null,
            lastConnectedWalletName: null,
            lastConnectedAccountAddress: null,
            connectionStatus: "disconnected",
            supportedIntents: []
          }));
        },
        setAccountSwitched(selectedAccount) {
          set(() => ({
            currentAccount: selectedAccount,
            lastConnectedAccountAddress: selectedAccount.address
          }));
        },
        setWalletRegistered(updatedWallets) {
          set(() => ({ wallets: updatedWallets }));
        },
        setWalletUnregistered(updatedWallets, unregisteredWallet) {
          if (unregisteredWallet === get().currentWallet) {
            set(() => ({
              wallets: updatedWallets,
              accounts: [],
              currentWallet: null,
              currentAccount: null,
              lastConnectedWalletName: null,
              lastConnectedAccountAddress: null,
              connectionStatus: "disconnected",
              supportedIntents: []
            }));
          } else {
            set(() => ({ wallets: updatedWallets }));
          }
        },
        updateWalletAccounts(accounts) {
          const currentAccount = get().currentAccount;
          set(() => ({
            accounts,
            currentAccount: currentAccount && accounts.find(({ address }) => address === currentAccount.address) || accounts[0]
          }));
        }
      }),
      {
        name: storageKey,
        storage: createJSONStorage(() => storage),
        partialize: ({ lastConnectedWalletName, lastConnectedAccountAddress }) => ({
          lastConnectedWalletName,
          lastConnectedAccountAddress
        })
      }
    )
  );
}

// src/components/styling/InjectedThemeStyles.tsx
import { assignInlineVars } from "@vanilla-extract/dynamic";

// src/themes/themeContract.ts
import { createGlobalThemeContract } from "@vanilla-extract/css";
var themeContractValues = {
  blurs: {
    modalOverlay: ""
  },
  backgroundColors: {
    primaryButton: "",
    primaryButtonHover: "",
    outlineButtonHover: "",
    walletItemHover: "",
    walletItemSelected: "",
    modalOverlay: "",
    modalPrimary: "",
    modalSecondary: "",
    iconButton: "",
    iconButtonHover: "",
    dropdownMenu: "",
    dropdownMenuSeparator: ""
  },
  borderColors: {
    outlineButton: ""
  },
  colors: {
    primaryButton: "",
    outlineButton: "",
    body: "",
    bodyMuted: "",
    bodyDanger: "",
    iconButton: ""
  },
  radii: {
    small: "",
    medium: "",
    large: "",
    xlarge: ""
  },
  shadows: {
    primaryButton: "",
    walletItemSelected: ""
  },
  fontWeights: {
    normal: "",
    medium: "",
    bold: ""
  },
  fontSizes: {
    small: "",
    medium: "",
    large: "",
    xlarge: ""
  },
  typography: {
    fontFamily: "",
    fontStyle: "",
    lineHeight: "",
    letterSpacing: ""
  }
};
var themeVars = createGlobalThemeContract(
  themeContractValues,
  (_, path) => `dapp-kit-${path.join("-")}`
);

// src/components/styling/InjectedThemeStyles.tsx
import { jsx as jsx21 } from "react/jsx-runtime";
function InjectedThemeStyles({ theme }) {
  const themeStyles = Array.isArray(theme) ? getDynamicThemeStyles(theme) : getStaticThemeStyles(theme);
  return /* @__PURE__ */ jsx21(
    "style",
    {
      precedence: "default",
      href: "mysten-dapp-kit-theme",
      dangerouslySetInnerHTML: {
        __html: themeStyles
      }
    }
  );
}
function getDynamicThemeStyles(themes) {
  return themes.map(({ mediaQuery, selector, variables }) => {
    const themeStyles = getStaticThemeStyles(variables);
    const themeStylesWithSelectorPrefix = selector ? `${selector} ${themeStyles}` : themeStyles;
    return mediaQuery ? `@media ${mediaQuery}{${themeStylesWithSelectorPrefix}}` : themeStylesWithSelectorPrefix;
  }).join(" ");
}
function getStaticThemeStyles(theme) {
  return `${styleDataAttributeSelector} {${cssStringFromTheme(theme)}}`;
}
function cssStringFromTheme(theme) {
  return Object.entries(assignInlineVars(themeVars, theme)).map(([key, value]) => `${key}:${value};`).join("");
}

// src/components/WalletProvider.tsx
import { jsx as jsx22, jsxs as jsxs9 } from "react/jsx-runtime";
function WalletProvider({
  preferredWallets = DEFAULT_PREFERRED_WALLETS,
  walletFilter = DEFAULT_WALLET_FILTER,
  storage = DEFAULT_STORAGE,
  storageKey = DEFAULT_STORAGE_KEY,
  enableUnsafeBurner = false,
  autoConnect = false,
  slushWallet,
  theme = lightTheme,
  children
}) {
  const storeRef = useRef(
    createWalletStore({
      autoConnectEnabled: autoConnect,
      wallets: getRegisteredWallets(preferredWallets, walletFilter),
      storage: storage || createInMemoryStore(),
      storageKey
    })
  );
  return /* @__PURE__ */ jsx22(WalletContext.Provider, { value: storeRef.current, children: /* @__PURE__ */ jsxs9(
    WalletConnectionManager,
    {
      preferredWallets,
      walletFilter,
      enableUnsafeBurner,
      slushWallet,
      children: [
        theme ? /* @__PURE__ */ jsx22(InjectedThemeStyles, { theme }) : null,
        children
      ]
    }
  ) });
}
function WalletConnectionManager({
  preferredWallets = DEFAULT_PREFERRED_WALLETS,
  walletFilter = DEFAULT_WALLET_FILTER,
  enableUnsafeBurner = false,
  slushWallet,
  children
}) {
  useWalletsChanged(preferredWallets, walletFilter);
  useWalletPropertiesChanged();
  useSlushWallet(slushWallet);
  useUnsafeBurnerWallet(enableUnsafeBurner);
  useAutoConnectWallet();
  return children;
}

// src/hooks/networkConfig.ts
function createNetworkConfig(networkConfig) {
  function useNetworkConfig() {
    const { config } = useSuiClientContext();
    if (!config) {
      throw new Error("No network config found");
    }
    return config;
  }
  function useNetworkVariables() {
    const { variables } = useNetworkConfig();
    return variables ?? {};
  }
  function useNetworkVariable(name) {
    const variables = useNetworkVariables();
    return variables[name];
  }
  return {
    networkConfig,
    useNetworkConfig,
    useNetworkVariables,
    useNetworkVariable
  };
}

// src/hooks/useSuiClientInfiniteQuery.ts
import { useInfiniteQuery } from "@tanstack/react-query";
function useSuiClientInfiniteQuery(method, params, {
  queryKey = [],
  enabled = !!params,
  ...options
} = {}) {
  const suiContext = useSuiClientContext();
  return useInfiniteQuery({
    ...options,
    initialPageParam: null,
    queryKey: [suiContext.network, method, params, ...queryKey],
    enabled,
    queryFn: ({ pageParam }) => suiContext.client[method]({
      ...params ?? {},
      cursor: pageParam
    }),
    getNextPageParam: (lastPage) => lastPage.hasNextPage ? lastPage.nextCursor ?? null : null
  });
}

// src/hooks/useSuiClientMutation.ts
import { useMutation as useMutation4 } from "@tanstack/react-query";
function useSuiClientMutation(method, options = {}) {
  const suiContext = useSuiClientContext();
  return useMutation4({
    ...options,
    mutationFn: async (params) => {
      return await suiContext.client[method](params);
    }
  });
}

// src/hooks/useSuiClientQueries.ts
import { useQueries } from "@tanstack/react-query";
function useSuiClientQueries({
  queries,
  combine
}) {
  const suiContext = useSuiClientContext();
  return useQueries({
    combine,
    queries: queries.map((query) => {
      const { method, params, options: { queryKey = [], ...restOptions } = {} } = query;
      return {
        ...restOptions,
        queryKey: [suiContext.network, method, params, ...queryKey],
        queryFn: async () => {
          return await suiContext.client[method](params);
        }
      };
    })
  });
}

// src/hooks/wallet/useSignAndExecuteTransaction.ts
import { toBase64 as toBase643 } from "@mysten/sui/utils";
import { signTransaction } from "@mysten/wallet-standard";
import { useMutation as useMutation6 } from "@tanstack/react-query";

// src/hooks/wallet/useReportTransactionEffects.ts
import { toBase64 as toBase642 } from "@mysten/sui/utils";
import { useMutation as useMutation5 } from "@tanstack/react-query";
function useReportTransactionEffects({
  mutationKey,
  ...mutationOptions
} = {}) {
  const { currentWallet } = useCurrentWallet();
  const currentAccount = useCurrentAccount();
  return useMutation5({
    mutationKey: walletMutationKeys.reportTransactionEffects(mutationKey),
    mutationFn: async ({ effects, chain = currentWallet?.chains[0], account = currentAccount }) => {
      if (!currentWallet) {
        throw new WalletNotConnectedError("No wallet is connected.");
      }
      if (!account) {
        throw new WalletNoAccountSelectedError(
          "No wallet account is selected to report transaction effects for"
        );
      }
      const reportTransactionEffectsFeature = currentWallet.features["sui:reportTransactionEffects"];
      if (reportTransactionEffectsFeature) {
        return await reportTransactionEffectsFeature.reportTransactionEffects({
          effects: Array.isArray(effects) ? toBase642(new Uint8Array(effects)) : effects,
          account,
          chain: chain ?? currentWallet?.chains[0]
        });
      }
    },
    ...mutationOptions
  });
}

// src/hooks/wallet/useSignAndExecuteTransaction.ts
function useSignAndExecuteTransaction({
  mutationKey,
  execute,
  ...mutationOptions
} = {}) {
  const { currentWallet, supportedIntents } = useCurrentWallet();
  const currentAccount = useCurrentAccount();
  const { client, network } = useSuiClientContext();
  const { mutate: reportTransactionEffects } = useReportTransactionEffects();
  const executeTransaction = execute ?? (async ({ bytes, signature }) => {
    const { digest, rawEffects } = await client.executeTransactionBlock({
      transactionBlock: bytes,
      signature,
      options: {
        showRawEffects: true
      }
    });
    return {
      digest,
      rawEffects,
      effects: toBase643(new Uint8Array(rawEffects)),
      bytes,
      signature
    };
  });
  return useMutation6({
    mutationKey: walletMutationKeys.signAndExecuteTransaction(mutationKey),
    mutationFn: async ({ transaction, ...signTransactionArgs }) => {
      if (!currentWallet) {
        throw new WalletNotConnectedError("No wallet is connected.");
      }
      const signerAccount = signTransactionArgs.account ?? currentAccount;
      if (!signerAccount) {
        throw new WalletNoAccountSelectedError(
          "No wallet account is selected to sign the transaction with."
        );
      }
      if (!currentWallet.features["sui:signTransaction"] && !currentWallet.features["sui:signTransactionBlock"]) {
        throw new WalletFeatureNotSupportedError(
          "This wallet doesn't support the `signTransaction` feature."
        );
      }
      const chain = signTransactionArgs.chain ?? `sui:${network}`;
      const { signature, bytes } = await signTransaction(currentWallet, {
        ...signTransactionArgs,
        transaction: {
          async toJSON() {
            return typeof transaction === "string" ? transaction : await transaction.toJSON({
              supportedIntents,
              client
            });
          }
        },
        account: signerAccount,
        chain
      });
      const result = await executeTransaction({ bytes, signature });
      let effects;
      if ("effects" in result && result.effects?.bcs) {
        effects = result.effects.bcs;
      } else if ("rawEffects" in result) {
        effects = toBase643(new Uint8Array(result.rawEffects));
      } else {
        throw new Error("Could not parse effects from transaction result.");
      }
      reportTransactionEffects({ effects, account: signerAccount, chain });
      return result;
    },
    ...mutationOptions
  });
}

// src/hooks/wallet/useSignPersonalMessage.ts
import { useMutation as useMutation7 } from "@tanstack/react-query";
function useSignPersonalMessage({
  mutationKey,
  ...mutationOptions
} = {}) {
  const { currentWallet } = useCurrentWallet();
  const currentAccount = useCurrentAccount();
  const { network } = useSuiClientContext();
  return useMutation7({
    mutationKey: walletMutationKeys.signPersonalMessage(mutationKey),
    mutationFn: async (signPersonalMessageArgs) => {
      if (!currentWallet) {
        throw new WalletNotConnectedError("No wallet is connected.");
      }
      const signerAccount = signPersonalMessageArgs.account ?? currentAccount;
      if (!signerAccount) {
        throw new WalletNoAccountSelectedError(
          "No wallet account is selected to sign the personal message with."
        );
      }
      const signPersonalMessageFeature = currentWallet.features["sui:signPersonalMessage"];
      if (signPersonalMessageFeature) {
        return await signPersonalMessageFeature.signPersonalMessage({
          ...signPersonalMessageArgs,
          account: signerAccount,
          chain: signPersonalMessageArgs.chain ?? `sui:${network}`
        });
      }
      const signMessageFeature = currentWallet.features["sui:signMessage"];
      if (signMessageFeature) {
        console.warn(
          "This wallet doesn't support the `signPersonalMessage` feature... falling back to `signMessage`."
        );
        const { messageBytes, signature } = await signMessageFeature.signMessage({
          ...signPersonalMessageArgs,
          account: signerAccount
        });
        return { bytes: messageBytes, signature };
      }
      throw new WalletFeatureNotSupportedError(
        "This wallet doesn't support the `signPersonalMessage` feature."
      );
    },
    ...mutationOptions
  });
}

// src/hooks/wallet/useSignTransaction.ts
import { signTransaction as signTransaction2 } from "@mysten/wallet-standard";
import { useMutation as useMutation8 } from "@tanstack/react-query";
function useSignTransaction({
  mutationKey,
  ...mutationOptions
} = {}) {
  const { currentWallet } = useCurrentWallet();
  const currentAccount = useCurrentAccount();
  const { client, network } = useSuiClientContext();
  const { mutate: reportTransactionEffects } = useReportTransactionEffects();
  return useMutation8({
    mutationKey: walletMutationKeys.signTransaction(mutationKey),
    mutationFn: async ({ transaction, ...signTransactionArgs }) => {
      if (!currentWallet) {
        throw new WalletNotConnectedError("No wallet is connected.");
      }
      const signerAccount = signTransactionArgs.account ?? currentAccount;
      if (!signerAccount) {
        throw new WalletNoAccountSelectedError(
          "No wallet account is selected to sign the transaction with."
        );
      }
      if (!currentWallet.features["sui:signTransaction"] && !currentWallet.features["sui:signTransactionBlock"]) {
        throw new WalletFeatureNotSupportedError(
          "This wallet doesn't support the `signTransaction` feature."
        );
      }
      const chain = signTransactionArgs.chain ?? `sui:${network}`;
      const { bytes, signature } = await signTransaction2(currentWallet, {
        ...signTransactionArgs,
        transaction: {
          toJSON: async () => {
            return typeof transaction === "string" ? transaction : await transaction.toJSON({
              supportedIntents: [],
              client
            });
          }
        },
        account: signerAccount,
        chain
      });
      return {
        bytes,
        signature,
        reportTransactionEffects: (effects) => {
          reportTransactionEffects({
            effects,
            account: signerAccount,
            chain
          });
        }
      };
    },
    ...mutationOptions
  });
}
export {
  ConnectButton,
  ConnectModal,
  SuiClientContext,
  SuiClientProvider,
  WalletProvider,
  createNetworkConfig,
  getSuiClientQuery,
  lightTheme,
  useAccounts,
  useAutoConnectWallet,
  useConnectWallet,
  useCurrentAccount,
  useCurrentWallet,
  useDisconnectWallet,
  useReportTransactionEffects,
  useResolveSuiNSName,
  useSignAndExecuteTransaction,
  useSignPersonalMessage,
  useSignTransaction,
  useSuiClient,
  useSuiClientContext,
  useSuiClientInfiniteQuery,
  useSuiClientMutation,
  useSuiClientQueries,
  useSuiClientQuery,
  useSuiClientSuspenseQuery,
  useSwitchAccount,
  useWallets
};
//# sourceMappingURL=index.js.map
