{"version": 3, "sources": ["../../../src/components/WalletProvider.tsx", "../../../src/constants/walletDefaults.ts", "../../../src/utils/stateStorage.ts", "../../../src/contexts/walletContext.ts", "../../../src/hooks/wallet/useAutoConnectWallet.ts", "../../../src/utils/walletUtils.ts", "../../../src/hooks/wallet/useConnectWallet.ts", "../../../src/constants/walletMutationKeys.ts", "../../../src/hooks/wallet/useWalletStore.ts", "../../../src/hooks/wallet/useCurrentWallet.ts", "../../../src/hooks/wallet/useWallets.ts", "../../../src/hooks/wallet/useSlushWallet.ts", "../../../src/hooks/wallet/useUnsafeBurnerWallet.ts", "../../../src/hooks/useSuiClient.ts", "../../../src/components/SuiClientProvider.tsx", "../../../src/hooks/wallet/useWalletPropertiesChanged.ts", "../../../src/hooks/wallet/useWalletsChanged.ts", "../../../src/themes/lightTheme.ts", "../../../src/walletStore.ts", "../../../src/components/styling/InjectedThemeStyles.tsx", "../../../src/constants/styleDataAttribute.ts", "../../../src/themes/themeContract.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { WalletWithFeatures, WalletWithRequiredFeatures } from '@mysten/wallet-standard';\nimport type { ReactNode } from 'react';\nimport { useRef } from 'react';\nimport type { StateStorage } from 'zustand/middleware';\n\nimport {\n\tDEFAULT_PREFERRED_WALLETS,\n\tDEFAULT_STORAGE,\n\tDEFAULT_STORAGE_KEY,\n\tDEFAULT_WALLET_FILTER,\n} from '../constants/walletDefaults.js';\nimport { WalletContext } from '../contexts/walletContext.js';\nimport { useAutoConnectWallet } from '../hooks/wallet/useAutoConnectWallet.js';\nimport type { SlushWalletConfig } from '../hooks/wallet/useSlushWallet.js';\nimport { useSlushWallet } from '../hooks/wallet/useSlushWallet.js';\nimport { useUnsafeBurnerWallet } from '../hooks/wallet/useUnsafeBurnerWallet.js';\nimport { useWalletPropertiesChanged } from '../hooks/wallet/useWalletPropertiesChanged.js';\nimport { useWalletsChanged } from '../hooks/wallet/useWalletsChanged.js';\nimport { lightTheme } from '../themes/lightTheme.js';\nimport type { Theme } from '../themes/themeContract.js';\nimport { createInMemoryStore } from '../utils/stateStorage.js';\nimport { getRegisteredWallets } from '../utils/walletUtils.js';\nimport { createWalletStore } from '../walletStore.js';\nimport { InjectedThemeStyles } from './styling/InjectedThemeStyles.js';\n\nexport type WalletProviderProps = {\n\t/** A list of wallets that are sorted to the top of the wallet list, if they are available to connect to. By default, wallets are sorted by the order they are loaded in. */\n\tpreferredWallets?: string[];\n\n\t/** A filter function to select wallets that support features required for the dApp to function. This filters the list of wallets presented to users when selecting a wallet to connect from, ensuring that only wallets that meet the dApps requirements can connect. */\n\twalletFilter?: (wallet: WalletWithRequiredFeatures) => boolean;\n\n\t/** Enables the development-only unsafe burner wallet, which can be useful for testing. */\n\tenableUnsafeBurner?: boolean;\n\n\t/** Enables automatically reconnecting to the most recently used wallet account upon mounting. */\n\tautoConnect?: boolean;\n\n\t/** Enables the Slush wallet */\n\tslushWallet?: SlushWalletConfig;\n\n\t/** Configures how the most recently connected to wallet account is stored. Set to `null` to disable persisting state entirely. Defaults to using localStorage if it is available. */\n\tstorage?: StateStorage | null;\n\n\t/** The key to use to store the most recently connected wallet account. */\n\tstorageKey?: string;\n\n\t/** The theme to use for styling UI components. Defaults to using the light theme. */\n\ttheme?: Theme | null;\n\n\tchildren: ReactNode;\n};\n\nexport type { WalletWithFeatures };\n\nexport function WalletProvider({\n\tpreferredWallets = DEFAULT_PREFERRED_WALLETS,\n\twalletFilter = DEFAULT_WALLET_FILTER,\n\tstorage = DEFAULT_STORAGE,\n\tstorageKey = DEFAULT_STORAGE_KEY,\n\tenableUnsafeBurner = false,\n\tautoConnect = false,\n\tslushWallet,\n\ttheme = lightTheme,\n\tchildren,\n}: WalletProviderProps) {\n\tconst storeRef = useRef(\n\t\tcreateWalletStore({\n\t\t\tautoConnectEnabled: autoConnect,\n\t\t\twallets: getRegisteredWallets(preferredWallets, walletFilter),\n\t\t\tstorage: storage || createInMemoryStore(),\n\t\t\tstorageKey,\n\t\t}),\n\t);\n\n\treturn (\n\t\t<WalletContext.Provider value={storeRef.current}>\n\t\t\t<WalletConnectionManager\n\t\t\t\tpreferredWallets={preferredWallets}\n\t\t\t\twalletFilter={walletFilter}\n\t\t\t\tenableUnsafeBurner={enableUnsafeBurner}\n\t\t\t\tslushWallet={slushWallet}\n\t\t\t>\n\t\t\t\t{/* TODO: We ideally don't want to inject styles if people aren't using the UI components */}\n\t\t\t\t{theme ? <InjectedThemeStyles theme={theme} /> : null}\n\t\t\t\t{children}\n\t\t\t</WalletConnectionManager>\n\t\t</WalletContext.Provider>\n\t);\n}\n\ntype WalletConnectionManagerProps = Pick<\n\tWalletProviderProps,\n\t'preferredWallets' | 'walletFilter' | 'enableUnsafeBurner' | 'slushWallet' | 'children'\n>;\n\nfunction WalletConnectionManager({\n\tpreferredWallets = DEFAULT_PREFERRED_WALLETS,\n\twalletFilter = DEFAULT_WALLET_FILTER,\n\tenableUnsafeBurner = false,\n\tslushWallet,\n\tchildren,\n}: WalletConnectionManagerProps) {\n\tuseWalletsChanged(preferredWallets, walletFilter);\n\tuseWalletPropertiesChanged();\n\tuseSlushWallet(slushWallet);\n\tuseUnsafeBurnerWallet(enableUnsafeBurner);\n\tuseAutoConnectWallet();\n\n\treturn children;\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SuiWalletFeatures, WalletWithRequiredFeatures } from '@mysten/wallet-standard';\nimport { SLUSH_WALLET_NAME } from '@mysten/slush-wallet';\n\nimport { createInMemoryStore } from '../utils/stateStorage.js';\n\nexport const SUI_WALLET_NAME = 'Sui Wallet';\n\nexport const DEFAULT_STORAGE =\n\ttypeof window !== 'undefined' && window.localStorage ? localStorage : createInMemoryStore();\n\nexport const DEFAULT_STORAGE_KEY = 'sui-dapp-kit:wallet-connection-info';\n\nconst SIGN_FEATURES = [\n\t'sui:signTransaction',\n\t'sui:signTransactionBlock',\n] satisfies (keyof SuiWalletFeatures)[];\n\nexport const DEFAULT_WALLET_FILTER = (wallet: WalletWithRequiredFeatures) =>\n\tSIGN_FEATURES.some((feature) => wallet.features[feature]);\n\nexport const DEFAULT_PREFERRED_WALLETS = [SUI_WALLET_NAME, SLUSH_WALLET_NAME];\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { StateStorage } from 'zustand/middleware';\n\nexport function createInMemoryStore(): StateStorage {\n\tconst store = new Map();\n\treturn {\n\t\tgetItem(key: string) {\n\t\t\treturn store.get(key);\n\t\t},\n\t\tsetItem(key: string, value: string) {\n\t\t\tstore.set(key, value);\n\t\t},\n\t\tremoveItem(key: string) {\n\t\t\tstore.delete(key);\n\t\t},\n\t};\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { createContext } from 'react';\n\nimport type { WalletStore } from '../walletStore.js';\n\nexport const WalletContext = createContext<WalletStore | null>(null);\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { useQuery } from '@tanstack/react-query';\nimport { useLayoutEffect, useState } from 'react';\n\nimport { getWalletUniqueIdentifier } from '../../utils/walletUtils.js';\nimport { useConnectWallet } from './useConnectWallet.js';\nimport { useCurrentWallet } from './useCurrentWallet.js';\nimport { useWallets } from './useWallets.js';\nimport { useWalletStore } from './useWalletStore.js';\n\nexport function useAutoConnectWallet(): 'disabled' | 'idle' | 'attempted' {\n\tconst { mutateAsync: connectWallet } = useConnectWallet();\n\tconst autoConnectEnabled = useWalletStore((state) => state.autoConnectEnabled);\n\tconst lastConnectedWalletName = useWalletStore((state) => state.lastConnectedWalletName);\n\tconst lastConnectedAccountAddress = useWalletStore((state) => state.lastConnectedAccountAddress);\n\tconst wallets = useWallets();\n\tconst { isConnected } = useCurrentWallet();\n\n\tconst [clientOnly, setClientOnly] = useState(false);\n\tuseLayoutEffect(() => {\n\t\tsetClientOnly(true);\n\t}, []);\n\n\tconst { data, isError } = useQuery({\n\t\tqueryKey: [\n\t\t\t'@mysten/dapp-kit',\n\t\t\t'autoconnect',\n\t\t\t{\n\t\t\t\tisConnected,\n\t\t\t\tautoConnectEnabled,\n\t\t\t\tlastConnectedWalletName,\n\t\t\t\tlastConnectedAccountAddress,\n\t\t\t\twalletCount: wallets.length,\n\t\t\t},\n\t\t],\n\t\tqueryFn: async () => {\n\t\t\tif (!autoConnectEnabled) {\n\t\t\t\treturn 'disabled';\n\t\t\t}\n\n\t\t\tif (!lastConnectedWalletName || !lastConnectedAccountAddress || isConnected) {\n\t\t\t\treturn 'attempted';\n\t\t\t}\n\n\t\t\tconst wallet = wallets.find(\n\t\t\t\t(wallet) => getWalletUniqueIdentifier(wallet) === lastConnectedWalletName,\n\t\t\t);\n\t\t\tif (wallet) {\n\t\t\t\tawait connectWallet({\n\t\t\t\t\twallet,\n\t\t\t\t\taccountAddress: lastConnectedAccountAddress,\n\t\t\t\t\tsilent: true,\n\t\t\t\t});\n\t\t\t}\n\n\t\t\treturn 'attempted';\n\t\t},\n\t\tenabled: autoConnectEnabled,\n\t\tpersister: undefined,\n\t\tgcTime: 0,\n\t\tstaleTime: 0,\n\t\tnetworkMode: 'always',\n\t\tretry: false,\n\t\tretryOnMount: false,\n\t\trefetchInterval: false,\n\t\trefetchIntervalInBackground: false,\n\t\trefetchOnMount: false,\n\t\trefetchOnReconnect: false,\n\t\trefetchOnWindowFocus: false,\n\t});\n\n\tif (!autoConnectEnabled) {\n\t\treturn 'disabled';\n\t}\n\n\t// We always initialize with \"idle\" so that in SSR environments, we guarantee that the initial render states always agree:\n\tif (!clientOnly) {\n\t\treturn 'idle';\n\t}\n\n\tif (isConnected) {\n\t\treturn 'attempted';\n\t}\n\n\tif (!lastConnectedWalletName) {\n\t\treturn 'attempted';\n\t}\n\n\treturn isError ? 'attempted' : (data ?? 'idle');\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type {\n\tMinimallyRequiredFeatures,\n\tWallet,\n\tWalletWithFeatures,\n\tWalletWithRequiredFeatures,\n} from '@mysten/wallet-standard';\nimport { getWallets, isWalletWithRequiredFeatureSet } from '@mysten/wallet-standard';\n\nexport function getRegisteredWallets<AdditionalFeatures extends Wallet['features']>(\n\tpreferredWallets: string[],\n\twalletFilter?: (wallet: WalletWithRequiredFeatures) => boolean,\n) {\n\tconst walletsApi = getWallets();\n\tconst wallets = walletsApi.get();\n\n\tconst suiWallets = wallets.filter(\n\t\t(wallet): wallet is WalletWithFeatures<MinimallyRequiredFeatures & AdditionalFeatures> =>\n\t\t\tisWalletWithRequiredFeatureSet(wallet) && (!walletFilter || walletFilter(wallet)),\n\t);\n\n\treturn [\n\t\t// Preferred wallets, in order:\n\t\t...(preferredWallets\n\t\t\t.map((name) => suiWallets.find((wallet) => wallet.name === name))\n\t\t\t.filter(Boolean) as WalletWithFeatures<MinimallyRequiredFeatures & AdditionalFeatures>[]),\n\n\t\t// Wallets in default order:\n\t\t...suiWallets.filter((wallet) => !preferredWallets.includes(wallet.name)),\n\t];\n}\n\nexport function getWalletUniqueIdentifier(wallet?: Wallet) {\n\treturn wallet?.id ?? wallet?.name;\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type {\n\tStandardConnectInput,\n\tStandardConnectOutput,\n\tWalletAccount,\n\tWalletWithRequiredFeatures,\n} from '@mysten/wallet-standard';\nimport type { UseMutationOptions, UseMutationResult } from '@tanstack/react-query';\nimport { useMutation } from '@tanstack/react-query';\n\nimport { walletMutationKeys } from '../../constants/walletMutationKeys.js';\nimport { useWalletStore } from './useWalletStore.js';\n\ntype ConnectWalletArgs = {\n\t/** The wallet to connect to. */\n\twallet: WalletWithRequiredFeatures;\n\n\t/** An optional account address to connect to. Defaults to the first authorized account. */\n\taccountAddress?: string;\n} & StandardConnectInput;\n\ntype ConnectWalletResult = StandardConnectOutput;\n\ntype UseConnectWalletMutationOptions = Omit<\n\tUseMutationOptions<ConnectWalletResult, Error, ConnectWalletArgs, unknown>,\n\t'mutationFn'\n>;\n\n/**\n * Mutation hook for establishing a connection to a specific wallet.\n */\nexport function useConnectWallet({\n\tmutationKey,\n\t...mutationOptions\n}: UseConnectWalletMutationOptions = {}): UseMutationResult<\n\tConnectWalletResult,\n\tError,\n\tConnectWalletArgs,\n\tunknown\n> {\n\tconst setWalletConnected = useWalletStore((state) => state.setWalletConnected);\n\tconst setConnectionStatus = useWalletStore((state) => state.setConnectionStatus);\n\n\treturn useMutation({\n\t\tmutationKey: walletMutationKeys.connectWallet(mutationKey),\n\t\tmutationFn: async ({ wallet, accountAddress, ...connectArgs }) => {\n\t\t\ttry {\n\t\t\t\tsetConnectionStatus('connecting');\n\n\t\t\t\tconst connectResult = await wallet.features['standard:connect'].connect(connectArgs);\n\t\t\t\tconst connectedSuiAccounts = connectResult.accounts.filter((account) =>\n\t\t\t\t\taccount.chains.some((chain) => chain.split(':')[0] === 'sui'),\n\t\t\t\t);\n\t\t\t\tconst selectedAccount = getSelectedAccount(connectedSuiAccounts, accountAddress);\n\n\t\t\t\tsetWalletConnected(\n\t\t\t\t\twallet,\n\t\t\t\t\tconnectedSuiAccounts,\n\t\t\t\t\tselectedAccount,\n\t\t\t\t\tconnectResult.supportedIntents,\n\t\t\t\t);\n\n\t\t\t\treturn { accounts: connectedSuiAccounts };\n\t\t\t} catch (error) {\n\t\t\t\tsetConnectionStatus('disconnected');\n\t\t\t\tthrow error;\n\t\t\t}\n\t\t},\n\t\t...mutationOptions,\n\t});\n}\n\nfunction getSelectedAccount(connectedAccounts: readonly WalletAccount[], accountAddress?: string) {\n\tif (connectedAccounts.length === 0) {\n\t\treturn null;\n\t}\n\n\tif (accountAddress) {\n\t\tconst selectedAccount = connectedAccounts.find((account) => account.address === accountAddress);\n\t\treturn selectedAccount ?? connectedAccounts[0];\n\t}\n\n\treturn connectedAccounts[0];\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { MutationKey } from '@tanstack/react-query';\n\nexport const walletMutationKeys = {\n\tall: { baseScope: 'wallet' },\n\tconnectWallet: formMutationKeyFn('connect-wallet'),\n\tautoconnectWallet: formMutationKeyFn('autoconnect-wallet'),\n\tdisconnectWallet: formMutationKeyFn('disconnect-wallet'),\n\tsignPersonalMessage: formMutationKeyFn('sign-personal-message'),\n\tsignTransaction: formMutationKeyFn('sign-transaction'),\n\tsignAndExecuteTransaction: formMutationKeyFn('sign-and-execute-transaction'),\n\tswitchAccount: formMutationKeyFn('switch-account'),\n\treportTransactionEffects: formMutationKeyFn('report-transaction-effects'),\n};\n\nfunction formMutationKeyFn(baseEntity: string) {\n\treturn function mutationKeyFn(additionalKeys: MutationKey = []) {\n\t\treturn [{ ...walletMutationKeys.all, baseEntity }, ...additionalKeys];\n\t};\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { useContext } from 'react';\nimport { useStore } from 'zustand';\n\nimport { WalletContext } from '../../contexts/walletContext.js';\nimport type { StoreState } from '../../walletStore.js';\n\nexport function useWalletStore<T>(selector: (state: StoreState) => T): T {\n\tconst store = useContext(WalletContext);\n\tif (!store) {\n\t\tthrow new Error(\n\t\t\t'Could not find WalletContext. Ensure that you have set up the WalletProvider.',\n\t\t);\n\t}\n\treturn useStore(store, selector);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { useWalletStore } from './useWalletStore.js';\n\n/**\n * Retrieves the wallet that is currently connected to the dApp, if one exists.\n */\nexport function useCurrentWallet() {\n\tconst currentWallet = useWalletStore((state) => state.currentWallet);\n\tconst connectionStatus = useWalletStore((state) => state.connectionStatus);\n\tconst supportedIntents = useWalletStore((state) => state.supportedIntents);\n\n\tswitch (connectionStatus) {\n\t\tcase 'connecting':\n\t\t\treturn {\n\t\t\t\tconnectionStatus,\n\t\t\t\tcurrentWallet: null,\n\t\t\t\tisDisconnected: false,\n\t\t\t\tisConnecting: true,\n\t\t\t\tisConnected: false,\n\t\t\t\tsupportedIntents: [],\n\t\t\t} as const;\n\t\tcase 'disconnected':\n\t\t\treturn {\n\t\t\t\tconnectionStatus,\n\t\t\t\tcurrentWallet: null,\n\t\t\t\tisDisconnected: true,\n\t\t\t\tisConnecting: false,\n\t\t\t\tisConnected: false,\n\t\t\t\tsupportedIntents: [],\n\t\t\t} as const;\n\t\tcase 'connected': {\n\t\t\treturn {\n\t\t\t\tconnectionStatus,\n\t\t\t\tcurrentWallet: currentWallet!,\n\t\t\t\tisDisconnected: false,\n\t\t\t\tisConnecting: false,\n\t\t\t\tisConnected: true,\n\t\t\t\tsupportedIntents,\n\t\t\t} as const;\n\t\t}\n\t}\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { useWalletStore } from './useWalletStore.js';\n\n/**\n * Retrieves a list of registered wallets available to the dApp sorted by preference.\n */\nexport function useWallets() {\n\treturn useWalletStore((state) => state.wallets);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { registerSlushWallet } from '@mysten/slush-wallet';\nimport { useLayoutEffect } from 'react';\n\nexport interface SlushWalletConfig {\n\tname: string;\n\torigin?: string;\n}\n\nexport function useSlushWallet(config?: SlushWalletConfig) {\n\tuseLayoutEffect(() => {\n\t\tif (!config?.name) {\n\t\t\treturn;\n\t\t}\n\n\t\tlet cleanup: (() => void) | undefined;\n\t\tlet isMounted = true;\n\n\t\ttry {\n\t\t\tconst result = registerSlushWallet(config.name, {\n\t\t\t\torigin: config.origin,\n\t\t\t});\n\n\t\t\tif (isMounted && result) {\n\t\t\t\tcleanup = result.unregister;\n\t\t\t} else if (result) {\n\t\t\t\tresult.unregister();\n\t\t\t}\n\t\t} catch (error) {\n\t\t\tconsole.error('Failed to register Slush wallet:', error);\n\t\t}\n\n\t\treturn () => {\n\t\t\tisMounted = false;\n\t\t\tif (cleanup) cleanup();\n\t\t};\n\t}, [config?.name, config?.origin]);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SuiClient } from '@mysten/sui/client';\nimport { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519';\nimport { Transaction } from '@mysten/sui/transactions';\nimport { toBase64 } from '@mysten/sui/utils';\nimport type {\n\tStandardConnectFeature,\n\tStandardConnectMethod,\n\tStandardEventsFeature,\n\tStandardEventsOnMethod,\n\tSuiFeatures,\n\tSuiSignAndExecuteTransactionBlockMethod,\n\tSuiSignAndExecuteTransactionMethod,\n\tSuiSignPersonalMessageMethod,\n\tSuiSignTransactionBlockMethod,\n\tSuiSignTransactionMethod,\n\tWallet,\n} from '@mysten/wallet-standard';\nimport { getWallets, ReadonlyWalletAccount, SUI_CHAINS } from '@mysten/wallet-standard';\nimport { useEffect } from 'react';\n\nimport { useSuiClient } from '../useSuiClient.js';\n\nconst WALLET_NAME = 'Unsafe Burner Wallet';\n\nexport function useUnsafeBurnerWallet(enabled: boolean) {\n\tconst suiClient = useSuiClient();\n\n\tuseEffect(() => {\n\t\tif (!enabled) {\n\t\t\treturn;\n\t\t}\n\t\tconst unregister = registerUnsafeBurnerWallet(suiClient);\n\t\treturn unregister;\n\t}, [enabled, suiClient]);\n}\n\nfunction registerUnsafeBurnerWallet(suiClient: SuiClient) {\n\tconst walletsApi = getWallets();\n\tconst registeredWallets = walletsApi.get();\n\n\tif (registeredWallets.find((wallet) => wallet.name === WALLET_NAME)) {\n\t\tconsole.warn(\n\t\t\t'registerUnsafeBurnerWallet: Unsafe Burner Wallet already registered, skipping duplicate registration.',\n\t\t);\n\t\treturn;\n\t}\n\n\tconsole.warn(\n\t\t'Your application is currently using the unsafe burner wallet. Make sure that this wallet is disabled in production.',\n\t);\n\n\tconst keypair = new Ed25519Keypair();\n\tconst account = new ReadonlyWalletAccount({\n\t\taddress: keypair.getPublicKey().toSuiAddress(),\n\t\tpublicKey: keypair.getPublicKey().toSuiBytes(),\n\t\tchains: ['sui:unknown'],\n\t\tfeatures: [\n\t\t\t'sui:signAndExecuteTransactionBlock',\n\t\t\t'sui:signTransactionBlock',\n\t\t\t'sui:signTransaction',\n\t\t\t'sui:signAndExecuteTransaction',\n\t\t],\n\t});\n\n\tclass UnsafeBurnerWallet implements Wallet {\n\t\tget version() {\n\t\t\treturn '1.0.0' as const;\n\t\t}\n\n\t\tget name() {\n\t\t\treturn WALLET_NAME;\n\t\t}\n\n\t\tget icon() {\n\t\t\treturn 'data:image/png;base64,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' as const;\n\t\t}\n\n\t\t// Return the Sui chains that your wallet supports.\n\t\tget chains() {\n\t\t\treturn SUI_CHAINS;\n\t\t}\n\n\t\tget accounts() {\n\t\t\treturn [account];\n\t\t}\n\n\t\tget features(): StandardConnectFeature & StandardEventsFeature & SuiFeatures {\n\t\t\treturn {\n\t\t\t\t'standard:connect': {\n\t\t\t\t\tversion: '1.0.0',\n\t\t\t\t\tconnect: this.#connect,\n\t\t\t\t},\n\t\t\t\t'standard:events': {\n\t\t\t\t\tversion: '1.0.0',\n\t\t\t\t\ton: this.#on,\n\t\t\t\t},\n\t\t\t\t'sui:signPersonalMessage': {\n\t\t\t\t\tversion: '1.1.0',\n\t\t\t\t\tsignPersonalMessage: this.#signPersonalMessage,\n\t\t\t\t},\n\t\t\t\t'sui:signTransactionBlock': {\n\t\t\t\t\tversion: '1.0.0',\n\t\t\t\t\tsignTransactionBlock: this.#signTransactionBlock,\n\t\t\t\t},\n\t\t\t\t'sui:signAndExecuteTransactionBlock': {\n\t\t\t\t\tversion: '1.0.0',\n\t\t\t\t\tsignAndExecuteTransactionBlock: this.#signAndExecuteTransactionBlock,\n\t\t\t\t},\n\t\t\t\t'sui:signTransaction': {\n\t\t\t\t\tversion: '2.0.0',\n\t\t\t\t\tsignTransaction: this.#signTransaction,\n\t\t\t\t},\n\t\t\t\t'sui:signAndExecuteTransaction': {\n\t\t\t\t\tversion: '2.0.0',\n\t\t\t\t\tsignAndExecuteTransaction: this.#signAndExecuteTransaction,\n\t\t\t\t},\n\t\t\t};\n\t\t}\n\n\t\t#on: StandardEventsOnMethod = () => {\n\t\t\treturn () => {};\n\t\t};\n\n\t\t#connect: StandardConnectMethod = async () => {\n\t\t\treturn { accounts: this.accounts };\n\t\t};\n\n\t\t#signPersonalMessage: SuiSignPersonalMessageMethod = async (messageInput) => {\n\t\t\tconst { bytes, signature } = await keypair.signPersonalMessage(messageInput.message);\n\t\t\treturn { bytes, signature };\n\t\t};\n\n\t\t#signTransactionBlock: SuiSignTransactionBlockMethod = async (transactionInput) => {\n\t\t\tconst { bytes, signature } = await transactionInput.transactionBlock.sign({\n\t\t\t\tclient: suiClient,\n\t\t\t\tsigner: keypair,\n\t\t\t});\n\n\t\t\treturn {\n\t\t\t\ttransactionBlockBytes: bytes,\n\t\t\t\tsignature: signature,\n\t\t\t};\n\t\t};\n\n\t\t#signTransaction: SuiSignTransactionMethod = async (transactionInput) => {\n\t\t\tconst { bytes, signature } = await Transaction.from(\n\t\t\t\tawait transactionInput.transaction.toJSON(),\n\t\t\t).sign({\n\t\t\t\tclient: suiClient,\n\t\t\t\tsigner: keypair,\n\t\t\t});\n\n\t\t\ttransactionInput.signal?.throwIfAborted();\n\n\t\t\treturn {\n\t\t\t\tbytes,\n\t\t\t\tsignature: signature,\n\t\t\t};\n\t\t};\n\n\t\t#signAndExecuteTransactionBlock: SuiSignAndExecuteTransactionBlockMethod = async (\n\t\t\ttransactionInput,\n\t\t) => {\n\t\t\tconst { bytes, signature } = await transactionInput.transactionBlock.sign({\n\t\t\t\tclient: suiClient,\n\t\t\t\tsigner: keypair,\n\t\t\t});\n\n\t\t\treturn suiClient.executeTransactionBlock({\n\t\t\t\tsignature,\n\t\t\t\ttransactionBlock: bytes,\n\t\t\t\toptions: transactionInput.options,\n\t\t\t});\n\t\t};\n\n\t\t#signAndExecuteTransaction: SuiSignAndExecuteTransactionMethod = async (transactionInput) => {\n\t\t\tconst { bytes, signature } = await Transaction.from(\n\t\t\t\tawait transactionInput.transaction.toJSON(),\n\t\t\t).sign({\n\t\t\t\tclient: suiClient,\n\t\t\t\tsigner: keypair,\n\t\t\t});\n\n\t\t\ttransactionInput.signal?.throwIfAborted();\n\n\t\t\tconst { rawEffects, digest } = await suiClient.executeTransactionBlock({\n\t\t\t\tsignature,\n\t\t\t\ttransactionBlock: bytes,\n\t\t\t\toptions: {\n\t\t\t\t\tshowRawEffects: true,\n\t\t\t\t},\n\t\t\t});\n\n\t\t\treturn {\n\t\t\t\tbytes,\n\t\t\t\tsignature,\n\t\t\t\tdigest,\n\t\t\t\teffects: toBase64(new Uint8Array(rawEffects!)),\n\t\t\t};\n\t\t};\n\t}\n\n\treturn walletsApi.register(new UnsafeBurnerWallet());\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SuiClient } from '@mysten/sui/client';\nimport { useContext } from 'react';\n\nimport { SuiClientContext } from '../components/SuiClientProvider.js';\n\nexport function useSuiClientContext() {\n\tconst suiClient = useContext(SuiClientContext);\n\n\tif (!suiClient) {\n\t\tthrow new Error(\n\t\t\t'Could not find SuiClientContext. Ensure that you have set up the SuiClientProvider',\n\t\t);\n\t}\n\n\treturn suiClient;\n}\n\nexport function useSuiClient(): SuiClient {\n\treturn useSuiClientContext().client;\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { getFullnodeUrl, isSuiClient, SuiClient } from '@mysten/sui/client';\nimport type { SuiClientOptions } from '@mysten/sui/client';\nimport { createContext, useMemo, useState } from 'react';\n\nimport type { NetworkConfig } from '../hooks/networkConfig.js';\n\ntype NetworkConfigs<T extends NetworkConfig | SuiClient = NetworkConfig | SuiClient> = Record<\n\tstring,\n\tT\n>;\n\nexport interface SuiClientProviderContext {\n\tclient: SuiClient;\n\tnetworks: NetworkConfigs;\n\tnetwork: string;\n\tconfig: NetworkConfig | null;\n\tselectNetwork: (network: string) => void;\n}\n\nexport const SuiClientContext = createContext<SuiClientProviderContext | null>(null);\n\nexport type SuiClientProviderProps<T extends NetworkConfigs> = {\n\tcreateClient?: (name: keyof T, config: T[keyof T]) => SuiClient;\n\tchildren: React.ReactNode;\n\tnetworks?: T;\n\tonNetworkChange?: (network: keyof T & string) => void;\n} & (\n\t| {\n\t\t\tdefaultNetwork?: keyof T & string;\n\t\t\tnetwork?: never;\n\t  }\n\t| {\n\t\t\tdefaultNetwork?: never;\n\t\t\tnetwork?: keyof T & string;\n\t  }\n);\n\nconst DEFAULT_NETWORKS = {\n\tlocalnet: { url: getFullnodeUrl('localnet') },\n};\n\nconst DEFAULT_CREATE_CLIENT = function createClient(\n\t_name: string,\n\tconfig: NetworkConfig | SuiClient,\n) {\n\tif (isSuiClient(config)) {\n\t\treturn config;\n\t}\n\n\treturn new SuiClient(config);\n};\n\nexport function SuiClientProvider<T extends NetworkConfigs>(props: SuiClientProviderProps<T>) {\n\tconst { onNetworkChange, network, children } = props;\n\tconst networks = (props.networks ?? DEFAULT_NETWORKS) as T;\n\tconst createClient =\n\t\t(props.createClient as typeof DEFAULT_CREATE_CLIENT) ?? DEFAULT_CREATE_CLIENT;\n\n\tconst [selectedNetwork, setSelectedNetwork] = useState<keyof T & string>(\n\t\tprops.network ?? props.defaultNetwork ?? (Object.keys(networks)[0] as keyof T & string),\n\t);\n\n\tconst currentNetwork = props.network ?? selectedNetwork;\n\n\tconst client = useMemo(() => {\n\t\treturn createClient(currentNetwork, networks[currentNetwork]);\n\t}, [createClient, currentNetwork, networks]);\n\n\tconst ctx = useMemo((): SuiClientProviderContext => {\n\t\treturn {\n\t\t\tclient,\n\t\t\tnetworks,\n\t\t\tnetwork: currentNetwork,\n\t\t\tconfig:\n\t\t\t\tnetworks[currentNetwork] instanceof SuiClient\n\t\t\t\t\t? null\n\t\t\t\t\t: (networks[currentNetwork] as SuiClientOptions),\n\t\t\tselectNetwork: (newNetwork) => {\n\t\t\t\tif (currentNetwork === newNetwork) {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tif (!network && newNetwork !== selectedNetwork) {\n\t\t\t\t\tsetSelectedNetwork(newNetwork);\n\t\t\t\t}\n\n\t\t\t\tonNetworkChange?.(newNetwork);\n\t\t\t},\n\t\t};\n\t}, [client, networks, selectedNetwork, currentNetwork, network, onNetworkChange]);\n\n\treturn <SuiClientContext.Provider value={ctx}>{children}</SuiClientContext.Provider>;\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { useEffect } from 'react';\n\nimport { useCurrentWallet } from './useCurrentWallet.js';\nimport { useWalletStore } from './useWalletStore.js';\n\n/**\n * Internal hook for easily handling various changes in properties for a wallet.\n */\nexport function useWalletPropertiesChanged() {\n\tconst { currentWallet } = useCurrentWallet();\n\tconst updateWalletAccounts = useWalletStore((state) => state.updateWalletAccounts);\n\n\tuseEffect(() => {\n\t\tconst unsubscribeFromEvents = currentWallet?.features['standard:events'].on(\n\t\t\t'change',\n\t\t\t({ accounts }) => {\n\t\t\t\t// TODO: We should handle features changing that might make the list of wallets\n\t\t\t\t// or even the current wallet incompatible with the dApp.\n\t\t\t\tif (accounts) {\n\t\t\t\t\tupdateWalletAccounts(accounts);\n\t\t\t\t}\n\t\t\t},\n\t\t);\n\t\treturn unsubscribeFromEvents;\n\t}, [currentWallet?.features, updateWalletAccounts]);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { WalletWithRequiredFeatures } from '@mysten/wallet-standard';\nimport { getWallets } from '@mysten/wallet-standard';\nimport { useEffect } from 'react';\n\nimport { getRegisteredWallets } from '../../utils/walletUtils.js';\nimport { useWalletStore } from './useWalletStore.js';\n\n/**\n * Internal hook for easily handling the addition and removal of new wallets.\n */\nexport function useWalletsChanged(\n\tpreferredWallets: string[],\n\twalletFilter?: (wallet: WalletWithRequiredFeatures) => boolean,\n) {\n\tconst setWalletRegistered = useWalletStore((state) => state.setWalletRegistered);\n\tconst setWalletUnregistered = useWalletStore((state) => state.setWalletUnregistered);\n\n\tuseEffect(() => {\n\t\tconst walletsApi = getWallets();\n\t\tsetWalletRegistered(getRegisteredWallets(preferredWallets, walletFilter));\n\n\t\tconst unsubscribeFromRegister = walletsApi.on('register', () => {\n\t\t\tsetWalletRegistered(getRegisteredWallets(preferredWallets, walletFilter));\n\t\t});\n\n\t\tconst unsubscribeFromUnregister = walletsApi.on('unregister', (unregisteredWallet) => {\n\t\t\tsetWalletUnregistered(\n\t\t\t\tgetRegisteredWallets(preferredWallets, walletFilter),\n\t\t\t\tunregisteredWallet,\n\t\t\t);\n\t\t});\n\n\t\treturn () => {\n\t\t\tunsubscribeFromRegister();\n\t\t\tunsubscribeFromUnregister();\n\t\t};\n\t}, [preferredWallets, walletFilter, setWalletRegistered, setWalletUnregistered]);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { ThemeVars } from './themeContract.js';\n\nexport const lightTheme: ThemeVars = {\n\tblurs: {\n\t\tmodalOverlay: 'blur(0)',\n\t},\n\tbackgroundColors: {\n\t\tprimaryButton: '#F6F7F9',\n\t\tprimaryButtonHover: '#F0F2F5',\n\t\toutlineButtonHover: '#F4F4F5',\n\t\tmodalOverlay: 'rgba(24 36 53 / 20%)',\n\t\tmodalPrimary: 'white',\n\t\tmodalSecondary: '#F7F8F8',\n\t\ticonButton: 'transparent',\n\t\ticonButtonHover: '#F0F1F2',\n\t\tdropdownMenu: '#FFFFFF',\n\t\tdropdownMenuSeparator: '#F3F6F8',\n\t\twalletItemSelected: 'white',\n\t\twalletItemHover: '#3C424226',\n\t},\n\tborderColors: {\n\t\toutlineButton: '#E4E4E7',\n\t},\n\tcolors: {\n\t\tprimaryButton: '#373737',\n\t\toutlineButton: '#373737',\n\t\ticonButton: '#000000',\n\t\tbody: '#182435',\n\t\tbodyMuted: '#767A81',\n\t\tbodyDanger: '#FF794B',\n\t},\n\tradii: {\n\t\tsmall: '6px',\n\t\tmedium: '8px',\n\t\tlarge: '12px',\n\t\txlarge: '16px',\n\t},\n\tshadows: {\n\t\tprimaryButton: '0px 4px 12px rgba(0, 0, 0, 0.1)',\n\t\twalletItemSelected: '0px 2px 6px rgba(0, 0, 0, 0.05)',\n\t},\n\tfontWeights: {\n\t\tnormal: '400',\n\t\tmedium: '500',\n\t\tbold: '600',\n\t},\n\tfontSizes: {\n\t\tsmall: '14px',\n\t\tmedium: '16px',\n\t\tlarge: '18px',\n\t\txlarge: '20px',\n\t},\n\ttypography: {\n\t\tfontFamily:\n\t\t\t'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, \"Noto Sans\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"',\n\t\tfontStyle: 'normal',\n\t\tlineHeight: '1.3',\n\t\tletterSpacing: '1',\n\t},\n};\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { Wallet, WalletAccount, WalletWithRequiredFeatures } from '@mysten/wallet-standard';\nimport { createStore } from 'zustand';\nimport type { StateStorage } from 'zustand/middleware';\nimport { createJSONStorage, persist } from 'zustand/middleware';\n\nimport { getWalletUniqueIdentifier } from './utils/walletUtils.js';\n\ntype WalletConnectionStatus = 'disconnected' | 'connecting' | 'connected';\n\nexport type WalletActions = {\n\tsetAccountSwitched: (selectedAccount: WalletAccount) => void;\n\tsetConnectionStatus: (connectionStatus: WalletConnectionStatus) => void;\n\tsetWalletConnected: (\n\t\twallet: WalletWithRequiredFeatures,\n\t\tconnectedAccounts: readonly WalletAccount[],\n\t\tselectedAccount: WalletAccount | null,\n\t\tsupportedIntents?: string[],\n\t) => void;\n\tupdateWalletAccounts: (accounts: readonly WalletAccount[]) => void;\n\tsetWalletDisconnected: () => void;\n\tsetWalletRegistered: (updatedWallets: WalletWithRequiredFeatures[]) => void;\n\tsetWalletUnregistered: (\n\t\tupdatedWallets: WalletWithRequiredFeatures[],\n\t\tunregisteredWallet: Wallet,\n\t) => void;\n};\n\nexport type WalletStore = ReturnType<typeof createWalletStore>;\n\nexport type StoreState = {\n\tautoConnectEnabled: boolean;\n\twallets: WalletWithRequiredFeatures[];\n\taccounts: readonly WalletAccount[];\n\tcurrentWallet: WalletWithRequiredFeatures | null;\n\tcurrentAccount: WalletAccount | null;\n\tlastConnectedAccountAddress: string | null;\n\tlastConnectedWalletName: string | null;\n\tconnectionStatus: WalletConnectionStatus;\n\tsupportedIntents: string[];\n} & WalletActions;\n\ntype WalletConfiguration = {\n\tautoConnectEnabled: boolean;\n\twallets: WalletWithRequiredFeatures[];\n\tstorage: StateStorage;\n\tstorageKey: string;\n};\n\nexport function createWalletStore({\n\twallets,\n\tstorage,\n\tstorageKey,\n\tautoConnectEnabled,\n}: WalletConfiguration) {\n\treturn createStore<StoreState>()(\n\t\tpersist(\n\t\t\t(set, get) => ({\n\t\t\t\tautoConnectEnabled,\n\t\t\t\twallets,\n\t\t\t\taccounts: [] as WalletAccount[],\n\t\t\t\tcurrentWallet: null,\n\t\t\t\tcurrentAccount: null,\n\t\t\t\tlastConnectedAccountAddress: null,\n\t\t\t\tlastConnectedWalletName: null,\n\t\t\t\tconnectionStatus: 'disconnected',\n\t\t\t\tsupportedIntents: [],\n\t\t\t\tsetConnectionStatus(connectionStatus) {\n\t\t\t\t\tset(() => ({\n\t\t\t\t\t\tconnectionStatus,\n\t\t\t\t\t}));\n\t\t\t\t},\n\t\t\t\tsetWalletConnected(wallet, connectedAccounts, selectedAccount, supportedIntents = []) {\n\t\t\t\t\tset(() => ({\n\t\t\t\t\t\taccounts: connectedAccounts,\n\t\t\t\t\t\tcurrentWallet: wallet,\n\t\t\t\t\t\tcurrentAccount: selectedAccount,\n\t\t\t\t\t\tlastConnectedWalletName: getWalletUniqueIdentifier(wallet),\n\t\t\t\t\t\tlastConnectedAccountAddress: selectedAccount?.address,\n\t\t\t\t\t\tconnectionStatus: 'connected',\n\t\t\t\t\t\tsupportedIntents,\n\t\t\t\t\t}));\n\t\t\t\t},\n\t\t\t\tsetWalletDisconnected() {\n\t\t\t\t\tset(() => ({\n\t\t\t\t\t\taccounts: [],\n\t\t\t\t\t\tcurrentWallet: null,\n\t\t\t\t\t\tcurrentAccount: null,\n\t\t\t\t\t\tlastConnectedWalletName: null,\n\t\t\t\t\t\tlastConnectedAccountAddress: null,\n\t\t\t\t\t\tconnectionStatus: 'disconnected',\n\t\t\t\t\t\tsupportedIntents: [],\n\t\t\t\t\t}));\n\t\t\t\t},\n\t\t\t\tsetAccountSwitched(selectedAccount) {\n\t\t\t\t\tset(() => ({\n\t\t\t\t\t\tcurrentAccount: selectedAccount,\n\t\t\t\t\t\tlastConnectedAccountAddress: selectedAccount.address,\n\t\t\t\t\t}));\n\t\t\t\t},\n\t\t\t\tsetWalletRegistered(updatedWallets) {\n\t\t\t\t\tset(() => ({ wallets: updatedWallets }));\n\t\t\t\t},\n\t\t\t\tsetWalletUnregistered(updatedWallets, unregisteredWallet) {\n\t\t\t\t\tif (unregisteredWallet === get().currentWallet) {\n\t\t\t\t\t\tset(() => ({\n\t\t\t\t\t\t\twallets: updatedWallets,\n\t\t\t\t\t\t\taccounts: [],\n\t\t\t\t\t\t\tcurrentWallet: null,\n\t\t\t\t\t\t\tcurrentAccount: null,\n\t\t\t\t\t\t\tlastConnectedWalletName: null,\n\t\t\t\t\t\t\tlastConnectedAccountAddress: null,\n\t\t\t\t\t\t\tconnectionStatus: 'disconnected',\n\t\t\t\t\t\t\tsupportedIntents: [],\n\t\t\t\t\t\t}));\n\t\t\t\t\t} else {\n\t\t\t\t\t\tset(() => ({ wallets: updatedWallets }));\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tupdateWalletAccounts(accounts) {\n\t\t\t\t\tconst currentAccount = get().currentAccount;\n\n\t\t\t\t\tset(() => ({\n\t\t\t\t\t\taccounts,\n\t\t\t\t\t\tcurrentAccount:\n\t\t\t\t\t\t\t(currentAccount &&\n\t\t\t\t\t\t\t\taccounts.find(({ address }) => address === currentAccount.address)) ||\n\t\t\t\t\t\t\taccounts[0],\n\t\t\t\t\t}));\n\t\t\t\t},\n\t\t\t}),\n\t\t\t{\n\t\t\t\tname: storageKey,\n\t\t\t\tstorage: createJSONStorage(() => storage),\n\t\t\t\tpartialize: ({ lastConnectedWalletName, lastConnectedAccountAddress }) => ({\n\t\t\t\t\tlastConnectedWalletName,\n\t\t\t\t\tlastConnectedAccountAddress,\n\t\t\t\t}),\n\t\t\t},\n\t\t),\n\t);\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { assignInlineVars } from '@vanilla-extract/dynamic';\n\nimport { styleDataAttributeSelector } from '../../constants/styleDataAttribute.js';\nimport { themeVars } from '../../themes/themeContract.js';\nimport type { DynamicTheme, Theme, ThemeVars } from '../../themes/themeContract.js';\n\ntype InjectedThemeStylesProps = {\n\ttheme: Theme;\n};\n\nexport function InjectedThemeStyles({ theme }: InjectedThemeStylesProps) {\n\tconst themeStyles = Array.isArray(theme)\n\t\t? getDynamicThemeStyles(theme)\n\t\t: getStaticThemeStyles(theme);\n\n\treturn (\n\t\t<style\n\t\t\t// @ts-expect-error The precedence prop hasn't made it to the stable release of React, but we\n\t\t\t// don't want this to break in frameworks like Next which use the latest canary build.\n\t\t\tprecedence=\"default\"\n\t\t\thref=\"mysten-dapp-kit-theme\"\n\t\t\tdangerouslySetInnerHTML={{\n\t\t\t\t__html: themeStyles,\n\t\t\t}}\n\t\t/>\n\t);\n}\n\nfunction getDynamicThemeStyles(themes: DynamicTheme[]) {\n\treturn themes\n\t\t.map(({ mediaQuery, selector, variables }) => {\n\t\t\tconst themeStyles = getStaticThemeStyles(variables);\n\t\t\tconst themeStylesWithSelectorPrefix = selector ? `${selector} ${themeStyles}` : themeStyles;\n\n\t\t\treturn mediaQuery\n\t\t\t\t? `@media ${mediaQuery}{${themeStylesWithSelectorPrefix}}`\n\t\t\t\t: themeStylesWithSelectorPrefix;\n\t\t})\n\t\t.join(' ');\n}\n\nfunction getStaticThemeStyles(theme: ThemeVars) {\n\treturn `${styleDataAttributeSelector} {${cssStringFromTheme(theme)}}`;\n}\n\nfunction cssStringFromTheme(theme: ThemeVars) {\n\treturn Object.entries(assignInlineVars(themeVars, theme))\n\t\t.map(([key, value]) => `${key}:${value};`)\n\t\t.join('');\n}\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nexport const styleDataAttributeName = 'data-dapp-kit';\n\nexport const styleDataAttributeSelector = `[${styleDataAttributeName}]`;\n\nexport const styleDataAttribute = { [styleDataAttributeName]: '' };\n", "// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { createGlobalThemeContract } from '@vanilla-extract/css';\n\nconst themeContractValues = {\n\tblurs: {\n\t\tmodalOverlay: '',\n\t},\n\tbackgroundColors: {\n\t\tprimaryButton: '',\n\t\tprimaryButtonHover: '',\n\t\toutlineButtonHover: '',\n\t\twalletItemHover: '',\n\t\twalletItemSelected: '',\n\t\tmodalOverlay: '',\n\t\tmodalPrimary: '',\n\t\tmodalSecondary: '',\n\t\ticonButton: '',\n\t\ticonButtonHover: '',\n\t\tdropdownMenu: '',\n\t\tdropdownMenuSeparator: '',\n\t},\n\tborderColors: {\n\t\toutlineButton: '',\n\t},\n\tcolors: {\n\t\tprimaryButton: '',\n\t\toutlineButton: '',\n\t\tbody: '',\n\t\tbodyMuted: '',\n\t\tbodyDanger: '',\n\t\ticonButton: '',\n\t},\n\tradii: {\n\t\tsmall: '',\n\t\tmedium: '',\n\t\tlarge: '',\n\t\txlarge: '',\n\t},\n\tshadows: {\n\t\tprimaryButton: '',\n\t\twalletItemSelected: '',\n\t},\n\tfontWeights: {\n\t\tnormal: '',\n\t\tmedium: '',\n\t\tbold: '',\n\t},\n\tfontSizes: {\n\t\tsmall: '',\n\t\tmedium: '',\n\t\tlarge: '',\n\t\txlarge: '',\n\t},\n\ttypography: {\n\t\tfontFamily: '',\n\t\tfontStyle: '',\n\t\tlineHeight: '',\n\t\tletterSpacing: '',\n\t},\n};\n\nexport type ThemeVars = typeof themeContractValues;\n\n/**\n * A custom theme that is enabled when various conditions are\n */\nexport type DynamicTheme = {\n\t/**\n\t * An optional media query required for the given theme to be enabled. This is useful\n\t * when you want the theme of your application to automatically switch depending on\n\t * a media feature.\n\t *\n\t * @example '(prefers-color-scheme: dark)'\n\t */\n\tmediaQuery?: string;\n\n\t/**\n\t * An optional CSS selector required for the given theme to be enabled. This is useful\n\t * when you have a manual theme switcher on your application that sets a top-level\n\t * class name or data-attribute to control the current theme.\n\t *\n\t * @example '.data-dark'\n\t */\n\tselector?: string;\n\n\t/** The theme definitions that will be set when the selector and mediaQuery criteria are matched. */\n\tvariables: ThemeVars;\n};\n\nexport type Theme = ThemeVars | DynamicTheme[];\n\nexport const themeVars = createGlobalThemeContract(\n\tthemeContractValues,\n\t(_, path) => `dapp-kit-${path.join('-')}`,\n);\n"], "mappings": ";;;;;;;;AAKA,SAAS,cAAc;;;ACDvB,SAAS,yBAAyB;;;ACC3B,SAAS,sBAAoC;AACnD,QAAM,QAAQ,oBAAI,IAAI;AACtB,SAAO;AAAA,IACN,QAAQ,KAAa;AACpB,aAAO,MAAM,IAAI,GAAG;AAAA,IACrB;AAAA,IACA,QAAQ,KAAa,OAAe;AACnC,YAAM,IAAI,KAAK,KAAK;AAAA,IACrB;AAAA,IACA,WAAW,KAAa;AACvB,YAAM,OAAO,GAAG;AAAA,IACjB;AAAA,EACD;AACD;;;ADVO,IAAM,kBAAkB;AAExB,IAAM,kBACZ,OAAO,WAAW,eAAe,OAAO,eAAe,eAAe,oBAAoB;AAEpF,IAAM,sBAAsB;AAEnC,IAAM,gBAAgB;AAAA,EACrB;AAAA,EACA;AACD;AAEO,IAAM,wBAAwB,CAAC,WACrC,cAAc,KAAK,CAAC,YAAY,OAAO,SAAS,OAAO,CAAC;AAElD,IAAM,4BAA4B,CAAC,iBAAiB,iBAAiB;;;AEpB5E,SAAS,qBAAqB;AAIvB,IAAM,gBAAgB,cAAkC,IAAI;;;ACJnE,SAAS,gBAAgB;AACzB,SAAS,iBAAiB,gBAAgB;;;ACK1C,SAAS,YAAY,sCAAsC;AAEpD,SAAS,qBACf,kBACA,cACC;AACD,QAAM,aAAa,WAAW;AAC9B,QAAM,UAAU,WAAW,IAAI;AAE/B,QAAM,aAAa,QAAQ;AAAA,IAC1B,CAAC,WACA,+BAA+B,MAAM,MAAM,CAAC,gBAAgB,aAAa,MAAM;AAAA,EACjF;AAEA,SAAO;AAAA;AAAA,IAEN,GAAI,iBACF,IAAI,CAAC,SAAS,WAAW,KAAK,CAAC,WAAW,OAAO,SAAS,IAAI,CAAC,EAC/D,OAAO,OAAO;AAAA;AAAA,IAGhB,GAAG,WAAW,OAAO,CAAC,WAAW,CAAC,iBAAiB,SAAS,OAAO,IAAI,CAAC;AAAA,EACzE;AACD;AAEO,SAAS,0BAA0B,QAAiB;AAC1D,SAAO,QAAQ,MAAM,QAAQ;AAC9B;;;AC1BA,SAAS,mBAAmB;;;ACLrB,IAAM,qBAAqB;AAAA,EACjC,KAAK,EAAE,WAAW,SAAS;AAAA,EAC3B,eAAe,kBAAkB,gBAAgB;AAAA,EACjD,mBAAmB,kBAAkB,oBAAoB;AAAA,EACzD,kBAAkB,kBAAkB,mBAAmB;AAAA,EACvD,qBAAqB,kBAAkB,uBAAuB;AAAA,EAC9D,iBAAiB,kBAAkB,kBAAkB;AAAA,EACrD,2BAA2B,kBAAkB,8BAA8B;AAAA,EAC3E,eAAe,kBAAkB,gBAAgB;AAAA,EACjD,0BAA0B,kBAAkB,4BAA4B;AACzE;AAEA,SAAS,kBAAkB,YAAoB;AAC9C,SAAO,SAAS,cAAc,iBAA8B,CAAC,GAAG;AAC/D,WAAO,CAAC,EAAE,GAAG,mBAAmB,KAAK,WAAW,GAAG,GAAG,cAAc;AAAA,EACrE;AACD;;;AClBA,SAAS,kBAAkB;AAC3B,SAAS,gBAAgB;AAKlB,SAAS,eAAkB,UAAuC;AACxE,QAAM,QAAQ,WAAW,aAAa;AACtC,MAAI,CAAC,OAAO;AACX,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AACA,SAAO,SAAS,OAAO,QAAQ;AAChC;;;AFgBO,SAAS,iBAAiB;AAAA,EAChC;AAAA,EACA,GAAG;AACJ,IAAqC,CAAC,GAKpC;AACD,QAAM,qBAAqB,eAAe,CAAC,UAAU,MAAM,kBAAkB;AAC7E,QAAM,sBAAsB,eAAe,CAAC,UAAU,MAAM,mBAAmB;AAE/E,SAAO,YAAY;AAAA,IAClB,aAAa,mBAAmB,cAAc,WAAW;AAAA,IACzD,YAAY,OAAO,EAAE,QAAQ,gBAAgB,GAAG,YAAY,MAAM;AACjE,UAAI;AACH,4BAAoB,YAAY;AAEhC,cAAM,gBAAgB,MAAM,OAAO,SAAS,kBAAkB,EAAE,QAAQ,WAAW;AACnF,cAAM,uBAAuB,cAAc,SAAS;AAAA,UAAO,CAAC,YAC3D,QAAQ,OAAO,KAAK,CAAC,UAAU,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,KAAK;AAAA,QAC7D;AACA,cAAM,kBAAkB,mBAAmB,sBAAsB,cAAc;AAE/E;AAAA,UACC;AAAA,UACA;AAAA,UACA;AAAA,UACA,cAAc;AAAA,QACf;AAEA,eAAO,EAAE,UAAU,qBAAqB;AAAA,MACzC,SAAS,OAAO;AACf,4BAAoB,cAAc;AAClC,cAAM;AAAA,MACP;AAAA,IACD;AAAA,IACA,GAAG;AAAA,EACJ,CAAC;AACF;AAEA,SAAS,mBAAmB,mBAA6C,gBAAyB;AACjG,MAAI,kBAAkB,WAAW,GAAG;AACnC,WAAO;AAAA,EACR;AAEA,MAAI,gBAAgB;AACnB,UAAM,kBAAkB,kBAAkB,KAAK,CAAC,YAAY,QAAQ,YAAY,cAAc;AAC9F,WAAO,mBAAmB,kBAAkB,CAAC;AAAA,EAC9C;AAEA,SAAO,kBAAkB,CAAC;AAC3B;;;AG7EO,SAAS,mBAAmB;AAClC,QAAM,gBAAgB,eAAe,CAAC,UAAU,MAAM,aAAa;AACnE,QAAM,mBAAmB,eAAe,CAAC,UAAU,MAAM,gBAAgB;AACzE,QAAM,mBAAmB,eAAe,CAAC,UAAU,MAAM,gBAAgB;AAEzE,UAAQ,kBAAkB;AAAA,IACzB,KAAK;AACJ,aAAO;AAAA,QACN;AAAA,QACA,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,kBAAkB,CAAC;AAAA,MACpB;AAAA,IACD,KAAK;AACJ,aAAO;AAAA,QACN;AAAA,QACA,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,aAAa;AAAA,QACb,kBAAkB,CAAC;AAAA,MACpB;AAAA,IACD,KAAK,aAAa;AACjB,aAAO;AAAA,QACN;AAAA,QACA;AAAA,QACA,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,aAAa;AAAA,QACb;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;;;ACnCO,SAAS,aAAa;AAC5B,SAAO,eAAe,CAAC,UAAU,MAAM,OAAO;AAC/C;;;ANEO,SAAS,uBAA0D;AACzE,QAAM,EAAE,aAAa,cAAc,IAAI,iBAAiB;AACxD,QAAM,qBAAqB,eAAe,CAAC,UAAU,MAAM,kBAAkB;AAC7E,QAAM,0BAA0B,eAAe,CAAC,UAAU,MAAM,uBAAuB;AACvF,QAAM,8BAA8B,eAAe,CAAC,UAAU,MAAM,2BAA2B;AAC/F,QAAM,UAAU,WAAW;AAC3B,QAAM,EAAE,YAAY,IAAI,iBAAiB;AAEzC,QAAM,CAAC,YAAY,aAAa,IAAI,SAAS,KAAK;AAClD,kBAAgB,MAAM;AACrB,kBAAc,IAAI;AAAA,EACnB,GAAG,CAAC,CAAC;AAEL,QAAM,EAAE,MAAM,QAAQ,IAAI,SAAS;AAAA,IAClC,UAAU;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,QACC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,aAAa,QAAQ;AAAA,MACtB;AAAA,IACD;AAAA,IACA,SAAS,YAAY;AACpB,UAAI,CAAC,oBAAoB;AACxB,eAAO;AAAA,MACR;AAEA,UAAI,CAAC,2BAA2B,CAAC,+BAA+B,aAAa;AAC5E,eAAO;AAAA,MACR;AAEA,YAAM,SAAS,QAAQ;AAAA,QACtB,CAACA,YAAW,0BAA0BA,OAAM,MAAM;AAAA,MACnD;AACA,UAAI,QAAQ;AACX,cAAM,cAAc;AAAA,UACnB;AAAA,UACA,gBAAgB;AAAA,UAChB,QAAQ;AAAA,QACT,CAAC;AAAA,MACF;AAEA,aAAO;AAAA,IACR;AAAA,IACA,SAAS;AAAA,IACT,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,IACb,OAAO;AAAA,IACP,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,6BAA6B;AAAA,IAC7B,gBAAgB;AAAA,IAChB,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,EACvB,CAAC;AAED,MAAI,CAAC,oBAAoB;AACxB,WAAO;AAAA,EACR;AAGA,MAAI,CAAC,YAAY;AAChB,WAAO;AAAA,EACR;AAEA,MAAI,aAAa;AAChB,WAAO;AAAA,EACR;AAEA,MAAI,CAAC,yBAAyB;AAC7B,WAAO;AAAA,EACR;AAEA,SAAO,UAAU,cAAe,QAAQ;AACzC;;;AOxFA,SAAS,2BAA2B;AACpC,SAAS,mBAAAC,wBAAuB;AAOzB,SAAS,eAAe,QAA4B;AAC1D,EAAAA,iBAAgB,MAAM;AACrB,QAAI,CAAC,QAAQ,MAAM;AAClB;AAAA,IACD;AAEA,QAAI;AACJ,QAAI,YAAY;AAEhB,QAAI;AACH,YAAM,SAAS,oBAAoB,OAAO,MAAM;AAAA,QAC/C,QAAQ,OAAO;AAAA,MAChB,CAAC;AAED,UAAI,aAAa,QAAQ;AACxB,kBAAU,OAAO;AAAA,MAClB,WAAW,QAAQ;AAClB,eAAO,WAAW;AAAA,MACnB;AAAA,IACD,SAAS,OAAO;AACf,cAAQ,MAAM,oCAAoC,KAAK;AAAA,IACxD;AAEA,WAAO,MAAM;AACZ,kBAAY;AACZ,UAAI,QAAS,SAAQ;AAAA,IACtB;AAAA,EACD,GAAG,CAAC,QAAQ,MAAM,QAAQ,MAAM,CAAC;AAClC;;;ACnCA,SAAS,sBAAsB;AAC/B,SAAS,mBAAmB;AAC5B,SAAS,gBAAgB;AAczB,SAAS,cAAAC,aAAY,uBAAuB,kBAAkB;AAC9D,SAAS,iBAAiB;;;ACjB1B,SAAS,cAAAC,mBAAkB;;;ACD3B,SAAS,gBAAgB,aAAa,iBAAiB;AAEvD,SAAS,iBAAAC,gBAAe,SAAS,YAAAC,iBAAgB;AAyFzC;AAxED,IAAM,mBAAmBD,eAA+C,IAAI;AAkBnF,IAAM,mBAAmB;AAAA,EACxB,UAAU,EAAE,KAAK,eAAe,UAAU,EAAE;AAC7C;;;ADlCO,SAAS,sBAAsB;AACrC,QAAM,YAAYE,YAAW,gBAAgB;AAE7C,MAAI,CAAC,WAAW;AACf,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AACR;AAEO,SAAS,eAA0B;AACzC,SAAO,oBAAoB,EAAE;AAC9B;;;ADGA,IAAM,cAAc;AAEb,SAAS,sBAAsB,SAAkB;AACvD,QAAM,YAAY,aAAa;AAE/B,YAAU,MAAM;AACf,QAAI,CAAC,SAAS;AACb;AAAA,IACD;AACA,UAAM,aAAa,2BAA2B,SAAS;AACvD,WAAO;AAAA,EACR,GAAG,CAAC,SAAS,SAAS,CAAC;AACxB;AAEA,SAAS,2BAA2B,WAAsB;AAvC1D;AAwCC,QAAM,aAAaC,YAAW;AAC9B,QAAM,oBAAoB,WAAW,IAAI;AAEzC,MAAI,kBAAkB,KAAK,CAAC,WAAW,OAAO,SAAS,WAAW,GAAG;AACpE,YAAQ;AAAA,MACP;AAAA,IACD;AACA;AAAA,EACD;AAEA,UAAQ;AAAA,IACP;AAAA,EACD;AAEA,QAAM,UAAU,IAAI,eAAe;AACnC,QAAM,UAAU,IAAI,sBAAsB;AAAA,IACzC,SAAS,QAAQ,aAAa,EAAE,aAAa;AAAA,IAC7C,WAAW,QAAQ,aAAa,EAAE,WAAW;AAAA,IAC7C,QAAQ,CAAC,aAAa;AAAA,IACtB,UAAU;AAAA,MACT;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAAA,EACD,CAAC;AAAA,EAED,MAAM,mBAAqC;AAAA,IAA3C;AAuDC,8BAA8B,MAAM;AACnC,eAAO,MAAM;AAAA,QAAC;AAAA,MACf;AAEA,mCAAkC,YAAY;AAC7C,eAAO,EAAE,UAAU,KAAK,SAAS;AAAA,MAClC;AAEA,+CAAqD,OAAO,iBAAiB;AAC5E,cAAM,EAAE,OAAO,UAAU,IAAI,MAAM,QAAQ,oBAAoB,aAAa,OAAO;AACnF,eAAO,EAAE,OAAO,UAAU;AAAA,MAC3B;AAEA,gDAAuD,OAAO,qBAAqB;AAClF,cAAM,EAAE,OAAO,UAAU,IAAI,MAAM,iBAAiB,iBAAiB,KAAK;AAAA,UACzE,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT,CAAC;AAED,eAAO;AAAA,UACN,uBAAuB;AAAA,UACvB;AAAA,QACD;AAAA,MACD;AAEA,2CAA6C,OAAO,qBAAqB;AACxE,cAAM,EAAE,OAAO,UAAU,IAAI,MAAM,YAAY;AAAA,UAC9C,MAAM,iBAAiB,YAAY,OAAO;AAAA,QAC3C,EAAE,KAAK;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT,CAAC;AAED,yBAAiB,QAAQ,eAAe;AAExC,eAAO;AAAA,UACN;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAEA,0DAA2E,OAC1E,qBACI;AACJ,cAAM,EAAE,OAAO,UAAU,IAAI,MAAM,iBAAiB,iBAAiB,KAAK;AAAA,UACzE,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT,CAAC;AAED,eAAO,UAAU,wBAAwB;AAAA,UACxC;AAAA,UACA,kBAAkB;AAAA,UAClB,SAAS,iBAAiB;AAAA,QAC3B,CAAC;AAAA,MACF;AAEA,qDAAiE,OAAO,qBAAqB;AAC5F,cAAM,EAAE,OAAO,UAAU,IAAI,MAAM,YAAY;AAAA,UAC9C,MAAM,iBAAiB,YAAY,OAAO;AAAA,QAC3C,EAAE,KAAK;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,QACT,CAAC;AAED,yBAAiB,QAAQ,eAAe;AAExC,cAAM,EAAE,YAAY,OAAO,IAAI,MAAM,UAAU,wBAAwB;AAAA,UACtE;AAAA,UACA,kBAAkB;AAAA,UAClB,SAAS;AAAA,YACR,gBAAgB;AAAA,UACjB;AAAA,QACD,CAAC;AAED,eAAO;AAAA,UACN;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAS,SAAS,IAAI,WAAW,UAAW,CAAC;AAAA,QAC9C;AAAA,MACD;AAAA;AAAA,IAtIA,IAAI,UAAU;AACb,aAAO;AAAA,IACR;AAAA,IAEA,IAAI,OAAO;AACV,aAAO;AAAA,IACR;AAAA,IAEA,IAAI,OAAO;AACV,aAAO;AAAA,IACR;AAAA;AAAA,IAGA,IAAI,SAAS;AACZ,aAAO;AAAA,IACR;AAAA,IAEA,IAAI,WAAW;AACd,aAAO,CAAC,OAAO;AAAA,IAChB;AAAA,IAEA,IAAI,WAAyE;AAC5E,aAAO;AAAA,QACN,oBAAoB;AAAA,UACnB,SAAS;AAAA,UACT,SAAS,mBAAK;AAAA,QACf;AAAA,QACA,mBAAmB;AAAA,UAClB,SAAS;AAAA,UACT,IAAI,mBAAK;AAAA,QACV;AAAA,QACA,2BAA2B;AAAA,UAC1B,SAAS;AAAA,UACT,qBAAqB,mBAAK;AAAA,QAC3B;AAAA,QACA,4BAA4B;AAAA,UAC3B,SAAS;AAAA,UACT,sBAAsB,mBAAK;AAAA,QAC5B;AAAA,QACA,sCAAsC;AAAA,UACrC,SAAS;AAAA,UACT,gCAAgC,mBAAK;AAAA,QACtC;AAAA,QACA,uBAAuB;AAAA,UACtB,SAAS;AAAA,UACT,iBAAiB,mBAAK;AAAA,QACvB;AAAA,QACA,iCAAiC;AAAA,UAChC,SAAS;AAAA,UACT,2BAA2B,mBAAK;AAAA,QACjC;AAAA,MACD;AAAA,IACD;AAAA,EAmFD;AAjFC;AAIA;AAIA;AAKA;AAYA;AAgBA;AAeA;AA2BD,SAAO,WAAW,SAAS,IAAI,mBAAmB,CAAC;AACpD;;;AG3MA,SAAS,aAAAC,kBAAiB;AAQnB,SAAS,6BAA6B;AAC5C,QAAM,EAAE,cAAc,IAAI,iBAAiB;AAC3C,QAAM,uBAAuB,eAAe,CAAC,UAAU,MAAM,oBAAoB;AAEjF,EAAAC,WAAU,MAAM;AACf,UAAM,wBAAwB,eAAe,SAAS,iBAAiB,EAAE;AAAA,MACxE;AAAA,MACA,CAAC,EAAE,SAAS,MAAM;AAGjB,YAAI,UAAU;AACb,+BAAqB,QAAQ;AAAA,QAC9B;AAAA,MACD;AAAA,IACD;AACA,WAAO;AAAA,EACR,GAAG,CAAC,eAAe,UAAU,oBAAoB,CAAC;AACnD;;;ACxBA,SAAS,cAAAC,mBAAkB;AAC3B,SAAS,aAAAC,kBAAiB;AAQnB,SAAS,kBACf,kBACA,cACC;AACD,QAAM,sBAAsB,eAAe,CAAC,UAAU,MAAM,mBAAmB;AAC/E,QAAM,wBAAwB,eAAe,CAAC,UAAU,MAAM,qBAAqB;AAEnF,EAAAC,WAAU,MAAM;AACf,UAAM,aAAaC,YAAW;AAC9B,wBAAoB,qBAAqB,kBAAkB,YAAY,CAAC;AAExE,UAAM,0BAA0B,WAAW,GAAG,YAAY,MAAM;AAC/D,0BAAoB,qBAAqB,kBAAkB,YAAY,CAAC;AAAA,IACzE,CAAC;AAED,UAAM,4BAA4B,WAAW,GAAG,cAAc,CAAC,uBAAuB;AACrF;AAAA,QACC,qBAAqB,kBAAkB,YAAY;AAAA,QACnD;AAAA,MACD;AAAA,IACD,CAAC;AAED,WAAO,MAAM;AACZ,8BAAwB;AACxB,gCAA0B;AAAA,IAC3B;AAAA,EACD,GAAG,CAAC,kBAAkB,cAAc,qBAAqB,qBAAqB,CAAC;AAChF;;;ACnCO,IAAM,aAAwB;AAAA,EACpC,OAAO;AAAA,IACN,cAAc;AAAA,EACf;AAAA,EACA,kBAAkB;AAAA,IACjB,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,cAAc;AAAA,IACd,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,uBAAuB;AAAA,IACvB,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,EAClB;AAAA,EACA,cAAc;AAAA,IACb,eAAe;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACP,eAAe;AAAA,IACf,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,MAAM;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACR,eAAe;AAAA,IACf,oBAAoB;AAAA,EACrB;AAAA,EACA,aAAa;AAAA,IACZ,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,EACP;AAAA,EACA,WAAW;AAAA,IACV,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACX,YACC;AAAA,IACD,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,EAChB;AACD;;;AC1DA,SAAS,mBAAmB;AAE5B,SAAS,mBAAmB,eAAe;AA6CpC,SAAS,kBAAkB;AAAA,EACjC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,GAAwB;AACvB,SAAO,YAAwB;AAAA,IAC9B;AAAA,MACC,CAAC,KAAK,SAAS;AAAA,QACd;AAAA,QACA;AAAA,QACA,UAAU,CAAC;AAAA,QACX,eAAe;AAAA,QACf,gBAAgB;AAAA,QAChB,6BAA6B;AAAA,QAC7B,yBAAyB;AAAA,QACzB,kBAAkB;AAAA,QAClB,kBAAkB,CAAC;AAAA,QACnB,oBAAoB,kBAAkB;AACrC,cAAI,OAAO;AAAA,YACV;AAAA,UACD,EAAE;AAAA,QACH;AAAA,QACA,mBAAmB,QAAQ,mBAAmB,iBAAiB,mBAAmB,CAAC,GAAG;AACrF,cAAI,OAAO;AAAA,YACV,UAAU;AAAA,YACV,eAAe;AAAA,YACf,gBAAgB;AAAA,YAChB,yBAAyB,0BAA0B,MAAM;AAAA,YACzD,6BAA6B,iBAAiB;AAAA,YAC9C,kBAAkB;AAAA,YAClB;AAAA,UACD,EAAE;AAAA,QACH;AAAA,QACA,wBAAwB;AACvB,cAAI,OAAO;AAAA,YACV,UAAU,CAAC;AAAA,YACX,eAAe;AAAA,YACf,gBAAgB;AAAA,YAChB,yBAAyB;AAAA,YACzB,6BAA6B;AAAA,YAC7B,kBAAkB;AAAA,YAClB,kBAAkB,CAAC;AAAA,UACpB,EAAE;AAAA,QACH;AAAA,QACA,mBAAmB,iBAAiB;AACnC,cAAI,OAAO;AAAA,YACV,gBAAgB;AAAA,YAChB,6BAA6B,gBAAgB;AAAA,UAC9C,EAAE;AAAA,QACH;AAAA,QACA,oBAAoB,gBAAgB;AACnC,cAAI,OAAO,EAAE,SAAS,eAAe,EAAE;AAAA,QACxC;AAAA,QACA,sBAAsB,gBAAgB,oBAAoB;AACzD,cAAI,uBAAuB,IAAI,EAAE,eAAe;AAC/C,gBAAI,OAAO;AAAA,cACV,SAAS;AAAA,cACT,UAAU,CAAC;AAAA,cACX,eAAe;AAAA,cACf,gBAAgB;AAAA,cAChB,yBAAyB;AAAA,cACzB,6BAA6B;AAAA,cAC7B,kBAAkB;AAAA,cAClB,kBAAkB,CAAC;AAAA,YACpB,EAAE;AAAA,UACH,OAAO;AACN,gBAAI,OAAO,EAAE,SAAS,eAAe,EAAE;AAAA,UACxC;AAAA,QACD;AAAA,QACA,qBAAqB,UAAU;AAC9B,gBAAM,iBAAiB,IAAI,EAAE;AAE7B,cAAI,OAAO;AAAA,YACV;AAAA,YACA,gBACE,kBACA,SAAS,KAAK,CAAC,EAAE,QAAQ,MAAM,YAAY,eAAe,OAAO,KAClE,SAAS,CAAC;AAAA,UACZ,EAAE;AAAA,QACH;AAAA,MACD;AAAA,MACA;AAAA,QACC,MAAM;AAAA,QACN,SAAS,kBAAkB,MAAM,OAAO;AAAA,QACxC,YAAY,CAAC,EAAE,yBAAyB,4BAA4B,OAAO;AAAA,UAC1E;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;;;AC5IA,SAAS,wBAAwB;;;ACA1B,IAAM,yBAAyB;AAE/B,IAAM,6BAA6B,IAAI,sBAAsB;AAE7D,IAAM,qBAAqB,EAAE,CAAC,sBAAsB,GAAG,GAAG;;;ACJjE,SAAS,iCAAiC;AAE1C,IAAM,sBAAsB;AAAA,EAC3B,OAAO;AAAA,IACN,cAAc;AAAA,EACf;AAAA,EACA,kBAAkB;AAAA,IACjB,eAAe;AAAA,IACf,oBAAoB;AAAA,IACpB,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,cAAc;AAAA,IACd,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,uBAAuB;AAAA,EACxB;AAAA,EACA,cAAc;AAAA,IACb,eAAe;AAAA,EAChB;AAAA,EACA,QAAQ;AAAA,IACP,eAAe;AAAA,IACf,eAAe;AAAA,IACf,MAAM;AAAA,IACN,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,EACb;AAAA,EACA,OAAO;AAAA,IACN,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,EACT;AAAA,EACA,SAAS;AAAA,IACR,eAAe;AAAA,IACf,oBAAoB;AAAA,EACrB;AAAA,EACA,aAAa;AAAA,IACZ,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR,MAAM;AAAA,EACP;AAAA,EACA,WAAW;AAAA,IACV,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACX,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,EAChB;AACD;AAgCO,IAAM,YAAY;AAAA,EACxB;AAAA,EACA,CAAC,GAAG,SAAS,YAAY,KAAK,KAAK,GAAG,CAAC;AACxC;;;AF7EE,gBAAAC,YAAA;AANK,SAAS,oBAAoB,EAAE,MAAM,GAA6B;AACxE,QAAM,cAAc,MAAM,QAAQ,KAAK,IACpC,sBAAsB,KAAK,IAC3B,qBAAqB,KAAK;AAE7B,SACC,gBAAAA;AAAA,IAAC;AAAA;AAAA,MAGA,YAAW;AAAA,MACX,MAAK;AAAA,MACL,yBAAyB;AAAA,QACxB,QAAQ;AAAA,MACT;AAAA;AAAA,EACD;AAEF;AAEA,SAAS,sBAAsB,QAAwB;AACtD,SAAO,OACL,IAAI,CAAC,EAAE,YAAY,UAAU,UAAU,MAAM;AAC7C,UAAM,cAAc,qBAAqB,SAAS;AAClD,UAAM,gCAAgC,WAAW,GAAG,QAAQ,IAAI,WAAW,KAAK;AAEhF,WAAO,aACJ,UAAU,UAAU,IAAI,6BAA6B,MACrD;AAAA,EACJ,CAAC,EACA,KAAK,GAAG;AACX;AAEA,SAAS,qBAAqB,OAAkB;AAC/C,SAAO,GAAG,0BAA0B,KAAK,mBAAmB,KAAK,CAAC;AACnE;AAEA,SAAS,mBAAmB,OAAkB;AAC7C,SAAO,OAAO,QAAQ,iBAAiB,WAAW,KAAK,CAAC,EACtD,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,GAAG,GAAG,IAAI,KAAK,GAAG,EACxC,KAAK,EAAE;AACV;;;AnB4BG,SAOU,OAAAC,MAPV;AAtBI,SAAS,eAAe;AAAA,EAC9B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,UAAU;AAAA,EACV,aAAa;AAAA,EACb,qBAAqB;AAAA,EACrB,cAAc;AAAA,EACd;AAAA,EACA,QAAQ;AAAA,EACR;AACD,GAAwB;AACvB,QAAM,WAAW;AAAA,IAChB,kBAAkB;AAAA,MACjB,oBAAoB;AAAA,MACpB,SAAS,qBAAqB,kBAAkB,YAAY;AAAA,MAC5D,SAAS,WAAW,oBAAoB;AAAA,MACxC;AAAA,IACD,CAAC;AAAA,EACF;AAEA,SACC,gBAAAA,KAAC,cAAc,UAAd,EAAuB,OAAO,SAAS,SACvC;AAAA,IAAC;AAAA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MAGC;AAAA,gBAAQ,gBAAAA,KAAC,uBAAoB,OAAc,IAAK;AAAA,QAChD;AAAA;AAAA;AAAA,EACF,GACD;AAEF;AAOA,SAAS,wBAAwB;AAAA,EAChC,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,qBAAqB;AAAA,EACrB;AAAA,EACA;AACD,GAAiC;AAChC,oBAAkB,kBAAkB,YAAY;AAChD,6BAA2B;AAC3B,iBAAe,WAAW;AAC1B,wBAAsB,kBAAkB;AACxC,uBAAqB;AAErB,SAAO;AACR;", "names": ["wallet", "useLayoutEffect", "getWallets", "useContext", "createContext", "useState", "useContext", "getWallets", "useEffect", "useEffect", "getWallets", "useEffect", "useEffect", "getWallets", "jsx", "jsx"]}