{"name": "@mysten/dapp-kit", "author": "Mysten Labs <<EMAIL>>", "description": "A collection of React hooks and components for interacting with the Sui blockchain and wallets.", "homepage": "https://sdk.mystenlabs.com/typescript", "version": "0.16.15", "license": "Apache-2.0", "files": ["CHANGELOG.md", "LICENSE", "README.md", "dist", "src"], "type": "commonjs", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/cjs/index.d.ts", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}, "./dist/index.css": {"import": "./dist/esm/index.css", "require": "./dist/cjs/index.css"}}, "bugs": {"url": "https://github.com/MystenLabs/ts-sdks/issues/new"}, "publishConfig": {"access": "public"}, "size-limit": [{"path": "dist/esm/index.js", "limit": "100 KB"}, {"path": "dist/cjs/index.js", "limit": "100 KB"}], "devDependencies": {"@size-limit/preset-small-lib": "^11.2.0", "@tanstack/react-query": "^5.80.2", "@testing-library/dom": "^10.3.1", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.3", "@vanilla-extract/esbuild-plugin": "^2.3.16", "@vanilla-extract/vite-plugin": "^5.0.3", "happy-dom": "^17.6.1", "jsdom": "^26.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "size-limit": "^11.2.0", "typescript": "^5.8.3", "vite": "^6.3.5", "vitest": "^3.2.1", "@mysten/build-scripts": "0.0.0"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@vanilla-extract/css": "^1.17.2", "@vanilla-extract/dynamic": "^2.1.3", "@vanilla-extract/recipes": "^0.5.3", "clsx": "^2.1.1", "zustand": "^4.5.4", "@mysten/slush-wallet": "0.1.15", "@mysten/sui": "1.36.0", "@mysten/utils": "0.1.1", "@mysten/wallet-standard": "0.16.5"}, "peerDependencies": {"@tanstack/react-query": "^5.0.0", "react": "*"}, "sideEffects": ["*.css.ts", "*.css"], "scripts": {"clean": "rm -rf tsconfig.tsbuildinfo ./dist", "build": "build-dapp-kit", "test": "pnpm test:typecheck && pnpm vitest run", "test:typecheck": "tsc -b ./test", "size": "size-limit", "analyze": "size-limit --why", "prettier:check": "prettier -c --ignore-unknown .", "prettier:fix": "prettier -w --ignore-unknown .", "eslint:check": "eslint --max-warnings=0 .", "eslint:fix": "pnpm run eslint:check --fix", "lint": "pnpm run eslint:check && pnpm run prettier:check", "lint:fix": "pnpm run eslint:fix && pnpm run prettier:fix"}}