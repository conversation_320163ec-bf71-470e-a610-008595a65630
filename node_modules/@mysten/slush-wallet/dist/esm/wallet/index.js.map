{"version": 3, "sources": ["../../../src/wallet/index.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { fromBase64, toBase64 } from '@mysten/sui/utils';\nimport type {\n\tStandardConnectFeature,\n\tStandardConnectMethod,\n\tStandardDisconnectFeature,\n\tStandardDisconnectMethod,\n\tStandardEventsFeature,\n\tStandardEventsListeners,\n\tStandardEventsOnMethod,\n\tSuiChain,\n\tSuiSignAndExecuteTransactionFeature,\n\tSuiSignAndExecuteTransactionMethod,\n\tSuiSignPersonalMessageFeature,\n\tSuiSignPersonalMessageMethod,\n\tSuiSignTransactionBlockFeature,\n\tSuiSignTransactionBlockMethod,\n\tSuiSignTransactionFeature,\n\tSuiSignTransactionMethod,\n\tWallet,\n\tWalletIcon,\n} from '@mysten/wallet-standard';\nimport { getWallets, ReadonlyWalletAccount, SUI_CHAINS } from '@mysten/wallet-standard';\nimport type { Emitter } from 'mitt';\nimport mitt from 'mitt';\nimport type { InferOutput } from 'valibot';\nimport { boolean, object, parse, string } from 'valibot';\nimport { DappPostMessageChannel, decodeJwtSession } from '@mysten/window-wallet-core';\n\nconst DEFAULT_SLUSH_ORIGIN = 'https://my.slush.app';\n\ntype WalletEventsMap = {\n\t[E in keyof StandardEventsListeners]: Parameters<StandardEventsListeners[E]>[0];\n};\n\nconst SLUSH_SESSION_KEY = 'slush:session';\n\nexport const SLUSH_WALLET_NAME = 'Slush' as const;\n\nconst SUI_WALLET_EXTENSION_ID = 'com.mystenlabs.suiwallet' as const;\nconst METADATA_API_URL = 'https://api.slush.app/api/wallet/metadata';\n\nconst FALLBACK_METADATA = {\n\tid: 'com.mystenlabs.suiwallet.web',\n\twalletName: 'Slush',\n\tdescription: 'Trade and earn on Sui.',\n\ticon: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjUwIiBoZWlnaHQ9IjUwIiBmaWxsPSIjNENBMkZGIi8+CjxwYXRoIGQ9Ik0xMi4zNDczIDM0LjcyNTRDMTMuNTU1MyAzOS4yMzM2IDE4LjA2NzMgNDMuMzE0OCAyNy40MDI1IDQwLjgxMzRDMzYuMzA5NyAzOC40MjY3IDQxLjg5MjEgMzEuMDk5MyA0MC40NDQ2IDI1LjY5NzJDMzkuOTQ0NyAyMy44MzE3IDM4LjQzOTEgMjIuNTY4OSAzNi4xMTc4IDIyLjc3NDRMMTUuMzYxNSAyNC41MDM4QzE0LjA1NDQgMjQuNjA0MSAxMy40NTUgMjQuMzg5OCAxMy4xMDkyIDIzLjU2NjFDMTIuNzczOCAyMi43ODEyIDEyLjk2NDkgMjEuOTM4NSAxNC41NDM3IDIxLjE0MDZMMzAuMzM5NiAxMy4wMzQyQzMxLjU1MDMgMTIuNDE4MiAzMi4zNTY3IDEyLjE2MDUgMzMuMDkzNiAxMi40MjEzQzMzLjU1NTUgMTIuNTg5MSAzMy44NTk2IDEzLjI1NzQgMzMuNTgwMyAxNC4wODJMMzIuNTU2MSAxNy4xMDU2QzMxLjI5OTIgMjAuODE2NCAzMy45ODk5IDIxLjY3ODQgMzUuNTA2OCAyMS4yNzE5QzM3LjgwMTcgMjAuNjU3IDM4LjM0MTYgMTguNDcxMiAzNy42MDIzIDE1LjcxMTlDMzUuNzI3OCA4LjcxNjI5IDI4LjMwNTkgNy42MjI1NCAyMS41NzY4IDkuNDI1NTlDMTQuNzMxMSAxMS4yNTk5IDguNzk2ODEgMTYuODA3MiAxMC42MDg4IDIzLjU2OTZDMTEuMDM1OCAyNS4xNjMgMTIuNTAyNSAyNi40MzYyIDE0LjIwMTQgMjYuMzk3NUwxNi43OTUgMjYuMzkxMkMxNy4zMjg0IDI2LjM3ODggMTcuMTM2MyAyNi40MjI3IDE4LjE2NTMgMjYuMzM3NEMxOS4xOTQ0IDI2LjI1MjIgMjEuOTQyNSAyNS45MTQgMjEuOTQyNSAyNS45MTRMMzUuNDI3NSAyNC4zODhMMzUuNzc1IDI0LjMzNzVDMzYuNTYzNyAyNC4yMDMgMzcuMTU5NyAyNC40MDc5IDM3LjY2MzYgMjUuMjc2QzM4LjQxNzcgMjYuNTc1IDM3LjI2NzIgMjcuNTU0NiAzNS44ODk5IDI4LjcyNzJDMzUuODUzIDI4Ljc1ODYgMzUuODE2IDI4Ljc5MDEgMzUuNzc4OSAyOC44MjE4TDIzLjkyNSAzOS4wMzc3QzIxLjg5MzMgNDAuNzkwMSAyMC40NjYgNDAuMTMxMSAxOS45NjYyIDM4LjI2NTZMMTguMTk1OCAzMS42NTg3QzE3Ljc1ODUgMzAuMDI2NCAxNi4xNjQ2IDI4Ljc0NTYgMTQuMjk3NiAyOS4yNDU5QzExLjk2MzggMjkuODcxMiAxMS43NzQ2IDMyLjU4NzggMTIuMzQ3MyAzNC43MjU0WiIgZmlsbD0iIzA2MEQxNCIvPgo8L3N2Zz4K',\n\tenabled: true,\n};\n\nconst WalletMetadataSchema = object({\n\tid: string('Wallet ID is required'),\n\twalletName: string('Wallet name is required'),\n\ticon: string('Icon must be a valid wallet icon format'),\n\tenabled: boolean('Enabled is required'),\n});\n\nfunction setSessionToStorage(session: string) {\n\tlocalStorage.setItem(SLUSH_SESSION_KEY, session);\n}\n\nfunction getSessionFromStorage() {\n\tconst session = localStorage.getItem(SLUSH_SESSION_KEY);\n\n\tif (!session) {\n\t\tthrow new Error('No session found');\n\t}\n\n\treturn session;\n}\n\nconst walletAccountFeatures = [\n\t'sui:signTransaction',\n\t'sui:signAndExecuteTransaction',\n\t'sui:signPersonalMessage',\n\t'sui:signTransactionBlock',\n\t'sui:signAndExecuteTransactionBlock',\n] as const;\n\nfunction getAccountsFromSession(session: string) {\n\tconst { payload } = decodeJwtSession(session);\n\treturn payload.accounts.map((account) => {\n\t\treturn new ReadonlyWalletAccount({\n\t\t\taddress: account.address,\n\t\t\tchains: SUI_CHAINS,\n\t\t\tfeatures: walletAccountFeatures,\n\t\t\tpublicKey: fromBase64(account.publicKey),\n\t\t});\n\t});\n}\n\ntype WalletMetadata = InferOutput<typeof WalletMetadataSchema>;\nexport class SlushWallet implements Wallet {\n\t#id: string;\n\t#events: Emitter<WalletEventsMap>;\n\t#accounts: ReadonlyWalletAccount[];\n\t#origin: string;\n\t#walletName: string;\n\t#icon: WalletIcon;\n\t#name: string;\n\n\tget name() {\n\t\treturn this.#walletName;\n\t}\n\n\tget id() {\n\t\treturn this.#id;\n\t}\n\n\tget icon() {\n\t\treturn this.#icon;\n\t}\n\n\tget version() {\n\t\treturn '1.0.0' as const;\n\t}\n\n\tget chains() {\n\t\treturn SUI_CHAINS;\n\t}\n\n\tget accounts() {\n\t\treturn this.#accounts;\n\t}\n\n\tget features(): StandardConnectFeature &\n\t\tStandardDisconnectFeature &\n\t\tStandardEventsFeature &\n\t\tSuiSignTransactionBlockFeature &\n\t\tSuiSignTransactionFeature &\n\t\tSuiSignPersonalMessageFeature &\n\t\tSuiSignAndExecuteTransactionFeature {\n\t\treturn {\n\t\t\t'standard:connect': {\n\t\t\t\tversion: '1.0.0',\n\t\t\t\tconnect: this.#connect,\n\t\t\t},\n\t\t\t'standard:disconnect': {\n\t\t\t\tversion: '1.0.0',\n\t\t\t\tdisconnect: this.#disconnect,\n\t\t\t},\n\t\t\t'standard:events': {\n\t\t\t\tversion: '1.0.0',\n\t\t\t\ton: this.#on,\n\t\t\t},\n\t\t\t'sui:signTransactionBlock': {\n\t\t\t\tversion: '1.0.0',\n\t\t\t\tsignTransactionBlock: this.#signTransactionBlock,\n\t\t\t},\n\t\t\t'sui:signTransaction': {\n\t\t\t\tversion: '2.0.0',\n\t\t\t\tsignTransaction: this.#signTransaction,\n\t\t\t},\n\t\t\t'sui:signPersonalMessage': {\n\t\t\t\tversion: '1.1.0',\n\t\t\t\tsignPersonalMessage: this.#signPersonalMessage,\n\t\t\t},\n\t\t\t'sui:signAndExecuteTransaction': {\n\t\t\t\tversion: '2.0.0',\n\t\t\t\tsignAndExecuteTransaction: this.#signAndExecuteTransaction,\n\t\t\t},\n\t\t};\n\t}\n\n\tconstructor({\n\t\tname,\n\t\torigin,\n\t\tmetadata,\n\t}: {\n\t\tname: string;\n\t\torigin?: string;\n\t\tchain?: SuiChain;\n\t\tmetadata: WalletMetadata;\n\t}) {\n\t\tthis.#id = metadata.id;\n\t\tthis.#accounts = this.#getPreviouslyAuthorizedAccounts();\n\t\tthis.#events = mitt();\n\t\tthis.#origin = origin || DEFAULT_SLUSH_ORIGIN;\n\t\tthis.#name = name;\n\t\tthis.#walletName = metadata.walletName;\n\t\tthis.#icon = metadata.icon as WalletIcon;\n\t}\n\n\t#signTransactionBlock: SuiSignTransactionBlockMethod = async ({\n\t\ttransactionBlock,\n\t\taccount,\n\t\tchain,\n\t}) => {\n\t\tconst data = await transactionBlock.toJSON();\n\n\t\tconst popup = this.#getNewPopupChannel();\n\n\t\tconst response = await popup.send({\n\t\t\ttype: 'sign-transaction',\n\t\t\ttransaction: data,\n\t\t\taddress: account.address,\n\t\t\tchain,\n\t\t\tsession: getSessionFromStorage(),\n\t\t});\n\n\t\treturn {\n\t\t\ttransactionBlockBytes: response.bytes,\n\t\t\tsignature: response.signature,\n\t\t};\n\t};\n\n\t#signTransaction: SuiSignTransactionMethod = async ({ transaction, account, chain }) => {\n\t\tconst popup = this.#getNewPopupChannel();\n\n\t\tconst tx = await transaction.toJSON();\n\n\t\tconst response = await popup.send({\n\t\t\ttype: 'sign-transaction',\n\t\t\ttransaction: tx,\n\t\t\taddress: account.address,\n\t\t\tchain,\n\t\t\tsession: getSessionFromStorage(),\n\t\t});\n\n\t\treturn {\n\t\t\tbytes: response.bytes,\n\t\t\tsignature: response.signature,\n\t\t};\n\t};\n\n\t#signAndExecuteTransaction: SuiSignAndExecuteTransactionMethod = async ({\n\t\ttransaction,\n\t\taccount,\n\t\tchain,\n\t}) => {\n\t\tconst popup = this.#getNewPopupChannel();\n\n\t\tconst data = await transaction.toJSON();\n\n\t\tconst response = await popup.send({\n\t\t\ttype: 'sign-and-execute-transaction',\n\t\t\ttransaction: data,\n\t\t\taddress: account.address,\n\t\t\tchain,\n\t\t\tsession: getSessionFromStorage(),\n\t\t});\n\t\treturn {\n\t\t\tbytes: response.bytes,\n\t\t\tsignature: response.signature,\n\t\t\tdigest: response.digest,\n\t\t\teffects: response.effects,\n\t\t};\n\t};\n\n\t#signPersonalMessage: SuiSignPersonalMessageMethod = async ({ message, account, chain }) => {\n\t\tconst popup = this.#getNewPopupChannel();\n\n\t\tconst response = await popup.send({\n\t\t\ttype: 'sign-personal-message',\n\t\t\tmessage: toBase64(message),\n\t\t\taddress: account.address,\n\t\t\tchain: chain ?? account.chains[0],\n\t\t\tsession: getSessionFromStorage(),\n\t\t});\n\n\t\treturn {\n\t\t\tbytes: response.bytes,\n\t\t\tsignature: response.signature,\n\t\t};\n\t};\n\n\t#on: StandardEventsOnMethod = (event, listener) => {\n\t\tthis.#events.on(event, listener);\n\t\treturn () => this.#events.off(event, listener);\n\t};\n\n\t#setAccounts(accounts: ReadonlyWalletAccount[]) {\n\t\tthis.#accounts = accounts;\n\t\tthis.#events.emit('change', { accounts: this.accounts });\n\t}\n\n\t#connect: StandardConnectMethod = async (input) => {\n\t\tif (input?.silent) {\n\t\t\treturn { accounts: this.accounts };\n\t\t}\n\n\t\tconst popup = this.#getNewPopupChannel();\n\t\tconst response = await popup.send({\n\t\t\ttype: 'connect',\n\t\t});\n\n\t\tsetSessionToStorage(response.session);\n\t\tthis.#setAccounts(getAccountsFromSession(response.session));\n\n\t\treturn { accounts: this.accounts };\n\t};\n\n\t#getPreviouslyAuthorizedAccounts() {\n\t\ttry {\n\t\t\treturn getAccountsFromSession(getSessionFromStorage());\n\t\t} catch (error) {\n\t\t\treturn [];\n\t\t}\n\t}\n\n\t#disconnect: StandardDisconnectMethod = async () => {\n\t\tlocalStorage.removeItem(SLUSH_SESSION_KEY);\n\t\tthis.#setAccounts([]);\n\t};\n\n\t#getNewPopupChannel() {\n\t\treturn new DappPostMessageChannel({\n\t\t\tappName: this.#name,\n\t\t\thostOrigin: this.#origin,\n\t\t});\n\t}\n\n\tupdateMetadata(metadata: WalletMetadata) {\n\t\tthis.#id = metadata.id;\n\t\tthis.#walletName = metadata.walletName;\n\t\tthis.#icon = metadata.icon as WalletIcon;\n\t}\n}\n\nasync function fetchMetadata(metadataApiUrl: string): Promise<WalletMetadata> {\n\tconst response = await fetch(metadataApiUrl);\n\tif (!response.ok) {\n\t\tthrow new Error('Failed to fetch wallet metadata');\n\t}\n\tconst data = await response.json();\n\treturn parse(WalletMetadataSchema, data);\n}\n\nexport function registerSlushWallet(\n\tname: string,\n\t{\n\t\torigin,\n\t\tmetadataApiUrl = METADATA_API_URL,\n\t}: {\n\t\torigin?: string;\n\t\tmetadataApiUrl?: string;\n\t} = {},\n) {\n\tconst wallets = getWallets();\n\n\tlet unregister: (() => void) | null = null;\n\n\t// listen for wallet registration\n\twallets.on('register', (wallet) => {\n\t\tif (wallet.id === SUI_WALLET_EXTENSION_ID) {\n\t\t\tunregister?.();\n\t\t}\n\t});\n\n\tconst extension = wallets.get().find((wallet) => wallet.id === SUI_WALLET_EXTENSION_ID);\n\tif (extension) {\n\t\treturn;\n\t}\n\n\tconst slushWalletInstance = new SlushWallet({\n\t\tname,\n\t\torigin,\n\t\tmetadata: FALLBACK_METADATA,\n\t});\n\tunregister = wallets.register(slushWalletInstance);\n\n\tfetchMetadata(metadataApiUrl)\n\t\t.then((metadata) => {\n\t\t\tif (!metadata.enabled) {\n\t\t\t\tconsole.log('Slush wallet is not currently enabled.');\n\t\t\t\tunregister?.();\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tslushWalletInstance.updateMetadata(metadata);\n\t\t})\n\t\t.catch((error) => {\n\t\t\tconsole.error('Error fetching metadata', error);\n\t\t});\n\n\treturn {\n\t\twallet: slushWalletInstance,\n\t\tunregister,\n\t};\n}\n"], "mappings": ";;;;;;;;AAAA;AAGA,SAAS,YAAY,gBAAgB;AAqBrC,SAAS,YAAY,uBAAuB,kBAAkB;AAE9D,OAAO,UAAU;AAEjB,SAAS,SAAS,QAAQ,OAAO,cAAc;AAC/C,SAAS,wBAAwB,wBAAwB;AAEzD,MAAM,uBAAuB;AAM7B,MAAM,oBAAoB;AAEnB,MAAM,oBAAoB;AAEjC,MAAM,0BAA0B;AAChC,MAAM,mBAAmB;AAEzB,MAAM,oBAAoB;AAAA,EACzB,IAAI;AAAA,EACJ,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,MAAM;AAAA,EACN,SAAS;AACV;AAEA,MAAM,uBAAuB,OAAO;AAAA,EACnC,IAAI,OAAO,uBAAuB;AAAA,EAClC,YAAY,OAAO,yBAAyB;AAAA,EAC5C,MAAM,OAAO,yCAAyC;AAAA,EACtD,SAAS,QAAQ,qBAAqB;AACvC,CAAC;AAED,SAAS,oBAAoB,SAAiB;AAC7C,eAAa,QAAQ,mBAAmB,OAAO;AAChD;AAEA,SAAS,wBAAwB;AAChC,QAAM,UAAU,aAAa,QAAQ,iBAAiB;AAEtD,MAAI,CAAC,SAAS;AACb,UAAM,IAAI,MAAM,kBAAkB;AAAA,EACnC;AAEA,SAAO;AACR;AAEA,MAAM,wBAAwB;AAAA,EAC7B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEA,SAAS,uBAAuB,SAAiB;AAChD,QAAM,EAAE,QAAQ,IAAI,iBAAiB,OAAO;AAC5C,SAAO,QAAQ,SAAS,IAAI,CAAC,YAAY;AACxC,WAAO,IAAI,sBAAsB;AAAA,MAChC,SAAS,QAAQ;AAAA,MACjB,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,WAAW,WAAW,QAAQ,SAAS;AAAA,IACxC,CAAC;AAAA,EACF,CAAC;AACF;AAGO,MAAM,YAA8B;AAAA,EAwE1C,YAAY;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAKG;AAjFG;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AAoFA,8CAAuD,OAAO;AAAA,MAC7D;AAAA,MACA;AAAA,MACA;AAAA,IACD,MAAM;AACL,YAAM,OAAO,MAAM,iBAAiB,OAAO;AAE3C,YAAM,QAAQ,sBAAK,+CAAL;AAEd,YAAM,WAAW,MAAM,MAAM,KAAK;AAAA,QACjC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS,QAAQ;AAAA,QACjB;AAAA,QACA,SAAS,sBAAsB;AAAA,MAChC,CAAC;AAED,aAAO;AAAA,QACN,uBAAuB,SAAS;AAAA,QAChC,WAAW,SAAS;AAAA,MACrB;AAAA,IACD;AAEA,yCAA6C,OAAO,EAAE,aAAa,SAAS,MAAM,MAAM;AACvF,YAAM,QAAQ,sBAAK,+CAAL;AAEd,YAAM,KAAK,MAAM,YAAY,OAAO;AAEpC,YAAM,WAAW,MAAM,MAAM,KAAK;AAAA,QACjC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS,QAAQ;AAAA,QACjB;AAAA,QACA,SAAS,sBAAsB;AAAA,MAChC,CAAC;AAED,aAAO;AAAA,QACN,OAAO,SAAS;AAAA,QAChB,WAAW,SAAS;AAAA,MACrB;AAAA,IACD;AAEA,mDAAiE,OAAO;AAAA,MACvE;AAAA,MACA;AAAA,MACA;AAAA,IACD,MAAM;AACL,YAAM,QAAQ,sBAAK,+CAAL;AAEd,YAAM,OAAO,MAAM,YAAY,OAAO;AAEtC,YAAM,WAAW,MAAM,MAAM,KAAK;AAAA,QACjC,MAAM;AAAA,QACN,aAAa;AAAA,QACb,SAAS,QAAQ;AAAA,QACjB;AAAA,QACA,SAAS,sBAAsB;AAAA,MAChC,CAAC;AACD,aAAO;AAAA,QACN,OAAO,SAAS;AAAA,QAChB,WAAW,SAAS;AAAA,QACpB,QAAQ,SAAS;AAAA,QACjB,SAAS,SAAS;AAAA,MACnB;AAAA,IACD;AAEA,6CAAqD,OAAO,EAAE,SAAS,SAAS,MAAM,MAAM;AAC3F,YAAM,QAAQ,sBAAK,+CAAL;AAEd,YAAM,WAAW,MAAM,MAAM,KAAK;AAAA,QACjC,MAAM;AAAA,QACN,SAAS,SAAS,OAAO;AAAA,QACzB,SAAS,QAAQ;AAAA,QACjB,OAAO,SAAS,QAAQ,OAAO,CAAC;AAAA,QAChC,SAAS,sBAAsB;AAAA,MAChC,CAAC;AAED,aAAO;AAAA,QACN,OAAO,SAAS;AAAA,QAChB,WAAW,SAAS;AAAA,MACrB;AAAA,IACD;AAEA,4BAA8B,CAAC,OAAO,aAAa;AAClD,yBAAK,SAAQ,GAAG,OAAO,QAAQ;AAC/B,aAAO,MAAM,mBAAK,SAAQ,IAAI,OAAO,QAAQ;AAAA,IAC9C;AAOA,iCAAkC,OAAO,UAAU;AAClD,UAAI,OAAO,QAAQ;AAClB,eAAO,EAAE,UAAU,KAAK,SAAS;AAAA,MAClC;AAEA,YAAM,QAAQ,sBAAK,+CAAL;AACd,YAAM,WAAW,MAAM,MAAM,KAAK;AAAA,QACjC,MAAM;AAAA,MACP,CAAC;AAED,0BAAoB,SAAS,OAAO;AACpC,4BAAK,wCAAL,WAAkB,uBAAuB,SAAS,OAAO;AAEzD,aAAO,EAAE,UAAU,KAAK,SAAS;AAAA,IAClC;AAUA,oCAAwC,YAAY;AACnD,mBAAa,WAAW,iBAAiB;AACzC,4BAAK,wCAAL,WAAkB,CAAC;AAAA,IACpB;AAjIC,uBAAK,KAAM,SAAS;AACpB,uBAAK,WAAY,sBAAK,4DAAL;AACjB,uBAAK,SAAU,KAAK;AACpB,uBAAK,SAAU,UAAU;AACzB,uBAAK,OAAQ;AACb,uBAAK,aAAc,SAAS;AAC5B,uBAAK,OAAQ,SAAS;AAAA,EACvB;AAAA,EAhFA,IAAI,OAAO;AACV,WAAO,mBAAK;AAAA,EACb;AAAA,EAEA,IAAI,KAAK;AACR,WAAO,mBAAK;AAAA,EACb;AAAA,EAEA,IAAI,OAAO;AACV,WAAO,mBAAK;AAAA,EACb;AAAA,EAEA,IAAI,UAAU;AACb,WAAO;AAAA,EACR;AAAA,EAEA,IAAI,SAAS;AACZ,WAAO;AAAA,EACR;AAAA,EAEA,IAAI,WAAW;AACd,WAAO,mBAAK;AAAA,EACb;AAAA,EAEA,IAAI,WAMiC;AACpC,WAAO;AAAA,MACN,oBAAoB;AAAA,QACnB,SAAS;AAAA,QACT,SAAS,mBAAK;AAAA,MACf;AAAA,MACA,uBAAuB;AAAA,QACtB,SAAS;AAAA,QACT,YAAY,mBAAK;AAAA,MAClB;AAAA,MACA,mBAAmB;AAAA,QAClB,SAAS;AAAA,QACT,IAAI,mBAAK;AAAA,MACV;AAAA,MACA,4BAA4B;AAAA,QAC3B,SAAS;AAAA,QACT,sBAAsB,mBAAK;AAAA,MAC5B;AAAA,MACA,uBAAuB;AAAA,QACtB,SAAS;AAAA,QACT,iBAAiB,mBAAK;AAAA,MACvB;AAAA,MACA,2BAA2B;AAAA,QAC1B,SAAS;AAAA,QACT,qBAAqB,mBAAK;AAAA,MAC3B;AAAA,MACA,iCAAiC;AAAA,QAChC,SAAS;AAAA,QACT,2BAA2B,mBAAK;AAAA,MACjC;AAAA,IACD;AAAA,EACD;AAAA,EAsJA,eAAe,UAA0B;AACxC,uBAAK,KAAM,SAAS;AACpB,uBAAK,aAAc,SAAS;AAC5B,uBAAK,OAAQ,SAAS;AAAA,EACvB;AACD;AAhOC;AACA;AACA;AACA;AACA;AACA;AACA;AAoFA;AAuBA;AAmBA;AAwBA;AAiBA;AA9KM;AAmLN,iBAAY,SAAC,UAAmC;AAC/C,qBAAK,WAAY;AACjB,qBAAK,SAAQ,KAAK,UAAU,EAAE,UAAU,KAAK,SAAS,CAAC;AACxD;AAEA;AAgBA,qCAAgC,WAAG;AAClC,MAAI;AACH,WAAO,uBAAuB,sBAAsB,CAAC;AAAA,EACtD,SAAS,OAAO;AACf,WAAO,CAAC;AAAA,EACT;AACD;AAEA;AAKA,wBAAmB,WAAG;AACrB,SAAO,IAAI,uBAAuB;AAAA,IACjC,SAAS,mBAAK;AAAA,IACd,YAAY,mBAAK;AAAA,EAClB,CAAC;AACF;AASD,eAAe,cAAc,gBAAiD;AAC7E,QAAM,WAAW,MAAM,MAAM,cAAc;AAC3C,MAAI,CAAC,SAAS,IAAI;AACjB,UAAM,IAAI,MAAM,iCAAiC;AAAA,EAClD;AACA,QAAM,OAAO,MAAM,SAAS,KAAK;AACjC,SAAO,MAAM,sBAAsB,IAAI;AACxC;AAEO,SAAS,oBACf,MACA;AAAA,EACC;AAAA,EACA,iBAAiB;AAClB,IAGI,CAAC,GACJ;AACD,QAAM,UAAU,WAAW;AAE3B,MAAI,aAAkC;AAGtC,UAAQ,GAAG,YAAY,CAAC,WAAW;AAClC,QAAI,OAAO,OAAO,yBAAyB;AAC1C,mBAAa;AAAA,IACd;AAAA,EACD,CAAC;AAED,QAAM,YAAY,QAAQ,IAAI,EAAE,KAAK,CAAC,WAAW,OAAO,OAAO,uBAAuB;AACtF,MAAI,WAAW;AACd;AAAA,EACD;AAEA,QAAM,sBAAsB,IAAI,YAAY;AAAA,IAC3C;AAAA,IACA;AAAA,IACA,UAAU;AAAA,EACX,CAAC;AACD,eAAa,QAAQ,SAAS,mBAAmB;AAEjD,gBAAc,cAAc,EAC1B,KAAK,CAAC,aAAa;AACnB,QAAI,CAAC,SAAS,SAAS;AACtB,cAAQ,IAAI,wCAAwC;AACpD,mBAAa;AACb;AAAA,IACD;AACA,wBAAoB,eAAe,QAAQ;AAAA,EAC5C,CAAC,EACA,MAAM,CAAC,UAAU;AACjB,YAAQ,MAAM,2BAA2B,KAAK;AAAA,EAC/C,CAAC;AAEF,SAAO;AAAA,IACN,QAAQ;AAAA,IACR;AAAA,EACD;AACD;", "names": []}