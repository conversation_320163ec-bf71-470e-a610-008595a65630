{"name": "@mysten/slush-wallet", "version": "0.1.15", "description": "Wallet adapter for Slush web wallet", "license": "Apache-2.0", "author": "Mysten Labs <<EMAIL>>", "type": "commonjs", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/cjs/index.d.ts", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "sideEffects": false, "files": ["CHANGELOG.md", "dist", "experimental"], "repository": {"type": "git", "url": "git+https://github.com/mystenlabs/ts-sdks.git"}, "bugs": {"url": "https://github.com/mystenlabs/ts-sdks/issues"}, "homepage": "https://github.com/MystenLabs/ts-sdks/tree/main/packages/slush-wallet#readme", "devDependencies": {"@types/node": "^22.15.29", "typescript": "^5.8.3", "vitest": "^3.2.1", "@mysten/build-scripts": "0.0.0"}, "dependencies": {"mitt": "^3.0.1", "valibot": "^0.36.0", "@mysten/sui": "1.36.0", "@mysten/utils": "0.1.1", "@mysten/wallet-standard": "0.16.5", "@mysten/window-wallet-core": "0.0.6"}, "scripts": {"clean": "rm -rf tsconfig.tsbuildinfo ./dist", "build": "build-package", "prettier:check": "prettier -c --ignore-unknown .", "prettier:fix": "prettier -w --ignore-unknown .", "eslint:check": "eslint --max-warnings=0 .", "eslint:fix": "pnpm run eslint:check --fix", "lint": "pnpm run eslint:check && pnpm run prettier:check", "lint:fix": "pnpm run eslint:fix && pnpm run prettier:fix"}}