{"version": 3, "sources": ["../../../src/zklogin/signature.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { fromBase64, toBase64 } from '@mysten/bcs';\n\nimport { SIGNATURE_SCHEME_TO_FLAG } from '../cryptography/signature-scheme.js';\nimport type { ZkLoginSignature } from './bcs.js';\nimport { zkLoginSignature } from './bcs.js';\n\ninterface ZkLoginSignatureExtended extends Omit<ZkLoginSignature, 'userSignature'> {\n\tuserSignature: string | ZkLoginSignature['userSignature'];\n}\n\nfunction getZkLoginSignatureBytes({ inputs, maxEpoch, userSignature }: ZkLoginSignatureExtended) {\n\treturn zkLoginSignature\n\t\t.serialize(\n\t\t\t{\n\t\t\t\tinputs,\n\t\t\t\tmaxEpoch,\n\t\t\t\tuserSignature:\n\t\t\t\t\ttypeof userSignature === 'string' ? fromBase64(userSignature) : userSignature,\n\t\t\t},\n\t\t\t{ maxSize: 2048 },\n\t\t)\n\t\t.toBytes();\n}\n\nexport function getZkLoginSignature({ inputs, maxEpoch, userSignature }: ZkLoginSignatureExtended) {\n\tconst bytes = getZkLoginSignatureBytes({ inputs, maxEpoch, userSignature });\n\tconst signatureBytes = new Uint8Array(bytes.length + 1);\n\tsignatureBytes.set([SIGNATURE_SCHEME_TO_FLAG.ZkLogin]);\n\tsignatureBytes.set(bytes, 1);\n\treturn toBase64(signatureBytes);\n}\n\nexport function parseZkLoginSignature(signature: string | Uint8Array) {\n\treturn zkLoginSignature.parse(typeof signature === 'string' ? fromBase64(signature) : signature);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAAqC;AAErC,8BAAyC;AAEzC,IAAAA,cAAiC;AAMjC,SAAS,yBAAyB,EAAE,QAAQ,UAAU,cAAc,GAA6B;AAChG,SAAO,6BACL;AAAA,IACA;AAAA,MACC;AAAA,MACA;AAAA,MACA,eACC,OAAO,kBAAkB,eAAW,uBAAW,aAAa,IAAI;AAAA,IAClE;AAAA,IACA,EAAE,SAAS,KAAK;AAAA,EACjB,EACC,QAAQ;AACX;AAEO,SAAS,oBAAoB,EAAE,QAAQ,UAAU,cAAc,GAA6B;AAClG,QAAM,QAAQ,yBAAyB,EAAE,QAAQ,UAAU,cAAc,CAAC;AAC1E,QAAM,iBAAiB,IAAI,WAAW,MAAM,SAAS,CAAC;AACtD,iBAAe,IAAI,CAAC,iDAAyB,OAAO,CAAC;AACrD,iBAAe,IAAI,OAAO,CAAC;AAC3B,aAAO,qBAAS,cAAc;AAC/B;AAEO,SAAS,sBAAsB,WAAgC;AACrE,SAAO,6BAAiB,MAAM,OAAO,cAAc,eAAW,uBAAW,SAAS,IAAI,SAAS;AAChG;", "names": ["import_bcs"]}