{"version": 3, "sources": ["../../../src/zklogin/address.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { blake2b } from '@noble/hashes/blake2b';\nimport { bytesToHex } from '@noble/hashes/utils';\n\nimport { SIGNATURE_SCHEME_TO_FLAG } from '../cryptography/signature-scheme.js';\nimport { normalizeSuiAddress, SUI_ADDRESS_LENGTH } from '../utils/index.js';\nimport { decodeJwt } from './jwt-utils.js';\nimport {\n\tgenAddressSeed,\n\tnormalizeZkLoginIssuer,\n\ttoBigEndianBytes,\n\ttoPaddedBigEndianBytes,\n} from './utils.js';\n\nexport function computeZkLoginAddressFromSeed(\n\taddressSeed: bigint,\n\tiss: string,\n\t/** TODO: This default should be changed in the next major release */\n\tlegacyAddress = true,\n) {\n\tconst addressSeedBytesBigEndian = legacyAddress\n\t\t? toBigEndianBytes(addressSeed, 32)\n\t\t: toPaddedBigEndianBytes(addressSeed, 32);\n\n\tconst addressParamBytes = new TextEncoder().encode(normalizeZkLoginIssuer(iss));\n\tconst tmp = new Uint8Array(2 + addressSeedBytesBigEndian.length + addressParamBytes.length);\n\n\ttmp.set([SIGNATURE_SCHEME_TO_FLAG.ZkLogin]);\n\ttmp.set([addressParamBytes.length], 1);\n\ttmp.set(addressParamBytes, 2);\n\ttmp.set(addressSeedBytesBigEndian, 2 + addressParamBytes.length);\n\n\treturn normalizeSuiAddress(\n\t\tbytesToHex(blake2b(tmp, { dkLen: 32 })).slice(0, SUI_ADDRESS_LENGTH * 2),\n\t);\n}\n\nexport const MAX_HEADER_LEN_B64 = 248;\nexport const MAX_PADDED_UNSIGNED_JWT_LEN = 64 * 25;\n\nexport function lengthChecks(jwt: string) {\n\tconst [header, payload] = jwt.split('.');\n\t/// Is the header small enough\n\tif (header.length > MAX_HEADER_LEN_B64) {\n\t\tthrow new Error(`Header is too long`);\n\t}\n\n\t/// Is the combined length of (header, payload, SHA2 padding) small enough?\n\t// unsigned_jwt = header + '.' + payload;\n\tconst L = (header.length + 1 + payload.length) * 8;\n\tconst K = (512 + 448 - ((L % 512) + 1)) % 512;\n\n\t// The SHA2 padding is 1 followed by K zeros, followed by the length of the message\n\tconst padded_unsigned_jwt_len = (L + 1 + K + 64) / 8;\n\n\t// The padded unsigned JWT must be less than the max_padded_unsigned_jwt_len\n\tif (padded_unsigned_jwt_len > MAX_PADDED_UNSIGNED_JWT_LEN) {\n\t\tthrow new Error(`JWT is too long`);\n\t}\n}\n\nexport function jwtToAddress(jwt: string, userSalt: string | bigint, legacyAddress = false) {\n\tlengthChecks(jwt);\n\n\tconst decodedJWT = decodeJwt(jwt);\n\n\treturn computeZkLoginAddress({\n\t\tuserSalt,\n\t\tclaimName: 'sub',\n\t\tclaimValue: decodedJWT.sub,\n\t\taud: decodedJWT.aud,\n\t\tiss: decodedJWT.iss,\n\t\tlegacyAddress,\n\t});\n}\n\nexport interface ComputeZkLoginAddressOptions {\n\tclaimName: string;\n\tclaimValue: string;\n\tuserSalt: string | bigint;\n\tiss: string;\n\taud: string;\n\tlegacyAddress?: boolean;\n}\n\nexport function computeZkLoginAddress({\n\tclaimName,\n\tclaimValue,\n\tiss,\n\taud,\n\tuserSalt,\n\tlegacyAddress = false,\n}: ComputeZkLoginAddressOptions) {\n\treturn computeZkLoginAddressFromSeed(\n\t\tgenAddressSeed(userSalt, claimName, claimValue, aud),\n\t\tiss,\n\t\tlegacyAddress,\n\t);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,qBAAwB;AACxB,mBAA2B;AAE3B,8BAAyC;AACzC,IAAAA,gBAAwD;AACxD,uBAA0B;AAC1B,IAAAA,gBAKO;AAEA,SAAS,8BACf,aACA,KAEA,gBAAgB,MACf;AACD,QAAM,4BAA4B,oBAC/B,gCAAiB,aAAa,EAAE,QAChC,sCAAuB,aAAa,EAAE;AAEzC,QAAM,oBAAoB,IAAI,YAAY,EAAE,WAAO,sCAAuB,GAAG,CAAC;AAC9E,QAAM,MAAM,IAAI,WAAW,IAAI,0BAA0B,SAAS,kBAAkB,MAAM;AAE1F,MAAI,IAAI,CAAC,iDAAyB,OAAO,CAAC;AAC1C,MAAI,IAAI,CAAC,kBAAkB,MAAM,GAAG,CAAC;AACrC,MAAI,IAAI,mBAAmB,CAAC;AAC5B,MAAI,IAAI,2BAA2B,IAAI,kBAAkB,MAAM;AAE/D,aAAO;AAAA,QACN,6BAAW,wBAAQ,KAAK,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,mCAAqB,CAAC;AAAA,EACxE;AACD;AAEO,MAAM,qBAAqB;AAC3B,MAAM,8BAA8B,KAAK;AAEzC,SAAS,aAAa,KAAa;AACzC,QAAM,CAAC,QAAQ,OAAO,IAAI,IAAI,MAAM,GAAG;AAEvC,MAAI,OAAO,SAAS,oBAAoB;AACvC,UAAM,IAAI,MAAM,oBAAoB;AAAA,EACrC;AAIA,QAAM,KAAK,OAAO,SAAS,IAAI,QAAQ,UAAU;AACjD,QAAM,KAAK,MAAM,OAAQ,IAAI,MAAO,MAAM;AAG1C,QAAM,2BAA2B,IAAI,IAAI,IAAI,MAAM;AAGnD,MAAI,0BAA0B,6BAA6B;AAC1D,UAAM,IAAI,MAAM,iBAAiB;AAAA,EAClC;AACD;AAEO,SAAS,aAAa,KAAa,UAA2B,gBAAgB,OAAO;AAC3F,eAAa,GAAG;AAEhB,QAAM,iBAAa,4BAAU,GAAG;AAEhC,SAAO,sBAAsB;AAAA,IAC5B;AAAA,IACA,WAAW;AAAA,IACX,YAAY,WAAW;AAAA,IACvB,KAAK,WAAW;AAAA,IAChB,KAAK,WAAW;AAAA,IAChB;AAAA,EACD,CAAC;AACF;AAWO,SAAS,sBAAsB;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,gBAAgB;AACjB,GAAiC;AAChC,SAAO;AAAA,QACN,8BAAe,UAAU,WAAW,YAAY,GAAG;AAAA,IACnD;AAAA,IACA;AAAA,EACD;AACD;", "names": ["import_utils"]}