{"version": 3, "sources": ["../../../src/zklogin/nonce.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { toHex } from '@mysten/bcs';\nimport { randomBytes } from '@noble/hashes/utils';\nimport { base64urlnopad } from '@scure/base';\n\nimport type { PublicKey } from '../cryptography/publickey.js';\nimport { poseidonHash } from './poseidon.js';\nimport { toPaddedBigEndianBytes } from './utils.js';\n\nexport const NONCE_LENGTH = 27;\n\nfunction toBigIntBE(bytes: Uint8Array) {\n\tconst hex = toHex(bytes);\n\tif (hex.length === 0) {\n\t\treturn BigInt(0);\n\t}\n\treturn BigInt(`0x${hex}`);\n}\n\nexport function generateRandomness() {\n\t// Once Node 20 enters LTS, we can just use crypto.getRandomValues(new Uint8Array(16)), but until then we use `randomBytes` to improve compatibility:\n\treturn String(toBigIntBE(randomBytes(16)));\n}\n\nexport function generateNonce(publicKey: PublicKey, maxEpoch: number, randomness: bigint | string) {\n\tconst publicKeyBytes = toBigIntBE(publicKey.toSuiBytes());\n\tconst eph_public_key_0 = publicKeyBytes / 2n ** 128n;\n\tconst eph_public_key_1 = publicKeyBytes % 2n ** 128n;\n\tconst bigNum = poseidonHash([eph_public_key_0, eph_public_key_1, maxEpoch, BigInt(randomness)]);\n\tconst Z = toPaddedBigEndianBytes(bigNum, 20);\n\tconst nonce = base64urlnopad.encode(Z);\n\n\tif (nonce.length !== NONCE_LENGTH) {\n\t\tthrow new Error(`Length of nonce ${nonce} (${nonce.length}) is not equal to ${NONCE_LENGTH}`);\n\t}\n\treturn nonce;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAAsB;AACtB,mBAA4B;AAC5B,kBAA+B;AAG/B,sBAA6B;AAC7B,IAAAA,gBAAuC;AAEhC,MAAM,eAAe;AAE5B,SAAS,WAAW,OAAmB;AACtC,QAAM,UAAM,kBAAM,KAAK;AACvB,MAAI,IAAI,WAAW,GAAG;AACrB,WAAO,OAAO,CAAC;AAAA,EAChB;AACA,SAAO,OAAO,KAAK,GAAG,EAAE;AACzB;AAEO,SAAS,qBAAqB;AAEpC,SAAO,OAAO,eAAW,0BAAY,EAAE,CAAC,CAAC;AAC1C;AAEO,SAAS,cAAc,WAAsB,UAAkB,YAA6B;AAClG,QAAM,iBAAiB,WAAW,UAAU,WAAW,CAAC;AACxD,QAAM,mBAAmB,iBAAiB,MAAM;AAChD,QAAM,mBAAmB,iBAAiB,MAAM;AAChD,QAAM,aAAS,8BAAa,CAAC,kBAAkB,kBAAkB,UAAU,OAAO,UAAU,CAAC,CAAC;AAC9F,QAAM,QAAI,sCAAuB,QAAQ,EAAE;AAC3C,QAAM,QAAQ,2BAAe,OAAO,CAAC;AAErC,MAAI,MAAM,WAAW,cAAc;AAClC,UAAM,IAAI,MAAM,mBAAmB,KAAK,KAAK,MAAM,MAAM,qBAAqB,YAAY,EAAE;AAAA,EAC7F;AACA,SAAO;AACR;", "names": ["import_utils"]}