{"version": 3, "sources": ["../../../src/zklogin/publickey.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { fromBase64, toBase64, toHex } from '@mysten/bcs';\nimport { blake2b } from '@noble/hashes/blake2b';\nimport { bytesToHex } from '@noble/hashes/utils';\n\nimport { PublicKey } from '../cryptography/publickey.js';\nimport type { PublicKeyInitData } from '../cryptography/publickey.js';\nimport { SIGNATURE_SCHEME_TO_FLAG } from '../cryptography/signature-scheme.js';\nimport { SuiGraphQLClient } from '../graphql/client.js';\nimport { normalizeSuiAddress, SUI_ADDRESS_LENGTH } from '../utils/sui-types.js';\nimport type { ZkLoginSignatureInputs } from './bcs.js';\nimport { extractClaimValue } from './jwt-utils.js';\nimport { parseZkLoginSignature } from './signature.js';\nimport { normalizeZkLoginIssuer, toBigEndianBytes, toPaddedBigEndianBytes } from './utils.js';\nimport type { ClientWithExtensions, Experimental_SuiClientTypes } from '../experimental/types.js';\n\nexport interface ZkLoginCompatibleClient\n\textends ClientWithExtensions<{\n\t\tcore: {\n\t\t\tverifyZkLoginSignature: Experimental_SuiClientTypes.TransportMethods['verifyZkLoginSignature'];\n\t\t};\n\t}> {}\n\n/**\n * A zkLogin public identifier\n */\nexport class ZkLoginPublicIdentifier extends PublicKey {\n\t#data: Uint8Array;\n\t#client?: ZkLoginCompatibleClient;\n\t#legacyAddress: boolean;\n\n\t/**\n\t * Create a new ZkLoginPublicIdentifier object\n\t * @param value zkLogin public identifier as buffer or base-64 encoded string\n\t */\n\tconstructor(value: PublicKeyInitData, { client }: { client?: ZkLoginCompatibleClient } = {}) {\n\t\tsuper();\n\n\t\tthis.#client = client;\n\n\t\tif (typeof value === 'string') {\n\t\t\tthis.#data = fromBase64(value);\n\t\t} else if (value instanceof Uint8Array) {\n\t\t\tthis.#data = value;\n\t\t} else {\n\t\t\tthis.#data = Uint8Array.from(value);\n\t\t}\n\t\tthis.#legacyAddress = this.#data.length !== this.#data[0] + 1 + 32;\n\n\t\tif (this.#legacyAddress) {\n\t\t\tthis.#data = normalizeZkLoginPublicKeyBytes(this.#data);\n\t\t}\n\t}\n\n\tstatic fromBytes(\n\t\tbytes: Uint8Array,\n\t\t{\n\t\t\tclient,\n\t\t\taddress,\n\t\t\tlegacyAddress,\n\t\t}: { client?: ZkLoginCompatibleClient; address?: string; legacyAddress?: boolean } = {},\n\t) {\n\t\tlet publicKey: ZkLoginPublicIdentifier;\n\n\t\tif (legacyAddress === true) {\n\t\t\tpublicKey = new ZkLoginPublicIdentifier(normalizeZkLoginPublicKeyBytes(bytes, true), {\n\t\t\t\tclient,\n\t\t\t});\n\t\t} else if (legacyAddress === false) {\n\t\t\tpublicKey = new ZkLoginPublicIdentifier(normalizeZkLoginPublicKeyBytes(bytes, false), {\n\t\t\t\tclient,\n\t\t\t});\n\t\t} else if (address) {\n\t\t\tpublicKey = new ZkLoginPublicIdentifier(normalizeZkLoginPublicKeyBytes(bytes, false), {\n\t\t\t\tclient,\n\t\t\t});\n\n\t\t\tif (publicKey.toSuiAddress() !== address) {\n\t\t\t\tpublicKey = new ZkLoginPublicIdentifier(normalizeZkLoginPublicKeyBytes(bytes, true), {\n\t\t\t\t\tclient,\n\t\t\t\t});\n\t\t\t}\n\t\t} else {\n\t\t\tpublicKey = new ZkLoginPublicIdentifier(bytes, {\n\t\t\t\tclient,\n\t\t\t});\n\t\t}\n\n\t\tif (address && publicKey.toSuiAddress() !== address) {\n\t\t\tthrow new Error('Public key bytes do not match the provided address');\n\t\t}\n\n\t\treturn publicKey;\n\t}\n\n\tstatic fromProof(address: string, proof: ZkLoginSignatureInputs) {\n\t\tconst { issBase64Details, addressSeed } = proof;\n\t\tconst iss = extractClaimValue<string>(issBase64Details, 'iss');\n\n\t\tconst legacyPublicKey = toZkLoginPublicIdentifier(BigInt(addressSeed), iss, {\n\t\t\tlegacyAddress: true,\n\t\t});\n\n\t\tif (legacyPublicKey.toSuiAddress() === address) {\n\t\t\treturn legacyPublicKey;\n\t\t}\n\n\t\tconst publicKey = toZkLoginPublicIdentifier(BigInt(addressSeed), iss, {\n\t\t\tlegacyAddress: false,\n\t\t});\n\n\t\tif (publicKey.toSuiAddress() !== address) {\n\t\t\tthrow new Error('Proof does not match address');\n\t\t}\n\n\t\treturn publicKey;\n\t}\n\n\t/**\n\t * Checks if two zkLogin public identifiers are equal\n\t */\n\toverride equals(publicKey: ZkLoginPublicIdentifier): boolean {\n\t\treturn super.equals(publicKey);\n\t}\n\n\toverride toSuiAddress(): string {\n\t\tif (this.#legacyAddress) {\n\t\t\treturn this.#toLegacyAddress();\n\t\t}\n\n\t\treturn super.toSuiAddress();\n\t}\n\n\t#toLegacyAddress() {\n\t\tconst legacyBytes = normalizeZkLoginPublicKeyBytes(this.#data, true);\n\t\tconst addressBytes = new Uint8Array(legacyBytes.length + 1);\n\t\taddressBytes[0] = this.flag();\n\t\taddressBytes.set(legacyBytes, 1);\n\t\treturn normalizeSuiAddress(\n\t\t\tbytesToHex(blake2b(addressBytes, { dkLen: 32 })).slice(0, SUI_ADDRESS_LENGTH * 2),\n\t\t);\n\t}\n\n\t/**\n\t * Return the byte array representation of the zkLogin public identifier\n\t */\n\ttoRawBytes(): Uint8Array {\n\t\treturn this.#data;\n\t}\n\n\t/**\n\t * Return the Sui address associated with this ZkLogin public identifier\n\t */\n\tflag(): number {\n\t\treturn SIGNATURE_SCHEME_TO_FLAG['ZkLogin'];\n\t}\n\n\t/**\n\t * Verifies that the signature is valid for for the provided message\n\t */\n\tasync verify(_message: Uint8Array, _signature: Uint8Array | string): Promise<boolean> {\n\t\tthrow Error('does not support');\n\t}\n\n\t/**\n\t * Verifies that the signature is valid for for the provided PersonalMessage\n\t */\n\tverifyPersonalMessage(message: Uint8Array, signature: Uint8Array | string): Promise<boolean> {\n\t\tconst parsedSignature = parseSerializedZkLoginSignature(signature);\n\t\tconst address = new ZkLoginPublicIdentifier(parsedSignature.publicKey).toSuiAddress();\n\n\t\treturn graphqlVerifyZkLoginSignature({\n\t\t\taddress: address,\n\t\t\tbytes: toBase64(message),\n\t\t\tsignature: parsedSignature.serializedSignature,\n\t\t\tintentScope: 'PersonalMessage',\n\t\t\tclient: this.#client,\n\t\t});\n\t}\n\n\t/**\n\t * Verifies that the signature is valid for for the provided Transaction\n\t */\n\tverifyTransaction(transaction: Uint8Array, signature: Uint8Array | string): Promise<boolean> {\n\t\tconst parsedSignature = parseSerializedZkLoginSignature(signature);\n\t\tconst address = new ZkLoginPublicIdentifier(parsedSignature.publicKey).toSuiAddress();\n\t\treturn graphqlVerifyZkLoginSignature({\n\t\t\taddress: address,\n\t\t\tbytes: toBase64(transaction),\n\t\t\tsignature: parsedSignature.serializedSignature,\n\t\t\tintentScope: 'TransactionData',\n\t\t\tclient: this.#client,\n\t\t});\n\t}\n\n\t/**\n\t * Verifies that the public key is associated with the provided address\n\t */\n\toverride verifyAddress(address: string): boolean {\n\t\treturn address === super.toSuiAddress() || address === this.#toLegacyAddress();\n\t}\n}\n\n// Derive the public identifier for zklogin based on address seed and iss.\nexport function toZkLoginPublicIdentifier(\n\taddressSeed: bigint,\n\tiss: string,\n\toptions?: { client?: ZkLoginCompatibleClient; legacyAddress?: boolean },\n): ZkLoginPublicIdentifier {\n\t// Consists of iss_bytes_len || iss_bytes || padded_32_byte_address_seed.\n\tconst addressSeedBytesBigEndian = options?.legacyAddress\n\t\t? toBigEndianBytes(addressSeed, 32)\n\t\t: toPaddedBigEndianBytes(addressSeed, 32);\n\n\tconst issBytes = new TextEncoder().encode(normalizeZkLoginIssuer(iss));\n\tconst tmp = new Uint8Array(1 + issBytes.length + addressSeedBytesBigEndian.length);\n\ttmp.set([issBytes.length], 0);\n\ttmp.set(issBytes, 1);\n\ttmp.set(addressSeedBytesBigEndian, 1 + issBytes.length);\n\treturn new ZkLoginPublicIdentifier(tmp, options);\n}\n\nfunction normalizeZkLoginPublicKeyBytes(bytes: Uint8Array, legacyAddress = false) {\n\tconst issByteLength = bytes[0] + 1;\n\tconst addressSeed = BigInt(`0x${toHex(bytes.slice(issByteLength))}`);\n\tconst seedBytes = legacyAddress\n\t\t? toBigEndianBytes(addressSeed, 32)\n\t\t: toPaddedBigEndianBytes(addressSeed, 32);\n\tconst data = new Uint8Array(issByteLength + seedBytes.length);\n\tdata.set(bytes.slice(0, issByteLength), 0);\n\tdata.set(seedBytes, issByteLength);\n\treturn data;\n}\n\nasync function graphqlVerifyZkLoginSignature({\n\taddress,\n\tbytes,\n\tsignature,\n\tintentScope,\n\tclient = new SuiGraphQLClient({\n\t\turl: 'https://sui-mainnet.mystenlabs.com/graphql',\n\t}),\n}: {\n\taddress: string;\n\tbytes: string;\n\tsignature: string;\n\tintentScope: 'PersonalMessage' | 'TransactionData';\n\tclient?: ZkLoginCompatibleClient;\n}) {\n\tconst resp = await client.core.verifyZkLoginSignature({\n\t\tbytes,\n\t\tsignature,\n\t\tintentScope,\n\t\tauthor: address,\n\t});\n\n\treturn resp.success === true && resp.errors.length === 0;\n}\n\nexport function parseSerializedZkLoginSignature(signature: Uint8Array | string) {\n\tconst bytes = typeof signature === 'string' ? fromBase64(signature) : signature;\n\n\tif (bytes[0] !== SIGNATURE_SCHEME_TO_FLAG.ZkLogin) {\n\t\tthrow new Error('Invalid signature scheme');\n\t}\n\n\tconst signatureBytes = bytes.slice(1);\n\tconst { inputs, maxEpoch, userSignature } = parseZkLoginSignature(signatureBytes);\n\tconst { issBase64Details, addressSeed } = inputs;\n\tconst iss = extractClaimValue<string>(issBase64Details, 'iss');\n\tconst publicIdentifer = toZkLoginPublicIdentifier(BigInt(addressSeed), iss);\n\treturn {\n\t\tserializedSignature: toBase64(bytes),\n\t\tsignatureScheme: 'ZkLogin' as const,\n\t\tzkLogin: {\n\t\t\tinputs,\n\t\t\tmaxEpoch,\n\t\t\tuserSignature,\n\t\t\tiss,\n\t\t\taddressSeed: BigInt(addressSeed),\n\t\t},\n\t\tsignature: bytes,\n\t\tpublicKey: publicIdentifer.toRawBytes(),\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAA4C;AAC5C,qBAAwB;AACxB,mBAA2B;AAE3B,uBAA0B;AAE1B,8BAAyC;AACzC,oBAAiC;AACjC,uBAAwD;AAExD,uBAAkC;AAClC,uBAAsC;AACtC,IAAAA,gBAAiF;AAfjF;AA4BO,MAAM,2BAAN,MAAM,iCAAgC,2BAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAStD,YAAY,OAA0B,EAAE,OAAO,IAA0C,CAAC,GAAG;AAC5F,UAAM;AAVD;AACN;AACA;AACA;AASC,uBAAK,SAAU;AAEf,QAAI,OAAO,UAAU,UAAU;AAC9B,yBAAK,WAAQ,uBAAW,KAAK;AAAA,IAC9B,WAAW,iBAAiB,YAAY;AACvC,yBAAK,OAAQ;AAAA,IACd,OAAO;AACN,yBAAK,OAAQ,WAAW,KAAK,KAAK;AAAA,IACnC;AACA,uBAAK,gBAAiB,mBAAK,OAAM,WAAW,mBAAK,OAAM,CAAC,IAAI,IAAI;AAEhE,QAAI,mBAAK,iBAAgB;AACxB,yBAAK,OAAQ,+BAA+B,mBAAK,MAAK;AAAA,IACvD;AAAA,EACD;AAAA,EAEA,OAAO,UACN,OACA;AAAA,IACC;AAAA,IACA;AAAA,IACA;AAAA,EACD,IAAqF,CAAC,GACrF;AACD,QAAI;AAEJ,QAAI,kBAAkB,MAAM;AAC3B,kBAAY,IAAI,yBAAwB,+BAA+B,OAAO,IAAI,GAAG;AAAA,QACpF;AAAA,MACD,CAAC;AAAA,IACF,WAAW,kBAAkB,OAAO;AACnC,kBAAY,IAAI,yBAAwB,+BAA+B,OAAO,KAAK,GAAG;AAAA,QACrF;AAAA,MACD,CAAC;AAAA,IACF,WAAW,SAAS;AACnB,kBAAY,IAAI,yBAAwB,+BAA+B,OAAO,KAAK,GAAG;AAAA,QACrF;AAAA,MACD,CAAC;AAED,UAAI,UAAU,aAAa,MAAM,SAAS;AACzC,oBAAY,IAAI,yBAAwB,+BAA+B,OAAO,IAAI,GAAG;AAAA,UACpF;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD,OAAO;AACN,kBAAY,IAAI,yBAAwB,OAAO;AAAA,QAC9C;AAAA,MACD,CAAC;AAAA,IACF;AAEA,QAAI,WAAW,UAAU,aAAa,MAAM,SAAS;AACpD,YAAM,IAAI,MAAM,oDAAoD;AAAA,IACrE;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,OAAO,UAAU,SAAiB,OAA+B;AAChE,UAAM,EAAE,kBAAkB,YAAY,IAAI;AAC1C,UAAM,UAAM,oCAA0B,kBAAkB,KAAK;AAE7D,UAAM,kBAAkB,0BAA0B,OAAO,WAAW,GAAG,KAAK;AAAA,MAC3E,eAAe;AAAA,IAChB,CAAC;AAED,QAAI,gBAAgB,aAAa,MAAM,SAAS;AAC/C,aAAO;AAAA,IACR;AAEA,UAAM,YAAY,0BAA0B,OAAO,WAAW,GAAG,KAAK;AAAA,MACrE,eAAe;AAAA,IAChB,CAAC;AAED,QAAI,UAAU,aAAa,MAAM,SAAS;AACzC,YAAM,IAAI,MAAM,8BAA8B;AAAA,IAC/C;AAEA,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKS,OAAO,WAA6C;AAC5D,WAAO,MAAM,OAAO,SAAS;AAAA,EAC9B;AAAA,EAES,eAAuB;AAC/B,QAAI,mBAAK,iBAAgB;AACxB,aAAO,sBAAK,wDAAL;AAAA,IACR;AAEA,WAAO,MAAM,aAAa;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA,EAeA,aAAyB;AACxB,WAAO,mBAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAKA,OAAe;AACd,WAAO,iDAAyB,SAAS;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO,UAAsB,YAAmD;AACrF,UAAM,MAAM,kBAAkB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKA,sBAAsB,SAAqB,WAAkD;AAC5F,UAAM,kBAAkB,gCAAgC,SAAS;AACjE,UAAM,UAAU,IAAI,yBAAwB,gBAAgB,SAAS,EAAE,aAAa;AAEpF,WAAO,8BAA8B;AAAA,MACpC;AAAA,MACA,WAAO,qBAAS,OAAO;AAAA,MACvB,WAAW,gBAAgB;AAAA,MAC3B,aAAa;AAAA,MACb,QAAQ,mBAAK;AAAA,IACd,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,aAAyB,WAAkD;AAC5F,UAAM,kBAAkB,gCAAgC,SAAS;AACjE,UAAM,UAAU,IAAI,yBAAwB,gBAAgB,SAAS,EAAE,aAAa;AACpF,WAAO,8BAA8B;AAAA,MACpC;AAAA,MACA,WAAO,qBAAS,WAAW;AAAA,MAC3B,WAAW,gBAAgB;AAAA,MAC3B,aAAa;AAAA,MACb,QAAQ,mBAAK;AAAA,IACd,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKS,cAAc,SAA0B;AAChD,WAAO,YAAY,MAAM,aAAa,KAAK,YAAY,sBAAK,wDAAL;AAAA,EACxD;AACD;AA9KC;AACA;AACA;AAHM;AA2GN,qBAAgB,WAAG;AAClB,QAAM,cAAc,+BAA+B,mBAAK,QAAO,IAAI;AACnE,QAAM,eAAe,IAAI,WAAW,YAAY,SAAS,CAAC;AAC1D,eAAa,CAAC,IAAI,KAAK,KAAK;AAC5B,eAAa,IAAI,aAAa,CAAC;AAC/B,aAAO;AAAA,QACN,6BAAW,wBAAQ,cAAc,EAAE,OAAO,GAAG,CAAC,CAAC,EAAE,MAAM,GAAG,sCAAqB,CAAC;AAAA,EACjF;AACD;AAnHM,IAAM,0BAAN;AAkLA,SAAS,0BACf,aACA,KACA,SAC0B;AAE1B,QAAM,4BAA4B,SAAS,oBACxC,gCAAiB,aAAa,EAAE,QAChC,sCAAuB,aAAa,EAAE;AAEzC,QAAM,WAAW,IAAI,YAAY,EAAE,WAAO,sCAAuB,GAAG,CAAC;AACrE,QAAM,MAAM,IAAI,WAAW,IAAI,SAAS,SAAS,0BAA0B,MAAM;AACjF,MAAI,IAAI,CAAC,SAAS,MAAM,GAAG,CAAC;AAC5B,MAAI,IAAI,UAAU,CAAC;AACnB,MAAI,IAAI,2BAA2B,IAAI,SAAS,MAAM;AACtD,SAAO,IAAI,wBAAwB,KAAK,OAAO;AAChD;AAEA,SAAS,+BAA+B,OAAmB,gBAAgB,OAAO;AACjF,QAAM,gBAAgB,MAAM,CAAC,IAAI;AACjC,QAAM,cAAc,OAAO,SAAK,kBAAM,MAAM,MAAM,aAAa,CAAC,CAAC,EAAE;AACnE,QAAM,YAAY,oBACf,gCAAiB,aAAa,EAAE,QAChC,sCAAuB,aAAa,EAAE;AACzC,QAAM,OAAO,IAAI,WAAW,gBAAgB,UAAU,MAAM;AAC5D,OAAK,IAAI,MAAM,MAAM,GAAG,aAAa,GAAG,CAAC;AACzC,OAAK,IAAI,WAAW,aAAa;AACjC,SAAO;AACR;AAEA,eAAe,8BAA8B;AAAA,EAC5C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,SAAS,IAAI,+BAAiB;AAAA,IAC7B,KAAK;AAAA,EACN,CAAC;AACF,GAMG;AACF,QAAM,OAAO,MAAM,OAAO,KAAK,uBAAuB;AAAA,IACrD;AAAA,IACA;AAAA,IACA;AAAA,IACA,QAAQ;AAAA,EACT,CAAC;AAED,SAAO,KAAK,YAAY,QAAQ,KAAK,OAAO,WAAW;AACxD;AAEO,SAAS,gCAAgC,WAAgC;AAC/E,QAAM,QAAQ,OAAO,cAAc,eAAW,uBAAW,SAAS,IAAI;AAEtE,MAAI,MAAM,CAAC,MAAM,iDAAyB,SAAS;AAClD,UAAM,IAAI,MAAM,0BAA0B;AAAA,EAC3C;AAEA,QAAM,iBAAiB,MAAM,MAAM,CAAC;AACpC,QAAM,EAAE,QAAQ,UAAU,cAAc,QAAI,wCAAsB,cAAc;AAChF,QAAM,EAAE,kBAAkB,YAAY,IAAI;AAC1C,QAAM,UAAM,oCAA0B,kBAAkB,KAAK;AAC7D,QAAM,kBAAkB,0BAA0B,OAAO,WAAW,GAAG,GAAG;AAC1E,SAAO;AAAA,IACN,yBAAqB,qBAAS,KAAK;AAAA,IACnC,iBAAiB;AAAA,IACjB,SAAS;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,aAAa,OAAO,WAAW;AAAA,IAChC;AAAA,IACA,WAAW;AAAA,IACX,WAAW,gBAAgB,WAAW;AAAA,EACvC;AACD;", "names": ["import_utils"]}