{"version": 3, "sources": ["../../../src/zklogin/jwt-utils.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { JwtPayload } from './jwt-decode.js';\nimport { jwtDecode } from './jwt-decode.js';\nimport { normalizeZk<PERSON><PERSON>inIssuer } from './utils.js';\n\nfunction base64UrlCharTo6Bits(base64UrlChar: string): number[] {\n\tif (base64UrlChar.length !== 1) {\n\t\tthrow new Error('Invalid base64Url character: ' + base64UrlChar);\n\t}\n\n\t// Define the base64URL character set\n\tconst base64UrlCharacterSet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_';\n\n\t// Find the index of the input character in the base64URL character set\n\tconst index = base64UrlCharacterSet.indexOf(base64UrlChar);\n\n\tif (index === -1) {\n\t\tthrow new Error('Invalid base64Url character: ' + base64UrlChar);\n\t}\n\n\t// Convert the index to a 6-bit binary string\n\tconst binaryString = index.toString(2).padStart(6, '0');\n\n\t// Convert the binary string to an array of bits\n\tconst bits = Array.from(binaryString).map(Number);\n\n\treturn bits;\n}\n\nfunction base64UrlStringToBitVector(base64UrlString: string) {\n\tlet bitVector: number[] = [];\n\tfor (let i = 0; i < base64UrlString.length; i++) {\n\t\tconst base64UrlChar = base64UrlString.charAt(i);\n\t\tconst bits = base64UrlCharTo6Bits(base64UrlChar);\n\t\tbitVector = bitVector.concat(bits);\n\t}\n\treturn bitVector;\n}\n\nfunction decodeBase64URL(s: string, i: number): string {\n\tif (s.length < 2) {\n\t\tthrow new Error(`Input (s = ${s}) is not tightly packed because s.length < 2`);\n\t}\n\tlet bits = base64UrlStringToBitVector(s);\n\n\tconst firstCharOffset = i % 4;\n\tif (firstCharOffset === 0) {\n\t\t// skip\n\t} else if (firstCharOffset === 1) {\n\t\tbits = bits.slice(2);\n\t} else if (firstCharOffset === 2) {\n\t\tbits = bits.slice(4);\n\t} else {\n\t\t// (offset == 3)\n\t\tthrow new Error(`Input (s = ${s}) is not tightly packed because i%4 = 3 (i = ${i}))`);\n\t}\n\n\tconst lastCharOffset = (i + s.length - 1) % 4;\n\tif (lastCharOffset === 3) {\n\t\t// skip\n\t} else if (lastCharOffset === 2) {\n\t\tbits = bits.slice(0, bits.length - 2);\n\t} else if (lastCharOffset === 1) {\n\t\tbits = bits.slice(0, bits.length - 4);\n\t} else {\n\t\t// (offset == 0)\n\t\tthrow new Error(\n\t\t\t`Input (s = ${s}) is not tightly packed because (i + s.length - 1)%4 = 0 (i = ${i}))`,\n\t\t);\n\t}\n\n\tif (bits.length % 8 !== 0) {\n\t\tthrow new Error(`We should never reach here...`);\n\t}\n\n\tconst bytes = new Uint8Array(Math.floor(bits.length / 8));\n\tlet currentByteIndex = 0;\n\tfor (let i = 0; i < bits.length; i += 8) {\n\t\tconst bitChunk = bits.slice(i, i + 8);\n\n\t\t// Convert the 8-bit chunk to a byte and add it to the bytes array\n\t\tconst byte = parseInt(bitChunk.join(''), 2);\n\t\tbytes[currentByteIndex++] = byte;\n\t}\n\treturn new TextDecoder().decode(bytes);\n}\n\nfunction verifyExtendedClaim(claim: string) {\n\t// Last character of each extracted_claim must be '}' or ','\n\tif (!(claim.slice(-1) === '}' || claim.slice(-1) === ',')) {\n\t\tthrow new Error('Invalid claim');\n\t}\n\n\t// A hack to parse the JSON key-value pair.. but it should work\n\tconst json = JSON.parse('{' + claim.slice(0, -1) + '}');\n\tif (Object.keys(json).length !== 1) {\n\t\tthrow new Error('Invalid claim');\n\t}\n\tconst key = Object.keys(json)[0];\n\treturn [key, json[key]];\n}\n\nexport type Claim = {\n\tvalue: string;\n\tindexMod4: number;\n};\n\nexport function extractClaimValue<R>(claim: Claim, claimName: string): R {\n\tconst extendedClaim = decodeBase64URL(claim.value, claim.indexMod4);\n\tconst [name, value] = verifyExtendedClaim(extendedClaim);\n\tif (name !== claimName) {\n\t\tthrow new Error(`Invalid field name: found ${name} expected ${claimName}`);\n\t}\n\treturn value;\n}\n\nexport function decodeJwt(jwt: string): Omit<JwtPayload, 'iss' | 'aud' | 'sub'> & {\n\tiss: string;\n\taud: string;\n\tsub: string;\n\trawIss: string;\n} {\n\tconst { iss, aud, sub, ...decodedJWT } = jwtDecode(jwt);\n\n\tif (!sub || !iss || !aud) {\n\t\tthrow new Error('Missing jwt data');\n\t}\n\n\tif (Array.isArray(aud)) {\n\t\tthrow new Error('Not supported aud. Aud is an array, string was expected.');\n\t}\n\n\treturn {\n\t\t...decodedJWT,\n\t\tiss: normalizeZkLoginIssuer(iss),\n\t\trawIss: iss,\n\t\taud,\n\t\tsub,\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,wBAA0B;AAC1B,mBAAuC;AAEvC,SAAS,qBAAqB,eAAiC;AAC9D,MAAI,cAAc,WAAW,GAAG;AAC/B,UAAM,IAAI,MAAM,kCAAkC,aAAa;AAAA,EAChE;AAGA,QAAM,wBAAwB;AAG9B,QAAM,QAAQ,sBAAsB,QAAQ,aAAa;AAEzD,MAAI,UAAU,IAAI;AACjB,UAAM,IAAI,MAAM,kCAAkC,aAAa;AAAA,EAChE;AAGA,QAAM,eAAe,MAAM,SAAS,CAAC,EAAE,SAAS,GAAG,GAAG;AAGtD,QAAM,OAAO,MAAM,KAAK,YAAY,EAAE,IAAI,MAAM;AAEhD,SAAO;AACR;AAEA,SAAS,2BAA2B,iBAAyB;AAC5D,MAAI,YAAsB,CAAC;AAC3B,WAAS,IAAI,GAAG,IAAI,gBAAgB,QAAQ,KAAK;AAChD,UAAM,gBAAgB,gBAAgB,OAAO,CAAC;AAC9C,UAAM,OAAO,qBAAqB,aAAa;AAC/C,gBAAY,UAAU,OAAO,IAAI;AAAA,EAClC;AACA,SAAO;AACR;AAEA,SAAS,gBAAgB,GAAW,GAAmB;AACtD,MAAI,EAAE,SAAS,GAAG;AACjB,UAAM,IAAI,MAAM,cAAc,CAAC,8CAA8C;AAAA,EAC9E;AACA,MAAI,OAAO,2BAA2B,CAAC;AAEvC,QAAM,kBAAkB,IAAI;AAC5B,MAAI,oBAAoB,GAAG;AAAA,EAE3B,WAAW,oBAAoB,GAAG;AACjC,WAAO,KAAK,MAAM,CAAC;AAAA,EACpB,WAAW,oBAAoB,GAAG;AACjC,WAAO,KAAK,MAAM,CAAC;AAAA,EACpB,OAAO;AAEN,UAAM,IAAI,MAAM,cAAc,CAAC,gDAAgD,CAAC,IAAI;AAAA,EACrF;AAEA,QAAM,kBAAkB,IAAI,EAAE,SAAS,KAAK;AAC5C,MAAI,mBAAmB,GAAG;AAAA,EAE1B,WAAW,mBAAmB,GAAG;AAChC,WAAO,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC;AAAA,EACrC,WAAW,mBAAmB,GAAG;AAChC,WAAO,KAAK,MAAM,GAAG,KAAK,SAAS,CAAC;AAAA,EACrC,OAAO;AAEN,UAAM,IAAI;AAAA,MACT,cAAc,CAAC,iEAAiE,CAAC;AAAA,IAClF;AAAA,EACD;AAEA,MAAI,KAAK,SAAS,MAAM,GAAG;AAC1B,UAAM,IAAI,MAAM,+BAA+B;AAAA,EAChD;AAEA,QAAM,QAAQ,IAAI,WAAW,KAAK,MAAM,KAAK,SAAS,CAAC,CAAC;AACxD,MAAI,mBAAmB;AACvB,WAASA,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK,GAAG;AACxC,UAAM,WAAW,KAAK,MAAMA,IAAGA,KAAI,CAAC;AAGpC,UAAM,OAAO,SAAS,SAAS,KAAK,EAAE,GAAG,CAAC;AAC1C,UAAM,kBAAkB,IAAI;AAAA,EAC7B;AACA,SAAO,IAAI,YAAY,EAAE,OAAO,KAAK;AACtC;AAEA,SAAS,oBAAoB,OAAe;AAE3C,MAAI,EAAE,MAAM,MAAM,EAAE,MAAM,OAAO,MAAM,MAAM,EAAE,MAAM,MAAM;AAC1D,UAAM,IAAI,MAAM,eAAe;AAAA,EAChC;AAGA,QAAM,OAAO,KAAK,MAAM,MAAM,MAAM,MAAM,GAAG,EAAE,IAAI,GAAG;AACtD,MAAI,OAAO,KAAK,IAAI,EAAE,WAAW,GAAG;AACnC,UAAM,IAAI,MAAM,eAAe;AAAA,EAChC;AACA,QAAM,MAAM,OAAO,KAAK,IAAI,EAAE,CAAC;AAC/B,SAAO,CAAC,KAAK,KAAK,GAAG,CAAC;AACvB;AAOO,SAAS,kBAAqB,OAAc,WAAsB;AACxE,QAAM,gBAAgB,gBAAgB,MAAM,OAAO,MAAM,SAAS;AAClE,QAAM,CAAC,MAAM,KAAK,IAAI,oBAAoB,aAAa;AACvD,MAAI,SAAS,WAAW;AACvB,UAAM,IAAI,MAAM,6BAA6B,IAAI,aAAa,SAAS,EAAE;AAAA,EAC1E;AACA,SAAO;AACR;AAEO,SAAS,UAAU,KAKxB;AACD,QAAM,EAAE,KAAK,KAAK,KAAK,GAAG,WAAW,QAAI,6BAAU,GAAG;AAEtD,MAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK;AACzB,UAAM,IAAI,MAAM,kBAAkB;AAAA,EACnC;AAEA,MAAI,MAAM,QAAQ,GAAG,GAAG;AACvB,UAAM,IAAI,MAAM,0DAA0D;AAAA,EAC3E;AAEA,SAAO;AAAA,IACN,GAAG;AAAA,IACH,SAAK,qCAAuB,GAAG;AAAA,IAC/B,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,EACD;AACD;", "names": ["i"]}