{"version": 3, "sources": ["../../../src/experimental/mvr.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { chunk, DataLoader } from '@mysten/utils';\nimport { isValidNamedPackage, isValidNamedType } from '../utils/move-registry.js';\nimport type { StructTag } from '../utils/sui-types.js';\nimport {\n\tisValidSuiAddress,\n\tnormalizeStructTag,\n\tnormalizeSuiAddress,\n\tparseStructTag,\n} from '../utils/sui-types.js';\nimport type { ClientCache } from './cache.js';\nimport type { TransactionDataBuilder } from '../transactions/TransactionData.js';\nimport { PACKAGE_VERSION } from '../version.js';\nimport type { Experimental_SuiClientTypes } from './types.js';\n\nconst NAME_SEPARATOR = '/';\nconst MVR_API_HEADER = {\n\t'Mvr-Source': `@mysten/sui@${PACKAGE_VERSION}`,\n};\n\nexport interface MvrClientOptions {\n\tcache: ClientCache;\n\turl?: string;\n\tpageSize?: number;\n\toverrides?: {\n\t\tpackages?: Record<string, string>;\n\t\ttypes?: Record<string, string>;\n\t};\n}\n\nexport class MvrClient implements Experimental_SuiClientTypes.MvrMethods {\n\t#cache: ClientCache;\n\t#url?: string;\n\t#pageSize: number;\n\t#overrides: {\n\t\tpackages?: Record<string, string>;\n\t\ttypes?: Record<string, string>;\n\t};\n\n\tconstructor({ cache, url, pageSize = 50, overrides }: MvrClientOptions) {\n\t\tthis.#cache = cache;\n\t\tthis.#url = url;\n\t\tthis.#pageSize = pageSize;\n\t\tthis.#overrides = {\n\t\t\tpackages: overrides?.packages,\n\t\t\ttypes: overrides?.types,\n\t\t};\n\n\t\tvalidateOverrides(this.#overrides);\n\t}\n\n\tget #mvrPackageDataLoader() {\n\t\treturn this.#cache.readSync(['#mvrPackageDataLoader', this.#url ?? ''], () => {\n\t\t\tconst loader = new DataLoader<string, string>(async (packages) => {\n\t\t\t\tif (!this.#url) {\n\t\t\t\t\tthrow new Error(\n\t\t\t\t\t\t`MVR Api URL is not set for the current client (resolving ${packages.join(', ')})`,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\tconst resolved = await this.#resolvePackages(packages);\n\n\t\t\t\treturn packages.map(\n\t\t\t\t\t(pkg) => resolved[pkg] ?? new Error(`Failed to resolve package: ${pkg}`),\n\t\t\t\t);\n\t\t\t});\n\t\t\tconst overrides = this.#overrides?.packages;\n\n\t\t\tif (overrides) {\n\t\t\t\tfor (const [pkg, id] of Object.entries(overrides)) {\n\t\t\t\t\tloader.prime(pkg, id);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn loader;\n\t\t});\n\t}\n\n\tget #mvrTypeDataLoader() {\n\t\treturn this.#cache.readSync(['#mvrTypeDataLoader', this.#url ?? ''], () => {\n\t\t\tconst loader = new DataLoader<string, string>(async (types) => {\n\t\t\t\tif (!this.#url) {\n\t\t\t\t\tthrow new Error(\n\t\t\t\t\t\t`MVR Api URL is not set for the current client (resolving ${types.join(', ')})`,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t\tconst resolved = await this.#resolveTypes(types);\n\n\t\t\t\treturn types.map((type) => resolved[type] ?? new Error(`Failed to resolve type: ${type}`));\n\t\t\t});\n\n\t\t\tconst overrides = this.#overrides?.types;\n\n\t\t\tif (overrides) {\n\t\t\t\tfor (const [type, id] of Object.entries(overrides)) {\n\t\t\t\t\tloader.prime(type, id);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn loader;\n\t\t});\n\t}\n\n\tasync #resolvePackages(packages: readonly string[]) {\n\t\tif (packages.length === 0) return {};\n\n\t\tconst batches = chunk(packages, this.#pageSize);\n\t\tconst results: Record<string, string> = {};\n\n\t\tawait Promise.all(\n\t\t\tbatches.map(async (batch) => {\n\t\t\t\tconst data = await this.#fetch<{ resolution: Record<string, { package_id: string }> }>(\n\t\t\t\t\t'/v1/resolution/bulk',\n\t\t\t\t\t{\n\t\t\t\t\t\tnames: batch,\n\t\t\t\t\t},\n\t\t\t\t);\n\n\t\t\t\tif (!data?.resolution) return;\n\n\t\t\t\tfor (const pkg of Object.keys(data?.resolution)) {\n\t\t\t\t\tconst pkgData = data.resolution[pkg]?.package_id;\n\n\t\t\t\t\tif (!pkgData) continue;\n\n\t\t\t\t\tresults[pkg] = pkgData;\n\t\t\t\t}\n\t\t\t}),\n\t\t);\n\n\t\treturn results;\n\t}\n\n\tasync #resolveTypes(types: readonly string[]) {\n\t\tif (types.length === 0) return {};\n\n\t\tconst batches = chunk(types, this.#pageSize);\n\t\tconst results: Record<string, string> = {};\n\n\t\tawait Promise.all(\n\t\t\tbatches.map(async (batch) => {\n\t\t\t\tconst data = await this.#fetch<{ resolution: Record<string, { type_tag: string }> }>(\n\t\t\t\t\t'/v1/struct-definition/bulk',\n\t\t\t\t\t{\n\t\t\t\t\t\ttypes: batch,\n\t\t\t\t\t},\n\t\t\t\t);\n\n\t\t\t\tif (!data?.resolution) return;\n\n\t\t\t\tfor (const type of Object.keys(data?.resolution)) {\n\t\t\t\t\tconst typeData = data.resolution[type]?.type_tag;\n\t\t\t\t\tif (!typeData) continue;\n\n\t\t\t\t\tresults[type] = typeData;\n\t\t\t\t}\n\t\t\t}),\n\t\t);\n\n\t\treturn results;\n\t}\n\n\tasync #fetch<T>(url: string, body: Record<string, unknown>): Promise<T> {\n\t\tif (!this.#url) {\n\t\t\tthrow new Error('MVR Api URL is not set for the current client');\n\t\t}\n\n\t\tconst response = await fetch(`${this.#url}${url}`, {\n\t\t\tmethod: 'POST',\n\t\t\theaders: {\n\t\t\t\t'Content-Type': 'application/json',\n\t\t\t\t...MVR_API_HEADER,\n\t\t\t},\n\t\t\tbody: JSON.stringify(body),\n\t\t});\n\n\t\tif (!response.ok) {\n\t\t\tconst errorBody = await response.json().catch(() => ({}));\n\t\t\tthrow new Error(`Failed to resolve types: ${errorBody?.message}`);\n\t\t}\n\n\t\treturn response.json();\n\t}\n\n\tasync resolvePackage({\n\t\tpackage: name,\n\t}: Experimental_SuiClientTypes.MvrResolvePackageOptions): Promise<Experimental_SuiClientTypes.MvrResolvePackageResponse> {\n\t\tconst resolved = await this.#mvrPackageDataLoader.load(name);\n\t\treturn {\n\t\t\tpackage: resolved,\n\t\t};\n\t}\n\n\tasync resolveType({\n\t\ttype,\n\t}: Experimental_SuiClientTypes.MvrResolveTypeOptions): Promise<Experimental_SuiClientTypes.MvrResolveTypeResponse> {\n\t\tconst mvrTypes = [...extractMvrTypes(type)];\n\t\tconst resolvedTypes = await this.#mvrTypeDataLoader.loadMany(mvrTypes);\n\n\t\tconst typeMap: Record<string, string> = {};\n\n\t\tfor (let i = 0; i < mvrTypes.length; i++) {\n\t\t\tconst resolvedType = resolvedTypes[i];\n\t\t\tif (resolvedType instanceof Error) {\n\t\t\t\tthrow resolvedType;\n\t\t\t}\n\t\t\ttypeMap[mvrTypes[i]] = resolvedType;\n\t\t}\n\n\t\treturn {\n\t\t\ttype: replaceMvrNames(type, typeMap),\n\t\t};\n\t}\n\n\tasync resolve({\n\t\ttypes = [],\n\t\tpackages = [],\n\t}: Experimental_SuiClientTypes.MvrResolveOptions): Promise<Experimental_SuiClientTypes.MvrResolveResponse> {\n\t\tconst mvrTypes = new Set<string>();\n\n\t\tfor (const type of types ?? []) {\n\t\t\textractMvrTypes(type, mvrTypes);\n\t\t}\n\n\t\tconst typesArray = [...mvrTypes];\n\t\tconst [resolvedTypes, resolvedPackages] = await Promise.all([\n\t\t\ttypesArray.length > 0 ? this.#mvrTypeDataLoader.loadMany(typesArray) : [],\n\t\t\tpackages.length > 0 ? this.#mvrPackageDataLoader.loadMany(packages) : [],\n\t\t]);\n\n\t\tconst typeMap: Record<string, string> = {\n\t\t\t...this.#overrides?.types,\n\t\t};\n\n\t\tfor (const [i, type] of typesArray.entries()) {\n\t\t\tconst resolvedType = resolvedTypes[i];\n\t\t\tif (resolvedType instanceof Error) {\n\t\t\t\tthrow resolvedType;\n\t\t\t}\n\t\t\ttypeMap[type] = resolvedType;\n\t\t}\n\n\t\tconst replacedTypes: Record<\n\t\t\tstring,\n\t\t\t{\n\t\t\t\ttype: string;\n\t\t\t}\n\t\t> = {};\n\n\t\tfor (const type of types ?? []) {\n\t\t\tconst resolvedType = replaceMvrNames(type, typeMap);\n\n\t\t\treplacedTypes[type] = {\n\t\t\t\ttype: resolvedType,\n\t\t\t};\n\t\t}\n\n\t\tconst replacedPackages: Record<\n\t\t\tstring,\n\t\t\t{\n\t\t\t\tpackage: string;\n\t\t\t}\n\t\t> = {};\n\n\t\tfor (const [i, pkg] of (packages ?? []).entries()) {\n\t\t\tconst resolvedPkg = this.#overrides?.packages?.[pkg] ?? resolvedPackages[i];\n\n\t\t\tif (resolvedPkg instanceof Error) {\n\t\t\t\tthrow resolvedPkg;\n\t\t\t}\n\n\t\t\treplacedPackages[pkg] = {\n\t\t\t\tpackage: resolvedPkg,\n\t\t\t};\n\t\t}\n\n\t\treturn {\n\t\t\ttypes: replacedTypes,\n\t\t\tpackages: replacedPackages,\n\t\t};\n\t}\n}\n\nfunction validateOverrides(overrides?: {\n\tpackages?: Record<string, string>;\n\ttypes?: Record<string, string>;\n}) {\n\tif (overrides?.packages) {\n\t\tfor (const [pkg, id] of Object.entries(overrides.packages)) {\n\t\t\tif (!isValidNamedPackage(pkg)) {\n\t\t\t\tthrow new Error(`Invalid package name: ${pkg}`);\n\t\t\t}\n\t\t\tif (!isValidSuiAddress(normalizeSuiAddress(id))) {\n\t\t\t\tthrow new Error(`Invalid package ID: ${id}`);\n\t\t\t}\n\t\t}\n\t}\n\n\tif (overrides?.types) {\n\t\tfor (const [type, val] of Object.entries(overrides.types)) {\n\t\t\t// validate that types are first-level only.\n\t\t\tif (parseStructTag(type).typeParams.length > 0) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t'Type overrides must be first-level only. If you want to supply generic types, just pass each type individually.',\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tconst parsedValue = parseStructTag(val);\n\n\t\t\tif (!isValidSuiAddress(parsedValue.address)) {\n\t\t\t\tthrow new Error(`Invalid type: ${val}`);\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * Extracts all named types from a given type.\n */\nexport function extractMvrTypes(type: string | StructTag, types = new Set<string>()) {\n\tif (typeof type === 'string' && !hasMvrName(type)) return types;\n\n\tconst tag = isStructTag(type) ? type : parseStructTag(type);\n\n\tif (hasMvrName(tag.address)) types.add(`${tag.address}::${tag.module}::${tag.name}`);\n\n\tfor (const param of tag.typeParams) {\n\t\textractMvrTypes(param, types);\n\t}\n\n\treturn types;\n}\n\n/**\n * Traverses a type, and replaces any found names with their resolved equivalents,\n * based on the supplied type cache.\n */\nfunction replaceMvrNames(tag: string | StructTag, typeCache: Record<string, string>): string {\n\tconst type = isStructTag(tag) ? tag : parseStructTag(tag);\n\n\tconst typeTag = `${type.address}::${type.module}::${type.name}`;\n\tconst cacheHit = typeCache[typeTag];\n\n\treturn normalizeStructTag({\n\t\t...type,\n\t\taddress: cacheHit ? cacheHit.split('::')[0] : type.address,\n\t\ttypeParams: type.typeParams.map((param) => replaceMvrNames(param, typeCache)),\n\t});\n}\n\nexport function hasMvrName(nameOrType: string) {\n\treturn (\n\t\tnameOrType.includes(NAME_SEPARATOR) || nameOrType.includes('@') || nameOrType.includes('.sui')\n\t);\n}\n\nfunction isStructTag(type: string | StructTag): type is StructTag {\n\treturn (\n\t\ttypeof type === 'object' &&\n\t\t'address' in type &&\n\t\t'module' in type &&\n\t\t'name' in type &&\n\t\t'typeParams' in type\n\t);\n}\n\nexport type NamedPackagesOverrides = {\n\tpackages: Record<string, string>;\n\ttypes: Record<string, string>;\n};\n\n/**\n * Looks up all `.move` names in a transaction block.\n * Returns a list of all the names found.\n */\nexport function findNamesInTransaction(builder: TransactionDataBuilder): {\n\tpackages: string[];\n\ttypes: string[];\n} {\n\tconst packages: Set<string> = new Set();\n\tconst types: Set<string> = new Set();\n\n\tfor (const command of builder.commands) {\n\t\tswitch (command.$kind) {\n\t\t\tcase 'MakeMoveVec':\n\t\t\t\tif (command.MakeMoveVec.type) {\n\t\t\t\t\tgetNamesFromTypeList([command.MakeMoveVec.type]).forEach((type) => {\n\t\t\t\t\t\ttypes.add(type);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'MoveCall':\n\t\t\t\tconst moveCall = command.MoveCall;\n\n\t\t\t\tconst pkg = moveCall.package.split('::')[0];\n\t\t\t\tif (hasMvrName(pkg)) {\n\t\t\t\t\tif (!isValidNamedPackage(pkg)) throw new Error(`Invalid package name: ${pkg}`);\n\t\t\t\t\tpackages.add(pkg);\n\t\t\t\t}\n\n\t\t\t\tgetNamesFromTypeList(moveCall.typeArguments ?? []).forEach((type) => {\n\t\t\t\t\ttypes.add(type);\n\t\t\t\t});\n\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tbreak;\n\t\t}\n\t}\n\n\treturn {\n\t\tpackages: [...packages],\n\t\ttypes: [...types],\n\t};\n}\n\n/**\n * Replace all names & types in a transaction block\n * with their resolved names/types.\n */\nexport function replaceNames(\n\tbuilder: TransactionDataBuilder,\n\tresolved: Experimental_SuiClientTypes.MvrResolveResponse,\n) {\n\tfor (const command of builder.commands) {\n\t\t// Replacements for `MakeMoveVec` commands (that can include types)\n\t\tif (command.MakeMoveVec?.type) {\n\t\t\tif (!hasMvrName(command.MakeMoveVec.type)) continue;\n\t\t\tif (!resolved.types[command.MakeMoveVec.type])\n\t\t\t\tthrow new Error(`No resolution found for type: ${command.MakeMoveVec.type}`);\n\t\t\tcommand.MakeMoveVec.type = resolved.types[command.MakeMoveVec.type].type;\n\t\t}\n\t\t// Replacements for `MoveCall` commands (that can include packages & types)\n\t\tconst tx = command.MoveCall;\n\t\tif (!tx) continue;\n\n\t\tconst nameParts = tx.package.split('::');\n\t\tconst name = nameParts[0];\n\n\t\tif (hasMvrName(name) && !resolved.packages[name])\n\t\t\tthrow new Error(`No address found for package: ${name}`);\n\n\t\t// Replace package name with address.\n\t\tif (hasMvrName(name)) {\n\t\t\tnameParts[0] = resolved.packages[name].package;\n\t\t\ttx.package = nameParts.join('::');\n\t\t}\n\n\t\tconst types = tx.typeArguments;\n\t\tif (!types) continue;\n\n\t\tfor (let i = 0; i < types.length; i++) {\n\t\t\tif (!hasMvrName(types[i])) continue;\n\n\t\t\tif (!resolved.types[types[i]]) throw new Error(`No resolution found for type: ${types[i]}`);\n\t\t\ttypes[i] = resolved.types[types[i]].type;\n\t\t}\n\n\t\ttx.typeArguments = types;\n\t}\n}\n\n/**\n * Returns a list of unique types that include a name\n * from the given list. This list is retrieved from the Transaction Data.\n */\nfunction getNamesFromTypeList(types: string[]) {\n\tconst names = new Set<string>();\n\tfor (const type of types) {\n\t\tif (hasMvrName(type)) {\n\t\t\tif (!isValidNamedType(type)) throw new Error(`Invalid type with names: ${type}`);\n\t\t\tnames.add(type);\n\t\t}\n\t}\n\treturn names;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,mBAAkC;AAClC,2BAAsD;AAEtD,uBAKO;AAGP,qBAAgC;AAdhC;AAiBA,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AAAA,EACtB,cAAc,eAAe,8BAAe;AAC7C;AAYO,MAAM,UAA4D;AAAA,EASxE,YAAY,EAAE,OAAO,KAAK,WAAW,IAAI,UAAU,GAAqB;AATlE;AACN;AACA;AACA;AACA;AAMC,uBAAK,QAAS;AACd,uBAAK,MAAO;AACZ,uBAAK,WAAY;AACjB,uBAAK,YAAa;AAAA,MACjB,UAAU,WAAW;AAAA,MACrB,OAAO,WAAW;AAAA,IACnB;AAEA,sBAAkB,mBAAK,WAAU;AAAA,EAClC;AAAA,EAsIA,MAAM,eAAe;AAAA,IACpB,SAAS;AAAA,EACV,GAAyH;AACxH,UAAM,WAAW,MAAM,mBAAK,gDAAsB,KAAK,IAAI;AAC3D,WAAO;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACD;AAAA,EAEA,MAAM,YAAY;AAAA,IACjB;AAAA,EACD,GAAmH;AAClH,UAAM,WAAW,CAAC,GAAG,gBAAgB,IAAI,CAAC;AAC1C,UAAM,gBAAgB,MAAM,mBAAK,6CAAmB,SAAS,QAAQ;AAErE,UAAM,UAAkC,CAAC;AAEzC,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACzC,YAAM,eAAe,cAAc,CAAC;AACpC,UAAI,wBAAwB,OAAO;AAClC,cAAM;AAAA,MACP;AACA,cAAQ,SAAS,CAAC,CAAC,IAAI;AAAA,IACxB;AAEA,WAAO;AAAA,MACN,MAAM,gBAAgB,MAAM,OAAO;AAAA,IACpC;AAAA,EACD;AAAA,EAEA,MAAM,QAAQ;AAAA,IACb,QAAQ,CAAC;AAAA,IACT,WAAW,CAAC;AAAA,EACb,GAA2G;AAC1G,UAAM,WAAW,oBAAI,IAAY;AAEjC,eAAW,QAAQ,SAAS,CAAC,GAAG;AAC/B,sBAAgB,MAAM,QAAQ;AAAA,IAC/B;AAEA,UAAM,aAAa,CAAC,GAAG,QAAQ;AAC/B,UAAM,CAAC,eAAe,gBAAgB,IAAI,MAAM,QAAQ,IAAI;AAAA,MAC3D,WAAW,SAAS,IAAI,mBAAK,6CAAmB,SAAS,UAAU,IAAI,CAAC;AAAA,MACxE,SAAS,SAAS,IAAI,mBAAK,gDAAsB,SAAS,QAAQ,IAAI,CAAC;AAAA,IACxE,CAAC;AAED,UAAM,UAAkC;AAAA,MACvC,GAAG,mBAAK,aAAY;AAAA,IACrB;AAEA,eAAW,CAAC,GAAG,IAAI,KAAK,WAAW,QAAQ,GAAG;AAC7C,YAAM,eAAe,cAAc,CAAC;AACpC,UAAI,wBAAwB,OAAO;AAClC,cAAM;AAAA,MACP;AACA,cAAQ,IAAI,IAAI;AAAA,IACjB;AAEA,UAAM,gBAKF,CAAC;AAEL,eAAW,QAAQ,SAAS,CAAC,GAAG;AAC/B,YAAM,eAAe,gBAAgB,MAAM,OAAO;AAElD,oBAAc,IAAI,IAAI;AAAA,QACrB,MAAM;AAAA,MACP;AAAA,IACD;AAEA,UAAM,mBAKF,CAAC;AAEL,eAAW,CAAC,GAAG,GAAG,MAAM,YAAY,CAAC,GAAG,QAAQ,GAAG;AAClD,YAAM,cAAc,mBAAK,aAAY,WAAW,GAAG,KAAK,iBAAiB,CAAC;AAE1E,UAAI,uBAAuB,OAAO;AACjC,cAAM;AAAA,MACP;AAEA,uBAAiB,GAAG,IAAI;AAAA,QACvB,SAAS;AAAA,MACV;AAAA,IACD;AAEA,WAAO;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,IACX;AAAA,EACD;AACD;AAzPC;AACA;AACA;AACA;AAJM;AAqBF,2BAAqB,WAAG;AAC3B,SAAO,mBAAK,QAAO,SAAS,CAAC,yBAAyB,mBAAK,SAAQ,EAAE,GAAG,MAAM;AAC7E,UAAM,SAAS,IAAI,wBAA2B,OAAO,aAAa;AACjE,UAAI,CAAC,mBAAK,OAAM;AACf,cAAM,IAAI;AAAA,UACT,4DAA4D,SAAS,KAAK,IAAI,CAAC;AAAA,QAChF;AAAA,MACD;AACA,YAAM,WAAW,MAAM,sBAAK,0CAAL,WAAsB;AAE7C,aAAO,SAAS;AAAA,QACf,CAAC,QAAQ,SAAS,GAAG,KAAK,IAAI,MAAM,8BAA8B,GAAG,EAAE;AAAA,MACxE;AAAA,IACD,CAAC;AACD,UAAM,YAAY,mBAAK,aAAY;AAEnC,QAAI,WAAW;AACd,iBAAW,CAAC,KAAK,EAAE,KAAK,OAAO,QAAQ,SAAS,GAAG;AAClD,eAAO,MAAM,KAAK,EAAE;AAAA,MACrB;AAAA,IACD;AAEA,WAAO;AAAA,EACR,CAAC;AACF;AAEI,wBAAkB,WAAG;AACxB,SAAO,mBAAK,QAAO,SAAS,CAAC,sBAAsB,mBAAK,SAAQ,EAAE,GAAG,MAAM;AAC1E,UAAM,SAAS,IAAI,wBAA2B,OAAO,UAAU;AAC9D,UAAI,CAAC,mBAAK,OAAM;AACf,cAAM,IAAI;AAAA,UACT,4DAA4D,MAAM,KAAK,IAAI,CAAC;AAAA,QAC7E;AAAA,MACD;AACA,YAAM,WAAW,MAAM,sBAAK,uCAAL,WAAmB;AAE1C,aAAO,MAAM,IAAI,CAAC,SAAS,SAAS,IAAI,KAAK,IAAI,MAAM,2BAA2B,IAAI,EAAE,CAAC;AAAA,IAC1F,CAAC;AAED,UAAM,YAAY,mBAAK,aAAY;AAEnC,QAAI,WAAW;AACd,iBAAW,CAAC,MAAM,EAAE,KAAK,OAAO,QAAQ,SAAS,GAAG;AACnD,eAAO,MAAM,MAAM,EAAE;AAAA,MACtB;AAAA,IACD;AAEA,WAAO;AAAA,EACR,CAAC;AACF;AAEM,qBAAgB,eAAC,UAA6B;AACnD,MAAI,SAAS,WAAW,EAAG,QAAO,CAAC;AAEnC,QAAM,cAAU,oBAAM,UAAU,mBAAK,UAAS;AAC9C,QAAM,UAAkC,CAAC;AAEzC,QAAM,QAAQ;AAAA,IACb,QAAQ,IAAI,OAAO,UAAU;AAC5B,YAAM,OAAO,MAAM,sBAAK,gCAAL,WAClB,uBACA;AAAA,QACC,OAAO;AAAA,MACR;AAGD,UAAI,CAAC,MAAM,WAAY;AAEvB,iBAAW,OAAO,OAAO,KAAK,MAAM,UAAU,GAAG;AAChD,cAAM,UAAU,KAAK,WAAW,GAAG,GAAG;AAEtC,YAAI,CAAC,QAAS;AAEd,gBAAQ,GAAG,IAAI;AAAA,MAChB;AAAA,IACD,CAAC;AAAA,EACF;AAEA,SAAO;AACR;AAEM,kBAAa,eAAC,OAA0B;AAC7C,MAAI,MAAM,WAAW,EAAG,QAAO,CAAC;AAEhC,QAAM,cAAU,oBAAM,OAAO,mBAAK,UAAS;AAC3C,QAAM,UAAkC,CAAC;AAEzC,QAAM,QAAQ;AAAA,IACb,QAAQ,IAAI,OAAO,UAAU;AAC5B,YAAM,OAAO,MAAM,sBAAK,gCAAL,WAClB,8BACA;AAAA,QACC,OAAO;AAAA,MACR;AAGD,UAAI,CAAC,MAAM,WAAY;AAEvB,iBAAW,QAAQ,OAAO,KAAK,MAAM,UAAU,GAAG;AACjD,cAAM,WAAW,KAAK,WAAW,IAAI,GAAG;AACxC,YAAI,CAAC,SAAU;AAEf,gBAAQ,IAAI,IAAI;AAAA,MACjB;AAAA,IACD,CAAC;AAAA,EACF;AAEA,SAAO;AACR;AAEM,WAAS,eAAC,KAAa,MAA2C;AACvE,MAAI,CAAC,mBAAK,OAAM;AACf,UAAM,IAAI,MAAM,+CAA+C;AAAA,EAChE;AAEA,QAAM,WAAW,MAAM,MAAM,GAAG,mBAAK,KAAI,GAAG,GAAG,IAAI;AAAA,IAClD,QAAQ;AAAA,IACR,SAAS;AAAA,MACR,gBAAgB;AAAA,MAChB,GAAG;AAAA,IACJ;AAAA,IACA,MAAM,KAAK,UAAU,IAAI;AAAA,EAC1B,CAAC;AAED,MAAI,CAAC,SAAS,IAAI;AACjB,UAAM,YAAY,MAAM,SAAS,KAAK,EAAE,MAAM,OAAO,CAAC,EAAE;AACxD,UAAM,IAAI,MAAM,4BAA4B,WAAW,OAAO,EAAE;AAAA,EACjE;AAEA,SAAO,SAAS,KAAK;AACtB;AAqGD,SAAS,kBAAkB,WAGxB;AACF,MAAI,WAAW,UAAU;AACxB,eAAW,CAAC,KAAK,EAAE,KAAK,OAAO,QAAQ,UAAU,QAAQ,GAAG;AAC3D,UAAI,KAAC,0CAAoB,GAAG,GAAG;AAC9B,cAAM,IAAI,MAAM,yBAAyB,GAAG,EAAE;AAAA,MAC/C;AACA,UAAI,KAAC,wCAAkB,sCAAoB,EAAE,CAAC,GAAG;AAChD,cAAM,IAAI,MAAM,uBAAuB,EAAE,EAAE;AAAA,MAC5C;AAAA,IACD;AAAA,EACD;AAEA,MAAI,WAAW,OAAO;AACrB,eAAW,CAAC,MAAM,GAAG,KAAK,OAAO,QAAQ,UAAU,KAAK,GAAG;AAE1D,cAAI,iCAAe,IAAI,EAAE,WAAW,SAAS,GAAG;AAC/C,cAAM,IAAI;AAAA,UACT;AAAA,QACD;AAAA,MACD;AAEA,YAAM,kBAAc,iCAAe,GAAG;AAEtC,UAAI,KAAC,oCAAkB,YAAY,OAAO,GAAG;AAC5C,cAAM,IAAI,MAAM,iBAAiB,GAAG,EAAE;AAAA,MACvC;AAAA,IACD;AAAA,EACD;AACD;AAKO,SAAS,gBAAgB,MAA0B,QAAQ,oBAAI,IAAY,GAAG;AACpF,MAAI,OAAO,SAAS,YAAY,CAAC,WAAW,IAAI,EAAG,QAAO;AAE1D,QAAM,MAAM,YAAY,IAAI,IAAI,WAAO,iCAAe,IAAI;AAE1D,MAAI,WAAW,IAAI,OAAO,EAAG,OAAM,IAAI,GAAG,IAAI,OAAO,KAAK,IAAI,MAAM,KAAK,IAAI,IAAI,EAAE;AAEnF,aAAW,SAAS,IAAI,YAAY;AACnC,oBAAgB,OAAO,KAAK;AAAA,EAC7B;AAEA,SAAO;AACR;AAMA,SAAS,gBAAgB,KAAyB,WAA2C;AAC5F,QAAM,OAAO,YAAY,GAAG,IAAI,UAAM,iCAAe,GAAG;AAExD,QAAM,UAAU,GAAG,KAAK,OAAO,KAAK,KAAK,MAAM,KAAK,KAAK,IAAI;AAC7D,QAAM,WAAW,UAAU,OAAO;AAElC,aAAO,qCAAmB;AAAA,IACzB,GAAG;AAAA,IACH,SAAS,WAAW,SAAS,MAAM,IAAI,EAAE,CAAC,IAAI,KAAK;AAAA,IACnD,YAAY,KAAK,WAAW,IAAI,CAAC,UAAU,gBAAgB,OAAO,SAAS,CAAC;AAAA,EAC7E,CAAC;AACF;AAEO,SAAS,WAAW,YAAoB;AAC9C,SACC,WAAW,SAAS,cAAc,KAAK,WAAW,SAAS,GAAG,KAAK,WAAW,SAAS,MAAM;AAE/F;AAEA,SAAS,YAAY,MAA6C;AACjE,SACC,OAAO,SAAS,YAChB,aAAa,QACb,YAAY,QACZ,UAAU,QACV,gBAAgB;AAElB;AAWO,SAAS,uBAAuB,SAGrC;AACD,QAAM,WAAwB,oBAAI,IAAI;AACtC,QAAM,QAAqB,oBAAI,IAAI;AAEnC,aAAW,WAAW,QAAQ,UAAU;AACvC,YAAQ,QAAQ,OAAO;AAAA,MACtB,KAAK;AACJ,YAAI,QAAQ,YAAY,MAAM;AAC7B,+BAAqB,CAAC,QAAQ,YAAY,IAAI,CAAC,EAAE,QAAQ,CAAC,SAAS;AAClE,kBAAM,IAAI,IAAI;AAAA,UACf,CAAC;AAAA,QACF;AACA;AAAA,MACD,KAAK;AACJ,cAAM,WAAW,QAAQ;AAEzB,cAAM,MAAM,SAAS,QAAQ,MAAM,IAAI,EAAE,CAAC;AAC1C,YAAI,WAAW,GAAG,GAAG;AACpB,cAAI,KAAC,0CAAoB,GAAG,EAAG,OAAM,IAAI,MAAM,yBAAyB,GAAG,EAAE;AAC7E,mBAAS,IAAI,GAAG;AAAA,QACjB;AAEA,6BAAqB,SAAS,iBAAiB,CAAC,CAAC,EAAE,QAAQ,CAAC,SAAS;AACpE,gBAAM,IAAI,IAAI;AAAA,QACf,CAAC;AAED;AAAA,MACD;AACC;AAAA,IACF;AAAA,EACD;AAEA,SAAO;AAAA,IACN,UAAU,CAAC,GAAG,QAAQ;AAAA,IACtB,OAAO,CAAC,GAAG,KAAK;AAAA,EACjB;AACD;AAMO,SAAS,aACf,SACA,UACC;AACD,aAAW,WAAW,QAAQ,UAAU;AAEvC,QAAI,QAAQ,aAAa,MAAM;AAC9B,UAAI,CAAC,WAAW,QAAQ,YAAY,IAAI,EAAG;AAC3C,UAAI,CAAC,SAAS,MAAM,QAAQ,YAAY,IAAI;AAC3C,cAAM,IAAI,MAAM,iCAAiC,QAAQ,YAAY,IAAI,EAAE;AAC5E,cAAQ,YAAY,OAAO,SAAS,MAAM,QAAQ,YAAY,IAAI,EAAE;AAAA,IACrE;AAEA,UAAM,KAAK,QAAQ;AACnB,QAAI,CAAC,GAAI;AAET,UAAM,YAAY,GAAG,QAAQ,MAAM,IAAI;AACvC,UAAM,OAAO,UAAU,CAAC;AAExB,QAAI,WAAW,IAAI,KAAK,CAAC,SAAS,SAAS,IAAI;AAC9C,YAAM,IAAI,MAAM,iCAAiC,IAAI,EAAE;AAGxD,QAAI,WAAW,IAAI,GAAG;AACrB,gBAAU,CAAC,IAAI,SAAS,SAAS,IAAI,EAAE;AACvC,SAAG,UAAU,UAAU,KAAK,IAAI;AAAA,IACjC;AAEA,UAAM,QAAQ,GAAG;AACjB,QAAI,CAAC,MAAO;AAEZ,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACtC,UAAI,CAAC,WAAW,MAAM,CAAC,CAAC,EAAG;AAE3B,UAAI,CAAC,SAAS,MAAM,MAAM,CAAC,CAAC,EAAG,OAAM,IAAI,MAAM,iCAAiC,MAAM,CAAC,CAAC,EAAE;AAC1F,YAAM,CAAC,IAAI,SAAS,MAAM,MAAM,CAAC,CAAC,EAAE;AAAA,IACrC;AAEA,OAAG,gBAAgB;AAAA,EACpB;AACD;AAMA,SAAS,qBAAqB,OAAiB;AAC9C,QAAM,QAAQ,oBAAI,IAAY;AAC9B,aAAW,QAAQ,OAAO;AACzB,QAAI,WAAW,IAAI,GAAG;AACrB,UAAI,KAAC,uCAAiB,IAAI,EAAG,OAAM,IAAI,MAAM,4BAA4B,IAAI,EAAE;AAC/E,YAAM,IAAI,IAAI;AAAA,IACf;AAAA,EACD;AACA,SAAO;AACR;", "names": []}