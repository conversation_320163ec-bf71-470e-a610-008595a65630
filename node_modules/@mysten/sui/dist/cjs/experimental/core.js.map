{"version": 3, "sources": ["../../../src/experimental/core.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { TypeTagSerializer } from '../bcs/type-tag-serializer.js';\nimport type { TransactionPlugin } from '../transactions/index.js';\nimport { deriveDynamicFieldID } from '../utils/dynamic-fields.js';\nimport { normalizeStructTag, parseStructTag, SUI_ADDRESS_LENGTH } from '../utils/sui-types.js';\nimport { Experimental_BaseClient } from './client.js';\nimport type { ClientWithExtensions, Experimental_SuiClientTypes } from './types.js';\nimport { MvrClient } from './mvr.js';\n\nexport type ClientWithCoreApi = ClientWithExtensions<{\n\tcore: Experimental_CoreClient;\n}>;\n\nexport interface Experimental_CoreClientOptions\n\textends Experimental_SuiClientTypes.SuiClientOptions {\n\tbase: Experimental_BaseClient;\n\tmvr?: Experimental_SuiClientTypes.MvrOptions;\n}\n\nconst DEFAULT_MVR_URLS: Record<string, string> = {\n\tmainnet: 'https://mainnet.mvr.mystenlabs.com',\n\ttestnet: 'https://testnet.mvr.mystenlabs.com',\n};\n\nexport abstract class Experimental_CoreClient\n\textends Experimental_BaseClient\n\timplements Experimental_SuiClientTypes.TransportMethods\n{\n\tcore = this;\n\tmvr: Experimental_SuiClientTypes.MvrMethods;\n\n\tconstructor(options: Experimental_CoreClientOptions) {\n\t\tsuper(options);\n\n\t\tthis.mvr = new MvrClient({\n\t\t\tcache: this.cache.scope('core.mvr'),\n\t\t\turl: options.mvr?.url ?? DEFAULT_MVR_URLS[this.network],\n\t\t\tpageSize: options.mvr?.pageSize,\n\t\t\toverrides: options.mvr?.overrides,\n\t\t});\n\t}\n\n\tabstract getObjects(\n\t\toptions: Experimental_SuiClientTypes.GetObjectsOptions,\n\t): Promise<Experimental_SuiClientTypes.GetObjectsResponse>;\n\n\tasync getObject(\n\t\toptions: Experimental_SuiClientTypes.GetObjectOptions,\n\t): Promise<Experimental_SuiClientTypes.GetObjectResponse> {\n\t\tconst { objectId } = options;\n\t\tconst {\n\t\t\tobjects: [result],\n\t\t} = await this.getObjects({ objectIds: [objectId], signal: options.signal });\n\t\tif (result instanceof Error) {\n\t\t\tthrow result;\n\t\t}\n\t\treturn { object: result };\n\t}\n\n\tabstract getCoins(\n\t\toptions: Experimental_SuiClientTypes.GetCoinsOptions,\n\t): Promise<Experimental_SuiClientTypes.GetCoinsResponse>;\n\n\tabstract getOwnedObjects(\n\t\toptions: Experimental_SuiClientTypes.GetOwnedObjectsOptions,\n\t): Promise<Experimental_SuiClientTypes.GetOwnedObjectsResponse>;\n\n\tabstract getBalance(\n\t\toptions: Experimental_SuiClientTypes.GetBalanceOptions,\n\t): Promise<Experimental_SuiClientTypes.GetBalanceResponse>;\n\n\tabstract getAllBalances(\n\t\toptions: Experimental_SuiClientTypes.GetAllBalancesOptions,\n\t): Promise<Experimental_SuiClientTypes.GetAllBalancesResponse>;\n\n\tabstract getTransaction(\n\t\toptions: Experimental_SuiClientTypes.GetTransactionOptions,\n\t): Promise<Experimental_SuiClientTypes.GetTransactionResponse>;\n\n\tabstract executeTransaction(\n\t\toptions: Experimental_SuiClientTypes.ExecuteTransactionOptions,\n\t): Promise<Experimental_SuiClientTypes.ExecuteTransactionResponse>;\n\n\tabstract dryRunTransaction(\n\t\toptions: Experimental_SuiClientTypes.DryRunTransactionOptions,\n\t): Promise<Experimental_SuiClientTypes.DryRunTransactionResponse>;\n\n\tabstract getReferenceGasPrice(\n\t\toptions?: Experimental_SuiClientTypes.GetReferenceGasPriceOptions,\n\t): Promise<Experimental_SuiClientTypes.GetReferenceGasPriceResponse>;\n\n\tabstract getDynamicFields(\n\t\toptions: Experimental_SuiClientTypes.GetDynamicFieldsOptions,\n\t): Promise<Experimental_SuiClientTypes.GetDynamicFieldsResponse>;\n\n\tabstract resolveTransactionPlugin(): TransactionPlugin;\n\n\tabstract verifyZkLoginSignature(\n\t\toptions: Experimental_SuiClientTypes.VerifyZkLoginSignatureOptions,\n\t): Promise<Experimental_SuiClientTypes.ZkLoginVerifyResponse>;\n\n\tasync getDynamicField(\n\t\toptions: Experimental_SuiClientTypes.GetDynamicFieldOptions,\n\t): Promise<Experimental_SuiClientTypes.GetDynamicFieldResponse> {\n\t\tconst fieldId = deriveDynamicFieldID(\n\t\t\toptions.parentId,\n\t\t\tTypeTagSerializer.parseFromStr(options.name.type),\n\t\t\toptions.name.bcs,\n\t\t);\n\t\tconst {\n\t\t\tobjects: [fieldObject],\n\t\t} = await this.getObjects({\n\t\t\tobjectIds: [fieldId],\n\t\t\tsignal: options.signal,\n\t\t});\n\n\t\tif (fieldObject instanceof Error) {\n\t\t\tthrow fieldObject;\n\t\t}\n\n\t\tconst fieldType = parseStructTag(fieldObject.type);\n\t\tconst content = await fieldObject.content;\n\n\t\treturn {\n\t\t\tdynamicField: {\n\t\t\t\tid: fieldObject.id,\n\t\t\t\tdigest: fieldObject.digest,\n\t\t\t\tversion: fieldObject.version,\n\t\t\t\ttype: fieldObject.type,\n\t\t\t\tname: {\n\t\t\t\t\ttype:\n\t\t\t\t\t\ttypeof fieldType.typeParams[0] === 'string'\n\t\t\t\t\t\t\t? fieldType.typeParams[0]\n\t\t\t\t\t\t\t: normalizeStructTag(fieldType.typeParams[0]),\n\t\t\t\t\tbcs: options.name.bcs,\n\t\t\t\t},\n\t\t\t\tvalue: {\n\t\t\t\t\ttype:\n\t\t\t\t\t\ttypeof fieldType.typeParams[1] === 'string'\n\t\t\t\t\t\t\t? fieldType.typeParams[1]\n\t\t\t\t\t\t\t: normalizeStructTag(fieldType.typeParams[1]),\n\t\t\t\t\tbcs: content.slice(SUI_ADDRESS_LENGTH + options.name.bcs.length),\n\t\t\t\t},\n\t\t\t},\n\t\t};\n\t}\n\n\tasync waitForTransaction({\n\t\tsignal,\n\t\ttimeout = 60 * 1000,\n\t\t...input\n\t}: {\n\t\t/** An optional abort signal that can be used to cancel the wait. */\n\t\tsignal?: AbortSignal;\n\t\t/** The amount of time to wait for transaction. Defaults to one minute. */\n\t\ttimeout?: number;\n\t} & Experimental_SuiClientTypes.GetTransactionOptions): Promise<Experimental_SuiClientTypes.GetTransactionResponse> {\n\t\tconst abortSignal = signal\n\t\t\t? AbortSignal.any([AbortSignal.timeout(timeout), signal])\n\t\t\t: AbortSignal.timeout(timeout);\n\n\t\tconst abortPromise = new Promise((_, reject) => {\n\t\t\tabortSignal.addEventListener('abort', () => reject(abortSignal.reason));\n\t\t});\n\n\t\tabortPromise.catch(() => {\n\t\t\t// Swallow unhandled rejections that might be thrown after early return\n\t\t});\n\n\t\t// eslint-disable-next-line no-constant-condition\n\t\twhile (true) {\n\t\t\tabortSignal.throwIfAborted();\n\t\t\ttry {\n\t\t\t\treturn await this.getTransaction({\n\t\t\t\t\t...input,\n\t\t\t\t\tsignal: abortSignal,\n\t\t\t\t});\n\t\t\t} catch (e) {\n\t\t\t\tawait Promise.race([new Promise((resolve) => setTimeout(resolve, 2_000)), abortPromise]);\n\t\t\t}\n\t\t}\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iCAAkC;AAElC,4BAAqC;AACrC,uBAAuE;AACvE,oBAAwC;AAExC,iBAA0B;AAY1B,MAAM,mBAA2C;AAAA,EAChD,SAAS;AAAA,EACT,SAAS;AACV;AAEO,MAAe,gCACb,sCAET;AAAA,EAIC,YAAY,SAAyC;AACpD,UAAM,OAAO;AAJd,gBAAO;AAMN,SAAK,MAAM,IAAI,qBAAU;AAAA,MACxB,OAAO,KAAK,MAAM,MAAM,UAAU;AAAA,MAClC,KAAK,QAAQ,KAAK,OAAO,iBAAiB,KAAK,OAAO;AAAA,MACtD,UAAU,QAAQ,KAAK;AAAA,MACvB,WAAW,QAAQ,KAAK;AAAA,IACzB,CAAC;AAAA,EACF;AAAA,EAMA,MAAM,UACL,SACyD;AACzD,UAAM,EAAE,SAAS,IAAI;AACrB,UAAM;AAAA,MACL,SAAS,CAAC,MAAM;AAAA,IACjB,IAAI,MAAM,KAAK,WAAW,EAAE,WAAW,CAAC,QAAQ,GAAG,QAAQ,QAAQ,OAAO,CAAC;AAC3E,QAAI,kBAAkB,OAAO;AAC5B,YAAM;AAAA,IACP;AACA,WAAO,EAAE,QAAQ,OAAO;AAAA,EACzB;AAAA,EA4CA,MAAM,gBACL,SAC+D;AAC/D,UAAM,cAAU;AAAA,MACf,QAAQ;AAAA,MACR,6CAAkB,aAAa,QAAQ,KAAK,IAAI;AAAA,MAChD,QAAQ,KAAK;AAAA,IACd;AACA,UAAM;AAAA,MACL,SAAS,CAAC,WAAW;AAAA,IACtB,IAAI,MAAM,KAAK,WAAW;AAAA,MACzB,WAAW,CAAC,OAAO;AAAA,MACnB,QAAQ,QAAQ;AAAA,IACjB,CAAC;AAED,QAAI,uBAAuB,OAAO;AACjC,YAAM;AAAA,IACP;AAEA,UAAM,gBAAY,iCAAe,YAAY,IAAI;AACjD,UAAM,UAAU,MAAM,YAAY;AAElC,WAAO;AAAA,MACN,cAAc;AAAA,QACb,IAAI,YAAY;AAAA,QAChB,QAAQ,YAAY;AAAA,QACpB,SAAS,YAAY;AAAA,QACrB,MAAM,YAAY;AAAA,QAClB,MAAM;AAAA,UACL,MACC,OAAO,UAAU,WAAW,CAAC,MAAM,WAChC,UAAU,WAAW,CAAC,QACtB,qCAAmB,UAAU,WAAW,CAAC,CAAC;AAAA,UAC9C,KAAK,QAAQ,KAAK;AAAA,QACnB;AAAA,QACA,OAAO;AAAA,UACN,MACC,OAAO,UAAU,WAAW,CAAC,MAAM,WAChC,UAAU,WAAW,CAAC,QACtB,qCAAmB,UAAU,WAAW,CAAC,CAAC;AAAA,UAC9C,KAAK,QAAQ,MAAM,sCAAqB,QAAQ,KAAK,IAAI,MAAM;AAAA,QAChE;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,mBAAmB;AAAA,IACxB;AAAA,IACA,UAAU,KAAK;AAAA,IACf,GAAG;AAAA,EACJ,GAKoH;AACnH,UAAM,cAAc,SACjB,YAAY,IAAI,CAAC,YAAY,QAAQ,OAAO,GAAG,MAAM,CAAC,IACtD,YAAY,QAAQ,OAAO;AAE9B,UAAM,eAAe,IAAI,QAAQ,CAAC,GAAG,WAAW;AAC/C,kBAAY,iBAAiB,SAAS,MAAM,OAAO,YAAY,MAAM,CAAC;AAAA,IACvE,CAAC;AAED,iBAAa,MAAM,MAAM;AAAA,IAEzB,CAAC;AAGD,WAAO,MAAM;AACZ,kBAAY,eAAe;AAC3B,UAAI;AACH,eAAO,MAAM,KAAK,eAAe;AAAA,UAChC,GAAG;AAAA,UACH,QAAQ;AAAA,QACT,CAAC;AAAA,MACF,SAAS,GAAG;AACX,cAAM,QAAQ,KAAK,CAAC,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,GAAK,CAAC,GAAG,YAAY,CAAC;AAAA,MACxF;AAAA,IACD;AAAA,EACD;AACD;", "names": []}