import type { Simplify, UnionToIntersection } from '@mysten/utils';
import { ClientCache } from './cache.js';
import type { Experimental_CoreClient } from './core.js';
import type { ClientWithExtensions, Experimental_SuiClientTypes, SuiClientRegistration } from './types.js';
export declare abstract class Experimental_BaseClient {
    network: Experimental_SuiClientTypes.Network;
    cache: ClientCache;
    base: Experimental_BaseClient;
    constructor({ network, base, cache, }: Experimental_SuiClientTypes.SuiClientOptions);
    abstract core: Experimental_CoreClient;
    $extend<const Registrations extends SuiClientRegistration<this>[]>(...registrations: Registrations): ClientWithExtensions<Simplify<UnionToIntersection<{ [K in keyof Registrations]: Registrations[K] extends SuiClientRegistration<this, infer Name extends string, infer Extension> ? { [K2 in Name]: Extension; } : never; }[number]>>, this>;
}
