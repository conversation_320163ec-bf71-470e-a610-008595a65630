{"version": 3, "sources": ["../../../../src/experimental/transports/utils.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { bcs } from '../../bcs/index.js';\nimport { TransactionDataBuilder } from '../../transactions/TransactionData.js';\nimport type { Experimental_SuiClientTypes } from '../types.js';\n\nexport function parseTransactionBcs(\n\tbytes: Uint8Array,\n): Experimental_SuiClientTypes.TransactionResponse['transaction'] {\n\treturn {\n\t\t...TransactionDataBuilder.fromBytes(bytes).snapshot(),\n\t\tbcs: bytes,\n\t};\n}\n\nexport function parseTransactionEffectsBcs(\n\teffects: Uint8Array,\n): Experimental_SuiClientTypes.TransactionEffects {\n\tconst parsed = bcs.TransactionEffects.parse(effects);\n\n\tswitch (parsed.$kind) {\n\t\tcase 'V1':\n\t\t\treturn parseTransactionEffectsV1({ bytes: effects, effects: parsed.V1 });\n\t\tcase 'V2':\n\t\t\treturn parseTransactionEffectsV2({ bytes: effects, effects: parsed.V2 });\n\t\tdefault:\n\t\t\tthrow new Error(\n\t\t\t\t`Unknown transaction effects version: ${(parsed as { $kind: string }).$kind}`,\n\t\t\t);\n\t}\n}\n\nfunction parseTransactionEffectsV1(_: {\n\tbytes: Uint8Array;\n\teffects: NonNullable<(typeof bcs.TransactionEffects.$inferType)['V1']>;\n}): Experimental_SuiClientTypes.TransactionEffects {\n\tthrow new Error('V1 effects are not supported yet');\n}\n\nfunction parseTransactionEffectsV2({\n\tbytes,\n\teffects,\n}: {\n\tbytes: Uint8Array;\n\teffects: NonNullable<(typeof bcs.TransactionEffects.$inferType)['V2']>;\n}): Experimental_SuiClientTypes.TransactionEffects {\n\tconst changedObjects = effects.changedObjects.map(\n\t\t([id, change]): Experimental_SuiClientTypes.ChangedObject => {\n\t\t\treturn {\n\t\t\t\tid,\n\t\t\t\tinputState: change.inputState.$kind === 'Exist' ? 'Exists' : 'DoesNotExist',\n\t\t\t\tinputVersion: change.inputState.Exist?.[0][0] ?? null,\n\t\t\t\tinputDigest: change.inputState.Exist?.[0][1] ?? null,\n\t\t\t\tinputOwner: change.inputState.Exist?.[1] ?? null,\n\t\t\t\toutputState:\n\t\t\t\t\tchange.outputState.$kind === 'NotExist' ? 'DoesNotExist' : change.outputState.$kind,\n\t\t\t\toutputVersion:\n\t\t\t\t\tchange.outputState.$kind === 'PackageWrite'\n\t\t\t\t\t\t? change.outputState.PackageWrite?.[0]\n\t\t\t\t\t\t: change.outputState.ObjectWrite\n\t\t\t\t\t\t\t? effects.lamportVersion\n\t\t\t\t\t\t\t: null,\n\t\t\t\toutputDigest:\n\t\t\t\t\tchange.outputState.$kind === 'PackageWrite'\n\t\t\t\t\t\t? change.outputState.PackageWrite?.[1]\n\t\t\t\t\t\t: (change.outputState.ObjectWrite?.[0] ?? null),\n\t\t\t\toutputOwner: change.outputState.ObjectWrite ? change.outputState.ObjectWrite[1] : null,\n\t\t\t\tidOperation: change.idOperation.$kind,\n\t\t\t};\n\t\t},\n\t);\n\n\treturn {\n\t\tbcs: bytes,\n\t\tdigest: effects.transactionDigest,\n\t\tversion: 2,\n\t\tstatus:\n\t\t\teffects.status.$kind === 'Success'\n\t\t\t\t? {\n\t\t\t\t\t\tsuccess: true,\n\t\t\t\t\t\terror: null,\n\t\t\t\t\t}\n\t\t\t\t: {\n\t\t\t\t\t\tsuccess: false,\n\t\t\t\t\t\t// TODO: add command\n\t\t\t\t\t\terror: effects.status.Failed.error.$kind,\n\t\t\t\t\t},\n\t\tgasUsed: effects.gasUsed,\n\t\ttransactionDigest: effects.transactionDigest,\n\t\tgasObject:\n\t\t\teffects.gasObjectIndex === null ? null : (changedObjects[effects.gasObjectIndex] ?? null),\n\t\teventsDigest: effects.eventsDigest,\n\t\tdependencies: effects.dependencies,\n\t\tlamportVersion: effects.lamportVersion,\n\t\tchangedObjects,\n\t\tunchangedSharedObjects: effects.unchangedSharedObjects.map(\n\t\t\t([objectId, object]): Experimental_SuiClientTypes.UnchangedSharedObject => {\n\t\t\t\treturn {\n\t\t\t\t\tkind: object.$kind,\n\t\t\t\t\tobjectId: objectId,\n\t\t\t\t\tversion:\n\t\t\t\t\t\tobject.$kind === 'ReadOnlyRoot'\n\t\t\t\t\t\t\t? object.ReadOnlyRoot[0]\n\t\t\t\t\t\t\t: (object[object.$kind] as string | null),\n\t\t\t\t\tdigest: object.$kind === 'ReadOnlyRoot' ? object.ReadOnlyRoot[1] : null,\n\t\t\t\t};\n\t\t\t},\n\t\t),\n\t\tauxiliaryDataDigest: effects.auxDataDigest,\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAAoB;AACpB,6BAAuC;AAGhC,SAAS,oBACf,OACiE;AACjE,SAAO;AAAA,IACN,GAAG,8CAAuB,UAAU,KAAK,EAAE,SAAS;AAAA,IACpD,KAAK;AAAA,EACN;AACD;AAEO,SAAS,2BACf,SACiD;AACjD,QAAM,SAAS,eAAI,mBAAmB,MAAM,OAAO;AAEnD,UAAQ,OAAO,OAAO;AAAA,IACrB,KAAK;AACJ,aAAO,0BAA0B,EAAE,OAAO,SAAS,SAAS,OAAO,GAAG,CAAC;AAAA,IACxE,KAAK;AACJ,aAAO,0BAA0B,EAAE,OAAO,SAAS,SAAS,OAAO,GAAG,CAAC;AAAA,IACxE;AACC,YAAM,IAAI;AAAA,QACT,wCAAyC,OAA6B,KAAK;AAAA,MAC5E;AAAA,EACF;AACD;AAEA,SAAS,0BAA0B,GAGgB;AAClD,QAAM,IAAI,MAAM,kCAAkC;AACnD;AAEA,SAAS,0BAA0B;AAAA,EAClC;AAAA,EACA;AACD,GAGmD;AAClD,QAAM,iBAAiB,QAAQ,eAAe;AAAA,IAC7C,CAAC,CAAC,IAAI,MAAM,MAAiD;AAC5D,aAAO;AAAA,QACN;AAAA,QACA,YAAY,OAAO,WAAW,UAAU,UAAU,WAAW;AAAA,QAC7D,cAAc,OAAO,WAAW,QAAQ,CAAC,EAAE,CAAC,KAAK;AAAA,QACjD,aAAa,OAAO,WAAW,QAAQ,CAAC,EAAE,CAAC,KAAK;AAAA,QAChD,YAAY,OAAO,WAAW,QAAQ,CAAC,KAAK;AAAA,QAC5C,aACC,OAAO,YAAY,UAAU,aAAa,iBAAiB,OAAO,YAAY;AAAA,QAC/E,eACC,OAAO,YAAY,UAAU,iBAC1B,OAAO,YAAY,eAAe,CAAC,IACnC,OAAO,YAAY,cAClB,QAAQ,iBACR;AAAA,QACL,cACC,OAAO,YAAY,UAAU,iBAC1B,OAAO,YAAY,eAAe,CAAC,IAClC,OAAO,YAAY,cAAc,CAAC,KAAK;AAAA,QAC5C,aAAa,OAAO,YAAY,cAAc,OAAO,YAAY,YAAY,CAAC,IAAI;AAAA,QAClF,aAAa,OAAO,YAAY;AAAA,MACjC;AAAA,IACD;AAAA,EACD;AAEA,SAAO;AAAA,IACN,KAAK;AAAA,IACL,QAAQ,QAAQ;AAAA,IAChB,SAAS;AAAA,IACT,QACC,QAAQ,OAAO,UAAU,YACtB;AAAA,MACA,SAAS;AAAA,MACT,OAAO;AAAA,IACR,IACC;AAAA,MACA,SAAS;AAAA;AAAA,MAET,OAAO,QAAQ,OAAO,OAAO,MAAM;AAAA,IACpC;AAAA,IACH,SAAS,QAAQ;AAAA,IACjB,mBAAmB,QAAQ;AAAA,IAC3B,WACC,QAAQ,mBAAmB,OAAO,OAAQ,eAAe,QAAQ,cAAc,KAAK;AAAA,IACrF,cAAc,QAAQ;AAAA,IACtB,cAAc,QAAQ;AAAA,IACtB,gBAAgB,QAAQ;AAAA,IACxB;AAAA,IACA,wBAAwB,QAAQ,uBAAuB;AAAA,MACtD,CAAC,CAAC,UAAU,MAAM,MAAyD;AAC1E,eAAO;AAAA,UACN,MAAM,OAAO;AAAA,UACb;AAAA,UACA,SACC,OAAO,UAAU,iBACd,OAAO,aAAa,CAAC,IACpB,OAAO,OAAO,KAAK;AAAA,UACxB,QAAQ,OAAO,UAAU,iBAAiB,OAAO,aAAa,CAAC,IAAI;AAAA,QACpE;AAAA,MACD;AAAA,IACD;AAAA,IACA,qBAAqB,QAAQ;AAAA,EAC9B;AACD;", "names": []}