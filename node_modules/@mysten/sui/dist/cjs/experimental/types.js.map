{"version": 3, "sources": ["../../../src/experimental/types.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n/* eslint-disable @typescript-eslint/ban-types */\n\nimport type { SerializedTransactionDataV2, TransactionPlugin } from '../transactions/index.js';\nimport type { ClientCache } from './cache.js';\nimport type { Experimental_BaseClient } from './client.js';\n\nexport type SuiClientRegistration<\n\tT extends Experimental_BaseClient = Experimental_BaseClient,\n\tName extends string = string,\n\tExtension = unknown,\n> =\n\t| {\n\t\t\treadonly name: Name;\n\t\t\treadonly register: (client: T) => Extension;\n\t  }\n\t| SelfRegisteringClientExtension<T, Name, Extension>;\n\nexport interface SelfRegisteringClientExtension<\n\tT extends Experimental_BaseClient = Experimental_BaseClient,\n\tName extends string = string,\n\tExtension = unknown,\n> {\n\texperimental_asClientExtension: () => {\n\t\treadonly name: Name;\n\t\treadonly register: (client: T) => Extension;\n\t};\n}\n\nexport type ClientWithExtensions<\n\tT,\n\tBase extends Experimental_BaseClient = Experimental_BaseClient,\n> = Base & T;\n\nexport namespace Experimental_SuiClientTypes {\n\texport type Network = 'mainnet' | 'testnet' | 'devnet' | 'localnet' | (string & {});\n\n\texport interface SuiClientOptions {\n\t\tnetwork: Network;\n\t\tbase?: Experimental_BaseClient;\n\t\tcache?: ClientCache;\n\t}\n\n\texport interface MvrOptions {\n\t\turl?: string;\n\t\tpageSize?: number;\n\t\toverrides?: {\n\t\t\tpackages?: Record<string, string>;\n\t\t\ttypes?: Record<string, string>;\n\t\t};\n\t}\n\n\texport interface CoreClientMethodOptions {\n\t\tsignal?: AbortSignal;\n\t}\n\n\t/** Object methods */\n\texport interface TransportMethods {\n\t\tgetObjects: (options: GetObjectsOptions) => Promise<GetObjectsResponse>;\n\t\tgetOwnedObjects: (options: GetOwnedObjectsOptions) => Promise<GetOwnedObjectsResponse>;\n\t\tgetCoins: (options: GetCoinsOptions) => Promise<GetCoinsResponse>;\n\t\tgetDynamicFields: (options: GetDynamicFieldsOptions) => Promise<GetDynamicFieldsResponse>;\n\t\tgetDynamicField: (options: GetDynamicFieldOptions) => Promise<GetDynamicFieldResponse>;\n\t}\n\n\texport interface GetObjectsOptions extends CoreClientMethodOptions {\n\t\tobjectIds: string[];\n\t}\n\n\texport interface GetObjectOptions extends CoreClientMethodOptions {\n\t\tobjectId: string;\n\t}\n\n\texport interface GetOwnedObjectsOptions extends CoreClientMethodOptions {\n\t\taddress: string;\n\t\tlimit?: number;\n\t\tcursor?: string | null;\n\t\ttype?: string;\n\t}\n\n\texport interface GetCoinsOptions extends CoreClientMethodOptions {\n\t\taddress: string;\n\t\tcoinType: string;\n\t\tlimit?: number;\n\t\tcursor?: string | null;\n\t}\n\n\texport interface GetDynamicFieldsOptions extends CoreClientMethodOptions {\n\t\tparentId: string;\n\t\tlimit?: number;\n\t\tcursor?: string | null;\n\t}\n\n\texport interface GetDynamicFieldOptions extends CoreClientMethodOptions {\n\t\tparentId: string;\n\t\tname: DynamicFieldName;\n\t}\n\n\texport interface GetObjectsResponse {\n\t\tobjects: (ObjectResponse | Error)[];\n\t}\n\n\texport interface GetObjectResponse {\n\t\tobject: ObjectResponse;\n\t}\n\n\texport interface GetOwnedObjectsResponse {\n\t\tobjects: ObjectResponse[];\n\t\thasNextPage: boolean;\n\t\tcursor: string | null;\n\t}\n\n\texport interface GetCoinsResponse {\n\t\tobjects: CoinResponse[];\n\t\thasNextPage: boolean;\n\t\tcursor: string | null;\n\t}\n\n\texport interface ObjectResponse {\n\t\tid: string;\n\t\tversion: string;\n\t\tdigest: string;\n\t\towner: ObjectOwner;\n\t\ttype: string;\n\t\tcontent: PromiseLike<Uint8Array>;\n\t}\n\n\texport interface CoinResponse extends ObjectResponse {\n\t\tbalance: string;\n\t}\n\n\texport interface GetDynamicFieldsResponse {\n\t\thasNextPage: boolean;\n\t\tcursor: string | null;\n\t\tdynamicFields: {\n\t\t\tid: string;\n\t\t\ttype: string;\n\t\t\tname: DynamicFieldName;\n\t\t}[];\n\t}\n\n\texport interface GetDynamicFieldResponse {\n\t\tdynamicField: {\n\t\t\tname: DynamicFieldName;\n\t\t\tvalue: DynamicFieldValue;\n\t\t\tid: string;\n\t\t\tversion: string;\n\t\t\tdigest: string;\n\t\t\ttype: string;\n\t\t};\n\t}\n\n\texport interface DynamicFieldName {\n\t\ttype: string;\n\t\tbcs: Uint8Array;\n\t}\n\n\texport interface DynamicFieldValue {\n\t\ttype: string;\n\t\tbcs: Uint8Array;\n\t}\n\n\t/** Balance methods */\n\texport interface TransportMethods {\n\t\tgetBalance: (options: GetBalanceOptions) => Promise<GetBalanceResponse>;\n\t\tgetAllBalances: (options: GetAllBalancesOptions) => Promise<GetAllBalancesResponse>;\n\t}\n\n\texport interface GetBalanceOptions extends CoreClientMethodOptions {\n\t\taddress: string;\n\t\tcoinType: string;\n\t}\n\n\texport interface CoinBalance {\n\t\tcoinType: string;\n\t\tbalance: string;\n\t}\n\n\texport interface GetBalanceResponse {\n\t\tbalance: CoinBalance;\n\t}\n\n\texport interface GetAllBalancesOptions extends CoreClientMethodOptions {\n\t\taddress: string;\n\t\tlimit?: number;\n\t\tcursor?: string | null;\n\t}\n\n\texport interface GetAllBalancesResponse {\n\t\tbalances: CoinBalance[];\n\t\thasNextPage: boolean;\n\t\tcursor: string | null;\n\t}\n\n\t/** Transaction methods */\n\texport interface TransportMethods {\n\t\tgetTransaction: (options: GetTransactionOptions) => Promise<GetTransactionResponse>;\n\t\texecuteTransaction: (options: ExecuteTransactionOptions) => Promise<ExecuteTransactionResponse>;\n\t\tdryRunTransaction: (options: DryRunTransactionOptions) => Promise<DryRunTransactionResponse>;\n\t\tresolveTransactionPlugin: () => TransactionPlugin;\n\t}\n\n\texport interface TransactionResponse {\n\t\tdigest: string;\n\t\tsignatures: string[];\n\t\tepoch: string | null;\n\t\teffects: TransactionEffects;\n\t\tobjectTypes: PromiseLike<Record<string, string>>;\n\t\ttransaction: TransactionData;\n\t\t// TODO: add events\n\t\t// events?: Uint8Array;\n\t}\n\n\texport interface TransactionData extends SerializedTransactionDataV2 {\n\t\tbcs: Uint8Array;\n\t}\n\n\texport interface GetTransactionOptions extends CoreClientMethodOptions {\n\t\tdigest: string;\n\t}\n\n\texport interface GetTransactionResponse {\n\t\ttransaction: TransactionResponse;\n\t}\n\n\texport interface ExecuteTransactionOptions extends CoreClientMethodOptions {\n\t\ttransaction: Uint8Array;\n\t\tsignatures: string[];\n\t}\n\n\texport interface DryRunTransactionOptions extends CoreClientMethodOptions {\n\t\ttransaction: Uint8Array;\n\t}\n\n\texport interface DryRunTransactionResponse {\n\t\ttransaction: TransactionResponse;\n\t}\n\n\texport interface ExecuteTransactionResponse {\n\t\ttransaction: TransactionResponse;\n\t}\n\n\texport interface GetReferenceGasPriceOptions extends CoreClientMethodOptions {}\n\n\texport interface TransportMethods {\n\t\tgetReferenceGasPrice?: (\n\t\t\toptions?: GetReferenceGasPriceOptions,\n\t\t) => Promise<GetReferenceGasPriceResponse>;\n\t}\n\n\texport interface GetReferenceGasPriceResponse {\n\t\treferenceGasPrice: string;\n\t}\n\n\t/** ZkLogin methods */\n\texport interface VerifyZkLoginSignatureOptions extends CoreClientMethodOptions {\n\t\tbytes: string;\n\t\tsignature: string;\n\t\tintentScope: 'TransactionData' | 'PersonalMessage';\n\t\tauthor: string;\n\t}\n\n\texport interface ZkLoginVerifyResponse {\n\t\tsuccess: boolean;\n\t\terrors: string[];\n\t}\n\n\texport interface TransportMethods {\n\t\tverifyZkLoginSignature: (\n\t\t\toptions: VerifyZkLoginSignatureOptions,\n\t\t) => Promise<ZkLoginVerifyResponse>;\n\t}\n\n\t/** Name service methods */\n\texport interface ResolveNameServiceNamesOptions extends CoreClientMethodOptions {\n\t\taddress: string;\n\t\tcursor?: string | null | undefined;\n\t\tlimit?: number | null | undefined;\n\t}\n\n\texport interface ResolveNameServiceNamesResponse {\n\t\tdata: string[];\n\t\thasNextPage: boolean;\n\t\tnextCursor: string | null;\n\t}\n\n\texport interface TransportMethods {\n\t\tresolveNameServiceNames?: (\n\t\t\toptions: ResolveNameServiceNamesOptions,\n\t\t) => Promise<ResolveNameServiceNamesResponse>;\n\t}\n\n\t/** MVR methods */\n\n\texport interface TransportMethods {\n\t\tmvr: MvrMethods;\n\t}\n\n\texport interface MvrMethods {\n\t\tresolvePackage: (options: MvrResolvePackageOptions) => Promise<MvrResolvePackageResponse>;\n\t\tresolveType: (options: MvrResolveTypeOptions) => Promise<MvrResolveTypeResponse>;\n\t\tresolve: (options: MvrResolveOptions) => Promise<MvrResolveResponse>;\n\t}\n\n\texport interface MvrResolvePackageOptions extends CoreClientMethodOptions {\n\t\tpackage: string;\n\t}\n\n\texport interface MvrResolveTypeOptions extends CoreClientMethodOptions {\n\t\ttype: string;\n\t}\n\n\texport interface MvrResolveOptions extends CoreClientMethodOptions {\n\t\tpackages?: string[];\n\t\ttypes?: string[];\n\t}\n\n\texport interface MvrResolvePackageResponse {\n\t\tpackage: string;\n\t}\n\n\texport interface MvrResolveTypeResponse {\n\t\ttype: string;\n\t}\n\n\texport interface MvrResolveResponse {\n\t\tpackages: Record<\n\t\t\tstring,\n\t\t\t{\n\t\t\t\tpackage: string;\n\t\t\t}\n\t\t>;\n\t\ttypes: Record<\n\t\t\tstring,\n\t\t\t{\n\t\t\t\ttype: string;\n\t\t\t}\n\t\t>;\n\t}\n\n\t/** ObjectOwner types */\n\n\texport interface AddressOwner {\n\t\t$kind: 'AddressOwner';\n\t\tAddressOwner: string;\n\t}\n\n\texport interface ParentOwner {\n\t\t$kind: 'ObjectOwner';\n\t\tObjectOwner: string;\n\t}\n\n\texport interface SharedOwner {\n\t\t$kind: 'Shared';\n\t\tShared: {\n\t\t\tinitialSharedVersion: string;\n\t\t};\n\t}\n\n\texport interface ImmutableOwner {\n\t\t$kind: 'Immutable';\n\t\tImmutable: true;\n\t}\n\n\texport interface ConsensusAddressOwner {\n\t\t$kind: 'ConsensusAddressOwner';\n\t\tConsensusAddressOwner: {\n\t\t\towner: string;\n\t\t\tstartVersion: string;\n\t\t};\n\t}\n\n\texport type ObjectOwner =\n\t\t| AddressOwner\n\t\t| ParentOwner\n\t\t| SharedOwner\n\t\t| ImmutableOwner\n\t\t| ConsensusAddressOwner;\n\n\t/** Effects */\n\n\texport interface TransactionEffects {\n\t\tbcs: Uint8Array | null;\n\t\tdigest: string;\n\t\tversion: number;\n\t\tstatus: ExecutionStatus;\n\t\tgasUsed: GasCostSummary;\n\t\ttransactionDigest: string;\n\t\tgasObject: ChangedObject | null;\n\t\teventsDigest: string | null;\n\t\tdependencies: string[];\n\t\tlamportVersion: string | null;\n\t\tchangedObjects: ChangedObject[];\n\t\tunchangedSharedObjects: UnchangedSharedObject[];\n\t\tauxiliaryDataDigest: string | null;\n\t}\n\n\texport interface ChangedObject {\n\t\tid: string;\n\t\tinputState: 'Unknown' | 'DoesNotExist' | 'Exists';\n\t\tinputVersion: string | null;\n\t\tinputDigest: string | null;\n\t\tinputOwner: ObjectOwner | null;\n\t\toutputState: 'Unknown' | 'DoesNotExist' | 'ObjectWrite' | 'PackageWrite';\n\t\toutputVersion: string | null;\n\t\toutputDigest: string | null;\n\t\toutputOwner: ObjectOwner | null;\n\t\tidOperation: 'Unknown' | 'None' | 'Created' | 'Deleted';\n\t}\n\n\texport interface GasCostSummary {\n\t\tcomputationCost: string;\n\t\tstorageCost: string;\n\t\tstorageRebate: string;\n\t\tnonRefundableStorageFee: string;\n\t}\n\n\texport type ExecutionStatus =\n\t\t| {\n\t\t\t\tsuccess: true;\n\t\t\t\terror: null;\n\t\t  }\n\t\t| {\n\t\t\t\tsuccess: false;\n\t\t\t\t// TODO: this should probably be typed better: https://github.com/bmwill/sui/blob/646a2c819346dc140cc649eb9fea368fb14f96e5/crates/sui-rpc-api/proto/sui/rpc/v2beta/execution_status.proto#L22\n\t\t\t\terror: string;\n\t\t  };\n\n\texport interface UnchangedSharedObject {\n\t\tkind:\n\t\t\t| 'Unknown'\n\t\t\t| 'ReadOnlyRoot'\n\t\t\t| 'MutateDeleted'\n\t\t\t| 'ReadDeleted'\n\t\t\t| 'Cancelled'\n\t\t\t| 'PerEpochConfig';\n\t\tobjectId: string;\n\t\tversion: string | null;\n\t\tdigest: string | null;\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}