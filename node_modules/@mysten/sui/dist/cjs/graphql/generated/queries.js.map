{"version": 3, "sources": ["../../../../src/graphql/generated/queries.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n/* eslint-disable */\n\nimport { DocumentTypeDecoration } from '@graphql-typed-document-node/core';\nexport type Maybe<T> = T | null;\nexport type InputMaybe<T> = Maybe<T>;\nexport type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };\nexport type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };\nexport type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };\nexport type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };\nexport type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };\n/** All built-in and custom scalars, mapped to their actual values */\nexport type Scalars = {\n  ID: { input: string; output: string; }\n  String: { input: string; output: string; }\n  Boolean: { input: boolean; output: boolean; }\n  Int: { input: number; output: number; }\n  Float: { input: number; output: number; }\n  /** String containing Base64-encoded binary data. */\n  Base64: { input: any; output: any; }\n  /** String representation of an arbitrary width, possibly signed integer. */\n  BigInt: { input: any; output: any; }\n  /** ISO-8601 Date and Time: RFC3339 in UTC with format: YYYY-MM-DDTHH:MM:SS.mmmZ. Note that the milliseconds part is optional, and it may be omitted if its value is 0. */\n  DateTime: { input: any; output: any; }\n  /** Arbitrary JSON data. */\n  JSON: { input: any; output: any; }\n  /**\n   * The contents of a Move Value, corresponding to the following recursive type:\n   *\n   * type MoveData =\n   *     { Address: SuiAddress }\n   *   | { UID:     SuiAddress }\n   *   | { ID:      SuiAddress }\n   *   | { Bool:    bool }\n   *   | { Number:  BigInt }\n   *   | { String:  string }\n   *   | { Vector:  [MoveData] }\n   *   | { Option:   MoveData? }\n   *   | { Struct:  [{ name: string , value: MoveData }] }\n   *   | { Variant: {\n   *       name: string,\n   *       fields: [{ name: string, value: MoveData }],\n   *   }\n   */\n  MoveData: { input: any; output: any; }\n  /**\n   * The shape of a concrete Move Type (a type with all its type parameters instantiated with concrete types), corresponding to the following recursive type:\n   *\n   * type MoveTypeLayout =\n   *     \"address\"\n   *   | \"bool\"\n   *   | \"u8\" | \"u16\" | ... | \"u256\"\n   *   | { vector: MoveTypeLayout }\n   *   | {\n   *       struct: {\n   *         type: string,\n   *         fields: [{ name: string, layout: MoveTypeLayout }],\n   *       }\n   *     }\n   *   | { enum: [{\n   *           type: string,\n   *           variants: [{\n   *               name: string,\n   *               fields: [{ name: string, layout: MoveTypeLayout }],\n   *           }]\n   *       }]\n   *   }\n   */\n  MoveTypeLayout: { input: any; output: any; }\n  /**\n   * The signature of a concrete Move Type (a type with all its type parameters instantiated with concrete types, that contains no references), corresponding to the following recursive type:\n   *\n   * type MoveTypeSignature =\n   *     \"address\"\n   *   | \"bool\"\n   *   | \"u8\" | \"u16\" | ... | \"u256\"\n   *   | { vector: MoveTypeSignature }\n   *   | {\n   *       datatype: {\n   *         package: string,\n   *         module: string,\n   *         type: string,\n   *         typeParameters: [MoveTypeSignature],\n   *       }\n   *     }\n   */\n  MoveTypeSignature: { input: any; output: any; }\n  /**\n   * The shape of an abstract Move Type (a type that can contain free type parameters, and can optionally be taken by reference), corresponding to the following recursive type:\n   *\n   * type OpenMoveTypeSignature = {\n   *   ref: (\"&\" | \"&mut\")?,\n   *   body: OpenMoveTypeSignatureBody,\n   * }\n   *\n   * type OpenMoveTypeSignatureBody =\n   *     \"address\"\n   *   | \"bool\"\n   *   | \"u8\" | \"u16\" | ... | \"u256\"\n   *   | { vector: OpenMoveTypeSignatureBody }\n   *   | {\n   *       datatype {\n   *         package: string,\n   *         module: string,\n   *         type: string,\n   *         typeParameters: [OpenMoveTypeSignatureBody]\n   *       }\n   *     }\n   *   | { typeParameter: number }\n   */\n  OpenMoveTypeSignature: { input: any; output: any; }\n  /** String containing 32B hex-encoded address, with a leading \"0x\". Leading zeroes can be omitted on input but will always appear in outputs (SuiAddress in output is guaranteed to be 66 characters long). */\n  SuiAddress: { input: any; output: any; }\n  /**\n   * An unsigned integer that can hold values up to 2^53 - 1. This can be treated similarly to `Int`,\n   * but it is guaranteed to be non-negative, and it may be larger than 2^32 - 1.\n   */\n  UInt53: { input: any; output: any; }\n};\n\nexport type ActiveJwk = {\n  __typename?: 'ActiveJwk';\n  /** The JWK algorithm parameter, (RFC 7517, Section 4.4). */\n  alg: Scalars['String']['output'];\n  /** The JWK RSA public exponent, (RFC 7517, Section 9.3). */\n  e: Scalars['String']['output'];\n  /** The most recent epoch in which the JWK was validated. */\n  epoch?: Maybe<Epoch>;\n  /** The string (Issuing Authority) that identifies the OIDC provider. */\n  iss: Scalars['String']['output'];\n  /** The string (Key ID) that identifies the JWK among a set of JWKs, (RFC 7517, Section 4.5). */\n  kid: Scalars['String']['output'];\n  /** The JWK key type parameter, (RFC 7517, Section 4.1). */\n  kty: Scalars['String']['output'];\n  /** The JWK RSA modulus, (RFC 7517, Section 9.3). */\n  n: Scalars['String']['output'];\n};\n\nexport type ActiveJwkConnection = {\n  __typename?: 'ActiveJwkConnection';\n  /** A list of edges. */\n  edges: Array<ActiveJwkEdge>;\n  /** A list of nodes. */\n  nodes: Array<ActiveJwk>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type ActiveJwkEdge = {\n  __typename?: 'ActiveJwkEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: ActiveJwk;\n};\n\n/** The 32-byte address that is an account address (corresponding to a public key). */\nexport type Address = IOwner & {\n  __typename?: 'Address';\n  address: Scalars['SuiAddress']['output'];\n  /**\n   * Total balance of all coins with marker type owned by this address. If type is not supplied,\n   * it defaults to `0x2::sui::SUI`.\n   */\n  balance?: Maybe<Balance>;\n  /** The balances of all coin types owned by this address. */\n  balances: BalanceConnection;\n  /**\n   * The coin objects for this address.\n   *\n   * `type` is a filter on the coin's type parameter, defaulting to `0x2::sui::SUI`.\n   */\n  coins: CoinConnection;\n  /** The domain explicitly configured as the default domain pointing to this address. */\n  defaultSuinsName?: Maybe<Scalars['String']['output']>;\n  /** Objects owned by this address, optionally `filter`-ed. */\n  objects: MoveObjectConnection;\n  /** The `0x3::staking_pool::StakedSui` objects owned by this address. */\n  stakedSuis: StakedSuiConnection;\n  /**\n   * The SuinsRegistration NFTs owned by this address. These grant the owner the capability to\n   * manage the associated domain.\n   */\n  suinsRegistrations: SuinsRegistrationConnection;\n  /**\n   * Similar behavior to the `transactionBlocks` in Query but supporting the additional\n   * `AddressTransactionBlockRelationship` filter, which defaults to `SENT`.\n   *\n   * `scanLimit` restricts the number of candidate transactions scanned when gathering a page of\n   * results. It is required for queries that apply more than two complex filters (on function,\n   * kind, sender, recipient, input object, changed object, or ids), and can be at most\n   * `serviceConfig.maxScanLimit`.\n   *\n   * When the scan limit is reached the page will be returned even if it has fewer than `first`\n   * results when paginating forward (`last` when paginating backwards). If there are more\n   * transactions to scan, `pageInfo.hasNextPage` (or `pageInfo.hasPreviousPage`) will be set to\n   * `true`, and `PageInfo.endCursor` (or `PageInfo.startCursor`) will be set to the last\n   * transaction that was scanned as opposed to the last (or first) transaction in the page.\n   *\n   * Requesting the next (or previous) page after this cursor will resume the search, scanning\n   * the next `scanLimit` many transactions in the direction of pagination, and so on until all\n   * transactions in the scanning range have been visited.\n   *\n   * By default, the scanning range includes all transactions known to GraphQL, but it can be\n   * restricted by the `after` and `before` cursors, and the `beforeCheckpoint`,\n   * `afterCheckpoint` and `atCheckpoint` filters.\n   */\n  transactionBlocks: TransactionBlockConnection;\n};\n\n\n/** The 32-byte address that is an account address (corresponding to a public key). */\nexport type AddressBalanceArgs = {\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/** The 32-byte address that is an account address (corresponding to a public key). */\nexport type AddressBalancesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** The 32-byte address that is an account address (corresponding to a public key). */\nexport type AddressCoinsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/** The 32-byte address that is an account address (corresponding to a public key). */\nexport type AddressDefaultSuinsNameArgs = {\n  format?: InputMaybe<DomainFormat>;\n};\n\n\n/** The 32-byte address that is an account address (corresponding to a public key). */\nexport type AddressObjectsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<ObjectFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** The 32-byte address that is an account address (corresponding to a public key). */\nexport type AddressStakedSuisArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** The 32-byte address that is an account address (corresponding to a public key). */\nexport type AddressSuinsRegistrationsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** The 32-byte address that is an account address (corresponding to a public key). */\nexport type AddressTransactionBlocksArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<TransactionBlockFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  relation?: InputMaybe<AddressTransactionBlockRelationship>;\n  scanLimit?: InputMaybe<Scalars['Int']['input']>;\n};\n\nexport type AddressConnection = {\n  __typename?: 'AddressConnection';\n  /** A list of edges. */\n  edges: Array<AddressEdge>;\n  /** A list of nodes. */\n  nodes: Array<Address>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type AddressEdge = {\n  __typename?: 'AddressEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: Address;\n};\n\n/**\n * An address-owned object is owned by a specific 32-byte address that is\n * either an account address (derived from a particular signature scheme) or\n * an object ID. An address-owned object is accessible only to its owner and no others.\n */\nexport type AddressOwner = {\n  __typename?: 'AddressOwner';\n  owner?: Maybe<Owner>;\n};\n\n/** The possible relationship types for a transaction block: sent, or received. */\nexport enum AddressTransactionBlockRelationship {\n  /**\n   * Transactions that this address was involved in, either as the sender, sponsor, or as the\n   * owner of some object that was created, modified or transfered.\n   */\n  Affected = 'AFFECTED',\n  /** Transactions this address has sent. */\n  Sent = 'SENT'\n}\n\n/** System transaction for creating the on-chain state used by zkLogin. */\nexport type AuthenticatorStateCreateTransaction = {\n  __typename?: 'AuthenticatorStateCreateTransaction';\n  /** A workaround to define an empty variant of a GraphQL union. */\n  _?: Maybe<Scalars['Boolean']['output']>;\n};\n\nexport type AuthenticatorStateExpireTransaction = {\n  __typename?: 'AuthenticatorStateExpireTransaction';\n  /** The initial version that the AuthenticatorStateUpdate was shared at. */\n  authenticatorObjInitialSharedVersion: Scalars['UInt53']['output'];\n  /** Expire JWKs that have a lower epoch than this. */\n  minEpoch?: Maybe<Epoch>;\n};\n\n/** System transaction for updating the on-chain state used by zkLogin. */\nexport type AuthenticatorStateUpdateTransaction = {\n  __typename?: 'AuthenticatorStateUpdateTransaction';\n  /** The initial version of the authenticator object that it was shared at. */\n  authenticatorObjInitialSharedVersion: Scalars['UInt53']['output'];\n  /** Epoch of the authenticator state update transaction. */\n  epoch?: Maybe<Epoch>;\n  /** Newly active JWKs (JSON Web Keys). */\n  newActiveJwks: ActiveJwkConnection;\n  /** Consensus round of the authenticator state update. */\n  round: Scalars['UInt53']['output'];\n};\n\n\n/** System transaction for updating the on-chain state used by zkLogin. */\nexport type AuthenticatorStateUpdateTransactionNewActiveJwksArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n/** Range of checkpoints that the RPC is guaranteed to produce a consistent response for. */\nexport type AvailableRange = {\n  __typename?: 'AvailableRange';\n  first?: Maybe<Checkpoint>;\n  last?: Maybe<Checkpoint>;\n};\n\n/** The total balance for a particular coin type. */\nexport type Balance = {\n  __typename?: 'Balance';\n  /** How many coins of this type constitute the balance */\n  coinObjectCount?: Maybe<Scalars['UInt53']['output']>;\n  /** Coin type for the balance, such as 0x2::sui::SUI */\n  coinType: MoveType;\n  /** Total balance across all coin objects of the coin type */\n  totalBalance?: Maybe<Scalars['BigInt']['output']>;\n};\n\n/** Effects to the balance (sum of coin values per coin type) owned by an address or object. */\nexport type BalanceChange = {\n  __typename?: 'BalanceChange';\n  /** The signed balance change. */\n  amount?: Maybe<Scalars['BigInt']['output']>;\n  /** The inner type of the coin whose balance has changed (e.g. `0x2::sui::SUI`). */\n  coinType?: Maybe<MoveType>;\n  /** The address or object whose balance has changed. */\n  owner?: Maybe<Owner>;\n};\n\nexport type BalanceChangeConnection = {\n  __typename?: 'BalanceChangeConnection';\n  /** A list of edges. */\n  edges: Array<BalanceChangeEdge>;\n  /** A list of nodes. */\n  nodes: Array<BalanceChange>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type BalanceChangeEdge = {\n  __typename?: 'BalanceChangeEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: BalanceChange;\n};\n\nexport type BalanceConnection = {\n  __typename?: 'BalanceConnection';\n  /** A list of edges. */\n  edges: Array<BalanceEdge>;\n  /** A list of nodes. */\n  nodes: Array<Balance>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type BalanceEdge = {\n  __typename?: 'BalanceEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: Balance;\n};\n\nexport type BridgeCommitteeInitTransaction = {\n  __typename?: 'BridgeCommitteeInitTransaction';\n  bridgeObjInitialSharedVersion: Scalars['UInt53']['output'];\n};\n\nexport type BridgeStateCreateTransaction = {\n  __typename?: 'BridgeStateCreateTransaction';\n  chainId: Scalars['String']['output'];\n};\n\n/**\n * A system transaction that updates epoch information on-chain (increments the current epoch).\n * Executed by the system once per epoch, without using gas. Epoch change transactions cannot be\n * submitted by users, because validators will refuse to sign them.\n *\n * This transaction kind is deprecated in favour of `EndOfEpochTransaction`.\n */\nexport type ChangeEpochTransaction = {\n  __typename?: 'ChangeEpochTransaction';\n  /** The total amount of gas charged for computation during the previous epoch (in MIST). */\n  computationCharge: Scalars['BigInt']['output'];\n  /** The next (to become) epoch. */\n  epoch?: Maybe<Epoch>;\n  /**\n   * The total gas retained from storage fees, that will not be returned by storage rebates when\n   * the relevant objects are cleaned up (in MIST).\n   */\n  nonRefundableStorageFee: Scalars['BigInt']['output'];\n  /** The protocol version in effect in the new epoch. */\n  protocolVersion: Scalars['UInt53']['output'];\n  /** Time at which the next epoch will start. */\n  startTimestamp: Scalars['DateTime']['output'];\n  /** The total amount of gas charged for storage during the previous epoch (in MIST). */\n  storageCharge: Scalars['BigInt']['output'];\n  /** The SUI returned to transaction senders for cleaning up objects (in MIST). */\n  storageRebate: Scalars['BigInt']['output'];\n  /**\n   * System packages (specifically framework and move stdlib) that are written before the new\n   * epoch starts, to upgrade them on-chain. Validators write these packages out when running the\n   * transaction.\n   */\n  systemPackages: MovePackageConnection;\n};\n\n\n/**\n * A system transaction that updates epoch information on-chain (increments the current epoch).\n * Executed by the system once per epoch, without using gas. Epoch change transactions cannot be\n * submitted by users, because validators will refuse to sign them.\n *\n * This transaction kind is deprecated in favour of `EndOfEpochTransaction`.\n */\nexport type ChangeEpochTransactionSystemPackagesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n/**\n * Checkpoints contain finalized transactions and are used for node synchronization\n * and global transaction ordering.\n */\nexport type Checkpoint = {\n  __typename?: 'Checkpoint';\n  /** The Base64 serialized BCS bytes of CheckpointSummary for this checkpoint. */\n  bcs?: Maybe<Scalars['Base64']['output']>;\n  /**\n   * A 32-byte hash that uniquely identifies the checkpoint contents, encoded in Base58. This\n   * hash can be used to verify checkpoint contents by checking signatures against the committee,\n   * Hashing contents to match digest, and checking that the previous checkpoint digest matches.\n   */\n  digest: Scalars['String']['output'];\n  /** The epoch this checkpoint is part of. */\n  epoch?: Maybe<Epoch>;\n  /** The total number of transaction blocks in the network by the end of this checkpoint. */\n  networkTotalTransactions?: Maybe<Scalars['UInt53']['output']>;\n  /** The digest of the checkpoint at the previous sequence number. */\n  previousCheckpointDigest?: Maybe<Scalars['String']['output']>;\n  /**\n   * The computation cost, storage cost, storage rebate, and non-refundable storage fee\n   * accumulated during this epoch, up to and including this checkpoint. These values increase\n   * monotonically across checkpoints in the same epoch, and reset on epoch boundaries.\n   */\n  rollingGasSummary?: Maybe<GasCostSummary>;\n  /**\n   * This checkpoint's position in the total order of finalized checkpoints, agreed upon by\n   * consensus.\n   */\n  sequenceNumber: Scalars['UInt53']['output'];\n  /**\n   * The timestamp at which the checkpoint is agreed to have happened according to consensus.\n   * Transactions that access time in this checkpoint will observe this timestamp.\n   */\n  timestamp: Scalars['DateTime']['output'];\n  /**\n   * Transactions in this checkpoint.\n   *\n   * `scanLimit` restricts the number of candidate transactions scanned when gathering a page of\n   * results. It is required for queries that apply more than two complex filters (on function,\n   * kind, sender, recipient, input object, changed object, or ids), and can be at most\n   * `serviceConfig.maxScanLimit`.\n   *\n   * When the scan limit is reached the page will be returned even if it has fewer than `first`\n   * results when paginating forward (`last` when paginating backwards). If there are more\n   * transactions to scan, `pageInfo.hasNextPage` (or `pageInfo.hasPreviousPage`) will be set to\n   * `true`, and `PageInfo.endCursor` (or `PageInfo.startCursor`) will be set to the last\n   * transaction that was scanned as opposed to the last (or first) transaction in the page.\n   *\n   * Requesting the next (or previous) page after this cursor will resume the search, scanning\n   * the next `scanLimit` many transactions in the direction of pagination, and so on until all\n   * transactions in the scanning range have been visited.\n   *\n   * By default, the scanning range consists of all transactions in this checkpoint.\n   */\n  transactionBlocks: TransactionBlockConnection;\n  /**\n   * This is an aggregation of signatures from a quorum of validators for the checkpoint\n   * proposal.\n   */\n  validatorSignatures: Scalars['Base64']['output'];\n};\n\n\n/**\n * Checkpoints contain finalized transactions and are used for node synchronization\n * and global transaction ordering.\n */\nexport type CheckpointTransactionBlocksArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<TransactionBlockFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  scanLimit?: InputMaybe<Scalars['Int']['input']>;\n};\n\nexport type CheckpointConnection = {\n  __typename?: 'CheckpointConnection';\n  /** A list of edges. */\n  edges: Array<CheckpointEdge>;\n  /** A list of nodes. */\n  nodes: Array<Checkpoint>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type CheckpointEdge = {\n  __typename?: 'CheckpointEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: Checkpoint;\n};\n\n/** Filter either by the digest, or the sequence number, or neither, to get the latest checkpoint. */\nexport type CheckpointId = {\n  digest?: InputMaybe<Scalars['String']['input']>;\n  sequenceNumber?: InputMaybe<Scalars['UInt53']['input']>;\n};\n\n/** Some 0x2::coin::Coin Move object. */\nexport type Coin = IMoveObject & IObject & IOwner & {\n  __typename?: 'Coin';\n  address: Scalars['SuiAddress']['output'];\n  /**\n   * Total balance of all coins with marker type owned by this object. If type is not supplied,\n   * it defaults to `0x2::sui::SUI`.\n   */\n  balance?: Maybe<Balance>;\n  /** The balances of all coin types owned by this object. */\n  balances: BalanceConnection;\n  /** The Base64-encoded BCS serialization of the object's content. */\n  bcs?: Maybe<Scalars['Base64']['output']>;\n  /** Balance of this coin object. */\n  coinBalance?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * The coin objects for this object.\n   *\n   * `type` is a filter on the coin's type parameter, defaulting to `0x2::sui::SUI`.\n   */\n  coins: CoinConnection;\n  /**\n   * Displays the contents of the Move object in a JSON string and through GraphQL types. Also\n   * provides the flat representation of the type signature, and the BCS of the corresponding\n   * data.\n   */\n  contents?: Maybe<MoveValue>;\n  /** The domain explicitly configured as the default domain pointing to this object. */\n  defaultSuinsName?: Maybe<Scalars['String']['output']>;\n  /** 32-byte hash that identifies the object's contents, encoded as a Base58 string. */\n  digest?: Maybe<Scalars['String']['output']>;\n  /**\n   * The set of named templates defined on-chain for the type of this object, to be handled\n   * off-chain. The server substitutes data from the object into these templates to generate a\n   * display string per template.\n   */\n  display?: Maybe<Array<DisplayEntry>>;\n  /**\n   * Access a dynamic field on an object using its name. Names are arbitrary Move values whose\n   * type have `copy`, `drop`, and `store`, and are specified using their type, and their BCS\n   * contents, Base64 encoded.\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicField?: Maybe<DynamicField>;\n  /**\n   * The dynamic fields and dynamic object fields on an object.\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicFields: DynamicFieldConnection;\n  /**\n   * Access a dynamic object field on an object using its name. Names are arbitrary Move values\n   * whose type have `copy`, `drop`, and `store`, and are specified using their type, and their\n   * BCS contents, Base64 encoded. The value of a dynamic object field can also be accessed\n   * off-chain directly via its address (e.g. using `Query.object`).\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicObjectField?: Maybe<DynamicField>;\n  /**\n   * Determines whether a transaction can transfer this object, using the TransferObjects\n   * transaction command or `sui::transfer::public_transfer`, both of which require the object to\n   * have the `key` and `store` abilities.\n   */\n  hasPublicTransfer: Scalars['Boolean']['output'];\n  /** Objects owned by this object, optionally `filter`-ed. */\n  objects: MoveObjectConnection;\n  /** The owner type of this object: Immutable, Shared, Parent, Address */\n  owner?: Maybe<ObjectOwner>;\n  /** The transaction block that created this version of the object. */\n  previousTransactionBlock?: Maybe<TransactionBlock>;\n  /**\n   * The transaction blocks that sent objects to this object.\n   *\n   * `scanLimit` restricts the number of candidate transactions scanned when gathering a page of\n   * results. It is required for queries that apply more than two complex filters (on function,\n   * kind, sender, recipient, input object, changed object, or ids), and can be at most\n   * `serviceConfig.maxScanLimit`.\n   *\n   * When the scan limit is reached the page will be returned even if it has fewer than `first`\n   * results when paginating forward (`last` when paginating backwards). If there are more\n   * transactions to scan, `pageInfo.hasNextPage` (or `pageInfo.hasPreviousPage`) will be set to\n   * `true`, and `PageInfo.endCursor` (or `PageInfo.startCursor`) will be set to the last\n   * transaction that was scanned as opposed to the last (or first) transaction in the page.\n   *\n   * Requesting the next (or previous) page after this cursor will resume the search, scanning\n   * the next `scanLimit` many transactions in the direction of pagination, and so on until all\n   * transactions in the scanning range have been visited.\n   *\n   * By default, the scanning range includes all transactions known to GraphQL, but it can be\n   * restricted by the `after` and `before` cursors, and the `beforeCheckpoint`,\n   * `afterCheckpoint` and `atCheckpoint` filters.\n   */\n  receivedTransactionBlocks: TransactionBlockConnection;\n  /** The `0x3::staking_pool::StakedSui` objects owned by this object. */\n  stakedSuis: StakedSuiConnection;\n  /**\n   * The current status of the object as read from the off-chain store. The possible states are:\n   * NOT_INDEXED, the object is loaded from serialized data, such as the contents of a genesis or\n   * system package upgrade transaction. LIVE, the version returned is the most recent for the\n   * object, and it is not deleted or wrapped at that version. HISTORICAL, the object was\n   * referenced at a specific version or checkpoint, so is fetched from historical tables and may\n   * not be the latest version of the object. WRAPPED_OR_DELETED, the object is deleted or\n   * wrapped and only partial information can be loaded.\"\n   */\n  status: ObjectKind;\n  /**\n   * The amount of SUI we would rebate if this object gets deleted or mutated. This number is\n   * recalculated based on the present storage gas price.\n   */\n  storageRebate?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * The SuinsRegistration NFTs owned by this object. These grant the owner the capability to\n   * manage the associated domain.\n   */\n  suinsRegistrations: SuinsRegistrationConnection;\n  version: Scalars['UInt53']['output'];\n};\n\n\n/** Some 0x2::coin::Coin Move object. */\nexport type CoinBalanceArgs = {\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/** Some 0x2::coin::Coin Move object. */\nexport type CoinBalancesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** Some 0x2::coin::Coin Move object. */\nexport type CoinCoinsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/** Some 0x2::coin::Coin Move object. */\nexport type CoinDefaultSuinsNameArgs = {\n  format?: InputMaybe<DomainFormat>;\n};\n\n\n/** Some 0x2::coin::Coin Move object. */\nexport type CoinDynamicFieldArgs = {\n  name: DynamicFieldName;\n};\n\n\n/** Some 0x2::coin::Coin Move object. */\nexport type CoinDynamicFieldsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** Some 0x2::coin::Coin Move object. */\nexport type CoinDynamicObjectFieldArgs = {\n  name: DynamicFieldName;\n};\n\n\n/** Some 0x2::coin::Coin Move object. */\nexport type CoinObjectsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<ObjectFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** Some 0x2::coin::Coin Move object. */\nexport type CoinReceivedTransactionBlocksArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<TransactionBlockFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  scanLimit?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** Some 0x2::coin::Coin Move object. */\nexport type CoinStakedSuisArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** Some 0x2::coin::Coin Move object. */\nexport type CoinSuinsRegistrationsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\nexport type CoinConnection = {\n  __typename?: 'CoinConnection';\n  /** A list of edges. */\n  edges: Array<CoinEdge>;\n  /** A list of nodes. */\n  nodes: Array<Coin>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\nexport type CoinDenyListStateCreateTransaction = {\n  __typename?: 'CoinDenyListStateCreateTransaction';\n  /** A workaround to define an empty variant of a GraphQL union. */\n  _?: Maybe<Scalars['Boolean']['output']>;\n};\n\n/** An edge in a connection. */\nexport type CoinEdge = {\n  __typename?: 'CoinEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: Coin;\n};\n\n/** The metadata for a coin type. */\nexport type CoinMetadata = IMoveObject & IObject & IOwner & {\n  __typename?: 'CoinMetadata';\n  address: Scalars['SuiAddress']['output'];\n  /**\n   * Total balance of all coins with marker type owned by this object. If type is not supplied,\n   * it defaults to `0x2::sui::SUI`.\n   */\n  balance?: Maybe<Balance>;\n  /** The balances of all coin types owned by this object. */\n  balances: BalanceConnection;\n  /** The Base64-encoded BCS serialization of the object's content. */\n  bcs?: Maybe<Scalars['Base64']['output']>;\n  /**\n   * The coin objects for this object.\n   *\n   * `type` is a filter on the coin's type parameter, defaulting to `0x2::sui::SUI`.\n   */\n  coins: CoinConnection;\n  /**\n   * Displays the contents of the Move object in a JSON string and through GraphQL types. Also\n   * provides the flat representation of the type signature, and the BCS of the corresponding\n   * data.\n   */\n  contents?: Maybe<MoveValue>;\n  /** The number of decimal places used to represent the token. */\n  decimals?: Maybe<Scalars['Int']['output']>;\n  /** The domain explicitly configured as the default domain pointing to this object. */\n  defaultSuinsName?: Maybe<Scalars['String']['output']>;\n  /** Optional description of the token, provided by the creator of the token. */\n  description?: Maybe<Scalars['String']['output']>;\n  /** 32-byte hash that identifies the object's contents, encoded as a Base58 string. */\n  digest?: Maybe<Scalars['String']['output']>;\n  /**\n   * The set of named templates defined on-chain for the type of this object, to be handled\n   * off-chain. The server substitutes data from the object into these templates to generate a\n   * display string per template.\n   */\n  display?: Maybe<Array<DisplayEntry>>;\n  /**\n   * Access a dynamic field on an object using its name. Names are arbitrary Move values whose\n   * type have `copy`, `drop`, and `store`, and are specified using their type, and their BCS\n   * contents, Base64 encoded.\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicField?: Maybe<DynamicField>;\n  /**\n   * The dynamic fields and dynamic object fields on an object.\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicFields: DynamicFieldConnection;\n  /**\n   * Access a dynamic object field on an object using its name. Names are arbitrary Move values\n   * whose type have `copy`, `drop`, and `store`, and are specified using their type, and their\n   * BCS contents, Base64 encoded. The value of a dynamic object field can also be accessed\n   * off-chain directly via its address (e.g. using `Query.object`).\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicObjectField?: Maybe<DynamicField>;\n  /**\n   * Determines whether a transaction can transfer this object, using the TransferObjects\n   * transaction command or `sui::transfer::public_transfer`, both of which require the object to\n   * have the `key` and `store` abilities.\n   */\n  hasPublicTransfer: Scalars['Boolean']['output'];\n  iconUrl?: Maybe<Scalars['String']['output']>;\n  /** Full, official name of the token. */\n  name?: Maybe<Scalars['String']['output']>;\n  /** Objects owned by this object, optionally `filter`-ed. */\n  objects: MoveObjectConnection;\n  /** The owner type of this object: Immutable, Shared, Parent, Address */\n  owner?: Maybe<ObjectOwner>;\n  /** The transaction block that created this version of the object. */\n  previousTransactionBlock?: Maybe<TransactionBlock>;\n  /**\n   * The transaction blocks that sent objects to this object.\n   *\n   * `scanLimit` restricts the number of candidate transactions scanned when gathering a page of\n   * results. It is required for queries that apply more than two complex filters (on function,\n   * kind, sender, recipient, input object, changed object, or ids), and can be at most\n   * `serviceConfig.maxScanLimit`.\n   *\n   * When the scan limit is reached the page will be returned even if it has fewer than `first`\n   * results when paginating forward (`last` when paginating backwards). If there are more\n   * transactions to scan, `pageInfo.hasNextPage` (or `pageInfo.hasPreviousPage`) will be set to\n   * `true`, and `PageInfo.endCursor` (or `PageInfo.startCursor`) will be set to the last\n   * transaction that was scanned as opposed to the last (or first) transaction in the page.\n   *\n   * Requesting the next (or previous) page after this cursor will resume the search, scanning\n   * the next `scanLimit` many transactions in the direction of pagination, and so on until all\n   * transactions in the scanning range have been visited.\n   *\n   * By default, the scanning range includes all transactions known to GraphQL, but it can be\n   * restricted by the `after` and `before` cursors, and the `beforeCheckpoint`,\n   * `afterCheckpoint` and `atCheckpoint` filters.\n   */\n  receivedTransactionBlocks: TransactionBlockConnection;\n  /** The `0x3::staking_pool::StakedSui` objects owned by this object. */\n  stakedSuis: StakedSuiConnection;\n  /**\n   * The current status of the object as read from the off-chain store. The possible states are:\n   * NOT_INDEXED, the object is loaded from serialized data, such as the contents of a genesis or\n   * system package upgrade transaction. LIVE, the version returned is the most recent for the\n   * object, and it is not deleted or wrapped at that version. HISTORICAL, the object was\n   * referenced at a specific version or checkpoint, so is fetched from historical tables and may\n   * not be the latest version of the object. WRAPPED_OR_DELETED, the object is deleted or\n   * wrapped and only partial information can be loaded.\"\n   */\n  status: ObjectKind;\n  /**\n   * The amount of SUI we would rebate if this object gets deleted or mutated. This number is\n   * recalculated based on the present storage gas price.\n   */\n  storageRebate?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * The SuinsRegistration NFTs owned by this object. These grant the owner the capability to\n   * manage the associated domain.\n   */\n  suinsRegistrations: SuinsRegistrationConnection;\n  /** The overall quantity of tokens that will be issued. */\n  supply?: Maybe<Scalars['BigInt']['output']>;\n  /** The token's identifying abbreviation. */\n  symbol?: Maybe<Scalars['String']['output']>;\n  version: Scalars['UInt53']['output'];\n};\n\n\n/** The metadata for a coin type. */\nexport type CoinMetadataBalanceArgs = {\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/** The metadata for a coin type. */\nexport type CoinMetadataBalancesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** The metadata for a coin type. */\nexport type CoinMetadataCoinsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/** The metadata for a coin type. */\nexport type CoinMetadataDefaultSuinsNameArgs = {\n  format?: InputMaybe<DomainFormat>;\n};\n\n\n/** The metadata for a coin type. */\nexport type CoinMetadataDynamicFieldArgs = {\n  name: DynamicFieldName;\n};\n\n\n/** The metadata for a coin type. */\nexport type CoinMetadataDynamicFieldsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** The metadata for a coin type. */\nexport type CoinMetadataDynamicObjectFieldArgs = {\n  name: DynamicFieldName;\n};\n\n\n/** The metadata for a coin type. */\nexport type CoinMetadataObjectsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<ObjectFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** The metadata for a coin type. */\nexport type CoinMetadataReceivedTransactionBlocksArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<TransactionBlockFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  scanLimit?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** The metadata for a coin type. */\nexport type CoinMetadataStakedSuisArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** The metadata for a coin type. */\nexport type CoinMetadataSuinsRegistrationsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n/** Same as AddressOwner, but the object is versioned by consensus. */\nexport type ConsensusAddressOwner = {\n  __typename?: 'ConsensusAddressOwner';\n  owner?: Maybe<Owner>;\n  startVersion: Scalars['UInt53']['output'];\n};\n\n/**\n * System transaction that runs at the beginning of a checkpoint, and is responsible for setting\n * the current value of the clock, based on the timestamp from consensus.\n */\nexport type ConsensusCommitPrologueTransaction = {\n  __typename?: 'ConsensusCommitPrologueTransaction';\n  /** Unix timestamp from consensus. */\n  commitTimestamp: Scalars['DateTime']['output'];\n  /**\n   * Digest of consensus output, encoded as a Base58 string (only available from V2 of the\n   * transaction).\n   */\n  consensusCommitDigest?: Maybe<Scalars['String']['output']>;\n  /** Epoch of the commit prologue transaction. */\n  epoch?: Maybe<Epoch>;\n  /** Consensus round of the commit. */\n  round: Scalars['UInt53']['output'];\n};\n\nexport type DependencyConnection = {\n  __typename?: 'DependencyConnection';\n  /** A list of edges. */\n  edges: Array<DependencyEdge>;\n  /** A list of nodes. */\n  nodes: Array<TransactionBlock>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type DependencyEdge = {\n  __typename?: 'DependencyEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node?: Maybe<TransactionBlock>;\n};\n\n/**\n * The set of named templates defined on-chain for the type of this object,\n * to be handled off-chain. The server substitutes data from the object\n * into these templates to generate a display string per template.\n */\nexport type DisplayEntry = {\n  __typename?: 'DisplayEntry';\n  /** An error string describing why the template could not be rendered. */\n  error?: Maybe<Scalars['String']['output']>;\n  /** The identifier for a particular template string of the Display object. */\n  key: Scalars['String']['output'];\n  /** The template string for the key with placeholder values substituted. */\n  value?: Maybe<Scalars['String']['output']>;\n};\n\nexport enum DomainFormat {\n  At = 'AT',\n  Dot = 'DOT'\n}\n\nexport type DryRunEffect = {\n  __typename?: 'DryRunEffect';\n  /** Changes made to arguments that were mutably borrowed by each command in this transaction. */\n  mutatedReferences?: Maybe<Array<DryRunMutation>>;\n  /** Return results of each command in this transaction. */\n  returnValues?: Maybe<Array<DryRunReturn>>;\n};\n\nexport type DryRunMutation = {\n  __typename?: 'DryRunMutation';\n  bcs: Scalars['Base64']['output'];\n  input: TransactionArgument;\n  type: MoveType;\n};\n\nexport type DryRunResult = {\n  __typename?: 'DryRunResult';\n  /** The error that occurred during dry run execution, if any. */\n  error?: Maybe<Scalars['String']['output']>;\n  /**\n   * The intermediate results for each command of the dry run execution, including\n   * contents of mutated references and return values.\n   */\n  results?: Maybe<Array<DryRunEffect>>;\n  /** The transaction block representing the dry run execution. */\n  transaction?: Maybe<TransactionBlock>;\n};\n\nexport type DryRunReturn = {\n  __typename?: 'DryRunReturn';\n  bcs: Scalars['Base64']['output'];\n  type: MoveType;\n};\n\n/**\n * Dynamic fields are heterogeneous fields that can be added or removed at runtime,\n * and can have arbitrary user-assigned names. There are two sub-types of dynamic\n * fields:\n *\n * 1) Dynamic Fields can store any value that has the `store` ability, however an object\n * stored in this kind of field will be considered wrapped and will not be accessible\n * directly via its ID by external tools (explorers, wallets, etc) accessing storage.\n * 2) Dynamic Object Fields values must be Sui objects (have the `key` and `store`\n * abilities, and id: UID as the first field), but will still be directly accessible off-chain\n * via their object ID after being attached.\n */\nexport type DynamicField = {\n  __typename?: 'DynamicField';\n  /**\n   * The string type, data, and serialized value of the DynamicField's 'name' field.\n   * This field is used to uniquely identify a child of the parent object.\n   */\n  name?: Maybe<MoveValue>;\n  /**\n   * The returned dynamic field is an object if its return type is `MoveObject`,\n   * in which case it is also accessible off-chain via its address. Its contents\n   * will be from the latest version that is at most equal to its parent object's\n   * version\n   */\n  value?: Maybe<DynamicFieldValue>;\n};\n\nexport type DynamicFieldConnection = {\n  __typename?: 'DynamicFieldConnection';\n  /** A list of edges. */\n  edges: Array<DynamicFieldEdge>;\n  /** A list of nodes. */\n  nodes: Array<DynamicField>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type DynamicFieldEdge = {\n  __typename?: 'DynamicFieldEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: DynamicField;\n};\n\nexport type DynamicFieldName = {\n  /** The Base64 encoded bcs serialization of the DynamicField's 'name' field. */\n  bcs: Scalars['Base64']['input'];\n  /**\n   * The string type of the DynamicField's 'name' field.\n   * A string representation of a Move primitive like 'u64', or a struct type like '0x2::kiosk::Listing'\n   */\n  type: Scalars['String']['input'];\n};\n\nexport type DynamicFieldValue = MoveObject | MoveValue;\n\n/**\n * System transaction that supersedes `ChangeEpochTransaction` as the new way to run transactions\n * at the end of an epoch. Behaves similarly to `ChangeEpochTransaction` but can accommodate other\n * optional transactions to run at the end of the epoch.\n */\nexport type EndOfEpochTransaction = {\n  __typename?: 'EndOfEpochTransaction';\n  /** The list of system transactions that are allowed to run at the end of the epoch. */\n  transactions: EndOfEpochTransactionKindConnection;\n};\n\n\n/**\n * System transaction that supersedes `ChangeEpochTransaction` as the new way to run transactions\n * at the end of an epoch. Behaves similarly to `ChangeEpochTransaction` but can accommodate other\n * optional transactions to run at the end of the epoch.\n */\nexport type EndOfEpochTransactionTransactionsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\nexport type EndOfEpochTransactionKind = AuthenticatorStateCreateTransaction | AuthenticatorStateExpireTransaction | BridgeCommitteeInitTransaction | BridgeStateCreateTransaction | ChangeEpochTransaction | CoinDenyListStateCreateTransaction | RandomnessStateCreateTransaction | StoreExecutionTimeObservationsTransaction;\n\nexport type EndOfEpochTransactionKindConnection = {\n  __typename?: 'EndOfEpochTransactionKindConnection';\n  /** A list of edges. */\n  edges: Array<EndOfEpochTransactionKindEdge>;\n  /** A list of nodes. */\n  nodes: Array<EndOfEpochTransactionKind>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type EndOfEpochTransactionKindEdge = {\n  __typename?: 'EndOfEpochTransactionKindEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: EndOfEpochTransactionKind;\n};\n\n/**\n * Operation of the Sui network is temporally partitioned into non-overlapping epochs,\n * and the network aims to keep epochs roughly the same duration as each other.\n * During a particular epoch the following data is fixed:\n *\n * - the protocol version\n * - the reference gas price\n * - the set of participating validators\n */\nexport type Epoch = {\n  __typename?: 'Epoch';\n  /** The epoch's corresponding checkpoints. */\n  checkpoints: CheckpointConnection;\n  /** The epoch's ending timestamp. */\n  endTimestamp?: Maybe<Scalars['DateTime']['output']>;\n  /** The epoch's id as a sequence number that starts at 0 and is incremented by one at every epoch change. */\n  epochId: Scalars['UInt53']['output'];\n  /** The storage fees paid for transactions executed during the epoch. */\n  fundInflow?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * The storage fee rebates paid to users who deleted the data associated with past\n   * transactions.\n   */\n  fundOutflow?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * The storage fund available in this epoch.\n   * This fund is used to redistribute storage fees from past transactions\n   * to future validators.\n   */\n  fundSize?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * A commitment by the committee at the end of epoch on the contents of the live object set at\n   * that time. This can be used to verify state snapshots.\n   */\n  liveObjectSetDigest?: Maybe<Scalars['String']['output']>;\n  /**\n   * The difference between the fund inflow and outflow, representing\n   * the net amount of storage fees accumulated in this epoch.\n   */\n  netInflow?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * The epoch's corresponding protocol configuration, including the feature flags and the\n   * configuration options.\n   */\n  protocolConfigs: ProtocolConfigs;\n  /** The minimum gas price that a quorum of validators are guaranteed to sign a transaction for. */\n  referenceGasPrice?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * Information about whether this epoch was started in safe mode, which happens if the full epoch\n   * change logic fails for some reason.\n   */\n  safeMode?: Maybe<SafeMode>;\n  /** The epoch's starting timestamp. */\n  startTimestamp: Scalars['DateTime']['output'];\n  /**\n   * SUI set aside to account for objects stored on-chain, at the start of the epoch.\n   * This is also used for storage rebates.\n   */\n  storageFund?: Maybe<StorageFund>;\n  /** Details of the system that are decided during genesis. */\n  systemParameters?: Maybe<SystemParameters>;\n  /** Parameters related to the subsidy that supplements staking rewards */\n  systemStakeSubsidy?: Maybe<StakeSubsidy>;\n  /**\n   * The value of the `version` field of `0x5`, the `0x3::sui::SuiSystemState` object.  This\n   * version changes whenever the fields contained in the system state object (held in a dynamic\n   * field attached to `0x5`) change.\n   */\n  systemStateVersion?: Maybe<Scalars['UInt53']['output']>;\n  /** The total number of checkpoints in this epoch. */\n  totalCheckpoints?: Maybe<Scalars['UInt53']['output']>;\n  /** The total amount of gas fees (in MIST) that were paid in this epoch. */\n  totalGasFees?: Maybe<Scalars['BigInt']['output']>;\n  /** The total MIST rewarded as stake. */\n  totalStakeRewards?: Maybe<Scalars['BigInt']['output']>;\n  /** The amount added to total gas fees to make up the total stake rewards. */\n  totalStakeSubsidies?: Maybe<Scalars['BigInt']['output']>;\n  /** The total number of transaction blocks in this epoch. */\n  totalTransactions?: Maybe<Scalars['UInt53']['output']>;\n  /**\n   * The epoch's corresponding transaction blocks.\n   *\n   * `scanLimit` restricts the number of candidate transactions scanned when gathering a page of\n   * results. It is required for queries that apply more than two complex filters (on function,\n   * kind, sender, recipient, input object, changed object, or ids), and can be at most\n   * `serviceConfig.maxScanLimit`.\n   *\n   * When the scan limit is reached the page will be returned even if it has fewer than `first`\n   * results when paginating forward (`last` when paginating backwards). If there are more\n   * transactions to scan, `pageInfo.hasNextPage` (or `pageInfo.hasPreviousPage`) will be set to\n   * `true`, and `PageInfo.endCursor` (or `PageInfo.startCursor`) will be set to the last\n   * transaction that was scanned as opposed to the last (or first) transaction in the page.\n   *\n   * Requesting the next (or previous) page after this cursor will resume the search, scanning\n   * the next `scanLimit` many transactions in the direction of pagination, and so on until all\n   * transactions in the scanning range have been visited.\n   *\n   * By default, the scanning range consists of all transactions in this epoch.\n   */\n  transactionBlocks: TransactionBlockConnection;\n  /** Validator related properties, including the active validators. */\n  validatorSet?: Maybe<ValidatorSet>;\n};\n\n\n/**\n * Operation of the Sui network is temporally partitioned into non-overlapping epochs,\n * and the network aims to keep epochs roughly the same duration as each other.\n * During a particular epoch the following data is fixed:\n *\n * - the protocol version\n * - the reference gas price\n * - the set of participating validators\n */\nexport type EpochCheckpointsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * Operation of the Sui network is temporally partitioned into non-overlapping epochs,\n * and the network aims to keep epochs roughly the same duration as each other.\n * During a particular epoch the following data is fixed:\n *\n * - the protocol version\n * - the reference gas price\n * - the set of participating validators\n */\nexport type EpochTransactionBlocksArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<TransactionBlockFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  scanLimit?: InputMaybe<Scalars['Int']['input']>;\n};\n\nexport type EpochConnection = {\n  __typename?: 'EpochConnection';\n  /** A list of edges. */\n  edges: Array<EpochEdge>;\n  /** A list of nodes. */\n  nodes: Array<Epoch>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type EpochEdge = {\n  __typename?: 'EpochEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: Epoch;\n};\n\nexport type Event = {\n  __typename?: 'Event';\n  /** The Base64 encoded BCS serialized bytes of the event. */\n  bcs: Scalars['Base64']['output'];\n  /** The event's contents as a Move value. */\n  contents: MoveValue;\n  /** Address of the sender of the event */\n  sender?: Maybe<Address>;\n  /**\n   * The Move module containing some function that when called by\n   * a programmable transaction block (PTB) emitted this event.\n   * For example, if a PTB invokes A::m1::foo, which internally\n   * calls A::m2::emit_event to emit an event,\n   * the sending module would be A::m1.\n   */\n  sendingModule?: Maybe<MoveModule>;\n  /** UTC timestamp in milliseconds since epoch (1/1/1970) */\n  timestamp?: Maybe<Scalars['DateTime']['output']>;\n  /**\n   * The transaction block that emitted this event. This information is only available for\n   * events from indexed transactions, and not from transactions that have just been executed or\n   * dry-run.\n   */\n  transactionBlock?: Maybe<TransactionBlock>;\n};\n\nexport type EventConnection = {\n  __typename?: 'EventConnection';\n  /** A list of edges. */\n  edges: Array<EventEdge>;\n  /** A list of nodes. */\n  nodes: Array<Event>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type EventEdge = {\n  __typename?: 'EventEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: Event;\n};\n\nexport type EventFilter = {\n  /**\n   * Events emitted by a particular module. An event is emitted by a\n   * particular module if some function in the module is called by a\n   * PTB and emits an event.\n   *\n   * Modules can be filtered by their package, or package::module.\n   * We currently do not support filtering by emitting module and event type\n   * at the same time so if both are provided in one filter, the query will error.\n   */\n  emittingModule?: InputMaybe<Scalars['String']['input']>;\n  /**\n   * This field is used to specify the type of event emitted.\n   *\n   * Events can be filtered by their type's package, package::module,\n   * or their fully qualified type name.\n   *\n   * Generic types can be queried by either the generic type name, e.g.\n   * `0x2::coin::Coin`, or by the full type name, such as\n   * `0x2::coin::Coin<0x2::sui::SUI>`.\n   */\n  eventType?: InputMaybe<Scalars['String']['input']>;\n  /** Filter down to events from transactions sent by this address. */\n  sender?: InputMaybe<Scalars['SuiAddress']['input']>;\n  /** Filter down to the events from this transaction (given by its transaction digest). */\n  transactionDigest?: InputMaybe<Scalars['String']['input']>;\n};\n\n/** The result of an execution, including errors that occurred during said execution. */\nexport type ExecutionResult = {\n  __typename?: 'ExecutionResult';\n  /**\n   * The effects of the executed transaction. Since the transaction was just executed\n   * and not indexed yet, fields including `balance_changes`, `timestamp` and `checkpoint`\n   * are not available.\n   */\n  effects: TransactionBlockEffects;\n  /** The errors field captures any errors that occurred during execution */\n  errors?: Maybe<Array<Scalars['String']['output']>>;\n};\n\n/** The execution status of this transaction block: success or failure. */\nexport enum ExecutionStatus {\n  /** The transaction block could not be executed */\n  Failure = 'FAILURE',\n  /** The transaction block was successfully executed */\n  Success = 'SUCCESS'\n}\n\n/**\n * Groups of features served by the RPC service.  The GraphQL Service can be configured to enable\n * or disable these features.\n */\nexport enum Feature {\n  /** Statistics about how the network was running (TPS, top packages, APY, etc) */\n  Analytics = 'ANALYTICS',\n  /** Coin metadata, per-address coin and balance information. */\n  Coins = 'COINS',\n  /** Querying an object's dynamic fields. */\n  DynamicFields = 'DYNAMIC_FIELDS',\n  /** Named packages service (utilizing dotmove package registry). */\n  MoveRegistry = 'MOVE_REGISTRY',\n  /** SuiNS name and reverse name look-up. */\n  NameService = 'NAME_SERVICE',\n  /** Transaction and Event subscriptions. */\n  Subscriptions = 'SUBSCRIPTIONS',\n  /**\n   * Aspects that affect the running of the system that are managed by the\n   * validators either directly, or through system transactions.\n   */\n  SystemState = 'SYSTEM_STATE'\n}\n\n/**\n * Access to the gas inputs, after they have been smashed into one coin. The gas coin can only be\n * used by reference, except for with `TransferObjectsTransaction` that can accept it by value.\n */\nexport type GasCoin = {\n  __typename?: 'GasCoin';\n  /** A workaround to define an empty variant of a GraphQL union. */\n  _?: Maybe<Scalars['Boolean']['output']>;\n};\n\n/** Breakdown of gas costs in effects. */\nexport type GasCostSummary = {\n  __typename?: 'GasCostSummary';\n  /** Gas paid for executing this transaction (in MIST). */\n  computationCost?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * Part of storage cost that is not reclaimed when data created by this transaction is cleaned\n   * up (in MIST).\n   */\n  nonRefundableStorageFee?: Maybe<Scalars['BigInt']['output']>;\n  /** Gas paid for the data stored on-chain by this transaction (in MIST). */\n  storageCost?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * Part of storage cost that can be reclaimed by cleaning up data created by this transaction\n   * (when objects are deleted or an object is modified, which is treated as a deletion followed\n   * by a creation) (in MIST).\n   */\n  storageRebate?: Maybe<Scalars['BigInt']['output']>;\n};\n\n/** Effects related to gas (costs incurred and the identity of the smashed gas object returned). */\nexport type GasEffects = {\n  __typename?: 'GasEffects';\n  gasObject?: Maybe<Object>;\n  gasSummary?: Maybe<GasCostSummary>;\n};\n\n/** Configuration for this transaction's gas price and the coins used to pay for gas. */\nexport type GasInput = {\n  __typename?: 'GasInput';\n  /** The maximum number of gas units that can be expended by executing this transaction */\n  gasBudget?: Maybe<Scalars['BigInt']['output']>;\n  /** Objects used to pay for a transaction's execution and storage */\n  gasPayment: ObjectConnection;\n  /**\n   * An unsigned integer specifying the number of native tokens per gas unit this transaction\n   * will pay (in MIST).\n   */\n  gasPrice?: Maybe<Scalars['BigInt']['output']>;\n  /** Address of the owner of the gas object(s) used */\n  gasSponsor?: Maybe<Address>;\n};\n\n\n/** Configuration for this transaction's gas price and the coins used to pay for gas. */\nexport type GasInputGasPaymentArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n/** System transaction that initializes the network and writes the initial set of objects on-chain. */\nexport type GenesisTransaction = {\n  __typename?: 'GenesisTransaction';\n  /** Objects to be created during genesis. */\n  objects: ObjectConnection;\n};\n\n\n/** System transaction that initializes the network and writes the initial set of objects on-chain. */\nexport type GenesisTransactionObjectsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n/**\n * Interface implemented by all GraphQL types that represent a Move datatype (either structs or\n * enums). This interface is used to provide a way to access fields that are shared by both\n * structs and enums, e.g., the module that the datatype belongs to, the name of the datatype,\n * type parameters etc.\n */\nexport type IMoveDatatype = {\n  /** The abilities of the datatype. */\n  abilities?: Maybe<Array<MoveAbility>>;\n  /** The module that the datatype belongs to. */\n  module: MoveModule;\n  /** The name of the datatype. */\n  name: Scalars['String']['output'];\n  /** The type parameters of the datatype. */\n  typeParameters?: Maybe<Array<MoveStructTypeParameter>>;\n};\n\n/**\n * This interface is implemented by types that represent a Move object on-chain (A Move value whose\n * type has `key`).\n */\nexport type IMoveObject = {\n  /** Displays the contents of the Move object in a JSON string and through GraphQL types. Also provides the flat representation of the type signature, and the BCS of the corresponding data. */\n  contents?: Maybe<MoveValue>;\n  /** The set of named templates defined on-chain for the type of this object, to be handled off-chain. The server substitutes data from the object into these templates to generate a display string per template. */\n  display?: Maybe<Array<DisplayEntry>>;\n  /**\n   * Access a dynamic field on an object using its name. Names are arbitrary Move values whose type have `copy`, `drop`, and `store`, and are specified using their type, and their BCS contents, Base64 encoded.\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Ownertype.\n   */\n  dynamicField?: Maybe<DynamicField>;\n  /**\n   * The dynamic fields and dynamic object fields on an object.\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner type.\n   */\n  dynamicFields: DynamicFieldConnection;\n  /**\n   * Access a dynamic object field on an object using its name. Names are arbitrary Move values whose type have `copy`, `drop`, and `store`, and are specified using their type, and their BCS contents, Base64 encoded. The value of a dynamic object field can also be accessed off-chain directly via its address (e.g. using `Query.object`).\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner type.\n   */\n  dynamicObjectField?: Maybe<DynamicField>;\n  /** Determines whether a transaction can transfer this object, using the TransferObjects transaction command or `sui::transfer::public_transfer`, both of which require the object to have the `key` and `store` abilities. */\n  hasPublicTransfer: Scalars['Boolean']['output'];\n};\n\n\n/**\n * This interface is implemented by types that represent a Move object on-chain (A Move value whose\n * type has `key`).\n */\nexport type IMoveObjectDynamicFieldArgs = {\n  name: DynamicFieldName;\n};\n\n\n/**\n * This interface is implemented by types that represent a Move object on-chain (A Move value whose\n * type has `key`).\n */\nexport type IMoveObjectDynamicFieldsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * This interface is implemented by types that represent a Move object on-chain (A Move value whose\n * type has `key`).\n */\nexport type IMoveObjectDynamicObjectFieldArgs = {\n  name: DynamicFieldName;\n};\n\n/**\n * Interface implemented by on-chain values that are addressable by an ID (also referred to as its\n * address). This includes Move objects and packages.\n */\nexport type IObject = {\n  /** The Base64-encoded BCS serialization of the object's content. */\n  bcs?: Maybe<Scalars['Base64']['output']>;\n  /** 32-byte hash that identifies the object's current contents, encoded as a Base58 string. */\n  digest?: Maybe<Scalars['String']['output']>;\n  /**\n   * The owner type of this object: Immutable, Shared, Parent, Address\n   * Immutable and Shared Objects do not have owners.\n   */\n  owner?: Maybe<ObjectOwner>;\n  /** The transaction block that created this version of the object. */\n  previousTransactionBlock?: Maybe<TransactionBlock>;\n  /** The transaction blocks that sent objects to this object. */\n  receivedTransactionBlocks: TransactionBlockConnection;\n  /** The current status of the object as read from the off-chain store. The possible states are: NOT_INDEXED, the object is loaded from serialized data, such as the contents of a genesis or system package upgrade transaction. LIVE, the version returned is the most recent for the object, and it is not deleted or wrapped at that version. HISTORICAL, the object was referenced at a specific version or checkpoint, so is fetched from historical tables and may not be the latest version of the object. WRAPPED_OR_DELETED, the object is deleted or wrapped and only partial information can be loaded. */\n  status: ObjectKind;\n  storageRebate?: Maybe<Scalars['BigInt']['output']>;\n  version: Scalars['UInt53']['output'];\n};\n\n\n/**\n * Interface implemented by on-chain values that are addressable by an ID (also referred to as its\n * address). This includes Move objects and packages.\n */\nexport type IObjectReceivedTransactionBlocksArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<TransactionBlockFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  scanLimit?: InputMaybe<Scalars['Int']['input']>;\n};\n\n/**\n * Interface implemented by GraphQL types representing entities that can own objects. Object owners\n * are identified by an address which can represent either the public key of an account or another\n * object. The same address can only refer to an account or an object, never both, but it is not\n * possible to know which up-front.\n */\nexport type IOwner = {\n  address: Scalars['SuiAddress']['output'];\n  /** Total balance of all coins with marker type owned by this object or address. If type is not supplied, it defaults to `0x2::sui::SUI`. */\n  balance?: Maybe<Balance>;\n  /** The balances of all coin types owned by this object or address. */\n  balances: BalanceConnection;\n  /**\n   * The coin objects for this object or address.\n   *\n   * `type` is a filter on the coin's type parameter, defaulting to `0x2::sui::SUI`.\n   */\n  coins: CoinConnection;\n  /** The domain explicitly configured as the default domain pointing to this object or address. */\n  defaultSuinsName?: Maybe<Scalars['String']['output']>;\n  /** Objects owned by this object or address, optionally `filter`-ed. */\n  objects: MoveObjectConnection;\n  /** The `0x3::staking_pool::StakedSui` objects owned by this object or address. */\n  stakedSuis: StakedSuiConnection;\n  /** The SuinsRegistration NFTs owned by this object or address. These grant the owner the capability to manage the associated domain. */\n  suinsRegistrations: SuinsRegistrationConnection;\n};\n\n\n/**\n * Interface implemented by GraphQL types representing entities that can own objects. Object owners\n * are identified by an address which can represent either the public key of an account or another\n * object. The same address can only refer to an account or an object, never both, but it is not\n * possible to know which up-front.\n */\nexport type IOwnerBalanceArgs = {\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/**\n * Interface implemented by GraphQL types representing entities that can own objects. Object owners\n * are identified by an address which can represent either the public key of an account or another\n * object. The same address can only refer to an account or an object, never both, but it is not\n * possible to know which up-front.\n */\nexport type IOwnerBalancesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * Interface implemented by GraphQL types representing entities that can own objects. Object owners\n * are identified by an address which can represent either the public key of an account or another\n * object. The same address can only refer to an account or an object, never both, but it is not\n * possible to know which up-front.\n */\nexport type IOwnerCoinsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/**\n * Interface implemented by GraphQL types representing entities that can own objects. Object owners\n * are identified by an address which can represent either the public key of an account or another\n * object. The same address can only refer to an account or an object, never both, but it is not\n * possible to know which up-front.\n */\nexport type IOwnerDefaultSuinsNameArgs = {\n  format?: InputMaybe<DomainFormat>;\n};\n\n\n/**\n * Interface implemented by GraphQL types representing entities that can own objects. Object owners\n * are identified by an address which can represent either the public key of an account or another\n * object. The same address can only refer to an account or an object, never both, but it is not\n * possible to know which up-front.\n */\nexport type IOwnerObjectsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<ObjectFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * Interface implemented by GraphQL types representing entities that can own objects. Object owners\n * are identified by an address which can represent either the public key of an account or another\n * object. The same address can only refer to an account or an object, never both, but it is not\n * possible to know which up-front.\n */\nexport type IOwnerStakedSuisArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * Interface implemented by GraphQL types representing entities that can own objects. Object owners\n * are identified by an address which can represent either the public key of an account or another\n * object. The same address can only refer to an account or an object, never both, but it is not\n * possible to know which up-front.\n */\nexport type IOwnerSuinsRegistrationsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n/**\n * An immutable object is an object that can't be mutated, transferred, or deleted.\n * Immutable objects have no owner, so anyone can use them.\n */\nexport type Immutable = {\n  __typename?: 'Immutable';\n  _?: Maybe<Scalars['Boolean']['output']>;\n};\n\n/** One of the input objects or primitive values to the programmable transaction block. */\nexport type Input = {\n  __typename?: 'Input';\n  /** Index of the programmable transaction block input (0-indexed). */\n  ix: Scalars['Int']['output'];\n};\n\n/** Information used by a package to link to a specific version of its dependency. */\nexport type Linkage = {\n  __typename?: 'Linkage';\n  /** The ID on-chain of the first version of the dependency. */\n  originalId: Scalars['SuiAddress']['output'];\n  /** The ID on-chain of the version of the dependency that this package depends on. */\n  upgradedId: Scalars['SuiAddress']['output'];\n  /** The version of the dependency that this package depends on. */\n  version: Scalars['UInt53']['output'];\n};\n\n/** Create a vector (possibly empty). */\nexport type MakeMoveVecTransaction = {\n  __typename?: 'MakeMoveVecTransaction';\n  /** The values to pack into the vector, all of the same type. */\n  elements: Array<TransactionArgument>;\n  /** If the elements are not objects, or the vector is empty, a type must be supplied. */\n  type?: Maybe<MoveType>;\n};\n\n/** Merges `coins` into the first `coin` (produces no results). */\nexport type MergeCoinsTransaction = {\n  __typename?: 'MergeCoinsTransaction';\n  /** The coin to merge into. */\n  coin: TransactionArgument;\n  /** The coins to be merged. */\n  coins: Array<TransactionArgument>;\n};\n\n/** Abilities are keywords in Sui Move that define how types behave at the compiler level. */\nexport enum MoveAbility {\n  /** Enables values to be copied. */\n  Copy = 'COPY',\n  /** Enables values to be popped/dropped. */\n  Drop = 'DROP',\n  /** Enables values to be held directly in global storage. */\n  Key = 'KEY',\n  /** Enables values to be held inside a struct in global storage. */\n  Store = 'STORE'\n}\n\n/** A call to either an entry or a public Move function. */\nexport type MoveCallTransaction = {\n  __typename?: 'MoveCallTransaction';\n  /** The actual function parameters passed in for this move call. */\n  arguments: Array<TransactionArgument>;\n  /** The function being called, resolved. */\n  function?: Maybe<MoveFunction>;\n  /** The name of the function being called. */\n  functionName: Scalars['String']['output'];\n  /** The name of the module the function being called is defined in. */\n  module: Scalars['String']['output'];\n  /** The storage ID of the package the function being called is defined in. */\n  package: Scalars['SuiAddress']['output'];\n  /** The actual type parameters passed in for this move call. */\n  typeArguments: Array<MoveType>;\n};\n\n/**\n * The generic representation of a Move datatype (either a struct or an enum) which exposes common\n * fields and information (module, name, abilities, type parameters etc.) that is shared across\n * them.\n */\nexport type MoveDatatype = IMoveDatatype & {\n  __typename?: 'MoveDatatype';\n  abilities?: Maybe<Array<MoveAbility>>;\n  asMoveEnum?: Maybe<MoveEnum>;\n  asMoveStruct?: Maybe<MoveStruct>;\n  module: MoveModule;\n  name: Scalars['String']['output'];\n  typeParameters?: Maybe<Array<MoveStructTypeParameter>>;\n};\n\nexport type MoveDatatypeConnection = {\n  __typename?: 'MoveDatatypeConnection';\n  /** A list of edges. */\n  edges: Array<MoveDatatypeEdge>;\n  /** A list of nodes. */\n  nodes: Array<MoveDatatype>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type MoveDatatypeEdge = {\n  __typename?: 'MoveDatatypeEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: MoveDatatype;\n};\n\n/** Description of an enum type, defined in a Move module. */\nexport type MoveEnum = IMoveDatatype & {\n  __typename?: 'MoveEnum';\n  /** The enum's abilities. */\n  abilities?: Maybe<Array<MoveAbility>>;\n  /** The module this enum was originally defined in. */\n  module: MoveModule;\n  /** The enum's (unqualified) type name. */\n  name: Scalars['String']['output'];\n  /**\n   * Constraints on the enum's formal type parameters.  Move bytecode does not name type\n   * parameters, so when they are referenced (e.g. in field types) they are identified by their\n   * index in this list.\n   */\n  typeParameters?: Maybe<Array<MoveStructTypeParameter>>;\n  /**\n   * The names and types of the enum's fields.  Field types reference type parameters, by their\n   * index in the defining enum's `typeParameters` list.\n   */\n  variants?: Maybe<Array<MoveEnumVariant>>;\n};\n\nexport type MoveEnumConnection = {\n  __typename?: 'MoveEnumConnection';\n  /** A list of edges. */\n  edges: Array<MoveEnumEdge>;\n  /** A list of nodes. */\n  nodes: Array<MoveEnum>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type MoveEnumEdge = {\n  __typename?: 'MoveEnumEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: MoveEnum;\n};\n\nexport type MoveEnumVariant = {\n  __typename?: 'MoveEnumVariant';\n  /**\n   * The names and types of the variant's fields.  Field types reference type parameters, by their\n   * index in the defining enum's `typeParameters` list.\n   */\n  fields?: Maybe<Array<MoveField>>;\n  /** The name of the variant */\n  name: Scalars['String']['output'];\n};\n\n/** Information for a particular field on a Move struct. */\nexport type MoveField = {\n  __typename?: 'MoveField';\n  name: Scalars['String']['output'];\n  type?: Maybe<OpenMoveType>;\n};\n\n/** Signature of a function, defined in a Move module. */\nexport type MoveFunction = {\n  __typename?: 'MoveFunction';\n  /** Whether the function has the `entry` modifier or not. */\n  isEntry?: Maybe<Scalars['Boolean']['output']>;\n  /** The module this function was defined in. */\n  module: MoveModule;\n  /** The function's (unqualified) name. */\n  name: Scalars['String']['output'];\n  /**\n   * The function's parameter types.  These types can reference type parameters introduce by this\n   * function (see `typeParameters`).\n   */\n  parameters?: Maybe<Array<OpenMoveType>>;\n  /**\n   * The function's return types.  There can be multiple because functions in Move can return\n   * multiple values.  These types can reference type parameters introduced by this function (see\n   * `typeParameters`).\n   */\n  return?: Maybe<Array<OpenMoveType>>;\n  /**\n   * Constraints on the function's formal type parameters.  Move bytecode does not name type\n   * parameters, so when they are referenced (e.g. in parameter and return types) they are\n   * identified by their index in this list.\n   */\n  typeParameters?: Maybe<Array<MoveFunctionTypeParameter>>;\n  /** The function's visibility: `public`, `public(friend)`, or `private`. */\n  visibility?: Maybe<MoveVisibility>;\n};\n\nexport type MoveFunctionConnection = {\n  __typename?: 'MoveFunctionConnection';\n  /** A list of edges. */\n  edges: Array<MoveFunctionEdge>;\n  /** A list of nodes. */\n  nodes: Array<MoveFunction>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type MoveFunctionEdge = {\n  __typename?: 'MoveFunctionEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: MoveFunction;\n};\n\nexport type MoveFunctionTypeParameter = {\n  __typename?: 'MoveFunctionTypeParameter';\n  constraints: Array<MoveAbility>;\n};\n\n/**\n * Represents a module in Move, a library that defines struct types\n * and functions that operate on these types.\n */\nexport type MoveModule = {\n  __typename?: 'MoveModule';\n  /** The Base64 encoded bcs serialization of the module. */\n  bytes?: Maybe<Scalars['Base64']['output']>;\n  /** Look-up the definition of a datatype (struct or enum) defined in this module, by its name. */\n  datatype?: Maybe<MoveDatatype>;\n  /** Iterate through the datatypes (enmums and structs) defined in this module. */\n  datatypes?: Maybe<MoveDatatypeConnection>;\n  /** Textual representation of the module's bytecode. */\n  disassembly?: Maybe<Scalars['String']['output']>;\n  /** Look-up the definition of a enum defined in this module, by its name. */\n  enum?: Maybe<MoveEnum>;\n  /** Iterate through the enums defined in this module. */\n  enums?: Maybe<MoveEnumConnection>;\n  /** Format version of this module's bytecode. */\n  fileFormatVersion: Scalars['Int']['output'];\n  /**\n   * Modules that this module considers friends (these modules can access `public(friend)`\n   * functions from this module).\n   */\n  friends: MoveModuleConnection;\n  /** Look-up the signature of a function defined in this module, by its name. */\n  function?: Maybe<MoveFunction>;\n  /** Iterate through the signatures of functions defined in this module. */\n  functions?: Maybe<MoveFunctionConnection>;\n  /** The module's (unqualified) name. */\n  name: Scalars['String']['output'];\n  /** The package that this Move module was defined in */\n  package: MovePackage;\n  /** Look-up the definition of a struct defined in this module, by its name. */\n  struct?: Maybe<MoveStruct>;\n  /** Iterate through the structs defined in this module. */\n  structs?: Maybe<MoveStructConnection>;\n};\n\n\n/**\n * Represents a module in Move, a library that defines struct types\n * and functions that operate on these types.\n */\nexport type MoveModuleDatatypeArgs = {\n  name: Scalars['String']['input'];\n};\n\n\n/**\n * Represents a module in Move, a library that defines struct types\n * and functions that operate on these types.\n */\nexport type MoveModuleDatatypesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * Represents a module in Move, a library that defines struct types\n * and functions that operate on these types.\n */\nexport type MoveModuleEnumArgs = {\n  name: Scalars['String']['input'];\n};\n\n\n/**\n * Represents a module in Move, a library that defines struct types\n * and functions that operate on these types.\n */\nexport type MoveModuleEnumsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * Represents a module in Move, a library that defines struct types\n * and functions that operate on these types.\n */\nexport type MoveModuleFriendsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * Represents a module in Move, a library that defines struct types\n * and functions that operate on these types.\n */\nexport type MoveModuleFunctionArgs = {\n  name: Scalars['String']['input'];\n};\n\n\n/**\n * Represents a module in Move, a library that defines struct types\n * and functions that operate on these types.\n */\nexport type MoveModuleFunctionsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * Represents a module in Move, a library that defines struct types\n * and functions that operate on these types.\n */\nexport type MoveModuleStructArgs = {\n  name: Scalars['String']['input'];\n};\n\n\n/**\n * Represents a module in Move, a library that defines struct types\n * and functions that operate on these types.\n */\nexport type MoveModuleStructsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\nexport type MoveModuleConnection = {\n  __typename?: 'MoveModuleConnection';\n  /** A list of edges. */\n  edges: Array<MoveModuleEdge>;\n  /** A list of nodes. */\n  nodes: Array<MoveModule>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type MoveModuleEdge = {\n  __typename?: 'MoveModuleEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: MoveModule;\n};\n\n/**\n * The representation of an object as a Move Object, which exposes additional information\n * (content, module that governs it, version, is transferrable, etc.) about this object.\n */\nexport type MoveObject = IMoveObject & IObject & IOwner & {\n  __typename?: 'MoveObject';\n  address: Scalars['SuiAddress']['output'];\n  /** Attempts to convert the Move object into a `0x2::coin::Coin`. */\n  asCoin?: Maybe<Coin>;\n  /** Attempts to convert the Move object into a `0x2::coin::CoinMetadata`. */\n  asCoinMetadata?: Maybe<CoinMetadata>;\n  /** Attempts to convert the Move object into a `0x3::staking_pool::StakedSui`. */\n  asStakedSui?: Maybe<StakedSui>;\n  /** Attempts to convert the Move object into a `SuinsRegistration` object. */\n  asSuinsRegistration?: Maybe<SuinsRegistration>;\n  /**\n   * Total balance of all coins with marker type owned by this object. If type is not supplied,\n   * it defaults to `0x2::sui::SUI`.\n   */\n  balance?: Maybe<Balance>;\n  /** The balances of all coin types owned by this object. */\n  balances: BalanceConnection;\n  /** The Base64-encoded BCS serialization of the object's content. */\n  bcs?: Maybe<Scalars['Base64']['output']>;\n  /**\n   * The coin objects for this object.\n   *\n   * `type` is a filter on the coin's type parameter, defaulting to `0x2::sui::SUI`.\n   */\n  coins: CoinConnection;\n  /**\n   * Displays the contents of the Move object in a JSON string and through GraphQL types. Also\n   * provides the flat representation of the type signature, and the BCS of the corresponding\n   * data.\n   */\n  contents?: Maybe<MoveValue>;\n  /** The domain explicitly configured as the default domain pointing to this object. */\n  defaultSuinsName?: Maybe<Scalars['String']['output']>;\n  /** 32-byte hash that identifies the object's contents, encoded as a Base58 string. */\n  digest?: Maybe<Scalars['String']['output']>;\n  /**\n   * The set of named templates defined on-chain for the type of this object, to be handled\n   * off-chain. The server substitutes data from the object into these templates to generate a\n   * display string per template.\n   */\n  display?: Maybe<Array<DisplayEntry>>;\n  /**\n   * Access a dynamic field on an object using its name. Names are arbitrary Move values whose\n   * type have `copy`, `drop`, and `store`, and are specified using their type, and their BCS\n   * contents, Base64 encoded.\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicField?: Maybe<DynamicField>;\n  /**\n   * The dynamic fields and dynamic object fields on an object.\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicFields: DynamicFieldConnection;\n  /**\n   * Access a dynamic object field on an object using its name. Names are arbitrary Move values\n   * whose type have `copy`, `drop`, and `store`, and are specified using their type, and their\n   * BCS contents, Base64 encoded. The value of a dynamic object field can also be accessed\n   * off-chain directly via its address (e.g. using `Query.object`).\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicObjectField?: Maybe<DynamicField>;\n  /**\n   * Determines whether a transaction can transfer this object, using the TransferObjects\n   * transaction command or `sui::transfer::public_transfer`, both of which require the object to\n   * have the `key` and `store` abilities.\n   */\n  hasPublicTransfer: Scalars['Boolean']['output'];\n  /** Objects owned by this object, optionally `filter`-ed. */\n  objects: MoveObjectConnection;\n  /** The owner type of this object: Immutable, Shared, Parent, Address */\n  owner?: Maybe<ObjectOwner>;\n  /** The transaction block that created this version of the object. */\n  previousTransactionBlock?: Maybe<TransactionBlock>;\n  /**\n   * The transaction blocks that sent objects to this object.\n   *\n   * `scanLimit` restricts the number of candidate transactions scanned when gathering a page of\n   * results. It is required for queries that apply more than two complex filters (on function,\n   * kind, sender, recipient, input object, changed object, or ids), and can be at most\n   * `serviceConfig.maxScanLimit`.\n   *\n   * When the scan limit is reached the page will be returned even if it has fewer than `first`\n   * results when paginating forward (`last` when paginating backwards). If there are more\n   * transactions to scan, `pageInfo.hasNextPage` (or `pageInfo.hasPreviousPage`) will be set to\n   * `true`, and `PageInfo.endCursor` (or `PageInfo.startCursor`) will be set to the last\n   * transaction that was scanned as opposed to the last (or first) transaction in the page.\n   *\n   * Requesting the next (or previous) page after this cursor will resume the search, scanning\n   * the next `scanLimit` many transactions in the direction of pagination, and so on until all\n   * transactions in the scanning range have been visited.\n   *\n   * By default, the scanning range includes all transactions known to GraphQL, but it can be\n   * restricted by the `after` and `before` cursors, and the `beforeCheckpoint`,\n   * `afterCheckpoint` and `atCheckpoint` filters.\n   */\n  receivedTransactionBlocks: TransactionBlockConnection;\n  /** The `0x3::staking_pool::StakedSui` objects owned by this object. */\n  stakedSuis: StakedSuiConnection;\n  /**\n   * The current status of the object as read from the off-chain store. The possible states are:\n   * NOT_INDEXED, the object is loaded from serialized data, such as the contents of a genesis or\n   * system package upgrade transaction. LIVE, the version returned is the most recent for the\n   * object, and it is not deleted or wrapped at that version. HISTORICAL, the object was\n   * referenced at a specific version or checkpoint, so is fetched from historical tables and may\n   * not be the latest version of the object. WRAPPED_OR_DELETED, the object is deleted or\n   * wrapped and only partial information can be loaded.\"\n   */\n  status: ObjectKind;\n  /**\n   * The amount of SUI we would rebate if this object gets deleted or mutated. This number is\n   * recalculated based on the present storage gas price.\n   */\n  storageRebate?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * The SuinsRegistration NFTs owned by this object. These grant the owner the capability to\n   * manage the associated domain.\n   */\n  suinsRegistrations: SuinsRegistrationConnection;\n  version: Scalars['UInt53']['output'];\n};\n\n\n/**\n * The representation of an object as a Move Object, which exposes additional information\n * (content, module that governs it, version, is transferrable, etc.) about this object.\n */\nexport type MoveObjectBalanceArgs = {\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/**\n * The representation of an object as a Move Object, which exposes additional information\n * (content, module that governs it, version, is transferrable, etc.) about this object.\n */\nexport type MoveObjectBalancesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * The representation of an object as a Move Object, which exposes additional information\n * (content, module that governs it, version, is transferrable, etc.) about this object.\n */\nexport type MoveObjectCoinsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/**\n * The representation of an object as a Move Object, which exposes additional information\n * (content, module that governs it, version, is transferrable, etc.) about this object.\n */\nexport type MoveObjectDefaultSuinsNameArgs = {\n  format?: InputMaybe<DomainFormat>;\n};\n\n\n/**\n * The representation of an object as a Move Object, which exposes additional information\n * (content, module that governs it, version, is transferrable, etc.) about this object.\n */\nexport type MoveObjectDynamicFieldArgs = {\n  name: DynamicFieldName;\n};\n\n\n/**\n * The representation of an object as a Move Object, which exposes additional information\n * (content, module that governs it, version, is transferrable, etc.) about this object.\n */\nexport type MoveObjectDynamicFieldsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * The representation of an object as a Move Object, which exposes additional information\n * (content, module that governs it, version, is transferrable, etc.) about this object.\n */\nexport type MoveObjectDynamicObjectFieldArgs = {\n  name: DynamicFieldName;\n};\n\n\n/**\n * The representation of an object as a Move Object, which exposes additional information\n * (content, module that governs it, version, is transferrable, etc.) about this object.\n */\nexport type MoveObjectObjectsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<ObjectFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * The representation of an object as a Move Object, which exposes additional information\n * (content, module that governs it, version, is transferrable, etc.) about this object.\n */\nexport type MoveObjectReceivedTransactionBlocksArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<TransactionBlockFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  scanLimit?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * The representation of an object as a Move Object, which exposes additional information\n * (content, module that governs it, version, is transferrable, etc.) about this object.\n */\nexport type MoveObjectStakedSuisArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * The representation of an object as a Move Object, which exposes additional information\n * (content, module that governs it, version, is transferrable, etc.) about this object.\n */\nexport type MoveObjectSuinsRegistrationsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\nexport type MoveObjectConnection = {\n  __typename?: 'MoveObjectConnection';\n  /** A list of edges. */\n  edges: Array<MoveObjectEdge>;\n  /** A list of nodes. */\n  nodes: Array<MoveObject>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type MoveObjectEdge = {\n  __typename?: 'MoveObjectEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: MoveObject;\n};\n\n/**\n * A MovePackage is a kind of Move object that represents code that has been published on chain.\n * It exposes information about its modules, type definitions, functions, and dependencies.\n */\nexport type MovePackage = IObject & IOwner & {\n  __typename?: 'MovePackage';\n  address: Scalars['SuiAddress']['output'];\n  /**\n   * Total balance of all coins with marker type owned by this package. If type is not supplied,\n   * it defaults to `0x2::sui::SUI`.\n   *\n   * Note that coins owned by a package are inaccessible, because packages are immutable and\n   * cannot be owned by an address.\n   */\n  balance?: Maybe<Balance>;\n  /**\n   * The balances of all coin types owned by this package.\n   *\n   * Note that coins owned by a package are inaccessible, because packages are immutable and\n   * cannot be owned by an address.\n   */\n  balances: BalanceConnection;\n  /** The Base64-encoded BCS serialization of the package's content. */\n  bcs?: Maybe<Scalars['Base64']['output']>;\n  /**\n   * The coin objects owned by this package.\n   *\n   * `type` is a filter on the coin's type parameter, defaulting to `0x2::sui::SUI`.\n   *\n   * Note that coins owned by a package are inaccessible, because packages are immutable and\n   * cannot be owned by an address.\n   */\n  coins: CoinConnection;\n  /** The domain explicitly configured as the default domain pointing to this object. */\n  defaultSuinsName?: Maybe<Scalars['String']['output']>;\n  /** 32-byte hash that identifies the package's contents, encoded as a Base58 string. */\n  digest?: Maybe<Scalars['String']['output']>;\n  /**\n   * Fetch the latest version of this package (the package with the highest `version` that shares\n   * this packages's original ID)\n   */\n  latestPackage: MovePackage;\n  /** The transitive dependencies of this package. */\n  linkage?: Maybe<Array<Linkage>>;\n  /**\n   * A representation of the module called `name` in this package, including the\n   * structs and functions it defines.\n   */\n  module?: Maybe<MoveModule>;\n  /**\n   * BCS representation of the package's modules.  Modules appear as a sequence of pairs (module\n   * name, followed by module bytes), in alphabetic order by module name.\n   */\n  moduleBcs?: Maybe<Scalars['Base64']['output']>;\n  /** Paginate through the MoveModules defined in this package. */\n  modules?: Maybe<MoveModuleConnection>;\n  /**\n   * Objects owned by this package, optionally `filter`-ed.\n   *\n   * Note that objects owned by a package are inaccessible, because packages are immutable and\n   * cannot be owned by an address.\n   */\n  objects: MoveObjectConnection;\n  /**\n   * The owner type of this object: Immutable, Shared, Parent, Address\n   * Packages are always Immutable.\n   */\n  owner?: Maybe<ObjectOwner>;\n  /**\n   * Fetch another version of this package (the package that shares this package's original ID,\n   * but has the specified `version`).\n   */\n  packageAtVersion?: Maybe<MovePackage>;\n  /** BCS representation of the package itself, as a MovePackage. */\n  packageBcs?: Maybe<Scalars['Base64']['output']>;\n  /**\n   * Fetch all versions of this package (packages that share this package's original ID),\n   * optionally bounding the versions exclusively from below with `afterVersion`, or from above\n   * with `beforeVersion`.\n   */\n  packageVersions: MovePackageConnection;\n  /** The transaction block that published or upgraded this package. */\n  previousTransactionBlock?: Maybe<TransactionBlock>;\n  /**\n   * The transaction blocks that sent objects to this package.\n   *\n   * Note that objects that have been sent to a package become inaccessible.\n   *\n   * `scanLimit` restricts the number of candidate transactions scanned when gathering a page of\n   * results. It is required for queries that apply more than two complex filters (on function,\n   * kind, sender, recipient, input object, changed object, or ids), and can be at most\n   * `serviceConfig.maxScanLimit`.\n   *\n   * When the scan limit is reached the page will be returned even if it has fewer than `first`\n   * results when paginating forward (`last` when paginating backwards). If there are more\n   * transactions to scan, `pageInfo.hasNextPage` (or `pageInfo.hasPreviousPage`) will be set to\n   * `true`, and `PageInfo.endCursor` (or `PageInfo.startCursor`) will be set to the last\n   * transaction that was scanned as opposed to the last (or first) transaction in the page.\n   *\n   * Requesting the next (or previous) page after this cursor will resume the search, scanning\n   * the next `scanLimit` many transactions in the direction of pagination, and so on until all\n   * transactions in the scanning range have been visited.\n   *\n   * By default, the scanning range includes all transactions known to GraphQL, but it can be\n   * restricted by the `after` and `before` cursors, and the `beforeCheckpoint`,\n   * `afterCheckpoint` and `atCheckpoint` filters.\n   */\n  receivedTransactionBlocks: TransactionBlockConnection;\n  /**\n   * The `0x3::staking_pool::StakedSui` objects owned by this package.\n   *\n   * Note that objects owned by a package are inaccessible, because packages are immutable and\n   * cannot be owned by an address.\n   */\n  stakedSuis: StakedSuiConnection;\n  /**\n   * The current status of the object as read from the off-chain store. The possible states are:\n   * NOT_INDEXED, the object is loaded from serialized data, such as the contents of a genesis or\n   * system package upgrade transaction. LIVE, the version returned is the most recent for the\n   * object, and it is not deleted or wrapped at that version. HISTORICAL, the object was\n   * referenced at a specific version or checkpoint, so is fetched from historical tables and may\n   * not be the latest version of the object. WRAPPED_OR_DELETED, the object is deleted or\n   * wrapped and only partial information can be loaded.\"\n   */\n  status: ObjectKind;\n  /**\n   * The amount of SUI we would rebate if this object gets deleted or mutated. This number is\n   * recalculated based on the present storage gas price.\n   *\n   * Note that packages cannot be deleted or mutated, so this number is provided purely for\n   * reference.\n   */\n  storageRebate?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * The SuinsRegistration NFTs owned by this package. These grant the owner the capability to\n   * manage the associated domain.\n   *\n   * Note that objects owned by a package are inaccessible, because packages are immutable and\n   * cannot be owned by an address.\n   */\n  suinsRegistrations: SuinsRegistrationConnection;\n  /** The (previous) versions of this package that introduced its types. */\n  typeOrigins?: Maybe<Array<TypeOrigin>>;\n  version: Scalars['UInt53']['output'];\n};\n\n\n/**\n * A MovePackage is a kind of Move object that represents code that has been published on chain.\n * It exposes information about its modules, type definitions, functions, and dependencies.\n */\nexport type MovePackageBalanceArgs = {\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/**\n * A MovePackage is a kind of Move object that represents code that has been published on chain.\n * It exposes information about its modules, type definitions, functions, and dependencies.\n */\nexport type MovePackageBalancesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * A MovePackage is a kind of Move object that represents code that has been published on chain.\n * It exposes information about its modules, type definitions, functions, and dependencies.\n */\nexport type MovePackageCoinsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/**\n * A MovePackage is a kind of Move object that represents code that has been published on chain.\n * It exposes information about its modules, type definitions, functions, and dependencies.\n */\nexport type MovePackageDefaultSuinsNameArgs = {\n  format?: InputMaybe<DomainFormat>;\n};\n\n\n/**\n * A MovePackage is a kind of Move object that represents code that has been published on chain.\n * It exposes information about its modules, type definitions, functions, and dependencies.\n */\nexport type MovePackageModuleArgs = {\n  name: Scalars['String']['input'];\n};\n\n\n/**\n * A MovePackage is a kind of Move object that represents code that has been published on chain.\n * It exposes information about its modules, type definitions, functions, and dependencies.\n */\nexport type MovePackageModulesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * A MovePackage is a kind of Move object that represents code that has been published on chain.\n * It exposes information about its modules, type definitions, functions, and dependencies.\n */\nexport type MovePackageObjectsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<ObjectFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * A MovePackage is a kind of Move object that represents code that has been published on chain.\n * It exposes information about its modules, type definitions, functions, and dependencies.\n */\nexport type MovePackagePackageAtVersionArgs = {\n  version: Scalars['Int']['input'];\n};\n\n\n/**\n * A MovePackage is a kind of Move object that represents code that has been published on chain.\n * It exposes information about its modules, type definitions, functions, and dependencies.\n */\nexport type MovePackagePackageVersionsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<MovePackageVersionFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * A MovePackage is a kind of Move object that represents code that has been published on chain.\n * It exposes information about its modules, type definitions, functions, and dependencies.\n */\nexport type MovePackageReceivedTransactionBlocksArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<TransactionBlockFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  scanLimit?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * A MovePackage is a kind of Move object that represents code that has been published on chain.\n * It exposes information about its modules, type definitions, functions, and dependencies.\n */\nexport type MovePackageStakedSuisArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * A MovePackage is a kind of Move object that represents code that has been published on chain.\n * It exposes information about its modules, type definitions, functions, and dependencies.\n */\nexport type MovePackageSuinsRegistrationsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n/** Filter for paginating `MovePackage`s that were created within a range of checkpoints. */\nexport type MovePackageCheckpointFilter = {\n  /**\n   * Fetch packages that were published strictly after this checkpoint. Omitting this fetches\n   * packages published since genesis.\n   */\n  afterCheckpoint?: InputMaybe<Scalars['UInt53']['input']>;\n  /**\n   * Fetch packages that were published strictly before this checkpoint. Omitting this fetches\n   * packages published up to the latest checkpoint (inclusive).\n   */\n  beforeCheckpoint?: InputMaybe<Scalars['UInt53']['input']>;\n};\n\nexport type MovePackageConnection = {\n  __typename?: 'MovePackageConnection';\n  /** A list of edges. */\n  edges: Array<MovePackageEdge>;\n  /** A list of nodes. */\n  nodes: Array<MovePackage>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type MovePackageEdge = {\n  __typename?: 'MovePackageEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: MovePackage;\n};\n\n/** Filter for paginating versions of a given `MovePackage`. */\nexport type MovePackageVersionFilter = {\n  /**\n   * Fetch versions of this package that are strictly newer than this version. Omitting this\n   * fetches versions since the original version.\n   */\n  afterVersion?: InputMaybe<Scalars['UInt53']['input']>;\n  /**\n   * Fetch versions of this package that are strictly older than this version. Omitting this\n   * fetches versions up to the latest version (inclusive).\n   */\n  beforeVersion?: InputMaybe<Scalars['UInt53']['input']>;\n};\n\n/** Description of a struct type, defined in a Move module. */\nexport type MoveStruct = IMoveDatatype & {\n  __typename?: 'MoveStruct';\n  /** Abilities this struct has. */\n  abilities?: Maybe<Array<MoveAbility>>;\n  /**\n   * The names and types of the struct's fields.  Field types reference type parameters, by their\n   * index in the defining struct's `typeParameters` list.\n   */\n  fields?: Maybe<Array<MoveField>>;\n  /** The module this struct was originally defined in. */\n  module: MoveModule;\n  /** The struct's (unqualified) type name. */\n  name: Scalars['String']['output'];\n  /**\n   * Constraints on the struct's formal type parameters.  Move bytecode does not name type\n   * parameters, so when they are referenced (e.g. in field types) they are identified by their\n   * index in this list.\n   */\n  typeParameters?: Maybe<Array<MoveStructTypeParameter>>;\n};\n\nexport type MoveStructConnection = {\n  __typename?: 'MoveStructConnection';\n  /** A list of edges. */\n  edges: Array<MoveStructEdge>;\n  /** A list of nodes. */\n  nodes: Array<MoveStruct>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type MoveStructEdge = {\n  __typename?: 'MoveStructEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: MoveStruct;\n};\n\nexport type MoveStructTypeParameter = {\n  __typename?: 'MoveStructTypeParameter';\n  constraints: Array<MoveAbility>;\n  isPhantom: Scalars['Boolean']['output'];\n};\n\n/** Represents concrete types (no type parameters, no references). */\nexport type MoveType = {\n  __typename?: 'MoveType';\n  /** The abilities this concrete type has. Returns no abilities if the type is invalid. */\n  abilities?: Maybe<Array<MoveAbility>>;\n  /**\n   * Structured representation of the \"shape\" of values that match this type. May return no\n   * layout if the type is invalid.\n   */\n  layout?: Maybe<Scalars['MoveTypeLayout']['output']>;\n  /** Flat representation of the type signature, as a displayable string. */\n  repr: Scalars['String']['output'];\n  /** Structured representation of the type signature. */\n  signature: Scalars['MoveTypeSignature']['output'];\n};\n\nexport type MoveValue = {\n  __typename?: 'MoveValue';\n  /** The BCS representation of this value, Base64 encoded. */\n  bcs: Scalars['Base64']['output'];\n  /** Structured contents of a Move value. */\n  data: Scalars['MoveData']['output'];\n  /**\n   * Representation of a Move value in JSON, where:\n   *\n   * - Addresses, IDs, and UIDs are represented in canonical form, as JSON strings.\n   * - Bools are represented by JSON boolean literals.\n   * - u8, u16, and u32 are represented as JSON numbers.\n   * - u64, u128, and u256 are represented as JSON strings.\n   * - Vectors are represented by JSON arrays.\n   * - Structs are represented by JSON objects.\n   * - Empty optional values are represented by `null`.\n   *\n   * This form is offered as a less verbose convenience in cases where the layout of the type is\n   * known by the client.\n   */\n  json: Scalars['JSON']['output'];\n  /** The value's Move type. */\n  type: MoveType;\n};\n\n/**\n * The visibility modifier describes which modules can access this module member.\n * By default, a module member can be called only within the same module.\n */\nexport enum MoveVisibility {\n  /**\n   * A friend member can be accessed in the module it is defined in and any other module in\n   * its package that is explicitly specified in its friend list.\n   */\n  Friend = 'FRIEND',\n  /** A private member can be accessed in the module it is defined in. */\n  Private = 'PRIVATE',\n  /** A public member can be accessed by any module. */\n  Public = 'PUBLIC'\n}\n\n/** Mutations are used to write to the Sui network. */\nexport type Mutation = {\n  __typename?: 'Mutation';\n  /**\n   * Execute a transaction, committing its effects on chain.\n   *\n   * - `txBytes` is a `TransactionData` struct that has been BCS-encoded and then Base64-encoded.\n   * - `signatures` are a list of `flag || signature || pubkey` bytes, Base64-encoded.\n   *\n   * Waits until the transaction has reached finality on chain to return its transaction digest,\n   * or returns the error that prevented finality if that was not possible. A transaction is\n   * final when its effects are guaranteed on chain (it cannot be revoked).\n   *\n   * There may be a delay between transaction finality and when GraphQL requests (including the\n   * request that issued the transaction) reflect its effects. As a result, queries that depend\n   * on indexing the state of the chain (e.g. contents of output objects, address-level balance\n   * information at the time of the transaction), must wait for indexing to catch up by polling\n   * for the transaction digest using `Query.transactionBlock`.\n   */\n  executeTransactionBlock: ExecutionResult;\n};\n\n\n/** Mutations are used to write to the Sui network. */\nexport type MutationExecuteTransactionBlockArgs = {\n  signatures: Array<Scalars['String']['input']>;\n  txBytes: Scalars['String']['input'];\n};\n\n/**\n * An object in Sui is a package (set of Move bytecode modules) or object (typed data structure\n * with fields) with additional metadata detailing its id, version, transaction digest, owner\n * field indicating how this object can be accessed.\n */\nexport type Object = IObject & IOwner & {\n  __typename?: 'Object';\n  address: Scalars['SuiAddress']['output'];\n  /** Attempts to convert the object into a MoveObject */\n  asMoveObject?: Maybe<MoveObject>;\n  /** Attempts to convert the object into a MovePackage */\n  asMovePackage?: Maybe<MovePackage>;\n  /**\n   * Total balance of all coins with marker type owned by this object. If type is not supplied,\n   * it defaults to `0x2::sui::SUI`.\n   */\n  balance?: Maybe<Balance>;\n  /** The balances of all coin types owned by this object. */\n  balances: BalanceConnection;\n  /** The Base64-encoded BCS serialization of the object's content. */\n  bcs?: Maybe<Scalars['Base64']['output']>;\n  /**\n   * The coin objects for this object.\n   *\n   * `type` is a filter on the coin's type parameter, defaulting to `0x2::sui::SUI`.\n   */\n  coins: CoinConnection;\n  /** The domain explicitly configured as the default domain pointing to this object. */\n  defaultSuinsName?: Maybe<Scalars['String']['output']>;\n  /** 32-byte hash that identifies the object's current contents, encoded as a Base58 string. */\n  digest?: Maybe<Scalars['String']['output']>;\n  /**\n   * The set of named templates defined on-chain for the type of this object, to be handled\n   * off-chain. The server substitutes data from the object into these templates to generate a\n   * display string per template.\n   */\n  display?: Maybe<Array<DisplayEntry>>;\n  /**\n   * Access a dynamic field on an object using its name. Names are arbitrary Move values whose\n   * type have `copy`, `drop`, and `store`, and are specified using their type, and their BCS\n   * contents, Base64 encoded.\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicField?: Maybe<DynamicField>;\n  /**\n   * The dynamic fields and dynamic object fields on an object.\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicFields: DynamicFieldConnection;\n  /**\n   * Access a dynamic object field on an object using its name. Names are arbitrary Move values\n   * whose type have `copy`, `drop`, and `store`, and are specified using their type, and their\n   * BCS contents, Base64 encoded. The value of a dynamic object field can also be accessed\n   * off-chain directly via its address (e.g. using `Query.object`).\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicObjectField?: Maybe<DynamicField>;\n  /** Objects owned by this object, optionally `filter`-ed. */\n  objects: MoveObjectConnection;\n  /**\n   * The owner type of this object: Immutable, Shared, Parent, Address\n   * Immutable and Shared Objects do not have owners.\n   */\n  owner?: Maybe<ObjectOwner>;\n  /** The transaction block that created this version of the object. */\n  previousTransactionBlock?: Maybe<TransactionBlock>;\n  /**\n   * The transaction blocks that sent objects to this object.\n   *\n   * `scanLimit` restricts the number of candidate transactions scanned when gathering a page of\n   * results. It is required for queries that apply more than two complex filters (on function,\n   * kind, sender, recipient, input object, changed object, or ids), and can be at most\n   * `serviceConfig.maxScanLimit`.\n   *\n   * When the scan limit is reached the page will be returned even if it has fewer than `first`\n   * results when paginating forward (`last` when paginating backwards). If there are more\n   * transactions to scan, `pageInfo.hasNextPage` (or `pageInfo.hasPreviousPage`) will be set to\n   * `true`, and `PageInfo.endCursor` (or `PageInfo.startCursor`) will be set to the last\n   * transaction that was scanned as opposed to the last (or first) transaction in the page.\n   *\n   * Requesting the next (or previous) page after this cursor will resume the search, scanning\n   * the next `scanLimit` many transactions in the direction of pagination, and so on until all\n   * transactions in the scanning range have been visited.\n   *\n   * By default, the scanning range includes all transactions known to GraphQL, but it can be\n   * restricted by the `after` and `before` cursors, and the `beforeCheckpoint`,\n   * `afterCheckpoint` and `atCheckpoint` filters.\n   */\n  receivedTransactionBlocks: TransactionBlockConnection;\n  /** The `0x3::staking_pool::StakedSui` objects owned by this object. */\n  stakedSuis: StakedSuiConnection;\n  /**\n   * The current status of the object as read from the off-chain store. The possible states are:\n   * NOT_INDEXED, the object is loaded from serialized data, such as the contents of a genesis or\n   * system package upgrade transaction. LIVE, the version returned is the most recent for the\n   * object, and it is not deleted or wrapped at that version. HISTORICAL, the object was\n   * referenced at a specific version or checkpoint, so is fetched from historical tables and may\n   * not be the latest version of the object. WRAPPED_OR_DELETED, the object is deleted or\n   * wrapped and only partial information can be loaded.\"\n   */\n  status: ObjectKind;\n  /**\n   * The amount of SUI we would rebate if this object gets deleted or mutated. This number is\n   * recalculated based on the present storage gas price.\n   */\n  storageRebate?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * The SuinsRegistration NFTs owned by this object. These grant the owner the capability to\n   * manage the associated domain.\n   */\n  suinsRegistrations: SuinsRegistrationConnection;\n  version: Scalars['UInt53']['output'];\n};\n\n\n/**\n * An object in Sui is a package (set of Move bytecode modules) or object (typed data structure\n * with fields) with additional metadata detailing its id, version, transaction digest, owner\n * field indicating how this object can be accessed.\n */\nexport type ObjectBalanceArgs = {\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/**\n * An object in Sui is a package (set of Move bytecode modules) or object (typed data structure\n * with fields) with additional metadata detailing its id, version, transaction digest, owner\n * field indicating how this object can be accessed.\n */\nexport type ObjectBalancesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * An object in Sui is a package (set of Move bytecode modules) or object (typed data structure\n * with fields) with additional metadata detailing its id, version, transaction digest, owner\n * field indicating how this object can be accessed.\n */\nexport type ObjectCoinsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/**\n * An object in Sui is a package (set of Move bytecode modules) or object (typed data structure\n * with fields) with additional metadata detailing its id, version, transaction digest, owner\n * field indicating how this object can be accessed.\n */\nexport type ObjectDefaultSuinsNameArgs = {\n  format?: InputMaybe<DomainFormat>;\n};\n\n\n/**\n * An object in Sui is a package (set of Move bytecode modules) or object (typed data structure\n * with fields) with additional metadata detailing its id, version, transaction digest, owner\n * field indicating how this object can be accessed.\n */\nexport type ObjectDynamicFieldArgs = {\n  name: DynamicFieldName;\n};\n\n\n/**\n * An object in Sui is a package (set of Move bytecode modules) or object (typed data structure\n * with fields) with additional metadata detailing its id, version, transaction digest, owner\n * field indicating how this object can be accessed.\n */\nexport type ObjectDynamicFieldsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * An object in Sui is a package (set of Move bytecode modules) or object (typed data structure\n * with fields) with additional metadata detailing its id, version, transaction digest, owner\n * field indicating how this object can be accessed.\n */\nexport type ObjectDynamicObjectFieldArgs = {\n  name: DynamicFieldName;\n};\n\n\n/**\n * An object in Sui is a package (set of Move bytecode modules) or object (typed data structure\n * with fields) with additional metadata detailing its id, version, transaction digest, owner\n * field indicating how this object can be accessed.\n */\nexport type ObjectObjectsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<ObjectFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * An object in Sui is a package (set of Move bytecode modules) or object (typed data structure\n * with fields) with additional metadata detailing its id, version, transaction digest, owner\n * field indicating how this object can be accessed.\n */\nexport type ObjectReceivedTransactionBlocksArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<TransactionBlockFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  scanLimit?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * An object in Sui is a package (set of Move bytecode modules) or object (typed data structure\n * with fields) with additional metadata detailing its id, version, transaction digest, owner\n * field indicating how this object can be accessed.\n */\nexport type ObjectStakedSuisArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * An object in Sui is a package (set of Move bytecode modules) or object (typed data structure\n * with fields) with additional metadata detailing its id, version, transaction digest, owner\n * field indicating how this object can be accessed.\n */\nexport type ObjectSuinsRegistrationsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n/** Effect on an individual Object (keyed by its ID). */\nexport type ObjectChange = {\n  __typename?: 'ObjectChange';\n  /** The address of the object that has changed. */\n  address: Scalars['SuiAddress']['output'];\n  /** Whether the ID was created in this transaction. */\n  idCreated?: Maybe<Scalars['Boolean']['output']>;\n  /** Whether the ID was deleted in this transaction. */\n  idDeleted?: Maybe<Scalars['Boolean']['output']>;\n  /** The contents of the object immediately before the transaction. */\n  inputState?: Maybe<Object>;\n  /** The contents of the object immediately after the transaction. */\n  outputState?: Maybe<Object>;\n};\n\nexport type ObjectChangeConnection = {\n  __typename?: 'ObjectChangeConnection';\n  /** A list of edges. */\n  edges: Array<ObjectChangeEdge>;\n  /** A list of nodes. */\n  nodes: Array<ObjectChange>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type ObjectChangeEdge = {\n  __typename?: 'ObjectChangeEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: ObjectChange;\n};\n\nexport type ObjectConnection = {\n  __typename?: 'ObjectConnection';\n  /** A list of edges. */\n  edges: Array<ObjectEdge>;\n  /** A list of nodes. */\n  nodes: Array<Object>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type ObjectEdge = {\n  __typename?: 'ObjectEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: Object;\n};\n\n/**\n * Constrains the set of objects returned. All filters are optional, and the resulting set of\n * objects are ones whose\n *\n * - Type matches the `type` filter,\n * - AND, whose owner matches the `owner` filter,\n * - AND, whose ID is in `objectIds`.\n */\nexport type ObjectFilter = {\n  /** Filter for live objects by their IDs. */\n  objectIds?: InputMaybe<Array<Scalars['SuiAddress']['input']>>;\n  /** Filter for live objects by their current owners. */\n  owner?: InputMaybe<Scalars['SuiAddress']['input']>;\n  /**\n   * Filter objects by their type's `package`, `package::module`, or their fully qualified type\n   * name.\n   *\n   * Generic types can be queried by either the generic type name, e.g. `0x2::coin::Coin`, or by\n   * the full type name, such as `0x2::coin::Coin<0x2::sui::SUI>`.\n   */\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\nexport type ObjectKey = {\n  objectId: Scalars['SuiAddress']['input'];\n  version: Scalars['UInt53']['input'];\n};\n\nexport enum ObjectKind {\n  /** The object is fetched from the index. */\n  Indexed = 'INDEXED',\n  /**\n   * The object is loaded from serialized data, such as the contents of a transaction that hasn't\n   * been indexed yet.\n   */\n  NotIndexed = 'NOT_INDEXED'\n}\n\n/** The object's owner type: Immutable, Shared, Parent, Address, or ConsensusAddress. */\nexport type ObjectOwner = AddressOwner | ConsensusAddressOwner | Immutable | Parent | Shared;\n\nexport type ObjectRef = {\n  /** ID of the object. */\n  address: Scalars['SuiAddress']['input'];\n  /** Digest of the object. */\n  digest: Scalars['String']['input'];\n  /** Version or sequence number of the object. */\n  version: Scalars['UInt53']['input'];\n};\n\n/**\n * Represents types that could contain references or free type parameters.  Such types can appear\n * as function parameters, in fields of structs, or as actual type parameter.\n */\nexport type OpenMoveType = {\n  __typename?: 'OpenMoveType';\n  /** Flat representation of the type signature, as a displayable string. */\n  repr: Scalars['String']['output'];\n  /** Structured representation of the type signature. */\n  signature: Scalars['OpenMoveTypeSignature']['output'];\n};\n\n/** A Move object, either immutable, or owned mutable. */\nexport type OwnedOrImmutable = {\n  __typename?: 'OwnedOrImmutable';\n  /** ID of the object being read. */\n  address: Scalars['SuiAddress']['output'];\n  /**\n   * 32-byte hash that identifies the object's contents at this version, encoded as a Base58\n   * string.\n   */\n  digest: Scalars['String']['output'];\n  /** The object at this version.  May not be available due to pruning. */\n  object?: Maybe<Object>;\n  /** Version of the object being read. */\n  version: Scalars['UInt53']['output'];\n};\n\n/**\n * An Owner is an entity that can own an object. Each Owner is identified by a SuiAddress which\n * represents either an Address (corresponding to a public key of an account) or an Object, but\n * never both (it is not known up-front whether a given Owner is an Address or an Object).\n */\nexport type Owner = IOwner & {\n  __typename?: 'Owner';\n  address: Scalars['SuiAddress']['output'];\n  asAddress?: Maybe<Address>;\n  asObject?: Maybe<Object>;\n  /**\n   * Total balance of all coins with marker type owned by this object or address. If type is not\n   * supplied, it defaults to `0x2::sui::SUI`.\n   */\n  balance?: Maybe<Balance>;\n  /** The balances of all coin types owned by this object or address. */\n  balances: BalanceConnection;\n  /**\n   * The coin objects for this object or address.\n   *\n   * `type` is a filter on the coin's type parameter, defaulting to `0x2::sui::SUI`.\n   */\n  coins: CoinConnection;\n  /** The domain explicitly configured as the default domain pointing to this object or address. */\n  defaultSuinsName?: Maybe<Scalars['String']['output']>;\n  /**\n   * Access a dynamic field on an object using its name. Names are arbitrary Move values whose\n   * type have `copy`, `drop`, and `store`, and are specified using their type, and their BCS\n   * contents, Base64 encoded.\n   *\n   * This field exists as a convenience when accessing a dynamic field on a wrapped object.\n   */\n  dynamicField?: Maybe<DynamicField>;\n  /**\n   * The dynamic fields and dynamic object fields on an object.\n   *\n   * This field exists as a convenience when accessing a dynamic field on a wrapped object.\n   */\n  dynamicFields: DynamicFieldConnection;\n  /**\n   * Access a dynamic object field on an object using its name. Names are arbitrary Move values\n   * whose type have `copy`, `drop`, and `store`, and are specified using their type, and their\n   * BCS contents, Base64 encoded. The value of a dynamic object field can also be accessed\n   * off-chain directly via its address (e.g. using `Query.object`).\n   *\n   * This field exists as a convenience when accessing a dynamic field on a wrapped object.\n   */\n  dynamicObjectField?: Maybe<DynamicField>;\n  /** Objects owned by this object or address, optionally `filter`-ed. */\n  objects: MoveObjectConnection;\n  /** The `0x3::staking_pool::StakedSui` objects owned by this object or address. */\n  stakedSuis: StakedSuiConnection;\n  /**\n   * The SuinsRegistration NFTs owned by this object or address. These grant the owner the\n   * capability to manage the associated domain.\n   */\n  suinsRegistrations: SuinsRegistrationConnection;\n};\n\n\n/**\n * An Owner is an entity that can own an object. Each Owner is identified by a SuiAddress which\n * represents either an Address (corresponding to a public key of an account) or an Object, but\n * never both (it is not known up-front whether a given Owner is an Address or an Object).\n */\nexport type OwnerBalanceArgs = {\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/**\n * An Owner is an entity that can own an object. Each Owner is identified by a SuiAddress which\n * represents either an Address (corresponding to a public key of an account) or an Object, but\n * never both (it is not known up-front whether a given Owner is an Address or an Object).\n */\nexport type OwnerBalancesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * An Owner is an entity that can own an object. Each Owner is identified by a SuiAddress which\n * represents either an Address (corresponding to a public key of an account) or an Object, but\n * never both (it is not known up-front whether a given Owner is an Address or an Object).\n */\nexport type OwnerCoinsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/**\n * An Owner is an entity that can own an object. Each Owner is identified by a SuiAddress which\n * represents either an Address (corresponding to a public key of an account) or an Object, but\n * never both (it is not known up-front whether a given Owner is an Address or an Object).\n */\nexport type OwnerDefaultSuinsNameArgs = {\n  format?: InputMaybe<DomainFormat>;\n};\n\n\n/**\n * An Owner is an entity that can own an object. Each Owner is identified by a SuiAddress which\n * represents either an Address (corresponding to a public key of an account) or an Object, but\n * never both (it is not known up-front whether a given Owner is an Address or an Object).\n */\nexport type OwnerDynamicFieldArgs = {\n  name: DynamicFieldName;\n};\n\n\n/**\n * An Owner is an entity that can own an object. Each Owner is identified by a SuiAddress which\n * represents either an Address (corresponding to a public key of an account) or an Object, but\n * never both (it is not known up-front whether a given Owner is an Address or an Object).\n */\nexport type OwnerDynamicFieldsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * An Owner is an entity that can own an object. Each Owner is identified by a SuiAddress which\n * represents either an Address (corresponding to a public key of an account) or an Object, but\n * never both (it is not known up-front whether a given Owner is an Address or an Object).\n */\nexport type OwnerDynamicObjectFieldArgs = {\n  name: DynamicFieldName;\n};\n\n\n/**\n * An Owner is an entity that can own an object. Each Owner is identified by a SuiAddress which\n * represents either an Address (corresponding to a public key of an account) or an Object, but\n * never both (it is not known up-front whether a given Owner is an Address or an Object).\n */\nexport type OwnerObjectsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<ObjectFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * An Owner is an entity that can own an object. Each Owner is identified by a SuiAddress which\n * represents either an Address (corresponding to a public key of an account) or an Object, but\n * never both (it is not known up-front whether a given Owner is an Address or an Object).\n */\nexport type OwnerStakedSuisArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * An Owner is an entity that can own an object. Each Owner is identified by a SuiAddress which\n * represents either an Address (corresponding to a public key of an account) or an Object, but\n * never both (it is not known up-front whether a given Owner is an Address or an Object).\n */\nexport type OwnerSuinsRegistrationsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n/** Information about pagination in a connection */\nexport type PageInfo = {\n  __typename?: 'PageInfo';\n  /** When paginating forwards, the cursor to continue. */\n  endCursor?: Maybe<Scalars['String']['output']>;\n  /** When paginating forwards, are there more items? */\n  hasNextPage: Scalars['Boolean']['output'];\n  /** When paginating backwards, are there more items? */\n  hasPreviousPage: Scalars['Boolean']['output'];\n  /** When paginating backwards, the cursor to continue. */\n  startCursor?: Maybe<Scalars['String']['output']>;\n};\n\n/**\n * If the object's owner is a Parent, this object is part of a dynamic field (it is the value of\n * the dynamic field, or the intermediate Field object itself), and it is owned by another object.\n *\n * Although its owner is guaranteed to be an object, it is exposed as an Owner, as the parent\n * object could be wrapped and therefore not directly accessible.\n */\nexport type Parent = {\n  __typename?: 'Parent';\n  parent?: Maybe<Owner>;\n};\n\n/** A single transaction, or command, in the programmable transaction block. */\nexport type ProgrammableTransaction = MakeMoveVecTransaction | MergeCoinsTransaction | MoveCallTransaction | PublishTransaction | SplitCoinsTransaction | TransferObjectsTransaction | UpgradeTransaction;\n\n/**\n * A user transaction that allows the interleaving of native commands (like transfer, split coins,\n * merge coins, etc) and move calls, executed atomically.\n */\nexport type ProgrammableTransactionBlock = {\n  __typename?: 'ProgrammableTransactionBlock';\n  /** Input objects or primitive values. */\n  inputs: TransactionInputConnection;\n  /** The transaction commands, executed sequentially. */\n  transactions: ProgrammableTransactionConnection;\n};\n\n\n/**\n * A user transaction that allows the interleaving of native commands (like transfer, split coins,\n * merge coins, etc) and move calls, executed atomically.\n */\nexport type ProgrammableTransactionBlockInputsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/**\n * A user transaction that allows the interleaving of native commands (like transfer, split coins,\n * merge coins, etc) and move calls, executed atomically.\n */\nexport type ProgrammableTransactionBlockTransactionsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\nexport type ProgrammableTransactionConnection = {\n  __typename?: 'ProgrammableTransactionConnection';\n  /** A list of edges. */\n  edges: Array<ProgrammableTransactionEdge>;\n  /** A list of nodes. */\n  nodes: Array<ProgrammableTransaction>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type ProgrammableTransactionEdge = {\n  __typename?: 'ProgrammableTransactionEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: ProgrammableTransaction;\n};\n\n/** A single protocol configuration value. */\nexport type ProtocolConfigAttr = {\n  __typename?: 'ProtocolConfigAttr';\n  key: Scalars['String']['output'];\n  value?: Maybe<Scalars['String']['output']>;\n};\n\n/** Whether or not a single feature is enabled in the protocol config. */\nexport type ProtocolConfigFeatureFlag = {\n  __typename?: 'ProtocolConfigFeatureFlag';\n  key: Scalars['String']['output'];\n  value: Scalars['Boolean']['output'];\n};\n\n/**\n * Constants that control how the chain operates.\n *\n * These can only change during protocol upgrades which happen on epoch boundaries.\n */\nexport type ProtocolConfigs = {\n  __typename?: 'ProtocolConfigs';\n  /** Query for the value of the configuration with name `key`. */\n  config?: Maybe<ProtocolConfigAttr>;\n  /**\n   * List all available configurations and their values.  These configurations can take any value\n   * (but they will all be represented in string form), and do not include feature flags.\n   */\n  configs: Array<ProtocolConfigAttr>;\n  /** Query for the state of the feature flag with name `key`. */\n  featureFlag?: Maybe<ProtocolConfigFeatureFlag>;\n  /**\n   * List all available feature flags and their values.  Feature flags are a form of boolean\n   * configuration that are usually used to gate features while they are in development.  Once a\n   * flag has been enabled, it is rare for it to be disabled.\n   */\n  featureFlags: Array<ProtocolConfigFeatureFlag>;\n  /**\n   * The protocol is not required to change on every epoch boundary, so the protocol version\n   * tracks which change to the protocol these configs are from.\n   */\n  protocolVersion: Scalars['UInt53']['output'];\n};\n\n\n/**\n * Constants that control how the chain operates.\n *\n * These can only change during protocol upgrades which happen on epoch boundaries.\n */\nexport type ProtocolConfigsConfigArgs = {\n  key: Scalars['String']['input'];\n};\n\n\n/**\n * Constants that control how the chain operates.\n *\n * These can only change during protocol upgrades which happen on epoch boundaries.\n */\nexport type ProtocolConfigsFeatureFlagArgs = {\n  key: Scalars['String']['input'];\n};\n\n/** Publishes a Move Package. */\nexport type PublishTransaction = {\n  __typename?: 'PublishTransaction';\n  /** IDs of the transitive dependencies of the package to be published. */\n  dependencies: Array<Scalars['SuiAddress']['output']>;\n  /** Bytecode for the modules to be published, BCS serialized and Base64 encoded. */\n  modules: Array<Scalars['Base64']['output']>;\n};\n\n/** BCS encoded primitive value (not an object or Move struct). */\nexport type Pure = {\n  __typename?: 'Pure';\n  /** BCS serialized and Base64 encoded primitive value. */\n  bytes: Scalars['Base64']['output'];\n};\n\nexport type Query = {\n  __typename?: 'Query';\n  /** Look-up an Account by its SuiAddress. */\n  address?: Maybe<Address>;\n  /**\n   * Range of checkpoints that the RPC has data available for (for data\n   * that can be tied to a particular checkpoint).\n   */\n  availableRange: AvailableRange;\n  /**\n   * First four bytes of the network's genesis checkpoint digest (uniquely identifies the\n   * network).\n   */\n  chainIdentifier: Scalars['String']['output'];\n  /**\n   * Fetch checkpoint information by sequence number or digest (defaults to the latest available\n   * checkpoint).\n   */\n  checkpoint?: Maybe<Checkpoint>;\n  /** The checkpoints that exist in the network. */\n  checkpoints: CheckpointConnection;\n  /**\n   * The coin metadata associated with the given coin type. Note that if the latest version of\n   * the coin's metadata is wrapped or deleted, it will not be found.\n   */\n  coinMetadata?: Maybe<CoinMetadata>;\n  /**\n   * The coin objects that exist in the network.\n   *\n   * The type field is a string of the inner type of the coin by which to filter (e.g.\n   * `0x2::sui::SUI`). If no type is provided, it will default to `0x2::sui::SUI`.\n   */\n  coins: CoinConnection;\n  /**\n   * Simulate running a transaction to inspect its effects without\n   * committing to them on-chain.\n   *\n   * `txBytes` either a `TransactionData` struct or a `TransactionKind`\n   * struct, BCS-encoded and then Base64-encoded.  The expected\n   * type is controlled by the presence or absence of `txMeta`: If\n   * present, `txBytes` is assumed to be a `TransactionKind`, if\n   * absent, then `TransactionData`.\n   *\n   * `txMeta` the data that is missing from a `TransactionKind` to make\n   * a `TransactionData` (sender address and gas information).  All\n   * its fields are nullable.\n   *\n   * `skipChecks` optional flag to disable the usual verification\n   * checks that prevent access to objects that are owned by\n   * addresses other than the sender, and calling non-public,\n   * non-entry functions, and some other checks.  Defaults to false.\n   */\n  dryRunTransactionBlock: DryRunResult;\n  /** Fetch epoch information by ID (defaults to the latest epoch). */\n  epoch?: Maybe<Epoch>;\n  epochs: EpochConnection;\n  /**\n   * Query events that are emitted in the network.\n   * We currently do not support filtering by emitting module and event type\n   * at the same time so if both are provided in one filter, the query will error.\n   */\n  events: EventConnection;\n  /**\n   * The latest version of the package at `address`.\n   *\n   * This corresponds to the package with the highest `version` that shares its original ID with\n   * the package at `address`.\n   */\n  latestPackage?: Maybe<MovePackage>;\n  /** Fetch a list of objects by their IDs and versions. */\n  multiGetObjects: Array<Maybe<Object>>;\n  /**\n   * The object corresponding to the given address at the (optionally) given version.\n   * When no version is given, the latest version is returned.\n   */\n  object?: Maybe<Object>;\n  /** The objects that exist in the network. */\n  objects: ObjectConnection;\n  /**\n   * Look up an Owner by its SuiAddress.\n   *\n   * `rootVersion` represents the version of the root object in some nested chain of dynamic\n   * fields. It allows consistent historical queries for the case of wrapped objects, which don't\n   * have a version. For example, if querying the dynamic field of a table wrapped in a parent\n   * object, passing the parent object's version here will ensure we get the dynamic field's\n   * state at the moment that parent's version was created.\n   *\n   * Also, if this Owner is an object itself, `rootVersion` will be used to bound its version\n   * from above when querying `Owner.asObject`. This can be used, for example, to get the\n   * contents of a dynamic object field when its parent was at `rootVersion`.\n   *\n   * If `rootVersion` is omitted, dynamic fields will be from a consistent snapshot of the Sui\n   * state at the latest checkpoint known to the GraphQL RPC. Similarly, `Owner.asObject` will\n   * return the object's version at the latest checkpoint.\n   */\n  owner?: Maybe<Owner>;\n  /**\n   * The package corresponding to the given address (at the optionally given version).\n   *\n   * When no version is given, the package is loaded directly from the address given. Otherwise,\n   * the address is translated before loading to point to the package whose original ID matches\n   * the package at `address`, but whose version is `version`. For non-system packages, this\n   * might result in a different address than `address` because different versions of a package,\n   * introduced by upgrades, exist at distinct addresses.\n   *\n   * Note that this interpretation of `version` is different from a historical object read (the\n   * interpretation of `version` for the `object` query).\n   */\n  package?: Maybe<MovePackage>;\n  /** Fetch a package by its name (using dot move service) */\n  packageByName?: Maybe<MovePackage>;\n  /**\n   * Fetch all versions of package at `address` (packages that share this package's original ID),\n   * optionally bounding the versions exclusively from below with `afterVersion`, or from above\n   * with `beforeVersion`.\n   */\n  packageVersions: MovePackageConnection;\n  /**\n   * The Move packages that exist in the network, optionally filtered to be strictly before\n   * `beforeCheckpoint` and/or strictly after `afterCheckpoint`.\n   *\n   * This query returns all versions of a given user package that appear between the specified\n   * checkpoints, but only records the latest versions of system packages.\n   */\n  packages: MovePackageConnection;\n  /**\n   * Fetch the protocol config by protocol version (defaults to the latest protocol\n   * version known to the GraphQL service).\n   */\n  protocolConfig: ProtocolConfigs;\n  /** Resolves a SuiNS `domain` name to an address, if it has been bound. */\n  resolveSuinsAddress?: Maybe<Address>;\n  /** Configuration for this RPC service */\n  serviceConfig: ServiceConfig;\n  /** Fetch a transaction block by its transaction digest. */\n  transactionBlock?: Maybe<TransactionBlock>;\n  /**\n   * The transaction blocks that exist in the network.\n   *\n   * `scanLimit` restricts the number of candidate transactions scanned when gathering a page of\n   * results. It is required for queries that apply more than two complex filters (on function,\n   * kind, sender, recipient, input object, changed object, or ids), and can be at most\n   * `serviceConfig.maxScanLimit`.\n   *\n   * When the scan limit is reached the page will be returned even if it has fewer than `first`\n   * results when paginating forward (`last` when paginating backwards). If there are more\n   * transactions to scan, `pageInfo.hasNextPage` (or `pageInfo.hasPreviousPage`) will be set to\n   * `true`, and `PageInfo.endCursor` (or `PageInfo.startCursor`) will be set to the last\n   * transaction that was scanned as opposed to the last (or first) transaction in the page.\n   *\n   * Requesting the next (or previous) page after this cursor will resume the search, scanning\n   * the next `scanLimit` many transactions in the direction of pagination, and so on until all\n   * transactions in the scanning range have been visited.\n   *\n   * By default, the scanning range includes all transactions known to GraphQL, but it can be\n   * restricted by the `after` and `before` cursors, and the `beforeCheckpoint`,\n   * `afterCheckpoint` and `atCheckpoint` filters.\n   */\n  transactionBlocks: TransactionBlockConnection;\n  /**\n   * Fetch a structured representation of a concrete type, including its layout information.\n   * Fails if the type is malformed.\n   */\n  type: MoveType;\n  /** Fetch a type that includes dot move service names in it. */\n  typeByName: MoveType;\n  /**\n   * Verify a zkLogin signature based on the provided transaction or personal message\n   * based on current epoch, chain id, and latest JWKs fetched on-chain. If the\n   * signature is valid, the function returns a `ZkLoginVerifyResult` with success as\n   * true and an empty list of errors. If the signature is invalid, the function returns\n   * a `ZkLoginVerifyResult` with success as false with a list of errors.\n   *\n   * - `bytes` is either the personal message in raw bytes or transaction data bytes in\n   * BCS-encoded and then Base64-encoded.\n   * - `signature` is a serialized zkLogin signature that is Base64-encoded.\n   * - `intentScope` is an enum that specifies the intent scope to be used to parse bytes.\n   * - `author` is the address of the signer of the transaction or personal msg.\n   */\n  verifyZkloginSignature: ZkLoginVerifyResult;\n};\n\n\nexport type QueryAddressArgs = {\n  address: Scalars['SuiAddress']['input'];\n};\n\n\nexport type QueryCheckpointArgs = {\n  id?: InputMaybe<CheckpointId>;\n};\n\n\nexport type QueryCheckpointsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\nexport type QueryCoinMetadataArgs = {\n  coinType: Scalars['String']['input'];\n};\n\n\nexport type QueryCoinsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\nexport type QueryDryRunTransactionBlockArgs = {\n  skipChecks?: InputMaybe<Scalars['Boolean']['input']>;\n  txBytes: Scalars['String']['input'];\n  txMeta?: InputMaybe<TransactionMetadata>;\n};\n\n\nexport type QueryEpochArgs = {\n  id?: InputMaybe<Scalars['UInt53']['input']>;\n};\n\n\nexport type QueryEpochsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\nexport type QueryEventsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<EventFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\nexport type QueryLatestPackageArgs = {\n  address: Scalars['SuiAddress']['input'];\n};\n\n\nexport type QueryMultiGetObjectsArgs = {\n  keys: Array<ObjectKey>;\n};\n\n\nexport type QueryObjectArgs = {\n  address: Scalars['SuiAddress']['input'];\n  version?: InputMaybe<Scalars['UInt53']['input']>;\n};\n\n\nexport type QueryObjectsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<ObjectFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\nexport type QueryOwnerArgs = {\n  address: Scalars['SuiAddress']['input'];\n  rootVersion?: InputMaybe<Scalars['UInt53']['input']>;\n};\n\n\nexport type QueryPackageArgs = {\n  address: Scalars['SuiAddress']['input'];\n  version?: InputMaybe<Scalars['UInt53']['input']>;\n};\n\n\nexport type QueryPackageByNameArgs = {\n  name: Scalars['String']['input'];\n};\n\n\nexport type QueryPackageVersionsArgs = {\n  address: Scalars['SuiAddress']['input'];\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<MovePackageVersionFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\nexport type QueryPackagesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<MovePackageCheckpointFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\nexport type QueryProtocolConfigArgs = {\n  protocolVersion?: InputMaybe<Scalars['UInt53']['input']>;\n};\n\n\nexport type QueryResolveSuinsAddressArgs = {\n  domain: Scalars['String']['input'];\n};\n\n\nexport type QueryTransactionBlockArgs = {\n  digest: Scalars['String']['input'];\n};\n\n\nexport type QueryTransactionBlocksArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<TransactionBlockFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  scanLimit?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\nexport type QueryTypeArgs = {\n  type: Scalars['String']['input'];\n};\n\n\nexport type QueryTypeByNameArgs = {\n  name: Scalars['String']['input'];\n};\n\n\nexport type QueryVerifyZkloginSignatureArgs = {\n  author: Scalars['SuiAddress']['input'];\n  bytes: Scalars['Base64']['input'];\n  intentScope: ZkLoginIntentScope;\n  signature: Scalars['Base64']['input'];\n};\n\nexport type RandomnessStateCreateTransaction = {\n  __typename?: 'RandomnessStateCreateTransaction';\n  /** A workaround to define an empty variant of a GraphQL union. */\n  _?: Maybe<Scalars['Boolean']['output']>;\n};\n\n/** System transaction to update the source of on-chain randomness. */\nexport type RandomnessStateUpdateTransaction = {\n  __typename?: 'RandomnessStateUpdateTransaction';\n  /** Epoch of the randomness state update transaction. */\n  epoch?: Maybe<Epoch>;\n  /** Updated random bytes, encoded as Base64. */\n  randomBytes: Scalars['Base64']['output'];\n  /** The initial version the randomness object was shared at. */\n  randomnessObjInitialSharedVersion: Scalars['UInt53']['output'];\n  /** Randomness round of the update. */\n  randomnessRound: Scalars['UInt53']['output'];\n};\n\n/** A Move object that can be received in this transaction. */\nexport type Receiving = {\n  __typename?: 'Receiving';\n  /** ID of the object being read. */\n  address: Scalars['SuiAddress']['output'];\n  /**\n   * 32-byte hash that identifies the object's contents at this version, encoded as a Base58\n   * string.\n   */\n  digest: Scalars['String']['output'];\n  /** The object at this version.  May not be available due to pruning. */\n  object?: Maybe<Object>;\n  /** Version of the object being read. */\n  version: Scalars['UInt53']['output'];\n};\n\n/** The result of another transaction command. */\nexport type Result = {\n  __typename?: 'Result';\n  /** The index of the previous command (0-indexed) that returned this result. */\n  cmd: Scalars['Int']['output'];\n  /**\n   * If the previous command returns multiple values, this is the index of the individual result\n   * among the multiple results from that command (also 0-indexed).\n   */\n  ix?: Maybe<Scalars['Int']['output']>;\n};\n\n/** Information about whether epoch changes are using safe mode. */\nexport type SafeMode = {\n  __typename?: 'SafeMode';\n  /**\n   * Whether safe mode was used for the last epoch change.  The system will retry a full epoch\n   * change on every epoch boundary and automatically reset this flag if so.\n   */\n  enabled?: Maybe<Scalars['Boolean']['output']>;\n  /**\n   * Accumulated fees for computation and cost that have not been added to the various reward\n   * pools, because the full epoch change did not happen.\n   */\n  gasSummary?: Maybe<GasCostSummary>;\n};\n\n/** The enabled features and service limits configured by the server. */\nexport type ServiceConfig = {\n  __typename?: 'ServiceConfig';\n  /** Default number of elements allowed on a single page of a connection. */\n  defaultPageSize: Scalars['Int']['output'];\n  /** List of all features that are enabled on this GraphQL service. */\n  enabledFeatures: Array<Feature>;\n  /** Check whether `feature` is enabled on this GraphQL service. */\n  isEnabled: Scalars['Boolean']['output'];\n  /**\n   * Maximum estimated cost of a database query used to serve a GraphQL request.  This is\n   * measured in the same units that the database uses in EXPLAIN queries.\n   */\n  maxDbQueryCost: Scalars['Int']['output'];\n  /** Maximum nesting allowed in struct fields when calculating the layout of a single Move Type. */\n  maxMoveValueDepth: Scalars['Int']['output'];\n  /** Maximum number of keys that can be passed to a `multiGetObjects` query. */\n  maxMultiGetObjectsKeys: Scalars['Int']['output'];\n  /**\n   * The maximum number of output nodes in a GraphQL response.\n   *\n   * Non-connection nodes have a count of 1, while connection nodes are counted as\n   * the specified 'first' or 'last' number of items, or the default_page_size\n   * as set by the server if those arguments are not set.\n   *\n   * Counts accumulate multiplicatively down the query tree. For example, if a query starts\n   * with a connection of first: 10 and has a field to a connection with last: 20, the count\n   * at the second level would be 200 nodes. This is then summed to the count of 10 nodes\n   * at the first level, for a total of 210 nodes.\n   */\n  maxOutputNodes: Scalars['Int']['output'];\n  /** Maximum number of elements allowed on a single page of a connection. */\n  maxPageSize: Scalars['Int']['output'];\n  /** The maximum depth a GraphQL query can be to be accepted by this service. */\n  maxQueryDepth: Scalars['Int']['output'];\n  /** The maximum number of nodes (field names) the service will accept in a single query. */\n  maxQueryNodes: Scalars['Int']['output'];\n  /**\n   * The maximum bytes allowed for the JSON object in the request body of a GraphQL query, for\n   * the read part of the query.\n   * In case of mutations or dryRunTransactionBlocks the txBytes and signatures are not\n   * included in this limit.\n   */\n  maxQueryPayloadSize: Scalars['Int']['output'];\n  /** Maximum number of candidates to scan when gathering a page of results. */\n  maxScanLimit: Scalars['Int']['output'];\n  /** Maximum number of transaction ids that can be passed to a `TransactionBlockFilter`. */\n  maxTransactionIds: Scalars['Int']['output'];\n  /**\n   * The maximum bytes allowed for the `txBytes` and `signatures` fields of the GraphQL mutation\n   * `executeTransactionBlock` node, or for the `txBytes` of a `dryRunTransactionBlock`.\n   *\n   * It is the value of the maximum transaction bytes (including the signatures) allowed by the\n   * protocol, plus the Base64 overhead (roughly 1/3 of the original string).\n   */\n  maxTransactionPayloadSize: Scalars['Int']['output'];\n  /** Maximum nesting allowed in type arguments in Move Types resolved by this service. */\n  maxTypeArgumentDepth: Scalars['Int']['output'];\n  /**\n   * Maximum number of type arguments passed into a generic instantiation of a Move Type resolved\n   * by this service.\n   */\n  maxTypeArgumentWidth: Scalars['Int']['output'];\n  /**\n   * Maximum number of structs that need to be processed when calculating the layout of a single\n   * Move Type.\n   */\n  maxTypeNodes: Scalars['Int']['output'];\n  /**\n   * Maximum time in milliseconds spent waiting for a response from fullnode after issuing a\n   * a transaction to execute. Note that the transaction may still succeed even in the case of a\n   * timeout. Transactions are idempotent, so a transaction that times out should be resubmitted\n   * until the network returns a definite response (success or failure, not timeout).\n   */\n  mutationTimeoutMs: Scalars['Int']['output'];\n  /** Maximum time in milliseconds that will be spent to serve one query request. */\n  requestTimeoutMs: Scalars['Int']['output'];\n};\n\n\n/** The enabled features and service limits configured by the server. */\nexport type ServiceConfigIsEnabledArgs = {\n  feature: Feature;\n};\n\n/**\n * A shared object is an object that is shared using the 0x2::transfer::share_object function.\n * Unlike owned objects, once an object is shared, it stays mutable and is accessible by anyone.\n */\nexport type Shared = {\n  __typename?: 'Shared';\n  initialSharedVersion: Scalars['UInt53']['output'];\n};\n\n/** A Move object that's shared. */\nexport type SharedInput = {\n  __typename?: 'SharedInput';\n  address: Scalars['SuiAddress']['output'];\n  /** The version that this this object was shared at. */\n  initialSharedVersion: Scalars['UInt53']['output'];\n  /**\n   * Controls whether the transaction block can reference the shared object as a mutable\n   * reference or by value. This has implications for scheduling: Transactions that just read\n   * shared objects at a certain version (mutable = false) can be executed concurrently, while\n   * transactions that write shared objects (mutable = true) must be executed serially with\n   * respect to each other.\n   */\n  mutable: Scalars['Boolean']['output'];\n};\n\n/** The transaction accpeted a shared object as input, but its execution was cancelled. */\nexport type SharedObjectCancelled = {\n  __typename?: 'SharedObjectCancelled';\n  /** ID of the shared object. */\n  address: Scalars['SuiAddress']['output'];\n  /** The assigned shared object version. It is a special version indicating transaction cancellation reason. */\n  version: Scalars['UInt53']['output'];\n};\n\n/**\n * The transaction accepted a shared object as input, but it was deleted before the transaction\n * executed.\n */\nexport type SharedObjectDelete = {\n  __typename?: 'SharedObjectDelete';\n  /** ID of the shared object. */\n  address: Scalars['SuiAddress']['output'];\n  /**\n   * Whether this transaction intended to use this shared object mutably or not. See\n   * `SharedInput.mutable` for further details.\n   */\n  mutable: Scalars['Boolean']['output'];\n  /**\n   * The version of the shared object that was assigned to this transaction during by consensus,\n   * during sequencing.\n   */\n  version: Scalars['UInt53']['output'];\n};\n\n/** The transaction accepted a shared object as input, but only to read it. */\nexport type SharedObjectRead = {\n  __typename?: 'SharedObjectRead';\n  /** ID of the object being read. */\n  address: Scalars['SuiAddress']['output'];\n  /**\n   * 32-byte hash that identifies the object's contents at this version, encoded as a Base58\n   * string.\n   */\n  digest: Scalars['String']['output'];\n  /** The object at this version.  May not be available due to pruning. */\n  object?: Maybe<Object>;\n  /** Version of the object being read. */\n  version: Scalars['UInt53']['output'];\n};\n\n/**\n * Splits off coins with denominations in `amounts` from `coin`, returning multiple results (as\n * many as there are amounts.)\n */\nexport type SplitCoinsTransaction = {\n  __typename?: 'SplitCoinsTransaction';\n  /** The denominations to split off from the coin. */\n  amounts: Array<TransactionArgument>;\n  /** The coin to split. */\n  coin: TransactionArgument;\n};\n\n/** The stake's possible status: active, pending, or unstaked. */\nexport enum StakeStatus {\n  /** The stake object is active in a staking pool and it is generating rewards. */\n  Active = 'ACTIVE',\n  /** The stake awaits to join a staking pool in the next epoch. */\n  Pending = 'PENDING',\n  /** The stake is no longer active in any staking pool. */\n  Unstaked = 'UNSTAKED'\n}\n\n/** Parameters that control the distribution of the stake subsidy. */\nexport type StakeSubsidy = {\n  __typename?: 'StakeSubsidy';\n  /**\n   * SUI set aside for stake subsidies -- reduces over time as stake subsidies are paid out over\n   * time.\n   */\n  balance?: Maybe<Scalars['BigInt']['output']>;\n  /** Amount of stake subsidy deducted from the balance per distribution -- decays over time. */\n  currentDistributionAmount?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * Percentage of the current distribution amount to deduct at the end of the current subsidy\n   * period, expressed in basis points.\n   */\n  decreaseRate?: Maybe<Scalars['Int']['output']>;\n  /**\n   * Number of times stake subsidies have been distributed subsidies are distributed with other\n   * staking rewards, at the end of the epoch.\n   */\n  distributionCounter?: Maybe<Scalars['Int']['output']>;\n  /**\n   * Maximum number of stake subsidy distributions that occur with the same distribution amount\n   * (before the amount is reduced).\n   */\n  periodLength?: Maybe<Scalars['Int']['output']>;\n};\n\n/** Represents a `0x3::staking_pool::StakedSui` Move object on-chain. */\nexport type StakedSui = IMoveObject & IObject & IOwner & {\n  __typename?: 'StakedSui';\n  /** The epoch at which this stake became active. */\n  activatedEpoch?: Maybe<Epoch>;\n  address: Scalars['SuiAddress']['output'];\n  /**\n   * Total balance of all coins with marker type owned by this object. If type is not supplied,\n   * it defaults to `0x2::sui::SUI`.\n   */\n  balance?: Maybe<Balance>;\n  /** The balances of all coin types owned by this object. */\n  balances: BalanceConnection;\n  /** The Base64-encoded BCS serialization of the object's content. */\n  bcs?: Maybe<Scalars['Base64']['output']>;\n  /**\n   * The coin objects for this object.\n   *\n   * `type` is a filter on the coin's type parameter, defaulting to `0x2::sui::SUI`.\n   */\n  coins: CoinConnection;\n  /**\n   * Displays the contents of the Move object in a JSON string and through GraphQL types. Also\n   * provides the flat representation of the type signature, and the BCS of the corresponding\n   * data.\n   */\n  contents?: Maybe<MoveValue>;\n  /** The domain explicitly configured as the default domain pointing to this object. */\n  defaultSuinsName?: Maybe<Scalars['String']['output']>;\n  /** 32-byte hash that identifies the object's contents, encoded as a Base58 string. */\n  digest?: Maybe<Scalars['String']['output']>;\n  /**\n   * The set of named templates defined on-chain for the type of this object, to be handled\n   * off-chain. The server substitutes data from the object into these templates to generate a\n   * display string per template.\n   */\n  display?: Maybe<Array<DisplayEntry>>;\n  /**\n   * Access a dynamic field on an object using its name. Names are arbitrary Move values whose\n   * type have `copy`, `drop`, and `store`, and are specified using their type, and their BCS\n   * contents, Base64 encoded.\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicField?: Maybe<DynamicField>;\n  /**\n   * The dynamic fields and dynamic object fields on an object.\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicFields: DynamicFieldConnection;\n  /**\n   * Access a dynamic object field on an object using its name. Names are arbitrary Move values\n   * whose type have `copy`, `drop`, and `store`, and are specified using their type, and their\n   * BCS contents, Base64 encoded. The value of a dynamic object field can also be accessed\n   * off-chain directly via its address (e.g. using `Query.object`).\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicObjectField?: Maybe<DynamicField>;\n  /**\n   * The estimated reward for this stake object, calculated as:\n   *\n   * principal * (initial_stake_rate / current_stake_rate - 1.0)\n   *\n   * Or 0, if this value is negative, where:\n   *\n   * - `initial_stake_rate` is the stake rate at the epoch this stake was activated at.\n   * - `current_stake_rate` is the stake rate in the current epoch.\n   *\n   * This value is only available if the stake is active.\n   */\n  estimatedReward?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * Determines whether a transaction can transfer this object, using the TransferObjects\n   * transaction command or `sui::transfer::public_transfer`, both of which require the object to\n   * have the `key` and `store` abilities.\n   */\n  hasPublicTransfer: Scalars['Boolean']['output'];\n  /** Objects owned by this object, optionally `filter`-ed. */\n  objects: MoveObjectConnection;\n  /** The owner type of this object: Immutable, Shared, Parent, Address */\n  owner?: Maybe<ObjectOwner>;\n  /** The object id of the validator staking pool this stake belongs to. */\n  poolId?: Maybe<Scalars['SuiAddress']['output']>;\n  /** The transaction block that created this version of the object. */\n  previousTransactionBlock?: Maybe<TransactionBlock>;\n  /** The SUI that was initially staked. */\n  principal?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * The transaction blocks that sent objects to this object.\n   *\n   * `scanLimit` restricts the number of candidate transactions scanned when gathering a page of\n   * results. It is required for queries that apply more than two complex filters (on function,\n   * kind, sender, recipient, input object, changed object, or ids), and can be at most\n   * `serviceConfig.maxScanLimit`.\n   *\n   * When the scan limit is reached the page will be returned even if it has fewer than `first`\n   * results when paginating forward (`last` when paginating backwards). If there are more\n   * transactions to scan, `pageInfo.hasNextPage` (or `pageInfo.hasPreviousPage`) will be set to\n   * `true`, and `PageInfo.endCursor` (or `PageInfo.startCursor`) will be set to the last\n   * transaction that was scanned as opposed to the last (or first) transaction in the page.\n   *\n   * Requesting the next (or previous) page after this cursor will resume the search, scanning\n   * the next `scanLimit` many transactions in the direction of pagination, and so on until all\n   * transactions in the scanning range have been visited.\n   *\n   * By default, the scanning range includes all transactions known to GraphQL, but it can be\n   * restricted by the `after` and `before` cursors, and the `beforeCheckpoint`,\n   * `afterCheckpoint` and `atCheckpoint` filters.\n   */\n  receivedTransactionBlocks: TransactionBlockConnection;\n  /** The epoch at which this object was requested to join a stake pool. */\n  requestedEpoch?: Maybe<Epoch>;\n  /** A stake can be pending, active, or unstaked */\n  stakeStatus: StakeStatus;\n  /** The `0x3::staking_pool::StakedSui` objects owned by this object. */\n  stakedSuis: StakedSuiConnection;\n  /**\n   * The current status of the object as read from the off-chain store. The possible states are:\n   * NOT_INDEXED, the object is loaded from serialized data, such as the contents of a genesis or\n   * system package upgrade transaction. LIVE, the version returned is the most recent for the\n   * object, and it is not deleted or wrapped at that version. HISTORICAL, the object was\n   * referenced at a specific version or checkpoint, so is fetched from historical tables and may\n   * not be the latest version of the object. WRAPPED_OR_DELETED, the object is deleted or\n   * wrapped and only partial information can be loaded.\"\n   */\n  status: ObjectKind;\n  /**\n   * The amount of SUI we would rebate if this object gets deleted or mutated. This number is\n   * recalculated based on the present storage gas price.\n   */\n  storageRebate?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * The SuinsRegistration NFTs owned by this object. These grant the owner the capability to\n   * manage the associated domain.\n   */\n  suinsRegistrations: SuinsRegistrationConnection;\n  version: Scalars['UInt53']['output'];\n};\n\n\n/** Represents a `0x3::staking_pool::StakedSui` Move object on-chain. */\nexport type StakedSuiBalanceArgs = {\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/** Represents a `0x3::staking_pool::StakedSui` Move object on-chain. */\nexport type StakedSuiBalancesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** Represents a `0x3::staking_pool::StakedSui` Move object on-chain. */\nexport type StakedSuiCoinsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\n/** Represents a `0x3::staking_pool::StakedSui` Move object on-chain. */\nexport type StakedSuiDefaultSuinsNameArgs = {\n  format?: InputMaybe<DomainFormat>;\n};\n\n\n/** Represents a `0x3::staking_pool::StakedSui` Move object on-chain. */\nexport type StakedSuiDynamicFieldArgs = {\n  name: DynamicFieldName;\n};\n\n\n/** Represents a `0x3::staking_pool::StakedSui` Move object on-chain. */\nexport type StakedSuiDynamicFieldsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** Represents a `0x3::staking_pool::StakedSui` Move object on-chain. */\nexport type StakedSuiDynamicObjectFieldArgs = {\n  name: DynamicFieldName;\n};\n\n\n/** Represents a `0x3::staking_pool::StakedSui` Move object on-chain. */\nexport type StakedSuiObjectsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<ObjectFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** Represents a `0x3::staking_pool::StakedSui` Move object on-chain. */\nexport type StakedSuiReceivedTransactionBlocksArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<TransactionBlockFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  scanLimit?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** Represents a `0x3::staking_pool::StakedSui` Move object on-chain. */\nexport type StakedSuiStakedSuisArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** Represents a `0x3::staking_pool::StakedSui` Move object on-chain. */\nexport type StakedSuiSuinsRegistrationsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\nexport type StakedSuiConnection = {\n  __typename?: 'StakedSuiConnection';\n  /** A list of edges. */\n  edges: Array<StakedSuiEdge>;\n  /** A list of nodes. */\n  nodes: Array<StakedSui>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type StakedSuiEdge = {\n  __typename?: 'StakedSuiEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: StakedSui;\n};\n\n/** SUI set aside to account for objects stored on-chain. */\nexport type StorageFund = {\n  __typename?: 'StorageFund';\n  /**\n   * The portion of the storage fund that will never be refunded through storage rebates.\n   *\n   * The system maintains an invariant that the sum of all storage fees into the storage fund is\n   * equal to the sum of of all storage rebates out, the total storage rebates remaining, and the\n   * non-refundable balance.\n   */\n  nonRefundableBalance?: Maybe<Scalars['BigInt']['output']>;\n  /** Sum of storage rebates of live objects on chain. */\n  totalObjectStorageRebates?: Maybe<Scalars['BigInt']['output']>;\n};\n\nexport type StoreExecutionTimeObservationsTransaction = {\n  __typename?: 'StoreExecutionTimeObservationsTransaction';\n  /** A workaround to define an empty variant of a GraphQL union. */\n  _?: Maybe<Scalars['Boolean']['output']>;\n};\n\nexport type SuinsRegistration = IMoveObject & IObject & IOwner & {\n  __typename?: 'SuinsRegistration';\n  address: Scalars['SuiAddress']['output'];\n  /**\n   * Total balance of all coins with marker type owned by this object. If type is not supplied,\n   * it defaults to `0x2::sui::SUI`.\n   */\n  balance?: Maybe<Balance>;\n  /** The balances of all coin types owned by this object. */\n  balances: BalanceConnection;\n  /** The Base64-encoded BCS serialization of the object's content. */\n  bcs?: Maybe<Scalars['Base64']['output']>;\n  /**\n   * The coin objects for this object.\n   *\n   * `type` is a filter on the coin's type parameter, defaulting to `0x2::sui::SUI`.\n   */\n  coins: CoinConnection;\n  /**\n   * Displays the contents of the Move object in a JSON string and through GraphQL types. Also\n   * provides the flat representation of the type signature, and the BCS of the corresponding\n   * data.\n   */\n  contents?: Maybe<MoveValue>;\n  /** The domain explicitly configured as the default domain pointing to this object. */\n  defaultSuinsName?: Maybe<Scalars['String']['output']>;\n  /** 32-byte hash that identifies the object's contents, encoded as a Base58 string. */\n  digest?: Maybe<Scalars['String']['output']>;\n  /**\n   * The set of named templates defined on-chain for the type of this object, to be handled\n   * off-chain. The server substitutes data from the object into these templates to generate a\n   * display string per template.\n   */\n  display?: Maybe<Array<DisplayEntry>>;\n  /** Domain name of the SuinsRegistration object */\n  domain: Scalars['String']['output'];\n  /**\n   * Access a dynamic field on an object using its name. Names are arbitrary Move values whose\n   * type have `copy`, `drop`, and `store`, and are specified using their type, and their BCS\n   * contents, Base64 encoded.\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicField?: Maybe<DynamicField>;\n  /**\n   * The dynamic fields and dynamic object fields on an object.\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicFields: DynamicFieldConnection;\n  /**\n   * Access a dynamic object field on an object using its name. Names are arbitrary Move values\n   * whose type have `copy`, `drop`, and `store`, and are specified using their type, and their\n   * BCS contents, Base64 encoded. The value of a dynamic object field can also be accessed\n   * off-chain directly via its address (e.g. using `Query.object`).\n   *\n   * Dynamic fields on wrapped objects can be accessed by using the same API under the Owner\n   * type.\n   */\n  dynamicObjectField?: Maybe<DynamicField>;\n  /**\n   * Determines whether a transaction can transfer this object, using the TransferObjects\n   * transaction command or `sui::transfer::public_transfer`, both of which require the object to\n   * have the `key` and `store` abilities.\n   */\n  hasPublicTransfer: Scalars['Boolean']['output'];\n  /** Objects owned by this object, optionally `filter`-ed. */\n  objects: MoveObjectConnection;\n  /** The owner type of this object: Immutable, Shared, Parent, Address */\n  owner?: Maybe<ObjectOwner>;\n  /** The transaction block that created this version of the object. */\n  previousTransactionBlock?: Maybe<TransactionBlock>;\n  /**\n   * The transaction blocks that sent objects to this object.\n   *\n   * `scanLimit` restricts the number of candidate transactions scanned when gathering a page of\n   * results. It is required for queries that apply more than two complex filters (on function,\n   * kind, sender, recipient, input object, changed object, or ids), and can be at most\n   * `serviceConfig.maxScanLimit`.\n   *\n   * When the scan limit is reached the page will be returned even if it has fewer than `first`\n   * results when paginating forward (`last` when paginating backwards). If there are more\n   * transactions to scan, `pageInfo.hasNextPage` (or `pageInfo.hasPreviousPage`) will be set to\n   * `true`, and `PageInfo.endCursor` (or `PageInfo.startCursor`) will be set to the last\n   * transaction that was scanned as opposed to the last (or first) transaction in the page.\n   *\n   * Requesting the next (or previous) page after this cursor will resume the search, scanning\n   * the next `scanLimit` many transactions in the direction of pagination, and so on until all\n   * transactions in the scanning range have been visited.\n   *\n   * By default, the scanning range includes all transactions known to GraphQL, but it can be\n   * restricted by the `after` and `before` cursors, and the `beforeCheckpoint`,\n   * `afterCheckpoint` and `atCheckpoint` filters.\n   */\n  receivedTransactionBlocks: TransactionBlockConnection;\n  /** The `0x3::staking_pool::StakedSui` objects owned by this object. */\n  stakedSuis: StakedSuiConnection;\n  /**\n   * The current status of the object as read from the off-chain store. The possible states are:\n   * NOT_INDEXED, the object is loaded from serialized data, such as the contents of a genesis or\n   * system package upgrade transaction. LIVE, the version returned is the most recent for the\n   * object, and it is not deleted or wrapped at that version. HISTORICAL, the object was\n   * referenced at a specific version or checkpoint, so is fetched from historical tables and may\n   * not be the latest version of the object. WRAPPED_OR_DELETED, the object is deleted or\n   * wrapped and only partial information can be loaded.\"\n   */\n  status: ObjectKind;\n  /**\n   * The amount of SUI we would rebate if this object gets deleted or mutated. This number is\n   * recalculated based on the present storage gas price.\n   */\n  storageRebate?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * The SuinsRegistration NFTs owned by this object. These grant the owner the capability to\n   * manage the associated domain.\n   */\n  suinsRegistrations: SuinsRegistrationConnection;\n  version: Scalars['UInt53']['output'];\n};\n\n\nexport type SuinsRegistrationBalanceArgs = {\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\nexport type SuinsRegistrationBalancesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\nexport type SuinsRegistrationCoinsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  type?: InputMaybe<Scalars['String']['input']>;\n};\n\n\nexport type SuinsRegistrationDefaultSuinsNameArgs = {\n  format?: InputMaybe<DomainFormat>;\n};\n\n\nexport type SuinsRegistrationDynamicFieldArgs = {\n  name: DynamicFieldName;\n};\n\n\nexport type SuinsRegistrationDynamicFieldsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\nexport type SuinsRegistrationDynamicObjectFieldArgs = {\n  name: DynamicFieldName;\n};\n\n\nexport type SuinsRegistrationObjectsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<ObjectFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\nexport type SuinsRegistrationReceivedTransactionBlocksArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<TransactionBlockFilter>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n  scanLimit?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\nexport type SuinsRegistrationStakedSuisArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\nexport type SuinsRegistrationSuinsRegistrationsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\nexport type SuinsRegistrationConnection = {\n  __typename?: 'SuinsRegistrationConnection';\n  /** A list of edges. */\n  edges: Array<SuinsRegistrationEdge>;\n  /** A list of nodes. */\n  nodes: Array<SuinsRegistration>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type SuinsRegistrationEdge = {\n  __typename?: 'SuinsRegistrationEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: SuinsRegistration;\n};\n\n/** Details of the system that are decided during genesis. */\nexport type SystemParameters = {\n  __typename?: 'SystemParameters';\n  /** Target duration of an epoch, in milliseconds. */\n  durationMs?: Maybe<Scalars['BigInt']['output']>;\n  /** The maximum number of active validators that the system supports. */\n  maxValidatorCount?: Maybe<Scalars['Int']['output']>;\n  /** The minimum number of active validators that the system supports. */\n  minValidatorCount?: Maybe<Scalars['Int']['output']>;\n  /** Minimum stake needed to become a new validator. */\n  minValidatorJoiningStake?: Maybe<Scalars['BigInt']['output']>;\n  /** The epoch at which stake subsidies start being paid out. */\n  stakeSubsidyStartEpoch?: Maybe<Scalars['UInt53']['output']>;\n  /**\n   * The number of epochs that a validator has to recover from having less than\n   * `validatorLowStakeThreshold` stake.\n   */\n  validatorLowStakeGracePeriod?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * Validators with stake below this threshold will enter the grace period (see\n   * `validatorLowStakeGracePeriod`), after which they are removed from the active validator set.\n   */\n  validatorLowStakeThreshold?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * Validators with stake below this threshold will be removed from the active validator set\n   * at the next epoch boundary, without a grace period.\n   */\n  validatorVeryLowStakeThreshold?: Maybe<Scalars['BigInt']['output']>;\n};\n\n/** An argument to a programmable transaction command. */\nexport type TransactionArgument = GasCoin | Input | Result;\n\nexport type TransactionBlock = {\n  __typename?: 'TransactionBlock';\n  /** Serialized form of this transaction's `TransactionData`, BCS serialized and Base64 encoded. */\n  bcs?: Maybe<Scalars['Base64']['output']>;\n  /**\n   * A 32-byte hash that uniquely identifies the transaction block contents, encoded in Base58.\n   * This serves as a unique id for the block on chain.\n   */\n  digest?: Maybe<Scalars['String']['output']>;\n  /** The effects field captures the results to the chain of executing this transaction. */\n  effects?: Maybe<TransactionBlockEffects>;\n  /**\n   * This field is set by senders of a transaction block. It is an epoch reference that sets a\n   * deadline after which validators will no longer consider the transaction valid. By default,\n   * there is no deadline for when a transaction must execute.\n   */\n  expiration?: Maybe<Epoch>;\n  /**\n   * The gas input field provides information on what objects were used as gas as well as the\n   * owner of the gas object(s) and information on the gas price and budget.\n   *\n   * If the owner of the gas object(s) is not the same as the sender, the transaction block is a\n   * sponsored transaction block.\n   */\n  gasInput?: Maybe<GasInput>;\n  /**\n   * The type of this transaction as well as the commands and/or parameters comprising the\n   * transaction of this kind.\n   */\n  kind?: Maybe<TransactionBlockKind>;\n  /**\n   * The address corresponding to the public key that signed this transaction. System\n   * transactions do not have senders.\n   */\n  sender?: Maybe<Address>;\n  /**\n   * A list of all signatures, Base64-encoded, from senders, and potentially the gas owner if\n   * this is a sponsored transaction.\n   */\n  signatures?: Maybe<Array<Scalars['Base64']['output']>>;\n};\n\nexport type TransactionBlockConnection = {\n  __typename?: 'TransactionBlockConnection';\n  /** A list of edges. */\n  edges: Array<TransactionBlockEdge>;\n  /** A list of nodes. */\n  nodes: Array<TransactionBlock>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type TransactionBlockEdge = {\n  __typename?: 'TransactionBlockEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: TransactionBlock;\n};\n\n/** The effects representing the result of executing a transaction block. */\nexport type TransactionBlockEffects = {\n  __typename?: 'TransactionBlockEffects';\n  /** The error code of the Move abort, populated if this transaction failed with a Move abort. */\n  abortCode?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * The effect this transaction had on the balances (sum of coin values per coin type) of\n   * addresses and objects.\n   */\n  balanceChanges: BalanceChangeConnection;\n  /** Base64 encoded bcs serialization of the on-chain transaction effects. */\n  bcs: Scalars['Base64']['output'];\n  /** The checkpoint this transaction was finalized in. */\n  checkpoint?: Maybe<Checkpoint>;\n  /** Transactions whose outputs this transaction depends upon. */\n  dependencies: DependencyConnection;\n  /** The epoch this transaction was finalized in. */\n  epoch?: Maybe<Epoch>;\n  /**\n   * The reason for a transaction failure, if it did fail.\n   * If the error is a Move abort, the error message will be resolved to a human-readable form if\n   * possible, otherwise it will fall back to displaying the abort code and location.\n   */\n  errors?: Maybe<Scalars['String']['output']>;\n  /** Events emitted by this transaction block. */\n  events: EventConnection;\n  /** Effects to the gas object. */\n  gasEffects?: Maybe<GasEffects>;\n  /**\n   * The latest version of all objects (apart from packages) that have been created or modified\n   * by this transaction, immediately following this transaction.\n   */\n  lamportVersion: Scalars['UInt53']['output'];\n  /** The effect this transaction had on objects on-chain. */\n  objectChanges: ObjectChangeConnection;\n  /** Whether the transaction executed successfully or not. */\n  status?: Maybe<ExecutionStatus>;\n  /** Timestamp corresponding to the checkpoint this transaction was finalized in. */\n  timestamp?: Maybe<Scalars['DateTime']['output']>;\n  /** The transaction that ran to produce these effects. */\n  transactionBlock?: Maybe<TransactionBlock>;\n  /** Shared objects that are referenced by but not changed by this transaction. */\n  unchangedSharedObjects: UnchangedSharedObjectConnection;\n};\n\n\n/** The effects representing the result of executing a transaction block. */\nexport type TransactionBlockEffectsBalanceChangesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** The effects representing the result of executing a transaction block. */\nexport type TransactionBlockEffectsDependenciesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** The effects representing the result of executing a transaction block. */\nexport type TransactionBlockEffectsEventsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** The effects representing the result of executing a transaction block. */\nexport type TransactionBlockEffectsObjectChangesArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n\n/** The effects representing the result of executing a transaction block. */\nexport type TransactionBlockEffectsUnchangedSharedObjectsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\nexport type TransactionBlockFilter = {\n  /**\n   * Limit to transactions that interacted with the given address. The address could be a\n   * sender, sponsor, or recipient of the transaction.\n   */\n  affectedAddress?: InputMaybe<Scalars['SuiAddress']['input']>;\n  /** Limit to transactions that occured strictly after the given checkpoint. */\n  afterCheckpoint?: InputMaybe<Scalars['UInt53']['input']>;\n  /** Limit to transactions in the given checkpoint. */\n  atCheckpoint?: InputMaybe<Scalars['UInt53']['input']>;\n  /** Limit to transaction that occured strictly before the given checkpoint. */\n  beforeCheckpoint?: InputMaybe<Scalars['UInt53']['input']>;\n  /**\n   * Limit to transactions that output a versioon of this object. NOTE: this input filter has\n   * been deprecated in favor of `affectedObject` which offers an easier to understand behavor.\n   *\n   * This filter will be removed with 1.36.0 (2024-10-14), or at least one release after\n   * `affectedObject` is introduced, whichever is later.\n   */\n  changedObject?: InputMaybe<Scalars['SuiAddress']['input']>;\n  /**\n   * Filter transactions by move function called. Calls can be filtered by the `package`,\n   * `package::module`, or the `package::module::name` of their function.\n   */\n  function?: InputMaybe<Scalars['String']['input']>;\n  /**\n   * Limit to transactions that accepted the given object as an input. NOTE: this input filter\n   * has been deprecated in favor of `affectedObject` which offers an easier to under behavior.\n   *\n   * This filter will be removed with 1.36.0 (2024-10-14), or at least one release after\n   * `affectedObject` is introduced, whichever is later.\n   */\n  inputObject?: InputMaybe<Scalars['SuiAddress']['input']>;\n  /** An input filter selecting for either system or programmable transactions. */\n  kind?: InputMaybe<TransactionBlockKindInput>;\n  /** Limit to transactions that were sent by the given address. */\n  sentAddress?: InputMaybe<Scalars['SuiAddress']['input']>;\n  /** Select transactions by their digest. */\n  transactionIds?: InputMaybe<Array<Scalars['String']['input']>>;\n};\n\n/** The kind of transaction block, either a programmable transaction or a system transaction. */\nexport type TransactionBlockKind = AuthenticatorStateUpdateTransaction | ChangeEpochTransaction | ConsensusCommitPrologueTransaction | EndOfEpochTransaction | GenesisTransaction | ProgrammableTransactionBlock | RandomnessStateUpdateTransaction;\n\n/** An input filter selecting for either system or programmable transactions. */\nexport enum TransactionBlockKindInput {\n  /** A user submitted transaction block. */\n  ProgrammableTx = 'PROGRAMMABLE_TX',\n  /**\n   * A system transaction can be one of several types of transactions.\n   * See [unions/transaction-block-kind] for more details.\n   */\n  SystemTx = 'SYSTEM_TX'\n}\n\nexport type TransactionInput = OwnedOrImmutable | Pure | Receiving | SharedInput;\n\nexport type TransactionInputConnection = {\n  __typename?: 'TransactionInputConnection';\n  /** A list of edges. */\n  edges: Array<TransactionInputEdge>;\n  /** A list of nodes. */\n  nodes: Array<TransactionInput>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type TransactionInputEdge = {\n  __typename?: 'TransactionInputEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: TransactionInput;\n};\n\n/**\n * The optional extra data a user can provide to a transaction dry run.\n * `sender` defaults to `0x0`. If `gasObjects` is not present, or is an empty list,\n * it is substituted with a mock Coin object, `gasPrice` defaults to the reference\n * gas price, `gasBudget` defaults to the max gas budget and `gasSponsor` defaults\n * to the sender.\n */\nexport type TransactionMetadata = {\n  gasBudget?: InputMaybe<Scalars['UInt53']['input']>;\n  gasObjects?: InputMaybe<Array<ObjectRef>>;\n  gasPrice?: InputMaybe<Scalars['UInt53']['input']>;\n  gasSponsor?: InputMaybe<Scalars['SuiAddress']['input']>;\n  sender?: InputMaybe<Scalars['SuiAddress']['input']>;\n};\n\n/**\n * Transfers `inputs` to `address`. All inputs must have the `store` ability (allows public\n * transfer) and must not be previously immutable or shared.\n */\nexport type TransferObjectsTransaction = {\n  __typename?: 'TransferObjectsTransaction';\n  /** The address to transfer to. */\n  address: TransactionArgument;\n  /** The objects to transfer. */\n  inputs: Array<TransactionArgument>;\n};\n\n/** Information about which previous versions of a package introduced its types. */\nexport type TypeOrigin = {\n  __typename?: 'TypeOrigin';\n  /** The storage ID of the package that first defined this type. */\n  definingId: Scalars['SuiAddress']['output'];\n  /** Module defining the type. */\n  module: Scalars['String']['output'];\n  /** Name of the struct. */\n  struct: Scalars['String']['output'];\n};\n\n/**\n * Details pertaining to shared objects that are referenced by but not changed by a transaction.\n * This information is considered part of the effects, because although the transaction specifies\n * the shared object as input, consensus must schedule it and pick the version that is actually\n * used.\n */\nexport type UnchangedSharedObject = SharedObjectCancelled | SharedObjectDelete | SharedObjectRead;\n\nexport type UnchangedSharedObjectConnection = {\n  __typename?: 'UnchangedSharedObjectConnection';\n  /** A list of edges. */\n  edges: Array<UnchangedSharedObjectEdge>;\n  /** A list of nodes. */\n  nodes: Array<UnchangedSharedObject>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** An edge in a connection. */\nexport type UnchangedSharedObjectEdge = {\n  __typename?: 'UnchangedSharedObjectEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: UnchangedSharedObject;\n};\n\n/** Upgrades a Move Package. */\nexport type UpgradeTransaction = {\n  __typename?: 'UpgradeTransaction';\n  /** ID of the package being upgraded. */\n  currentPackage: Scalars['SuiAddress']['output'];\n  /** IDs of the transitive dependencies of the package to be published. */\n  dependencies: Array<Scalars['SuiAddress']['output']>;\n  /** Bytecode for the modules to be published, BCS serialized and Base64 encoded. */\n  modules: Array<Scalars['Base64']['output']>;\n  /** The `UpgradeTicket` authorizing the upgrade. */\n  upgradeTicket: TransactionArgument;\n};\n\nexport type Validator = {\n  __typename?: 'Validator';\n  /** The validator's address. */\n  address: Address;\n  /**\n   * The APY of this validator in basis points.\n   * To get the APY in percentage, divide by 100.\n   */\n  apy?: Maybe<Scalars['Int']['output']>;\n  /**\n   * The number of epochs for which this validator has been below the\n   * low stake threshold.\n   */\n  atRisk?: Maybe<Scalars['UInt53']['output']>;\n  /** The fee charged by the validator for staking services. */\n  commissionRate?: Maybe<Scalars['Int']['output']>;\n  /** Validator's set of credentials such as public keys, network addresses and others. */\n  credentials?: Maybe<ValidatorCredentials>;\n  /** Validator's description. */\n  description?: Maybe<Scalars['String']['output']>;\n  /**\n   * The validator's current exchange object. The exchange rate is used to determine\n   * the amount of SUI tokens that each past SUI staker can withdraw in the future.\n   * @deprecated The exchange object is a wrapped object. Access its dynamic fields through the `exchangeRatesTable` query.\n   */\n  exchangeRates?: Maybe<MoveObject>;\n  /** Number of exchange rates in the table. */\n  exchangeRatesSize?: Maybe<Scalars['UInt53']['output']>;\n  /**\n   * A wrapped object containing the validator's exchange rates. This is a table from epoch\n   * number to `PoolTokenExchangeRate` value. The exchange rate is used to determine the amount\n   * of SUI tokens that each past SUI staker can withdraw in the future.\n   */\n  exchangeRatesTable?: Maybe<Owner>;\n  /** The reference gas price for this epoch. */\n  gasPrice?: Maybe<Scalars['BigInt']['output']>;\n  /** Validator's url containing their custom image. */\n  imageUrl?: Maybe<Scalars['String']['output']>;\n  /** Validator's name. */\n  name?: Maybe<Scalars['String']['output']>;\n  /** The proposed next epoch fee for the validator's staking services. */\n  nextEpochCommissionRate?: Maybe<Scalars['Int']['output']>;\n  /** Validator's set of credentials for the next epoch. */\n  nextEpochCredentials?: Maybe<ValidatorCredentials>;\n  /** The validator's gas price quote for the next epoch. */\n  nextEpochGasPrice?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * The total number of SUI tokens in this pool plus\n   * the pending stake amount for this epoch.\n   */\n  nextEpochStake?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * The validator's current valid `Cap` object. Validators can delegate\n   * the operation ability to another address. The address holding this `Cap` object\n   * can then update the reference gas price and tallying rule on behalf of the validator.\n   */\n  operationCap?: Maybe<MoveObject>;\n  /** Pending pool token withdrawn during the current epoch, emptied at epoch boundaries. */\n  pendingPoolTokenWithdraw?: Maybe<Scalars['BigInt']['output']>;\n  /** Pending stake amount for this epoch. */\n  pendingStake?: Maybe<Scalars['BigInt']['output']>;\n  /** Pending stake withdrawn during the current epoch, emptied at epoch boundaries. */\n  pendingTotalSuiWithdraw?: Maybe<Scalars['BigInt']['output']>;\n  /** Total number of pool tokens issued by the pool. */\n  poolTokenBalance?: Maybe<Scalars['BigInt']['output']>;\n  /** Validator's homepage URL. */\n  projectUrl?: Maybe<Scalars['String']['output']>;\n  /** The addresses of other validators this validator has reported. */\n  reportRecords: AddressConnection;\n  /** The epoch stake rewards will be added here at the end of each epoch. */\n  rewardsPool?: Maybe<Scalars['BigInt']['output']>;\n  /**\n   * The validator's current staking pool object, used to track the amount of stake\n   * and to compound staking rewards.\n   * @deprecated The staking pool is a wrapped object. Access its fields directly on the `Validator` type.\n   */\n  stakingPool?: Maybe<MoveObject>;\n  /** The epoch at which this pool became active. */\n  stakingPoolActivationEpoch?: Maybe<Scalars['UInt53']['output']>;\n  /** The ID of this validator's `0x3::staking_pool::StakingPool`. */\n  stakingPoolId: Scalars['SuiAddress']['output'];\n  /** The total number of SUI tokens in this pool. */\n  stakingPoolSuiBalance?: Maybe<Scalars['BigInt']['output']>;\n  /** The voting power of this validator in basis points (e.g., 100 = 1% voting power). */\n  votingPower?: Maybe<Scalars['Int']['output']>;\n};\n\n\nexport type ValidatorReportRecordsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\nexport type ValidatorConnection = {\n  __typename?: 'ValidatorConnection';\n  /** A list of edges. */\n  edges: Array<ValidatorEdge>;\n  /** A list of nodes. */\n  nodes: Array<Validator>;\n  /** Information to aid in pagination. */\n  pageInfo: PageInfo;\n};\n\n/** The credentials related fields associated with a validator. */\nexport type ValidatorCredentials = {\n  __typename?: 'ValidatorCredentials';\n  netAddress?: Maybe<Scalars['String']['output']>;\n  networkPubKey?: Maybe<Scalars['Base64']['output']>;\n  p2PAddress?: Maybe<Scalars['String']['output']>;\n  primaryAddress?: Maybe<Scalars['String']['output']>;\n  proofOfPossession?: Maybe<Scalars['Base64']['output']>;\n  protocolPubKey?: Maybe<Scalars['Base64']['output']>;\n  workerAddress?: Maybe<Scalars['String']['output']>;\n  workerPubKey?: Maybe<Scalars['Base64']['output']>;\n};\n\n/** An edge in a connection. */\nexport type ValidatorEdge = {\n  __typename?: 'ValidatorEdge';\n  /** A cursor for use in pagination */\n  cursor: Scalars['String']['output'];\n  /** The item at the end of the edge */\n  node: Validator;\n};\n\n/** Representation of `0x3::validator_set::ValidatorSet`. */\nexport type ValidatorSet = {\n  __typename?: 'ValidatorSet';\n  /** The current set of active validators. */\n  activeValidators: ValidatorConnection;\n  /** Object ID of the `Table` storing the inactive staking pools. */\n  inactivePoolsId?: Maybe<Scalars['SuiAddress']['output']>;\n  /** Size of the inactive pools `Table`. */\n  inactivePoolsSize?: Maybe<Scalars['Int']['output']>;\n  /** Object ID of the wrapped object `TableVec` storing the pending active validators. */\n  pendingActiveValidatorsId?: Maybe<Scalars['SuiAddress']['output']>;\n  /** Size of the pending active validators table. */\n  pendingActiveValidatorsSize?: Maybe<Scalars['Int']['output']>;\n  /**\n   * Validators that are pending removal from the active validator set, expressed as indices in\n   * to `activeValidators`.\n   */\n  pendingRemovals?: Maybe<Array<Scalars['Int']['output']>>;\n  /**\n   * Object ID of the `Table` storing the mapping from staking pool ids to the addresses\n   * of the corresponding validators. This is needed because a validator's address\n   * can potentially change but the object ID of its pool will not.\n   */\n  stakingPoolMappingsId?: Maybe<Scalars['SuiAddress']['output']>;\n  /** Size of the stake pool mappings `Table`. */\n  stakingPoolMappingsSize?: Maybe<Scalars['Int']['output']>;\n  /** Total amount of stake for all active validators at the beginning of the epoch. */\n  totalStake?: Maybe<Scalars['BigInt']['output']>;\n  /** Object ID of the `Table` storing the validator candidates. */\n  validatorCandidatesId?: Maybe<Scalars['SuiAddress']['output']>;\n  /** Size of the validator candidates `Table`. */\n  validatorCandidatesSize?: Maybe<Scalars['Int']['output']>;\n};\n\n\n/** Representation of `0x3::validator_set::ValidatorSet`. */\nexport type ValidatorSetActiveValidatorsArgs = {\n  after?: InputMaybe<Scalars['String']['input']>;\n  before?: InputMaybe<Scalars['String']['input']>;\n  first?: InputMaybe<Scalars['Int']['input']>;\n  last?: InputMaybe<Scalars['Int']['input']>;\n};\n\n/**\n * An enum that specifies the intent scope to be used to parse the bytes for signature\n * verification.\n */\nexport enum ZkLoginIntentScope {\n  /** Indicates that the bytes are to be parsed as a personal message. */\n  PersonalMessage = 'PERSONAL_MESSAGE',\n  /** Indicates that the bytes are to be parsed as transaction data bytes. */\n  TransactionData = 'TRANSACTION_DATA'\n}\n\n/** The result of the zkLogin signature verification. */\nexport type ZkLoginVerifyResult = {\n  __typename?: 'ZkLoginVerifyResult';\n  /** The errors field captures any verification error */\n  errors: Array<Scalars['String']['output']>;\n  /** The boolean result of the verification. If true, errors should be empty. */\n  success: Scalars['Boolean']['output'];\n};\n\nexport type GetAllBalancesQueryVariables = Exact<{\n  owner: Scalars['SuiAddress']['input'];\n  limit?: InputMaybe<Scalars['Int']['input']>;\n  cursor?: InputMaybe<Scalars['String']['input']>;\n}>;\n\n\nexport type GetAllBalancesQuery = { __typename?: 'Query', address?: { __typename?: 'Address', balances: { __typename?: 'BalanceConnection', pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null }, nodes: Array<{ __typename?: 'Balance', coinObjectCount?: any | null, totalBalance?: any | null, coinType: { __typename?: 'MoveType', repr: string } }> } } | null };\n\nexport type GetBalanceQueryVariables = Exact<{\n  owner: Scalars['SuiAddress']['input'];\n  type?: InputMaybe<Scalars['String']['input']>;\n}>;\n\n\nexport type GetBalanceQuery = { __typename?: 'Query', address?: { __typename?: 'Address', balance?: { __typename?: 'Balance', coinObjectCount?: any | null, totalBalance?: any | null, coinType: { __typename?: 'MoveType', repr: string } } | null } | null };\n\nexport type GetCoinsQueryVariables = Exact<{\n  owner: Scalars['SuiAddress']['input'];\n  first?: InputMaybe<Scalars['Int']['input']>;\n  cursor?: InputMaybe<Scalars['String']['input']>;\n  type?: InputMaybe<Scalars['String']['input']>;\n}>;\n\n\nexport type GetCoinsQuery = { __typename?: 'Query', address?: { __typename?: 'Address', address: any, coins: { __typename?: 'CoinConnection', pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null }, nodes: Array<{ __typename?: 'Coin', coinBalance?: any | null, address: any, version: any, digest?: string | null, owner?: { __typename: 'AddressOwner', owner?: { __typename?: 'Owner', asObject?: { __typename?: 'Object', address: any } | null, asAddress?: { __typename?: 'Address', address: any } | null } | null } | { __typename: 'ConsensusAddressOwner', startVersion: any, owner?: { __typename?: 'Owner', address: any } | null } | { __typename: 'Immutable' } | { __typename: 'Parent', parent?: { __typename?: 'Owner', address: any } | null } | { __typename: 'Shared', initialSharedVersion: any } | null, contents?: { __typename?: 'MoveValue', bcs: any, type: { __typename?: 'MoveType', repr: string } } | null }> } } | null };\n\nexport type GetDynamicFieldsQueryVariables = Exact<{\n  parentId: Scalars['SuiAddress']['input'];\n  first?: InputMaybe<Scalars['Int']['input']>;\n  cursor?: InputMaybe<Scalars['String']['input']>;\n}>;\n\n\nexport type GetDynamicFieldsQuery = { __typename?: 'Query', owner?: { __typename?: 'Owner', dynamicFields: { __typename?: 'DynamicFieldConnection', pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null }, nodes: Array<{ __typename?: 'DynamicField', name?: { __typename?: 'MoveValue', bcs: any, type: { __typename?: 'MoveType', repr: string } } | null, value?: { __typename: 'MoveObject', contents?: { __typename?: 'MoveValue', type: { __typename?: 'MoveType', repr: string } } | null } | { __typename: 'MoveValue', type: { __typename?: 'MoveType', repr: string } } | null }> } } | null };\n\nexport type GetReferenceGasPriceQueryVariables = Exact<{ [key: string]: never; }>;\n\n\nexport type GetReferenceGasPriceQuery = { __typename?: 'Query', epoch?: { __typename?: 'Epoch', referenceGasPrice?: any | null } | null };\n\nexport type ResolveNameServiceNamesQueryVariables = Exact<{\n  address: Scalars['SuiAddress']['input'];\n  limit?: InputMaybe<Scalars['Int']['input']>;\n  cursor?: InputMaybe<Scalars['String']['input']>;\n}>;\n\n\nexport type ResolveNameServiceNamesQuery = { __typename?: 'Query', address?: { __typename?: 'Address', suinsRegistrations: { __typename?: 'SuinsRegistrationConnection', pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null }, nodes: Array<{ __typename?: 'SuinsRegistration', domain: string }> } } | null };\n\nexport type GetOwnedObjectsQueryVariables = Exact<{\n  owner: Scalars['SuiAddress']['input'];\n  limit?: InputMaybe<Scalars['Int']['input']>;\n  cursor?: InputMaybe<Scalars['String']['input']>;\n  filter?: InputMaybe<ObjectFilter>;\n}>;\n\n\nexport type GetOwnedObjectsQuery = { __typename?: 'Query', address?: { __typename?: 'Address', objects: { __typename?: 'MoveObjectConnection', pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null }, nodes: Array<{ __typename?: 'MoveObject', address: any, digest?: string | null, version: any, contents?: { __typename?: 'MoveValue', bcs: any, type: { __typename?: 'MoveType', repr: string } } | null, owner?: { __typename: 'AddressOwner', owner?: { __typename?: 'Owner', asObject?: { __typename?: 'Object', address: any } | null, asAddress?: { __typename?: 'Address', address: any } | null } | null } | { __typename: 'ConsensusAddressOwner', startVersion: any, owner?: { __typename?: 'Owner', address: any } | null } | { __typename: 'Immutable' } | { __typename: 'Parent', parent?: { __typename?: 'Owner', address: any } | null } | { __typename: 'Shared', initialSharedVersion: any } | null }> } } | null };\n\nexport type MultiGetObjectsQueryVariables = Exact<{\n  objectIds: Array<Scalars['SuiAddress']['input']> | Scalars['SuiAddress']['input'];\n  limit?: InputMaybe<Scalars['Int']['input']>;\n  cursor?: InputMaybe<Scalars['String']['input']>;\n}>;\n\n\nexport type MultiGetObjectsQuery = { __typename?: 'Query', objects: { __typename?: 'ObjectConnection', pageInfo: { __typename?: 'PageInfo', hasNextPage: boolean, endCursor?: string | null }, nodes: Array<{ __typename?: 'Object', address: any, digest?: string | null, version: any, asMoveObject?: { __typename?: 'MoveObject', contents?: { __typename?: 'MoveValue', bcs: any, type: { __typename?: 'MoveType', repr: string } } | null } | null, owner?: { __typename: 'AddressOwner', owner?: { __typename?: 'Owner', asObject?: { __typename?: 'Object', address: any } | null, asAddress?: { __typename?: 'Address', address: any } | null } | null } | { __typename: 'ConsensusAddressOwner', startVersion: any, owner?: { __typename?: 'Owner', address: any } | null } | { __typename: 'Immutable' } | { __typename: 'Parent', parent?: { __typename?: 'Owner', address: any } | null } | { __typename: 'Shared', initialSharedVersion: any } | null }> } };\n\nexport type Object_FieldsFragment = { __typename?: 'Object', address: any, digest?: string | null, version: any, asMoveObject?: { __typename?: 'MoveObject', contents?: { __typename?: 'MoveValue', bcs: any, type: { __typename?: 'MoveType', repr: string } } | null } | null, owner?: { __typename: 'AddressOwner', owner?: { __typename?: 'Owner', asObject?: { __typename?: 'Object', address: any } | null, asAddress?: { __typename?: 'Address', address: any } | null } | null } | { __typename: 'ConsensusAddressOwner', startVersion: any, owner?: { __typename?: 'Owner', address: any } | null } | { __typename: 'Immutable' } | { __typename: 'Parent', parent?: { __typename?: 'Owner', address: any } | null } | { __typename: 'Shared', initialSharedVersion: any } | null };\n\nexport type Move_Object_FieldsFragment = { __typename?: 'MoveObject', address: any, digest?: string | null, version: any, contents?: { __typename?: 'MoveValue', bcs: any, type: { __typename?: 'MoveType', repr: string } } | null, owner?: { __typename: 'AddressOwner', owner?: { __typename?: 'Owner', asObject?: { __typename?: 'Object', address: any } | null, asAddress?: { __typename?: 'Address', address: any } | null } | null } | { __typename: 'ConsensusAddressOwner', startVersion: any, owner?: { __typename?: 'Owner', address: any } | null } | { __typename: 'Immutable' } | { __typename: 'Parent', parent?: { __typename?: 'Owner', address: any } | null } | { __typename: 'Shared', initialSharedVersion: any } | null };\n\ntype Object_Owner_Fields_AddressOwner_Fragment = { __typename: 'AddressOwner', owner?: { __typename?: 'Owner', asObject?: { __typename?: 'Object', address: any } | null, asAddress?: { __typename?: 'Address', address: any } | null } | null };\n\ntype Object_Owner_Fields_ConsensusAddressOwner_Fragment = { __typename: 'ConsensusAddressOwner', startVersion: any, owner?: { __typename?: 'Owner', address: any } | null };\n\ntype Object_Owner_Fields_Immutable_Fragment = { __typename: 'Immutable' };\n\ntype Object_Owner_Fields_Parent_Fragment = { __typename: 'Parent', parent?: { __typename?: 'Owner', address: any } | null };\n\ntype Object_Owner_Fields_Shared_Fragment = { __typename: 'Shared', initialSharedVersion: any };\n\nexport type Object_Owner_FieldsFragment = Object_Owner_Fields_AddressOwner_Fragment | Object_Owner_Fields_ConsensusAddressOwner_Fragment | Object_Owner_Fields_Immutable_Fragment | Object_Owner_Fields_Parent_Fragment | Object_Owner_Fields_Shared_Fragment;\n\nexport type DryRunTransactionBlockQueryVariables = Exact<{\n  txBytes: Scalars['String']['input'];\n}>;\n\n\nexport type DryRunTransactionBlockQuery = { __typename?: 'Query', dryRunTransactionBlock: { __typename?: 'DryRunResult', error?: string | null, transaction?: { __typename?: 'TransactionBlock', digest?: string | null, bcs?: any | null, signatures?: Array<any> | null, effects?: { __typename?: 'TransactionBlockEffects', bcs: any, epoch?: { __typename?: 'Epoch', epochId: any } | null, unchangedSharedObjects: { __typename?: 'UnchangedSharedObjectConnection', nodes: Array<{ __typename: 'SharedObjectCancelled' } | { __typename: 'SharedObjectDelete' } | { __typename: 'SharedObjectRead', object?: { __typename?: 'Object', asMoveObject?: { __typename?: 'MoveObject', address: any, contents?: { __typename?: 'MoveValue', type: { __typename?: 'MoveType', repr: string } } | null } | null } | null }> }, objectChanges: { __typename?: 'ObjectChangeConnection', nodes: Array<{ __typename?: 'ObjectChange', address: any, inputState?: { __typename?: 'Object', version: any, asMoveObject?: { __typename?: 'MoveObject', address: any, contents?: { __typename?: 'MoveValue', type: { __typename?: 'MoveType', repr: string } } | null } | null } | null, outputState?: { __typename?: 'Object', asMoveObject?: { __typename?: 'MoveObject', address: any, contents?: { __typename?: 'MoveValue', type: { __typename?: 'MoveType', repr: string } } | null } | null } | null }> } } | null } | null } };\n\nexport type ExecuteTransactionBlockMutationVariables = Exact<{\n  txBytes: Scalars['String']['input'];\n  signatures: Array<Scalars['String']['input']> | Scalars['String']['input'];\n}>;\n\n\nexport type ExecuteTransactionBlockMutation = { __typename?: 'Mutation', executeTransactionBlock: { __typename?: 'ExecutionResult', errors?: Array<string> | null, effects: { __typename?: 'TransactionBlockEffects', transactionBlock?: { __typename?: 'TransactionBlock', digest?: string | null, bcs?: any | null, signatures?: Array<any> | null, effects?: { __typename?: 'TransactionBlockEffects', bcs: any, epoch?: { __typename?: 'Epoch', epochId: any } | null, unchangedSharedObjects: { __typename?: 'UnchangedSharedObjectConnection', nodes: Array<{ __typename: 'SharedObjectCancelled' } | { __typename: 'SharedObjectDelete' } | { __typename: 'SharedObjectRead', object?: { __typename?: 'Object', asMoveObject?: { __typename?: 'MoveObject', address: any, contents?: { __typename?: 'MoveValue', type: { __typename?: 'MoveType', repr: string } } | null } | null } | null }> }, objectChanges: { __typename?: 'ObjectChangeConnection', nodes: Array<{ __typename?: 'ObjectChange', address: any, inputState?: { __typename?: 'Object', version: any, asMoveObject?: { __typename?: 'MoveObject', address: any, contents?: { __typename?: 'MoveValue', type: { __typename?: 'MoveType', repr: string } } | null } | null } | null, outputState?: { __typename?: 'Object', asMoveObject?: { __typename?: 'MoveObject', address: any, contents?: { __typename?: 'MoveValue', type: { __typename?: 'MoveType', repr: string } } | null } | null } | null }> } } | null } | null } } };\n\nexport type GetTransactionBlockQueryVariables = Exact<{\n  digest: Scalars['String']['input'];\n}>;\n\n\nexport type GetTransactionBlockQuery = { __typename?: 'Query', transactionBlock?: { __typename?: 'TransactionBlock', digest?: string | null, bcs?: any | null, signatures?: Array<any> | null, effects?: { __typename?: 'TransactionBlockEffects', bcs: any, epoch?: { __typename?: 'Epoch', epochId: any } | null, unchangedSharedObjects: { __typename?: 'UnchangedSharedObjectConnection', nodes: Array<{ __typename: 'SharedObjectCancelled' } | { __typename: 'SharedObjectDelete' } | { __typename: 'SharedObjectRead', object?: { __typename?: 'Object', asMoveObject?: { __typename?: 'MoveObject', address: any, contents?: { __typename?: 'MoveValue', type: { __typename?: 'MoveType', repr: string } } | null } | null } | null }> }, objectChanges: { __typename?: 'ObjectChangeConnection', nodes: Array<{ __typename?: 'ObjectChange', address: any, inputState?: { __typename?: 'Object', version: any, asMoveObject?: { __typename?: 'MoveObject', address: any, contents?: { __typename?: 'MoveValue', type: { __typename?: 'MoveType', repr: string } } | null } | null } | null, outputState?: { __typename?: 'Object', asMoveObject?: { __typename?: 'MoveObject', address: any, contents?: { __typename?: 'MoveValue', type: { __typename?: 'MoveType', repr: string } } | null } | null } | null }> } } | null } | null };\n\nexport type Transaction_FieldsFragment = { __typename?: 'TransactionBlock', digest?: string | null, bcs?: any | null, signatures?: Array<any> | null, effects?: { __typename?: 'TransactionBlockEffects', bcs: any, epoch?: { __typename?: 'Epoch', epochId: any } | null, unchangedSharedObjects: { __typename?: 'UnchangedSharedObjectConnection', nodes: Array<{ __typename: 'SharedObjectCancelled' } | { __typename: 'SharedObjectDelete' } | { __typename: 'SharedObjectRead', object?: { __typename?: 'Object', asMoveObject?: { __typename?: 'MoveObject', address: any, contents?: { __typename?: 'MoveValue', type: { __typename?: 'MoveType', repr: string } } | null } | null } | null }> }, objectChanges: { __typename?: 'ObjectChangeConnection', nodes: Array<{ __typename?: 'ObjectChange', address: any, inputState?: { __typename?: 'Object', version: any, asMoveObject?: { __typename?: 'MoveObject', address: any, contents?: { __typename?: 'MoveValue', type: { __typename?: 'MoveType', repr: string } } | null } | null } | null, outputState?: { __typename?: 'Object', asMoveObject?: { __typename?: 'MoveObject', address: any, contents?: { __typename?: 'MoveValue', type: { __typename?: 'MoveType', repr: string } } | null } | null } | null }> } } | null };\n\nexport type VerifyZkLoginSignatureQueryVariables = Exact<{\n  bytes: Scalars['Base64']['input'];\n  signature: Scalars['Base64']['input'];\n  intentScope: ZkLoginIntentScope;\n  author: Scalars['SuiAddress']['input'];\n}>;\n\n\nexport type VerifyZkLoginSignatureQuery = { __typename?: 'Query', verifyZkloginSignature: { __typename?: 'ZkLoginVerifyResult', success: boolean, errors: Array<string> } };\n\nexport class TypedDocumentString<TResult, TVariables>\n  extends String\n  implements DocumentTypeDecoration<TResult, TVariables>\n{\n  __apiType?: DocumentTypeDecoration<TResult, TVariables>['__apiType'];\n  private value: string;\n  public __meta__?: Record<string, any> | undefined;\n\n  constructor(value: string, __meta__?: Record<string, any> | undefined) {\n    super(value);\n    this.value = value;\n    this.__meta__ = __meta__;\n  }\n\n  toString(): string & DocumentTypeDecoration<TResult, TVariables> {\n    return this.value;\n  }\n}\nexport const Object_Owner_FieldsFragmentDoc = new TypedDocumentString(`\n    fragment OBJECT_OWNER_FIELDS on ObjectOwner {\n  __typename\n  ... on AddressOwner {\n    owner {\n      asObject {\n        address\n      }\n      asAddress {\n        address\n      }\n    }\n  }\n  ... on Parent {\n    parent {\n      address\n    }\n  }\n  ... on Shared {\n    initialSharedVersion\n  }\n  ... on ConsensusAddressOwner {\n    startVersion\n    owner {\n      address\n    }\n  }\n}\n    `, {\"fragmentName\":\"OBJECT_OWNER_FIELDS\"}) as unknown as TypedDocumentString<Object_Owner_FieldsFragment, unknown>;\nexport const Object_FieldsFragmentDoc = new TypedDocumentString(`\n    fragment OBJECT_FIELDS on Object {\n  address\n  digest\n  version\n  asMoveObject {\n    contents {\n      bcs\n      type {\n        repr\n      }\n    }\n  }\n  owner {\n    ...OBJECT_OWNER_FIELDS\n  }\n}\n    fragment OBJECT_OWNER_FIELDS on ObjectOwner {\n  __typename\n  ... on AddressOwner {\n    owner {\n      asObject {\n        address\n      }\n      asAddress {\n        address\n      }\n    }\n  }\n  ... on Parent {\n    parent {\n      address\n    }\n  }\n  ... on Shared {\n    initialSharedVersion\n  }\n  ... on ConsensusAddressOwner {\n    startVersion\n    owner {\n      address\n    }\n  }\n}`, {\"fragmentName\":\"OBJECT_FIELDS\"}) as unknown as TypedDocumentString<Object_FieldsFragment, unknown>;\nexport const Move_Object_FieldsFragmentDoc = new TypedDocumentString(`\n    fragment MOVE_OBJECT_FIELDS on MoveObject {\n  address\n  digest\n  version\n  contents {\n    bcs\n    type {\n      repr\n    }\n  }\n  owner {\n    ...OBJECT_OWNER_FIELDS\n  }\n}\n    fragment OBJECT_OWNER_FIELDS on ObjectOwner {\n  __typename\n  ... on AddressOwner {\n    owner {\n      asObject {\n        address\n      }\n      asAddress {\n        address\n      }\n    }\n  }\n  ... on Parent {\n    parent {\n      address\n    }\n  }\n  ... on Shared {\n    initialSharedVersion\n  }\n  ... on ConsensusAddressOwner {\n    startVersion\n    owner {\n      address\n    }\n  }\n}`, {\"fragmentName\":\"MOVE_OBJECT_FIELDS\"}) as unknown as TypedDocumentString<Move_Object_FieldsFragment, unknown>;\nexport const Transaction_FieldsFragmentDoc = new TypedDocumentString(`\n    fragment TRANSACTION_FIELDS on TransactionBlock {\n  digest\n  bcs\n  signatures\n  effects {\n    bcs\n    epoch {\n      epochId\n    }\n    unchangedSharedObjects {\n      nodes {\n        __typename\n        ... on SharedObjectRead {\n          object {\n            asMoveObject {\n              address\n              contents {\n                type {\n                  repr\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    objectChanges {\n      nodes {\n        address\n        inputState {\n          version\n          asMoveObject {\n            address\n            contents {\n              type {\n                repr\n              }\n            }\n          }\n        }\n        outputState {\n          asMoveObject {\n            address\n            contents {\n              type {\n                repr\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n    `, {\"fragmentName\":\"TRANSACTION_FIELDS\"}) as unknown as TypedDocumentString<Transaction_FieldsFragment, unknown>;\nexport const GetAllBalancesDocument = new TypedDocumentString(`\n    query getAllBalances($owner: SuiAddress!, $limit: Int, $cursor: String) {\n  address(address: $owner) {\n    balances(first: $limit, after: $cursor) {\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n      nodes {\n        coinType {\n          repr\n        }\n        coinObjectCount\n        totalBalance\n      }\n    }\n  }\n}\n    `) as unknown as TypedDocumentString<GetAllBalancesQuery, GetAllBalancesQueryVariables>;\nexport const GetBalanceDocument = new TypedDocumentString(`\n    query getBalance($owner: SuiAddress!, $type: String = \"0x2::sui::SUI\") {\n  address(address: $owner) {\n    balance(type: $type) {\n      coinType {\n        repr\n      }\n      coinObjectCount\n      totalBalance\n    }\n  }\n}\n    `) as unknown as TypedDocumentString<GetBalanceQuery, GetBalanceQueryVariables>;\nexport const GetCoinsDocument = new TypedDocumentString(`\n    query getCoins($owner: SuiAddress!, $first: Int, $cursor: String, $type: String = \"0x2::sui::SUI\") {\n  address(address: $owner) {\n    address\n    coins(first: $first, after: $cursor, type: $type) {\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n      nodes {\n        coinBalance\n        owner {\n          ...OBJECT_OWNER_FIELDS\n        }\n        contents {\n          bcs\n          type {\n            repr\n          }\n        }\n        address\n        version\n        digest\n      }\n    }\n  }\n}\n    fragment OBJECT_OWNER_FIELDS on ObjectOwner {\n  __typename\n  ... on AddressOwner {\n    owner {\n      asObject {\n        address\n      }\n      asAddress {\n        address\n      }\n    }\n  }\n  ... on Parent {\n    parent {\n      address\n    }\n  }\n  ... on Shared {\n    initialSharedVersion\n  }\n  ... on ConsensusAddressOwner {\n    startVersion\n    owner {\n      address\n    }\n  }\n}`) as unknown as TypedDocumentString<GetCoinsQuery, GetCoinsQueryVariables>;\nexport const GetDynamicFieldsDocument = new TypedDocumentString(`\n    query getDynamicFields($parentId: SuiAddress!, $first: Int, $cursor: String) {\n  owner(address: $parentId) {\n    dynamicFields(first: $first, after: $cursor) {\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n      nodes {\n        name {\n          bcs\n          type {\n            repr\n          }\n        }\n        value {\n          __typename\n          ... on MoveValue {\n            type {\n              repr\n            }\n          }\n          ... on MoveObject {\n            contents {\n              type {\n                repr\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n    `) as unknown as TypedDocumentString<GetDynamicFieldsQuery, GetDynamicFieldsQueryVariables>;\nexport const GetReferenceGasPriceDocument = new TypedDocumentString(`\n    query getReferenceGasPrice {\n  epoch {\n    referenceGasPrice\n  }\n}\n    `) as unknown as TypedDocumentString<GetReferenceGasPriceQuery, GetReferenceGasPriceQueryVariables>;\nexport const ResolveNameServiceNamesDocument = new TypedDocumentString(`\n    query resolveNameServiceNames($address: SuiAddress!, $limit: Int, $cursor: String) {\n  address(address: $address) {\n    suinsRegistrations(first: $limit, after: $cursor) {\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n      nodes {\n        domain\n      }\n    }\n  }\n}\n    `) as unknown as TypedDocumentString<ResolveNameServiceNamesQuery, ResolveNameServiceNamesQueryVariables>;\nexport const GetOwnedObjectsDocument = new TypedDocumentString(`\n    query getOwnedObjects($owner: SuiAddress!, $limit: Int, $cursor: String, $filter: ObjectFilter) {\n  address(address: $owner) {\n    objects(first: $limit, after: $cursor, filter: $filter) {\n      pageInfo {\n        hasNextPage\n        endCursor\n      }\n      nodes {\n        ...MOVE_OBJECT_FIELDS\n      }\n    }\n  }\n}\n    fragment MOVE_OBJECT_FIELDS on MoveObject {\n  address\n  digest\n  version\n  contents {\n    bcs\n    type {\n      repr\n    }\n  }\n  owner {\n    ...OBJECT_OWNER_FIELDS\n  }\n}\nfragment OBJECT_OWNER_FIELDS on ObjectOwner {\n  __typename\n  ... on AddressOwner {\n    owner {\n      asObject {\n        address\n      }\n      asAddress {\n        address\n      }\n    }\n  }\n  ... on Parent {\n    parent {\n      address\n    }\n  }\n  ... on Shared {\n    initialSharedVersion\n  }\n  ... on ConsensusAddressOwner {\n    startVersion\n    owner {\n      address\n    }\n  }\n}`) as unknown as TypedDocumentString<GetOwnedObjectsQuery, GetOwnedObjectsQueryVariables>;\nexport const MultiGetObjectsDocument = new TypedDocumentString(`\n    query multiGetObjects($objectIds: [SuiAddress!]!, $limit: Int, $cursor: String) {\n  objects(first: $limit, after: $cursor, filter: {objectIds: $objectIds}) {\n    pageInfo {\n      hasNextPage\n      endCursor\n    }\n    nodes {\n      ...OBJECT_FIELDS\n    }\n  }\n}\n    fragment OBJECT_FIELDS on Object {\n  address\n  digest\n  version\n  asMoveObject {\n    contents {\n      bcs\n      type {\n        repr\n      }\n    }\n  }\n  owner {\n    ...OBJECT_OWNER_FIELDS\n  }\n}\nfragment OBJECT_OWNER_FIELDS on ObjectOwner {\n  __typename\n  ... on AddressOwner {\n    owner {\n      asObject {\n        address\n      }\n      asAddress {\n        address\n      }\n    }\n  }\n  ... on Parent {\n    parent {\n      address\n    }\n  }\n  ... on Shared {\n    initialSharedVersion\n  }\n  ... on ConsensusAddressOwner {\n    startVersion\n    owner {\n      address\n    }\n  }\n}`) as unknown as TypedDocumentString<MultiGetObjectsQuery, MultiGetObjectsQueryVariables>;\nexport const DryRunTransactionBlockDocument = new TypedDocumentString(`\n    query dryRunTransactionBlock($txBytes: String!) {\n  dryRunTransactionBlock(txBytes: $txBytes) {\n    error\n    transaction {\n      ...TRANSACTION_FIELDS\n    }\n  }\n}\n    fragment TRANSACTION_FIELDS on TransactionBlock {\n  digest\n  bcs\n  signatures\n  effects {\n    bcs\n    epoch {\n      epochId\n    }\n    unchangedSharedObjects {\n      nodes {\n        __typename\n        ... on SharedObjectRead {\n          object {\n            asMoveObject {\n              address\n              contents {\n                type {\n                  repr\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    objectChanges {\n      nodes {\n        address\n        inputState {\n          version\n          asMoveObject {\n            address\n            contents {\n              type {\n                repr\n              }\n            }\n          }\n        }\n        outputState {\n          asMoveObject {\n            address\n            contents {\n              type {\n                repr\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}`) as unknown as TypedDocumentString<DryRunTransactionBlockQuery, DryRunTransactionBlockQueryVariables>;\nexport const ExecuteTransactionBlockDocument = new TypedDocumentString(`\n    mutation executeTransactionBlock($txBytes: String!, $signatures: [String!]!) {\n  executeTransactionBlock(txBytes: $txBytes, signatures: $signatures) {\n    errors\n    effects {\n      transactionBlock {\n        ...TRANSACTION_FIELDS\n      }\n    }\n  }\n}\n    fragment TRANSACTION_FIELDS on TransactionBlock {\n  digest\n  bcs\n  signatures\n  effects {\n    bcs\n    epoch {\n      epochId\n    }\n    unchangedSharedObjects {\n      nodes {\n        __typename\n        ... on SharedObjectRead {\n          object {\n            asMoveObject {\n              address\n              contents {\n                type {\n                  repr\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    objectChanges {\n      nodes {\n        address\n        inputState {\n          version\n          asMoveObject {\n            address\n            contents {\n              type {\n                repr\n              }\n            }\n          }\n        }\n        outputState {\n          asMoveObject {\n            address\n            contents {\n              type {\n                repr\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}`) as unknown as TypedDocumentString<ExecuteTransactionBlockMutation, ExecuteTransactionBlockMutationVariables>;\nexport const GetTransactionBlockDocument = new TypedDocumentString(`\n    query getTransactionBlock($digest: String!) {\n  transactionBlock(digest: $digest) {\n    ...TRANSACTION_FIELDS\n  }\n}\n    fragment TRANSACTION_FIELDS on TransactionBlock {\n  digest\n  bcs\n  signatures\n  effects {\n    bcs\n    epoch {\n      epochId\n    }\n    unchangedSharedObjects {\n      nodes {\n        __typename\n        ... on SharedObjectRead {\n          object {\n            asMoveObject {\n              address\n              contents {\n                type {\n                  repr\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n    objectChanges {\n      nodes {\n        address\n        inputState {\n          version\n          asMoveObject {\n            address\n            contents {\n              type {\n                repr\n              }\n            }\n          }\n        }\n        outputState {\n          asMoveObject {\n            address\n            contents {\n              type {\n                repr\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}`) as unknown as TypedDocumentString<GetTransactionBlockQuery, GetTransactionBlockQueryVariables>;\nexport const VerifyZkLoginSignatureDocument = new TypedDocumentString(`\n    query verifyZkLoginSignature($bytes: Base64!, $signature: Base64!, $intentScope: ZkLoginIntentScope!, $author: SuiAddress!) {\n  verifyZkloginSignature(\n    bytes: $bytes\n    signature: $signature\n    intentScope: $intentScope\n    author: $author\n  ) {\n    success\n    errors\n  }\n}\n    `) as unknown as TypedDocumentString<VerifyZkLoginSignatureQuery, VerifyZkLoginSignatureQueryVariables>;"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyTO,IAAK,sCAAL,kBAAKA,yCAAL;AAKL,EAAAA,qCAAA,cAAW;AAEX,EAAAA,qCAAA,UAAO;AAPG,SAAAA;AAAA,GAAA;AA8xBL,IAAK,eAAL,kBAAKC,kBAAL;AACL,EAAAA,cAAA,QAAK;AACL,EAAAA,cAAA,SAAM;AAFI,SAAAA;AAAA,GAAA;AAqYL,IAAK,kBAAL,kBAAKC,qBAAL;AAEL,EAAAA,iBAAA,aAAU;AAEV,EAAAA,iBAAA,aAAU;AAJA,SAAAA;AAAA,GAAA;AAWL,IAAK,UAAL,kBAAKC,aAAL;AAEL,EAAAA,SAAA,eAAY;AAEZ,EAAAA,SAAA,WAAQ;AAER,EAAAA,SAAA,mBAAgB;AAEhB,EAAAA,SAAA,kBAAe;AAEf,EAAAA,SAAA,iBAAc;AAEd,EAAAA,SAAA,mBAAgB;AAKhB,EAAAA,SAAA,iBAAc;AAjBJ,SAAAA;AAAA,GAAA;AA8XL,IAAK,cAAL,kBAAKC,iBAAL;AAEL,EAAAA,aAAA,UAAO;AAEP,EAAAA,aAAA,UAAO;AAEP,EAAAA,aAAA,SAAM;AAEN,EAAAA,aAAA,WAAQ;AARE,SAAAA;AAAA,GAAA;AAkgCL,IAAK,iBAAL,kBAAKC,oBAAL;AAKL,EAAAA,gBAAA,YAAS;AAET,EAAAA,gBAAA,aAAU;AAEV,EAAAA,gBAAA,YAAS;AATC,SAAAA;AAAA,GAAA;AAyXL,IAAK,aAAL,kBAAKC,gBAAL;AAEL,EAAAA,YAAA,aAAU;AAKV,EAAAA,YAAA,gBAAa;AAPH,SAAAA;AAAA,GAAA;AAw8BL,IAAK,cAAL,kBAAKC,iBAAL;AAEL,EAAAA,aAAA,YAAS;AAET,EAAAA,aAAA,aAAU;AAEV,EAAAA,aAAA,cAAW;AAND,SAAAA;AAAA,GAAA;AAyvBL,IAAK,4BAAL,kBAAKC,+BAAL;AAEL,EAAAA,2BAAA,oBAAiB;AAKjB,EAAAA,2BAAA,cAAW;AAPD,SAAAA;AAAA,GAAA;AA2RL,IAAK,qBAAL,kBAAKC,wBAAL;AAEL,EAAAA,oBAAA,qBAAkB;AAElB,EAAAA,oBAAA,qBAAkB;AAJR,SAAAA;AAAA,GAAA;AAuIL,MAAM,4BACH,OAEV;AAAA,EAKE,YAAY,OAAe,UAA4C;AACrE,UAAM,KAAK;AACX,SAAK,QAAQ;AACb,SAAK,WAAW;AAAA,EAClB;AAAA,EAEA,WAAiE;AAC/D,WAAO,KAAK;AAAA,EACd;AACF;AACO,MAAM,iCAAiC,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OA4B/D,EAAC,gBAAe,sBAAqB,CAAC;AACtC,MAAM,2BAA2B,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IA2C5D,EAAC,gBAAe,gBAAe,CAAC;AAC7B,MAAM,gCAAgC,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAyCjE,EAAC,gBAAe,qBAAoB,CAAC;AAClC,MAAM,gCAAgC,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OAuD9D,EAAC,gBAAe,qBAAoB,CAAC;AACrC,MAAM,yBAAyB,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAkBzD;AACE,MAAM,qBAAqB,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAYrD;AACE,MAAM,mBAAmB,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAqDtD;AACK,MAAM,2BAA2B,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAkC3D;AACE,MAAM,+BAA+B,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAM/D;AACE,MAAM,kCAAkC,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAclE;AACE,MAAM,0BAA0B,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsD7D;AACK,MAAM,0BAA0B,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsD7D;AACK,MAAM,iCAAiC,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA8DpE;AACK,MAAM,kCAAkC,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgErE;AACK,MAAM,8BAA8B,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2DjE;AACK,MAAM,iCAAiC,IAAI,oBAAoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAYjE;", "names": ["AddressTransactionBlockRelationship", "DomainFormat", "ExecutionStatus", "Feature", "MoveAbility", "MoveVisibility", "ObjectKind", "StakeStatus", "TransactionBlockKindInput", "ZkLoginIntentScope"]}