{"version": 3, "sources": ["../../../src/transactions/ObjectCache.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { bcs } from '../bcs/index.js';\nimport { normalizeSuiAddress } from '../utils/sui-types.js';\nimport type { OpenMoveTypeSignature } from './data/internal.js';\nimport type { TransactionPlugin } from './resolve.js';\n\nexport interface ObjectCacheEntry {\n\tobjectId: string;\n\tversion: string;\n\tdigest: string;\n\towner: string | null;\n\tinitialSharedVersion: string | null;\n}\n\nexport interface MoveFunctionCacheEntry {\n\tpackage: string;\n\tmodule: string;\n\tfunction: string;\n\tparameters: OpenMoveTypeSignature[];\n}\n\nexport interface CacheEntryTypes {\n\tOwnedObject: ObjectCacheEntry;\n\tSharedOrImmutableObject: ObjectCacheEntry;\n\tMoveFunction: MoveFunctionCacheEntry;\n\tCustom: unknown;\n}\nexport abstract class AsyncCache {\n\tprotected abstract get<T extends keyof CacheEntryTypes>(\n\t\ttype: T,\n\t\tkey: string,\n\t): Promise<CacheEntryTypes[T] | null>;\n\tprotected abstract set<T extends keyof CacheEntryTypes>(\n\t\ttype: T,\n\t\tkey: string,\n\t\tvalue: CacheEntryTypes[T],\n\t): Promise<void>;\n\tprotected abstract delete<T extends keyof CacheEntryTypes>(type: T, key: string): Promise<void>;\n\tabstract clear<T extends keyof CacheEntryTypes>(type?: T): Promise<void>;\n\n\tasync getObject(id: string) {\n\t\tconst [owned, shared] = await Promise.all([\n\t\t\tthis.get('OwnedObject', id),\n\t\t\tthis.get('SharedOrImmutableObject', id),\n\t\t]);\n\n\t\treturn owned ?? shared ?? null;\n\t}\n\n\tasync getObjects(ids: string[]) {\n\t\treturn Promise.all([...ids.map((id) => this.getObject(id))]);\n\t}\n\n\tasync addObject(object: ObjectCacheEntry) {\n\t\tif (object.owner) {\n\t\t\tawait this.set('OwnedObject', object.objectId, object);\n\t\t} else {\n\t\t\tawait this.set('SharedOrImmutableObject', object.objectId, object);\n\t\t}\n\n\t\treturn object;\n\t}\n\n\tasync addObjects(objects: ObjectCacheEntry[]) {\n\t\tawait Promise.all(objects.map(async (object) => this.addObject(object)));\n\t}\n\n\tasync deleteObject(id: string) {\n\t\tawait Promise.all([this.delete('OwnedObject', id), this.delete('SharedOrImmutableObject', id)]);\n\t}\n\n\tasync deleteObjects(ids: string[]) {\n\t\tawait Promise.all(ids.map((id) => this.deleteObject(id)));\n\t}\n\n\tasync getMoveFunctionDefinition(ref: { package: string; module: string; function: string }) {\n\t\tconst functionName = `${normalizeSuiAddress(ref.package)}::${ref.module}::${ref.function}`;\n\t\treturn this.get('MoveFunction', functionName);\n\t}\n\n\tasync addMoveFunctionDefinition(functionEntry: MoveFunctionCacheEntry) {\n\t\tconst pkg = normalizeSuiAddress(functionEntry.package);\n\t\tconst functionName = `${pkg}::${functionEntry.module}::${functionEntry.function}`;\n\t\tconst entry = {\n\t\t\t...functionEntry,\n\t\t\tpackage: pkg,\n\t\t};\n\n\t\tawait this.set('MoveFunction', functionName, entry);\n\n\t\treturn entry;\n\t}\n\n\tasync deleteMoveFunctionDefinition(ref: { package: string; module: string; function: string }) {\n\t\tconst functionName = `${normalizeSuiAddress(ref.package)}::${ref.module}::${ref.function}`;\n\t\tawait this.delete('MoveFunction', functionName);\n\t}\n\n\tasync getCustom<T>(key: string) {\n\t\treturn this.get('Custom', key) as Promise<T | null>;\n\t}\n\n\tasync setCustom<T>(key: string, value: T) {\n\t\treturn this.set('Custom', key, value);\n\t}\n\n\tasync deleteCustom(key: string) {\n\t\treturn this.delete('Custom', key);\n\t}\n}\n\nexport class InMemoryCache extends AsyncCache {\n\t#caches = {\n\t\tOwnedObject: new Map<string, ObjectCacheEntry>(),\n\t\tSharedOrImmutableObject: new Map<string, ObjectCacheEntry>(),\n\t\tMoveFunction: new Map<string, MoveFunctionCacheEntry>(),\n\t\tCustom: new Map<string, unknown>(),\n\t};\n\n\tprotected async get<T extends keyof CacheEntryTypes>(type: T, key: string) {\n\t\treturn (this.#caches[type].get(key) as CacheEntryTypes[T]) ?? null;\n\t}\n\n\tprotected async set<T extends keyof CacheEntryTypes>(\n\t\ttype: T,\n\t\tkey: string,\n\t\tvalue: CacheEntryTypes[T],\n\t) {\n\t\t(this.#caches[type] as Map<string, typeof value>).set(key, value as never);\n\t}\n\n\tprotected async delete<T extends keyof CacheEntryTypes>(type: T, key: string) {\n\t\tthis.#caches[type].delete(key);\n\t}\n\n\tasync clear<T extends keyof CacheEntryTypes>(type?: T) {\n\t\tif (type) {\n\t\t\tthis.#caches[type].clear();\n\t\t} else {\n\t\t\tfor (const cache of Object.values(this.#caches)) {\n\t\t\t\tcache.clear();\n\t\t\t}\n\t\t}\n\t}\n}\n\nexport interface ObjectCacheOptions {\n\tcache?: AsyncCache;\n\tonEffects?: (effects: typeof bcs.TransactionEffects.$inferType) => Promise<void>;\n}\n\nexport class ObjectCache {\n\t#cache: AsyncCache;\n\t#onEffects?: (effects: typeof bcs.TransactionEffects.$inferType) => Promise<void>;\n\n\tconstructor({ cache = new InMemoryCache(), onEffects }: ObjectCacheOptions) {\n\t\tthis.#cache = cache;\n\t\tthis.#onEffects = onEffects;\n\t}\n\n\tasPlugin(): TransactionPlugin {\n\t\treturn async (transactionData, _options, next) => {\n\t\t\tconst unresolvedObjects = transactionData.inputs\n\t\t\t\t.filter((input) => input.UnresolvedObject)\n\t\t\t\t.map((input) => input.UnresolvedObject!.objectId);\n\n\t\t\tconst cached = (await this.#cache.getObjects(unresolvedObjects)).filter(\n\t\t\t\t(obj) => obj !== null,\n\t\t\t);\n\n\t\t\tconst byId = new Map(cached.map((obj) => [obj!.objectId, obj]));\n\n\t\t\tfor (const input of transactionData.inputs) {\n\t\t\t\tif (!input.UnresolvedObject) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tconst cached = byId.get(input.UnresolvedObject.objectId);\n\n\t\t\t\tif (!cached) {\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tif (cached.initialSharedVersion && !input.UnresolvedObject.initialSharedVersion) {\n\t\t\t\t\tinput.UnresolvedObject.initialSharedVersion = cached.initialSharedVersion;\n\t\t\t\t} else {\n\t\t\t\t\tif (cached.version && !input.UnresolvedObject.version) {\n\t\t\t\t\t\tinput.UnresolvedObject.version = cached.version;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (cached.digest && !input.UnresolvedObject.digest) {\n\t\t\t\t\t\tinput.UnresolvedObject.digest = cached.digest;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tawait Promise.all(\n\t\t\t\ttransactionData.commands.map(async (commands) => {\n\t\t\t\t\tif (commands.MoveCall) {\n\t\t\t\t\t\tconst def = await this.getMoveFunctionDefinition({\n\t\t\t\t\t\t\tpackage: commands.MoveCall.package,\n\t\t\t\t\t\t\tmodule: commands.MoveCall.module,\n\t\t\t\t\t\t\tfunction: commands.MoveCall.function,\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tif (def) {\n\t\t\t\t\t\t\tcommands.MoveCall._argumentTypes = def.parameters;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}),\n\t\t\t);\n\n\t\t\tawait next();\n\n\t\t\tawait Promise.all(\n\t\t\t\ttransactionData.commands.map(async (commands) => {\n\t\t\t\t\tif (commands.MoveCall?._argumentTypes) {\n\t\t\t\t\t\tawait this.#cache.addMoveFunctionDefinition({\n\t\t\t\t\t\t\tpackage: commands.MoveCall.package,\n\t\t\t\t\t\t\tmodule: commands.MoveCall.module,\n\t\t\t\t\t\t\tfunction: commands.MoveCall.function,\n\t\t\t\t\t\t\tparameters: commands.MoveCall._argumentTypes,\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t}),\n\t\t\t);\n\t\t};\n\t}\n\n\tasync clear() {\n\t\tawait this.#cache.clear();\n\t}\n\n\tasync getMoveFunctionDefinition(ref: { package: string; module: string; function: string }) {\n\t\treturn this.#cache.getMoveFunctionDefinition(ref);\n\t}\n\n\tasync getObjects(ids: string[]) {\n\t\treturn this.#cache.getObjects(ids);\n\t}\n\n\tasync deleteObjects(ids: string[]) {\n\t\treturn this.#cache.deleteObjects(ids);\n\t}\n\n\tasync clearOwnedObjects() {\n\t\tawait this.#cache.clear('OwnedObject');\n\t}\n\n\tasync clearCustom() {\n\t\tawait this.#cache.clear('Custom');\n\t}\n\n\tasync getCustom<T>(key: string) {\n\t\treturn this.#cache.getCustom<T>(key);\n\t}\n\n\tasync setCustom<T>(key: string, value: T) {\n\t\treturn this.#cache.setCustom(key, value);\n\t}\n\n\tasync deleteCustom(key: string) {\n\t\treturn this.#cache.deleteCustom(key);\n\t}\n\n\tasync applyEffects(effects: typeof bcs.TransactionEffects.$inferType) {\n\t\tif (!effects.V2) {\n\t\t\tthrow new Error(`Unsupported transaction effects version ${effects.$kind}`);\n\t\t}\n\n\t\tconst { lamportVersion, changedObjects } = effects.V2;\n\n\t\tconst deletedIds: string[] = [];\n\t\tconst addedObjects: ObjectCacheEntry[] = [];\n\n\t\tchangedObjects.forEach(([id, change]) => {\n\t\t\tif (change.outputState.NotExist) {\n\t\t\t\tdeletedIds.push(id);\n\t\t\t} else if (change.outputState.ObjectWrite) {\n\t\t\t\tconst [digest, owner] = change.outputState.ObjectWrite;\n\n\t\t\t\taddedObjects.push({\n\t\t\t\t\tobjectId: id,\n\t\t\t\t\tdigest,\n\t\t\t\t\tversion: lamportVersion,\n\t\t\t\t\towner: owner.AddressOwner ?? owner.ObjectOwner ?? null,\n\t\t\t\t\tinitialSharedVersion: owner.Shared?.initialSharedVersion ?? null,\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\n\t\tawait Promise.all([\n\t\t\tthis.#cache.addObjects(addedObjects),\n\t\t\tthis.#cache.deleteObjects(deletedIds),\n\t\t\tthis.#onEffects?.(effects),\n\t\t]);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,uBAAoC;AAJpC;AA6BO,MAAe,WAAW;AAAA,EAahC,MAAM,UAAU,IAAY;AAC3B,UAAM,CAAC,OAAO,MAAM,IAAI,MAAM,QAAQ,IAAI;AAAA,MACzC,KAAK,IAAI,eAAe,EAAE;AAAA,MAC1B,KAAK,IAAI,2BAA2B,EAAE;AAAA,IACvC,CAAC;AAED,WAAO,SAAS,UAAU;AAAA,EAC3B;AAAA,EAEA,MAAM,WAAW,KAAe;AAC/B,WAAO,QAAQ,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,OAAO,KAAK,UAAU,EAAE,CAAC,CAAC,CAAC;AAAA,EAC5D;AAAA,EAEA,MAAM,UAAU,QAA0B;AACzC,QAAI,OAAO,OAAO;AACjB,YAAM,KAAK,IAAI,eAAe,OAAO,UAAU,MAAM;AAAA,IACtD,OAAO;AACN,YAAM,KAAK,IAAI,2BAA2B,OAAO,UAAU,MAAM;AAAA,IAClE;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,WAAW,SAA6B;AAC7C,UAAM,QAAQ,IAAI,QAAQ,IAAI,OAAO,WAAW,KAAK,UAAU,MAAM,CAAC,CAAC;AAAA,EACxE;AAAA,EAEA,MAAM,aAAa,IAAY;AAC9B,UAAM,QAAQ,IAAI,CAAC,KAAK,OAAO,eAAe,EAAE,GAAG,KAAK,OAAO,2BAA2B,EAAE,CAAC,CAAC;AAAA,EAC/F;AAAA,EAEA,MAAM,cAAc,KAAe;AAClC,UAAM,QAAQ,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,aAAa,EAAE,CAAC,CAAC;AAAA,EACzD;AAAA,EAEA,MAAM,0BAA0B,KAA4D;AAC3F,UAAM,eAAe,OAAG,sCAAoB,IAAI,OAAO,CAAC,KAAK,IAAI,MAAM,KAAK,IAAI,QAAQ;AACxF,WAAO,KAAK,IAAI,gBAAgB,YAAY;AAAA,EAC7C;AAAA,EAEA,MAAM,0BAA0B,eAAuC;AACtE,UAAM,UAAM,sCAAoB,cAAc,OAAO;AACrD,UAAM,eAAe,GAAG,GAAG,KAAK,cAAc,MAAM,KAAK,cAAc,QAAQ;AAC/E,UAAM,QAAQ;AAAA,MACb,GAAG;AAAA,MACH,SAAS;AAAA,IACV;AAEA,UAAM,KAAK,IAAI,gBAAgB,cAAc,KAAK;AAElD,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,6BAA6B,KAA4D;AAC9F,UAAM,eAAe,OAAG,sCAAoB,IAAI,OAAO,CAAC,KAAK,IAAI,MAAM,KAAK,IAAI,QAAQ;AACxF,UAAM,KAAK,OAAO,gBAAgB,YAAY;AAAA,EAC/C;AAAA,EAEA,MAAM,UAAa,KAAa;AAC/B,WAAO,KAAK,IAAI,UAAU,GAAG;AAAA,EAC9B;AAAA,EAEA,MAAM,UAAa,KAAa,OAAU;AACzC,WAAO,KAAK,IAAI,UAAU,KAAK,KAAK;AAAA,EACrC;AAAA,EAEA,MAAM,aAAa,KAAa;AAC/B,WAAO,KAAK,OAAO,UAAU,GAAG;AAAA,EACjC;AACD;AAEO,MAAM,sBAAsB,WAAW;AAAA,EAAvC;AAAA;AACN,gCAAU;AAAA,MACT,aAAa,oBAAI,IAA8B;AAAA,MAC/C,yBAAyB,oBAAI,IAA8B;AAAA,MAC3D,cAAc,oBAAI,IAAoC;AAAA,MACtD,QAAQ,oBAAI,IAAqB;AAAA,IAClC;AAAA;AAAA,EAEA,MAAgB,IAAqC,MAAS,KAAa;AAC1E,WAAQ,mBAAK,SAAQ,IAAI,EAAE,IAAI,GAAG,KAA4B;AAAA,EAC/D;AAAA,EAEA,MAAgB,IACf,MACA,KACA,OACC;AACD,IAAC,mBAAK,SAAQ,IAAI,EAAgC,IAAI,KAAK,KAAc;AAAA,EAC1E;AAAA,EAEA,MAAgB,OAAwC,MAAS,KAAa;AAC7E,uBAAK,SAAQ,IAAI,EAAE,OAAO,GAAG;AAAA,EAC9B;AAAA,EAEA,MAAM,MAAuC,MAAU;AACtD,QAAI,MAAM;AACT,yBAAK,SAAQ,IAAI,EAAE,MAAM;AAAA,IAC1B,OAAO;AACN,iBAAW,SAAS,OAAO,OAAO,mBAAK,QAAO,GAAG;AAChD,cAAM,MAAM;AAAA,MACb;AAAA,IACD;AAAA,EACD;AACD;AAhCC;AAuCM,MAAM,YAAY;AAAA,EAIxB,YAAY,EAAE,QAAQ,IAAI,cAAc,GAAG,UAAU,GAAuB;AAH5E;AACA;AAGC,uBAAK,QAAS;AACd,uBAAK,YAAa;AAAA,EACnB;AAAA,EAEA,WAA8B;AAC7B,WAAO,OAAO,iBAAiB,UAAU,SAAS;AACjD,YAAM,oBAAoB,gBAAgB,OACxC,OAAO,CAAC,UAAU,MAAM,gBAAgB,EACxC,IAAI,CAAC,UAAU,MAAM,iBAAkB,QAAQ;AAEjD,YAAM,UAAU,MAAM,mBAAK,QAAO,WAAW,iBAAiB,GAAG;AAAA,QAChE,CAAC,QAAQ,QAAQ;AAAA,MAClB;AAEA,YAAM,OAAO,IAAI,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAK,UAAU,GAAG,CAAC,CAAC;AAE9D,iBAAW,SAAS,gBAAgB,QAAQ;AAC3C,YAAI,CAAC,MAAM,kBAAkB;AAC5B;AAAA,QACD;AAEA,cAAMA,UAAS,KAAK,IAAI,MAAM,iBAAiB,QAAQ;AAEvD,YAAI,CAACA,SAAQ;AACZ;AAAA,QACD;AAEA,YAAIA,QAAO,wBAAwB,CAAC,MAAM,iBAAiB,sBAAsB;AAChF,gBAAM,iBAAiB,uBAAuBA,QAAO;AAAA,QACtD,OAAO;AACN,cAAIA,QAAO,WAAW,CAAC,MAAM,iBAAiB,SAAS;AACtD,kBAAM,iBAAiB,UAAUA,QAAO;AAAA,UACzC;AAEA,cAAIA,QAAO,UAAU,CAAC,MAAM,iBAAiB,QAAQ;AACpD,kBAAM,iBAAiB,SAASA,QAAO;AAAA,UACxC;AAAA,QACD;AAAA,MACD;AAEA,YAAM,QAAQ;AAAA,QACb,gBAAgB,SAAS,IAAI,OAAO,aAAa;AAChD,cAAI,SAAS,UAAU;AACtB,kBAAM,MAAM,MAAM,KAAK,0BAA0B;AAAA,cAChD,SAAS,SAAS,SAAS;AAAA,cAC3B,QAAQ,SAAS,SAAS;AAAA,cAC1B,UAAU,SAAS,SAAS;AAAA,YAC7B,CAAC;AAED,gBAAI,KAAK;AACR,uBAAS,SAAS,iBAAiB,IAAI;AAAA,YACxC;AAAA,UACD;AAAA,QACD,CAAC;AAAA,MACF;AAEA,YAAM,KAAK;AAEX,YAAM,QAAQ;AAAA,QACb,gBAAgB,SAAS,IAAI,OAAO,aAAa;AAChD,cAAI,SAAS,UAAU,gBAAgB;AACtC,kBAAM,mBAAK,QAAO,0BAA0B;AAAA,cAC3C,SAAS,SAAS,SAAS;AAAA,cAC3B,QAAQ,SAAS,SAAS;AAAA,cAC1B,UAAU,SAAS,SAAS;AAAA,cAC5B,YAAY,SAAS,SAAS;AAAA,YAC/B,CAAC;AAAA,UACF;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,QAAQ;AACb,UAAM,mBAAK,QAAO,MAAM;AAAA,EACzB;AAAA,EAEA,MAAM,0BAA0B,KAA4D;AAC3F,WAAO,mBAAK,QAAO,0BAA0B,GAAG;AAAA,EACjD;AAAA,EAEA,MAAM,WAAW,KAAe;AAC/B,WAAO,mBAAK,QAAO,WAAW,GAAG;AAAA,EAClC;AAAA,EAEA,MAAM,cAAc,KAAe;AAClC,WAAO,mBAAK,QAAO,cAAc,GAAG;AAAA,EACrC;AAAA,EAEA,MAAM,oBAAoB;AACzB,UAAM,mBAAK,QAAO,MAAM,aAAa;AAAA,EACtC;AAAA,EAEA,MAAM,cAAc;AACnB,UAAM,mBAAK,QAAO,MAAM,QAAQ;AAAA,EACjC;AAAA,EAEA,MAAM,UAAa,KAAa;AAC/B,WAAO,mBAAK,QAAO,UAAa,GAAG;AAAA,EACpC;AAAA,EAEA,MAAM,UAAa,KAAa,OAAU;AACzC,WAAO,mBAAK,QAAO,UAAU,KAAK,KAAK;AAAA,EACxC;AAAA,EAEA,MAAM,aAAa,KAAa;AAC/B,WAAO,mBAAK,QAAO,aAAa,GAAG;AAAA,EACpC;AAAA,EAEA,MAAM,aAAa,SAAmD;AA3QvE;AA4QE,QAAI,CAAC,QAAQ,IAAI;AAChB,YAAM,IAAI,MAAM,2CAA2C,QAAQ,KAAK,EAAE;AAAA,IAC3E;AAEA,UAAM,EAAE,gBAAgB,eAAe,IAAI,QAAQ;AAEnD,UAAM,aAAuB,CAAC;AAC9B,UAAM,eAAmC,CAAC;AAE1C,mBAAe,QAAQ,CAAC,CAAC,IAAI,MAAM,MAAM;AACxC,UAAI,OAAO,YAAY,UAAU;AAChC,mBAAW,KAAK,EAAE;AAAA,MACnB,WAAW,OAAO,YAAY,aAAa;AAC1C,cAAM,CAAC,QAAQ,KAAK,IAAI,OAAO,YAAY;AAE3C,qBAAa,KAAK;AAAA,UACjB,UAAU;AAAA,UACV;AAAA,UACA,SAAS;AAAA,UACT,OAAO,MAAM,gBAAgB,MAAM,eAAe;AAAA,UAClD,sBAAsB,MAAM,QAAQ,wBAAwB;AAAA,QAC7D,CAAC;AAAA,MACF;AAAA,IACD,CAAC;AAED,UAAM,QAAQ,IAAI;AAAA,MACjB,mBAAK,QAAO,WAAW,YAAY;AAAA,MACnC,mBAAK,QAAO,cAAc,UAAU;AAAA,OACpC,wBAAK,gBAAL,8BAAkB;AAAA,IACnB,CAAC;AAAA,EACF;AACD;AAjJC;AACA;", "names": ["cached"]}