{"version": 3, "sources": ["../../../src/transactions/TransactionData.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { toBase58 } from '@mysten/bcs';\nimport type { InferInput } from 'valibot';\nimport { parse } from 'valibot';\n\nimport { bcs } from '../bcs/index.js';\nimport { normalizeSuiAddress } from '../utils/sui-types.js';\nimport type {\n\tArgument,\n\tCallArg,\n\tCommand,\n\tGasData,\n\tTransactionExpiration,\n} from './data/internal.js';\nimport { TransactionData } from './data/internal.js';\nimport { transactionDataFromV1 } from './data/v1.js';\nimport type { SerializedTransactionDataV1 } from './data/v1.js';\nimport type { SerializedTransactionDataV2 } from './data/v2.js';\nimport { hashTypedData } from './hash.js';\n\nfunction prepareSuiAddress(address: string) {\n\treturn normalizeSuiAddress(address).replace('0x', '');\n}\n\nexport class TransactionDataBuilder implements TransactionData {\n\tstatic fromKindBytes(bytes: Uint8Array) {\n\t\tconst kind = bcs.TransactionKind.parse(bytes);\n\n\t\tconst programmableTx = kind.ProgrammableTransaction;\n\t\tif (!programmableTx) {\n\t\t\tthrow new Error('Unable to deserialize from bytes.');\n\t\t}\n\n\t\treturn TransactionDataBuilder.restore({\n\t\t\tversion: 2,\n\t\t\tsender: null,\n\t\t\texpiration: null,\n\t\t\tgasData: {\n\t\t\t\tbudget: null,\n\t\t\t\towner: null,\n\t\t\t\tpayment: null,\n\t\t\t\tprice: null,\n\t\t\t},\n\t\t\tinputs: programmableTx.inputs,\n\t\t\tcommands: programmableTx.commands,\n\t\t});\n\t}\n\n\tstatic fromBytes(bytes: Uint8Array) {\n\t\tconst rawData = bcs.TransactionData.parse(bytes);\n\t\tconst data = rawData?.V1;\n\t\tconst programmableTx = data.kind.ProgrammableTransaction;\n\n\t\tif (!data || !programmableTx) {\n\t\t\tthrow new Error('Unable to deserialize from bytes.');\n\t\t}\n\n\t\treturn TransactionDataBuilder.restore({\n\t\t\tversion: 2,\n\t\t\tsender: data.sender,\n\t\t\texpiration: data.expiration,\n\t\t\tgasData: data.gasData,\n\t\t\tinputs: programmableTx.inputs,\n\t\t\tcommands: programmableTx.commands,\n\t\t});\n\t}\n\n\tstatic restore(\n\t\tdata:\n\t\t\t| InferInput<typeof SerializedTransactionDataV2>\n\t\t\t| InferInput<typeof SerializedTransactionDataV1>,\n\t) {\n\t\tif (data.version === 2) {\n\t\t\treturn new TransactionDataBuilder(parse(TransactionData, data));\n\t\t} else {\n\t\t\treturn new TransactionDataBuilder(parse(TransactionData, transactionDataFromV1(data)));\n\t\t}\n\t}\n\n\t/**\n\t * Generate transaction digest.\n\t *\n\t * @param bytes BCS serialized transaction data\n\t * @returns transaction digest.\n\t */\n\tstatic getDigestFromBytes(bytes: Uint8Array) {\n\t\tconst hash = hashTypedData('TransactionData', bytes);\n\t\treturn toBase58(hash);\n\t}\n\n\t// @deprecated use gasData instead\n\tget gasConfig() {\n\t\treturn this.gasData;\n\t}\n\t// @deprecated use gasData instead\n\tset gasConfig(value) {\n\t\tthis.gasData = value;\n\t}\n\n\tversion = 2 as const;\n\tsender: string | null;\n\texpiration: TransactionExpiration | null;\n\tgasData: GasData;\n\tinputs: CallArg[];\n\tcommands: Command[];\n\n\tconstructor(clone?: TransactionData) {\n\t\tthis.sender = clone?.sender ?? null;\n\t\tthis.expiration = clone?.expiration ?? null;\n\t\tthis.inputs = clone?.inputs ?? [];\n\t\tthis.commands = clone?.commands ?? [];\n\t\tthis.gasData = clone?.gasData ?? {\n\t\t\tbudget: null,\n\t\t\tprice: null,\n\t\t\towner: null,\n\t\t\tpayment: null,\n\t\t};\n\t}\n\n\tbuild({\n\t\tmaxSizeBytes = Infinity,\n\t\toverrides,\n\t\tonlyTransactionKind,\n\t}: {\n\t\tmaxSizeBytes?: number;\n\t\toverrides?: {\n\t\t\texpiration?: TransactionExpiration;\n\t\t\tsender?: string;\n\t\t\t// @deprecated use gasData instead\n\t\t\tgasConfig?: Partial<GasData>;\n\t\t\tgasData?: Partial<GasData>;\n\t\t};\n\t\tonlyTransactionKind?: boolean;\n\t} = {}) {\n\t\t// TODO validate that inputs and intents are actually resolved\n\t\tconst inputs = this.inputs as (typeof bcs.CallArg.$inferInput)[];\n\t\tconst commands = this.commands as Extract<\n\t\t\tCommand<Exclude<Argument, { IntentResult: unknown } | { NestedIntentResult: unknown }>>,\n\t\t\t{ Upgrade: unknown }\n\t\t>[];\n\n\t\tconst kind = {\n\t\t\tProgrammableTransaction: {\n\t\t\t\tinputs,\n\t\t\t\tcommands,\n\t\t\t},\n\t\t};\n\n\t\tif (onlyTransactionKind) {\n\t\t\treturn bcs.TransactionKind.serialize(kind, { maxSize: maxSizeBytes }).toBytes();\n\t\t}\n\n\t\tconst expiration = overrides?.expiration ?? this.expiration;\n\t\tconst sender = overrides?.sender ?? this.sender;\n\t\tconst gasData = { ...this.gasData, ...overrides?.gasConfig, ...overrides?.gasData };\n\n\t\tif (!sender) {\n\t\t\tthrow new Error('Missing transaction sender');\n\t\t}\n\n\t\tif (!gasData.budget) {\n\t\t\tthrow new Error('Missing gas budget');\n\t\t}\n\n\t\tif (!gasData.payment) {\n\t\t\tthrow new Error('Missing gas payment');\n\t\t}\n\n\t\tif (!gasData.price) {\n\t\t\tthrow new Error('Missing gas price');\n\t\t}\n\n\t\tconst transactionData = {\n\t\t\tsender: prepareSuiAddress(sender),\n\t\t\texpiration: expiration ? expiration : { None: true },\n\t\t\tgasData: {\n\t\t\t\tpayment: gasData.payment,\n\t\t\t\towner: prepareSuiAddress(this.gasData.owner ?? sender),\n\t\t\t\tprice: BigInt(gasData.price),\n\t\t\t\tbudget: BigInt(gasData.budget),\n\t\t\t},\n\t\t\tkind: {\n\t\t\t\tProgrammableTransaction: {\n\t\t\t\t\tinputs,\n\t\t\t\t\tcommands,\n\t\t\t\t},\n\t\t\t},\n\t\t};\n\n\t\treturn bcs.TransactionData.serialize(\n\t\t\t{ V1: transactionData },\n\t\t\t{ maxSize: maxSizeBytes },\n\t\t).toBytes();\n\t}\n\n\taddInput<T extends 'object' | 'pure'>(type: T, arg: CallArg) {\n\t\tconst index = this.inputs.length;\n\t\tthis.inputs.push(arg);\n\t\treturn { Input: index, type, $kind: 'Input' as const };\n\t}\n\n\tgetInputUses(index: number, fn: (arg: Argument, command: Command) => void) {\n\t\tthis.mapArguments((arg, command) => {\n\t\t\tif (arg.$kind === 'Input' && arg.Input === index) {\n\t\t\t\tfn(arg, command);\n\t\t\t}\n\n\t\t\treturn arg;\n\t\t});\n\t}\n\n\tmapCommandArguments(\n\t\tindex: number,\n\t\tfn: (arg: Argument, command: Command, commandIndex: number) => Argument,\n\t) {\n\t\tconst command = this.commands[index];\n\n\t\tswitch (command.$kind) {\n\t\t\tcase 'MoveCall':\n\t\t\t\tcommand.MoveCall.arguments = command.MoveCall.arguments.map((arg) =>\n\t\t\t\t\tfn(arg, command, index),\n\t\t\t\t);\n\t\t\t\tbreak;\n\t\t\tcase 'TransferObjects':\n\t\t\t\tcommand.TransferObjects.objects = command.TransferObjects.objects.map((arg) =>\n\t\t\t\t\tfn(arg, command, index),\n\t\t\t\t);\n\t\t\t\tcommand.TransferObjects.address = fn(command.TransferObjects.address, command, index);\n\t\t\t\tbreak;\n\t\t\tcase 'SplitCoins':\n\t\t\t\tcommand.SplitCoins.coin = fn(command.SplitCoins.coin, command, index);\n\t\t\t\tcommand.SplitCoins.amounts = command.SplitCoins.amounts.map((arg) =>\n\t\t\t\t\tfn(arg, command, index),\n\t\t\t\t);\n\t\t\t\tbreak;\n\t\t\tcase 'MergeCoins':\n\t\t\t\tcommand.MergeCoins.destination = fn(command.MergeCoins.destination, command, index);\n\t\t\t\tcommand.MergeCoins.sources = command.MergeCoins.sources.map((arg) =>\n\t\t\t\t\tfn(arg, command, index),\n\t\t\t\t);\n\t\t\t\tbreak;\n\t\t\tcase 'MakeMoveVec':\n\t\t\t\tcommand.MakeMoveVec.elements = command.MakeMoveVec.elements.map((arg) =>\n\t\t\t\t\tfn(arg, command, index),\n\t\t\t\t);\n\t\t\t\tbreak;\n\t\t\tcase 'Upgrade':\n\t\t\t\tcommand.Upgrade.ticket = fn(command.Upgrade.ticket, command, index);\n\t\t\t\tbreak;\n\t\t\tcase '$Intent':\n\t\t\t\tconst inputs = command.$Intent.inputs;\n\t\t\t\tcommand.$Intent.inputs = {};\n\n\t\t\t\tfor (const [key, value] of Object.entries(inputs)) {\n\t\t\t\t\tcommand.$Intent.inputs[key] = Array.isArray(value)\n\t\t\t\t\t\t? value.map((arg) => fn(arg, command, index))\n\t\t\t\t\t\t: fn(value, command, index);\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\t\t\tcase 'Publish':\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tthrow new Error(`Unexpected transaction kind: ${(command as { $kind: unknown }).$kind}`);\n\t\t}\n\t}\n\n\tmapArguments(fn: (arg: Argument, command: Command, commandIndex: number) => Argument) {\n\t\tfor (const commandIndex of this.commands.keys()) {\n\t\t\tthis.mapCommandArguments(commandIndex, fn);\n\t\t}\n\t}\n\n\treplaceCommand(index: number, replacement: Command | Command[], resultIndex = index) {\n\t\tif (!Array.isArray(replacement)) {\n\t\t\tthis.commands[index] = replacement;\n\t\t\treturn;\n\t\t}\n\n\t\tconst sizeDiff = replacement.length - 1;\n\t\tthis.commands.splice(index, 1, ...replacement);\n\n\t\tif (sizeDiff !== 0) {\n\t\t\tthis.mapArguments((arg, _command, commandIndex) => {\n\t\t\t\tif (commandIndex < index + replacement.length) {\n\t\t\t\t\treturn arg;\n\t\t\t\t}\n\n\t\t\t\tswitch (arg.$kind) {\n\t\t\t\t\tcase 'Result':\n\t\t\t\t\t\tif (arg.Result === index) {\n\t\t\t\t\t\t\targ.Result = resultIndex;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (arg.Result > index) {\n\t\t\t\t\t\t\targ.Result += sizeDiff;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\n\t\t\t\t\tcase 'NestedResult':\n\t\t\t\t\t\tif (arg.NestedResult[0] === index) {\n\t\t\t\t\t\t\targ.NestedResult[0] = resultIndex;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (arg.NestedResult[0] > index) {\n\t\t\t\t\t\t\targ.NestedResult[0] += sizeDiff;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\treturn arg;\n\t\t\t});\n\t\t}\n\t}\n\n\tgetDigest() {\n\t\tconst bytes = this.build({ onlyTransactionKind: false });\n\t\treturn TransactionDataBuilder.getDigestFromBytes(bytes);\n\t}\n\n\tsnapshot(): TransactionData {\n\t\treturn parse(TransactionData, this);\n\t}\n\n\tshallowClone() {\n\t\treturn new TransactionDataBuilder({\n\t\t\tversion: this.version,\n\t\t\tsender: this.sender,\n\t\t\texpiration: this.expiration,\n\t\t\tgasData: {\n\t\t\t\t...this.gasData,\n\t\t\t},\n\t\t\tinputs: [...this.inputs],\n\t\t\tcommands: [...this.commands],\n\t\t});\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAAyB;AAEzB,qBAAsB;AAEtB,IAAAA,cAAoB;AACpB,uBAAoC;AAQpC,sBAAgC;AAChC,gBAAsC;AAGtC,kBAA8B;AAE9B,SAAS,kBAAkB,SAAiB;AAC3C,aAAO,sCAAoB,OAAO,EAAE,QAAQ,MAAM,EAAE;AACrD;AAEO,MAAM,uBAAkD;AAAA,EAkF9D,YAAY,OAAyB;AAPrC,mBAAU;AAQT,SAAK,SAAS,OAAO,UAAU;AAC/B,SAAK,aAAa,OAAO,cAAc;AACvC,SAAK,SAAS,OAAO,UAAU,CAAC;AAChC,SAAK,WAAW,OAAO,YAAY,CAAC;AACpC,SAAK,UAAU,OAAO,WAAW;AAAA,MAChC,QAAQ;AAAA,MACR,OAAO;AAAA,MACP,OAAO;AAAA,MACP,SAAS;AAAA,IACV;AAAA,EACD;AAAA,EA5FA,OAAO,cAAc,OAAmB;AACvC,UAAM,OAAO,gBAAI,gBAAgB,MAAM,KAAK;AAE5C,UAAM,iBAAiB,KAAK;AAC5B,QAAI,CAAC,gBAAgB;AACpB,YAAM,IAAI,MAAM,mCAAmC;AAAA,IACpD;AAEA,WAAO,uBAAuB,QAAQ;AAAA,MACrC,SAAS;AAAA,MACT,QAAQ;AAAA,MACR,YAAY;AAAA,MACZ,SAAS;AAAA,QACR,QAAQ;AAAA,QACR,OAAO;AAAA,QACP,SAAS;AAAA,QACT,OAAO;AAAA,MACR;AAAA,MACA,QAAQ,eAAe;AAAA,MACvB,UAAU,eAAe;AAAA,IAC1B,CAAC;AAAA,EACF;AAAA,EAEA,OAAO,UAAU,OAAmB;AACnC,UAAM,UAAU,gBAAI,gBAAgB,MAAM,KAAK;AAC/C,UAAM,OAAO,SAAS;AACtB,UAAM,iBAAiB,KAAK,KAAK;AAEjC,QAAI,CAAC,QAAQ,CAAC,gBAAgB;AAC7B,YAAM,IAAI,MAAM,mCAAmC;AAAA,IACpD;AAEA,WAAO,uBAAuB,QAAQ;AAAA,MACrC,SAAS;AAAA,MACT,QAAQ,KAAK;AAAA,MACb,YAAY,KAAK;AAAA,MACjB,SAAS,KAAK;AAAA,MACd,QAAQ,eAAe;AAAA,MACvB,UAAU,eAAe;AAAA,IAC1B,CAAC;AAAA,EACF;AAAA,EAEA,OAAO,QACN,MAGC;AACD,QAAI,KAAK,YAAY,GAAG;AACvB,aAAO,IAAI,2BAAuB,sBAAM,iCAAiB,IAAI,CAAC;AAAA,IAC/D,OAAO;AACN,aAAO,IAAI,2BAAuB,sBAAM,qCAAiB,iCAAsB,IAAI,CAAC,CAAC;AAAA,IACtF;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,mBAAmB,OAAmB;AAC5C,UAAM,WAAO,2BAAc,mBAAmB,KAAK;AACnD,eAAO,qBAAS,IAAI;AAAA,EACrB;AAAA;AAAA,EAGA,IAAI,YAAY;AACf,WAAO,KAAK;AAAA,EACb;AAAA;AAAA,EAEA,IAAI,UAAU,OAAO;AACpB,SAAK,UAAU;AAAA,EAChB;AAAA,EAsBA,MAAM;AAAA,IACL,eAAe;AAAA,IACf;AAAA,IACA;AAAA,EACD,IAUI,CAAC,GAAG;AAEP,UAAM,SAAS,KAAK;AACpB,UAAM,WAAW,KAAK;AAKtB,UAAM,OAAO;AAAA,MACZ,yBAAyB;AAAA,QACxB;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAEA,QAAI,qBAAqB;AACxB,aAAO,gBAAI,gBAAgB,UAAU,MAAM,EAAE,SAAS,aAAa,CAAC,EAAE,QAAQ;AAAA,IAC/E;AAEA,UAAM,aAAa,WAAW,cAAc,KAAK;AACjD,UAAM,SAAS,WAAW,UAAU,KAAK;AACzC,UAAM,UAAU,EAAE,GAAG,KAAK,SAAS,GAAG,WAAW,WAAW,GAAG,WAAW,QAAQ;AAElF,QAAI,CAAC,QAAQ;AACZ,YAAM,IAAI,MAAM,4BAA4B;AAAA,IAC7C;AAEA,QAAI,CAAC,QAAQ,QAAQ;AACpB,YAAM,IAAI,MAAM,oBAAoB;AAAA,IACrC;AAEA,QAAI,CAAC,QAAQ,SAAS;AACrB,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACtC;AAEA,QAAI,CAAC,QAAQ,OAAO;AACnB,YAAM,IAAI,MAAM,mBAAmB;AAAA,IACpC;AAEA,UAAM,kBAAkB;AAAA,MACvB,QAAQ,kBAAkB,MAAM;AAAA,MAChC,YAAY,aAAa,aAAa,EAAE,MAAM,KAAK;AAAA,MACnD,SAAS;AAAA,QACR,SAAS,QAAQ;AAAA,QACjB,OAAO,kBAAkB,KAAK,QAAQ,SAAS,MAAM;AAAA,QACrD,OAAO,OAAO,QAAQ,KAAK;AAAA,QAC3B,QAAQ,OAAO,QAAQ,MAAM;AAAA,MAC9B;AAAA,MACA,MAAM;AAAA,QACL,yBAAyB;AAAA,UACxB;AAAA,UACA;AAAA,QACD;AAAA,MACD;AAAA,IACD;AAEA,WAAO,gBAAI,gBAAgB;AAAA,MAC1B,EAAE,IAAI,gBAAgB;AAAA,MACtB,EAAE,SAAS,aAAa;AAAA,IACzB,EAAE,QAAQ;AAAA,EACX;AAAA,EAEA,SAAsC,MAAS,KAAc;AAC5D,UAAM,QAAQ,KAAK,OAAO;AAC1B,SAAK,OAAO,KAAK,GAAG;AACpB,WAAO,EAAE,OAAO,OAAO,MAAM,OAAO,QAAiB;AAAA,EACtD;AAAA,EAEA,aAAa,OAAe,IAA+C;AAC1E,SAAK,aAAa,CAAC,KAAK,YAAY;AACnC,UAAI,IAAI,UAAU,WAAW,IAAI,UAAU,OAAO;AACjD,WAAG,KAAK,OAAO;AAAA,MAChB;AAEA,aAAO;AAAA,IACR,CAAC;AAAA,EACF;AAAA,EAEA,oBACC,OACA,IACC;AACD,UAAM,UAAU,KAAK,SAAS,KAAK;AAEnC,YAAQ,QAAQ,OAAO;AAAA,MACtB,KAAK;AACJ,gBAAQ,SAAS,YAAY,QAAQ,SAAS,UAAU;AAAA,UAAI,CAAC,QAC5D,GAAG,KAAK,SAAS,KAAK;AAAA,QACvB;AACA;AAAA,MACD,KAAK;AACJ,gBAAQ,gBAAgB,UAAU,QAAQ,gBAAgB,QAAQ;AAAA,UAAI,CAAC,QACtE,GAAG,KAAK,SAAS,KAAK;AAAA,QACvB;AACA,gBAAQ,gBAAgB,UAAU,GAAG,QAAQ,gBAAgB,SAAS,SAAS,KAAK;AACpF;AAAA,MACD,KAAK;AACJ,gBAAQ,WAAW,OAAO,GAAG,QAAQ,WAAW,MAAM,SAAS,KAAK;AACpE,gBAAQ,WAAW,UAAU,QAAQ,WAAW,QAAQ;AAAA,UAAI,CAAC,QAC5D,GAAG,KAAK,SAAS,KAAK;AAAA,QACvB;AACA;AAAA,MACD,KAAK;AACJ,gBAAQ,WAAW,cAAc,GAAG,QAAQ,WAAW,aAAa,SAAS,KAAK;AAClF,gBAAQ,WAAW,UAAU,QAAQ,WAAW,QAAQ;AAAA,UAAI,CAAC,QAC5D,GAAG,KAAK,SAAS,KAAK;AAAA,QACvB;AACA;AAAA,MACD,KAAK;AACJ,gBAAQ,YAAY,WAAW,QAAQ,YAAY,SAAS;AAAA,UAAI,CAAC,QAChE,GAAG,KAAK,SAAS,KAAK;AAAA,QACvB;AACA;AAAA,MACD,KAAK;AACJ,gBAAQ,QAAQ,SAAS,GAAG,QAAQ,QAAQ,QAAQ,SAAS,KAAK;AAClE;AAAA,MACD,KAAK;AACJ,cAAM,SAAS,QAAQ,QAAQ;AAC/B,gBAAQ,QAAQ,SAAS,CAAC;AAE1B,mBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AAClD,kBAAQ,QAAQ,OAAO,GAAG,IAAI,MAAM,QAAQ,KAAK,IAC9C,MAAM,IAAI,CAAC,QAAQ,GAAG,KAAK,SAAS,KAAK,CAAC,IAC1C,GAAG,OAAO,SAAS,KAAK;AAAA,QAC5B;AAEA;AAAA,MACD,KAAK;AACJ;AAAA,MACD;AACC,cAAM,IAAI,MAAM,gCAAiC,QAA+B,KAAK,EAAE;AAAA,IACzF;AAAA,EACD;AAAA,EAEA,aAAa,IAAyE;AACrF,eAAW,gBAAgB,KAAK,SAAS,KAAK,GAAG;AAChD,WAAK,oBAAoB,cAAc,EAAE;AAAA,IAC1C;AAAA,EACD;AAAA,EAEA,eAAe,OAAe,aAAkC,cAAc,OAAO;AACpF,QAAI,CAAC,MAAM,QAAQ,WAAW,GAAG;AAChC,WAAK,SAAS,KAAK,IAAI;AACvB;AAAA,IACD;AAEA,UAAM,WAAW,YAAY,SAAS;AACtC,SAAK,SAAS,OAAO,OAAO,GAAG,GAAG,WAAW;AAE7C,QAAI,aAAa,GAAG;AACnB,WAAK,aAAa,CAAC,KAAK,UAAU,iBAAiB;AAClD,YAAI,eAAe,QAAQ,YAAY,QAAQ;AAC9C,iBAAO;AAAA,QACR;AAEA,gBAAQ,IAAI,OAAO;AAAA,UAClB,KAAK;AACJ,gBAAI,IAAI,WAAW,OAAO;AACzB,kBAAI,SAAS;AAAA,YACd;AAEA,gBAAI,IAAI,SAAS,OAAO;AACvB,kBAAI,UAAU;AAAA,YACf;AACA;AAAA,UAED,KAAK;AACJ,gBAAI,IAAI,aAAa,CAAC,MAAM,OAAO;AAClC,kBAAI,aAAa,CAAC,IAAI;AAAA,YACvB;AAEA,gBAAI,IAAI,aAAa,CAAC,IAAI,OAAO;AAChC,kBAAI,aAAa,CAAC,KAAK;AAAA,YACxB;AACA;AAAA,QACF;AACA,eAAO;AAAA,MACR,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EAEA,YAAY;AACX,UAAM,QAAQ,KAAK,MAAM,EAAE,qBAAqB,MAAM,CAAC;AACvD,WAAO,uBAAuB,mBAAmB,KAAK;AAAA,EACvD;AAAA,EAEA,WAA4B;AAC3B,eAAO,sBAAM,iCAAiB,IAAI;AAAA,EACnC;AAAA,EAEA,eAAe;AACd,WAAO,IAAI,uBAAuB;AAAA,MACjC,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,MACb,YAAY,KAAK;AAAA,MACjB,SAAS;AAAA,QACR,GAAG,KAAK;AAAA,MACT;AAAA,MACA,QAAQ,CAAC,GAAG,KAAK,MAAM;AAAA,MACvB,UAAU,CAAC,GAAG,KAAK,QAAQ;AAAA,IAC5B,CAAC;AAAA,EACF;AACD;", "names": ["import_bcs"]}