{"version": 3, "sources": ["../../../src/transactions/hash.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { blake2b } from '@noble/hashes/blake2b';\n\n/**\n * Generates a Blake2b hash of typed data as a base64 string.\n *\n * @param typeTag type tag (e.g. TransactionData, SenderSignedData)\n * @param data data to hash\n */\nexport function hashTypedData(typeTag: string, data: Uint8Array): Uint8Array {\n\tconst typeTagBytes = Array.from(`${typeTag}::`).map((e) => e.charCodeAt(0));\n\n\tconst dataWithTag = new Uint8Array(typeTagBytes.length + data.length);\n\tdataWithTag.set(typeTagBytes);\n\tdataWithTag.set(data, typeTagBytes.length);\n\n\treturn blake2b(dataWithTag, { dkLen: 32 });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,qBAAwB;AAQjB,SAAS,cAAc,SAAiB,MAA8B;AAC5E,QAAM,eAAe,MAAM,KAAK,GAAG,OAAO,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AAE1E,QAAM,cAAc,IAAI,WAAW,aAAa,SAAS,KAAK,MAAM;AACpE,cAAY,IAAI,YAAY;AAC5B,cAAY,IAAI,MAAM,aAAa,MAAM;AAEzC,aAAO,wBAAQ,aAAa,EAAE,OAAO,GAAG,CAAC;AAC1C;", "names": []}