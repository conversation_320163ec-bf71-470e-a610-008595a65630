{"version": 3, "sources": ["../../../../src/transactions/executor/serial.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { toBase64 } from '@mysten/bcs';\n\nimport type { bcs } from '../../bcs/index.js';\nimport type { SuiClient, SuiTransactionBlockResponseOptions } from '../../client/index.js';\nimport type { Signer } from '../../cryptography/keypair.js';\nimport type { ObjectCacheOptions } from '../ObjectCache.js';\nimport { isTransaction, Transaction } from '../Transaction.js';\nimport { CachingTransactionExecutor } from './caching.js';\nimport { SerialQueue } from './queue.js';\n\nexport class SerialTransactionExecutor {\n\t#queue = new SerialQueue();\n\t#signer: Signer;\n\t#cache: CachingTransactionExecutor;\n\t#defaultGasBudget: bigint;\n\n\tconstructor({\n\t\tsigner,\n\t\tdefaultGasBudget = 50_000_000n,\n\t\t...options\n\t}: Omit<ObjectCacheOptions, 'address'> & {\n\t\tclient: SuiClient;\n\t\tsigner: Signer;\n\t\t/** The gasBudget to use if the transaction has not defined it's own gasBudget, defaults to `50_000_000n` */\n\t\tdefaultGasBudget?: bigint;\n\t}) {\n\t\tthis.#signer = signer;\n\t\tthis.#defaultGasBudget = defaultGasBudget;\n\t\tthis.#cache = new CachingTransactionExecutor({\n\t\t\tclient: options.client,\n\t\t\tcache: options.cache,\n\t\t\tonEffects: (effects) => this.#cacheGasCoin(effects),\n\t\t});\n\t}\n\n\tasync applyEffects(effects: typeof bcs.TransactionEffects.$inferType) {\n\t\treturn this.#cache.applyEffects(effects);\n\t}\n\n\t#cacheGasCoin = async (effects: typeof bcs.TransactionEffects.$inferType) => {\n\t\tif (!effects.V2) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst gasCoin = getGasCoinFromEffects(effects).ref;\n\t\tif (gasCoin) {\n\t\t\tthis.#cache.cache.setCustom('gasCoin', gasCoin);\n\t\t} else {\n\t\t\tthis.#cache.cache.deleteCustom('gasCoin');\n\t\t}\n\t};\n\n\tasync buildTransaction(transaction: Transaction) {\n\t\treturn this.#queue.runTask(() => this.#buildTransaction(transaction));\n\t}\n\n\t#buildTransaction = async (transaction: Transaction) => {\n\t\tconst gasCoin = await this.#cache.cache.getCustom<{\n\t\t\tobjectId: string;\n\t\t\tversion: string;\n\t\t\tdigest: string;\n\t\t}>('gasCoin');\n\n\t\tconst copy = Transaction.from(transaction);\n\t\tif (gasCoin) {\n\t\t\tcopy.setGasPayment([gasCoin]);\n\t\t}\n\n\t\tcopy.setGasBudgetIfNotSet(this.#defaultGasBudget);\n\t\tcopy.setSenderIfNotSet(this.#signer.toSuiAddress());\n\n\t\treturn this.#cache.buildTransaction({ transaction: copy });\n\t};\n\n\tresetCache() {\n\t\treturn this.#cache.reset();\n\t}\n\n\twaitForLastTransaction() {\n\t\treturn this.#cache.waitForLastTransaction();\n\t}\n\n\texecuteTransaction(\n\t\ttransaction: Transaction | Uint8Array,\n\t\toptions?: SuiTransactionBlockResponseOptions,\n\t\tadditionalSignatures: string[] = [],\n\t) {\n\t\treturn this.#queue.runTask(async () => {\n\t\t\tconst bytes = isTransaction(transaction)\n\t\t\t\t? await this.#buildTransaction(transaction)\n\t\t\t\t: transaction;\n\n\t\t\tconst { signature } = await this.#signer.signTransaction(bytes);\n\t\t\tconst results = await this.#cache\n\t\t\t\t.executeTransaction({\n\t\t\t\t\tsignature: [signature, ...additionalSignatures],\n\t\t\t\t\ttransaction: bytes,\n\t\t\t\t\toptions,\n\t\t\t\t})\n\t\t\t\t.catch(async (error) => {\n\t\t\t\t\tawait this.resetCache();\n\t\t\t\t\tthrow error;\n\t\t\t\t});\n\n\t\t\tconst effectsBytes = Uint8Array.from(results.rawEffects!);\n\t\t\treturn {\n\t\t\t\tdigest: results.digest,\n\t\t\t\teffects: toBase64(effectsBytes),\n\t\t\t\tdata: results,\n\t\t\t};\n\t\t});\n\t}\n}\n\nexport function getGasCoinFromEffects(effects: typeof bcs.TransactionEffects.$inferType) {\n\tif (!effects.V2) {\n\t\tthrow new Error('Unexpected effects version');\n\t}\n\n\tconst gasObjectChange = effects.V2.changedObjects[effects.V2.gasObjectIndex!];\n\n\tif (!gasObjectChange) {\n\t\tthrow new Error('Gas object not found in effects');\n\t}\n\n\tconst [objectId, { outputState }] = gasObjectChange;\n\n\tif (!outputState.ObjectWrite) {\n\t\tthrow new Error('Unexpected gas object state');\n\t}\n\n\tconst [digest, owner] = outputState.ObjectWrite;\n\n\treturn {\n\t\tref: {\n\t\t\tobjectId,\n\t\t\tdigest,\n\t\t\tversion: effects.V2.lamportVersion,\n\t\t},\n\t\towner: owner.AddressOwner || owner.ObjectOwner!,\n\t};\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAAyB;AAMzB,yBAA2C;AAC3C,qBAA2C;AAC3C,mBAA4B;AAX5B;AAaO,MAAM,0BAA0B;AAAA,EAMtC,YAAY;AAAA,IACX;AAAA,IACA,mBAAmB;AAAA,IACnB,GAAG;AAAA,EACJ,GAKG;AAdH,+BAAS,IAAI,yBAAY;AACzB;AACA;AACA;AAyBA,sCAAgB,OAAO,YAAsD;AAC5E,UAAI,CAAC,QAAQ,IAAI;AAChB;AAAA,MACD;AAEA,YAAM,UAAU,sBAAsB,OAAO,EAAE;AAC/C,UAAI,SAAS;AACZ,2BAAK,QAAO,MAAM,UAAU,WAAW,OAAO;AAAA,MAC/C,OAAO;AACN,2BAAK,QAAO,MAAM,aAAa,SAAS;AAAA,MACzC;AAAA,IACD;AAMA,0CAAoB,OAAO,gBAA6B;AACvD,YAAM,UAAU,MAAM,mBAAK,QAAO,MAAM,UAIrC,SAAS;AAEZ,YAAM,OAAO,+BAAY,KAAK,WAAW;AACzC,UAAI,SAAS;AACZ,aAAK,cAAc,CAAC,OAAO,CAAC;AAAA,MAC7B;AAEA,WAAK,qBAAqB,mBAAK,kBAAiB;AAChD,WAAK,kBAAkB,mBAAK,SAAQ,aAAa,CAAC;AAElD,aAAO,mBAAK,QAAO,iBAAiB,EAAE,aAAa,KAAK,CAAC;AAAA,IAC1D;AA9CC,uBAAK,SAAU;AACf,uBAAK,mBAAoB;AACzB,uBAAK,QAAS,IAAI,0CAA2B;AAAA,MAC5C,QAAQ,QAAQ;AAAA,MAChB,OAAO,QAAQ;AAAA,MACf,WAAW,CAAC,YAAY,mBAAK,eAAL,WAAmB;AAAA,IAC5C,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,aAAa,SAAmD;AACrE,WAAO,mBAAK,QAAO,aAAa,OAAO;AAAA,EACxC;AAAA,EAeA,MAAM,iBAAiB,aAA0B;AAChD,WAAO,mBAAK,QAAO,QAAQ,MAAM,mBAAK,mBAAL,WAAuB,YAAY;AAAA,EACrE;AAAA,EAoBA,aAAa;AACZ,WAAO,mBAAK,QAAO,MAAM;AAAA,EAC1B;AAAA,EAEA,yBAAyB;AACxB,WAAO,mBAAK,QAAO,uBAAuB;AAAA,EAC3C;AAAA,EAEA,mBACC,aACA,SACA,uBAAiC,CAAC,GACjC;AACD,WAAO,mBAAK,QAAO,QAAQ,YAAY;AACtC,YAAM,YAAQ,kCAAc,WAAW,IACpC,MAAM,mBAAK,mBAAL,WAAuB,eAC7B;AAEH,YAAM,EAAE,UAAU,IAAI,MAAM,mBAAK,SAAQ,gBAAgB,KAAK;AAC9D,YAAM,UAAU,MAAM,mBAAK,QACzB,mBAAmB;AAAA,QACnB,WAAW,CAAC,WAAW,GAAG,oBAAoB;AAAA,QAC9C,aAAa;AAAA,QACb;AAAA,MACD,CAAC,EACA,MAAM,OAAO,UAAU;AACvB,cAAM,KAAK,WAAW;AACtB,cAAM;AAAA,MACP,CAAC;AAEF,YAAM,eAAe,WAAW,KAAK,QAAQ,UAAW;AACxD,aAAO;AAAA,QACN,QAAQ,QAAQ;AAAA,QAChB,aAAS,qBAAS,YAAY;AAAA,QAC9B,MAAM;AAAA,MACP;AAAA,IACD,CAAC;AAAA,EACF;AACD;AArGC;AACA;AACA;AACA;AAyBA;AAiBA;AA0DM,SAAS,sBAAsB,SAAmD;AACxF,MAAI,CAAC,QAAQ,IAAI;AAChB,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC7C;AAEA,QAAM,kBAAkB,QAAQ,GAAG,eAAe,QAAQ,GAAG,cAAe;AAE5E,MAAI,CAAC,iBAAiB;AACrB,UAAM,IAAI,MAAM,iCAAiC;AAAA,EAClD;AAEA,QAAM,CAAC,UAAU,EAAE,YAAY,CAAC,IAAI;AAEpC,MAAI,CAAC,YAAY,aAAa;AAC7B,UAAM,IAAI,MAAM,6BAA6B;AAAA,EAC9C;AAEA,QAAM,CAAC,QAAQ,KAAK,IAAI,YAAY;AAEpC,SAAO;AAAA,IACN,KAAK;AAAA,MACJ;AAAA,MACA;AAAA,MACA,SAAS,QAAQ,GAAG;AAAA,IACrB;AAAA,IACA,OAAO,MAAM,gBAAgB,MAAM;AAAA,EACpC;AACD;", "names": []}