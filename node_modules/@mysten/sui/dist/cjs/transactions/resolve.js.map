{"version": 3, "sources": ["../../../src/transactions/resolve.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { Argument } from './data/internal.js';\n\nimport type { ClientWithCoreApi } from '../experimental/index.js';\nimport type { TransactionDataBuilder } from './TransactionData.js';\nimport type { BcsType } from '@mysten/bcs';\nimport { Inputs } from './Inputs.js';\nimport { bcs } from '../bcs/index.js';\nimport { suiClientResolveTransactionPlugin } from '../experimental/transports/json-rpc-resolver.js';\nimport type { SuiClient } from '../client/index.js';\n\nexport interface BuildTransactionOptions {\n\tclient?: ClientWithCoreApi;\n\tonlyTransactionKind?: boolean;\n}\n\nexport interface SerializeTransactionOptions extends BuildTransactionOptions {\n\tsupportedIntents?: string[];\n}\n\nexport type TransactionPlugin = (\n\ttransactionData: TransactionDataBuilder,\n\toptions: BuildTransactionOptions,\n\tnext: () => Promise<void>,\n) => Promise<void>;\n\nexport function needsTransactionResolution(\n\tdata: TransactionDataBuilder,\n\toptions: BuildTransactionOptions,\n): boolean {\n\tif (\n\t\tdata.inputs.some((input) => {\n\t\t\treturn input.UnresolvedObject || input.UnresolvedPure;\n\t\t})\n\t) {\n\t\treturn true;\n\t}\n\n\tif (!options.onlyTransactionKind) {\n\t\tif (!data.gasConfig.price || !data.gasConfig.budget || !data.gasConfig.payment) {\n\t\t\treturn true;\n\t\t}\n\t}\n\n\treturn false;\n}\n\nexport async function resolveTransactionPlugin(\n\ttransactionData: TransactionDataBuilder,\n\toptions: BuildTransactionOptions,\n\tnext: () => Promise<void>,\n) {\n\tnormalizeRawArguments(transactionData);\n\tif (!needsTransactionResolution(transactionData, options)) {\n\t\tawait validate(transactionData);\n\t\treturn next();\n\t}\n\n\tconst client = getClient(options);\n\tconst plugin =\n\t\tclient.core?.resolveTransactionPlugin() ??\n\t\tsuiClientResolveTransactionPlugin(client as SuiClient);\n\n\treturn plugin(transactionData, options, async () => {\n\t\tawait validate(transactionData);\n\t\tawait next();\n\t});\n}\n\nfunction validate(transactionData: TransactionDataBuilder) {\n\ttransactionData.inputs.forEach((input, index) => {\n\t\tif (input.$kind !== 'Object' && input.$kind !== 'Pure') {\n\t\t\tthrow new Error(\n\t\t\t\t`Input at index ${index} has not been resolved.  Expected a Pure or Object input, but found ${JSON.stringify(\n\t\t\t\t\tinput,\n\t\t\t\t)}`,\n\t\t\t);\n\t\t}\n\t});\n}\n\nexport function getClient(options: BuildTransactionOptions) {\n\tif (!options.client) {\n\t\tthrow new Error(\n\t\t\t`No sui client passed to Transaction#build, but transaction data was not sufficient to build offline.`,\n\t\t);\n\t}\n\n\treturn options.client;\n}\n\nfunction normalizeRawArguments(transactionData: TransactionDataBuilder) {\n\tfor (const command of transactionData.commands) {\n\t\tswitch (command.$kind) {\n\t\t\tcase 'SplitCoins':\n\t\t\t\tcommand.SplitCoins.amounts.forEach((amount) => {\n\t\t\t\t\tnormalizeRawArgument(amount, bcs.U64, transactionData);\n\t\t\t\t});\n\t\t\t\tbreak;\n\t\t\tcase 'TransferObjects':\n\t\t\t\tnormalizeRawArgument(command.TransferObjects.address, bcs.Address, transactionData);\n\t\t\t\tbreak;\n\t\t}\n\t}\n}\n\nfunction normalizeRawArgument(\n\targ: Argument,\n\tschema: BcsType<any>,\n\ttransactionData: TransactionDataBuilder,\n) {\n\tif (arg.$kind !== 'Input') {\n\t\treturn;\n\t}\n\tconst input = transactionData.inputs[arg.Input];\n\n\tif (input.$kind !== 'UnresolvedPure') {\n\t\treturn;\n\t}\n\n\ttransactionData.inputs[arg.Input] = Inputs.Pure(schema.serialize(input.UnresolvedPure.value));\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA,oBAAuB;AACvB,iBAAoB;AACpB,+BAAkD;AAkB3C,SAAS,2BACf,MACA,SACU;AACV,MACC,KAAK,OAAO,KAAK,CAAC,UAAU;AAC3B,WAAO,MAAM,oBAAoB,MAAM;AAAA,EACxC,CAAC,GACA;AACD,WAAO;AAAA,EACR;AAEA,MAAI,CAAC,QAAQ,qBAAqB;AACjC,QAAI,CAAC,KAAK,UAAU,SAAS,CAAC,KAAK,UAAU,UAAU,CAAC,KAAK,UAAU,SAAS;AAC/E,aAAO;AAAA,IACR;AAAA,EACD;AAEA,SAAO;AACR;AAEA,eAAsB,yBACrB,iBACA,SACA,MACC;AACD,wBAAsB,eAAe;AACrC,MAAI,CAAC,2BAA2B,iBAAiB,OAAO,GAAG;AAC1D,UAAM,SAAS,eAAe;AAC9B,WAAO,KAAK;AAAA,EACb;AAEA,QAAM,SAAS,UAAU,OAAO;AAChC,QAAM,SACL,OAAO,MAAM,yBAAyB,SACtC,4DAAkC,MAAmB;AAEtD,SAAO,OAAO,iBAAiB,SAAS,YAAY;AACnD,UAAM,SAAS,eAAe;AAC9B,UAAM,KAAK;AAAA,EACZ,CAAC;AACF;AAEA,SAAS,SAAS,iBAAyC;AAC1D,kBAAgB,OAAO,QAAQ,CAAC,OAAO,UAAU;AAChD,QAAI,MAAM,UAAU,YAAY,MAAM,UAAU,QAAQ;AACvD,YAAM,IAAI;AAAA,QACT,kBAAkB,KAAK,uEAAuE,KAAK;AAAA,UAClG;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD;AAAA,EACD,CAAC;AACF;AAEO,SAAS,UAAU,SAAkC;AAC3D,MAAI,CAAC,QAAQ,QAAQ;AACpB,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,SAAO,QAAQ;AAChB;AAEA,SAAS,sBAAsB,iBAAyC;AACvE,aAAW,WAAW,gBAAgB,UAAU;AAC/C,YAAQ,QAAQ,OAAO;AAAA,MACtB,KAAK;AACJ,gBAAQ,WAAW,QAAQ,QAAQ,CAAC,WAAW;AAC9C,+BAAqB,QAAQ,eAAI,KAAK,eAAe;AAAA,QACtD,CAAC;AACD;AAAA,MACD,KAAK;AACJ,6BAAqB,QAAQ,gBAAgB,SAAS,eAAI,SAAS,eAAe;AAClF;AAAA,IACF;AAAA,EACD;AACD;AAEA,SAAS,qBACR,KACA,QACA,iBACC;AACD,MAAI,IAAI,UAAU,SAAS;AAC1B;AAAA,EACD;AACA,QAAM,QAAQ,gBAAgB,OAAO,IAAI,KAAK;AAE9C,MAAI,MAAM,UAAU,kBAAkB;AACrC;AAAA,EACD;AAEA,kBAAgB,OAAO,IAAI,KAAK,IAAI,qBAAO,KAAK,OAAO,UAAU,MAAM,eAAe,KAAK,CAAC;AAC7F;", "names": []}