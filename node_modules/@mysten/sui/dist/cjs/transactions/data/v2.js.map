{"version": 3, "sources": ["../../../../src/transactions/data/v2.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { EnumInputShape } from '@mysten/bcs';\nimport type { GenericSchema, InferInput, InferOutput } from 'valibot';\nimport {\n\tarray,\n\tboolean,\n\tinteger,\n\tliteral,\n\tnullable,\n\tnullish,\n\tnumber,\n\tobject,\n\toptional,\n\tpipe,\n\trecord,\n\tstring,\n\ttuple,\n\tunion,\n\tunknown,\n} from 'valibot';\n\nimport { BCSBytes, JsonU64, ObjectID, ObjectRef, SuiAddress } from './internal.js';\nimport type { Simplify } from '@mysten/utils';\n\nfunction enumUnion<T extends Record<string, GenericSchema<any>>>(options: T) {\n\treturn union(\n\t\tObject.entries(options).map(([key, value]) => object({ [key]: value })),\n\t) as GenericSchema<\n\t\tEnumInputShape<\n\t\t\tSimplify<{\n\t\t\t\t[K in keyof T]: InferInput<T[K]>;\n\t\t\t}>\n\t\t>\n\t>;\n}\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L690-L702\nconst Argument = enumUnion({\n\tGasCoin: literal(true),\n\tInput: pipe(number(), integer()),\n\tResult: pipe(number(), integer()),\n\tNestedResult: tuple([pipe(number(), integer()), pipe(number(), integer())]),\n});\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L1387-L1392\nconst GasData = object({\n\tbudget: nullable(JsonU64),\n\tprice: nullable(JsonU64),\n\towner: nullable(SuiAddress),\n\tpayment: nullable(array(ObjectRef)),\n});\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L707-L718\nconst ProgrammableMoveCall = object({\n\tpackage: ObjectID,\n\tmodule: string(),\n\tfunction: string(),\n\t// snake case in rust\n\ttypeArguments: array(string()),\n\targuments: array(Argument),\n});\n\nconst $Intent = object({\n\tname: string(),\n\tinputs: record(string(), union([Argument, array(Argument)])),\n\tdata: record(string(), unknown()),\n});\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L657-L685\nconst Command = enumUnion({\n\tMoveCall: ProgrammableMoveCall,\n\tTransferObjects: object({\n\t\tobjects: array(Argument),\n\t\taddress: Argument,\n\t}),\n\tSplitCoins: object({\n\t\tcoin: Argument,\n\t\tamounts: array(Argument),\n\t}),\n\tMergeCoins: object({\n\t\tdestination: Argument,\n\t\tsources: array(Argument),\n\t}),\n\tPublish: object({\n\t\tmodules: array(BCSBytes),\n\t\tdependencies: array(ObjectID),\n\t}),\n\tMakeMoveVec: object({\n\t\ttype: nullable(string()),\n\t\telements: array(Argument),\n\t}),\n\tUpgrade: object({\n\t\tmodules: array(BCSBytes),\n\t\tdependencies: array(ObjectID),\n\t\tpackage: ObjectID,\n\t\tticket: Argument,\n\t}),\n\t$Intent,\n});\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L102-L114\nconst ObjectArg = enumUnion({\n\tImmOrOwnedObject: ObjectRef,\n\tSharedObject: object({\n\t\tobjectId: ObjectID,\n\t\t// snake case in rust\n\t\tinitialSharedVersion: JsonU64,\n\t\tmutable: boolean(),\n\t}),\n\tReceiving: ObjectRef,\n});\n\n// https://github.com/MystenLabs/sui/blob/df41d5fa8127634ff4285671a01ead00e519f806/crates/sui-types/src/transaction.rs#L75-L80\nconst CallArg = enumUnion({\n\tObject: ObjectArg,\n\tPure: object({\n\t\tbytes: BCSBytes,\n\t}),\n\tUnresolvedPure: object({\n\t\tvalue: unknown(),\n\t}),\n\tUnresolvedObject: object({\n\t\tobjectId: ObjectID,\n\t\tversion: optional(nullable(JsonU64)),\n\t\tdigest: optional(nullable(string())),\n\t\tinitialSharedVersion: optional(nullable(JsonU64)),\n\t}),\n});\n\nconst TransactionExpiration = enumUnion({\n\tNone: literal(true),\n\tEpoch: JsonU64,\n});\n\nexport const SerializedTransactionDataV2 = object({\n\tversion: literal(2),\n\tsender: nullish(SuiAddress),\n\texpiration: nullish(TransactionExpiration),\n\tgasData: GasData,\n\tinputs: array(CallArg),\n\tcommands: array(Command),\n\tdigest: optional(nullable(string())),\n});\n\nexport type SerializedTransactionDataV2 = InferOutput<typeof SerializedTransactionDataV2>;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,qBAgBO;AAEP,sBAAmE;AAGnE,SAAS,UAAwD,SAAY;AAC5E,aAAO;AAAA,IACN,OAAO,QAAQ,OAAO,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,UAAM,uBAAO,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC;AAAA,EACvE;AAOD;AAGA,MAAM,WAAW,UAAU;AAAA,EAC1B,aAAS,wBAAQ,IAAI;AAAA,EACrB,WAAO,yBAAK,uBAAO,OAAG,wBAAQ,CAAC;AAAA,EAC/B,YAAQ,yBAAK,uBAAO,OAAG,wBAAQ,CAAC;AAAA,EAChC,kBAAc,sBAAM,KAAC,yBAAK,uBAAO,OAAG,wBAAQ,CAAC,OAAG,yBAAK,uBAAO,OAAG,wBAAQ,CAAC,CAAC,CAAC;AAC3E,CAAC;AAGD,MAAM,cAAU,uBAAO;AAAA,EACtB,YAAQ,yBAAS,uBAAO;AAAA,EACxB,WAAO,yBAAS,uBAAO;AAAA,EACvB,WAAO,yBAAS,0BAAU;AAAA,EAC1B,aAAS,6BAAS,sBAAM,yBAAS,CAAC;AACnC,CAAC;AAGD,MAAM,2BAAuB,uBAAO;AAAA,EACnC,SAAS;AAAA,EACT,YAAQ,uBAAO;AAAA,EACf,cAAU,uBAAO;AAAA;AAAA,EAEjB,mBAAe,0BAAM,uBAAO,CAAC;AAAA,EAC7B,eAAW,sBAAM,QAAQ;AAC1B,CAAC;AAED,MAAM,cAAU,uBAAO;AAAA,EACtB,UAAM,uBAAO;AAAA,EACb,YAAQ,2BAAO,uBAAO,OAAG,sBAAM,CAAC,cAAU,sBAAM,QAAQ,CAAC,CAAC,CAAC;AAAA,EAC3D,UAAM,2BAAO,uBAAO,OAAG,wBAAQ,CAAC;AACjC,CAAC;AAGD,MAAM,UAAU,UAAU;AAAA,EACzB,UAAU;AAAA,EACV,qBAAiB,uBAAO;AAAA,IACvB,aAAS,sBAAM,QAAQ;AAAA,IACvB,SAAS;AAAA,EACV,CAAC;AAAA,EACD,gBAAY,uBAAO;AAAA,IAClB,MAAM;AAAA,IACN,aAAS,sBAAM,QAAQ;AAAA,EACxB,CAAC;AAAA,EACD,gBAAY,uBAAO;AAAA,IAClB,aAAa;AAAA,IACb,aAAS,sBAAM,QAAQ;AAAA,EACxB,CAAC;AAAA,EACD,aAAS,uBAAO;AAAA,IACf,aAAS,sBAAM,wBAAQ;AAAA,IACvB,kBAAc,sBAAM,wBAAQ;AAAA,EAC7B,CAAC;AAAA,EACD,iBAAa,uBAAO;AAAA,IACnB,UAAM,6BAAS,uBAAO,CAAC;AAAA,IACvB,cAAU,sBAAM,QAAQ;AAAA,EACzB,CAAC;AAAA,EACD,aAAS,uBAAO;AAAA,IACf,aAAS,sBAAM,wBAAQ;AAAA,IACvB,kBAAc,sBAAM,wBAAQ;AAAA,IAC5B,SAAS;AAAA,IACT,QAAQ;AAAA,EACT,CAAC;AAAA,EACD;AACD,CAAC;AAGD,MAAM,YAAY,UAAU;AAAA,EAC3B,kBAAkB;AAAA,EAClB,kBAAc,uBAAO;AAAA,IACpB,UAAU;AAAA;AAAA,IAEV,sBAAsB;AAAA,IACtB,aAAS,wBAAQ;AAAA,EAClB,CAAC;AAAA,EACD,WAAW;AACZ,CAAC;AAGD,MAAM,UAAU,UAAU;AAAA,EACzB,QAAQ;AAAA,EACR,UAAM,uBAAO;AAAA,IACZ,OAAO;AAAA,EACR,CAAC;AAAA,EACD,oBAAgB,uBAAO;AAAA,IACtB,WAAO,wBAAQ;AAAA,EAChB,CAAC;AAAA,EACD,sBAAkB,uBAAO;AAAA,IACxB,UAAU;AAAA,IACV,aAAS,6BAAS,yBAAS,uBAAO,CAAC;AAAA,IACnC,YAAQ,6BAAS,6BAAS,uBAAO,CAAC,CAAC;AAAA,IACnC,0BAAsB,6BAAS,yBAAS,uBAAO,CAAC;AAAA,EACjD,CAAC;AACF,CAAC;AAED,MAAM,wBAAwB,UAAU;AAAA,EACvC,UAAM,wBAAQ,IAAI;AAAA,EAClB,OAAO;AACR,CAAC;AAEM,MAAM,kCAA8B,uBAAO;AAAA,EACjD,aAAS,wBAAQ,CAAC;AAAA,EAClB,YAAQ,wBAAQ,0BAAU;AAAA,EAC1B,gBAAY,wBAAQ,qBAAqB;AAAA,EACzC,SAAS;AAAA,EACT,YAAQ,sBAAM,OAAO;AAAA,EACrB,cAAU,sBAAM,OAAO;AAAA,EACvB,YAAQ,6BAAS,6BAAS,uBAAO,CAAC,CAAC;AACpC,CAAC;", "names": []}