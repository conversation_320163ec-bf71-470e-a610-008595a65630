{"version": 3, "sources": ["../../../../src/transactions/data/v1.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { fromBase64, toBase64 } from '@mysten/bcs';\nimport type { GenericSchema, InferInput, InferOutput } from 'valibot';\nimport {\n\tarray,\n\tbigint,\n\tboolean,\n\tcheck,\n\tinteger,\n\tis,\n\tlazy,\n\tliteral,\n\tnullable,\n\tnullish,\n\tnumber,\n\tobject,\n\toptional,\n\tparse,\n\tpipe,\n\tstring,\n\tunion,\n\tunknown,\n} from 'valibot';\n\nimport { TypeTagSerializer } from '../../bcs/index.js';\nimport type { StructTag as StructTagType, TypeTag as TypeTagType } from '../../bcs/types.js';\nimport { JsonU64, ObjectID, safeEnum, TransactionData } from './internal.js';\nimport type { Argument } from './internal.js';\n\nexport const ObjectRef = object({\n\tdigest: string(),\n\tobjectId: string(),\n\tversion: union([pipe(number(), integer()), string(), bigint()]),\n});\n\nconst ObjectArg = safeEnum({\n\tImmOrOwned: ObjectRef,\n\tShared: object({\n\t\tobjectId: ObjectID,\n\t\tinitialSharedVersion: JsonU64,\n\t\tmutable: boolean(),\n\t}),\n\tReceiving: ObjectRef,\n});\n\nexport const NormalizedCallArg = safeEnum({\n\tObject: ObjectArg,\n\tPure: array(pipe(number(), integer())),\n});\n\nconst TransactionInput = union([\n\tobject({\n\t\tkind: literal('Input'),\n\t\tindex: pipe(number(), integer()),\n\t\tvalue: unknown(),\n\t\ttype: optional(literal('object')),\n\t}),\n\tobject({\n\t\tkind: literal('Input'),\n\t\tindex: pipe(number(), integer()),\n\t\tvalue: unknown(),\n\t\ttype: literal('pure'),\n\t}),\n]);\n\nconst TransactionExpiration = union([\n\tobject({ Epoch: pipe(number(), integer()) }),\n\tobject({ None: nullable(literal(true)) }),\n]);\n\nconst StringEncodedBigint = pipe(\n\tunion([number(), string(), bigint()]),\n\tcheck((val) => {\n\t\tif (!['string', 'number', 'bigint'].includes(typeof val)) return false;\n\n\t\ttry {\n\t\t\tBigInt(val as string);\n\t\t\treturn true;\n\t\t} catch {\n\t\t\treturn false;\n\t\t}\n\t}),\n);\n\nexport const TypeTag: GenericSchema<TypeTagType> = union([\n\tobject({ bool: nullable(literal(true)) }),\n\tobject({ u8: nullable(literal(true)) }),\n\tobject({ u64: nullable(literal(true)) }),\n\tobject({ u128: nullable(literal(true)) }),\n\tobject({ address: nullable(literal(true)) }),\n\tobject({ signer: nullable(literal(true)) }),\n\tobject({ vector: lazy(() => TypeTag) }),\n\tobject({ struct: lazy(() => StructTag) }),\n\tobject({ u16: nullable(literal(true)) }),\n\tobject({ u32: nullable(literal(true)) }),\n\tobject({ u256: nullable(literal(true)) }),\n]);\n\n// https://github.com/MystenLabs/sui/blob/cea8742e810142a8145fd83c4c142d61e561004a/external-crates/move/crates/move-core-types/src/language_storage.rs#L140-L147\nexport const StructTag: GenericSchema<StructTagType> = object({\n\taddress: string(),\n\tmodule: string(),\n\tname: string(),\n\ttypeParams: array(TypeTag),\n});\n\nconst GasConfig = object({\n\tbudget: optional(StringEncodedBigint),\n\tprice: optional(StringEncodedBigint),\n\tpayment: optional(array(ObjectRef)),\n\towner: optional(string()),\n});\n\nconst TransactionArgumentTypes = [\n\tTransactionInput,\n\tobject({ kind: literal('GasCoin') }),\n\tobject({ kind: literal('Result'), index: pipe(number(), integer()) }),\n\tobject({\n\t\tkind: literal('NestedResult'),\n\t\tindex: pipe(number(), integer()),\n\t\tresultIndex: pipe(number(), integer()),\n\t}),\n] as const;\n\n// Generic transaction argument\nexport const TransactionArgument = union([...TransactionArgumentTypes]);\n\nconst MoveCallTransaction = object({\n\tkind: literal('MoveCall'),\n\ttarget: pipe(\n\t\tstring(),\n\t\tcheck((target) => target.split('::').length === 3),\n\t) as GenericSchema<`${string}::${string}::${string}`>,\n\ttypeArguments: array(string()),\n\targuments: array(TransactionArgument),\n});\n\nconst TransferObjectsTransaction = object({\n\tkind: literal('TransferObjects'),\n\tobjects: array(TransactionArgument),\n\taddress: TransactionArgument,\n});\n\nconst SplitCoinsTransaction = object({\n\tkind: literal('SplitCoins'),\n\tcoin: TransactionArgument,\n\tamounts: array(TransactionArgument),\n});\n\nconst MergeCoinsTransaction = object({\n\tkind: literal('MergeCoins'),\n\tdestination: TransactionArgument,\n\tsources: array(TransactionArgument),\n});\n\nconst MakeMoveVecTransaction = object({\n\tkind: literal('MakeMoveVec'),\n\ttype: union([object({ Some: TypeTag }), object({ None: nullable(literal(true)) })]),\n\tobjects: array(TransactionArgument),\n});\n\nconst PublishTransaction = object({\n\tkind: literal('Publish'),\n\tmodules: array(array(pipe(number(), integer()))),\n\tdependencies: array(string()),\n});\n\nconst UpgradeTransaction = object({\n\tkind: literal('Upgrade'),\n\tmodules: array(array(pipe(number(), integer()))),\n\tdependencies: array(string()),\n\tpackageId: string(),\n\tticket: TransactionArgument,\n});\n\nconst TransactionTypes = [\n\tMoveCallTransaction,\n\tTransferObjectsTransaction,\n\tSplitCoinsTransaction,\n\tMergeCoinsTransaction,\n\tPublishTransaction,\n\tUpgradeTransaction,\n\tMakeMoveVecTransaction,\n] as const;\n\nconst TransactionType = union([...TransactionTypes]);\n\nexport const SerializedTransactionDataV1 = object({\n\tversion: literal(1),\n\tsender: optional(string()),\n\texpiration: nullish(TransactionExpiration),\n\tgasConfig: GasConfig,\n\tinputs: array(TransactionInput),\n\ttransactions: array(TransactionType),\n});\n\nexport type SerializedTransactionDataV1 = InferOutput<typeof SerializedTransactionDataV1>;\n\nexport function serializeV1TransactionData(\n\ttransactionData: TransactionData,\n): SerializedTransactionDataV1 {\n\tconst inputs: InferOutput<typeof TransactionInput>[] = transactionData.inputs.map(\n\t\t(input, index) => {\n\t\t\tif (input.Object) {\n\t\t\t\treturn {\n\t\t\t\t\tkind: 'Input',\n\t\t\t\t\tindex,\n\t\t\t\t\tvalue: {\n\t\t\t\t\t\tObject: input.Object.ImmOrOwnedObject\n\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\tImmOrOwned: input.Object.ImmOrOwnedObject,\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t: input.Object.Receiving\n\t\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\t\tReceiving: {\n\t\t\t\t\t\t\t\t\t\t\tdigest: input.Object.Receiving.digest,\n\t\t\t\t\t\t\t\t\t\t\tversion: input.Object.Receiving.version,\n\t\t\t\t\t\t\t\t\t\t\tobjectId: input.Object.Receiving.objectId,\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t: {\n\t\t\t\t\t\t\t\t\t\tShared: {\n\t\t\t\t\t\t\t\t\t\t\tmutable: input.Object.SharedObject.mutable,\n\t\t\t\t\t\t\t\t\t\t\tinitialSharedVersion: input.Object.SharedObject.initialSharedVersion,\n\t\t\t\t\t\t\t\t\t\t\tobjectId: input.Object.SharedObject.objectId,\n\t\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t},\n\t\t\t\t\ttype: 'object',\n\t\t\t\t};\n\t\t\t}\n\t\t\tif (input.Pure) {\n\t\t\t\treturn {\n\t\t\t\t\tkind: 'Input',\n\t\t\t\t\tindex,\n\t\t\t\t\tvalue: {\n\t\t\t\t\t\tPure: Array.from(fromBase64(input.Pure.bytes)),\n\t\t\t\t\t},\n\t\t\t\t\ttype: 'pure',\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (input.UnresolvedPure) {\n\t\t\t\treturn {\n\t\t\t\t\tkind: 'Input',\n\t\t\t\t\ttype: 'pure',\n\t\t\t\t\tindex,\n\t\t\t\t\tvalue: input.UnresolvedPure.value,\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (input.UnresolvedObject) {\n\t\t\t\treturn {\n\t\t\t\t\tkind: 'Input',\n\t\t\t\t\ttype: 'object',\n\t\t\t\t\tindex,\n\t\t\t\t\tvalue: input.UnresolvedObject.objectId,\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tthrow new Error('Invalid input');\n\t\t},\n\t);\n\n\treturn {\n\t\tversion: 1,\n\t\tsender: transactionData.sender ?? undefined,\n\t\texpiration:\n\t\t\ttransactionData.expiration?.$kind === 'Epoch'\n\t\t\t\t? { Epoch: Number(transactionData.expiration.Epoch) }\n\t\t\t\t: transactionData.expiration\n\t\t\t\t\t? { None: true }\n\t\t\t\t\t: null,\n\t\tgasConfig: {\n\t\t\towner: transactionData.gasData.owner ?? undefined,\n\t\t\tbudget: transactionData.gasData.budget ?? undefined,\n\t\t\tprice: transactionData.gasData.price ?? undefined,\n\t\t\tpayment: transactionData.gasData.payment ?? undefined,\n\t\t},\n\t\tinputs,\n\t\ttransactions: transactionData.commands.map((command): InferOutput<typeof TransactionType> => {\n\t\t\tif (command.MakeMoveVec) {\n\t\t\t\treturn {\n\t\t\t\t\tkind: 'MakeMoveVec',\n\t\t\t\t\ttype:\n\t\t\t\t\t\tcommand.MakeMoveVec.type === null\n\t\t\t\t\t\t\t? { None: true }\n\t\t\t\t\t\t\t: { Some: TypeTagSerializer.parseFromStr(command.MakeMoveVec.type) },\n\t\t\t\t\tobjects: command.MakeMoveVec.elements.map((arg) =>\n\t\t\t\t\t\tconvertTransactionArgument(arg, inputs),\n\t\t\t\t\t),\n\t\t\t\t};\n\t\t\t}\n\t\t\tif (command.MergeCoins) {\n\t\t\t\treturn {\n\t\t\t\t\tkind: 'MergeCoins',\n\t\t\t\t\tdestination: convertTransactionArgument(command.MergeCoins.destination, inputs),\n\t\t\t\t\tsources: command.MergeCoins.sources.map((arg) => convertTransactionArgument(arg, inputs)),\n\t\t\t\t};\n\t\t\t}\n\t\t\tif (command.MoveCall) {\n\t\t\t\treturn {\n\t\t\t\t\tkind: 'MoveCall',\n\t\t\t\t\ttarget: `${command.MoveCall.package}::${command.MoveCall.module}::${command.MoveCall.function}`,\n\t\t\t\t\ttypeArguments: command.MoveCall.typeArguments,\n\t\t\t\t\targuments: command.MoveCall.arguments.map((arg) =>\n\t\t\t\t\t\tconvertTransactionArgument(arg, inputs),\n\t\t\t\t\t),\n\t\t\t\t};\n\t\t\t}\n\t\t\tif (command.Publish) {\n\t\t\t\treturn {\n\t\t\t\t\tkind: 'Publish',\n\t\t\t\t\tmodules: command.Publish.modules.map((mod) => Array.from(fromBase64(mod))),\n\t\t\t\t\tdependencies: command.Publish.dependencies,\n\t\t\t\t};\n\t\t\t}\n\t\t\tif (command.SplitCoins) {\n\t\t\t\treturn {\n\t\t\t\t\tkind: 'SplitCoins',\n\t\t\t\t\tcoin: convertTransactionArgument(command.SplitCoins.coin, inputs),\n\t\t\t\t\tamounts: command.SplitCoins.amounts.map((arg) => convertTransactionArgument(arg, inputs)),\n\t\t\t\t};\n\t\t\t}\n\t\t\tif (command.TransferObjects) {\n\t\t\t\treturn {\n\t\t\t\t\tkind: 'TransferObjects',\n\t\t\t\t\tobjects: command.TransferObjects.objects.map((arg) =>\n\t\t\t\t\t\tconvertTransactionArgument(arg, inputs),\n\t\t\t\t\t),\n\t\t\t\t\taddress: convertTransactionArgument(command.TransferObjects.address, inputs),\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (command.Upgrade) {\n\t\t\t\treturn {\n\t\t\t\t\tkind: 'Upgrade',\n\t\t\t\t\tmodules: command.Upgrade.modules.map((mod) => Array.from(fromBase64(mod))),\n\t\t\t\t\tdependencies: command.Upgrade.dependencies,\n\t\t\t\t\tpackageId: command.Upgrade.package,\n\t\t\t\t\tticket: convertTransactionArgument(command.Upgrade.ticket, inputs),\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tthrow new Error(`Unknown transaction ${Object.keys(command)}`);\n\t\t}),\n\t};\n}\n\nfunction convertTransactionArgument(\n\targ: Argument,\n\tinputs: InferOutput<typeof TransactionInput>[],\n): InferOutput<typeof TransactionArgument> {\n\tif (arg.$kind === 'GasCoin') {\n\t\treturn { kind: 'GasCoin' };\n\t}\n\tif (arg.$kind === 'Result') {\n\t\treturn { kind: 'Result', index: arg.Result };\n\t}\n\tif (arg.$kind === 'NestedResult') {\n\t\treturn { kind: 'NestedResult', index: arg.NestedResult[0], resultIndex: arg.NestedResult[1] };\n\t}\n\tif (arg.$kind === 'Input') {\n\t\treturn inputs[arg.Input];\n\t}\n\n\tthrow new Error(`Invalid argument ${Object.keys(arg)}`);\n}\n\nexport function transactionDataFromV1(data: SerializedTransactionDataV1): TransactionData {\n\treturn parse(TransactionData, {\n\t\tversion: 2,\n\t\tsender: data.sender ?? null,\n\t\texpiration: data.expiration\n\t\t\t? 'Epoch' in data.expiration\n\t\t\t\t? { Epoch: data.expiration.Epoch }\n\t\t\t\t: { None: true }\n\t\t\t: null,\n\t\tgasData: {\n\t\t\towner: data.gasConfig.owner ?? null,\n\t\t\tbudget: data.gasConfig.budget?.toString() ?? null,\n\t\t\tprice: data.gasConfig.price?.toString() ?? null,\n\t\t\tpayment:\n\t\t\t\tdata.gasConfig.payment?.map((ref) => ({\n\t\t\t\t\tdigest: ref.digest,\n\t\t\t\t\tobjectId: ref.objectId,\n\t\t\t\t\tversion: ref.version.toString(),\n\t\t\t\t})) ?? null,\n\t\t},\n\t\tinputs: data.inputs.map((input) => {\n\t\t\tif (input.kind === 'Input') {\n\t\t\t\tif (is(NormalizedCallArg, input.value)) {\n\t\t\t\t\tconst value = parse(NormalizedCallArg, input.value);\n\n\t\t\t\t\tif (value.Object) {\n\t\t\t\t\t\tif (value.Object.ImmOrOwned) {\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\tObject: {\n\t\t\t\t\t\t\t\t\tImmOrOwnedObject: {\n\t\t\t\t\t\t\t\t\t\tobjectId: value.Object.ImmOrOwned.objectId,\n\t\t\t\t\t\t\t\t\t\tversion: String(value.Object.ImmOrOwned.version),\n\t\t\t\t\t\t\t\t\t\tdigest: value.Object.ImmOrOwned.digest,\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (value.Object.Shared) {\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\tObject: {\n\t\t\t\t\t\t\t\t\tSharedObject: {\n\t\t\t\t\t\t\t\t\t\tmutable: value.Object.Shared.mutable ?? null,\n\t\t\t\t\t\t\t\t\t\tinitialSharedVersion: value.Object.Shared.initialSharedVersion,\n\t\t\t\t\t\t\t\t\t\tobjectId: value.Object.Shared.objectId,\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (value.Object.Receiving) {\n\t\t\t\t\t\t\treturn {\n\t\t\t\t\t\t\t\tObject: {\n\t\t\t\t\t\t\t\t\tReceiving: {\n\t\t\t\t\t\t\t\t\t\tdigest: value.Object.Receiving.digest,\n\t\t\t\t\t\t\t\t\t\tversion: String(value.Object.Receiving.version),\n\t\t\t\t\t\t\t\t\t\tobjectId: value.Object.Receiving.objectId,\n\t\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t\t},\n\t\t\t\t\t\t\t};\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tthrow new Error('Invalid object input');\n\t\t\t\t\t}\n\n\t\t\t\t\treturn {\n\t\t\t\t\t\tPure: {\n\t\t\t\t\t\t\tbytes: toBase64(new Uint8Array(value.Pure)),\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\tif (input.type === 'object') {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tUnresolvedObject: {\n\t\t\t\t\t\t\tobjectId: input.value as string,\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\t\t\t\t}\n\n\t\t\t\treturn {\n\t\t\t\t\tUnresolvedPure: {\n\t\t\t\t\t\tvalue: input.value,\n\t\t\t\t\t},\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tthrow new Error('Invalid input');\n\t\t}),\n\t\tcommands: data.transactions.map((transaction) => {\n\t\t\tswitch (transaction.kind) {\n\t\t\t\tcase 'MakeMoveVec':\n\t\t\t\t\treturn {\n\t\t\t\t\t\tMakeMoveVec: {\n\t\t\t\t\t\t\ttype:\n\t\t\t\t\t\t\t\t'Some' in transaction.type\n\t\t\t\t\t\t\t\t\t? TypeTagSerializer.tagToString(transaction.type.Some)\n\t\t\t\t\t\t\t\t\t: null,\n\t\t\t\t\t\t\telements: transaction.objects.map((arg) => parseV1TransactionArgument(arg)),\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\t\t\t\tcase 'MergeCoins': {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tMergeCoins: {\n\t\t\t\t\t\t\tdestination: parseV1TransactionArgument(transaction.destination),\n\t\t\t\t\t\t\tsources: transaction.sources.map((arg) => parseV1TransactionArgument(arg)),\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tcase 'MoveCall': {\n\t\t\t\t\tconst [pkg, mod, fn] = transaction.target.split('::');\n\t\t\t\t\treturn {\n\t\t\t\t\t\tMoveCall: {\n\t\t\t\t\t\t\tpackage: pkg,\n\t\t\t\t\t\t\tmodule: mod,\n\t\t\t\t\t\t\tfunction: fn,\n\t\t\t\t\t\t\ttypeArguments: transaction.typeArguments,\n\t\t\t\t\t\t\targuments: transaction.arguments.map((arg) => parseV1TransactionArgument(arg)),\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tcase 'Publish': {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tPublish: {\n\t\t\t\t\t\t\tmodules: transaction.modules.map((mod) => toBase64(Uint8Array.from(mod))),\n\t\t\t\t\t\t\tdependencies: transaction.dependencies,\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tcase 'SplitCoins': {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tSplitCoins: {\n\t\t\t\t\t\t\tcoin: parseV1TransactionArgument(transaction.coin),\n\t\t\t\t\t\t\tamounts: transaction.amounts.map((arg) => parseV1TransactionArgument(arg)),\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tcase 'TransferObjects': {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tTransferObjects: {\n\t\t\t\t\t\t\tobjects: transaction.objects.map((arg) => parseV1TransactionArgument(arg)),\n\t\t\t\t\t\t\taddress: parseV1TransactionArgument(transaction.address),\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tcase 'Upgrade': {\n\t\t\t\t\treturn {\n\t\t\t\t\t\tUpgrade: {\n\t\t\t\t\t\t\tmodules: transaction.modules.map((mod) => toBase64(Uint8Array.from(mod))),\n\t\t\t\t\t\t\tdependencies: transaction.dependencies,\n\t\t\t\t\t\t\tpackage: transaction.packageId,\n\t\t\t\t\t\t\tticket: parseV1TransactionArgument(transaction.ticket),\n\t\t\t\t\t\t},\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthrow new Error(`Unknown transaction ${Object.keys(transaction)}`);\n\t\t}),\n\t} satisfies InferInput<typeof TransactionData>);\n}\n\nfunction parseV1TransactionArgument(\n\targ: InferOutput<typeof TransactionArgument>,\n): InferInput<typeof Argument> {\n\tswitch (arg.kind) {\n\t\tcase 'GasCoin': {\n\t\t\treturn { GasCoin: true };\n\t\t}\n\t\tcase 'Result':\n\t\t\treturn { Result: arg.index };\n\t\tcase 'NestedResult': {\n\t\t\treturn { NestedResult: [arg.index, arg.resultIndex] };\n\t\t}\n\t\tcase 'Input': {\n\t\t\treturn { Input: arg.index };\n\t\t}\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,iBAAqC;AAErC,qBAmBO;AAEP,IAAAA,cAAkC;AAElC,sBAA6D;AAGtD,MAAM,gBAAY,uBAAO;AAAA,EAC/B,YAAQ,uBAAO;AAAA,EACf,cAAU,uBAAO;AAAA,EACjB,aAAS,sBAAM,KAAC,yBAAK,uBAAO,OAAG,wBAAQ,CAAC,OAAG,uBAAO,OAAG,uBAAO,CAAC,CAAC;AAC/D,CAAC;AAED,MAAM,gBAAY,0BAAS;AAAA,EAC1B,YAAY;AAAA,EACZ,YAAQ,uBAAO;AAAA,IACd,UAAU;AAAA,IACV,sBAAsB;AAAA,IACtB,aAAS,wBAAQ;AAAA,EAClB,CAAC;AAAA,EACD,WAAW;AACZ,CAAC;AAEM,MAAM,wBAAoB,0BAAS;AAAA,EACzC,QAAQ;AAAA,EACR,UAAM,0BAAM,yBAAK,uBAAO,OAAG,wBAAQ,CAAC,CAAC;AACtC,CAAC;AAED,MAAM,uBAAmB,sBAAM;AAAA,MAC9B,uBAAO;AAAA,IACN,UAAM,wBAAQ,OAAO;AAAA,IACrB,WAAO,yBAAK,uBAAO,OAAG,wBAAQ,CAAC;AAAA,IAC/B,WAAO,wBAAQ;AAAA,IACf,UAAM,6BAAS,wBAAQ,QAAQ,CAAC;AAAA,EACjC,CAAC;AAAA,MACD,uBAAO;AAAA,IACN,UAAM,wBAAQ,OAAO;AAAA,IACrB,WAAO,yBAAK,uBAAO,OAAG,wBAAQ,CAAC;AAAA,IAC/B,WAAO,wBAAQ;AAAA,IACf,UAAM,wBAAQ,MAAM;AAAA,EACrB,CAAC;AACF,CAAC;AAED,MAAM,4BAAwB,sBAAM;AAAA,MACnC,uBAAO,EAAE,WAAO,yBAAK,uBAAO,OAAG,wBAAQ,CAAC,EAAE,CAAC;AAAA,MAC3C,uBAAO,EAAE,UAAM,6BAAS,wBAAQ,IAAI,CAAC,EAAE,CAAC;AACzC,CAAC;AAED,MAAM,0BAAsB;AAAA,MAC3B,sBAAM,KAAC,uBAAO,OAAG,uBAAO,OAAG,uBAAO,CAAC,CAAC;AAAA,MACpC,sBAAM,CAAC,QAAQ;AACd,QAAI,CAAC,CAAC,UAAU,UAAU,QAAQ,EAAE,SAAS,OAAO,GAAG,EAAG,QAAO;AAEjE,QAAI;AACH,aAAO,GAAa;AACpB,aAAO;AAAA,IACR,QAAQ;AACP,aAAO;AAAA,IACR;AAAA,EACD,CAAC;AACF;AAEO,MAAM,cAAsC,sBAAM;AAAA,MACxD,uBAAO,EAAE,UAAM,6BAAS,wBAAQ,IAAI,CAAC,EAAE,CAAC;AAAA,MACxC,uBAAO,EAAE,QAAI,6BAAS,wBAAQ,IAAI,CAAC,EAAE,CAAC;AAAA,MACtC,uBAAO,EAAE,SAAK,6BAAS,wBAAQ,IAAI,CAAC,EAAE,CAAC;AAAA,MACvC,uBAAO,EAAE,UAAM,6BAAS,wBAAQ,IAAI,CAAC,EAAE,CAAC;AAAA,MACxC,uBAAO,EAAE,aAAS,6BAAS,wBAAQ,IAAI,CAAC,EAAE,CAAC;AAAA,MAC3C,uBAAO,EAAE,YAAQ,6BAAS,wBAAQ,IAAI,CAAC,EAAE,CAAC;AAAA,MAC1C,uBAAO,EAAE,YAAQ,qBAAK,MAAM,OAAO,EAAE,CAAC;AAAA,MACtC,uBAAO,EAAE,YAAQ,qBAAK,MAAM,SAAS,EAAE,CAAC;AAAA,MACxC,uBAAO,EAAE,SAAK,6BAAS,wBAAQ,IAAI,CAAC,EAAE,CAAC;AAAA,MACvC,uBAAO,EAAE,SAAK,6BAAS,wBAAQ,IAAI,CAAC,EAAE,CAAC;AAAA,MACvC,uBAAO,EAAE,UAAM,6BAAS,wBAAQ,IAAI,CAAC,EAAE,CAAC;AACzC,CAAC;AAGM,MAAM,gBAA0C,uBAAO;AAAA,EAC7D,aAAS,uBAAO;AAAA,EAChB,YAAQ,uBAAO;AAAA,EACf,UAAM,uBAAO;AAAA,EACb,gBAAY,sBAAM,OAAO;AAC1B,CAAC;AAED,MAAM,gBAAY,uBAAO;AAAA,EACxB,YAAQ,yBAAS,mBAAmB;AAAA,EACpC,WAAO,yBAAS,mBAAmB;AAAA,EACnC,aAAS,6BAAS,sBAAM,SAAS,CAAC;AAAA,EAClC,WAAO,6BAAS,uBAAO,CAAC;AACzB,CAAC;AAED,MAAM,2BAA2B;AAAA,EAChC;AAAA,MACA,uBAAO,EAAE,UAAM,wBAAQ,SAAS,EAAE,CAAC;AAAA,MACnC,uBAAO,EAAE,UAAM,wBAAQ,QAAQ,GAAG,WAAO,yBAAK,uBAAO,OAAG,wBAAQ,CAAC,EAAE,CAAC;AAAA,MACpE,uBAAO;AAAA,IACN,UAAM,wBAAQ,cAAc;AAAA,IAC5B,WAAO,yBAAK,uBAAO,OAAG,wBAAQ,CAAC;AAAA,IAC/B,iBAAa,yBAAK,uBAAO,OAAG,wBAAQ,CAAC;AAAA,EACtC,CAAC;AACF;AAGO,MAAM,0BAAsB,sBAAM,CAAC,GAAG,wBAAwB,CAAC;AAEtE,MAAM,0BAAsB,uBAAO;AAAA,EAClC,UAAM,wBAAQ,UAAU;AAAA,EACxB,YAAQ;AAAA,QACP,uBAAO;AAAA,QACP,sBAAM,CAAC,WAAW,OAAO,MAAM,IAAI,EAAE,WAAW,CAAC;AAAA,EAClD;AAAA,EACA,mBAAe,0BAAM,uBAAO,CAAC;AAAA,EAC7B,eAAW,sBAAM,mBAAmB;AACrC,CAAC;AAED,MAAM,iCAA6B,uBAAO;AAAA,EACzC,UAAM,wBAAQ,iBAAiB;AAAA,EAC/B,aAAS,sBAAM,mBAAmB;AAAA,EAClC,SAAS;AACV,CAAC;AAED,MAAM,4BAAwB,uBAAO;AAAA,EACpC,UAAM,wBAAQ,YAAY;AAAA,EAC1B,MAAM;AAAA,EACN,aAAS,sBAAM,mBAAmB;AACnC,CAAC;AAED,MAAM,4BAAwB,uBAAO;AAAA,EACpC,UAAM,wBAAQ,YAAY;AAAA,EAC1B,aAAa;AAAA,EACb,aAAS,sBAAM,mBAAmB;AACnC,CAAC;AAED,MAAM,6BAAyB,uBAAO;AAAA,EACrC,UAAM,wBAAQ,aAAa;AAAA,EAC3B,UAAM,sBAAM,KAAC,uBAAO,EAAE,MAAM,QAAQ,CAAC,OAAG,uBAAO,EAAE,UAAM,6BAAS,wBAAQ,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAAA,EAClF,aAAS,sBAAM,mBAAmB;AACnC,CAAC;AAED,MAAM,yBAAqB,uBAAO;AAAA,EACjC,UAAM,wBAAQ,SAAS;AAAA,EACvB,aAAS,0BAAM,0BAAM,yBAAK,uBAAO,OAAG,wBAAQ,CAAC,CAAC,CAAC;AAAA,EAC/C,kBAAc,0BAAM,uBAAO,CAAC;AAC7B,CAAC;AAED,MAAM,yBAAqB,uBAAO;AAAA,EACjC,UAAM,wBAAQ,SAAS;AAAA,EACvB,aAAS,0BAAM,0BAAM,yBAAK,uBAAO,OAAG,wBAAQ,CAAC,CAAC,CAAC;AAAA,EAC/C,kBAAc,0BAAM,uBAAO,CAAC;AAAA,EAC5B,eAAW,uBAAO;AAAA,EAClB,QAAQ;AACT,CAAC;AAED,MAAM,mBAAmB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEA,MAAM,sBAAkB,sBAAM,CAAC,GAAG,gBAAgB,CAAC;AAE5C,MAAM,kCAA8B,uBAAO;AAAA,EACjD,aAAS,wBAAQ,CAAC;AAAA,EAClB,YAAQ,6BAAS,uBAAO,CAAC;AAAA,EACzB,gBAAY,wBAAQ,qBAAqB;AAAA,EACzC,WAAW;AAAA,EACX,YAAQ,sBAAM,gBAAgB;AAAA,EAC9B,kBAAc,sBAAM,eAAe;AACpC,CAAC;AAIM,SAAS,2BACf,iBAC8B;AAC9B,QAAM,SAAiD,gBAAgB,OAAO;AAAA,IAC7E,CAAC,OAAO,UAAU;AACjB,UAAI,MAAM,QAAQ;AACjB,eAAO;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA,OAAO;AAAA,YACN,QAAQ,MAAM,OAAO,mBAClB;AAAA,cACA,YAAY,MAAM,OAAO;AAAA,YAC1B,IACC,MAAM,OAAO,YACZ;AAAA,cACA,WAAW;AAAA,gBACV,QAAQ,MAAM,OAAO,UAAU;AAAA,gBAC/B,SAAS,MAAM,OAAO,UAAU;AAAA,gBAChC,UAAU,MAAM,OAAO,UAAU;AAAA,cAClC;AAAA,YACD,IACC;AAAA,cACA,QAAQ;AAAA,gBACP,SAAS,MAAM,OAAO,aAAa;AAAA,gBACnC,sBAAsB,MAAM,OAAO,aAAa;AAAA,gBAChD,UAAU,MAAM,OAAO,aAAa;AAAA,cACrC;AAAA,YACD;AAAA,UACJ;AAAA,UACA,MAAM;AAAA,QACP;AAAA,MACD;AACA,UAAI,MAAM,MAAM;AACf,eAAO;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA,OAAO;AAAA,YACN,MAAM,MAAM,SAAK,uBAAW,MAAM,KAAK,KAAK,CAAC;AAAA,UAC9C;AAAA,UACA,MAAM;AAAA,QACP;AAAA,MACD;AAEA,UAAI,MAAM,gBAAgB;AACzB,eAAO;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA,OAAO,MAAM,eAAe;AAAA,QAC7B;AAAA,MACD;AAEA,UAAI,MAAM,kBAAkB;AAC3B,eAAO;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA,OAAO,MAAM,iBAAiB;AAAA,QAC/B;AAAA,MACD;AAEA,YAAM,IAAI,MAAM,eAAe;AAAA,IAChC;AAAA,EACD;AAEA,SAAO;AAAA,IACN,SAAS;AAAA,IACT,QAAQ,gBAAgB,UAAU;AAAA,IAClC,YACC,gBAAgB,YAAY,UAAU,UACnC,EAAE,OAAO,OAAO,gBAAgB,WAAW,KAAK,EAAE,IAClD,gBAAgB,aACf,EAAE,MAAM,KAAK,IACb;AAAA,IACL,WAAW;AAAA,MACV,OAAO,gBAAgB,QAAQ,SAAS;AAAA,MACxC,QAAQ,gBAAgB,QAAQ,UAAU;AAAA,MAC1C,OAAO,gBAAgB,QAAQ,SAAS;AAAA,MACxC,SAAS,gBAAgB,QAAQ,WAAW;AAAA,IAC7C;AAAA,IACA;AAAA,IACA,cAAc,gBAAgB,SAAS,IAAI,CAAC,YAAiD;AAC5F,UAAI,QAAQ,aAAa;AACxB,eAAO;AAAA,UACN,MAAM;AAAA,UACN,MACC,QAAQ,YAAY,SAAS,OAC1B,EAAE,MAAM,KAAK,IACb,EAAE,MAAM,8BAAkB,aAAa,QAAQ,YAAY,IAAI,EAAE;AAAA,UACrE,SAAS,QAAQ,YAAY,SAAS;AAAA,YAAI,CAAC,QAC1C,2BAA2B,KAAK,MAAM;AAAA,UACvC;AAAA,QACD;AAAA,MACD;AACA,UAAI,QAAQ,YAAY;AACvB,eAAO;AAAA,UACN,MAAM;AAAA,UACN,aAAa,2BAA2B,QAAQ,WAAW,aAAa,MAAM;AAAA,UAC9E,SAAS,QAAQ,WAAW,QAAQ,IAAI,CAAC,QAAQ,2BAA2B,KAAK,MAAM,CAAC;AAAA,QACzF;AAAA,MACD;AACA,UAAI,QAAQ,UAAU;AACrB,eAAO;AAAA,UACN,MAAM;AAAA,UACN,QAAQ,GAAG,QAAQ,SAAS,OAAO,KAAK,QAAQ,SAAS,MAAM,KAAK,QAAQ,SAAS,QAAQ;AAAA,UAC7F,eAAe,QAAQ,SAAS;AAAA,UAChC,WAAW,QAAQ,SAAS,UAAU;AAAA,YAAI,CAAC,QAC1C,2BAA2B,KAAK,MAAM;AAAA,UACvC;AAAA,QACD;AAAA,MACD;AACA,UAAI,QAAQ,SAAS;AACpB,eAAO;AAAA,UACN,MAAM;AAAA,UACN,SAAS,QAAQ,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,SAAK,uBAAW,GAAG,CAAC,CAAC;AAAA,UACzE,cAAc,QAAQ,QAAQ;AAAA,QAC/B;AAAA,MACD;AACA,UAAI,QAAQ,YAAY;AACvB,eAAO;AAAA,UACN,MAAM;AAAA,UACN,MAAM,2BAA2B,QAAQ,WAAW,MAAM,MAAM;AAAA,UAChE,SAAS,QAAQ,WAAW,QAAQ,IAAI,CAAC,QAAQ,2BAA2B,KAAK,MAAM,CAAC;AAAA,QACzF;AAAA,MACD;AACA,UAAI,QAAQ,iBAAiB;AAC5B,eAAO;AAAA,UACN,MAAM;AAAA,UACN,SAAS,QAAQ,gBAAgB,QAAQ;AAAA,YAAI,CAAC,QAC7C,2BAA2B,KAAK,MAAM;AAAA,UACvC;AAAA,UACA,SAAS,2BAA2B,QAAQ,gBAAgB,SAAS,MAAM;AAAA,QAC5E;AAAA,MACD;AAEA,UAAI,QAAQ,SAAS;AACpB,eAAO;AAAA,UACN,MAAM;AAAA,UACN,SAAS,QAAQ,QAAQ,QAAQ,IAAI,CAAC,QAAQ,MAAM,SAAK,uBAAW,GAAG,CAAC,CAAC;AAAA,UACzE,cAAc,QAAQ,QAAQ;AAAA,UAC9B,WAAW,QAAQ,QAAQ;AAAA,UAC3B,QAAQ,2BAA2B,QAAQ,QAAQ,QAAQ,MAAM;AAAA,QAClE;AAAA,MACD;AAEA,YAAM,IAAI,MAAM,uBAAuB,OAAO,KAAK,OAAO,CAAC,EAAE;AAAA,IAC9D,CAAC;AAAA,EACF;AACD;AAEA,SAAS,2BACR,KACA,QAC0C;AAC1C,MAAI,IAAI,UAAU,WAAW;AAC5B,WAAO,EAAE,MAAM,UAAU;AAAA,EAC1B;AACA,MAAI,IAAI,UAAU,UAAU;AAC3B,WAAO,EAAE,MAAM,UAAU,OAAO,IAAI,OAAO;AAAA,EAC5C;AACA,MAAI,IAAI,UAAU,gBAAgB;AACjC,WAAO,EAAE,MAAM,gBAAgB,OAAO,IAAI,aAAa,CAAC,GAAG,aAAa,IAAI,aAAa,CAAC,EAAE;AAAA,EAC7F;AACA,MAAI,IAAI,UAAU,SAAS;AAC1B,WAAO,OAAO,IAAI,KAAK;AAAA,EACxB;AAEA,QAAM,IAAI,MAAM,oBAAoB,OAAO,KAAK,GAAG,CAAC,EAAE;AACvD;AAEO,SAAS,sBAAsB,MAAoD;AACzF,aAAO,sBAAM,iCAAiB;AAAA,IAC7B,SAAS;AAAA,IACT,QAAQ,KAAK,UAAU;AAAA,IACvB,YAAY,KAAK,aACd,WAAW,KAAK,aACf,EAAE,OAAO,KAAK,WAAW,MAAM,IAC/B,EAAE,MAAM,KAAK,IACd;AAAA,IACH,SAAS;AAAA,MACR,OAAO,KAAK,UAAU,SAAS;AAAA,MAC/B,QAAQ,KAAK,UAAU,QAAQ,SAAS,KAAK;AAAA,MAC7C,OAAO,KAAK,UAAU,OAAO,SAAS,KAAK;AAAA,MAC3C,SACC,KAAK,UAAU,SAAS,IAAI,CAAC,SAAS;AAAA,QACrC,QAAQ,IAAI;AAAA,QACZ,UAAU,IAAI;AAAA,QACd,SAAS,IAAI,QAAQ,SAAS;AAAA,MAC/B,EAAE,KAAK;AAAA,IACT;AAAA,IACA,QAAQ,KAAK,OAAO,IAAI,CAAC,UAAU;AAClC,UAAI,MAAM,SAAS,SAAS;AAC3B,gBAAI,mBAAG,mBAAmB,MAAM,KAAK,GAAG;AACvC,gBAAM,YAAQ,sBAAM,mBAAmB,MAAM,KAAK;AAElD,cAAI,MAAM,QAAQ;AACjB,gBAAI,MAAM,OAAO,YAAY;AAC5B,qBAAO;AAAA,gBACN,QAAQ;AAAA,kBACP,kBAAkB;AAAA,oBACjB,UAAU,MAAM,OAAO,WAAW;AAAA,oBAClC,SAAS,OAAO,MAAM,OAAO,WAAW,OAAO;AAAA,oBAC/C,QAAQ,MAAM,OAAO,WAAW;AAAA,kBACjC;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AACA,gBAAI,MAAM,OAAO,QAAQ;AACxB,qBAAO;AAAA,gBACN,QAAQ;AAAA,kBACP,cAAc;AAAA,oBACb,SAAS,MAAM,OAAO,OAAO,WAAW;AAAA,oBACxC,sBAAsB,MAAM,OAAO,OAAO;AAAA,oBAC1C,UAAU,MAAM,OAAO,OAAO;AAAA,kBAC/B;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AACA,gBAAI,MAAM,OAAO,WAAW;AAC3B,qBAAO;AAAA,gBACN,QAAQ;AAAA,kBACP,WAAW;AAAA,oBACV,QAAQ,MAAM,OAAO,UAAU;AAAA,oBAC/B,SAAS,OAAO,MAAM,OAAO,UAAU,OAAO;AAAA,oBAC9C,UAAU,MAAM,OAAO,UAAU;AAAA,kBAClC;AAAA,gBACD;AAAA,cACD;AAAA,YACD;AAEA,kBAAM,IAAI,MAAM,sBAAsB;AAAA,UACvC;AAEA,iBAAO;AAAA,YACN,MAAM;AAAA,cACL,WAAO,qBAAS,IAAI,WAAW,MAAM,IAAI,CAAC;AAAA,YAC3C;AAAA,UACD;AAAA,QACD;AAEA,YAAI,MAAM,SAAS,UAAU;AAC5B,iBAAO;AAAA,YACN,kBAAkB;AAAA,cACjB,UAAU,MAAM;AAAA,YACjB;AAAA,UACD;AAAA,QACD;AAEA,eAAO;AAAA,UACN,gBAAgB;AAAA,YACf,OAAO,MAAM;AAAA,UACd;AAAA,QACD;AAAA,MACD;AAEA,YAAM,IAAI,MAAM,eAAe;AAAA,IAChC,CAAC;AAAA,IACD,UAAU,KAAK,aAAa,IAAI,CAAC,gBAAgB;AAChD,cAAQ,YAAY,MAAM;AAAA,QACzB,KAAK;AACJ,iBAAO;AAAA,YACN,aAAa;AAAA,cACZ,MACC,UAAU,YAAY,OACnB,8BAAkB,YAAY,YAAY,KAAK,IAAI,IACnD;AAAA,cACJ,UAAU,YAAY,QAAQ,IAAI,CAAC,QAAQ,2BAA2B,GAAG,CAAC;AAAA,YAC3E;AAAA,UACD;AAAA,QACD,KAAK,cAAc;AAClB,iBAAO;AAAA,YACN,YAAY;AAAA,cACX,aAAa,2BAA2B,YAAY,WAAW;AAAA,cAC/D,SAAS,YAAY,QAAQ,IAAI,CAAC,QAAQ,2BAA2B,GAAG,CAAC;AAAA,YAC1E;AAAA,UACD;AAAA,QACD;AAAA,QACA,KAAK,YAAY;AAChB,gBAAM,CAAC,KAAK,KAAK,EAAE,IAAI,YAAY,OAAO,MAAM,IAAI;AACpD,iBAAO;AAAA,YACN,UAAU;AAAA,cACT,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,UAAU;AAAA,cACV,eAAe,YAAY;AAAA,cAC3B,WAAW,YAAY,UAAU,IAAI,CAAC,QAAQ,2BAA2B,GAAG,CAAC;AAAA,YAC9E;AAAA,UACD;AAAA,QACD;AAAA,QACA,KAAK,WAAW;AACf,iBAAO;AAAA,YACN,SAAS;AAAA,cACR,SAAS,YAAY,QAAQ,IAAI,CAAC,YAAQ,qBAAS,WAAW,KAAK,GAAG,CAAC,CAAC;AAAA,cACxE,cAAc,YAAY;AAAA,YAC3B;AAAA,UACD;AAAA,QACD;AAAA,QACA,KAAK,cAAc;AAClB,iBAAO;AAAA,YACN,YAAY;AAAA,cACX,MAAM,2BAA2B,YAAY,IAAI;AAAA,cACjD,SAAS,YAAY,QAAQ,IAAI,CAAC,QAAQ,2BAA2B,GAAG,CAAC;AAAA,YAC1E;AAAA,UACD;AAAA,QACD;AAAA,QACA,KAAK,mBAAmB;AACvB,iBAAO;AAAA,YACN,iBAAiB;AAAA,cAChB,SAAS,YAAY,QAAQ,IAAI,CAAC,QAAQ,2BAA2B,GAAG,CAAC;AAAA,cACzE,SAAS,2BAA2B,YAAY,OAAO;AAAA,YACxD;AAAA,UACD;AAAA,QACD;AAAA,QACA,KAAK,WAAW;AACf,iBAAO;AAAA,YACN,SAAS;AAAA,cACR,SAAS,YAAY,QAAQ,IAAI,CAAC,YAAQ,qBAAS,WAAW,KAAK,GAAG,CAAC,CAAC;AAAA,cACxE,cAAc,YAAY;AAAA,cAC1B,SAAS,YAAY;AAAA,cACrB,QAAQ,2BAA2B,YAAY,MAAM;AAAA,YACtD;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,YAAM,IAAI,MAAM,uBAAuB,OAAO,KAAK,WAAW,CAAC,EAAE;AAAA,IAClE,CAAC;AAAA,EACF,CAA8C;AAC/C;AAEA,SAAS,2BACR,KAC8B;AAC9B,UAAQ,IAAI,MAAM;AAAA,IACjB,KAAK,WAAW;AACf,aAAO,EAAE,SAAS,KAAK;AAAA,IACxB;AAAA,IACA,KAAK;AACJ,aAAO,EAAE,QAAQ,IAAI,MAAM;AAAA,IAC5B,KAAK,gBAAgB;AACpB,aAAO,EAAE,cAAc,CAAC,IAAI,OAAO,IAAI,WAAW,EAAE;AAAA,IACrD;AAAA,IACA,KAAK,SAAS;AACb,aAAO,EAAE,OAAO,IAAI,MAAM;AAAA,IAC3B;AAAA,EACD;AACD;", "names": ["import_bcs"]}