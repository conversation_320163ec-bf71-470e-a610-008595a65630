{"version": 3, "sources": ["../../../src/transactions/serializer.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { BcsType } from '@mysten/bcs';\n\nimport { bcs } from '../bcs/index.js';\nimport type { SuiMoveNormalizedType } from '../client/index.js';\nimport { MOVE_STDLIB_ADDRESS, SUI_FRAMEWORK_ADDRESS } from '../utils/index.js';\nimport { normalizeSuiAddress } from '../utils/sui-types.js';\nimport type { OpenMoveTypeSignature, OpenMoveTypeSignatureBody } from './data/internal.js';\n\nconst OBJECT_MODULE_NAME = 'object';\nconst ID_STRUCT_NAME = 'ID';\n\nconst STD_ASCII_MODULE_NAME = 'ascii';\nconst STD_ASCII_STRUCT_NAME = 'String';\n\nconst STD_UTF8_MODULE_NAME = 'string';\nconst STD_UTF8_STRUCT_NAME = 'String';\n\nconst STD_OPTION_MODULE_NAME = 'option';\nconst STD_OPTION_STRUCT_NAME = 'Option';\n\nexport function isTxContext(param: OpenMoveTypeSignature): boolean {\n\tconst struct =\n\t\ttypeof param.body === 'object' && 'datatype' in param.body ? param.body.datatype : null;\n\n\treturn (\n\t\t!!struct &&\n\t\tnormalizeSuiAddress(struct.package) === normalizeSuiAddress('0x2') &&\n\t\tstruct.module === 'tx_context' &&\n\t\tstruct.type === 'TxContext'\n\t);\n}\n\nexport function getPureBcsSchema(typeSignature: OpenMoveTypeSignatureBody): BcsType<any> | null {\n\tif (typeof typeSignature === 'string') {\n\t\tswitch (typeSignature) {\n\t\t\tcase 'address':\n\t\t\t\treturn bcs.Address;\n\t\t\tcase 'bool':\n\t\t\t\treturn bcs.Bool;\n\t\t\tcase 'u8':\n\t\t\t\treturn bcs.U8;\n\t\t\tcase 'u16':\n\t\t\t\treturn bcs.U16;\n\t\t\tcase 'u32':\n\t\t\t\treturn bcs.U32;\n\t\t\tcase 'u64':\n\t\t\t\treturn bcs.U64;\n\t\t\tcase 'u128':\n\t\t\t\treturn bcs.U128;\n\t\t\tcase 'u256':\n\t\t\t\treturn bcs.U256;\n\t\t\tdefault:\n\t\t\t\tthrow new Error(`Unknown type signature ${typeSignature}`);\n\t\t}\n\t}\n\n\tif ('vector' in typeSignature) {\n\t\tif (typeSignature.vector === 'u8') {\n\t\t\treturn bcs.vector(bcs.U8).transform({\n\t\t\t\tinput: (val: string | Uint8Array) =>\n\t\t\t\t\ttypeof val === 'string' ? new TextEncoder().encode(val) : val,\n\t\t\t\toutput: (val) => val,\n\t\t\t});\n\t\t}\n\t\tconst type = getPureBcsSchema(typeSignature.vector);\n\t\treturn type ? bcs.vector(type) : null;\n\t}\n\n\tif ('datatype' in typeSignature) {\n\t\tconst pkg = normalizeSuiAddress(typeSignature.datatype.package);\n\n\t\tif (pkg === normalizeSuiAddress(MOVE_STDLIB_ADDRESS)) {\n\t\t\tif (\n\t\t\t\ttypeSignature.datatype.module === STD_ASCII_MODULE_NAME &&\n\t\t\t\ttypeSignature.datatype.type === STD_ASCII_STRUCT_NAME\n\t\t\t) {\n\t\t\t\treturn bcs.String;\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\ttypeSignature.datatype.module === STD_UTF8_MODULE_NAME &&\n\t\t\t\ttypeSignature.datatype.type === STD_UTF8_STRUCT_NAME\n\t\t\t) {\n\t\t\t\treturn bcs.String;\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\ttypeSignature.datatype.module === STD_OPTION_MODULE_NAME &&\n\t\t\t\ttypeSignature.datatype.type === STD_OPTION_STRUCT_NAME\n\t\t\t) {\n\t\t\t\tconst type = getPureBcsSchema(typeSignature.datatype.typeParameters[0]);\n\t\t\t\treturn type ? bcs.vector(type) : null;\n\t\t\t}\n\t\t}\n\n\t\tif (\n\t\t\tpkg === normalizeSuiAddress(SUI_FRAMEWORK_ADDRESS) &&\n\t\t\ttypeSignature.datatype.module === OBJECT_MODULE_NAME &&\n\t\t\ttypeSignature.datatype.type === ID_STRUCT_NAME\n\t\t) {\n\t\t\treturn bcs.Address;\n\t\t}\n\t}\n\n\treturn null;\n}\n\nexport function normalizedTypeToMoveTypeSignature(\n\ttype: SuiMoveNormalizedType,\n): OpenMoveTypeSignature {\n\tif (typeof type === 'object' && 'Reference' in type) {\n\t\treturn {\n\t\t\tref: '&',\n\t\t\tbody: normalizedTypeToMoveTypeSignatureBody(type.Reference),\n\t\t};\n\t}\n\tif (typeof type === 'object' && 'MutableReference' in type) {\n\t\treturn {\n\t\t\tref: '&mut',\n\t\t\tbody: normalizedTypeToMoveTypeSignatureBody(type.MutableReference),\n\t\t};\n\t}\n\n\treturn {\n\t\tref: null,\n\t\tbody: normalizedTypeToMoveTypeSignatureBody(type),\n\t};\n}\n\nfunction normalizedTypeToMoveTypeSignatureBody(\n\ttype: SuiMoveNormalizedType,\n): OpenMoveTypeSignatureBody {\n\tif (typeof type === 'string') {\n\t\tswitch (type) {\n\t\t\tcase 'Address':\n\t\t\t\treturn 'address';\n\t\t\tcase 'Bool':\n\t\t\t\treturn 'bool';\n\t\t\tcase 'U8':\n\t\t\t\treturn 'u8';\n\t\t\tcase 'U16':\n\t\t\t\treturn 'u16';\n\t\t\tcase 'U32':\n\t\t\t\treturn 'u32';\n\t\t\tcase 'U64':\n\t\t\t\treturn 'u64';\n\t\t\tcase 'U128':\n\t\t\t\treturn 'u128';\n\t\t\tcase 'U256':\n\t\t\t\treturn 'u256';\n\t\t\tdefault:\n\t\t\t\tthrow new Error(`Unexpected type ${type}`);\n\t\t}\n\t}\n\n\tif ('Vector' in type) {\n\t\treturn { vector: normalizedTypeToMoveTypeSignatureBody(type.Vector) };\n\t}\n\n\tif ('Struct' in type) {\n\t\treturn {\n\t\t\tdatatype: {\n\t\t\t\tpackage: type.Struct.address,\n\t\t\t\tmodule: type.Struct.module,\n\t\t\t\ttype: type.Struct.name,\n\t\t\t\ttypeParameters: type.Struct.typeArguments.map(normalizedTypeToMoveTypeSignatureBody),\n\t\t\t},\n\t\t};\n\t}\n\n\tif ('TypeParameter' in type) {\n\t\treturn { typeParameter: type.TypeParameter };\n\t}\n\n\tthrow new Error(`Unexpected type ${JSON.stringify(type)}`);\n}\n\nexport function pureBcsSchemaFromOpenMoveTypeSignatureBody(\n\ttypeSignature: OpenMoveTypeSignatureBody,\n): BcsType<any> {\n\tif (typeof typeSignature === 'string') {\n\t\tswitch (typeSignature) {\n\t\t\tcase 'address':\n\t\t\t\treturn bcs.Address;\n\t\t\tcase 'bool':\n\t\t\t\treturn bcs.Bool;\n\t\t\tcase 'u8':\n\t\t\t\treturn bcs.U8;\n\t\t\tcase 'u16':\n\t\t\t\treturn bcs.U16;\n\t\t\tcase 'u32':\n\t\t\t\treturn bcs.U32;\n\t\t\tcase 'u64':\n\t\t\t\treturn bcs.U64;\n\t\t\tcase 'u128':\n\t\t\t\treturn bcs.U128;\n\t\t\tcase 'u256':\n\t\t\t\treturn bcs.U256;\n\t\t\tdefault:\n\t\t\t\tthrow new Error(`Unknown type signature ${typeSignature}`);\n\t\t}\n\t}\n\n\tif ('vector' in typeSignature) {\n\t\treturn bcs.vector(pureBcsSchemaFromOpenMoveTypeSignatureBody(typeSignature.vector));\n\t}\n\n\tthrow new Error(`Expected pure typeSignature, but got ${JSON.stringify(typeSignature)}`);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAKA,iBAAoB;AAEpB,mBAA2D;AAC3D,uBAAoC;AAGpC,MAAM,qBAAqB;AAC3B,MAAM,iBAAiB;AAEvB,MAAM,wBAAwB;AAC9B,MAAM,wBAAwB;AAE9B,MAAM,uBAAuB;AAC7B,MAAM,uBAAuB;AAE7B,MAAM,yBAAyB;AAC/B,MAAM,yBAAyB;AAExB,SAAS,YAAY,OAAuC;AAClE,QAAM,SACL,OAAO,MAAM,SAAS,YAAY,cAAc,MAAM,OAAO,MAAM,KAAK,WAAW;AAEpF,SACC,CAAC,CAAC,cACF,sCAAoB,OAAO,OAAO,UAAM,sCAAoB,KAAK,KACjE,OAAO,WAAW,gBAClB,OAAO,SAAS;AAElB;AAEO,SAAS,iBAAiB,eAA+D;AAC/F,MAAI,OAAO,kBAAkB,UAAU;AACtC,YAAQ,eAAe;AAAA,MACtB,KAAK;AACJ,eAAO,eAAI;AAAA,MACZ,KAAK;AACJ,eAAO,eAAI;AAAA,MACZ,KAAK;AACJ,eAAO,eAAI;AAAA,MACZ,KAAK;AACJ,eAAO,eAAI;AAAA,MACZ,KAAK;AACJ,eAAO,eAAI;AAAA,MACZ,KAAK;AACJ,eAAO,eAAI;AAAA,MACZ,KAAK;AACJ,eAAO,eAAI;AAAA,MACZ,KAAK;AACJ,eAAO,eAAI;AAAA,MACZ;AACC,cAAM,IAAI,MAAM,0BAA0B,aAAa,EAAE;AAAA,IAC3D;AAAA,EACD;AAEA,MAAI,YAAY,eAAe;AAC9B,QAAI,cAAc,WAAW,MAAM;AAClC,aAAO,eAAI,OAAO,eAAI,EAAE,EAAE,UAAU;AAAA,QACnC,OAAO,CAAC,QACP,OAAO,QAAQ,WAAW,IAAI,YAAY,EAAE,OAAO,GAAG,IAAI;AAAA,QAC3D,QAAQ,CAAC,QAAQ;AAAA,MAClB,CAAC;AAAA,IACF;AACA,UAAM,OAAO,iBAAiB,cAAc,MAAM;AAClD,WAAO,OAAO,eAAI,OAAO,IAAI,IAAI;AAAA,EAClC;AAEA,MAAI,cAAc,eAAe;AAChC,UAAM,UAAM,sCAAoB,cAAc,SAAS,OAAO;AAE9D,QAAI,YAAQ,sCAAoB,gCAAmB,GAAG;AACrD,UACC,cAAc,SAAS,WAAW,yBAClC,cAAc,SAAS,SAAS,uBAC/B;AACD,eAAO,eAAI;AAAA,MACZ;AAEA,UACC,cAAc,SAAS,WAAW,wBAClC,cAAc,SAAS,SAAS,sBAC/B;AACD,eAAO,eAAI;AAAA,MACZ;AAEA,UACC,cAAc,SAAS,WAAW,0BAClC,cAAc,SAAS,SAAS,wBAC/B;AACD,cAAM,OAAO,iBAAiB,cAAc,SAAS,eAAe,CAAC,CAAC;AACtE,eAAO,OAAO,eAAI,OAAO,IAAI,IAAI;AAAA,MAClC;AAAA,IACD;AAEA,QACC,YAAQ,sCAAoB,kCAAqB,KACjD,cAAc,SAAS,WAAW,sBAClC,cAAc,SAAS,SAAS,gBAC/B;AACD,aAAO,eAAI;AAAA,IACZ;AAAA,EACD;AAEA,SAAO;AACR;AAEO,SAAS,kCACf,MACwB;AACxB,MAAI,OAAO,SAAS,YAAY,eAAe,MAAM;AACpD,WAAO;AAAA,MACN,KAAK;AAAA,MACL,MAAM,sCAAsC,KAAK,SAAS;AAAA,IAC3D;AAAA,EACD;AACA,MAAI,OAAO,SAAS,YAAY,sBAAsB,MAAM;AAC3D,WAAO;AAAA,MACN,KAAK;AAAA,MACL,MAAM,sCAAsC,KAAK,gBAAgB;AAAA,IAClE;AAAA,EACD;AAEA,SAAO;AAAA,IACN,KAAK;AAAA,IACL,MAAM,sCAAsC,IAAI;AAAA,EACjD;AACD;AAEA,SAAS,sCACR,MAC4B;AAC5B,MAAI,OAAO,SAAS,UAAU;AAC7B,YAAQ,MAAM;AAAA,MACb,KAAK;AACJ,eAAO;AAAA,MACR,KAAK;AACJ,eAAO;AAAA,MACR,KAAK;AACJ,eAAO;AAAA,MACR,KAAK;AACJ,eAAO;AAAA,MACR,KAAK;AACJ,eAAO;AAAA,MACR,KAAK;AACJ,eAAO;AAAA,MACR,KAAK;AACJ,eAAO;AAAA,MACR,KAAK;AACJ,eAAO;AAAA,MACR;AACC,cAAM,IAAI,MAAM,mBAAmB,IAAI,EAAE;AAAA,IAC3C;AAAA,EACD;AAEA,MAAI,YAAY,MAAM;AACrB,WAAO,EAAE,QAAQ,sCAAsC,KAAK,MAAM,EAAE;AAAA,EACrE;AAEA,MAAI,YAAY,MAAM;AACrB,WAAO;AAAA,MACN,UAAU;AAAA,QACT,SAAS,KAAK,OAAO;AAAA,QACrB,QAAQ,KAAK,OAAO;AAAA,QACpB,MAAM,KAAK,OAAO;AAAA,QAClB,gBAAgB,KAAK,OAAO,cAAc,IAAI,qCAAqC;AAAA,MACpF;AAAA,IACD;AAAA,EACD;AAEA,MAAI,mBAAmB,MAAM;AAC5B,WAAO,EAAE,eAAe,KAAK,cAAc;AAAA,EAC5C;AAEA,QAAM,IAAI,MAAM,mBAAmB,KAAK,UAAU,IAAI,CAAC,EAAE;AAC1D;AAEO,SAAS,2CACf,eACe;AACf,MAAI,OAAO,kBAAkB,UAAU;AACtC,YAAQ,eAAe;AAAA,MACtB,KAAK;AACJ,eAAO,eAAI;AAAA,MACZ,KAAK;AACJ,eAAO,eAAI;AAAA,MACZ,KAAK;AACJ,eAAO,eAAI;AAAA,MACZ,KAAK;AACJ,eAAO,eAAI;AAAA,MACZ,KAAK;AACJ,eAAO,eAAI;AAAA,MACZ,KAAK;AACJ,eAAO,eAAI;AAAA,MACZ,KAAK;AACJ,eAAO,eAAI;AAAA,MACZ,KAAK;AACJ,eAAO,eAAI;AAAA,MACZ;AACC,cAAM,IAAI,MAAM,0BAA0B,aAAa,EAAE;AAAA,IAC3D;AAAA,EACD;AAEA,MAAI,YAAY,eAAe;AAC9B,WAAO,eAAI,OAAO,2CAA2C,cAAc,MAAM,CAAC;AAAA,EACnF;AAEA,QAAM,IAAI,MAAM,wCAAwC,KAAK,UAAU,aAAa,CAAC,EAAE;AACxF;", "names": []}