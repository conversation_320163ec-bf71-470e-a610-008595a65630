{"version": 3, "sources": ["../../../src/utils/constants.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { normalizeSuiObjectId } from './sui-types.js';\n\nexport const SUI_DECIMALS = 9;\nexport const MIST_PER_SUI = BigInt(**********);\n\nexport const MOVE_STDLIB_ADDRESS = '0x1';\nexport const SUI_FRAMEWORK_ADDRESS = '0x2';\nexport const SUI_SYSTEM_ADDRESS = '0x3';\nexport const SUI_CLOCK_OBJECT_ID = normalizeSuiObjectId('0x6');\nexport const SUI_SYSTEM_MODULE_NAME = 'sui_system';\nexport const SUI_TYPE_ARG = `${SUI_FRAMEWORK_ADDRESS}::sui::SUI`;\nexport const SUI_SYSTEM_STATE_OBJECT_ID: string = normalizeSuiObjectId('0x5');\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,uBAAqC;AAE9B,MAAM,eAAe;AACrB,MAAM,eAAe,OAAO,GAAU;AAEtC,MAAM,sBAAsB;AAC5B,MAAM,wBAAwB;AAC9B,MAAM,qBAAqB;AAC3B,MAAM,0BAAsB,uCAAqB,KAAK;AACtD,MAAM,yBAAyB;AAC/B,MAAM,eAAe,GAAG,qBAAqB;AAC7C,MAAM,iCAAqC,uCAAqB,KAAK;", "names": []}