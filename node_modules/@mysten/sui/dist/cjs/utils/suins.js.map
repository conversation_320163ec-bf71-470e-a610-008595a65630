{"version": 3, "sources": ["../../../src/utils/suins.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nconst SUI_NS_NAME_REGEX =\n\t/^(?!.*(^(?!@)|[-.@])($|[-.@]))(?:[a-z0-9-]{0,63}(?:\\.[a-z0-9-]{0,63})*)?@[a-z0-9-]{0,63}$/i;\nconst SUI_NS_DOMAIN_REGEX = /^(?!.*(^|[-.])($|[-.]))(?:[a-z0-9-]{0,63}\\.)+sui$/i;\nconst MAX_SUI_NS_NAME_LENGTH = 235;\n\nexport function isValidSuiNSName(name: string): boolean {\n\tif (name.length > MAX_SUI_NS_NAME_LENGTH) {\n\t\treturn false;\n\t}\n\n\tif (name.includes('@')) {\n\t\treturn SUI_NS_NAME_REGEX.test(name);\n\t}\n\n\treturn SUI_NS_DOMAIN_REGEX.test(name);\n}\n\nexport function normalizeSuiNSName(name: string, format: 'at' | 'dot' = 'at'): string {\n\tconst lowerCase = name.toLowerCase();\n\tlet parts;\n\n\tif (lowerCase.includes('@')) {\n\t\tif (!SUI_NS_NAME_REGEX.test(lowerCase)) {\n\t\t\tthrow new Error(`Invalid SuiNS name ${name}`);\n\t\t}\n\t\tconst [labels, domain] = lowerCase.split('@');\n\t\tparts = [...(labels ? labels.split('.') : []), domain];\n\t} else {\n\t\tif (!SUI_NS_DOMAIN_REGEX.test(lowerCase)) {\n\t\t\tthrow new Error(`Invalid SuiNS name ${name}`);\n\t\t}\n\t\tparts = lowerCase.split('.').slice(0, -1);\n\t}\n\n\tif (format === 'dot') {\n\t\treturn `${parts.join('.')}.sui`;\n\t}\n\n\treturn `${parts.slice(0, -1).join('.')}@${parts[parts.length - 1]}`;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,MAAM,oBACL;AACD,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB;AAExB,SAAS,iBAAiB,MAAuB;AACvD,MAAI,KAAK,SAAS,wBAAwB;AACzC,WAAO;AAAA,EACR;AAEA,MAAI,KAAK,SAAS,GAAG,GAAG;AACvB,WAAO,kBAAkB,KAAK,IAAI;AAAA,EACnC;AAEA,SAAO,oBAAoB,KAAK,IAAI;AACrC;AAEO,SAAS,mBAAmB,MAAc,SAAuB,MAAc;AACrF,QAAM,YAAY,KAAK,YAAY;AACnC,MAAI;AAEJ,MAAI,UAAU,SAAS,GAAG,GAAG;AAC5B,QAAI,CAAC,kBAAkB,KAAK,SAAS,GAAG;AACvC,YAAM,IAAI,MAAM,sBAAsB,IAAI,EAAE;AAAA,IAC7C;AACA,UAAM,CAAC,QAAQ,MAAM,IAAI,UAAU,MAAM,GAAG;AAC5C,YAAQ,CAAC,GAAI,SAAS,OAAO,MAAM,GAAG,IAAI,CAAC,GAAI,MAAM;AAAA,EACtD,OAAO;AACN,QAAI,CAAC,oBAAoB,KAAK,SAAS,GAAG;AACzC,YAAM,IAAI,MAAM,sBAAsB,IAAI,EAAE;AAAA,IAC7C;AACA,YAAQ,UAAU,MAAM,GAAG,EAAE,MAAM,GAAG,EAAE;AAAA,EACzC;AAEA,MAAI,WAAW,OAAO;AACrB,WAAO,GAAG,MAAM,KAAK,GAAG,CAAC;AAAA,EAC1B;AAEA,SAAO,GAAG,MAAM,MAAM,GAAG,EAAE,EAAE,KAAK,GAAG,CAAC,IAAI,MAAM,MAAM,SAAS,CAAC,CAAC;AAClE;", "names": []}