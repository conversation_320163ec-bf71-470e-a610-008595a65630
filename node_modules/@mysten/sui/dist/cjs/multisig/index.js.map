{"version": 3, "sources": ["../../../src/multisig/index.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nexport { MultiSigSigner } from './signer.js';\nexport { type MultiSigStruct, MultiSigPublicKey, parsePartialSignatures } from './publickey.js';\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,oBAA+B;AAC/B,uBAA+E;", "names": []}