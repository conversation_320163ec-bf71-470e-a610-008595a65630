{"version": 3, "sources": ["../../../src/cryptography/mnemonics.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\nimport { toHex } from '@mysten/bcs';\nimport { mnemonicToSeedSync as bip39MnemonicToSeedSync } from '@scure/bip39';\n\n/**\n * Parse and validate a path that is compliant to SLIP-0010 in form m/44'/784'/{account_index}'/{change_index}'/{address_index}'.\n *\n * @param path path string (e.g. `m/44'/784'/0'/0'/0'`).\n */\nexport function isValidHardenedPath(path: string): boolean {\n\tif (!new RegExp(\"^m\\\\/44'\\\\/784'\\\\/[0-9]+'\\\\/[0-9]+'\\\\/[0-9]+'+$\").test(path)) {\n\t\treturn false;\n\t}\n\treturn true;\n}\n\n/**\n * Parse and validate a path that is compliant to BIP-32 in form m/54'/784'/{account_index}'/{change_index}/{address_index}\n * for Secp256k1 and m/74'/784'/{account_index}'/{change_index}/{address_index} for Secp256r1.\n *\n * Note that the purpose for Secp256k1 is registered as 54, to differentiate from Ed25519 with purpose 44.\n *\n * @param path path string (e.g. `m/54'/784'/0'/0/0`).\n */\nexport function isValidBIP32Path(path: string): boolean {\n\tif (!new RegExp(\"^m\\\\/(54|74)'\\\\/784'\\\\/[0-9]+'\\\\/[0-9]+\\\\/[0-9]+$\").test(path)) {\n\t\treturn false;\n\t}\n\treturn true;\n}\n\n/**\n * Uses KDF to derive 64 bytes of key data from mnemonic with empty password.\n *\n * @param mnemonics 12 words string split by spaces.\n */\nexport function mnemonicToSeed(mnemonics: string): Uint8Array {\n\treturn bip39MnemonicToSeedSync(mnemonics, '');\n}\n\n/**\n * Derive the seed in hex format from a 12-word mnemonic string.\n *\n * @param mnemonics 12 words string split by spaces.\n */\nexport function mnemonicToSeedHex(mnemonics: string): string {\n\treturn toHex(mnemonicToSeed(mnemonics));\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,iBAAsB;AACtB,mBAA8D;AAOvD,SAAS,oBAAoB,MAAuB;AAC1D,MAAI,CAAC,IAAI,OAAO,iDAAiD,EAAE,KAAK,IAAI,GAAG;AAC9E,WAAO;AAAA,EACR;AACA,SAAO;AACR;AAUO,SAAS,iBAAiB,MAAuB;AACvD,MAAI,CAAC,IAAI,OAAO,mDAAmD,EAAE,KAAK,IAAI,GAAG;AAChF,WAAO;AAAA,EACR;AACA,SAAO;AACR;AAOO,SAAS,eAAe,WAA+B;AAC7D,aAAO,aAAAA,oBAAwB,WAAW,EAAE;AAC7C;AAOO,SAAS,kBAAkB,WAA2B;AAC5D,aAAO,kBAAM,eAAe,SAAS,CAAC;AACvC;", "names": ["bip39MnemonicToSeedSync"]}