{"version": 3, "sources": ["../../../../src/keypairs/ed25519/ed25519-hd-key.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\n// This is adapted from https://github.com/alepop/ed25519-hd-key replacing create-hmac\n// with @noble/hashes to be browser compatible.\n\nimport { fromHex } from '@mysten/bcs';\nimport { hmac } from '@noble/hashes/hmac';\nimport { sha512 } from '@noble/hashes/sha512';\n\ntype Hex = string;\ntype Path = string;\n\ntype Keys = {\n\tkey: Uint8Array;\n\tchainCode: Uint8Array;\n};\n\nconst ED25519_CURVE = 'ed25519 seed';\nconst HARDENED_OFFSET = 0x80000000;\n\nconst pathRegex = new RegExp(\"^m(\\\\/[0-9]+')+$\");\n\nconst replaceDerive = (val: string): string => val.replace(\"'\", '');\n\nconst getMasterKeyFromSeed = (seed: Hex): Keys => {\n\tconst h = hmac.create(sha512, ED25519_CURVE);\n\tconst I = h.update(fromHex(seed)).digest();\n\tconst IL = I.slice(0, 32);\n\tconst IR = I.slice(32);\n\treturn {\n\t\tkey: IL,\n\t\tchainCode: IR,\n\t};\n};\n\nconst CKDPriv = ({ key, chainCode }: Keys, index: number): Keys => {\n\tconst indexBuffer = new ArrayBuffer(4);\n\tconst cv = new DataView(indexBuffer);\n\tcv.setUint32(0, index);\n\n\tconst data = new Uint8Array(1 + key.length + indexBuffer.byteLength);\n\tdata.set(new Uint8Array(1).fill(0));\n\tdata.set(key, 1);\n\tdata.set(new Uint8Array(indexBuffer, 0, indexBuffer.byteLength), key.length + 1);\n\n\tconst I = hmac.create(sha512, chainCode).update(data).digest();\n\tconst IL = I.slice(0, 32);\n\tconst IR = I.slice(32);\n\treturn {\n\t\tkey: IL,\n\t\tchainCode: IR,\n\t};\n};\n\nconst isValidPath = (path: string): boolean => {\n\tif (!pathRegex.test(path)) {\n\t\treturn false;\n\t}\n\treturn !path\n\t\t.split('/')\n\t\t.slice(1)\n\t\t.map(replaceDerive)\n\t\t.some(isNaN as any /* ts T_T*/);\n};\n\nexport const derivePath = (path: Path, seed: Hex, offset = HARDENED_OFFSET): Keys => {\n\tif (!isValidPath(path)) {\n\t\tthrow new Error('Invalid derivation path');\n\t}\n\n\tconst { key, chainCode } = getMasterKeyFromSeed(seed);\n\tconst segments = path\n\t\t.split('/')\n\t\t.slice(1)\n\t\t.map(replaceDerive)\n\t\t.map((el) => parseInt(el, 10));\n\n\treturn segments.reduce((parentKeys, segment) => CKDPriv(parentKeys, segment + offset), {\n\t\tkey,\n\t\tchainCode,\n\t});\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAMA,iBAAwB;AACxB,kBAAqB;AACrB,oBAAuB;AAUvB,MAAM,gBAAgB;AACtB,MAAM,kBAAkB;AAExB,MAAM,YAAY,IAAI,OAAO,kBAAkB;AAE/C,MAAM,gBAAgB,CAAC,QAAwB,IAAI,QAAQ,KAAK,EAAE;AAElE,MAAM,uBAAuB,CAAC,SAAoB;AACjD,QAAM,IAAI,iBAAK,OAAO,sBAAQ,aAAa;AAC3C,QAAM,IAAI,EAAE,WAAO,oBAAQ,IAAI,CAAC,EAAE,OAAO;AACzC,QAAM,KAAK,EAAE,MAAM,GAAG,EAAE;AACxB,QAAM,KAAK,EAAE,MAAM,EAAE;AACrB,SAAO;AAAA,IACN,KAAK;AAAA,IACL,WAAW;AAAA,EACZ;AACD;AAEA,MAAM,UAAU,CAAC,EAAE,KAAK,UAAU,GAAS,UAAwB;AAClE,QAAM,cAAc,IAAI,YAAY,CAAC;AACrC,QAAM,KAAK,IAAI,SAAS,WAAW;AACnC,KAAG,UAAU,GAAG,KAAK;AAErB,QAAM,OAAO,IAAI,WAAW,IAAI,IAAI,SAAS,YAAY,UAAU;AACnE,OAAK,IAAI,IAAI,WAAW,CAAC,EAAE,KAAK,CAAC,CAAC;AAClC,OAAK,IAAI,KAAK,CAAC;AACf,OAAK,IAAI,IAAI,WAAW,aAAa,GAAG,YAAY,UAAU,GAAG,IAAI,SAAS,CAAC;AAE/E,QAAM,IAAI,iBAAK,OAAO,sBAAQ,SAAS,EAAE,OAAO,IAAI,EAAE,OAAO;AAC7D,QAAM,KAAK,EAAE,MAAM,GAAG,EAAE;AACxB,QAAM,KAAK,EAAE,MAAM,EAAE;AACrB,SAAO;AAAA,IACN,KAAK;AAAA,IACL,WAAW;AAAA,EACZ;AACD;AAEA,MAAM,cAAc,CAAC,SAA0B;AAC9C,MAAI,CAAC,UAAU,KAAK,IAAI,GAAG;AAC1B,WAAO;AAAA,EACR;AACA,SAAO,CAAC,KACN,MAAM,GAAG,EACT,MAAM,CAAC,EACP,IAAI,aAAa,EACjB;AAAA,IAAK;AAAA;AAAA,EAAwB;AAChC;AAEO,MAAM,aAAa,CAAC,MAAY,MAAW,SAAS,oBAA0B;AACpF,MAAI,CAAC,YAAY,IAAI,GAAG;AACvB,UAAM,IAAI,MAAM,yBAAyB;AAAA,EAC1C;AAEA,QAAM,EAAE,KAAK,UAAU,IAAI,qBAAqB,IAAI;AACpD,QAAM,WAAW,KACf,MAAM,GAAG,EACT,MAAM,CAAC,EACP,IAAI,aAAa,EACjB,IAAI,CAAC,OAAO,SAAS,IAAI,EAAE,CAAC;AAE9B,SAAO,SAAS,OAAO,CAAC,YAAY,YAAY,QAAQ,YAAY,UAAU,MAAM,GAAG;AAAA,IACtF;AAAA,IACA;AAAA,EACD,CAAC;AACF;", "names": []}