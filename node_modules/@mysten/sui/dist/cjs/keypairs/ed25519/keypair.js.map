{"version": 3, "sources": ["../../../../src/keypairs/ed25519/keypair.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { ed25519 } from '@noble/curves/ed25519';\n\nimport {\n\tdecodeSuiPrivate<PERSON><PERSON>,\n\tencodeSuiPrivate<PERSON>ey,\n\tKeypair,\n\tPRIVATE_KEY_SIZE,\n} from '../../cryptography/keypair.js';\nimport { isValidHardenedPath, mnemonicToSeedHex } from '../../cryptography/mnemonics.js';\nimport type { SignatureScheme } from '../../cryptography/signature-scheme.js';\nimport { derivePath } from './ed25519-hd-key.js';\nimport { Ed25519PublicKey } from './publickey.js';\n\nexport const DEFAULT_ED25519_DERIVATION_PATH = \"m/44'/784'/0'/0'/0'\";\n\n/**\n * Ed25519 Keypair data. The publickey is the 32-byte public key and\n * the secretkey is 64-byte, where the first 32 bytes is the secret\n * key and the last 32 bytes is the public key.\n */\nexport interface Ed25519KeypairData {\n\tpublicKey: Uint8Array;\n\tsecretKey: Uint8Array;\n}\n\n/**\n * An Ed25519 Keypair used for signing transactions.\n */\nexport class Ed25519Keypair extends Keypair {\n\tprivate keypair: Ed25519KeypairData;\n\n\t/**\n\t * Create a new Ed25519 keypair instance.\n\t * Generate random keypair if no {@link Ed25519Keypair} is provided.\n\t *\n\t * @param keypair Ed25519 keypair\n\t */\n\tconstructor(keypair?: Ed25519KeypairData) {\n\t\tsuper();\n\t\tif (keypair) {\n\t\t\tthis.keypair = {\n\t\t\t\tpublicKey: keypair.publicKey,\n\t\t\t\tsecretKey: keypair.secretKey.slice(0, 32),\n\t\t\t};\n\t\t} else {\n\t\t\tconst privateKey = ed25519.utils.randomPrivateKey();\n\t\t\tthis.keypair = {\n\t\t\t\tpublicKey: ed25519.getPublicKey(privateKey),\n\t\t\t\tsecretKey: privateKey,\n\t\t\t};\n\t\t}\n\t}\n\n\t/**\n\t * Get the key scheme of the keypair ED25519\n\t */\n\tgetKeyScheme(): SignatureScheme {\n\t\treturn 'ED25519';\n\t}\n\n\t/**\n\t * Generate a new random Ed25519 keypair\n\t */\n\tstatic generate(): Ed25519Keypair {\n\t\tconst secretKey = ed25519.utils.randomPrivateKey();\n\t\treturn new Ed25519Keypair({\n\t\t\tpublicKey: ed25519.getPublicKey(secretKey),\n\t\t\tsecretKey,\n\t\t});\n\t}\n\n\t/**\n\t * Create a Ed25519 keypair from a raw secret key byte array, also known as seed.\n\t * This is NOT the private scalar which is result of hashing and bit clamping of\n\t * the raw secret key.\n\t *\n\t * @throws error if the provided secret key is invalid and validation is not skipped.\n\t *\n\t * @param secretKey secret key as a byte array or Bech32 secret key string\n\t * @param options: skip secret key validation\n\t */\n\tstatic fromSecretKey(\n\t\tsecretKey: Uint8Array | string,\n\t\toptions?: { skipValidation?: boolean },\n\t): Ed25519Keypair {\n\t\tif (typeof secretKey === 'string') {\n\t\t\tconst decoded = decodeSuiPrivateKey(secretKey);\n\n\t\t\tif (decoded.schema !== 'ED25519') {\n\t\t\t\tthrow new Error(`Expected a ED25519 keypair, got ${decoded.schema}`);\n\t\t\t}\n\n\t\t\treturn this.fromSecretKey(decoded.secretKey, options);\n\t\t}\n\n\t\tconst secretKeyLength = secretKey.length;\n\t\tif (secretKeyLength !== PRIVATE_KEY_SIZE) {\n\t\t\tthrow new Error(\n\t\t\t\t`Wrong secretKey size. Expected ${PRIVATE_KEY_SIZE} bytes, got ${secretKeyLength}.`,\n\t\t\t);\n\t\t}\n\t\tconst keypair = {\n\t\t\tpublicKey: ed25519.getPublicKey(secretKey),\n\t\t\tsecretKey,\n\t\t};\n\n\t\tif (!options || !options.skipValidation) {\n\t\t\tconst encoder = new TextEncoder();\n\t\t\tconst signData = encoder.encode('sui validation');\n\t\t\tconst signature = ed25519.sign(signData, secretKey);\n\t\t\tif (!ed25519.verify(signature, signData, keypair.publicKey)) {\n\t\t\t\tthrow new Error('provided secretKey is invalid');\n\t\t\t}\n\t\t}\n\t\treturn new Ed25519Keypair(keypair);\n\t}\n\n\t/**\n\t * The public key for this Ed25519 keypair\n\t */\n\tgetPublicKey(): Ed25519PublicKey {\n\t\treturn new Ed25519PublicKey(this.keypair.publicKey);\n\t}\n\n\t/**\n\t * The Bech32 secret key string for this Ed25519 keypair\n\t */\n\tgetSecretKey(): string {\n\t\treturn encodeSuiPrivateKey(\n\t\t\tthis.keypair.secretKey.slice(0, PRIVATE_KEY_SIZE),\n\t\t\tthis.getKeyScheme(),\n\t\t);\n\t}\n\n\t/**\n\t * Return the signature for the provided data using Ed25519.\n\t */\n\tasync sign(data: Uint8Array) {\n\t\treturn ed25519.sign(data, this.keypair.secretKey);\n\t}\n\n\t/**\n\t * Derive Ed25519 keypair from mnemonics and path. The mnemonics must be normalized\n\t * and validated against the english wordlist.\n\t *\n\t * If path is none, it will default to m/44'/784'/0'/0'/0', otherwise the path must\n\t * be compliant to SLIP-0010 in form m/44'/784'/{account_index}'/{change_index}'/{address_index}'.\n\t */\n\tstatic deriveKeypair(mnemonics: string, path?: string): Ed25519Keypair {\n\t\tif (path == null) {\n\t\t\tpath = DEFAULT_ED25519_DERIVATION_PATH;\n\t\t}\n\t\tif (!isValidHardenedPath(path)) {\n\t\t\tthrow new Error('Invalid derivation path');\n\t\t}\n\t\tconst { key } = derivePath(path, mnemonicToSeedHex(mnemonics));\n\n\t\treturn Ed25519Keypair.fromSecretKey(key);\n\t}\n\n\t/**\n\t * Derive Ed25519 keypair from mnemonicSeed and path.\n\t *\n\t * If path is none, it will default to m/44'/784'/0'/0'/0', otherwise the path must\n\t * be compliant to SLIP-0010 in form m/44'/784'/{account_index}'/{change_index}'/{address_index}'.\n\t */\n\tstatic deriveKeypairFromSeed(seedHex: string, path?: string): Ed25519Keypair {\n\t\tif (path == null) {\n\t\t\tpath = DEFAULT_ED25519_DERIVATION_PATH;\n\t\t}\n\t\tif (!isValidHardenedPath(path)) {\n\t\t\tthrow new Error('Invalid derivation path');\n\t\t}\n\t\tconst { key } = derivePath(path, seedHex);\n\n\t\treturn Ed25519Keypair.fromSecretKey(key);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,qBAAwB;AAExB,qBAKO;AACP,uBAAuD;AAEvD,4BAA2B;AAC3B,uBAAiC;AAE1B,MAAM,kCAAkC;AAexC,MAAM,uBAAuB,uBAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAS3C,YAAY,SAA8B;AACzC,UAAM;AACN,QAAI,SAAS;AACZ,WAAK,UAAU;AAAA,QACd,WAAW,QAAQ;AAAA,QACnB,WAAW,QAAQ,UAAU,MAAM,GAAG,EAAE;AAAA,MACzC;AAAA,IACD,OAAO;AACN,YAAM,aAAa,uBAAQ,MAAM,iBAAiB;AAClD,WAAK,UAAU;AAAA,QACd,WAAW,uBAAQ,aAAa,UAAU;AAAA,QAC1C,WAAW;AAAA,MACZ;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,eAAgC;AAC/B,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,WAA2B;AACjC,UAAM,YAAY,uBAAQ,MAAM,iBAAiB;AACjD,WAAO,IAAI,eAAe;AAAA,MACzB,WAAW,uBAAQ,aAAa,SAAS;AAAA,MACzC;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,OAAO,cACN,WACA,SACiB;AACjB,QAAI,OAAO,cAAc,UAAU;AAClC,YAAM,cAAU,oCAAoB,SAAS;AAE7C,UAAI,QAAQ,WAAW,WAAW;AACjC,cAAM,IAAI,MAAM,mCAAmC,QAAQ,MAAM,EAAE;AAAA,MACpE;AAEA,aAAO,KAAK,cAAc,QAAQ,WAAW,OAAO;AAAA,IACrD;AAEA,UAAM,kBAAkB,UAAU;AAClC,QAAI,oBAAoB,iCAAkB;AACzC,YAAM,IAAI;AAAA,QACT,kCAAkC,+BAAgB,eAAe,eAAe;AAAA,MACjF;AAAA,IACD;AACA,UAAM,UAAU;AAAA,MACf,WAAW,uBAAQ,aAAa,SAAS;AAAA,MACzC;AAAA,IACD;AAEA,QAAI,CAAC,WAAW,CAAC,QAAQ,gBAAgB;AACxC,YAAM,UAAU,IAAI,YAAY;AAChC,YAAM,WAAW,QAAQ,OAAO,gBAAgB;AAChD,YAAM,YAAY,uBAAQ,KAAK,UAAU,SAAS;AAClD,UAAI,CAAC,uBAAQ,OAAO,WAAW,UAAU,QAAQ,SAAS,GAAG;AAC5D,cAAM,IAAI,MAAM,+BAA+B;AAAA,MAChD;AAAA,IACD;AACA,WAAO,IAAI,eAAe,OAAO;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAKA,eAAiC;AAChC,WAAO,IAAI,kCAAiB,KAAK,QAAQ,SAAS;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAKA,eAAuB;AACtB,eAAO;AAAA,MACN,KAAK,QAAQ,UAAU,MAAM,GAAG,+BAAgB;AAAA,MAChD,KAAK,aAAa;AAAA,IACnB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,KAAK,MAAkB;AAC5B,WAAO,uBAAQ,KAAK,MAAM,KAAK,QAAQ,SAAS;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,cAAc,WAAmB,MAA+B;AACtE,QAAI,QAAQ,MAAM;AACjB,aAAO;AAAA,IACR;AACA,QAAI,KAAC,sCAAoB,IAAI,GAAG;AAC/B,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC1C;AACA,UAAM,EAAE,IAAI,QAAI,kCAAW,UAAM,oCAAkB,SAAS,CAAC;AAE7D,WAAO,eAAe,cAAc,GAAG;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,sBAAsB,SAAiB,MAA+B;AAC5E,QAAI,QAAQ,MAAM;AACjB,aAAO;AAAA,IACR;AACA,QAAI,KAAC,sCAAoB,IAAI,GAAG;AAC/B,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC1C;AACA,UAAM,EAAE,IAAI,QAAI,kCAAW,MAAM,OAAO;AAExC,WAAO,eAAe,cAAc,GAAG;AAAA,EACxC;AACD;", "names": []}