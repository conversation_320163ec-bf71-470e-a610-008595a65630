import { IntentMessage } from './bcs.js';
export type { TypeTag } from './types.js';
export { TypeTagSerializer } from './type-tag-serializer.js';
export { BcsType, type BcsTypeOptions } from '@mysten/bcs';
declare const suiBcs: {
    U8: import("@mysten/bcs").BcsType<number, number>;
    U16: import("@mysten/bcs").BcsType<number, number>;
    U32: import("@mysten/bcs").BcsType<number, number>;
    U64: import("@mysten/bcs").BcsType<string, string | number | bigint>;
    U128: import("@mysten/bcs").BcsType<string, string | number | bigint>;
    U256: import("@mysten/bcs").BcsType<string, string | number | bigint>;
    ULEB128: import("@mysten/bcs").BcsType<number, number>;
    Bool: import("@mysten/bcs").BcsType<boolean, boolean>;
    String: import("@mysten/bcs").BcsType<string, string>;
    Address: import("@mysten/bcs").BcsType<string, string | Uint8Array<ArrayBufferLike>>;
    AppId: import("@mysten/bcs").BcsType<{
        Sui: true;
        $kind: "Sui";
    }, {
        Sui: boolean | object | null;
    }>;
    Argument: import("@mysten/bcs").BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
        GasCoin: true;
        Input: number;
        Result: number;
        NestedResult: [number, number];
    }, "GasCoin" | "Input" | "Result" | "NestedResult">, import("@mysten/bcs").EnumInputShape<{
        GasCoin: boolean | object | null;
        Input: number;
        Result: number;
        NestedResult: readonly [number, number];
    }>>;
    CallArg: import("@mysten/bcs").BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
        Pure: {
            bytes: string;
        };
        Object: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            ImmOrOwnedObject: {
                objectId: string;
                version: string;
                digest: string;
            };
            SharedObject: {
                objectId: string;
                initialSharedVersion: string;
                mutable: boolean;
            };
            Receiving: {
                objectId: string;
                version: string;
                digest: string;
            };
        }, "ImmOrOwnedObject" | "SharedObject" | "Receiving">;
    }, "Pure" | "Object">, import("@mysten/bcs").EnumInputShape<{
        Pure: {
            bytes: string | Uint8Array<ArrayBufferLike>;
        };
        Object: import("@mysten/bcs").EnumInputShape<{
            ImmOrOwnedObject: {
                objectId: string | Uint8Array<ArrayBufferLike>;
                version: string | number | bigint;
                digest: string;
            };
            SharedObject: {
                objectId: string | Uint8Array<ArrayBufferLike>;
                initialSharedVersion: string | number | bigint;
                mutable: boolean;
            };
            Receiving: {
                objectId: string | Uint8Array<ArrayBufferLike>;
                version: string | number | bigint;
                digest: string;
            };
        }>;
    }>>;
    Command: import("@mysten/bcs").BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
        MoveCall: {
            package: string;
            module: string;
            function: string;
            typeArguments: string[];
            arguments: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
        };
        TransferObjects: {
            objects: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
            address: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">;
        };
        SplitCoins: {
            coin: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">;
            amounts: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
        };
        MergeCoins: {
            destination: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">;
            sources: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
        };
        Publish: {
            modules: string[];
            dependencies: string[];
        };
        MakeMoveVec: {
            type: string | null;
            elements: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
        };
        Upgrade: {
            modules: string[];
            dependencies: string[];
            package: string;
            ticket: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                GasCoin: true;
                Input: number;
                Result: number;
                NestedResult: [number, number];
            }, "GasCoin" | "Input" | "Result" | "NestedResult">;
        };
    }, "MoveCall" | "TransferObjects" | "SplitCoins" | "MergeCoins" | "Publish" | "MakeMoveVec" | "Upgrade">, import("@mysten/bcs").EnumInputShape<{
        MoveCall: {
            package: string | Uint8Array<ArrayBufferLike>;
            module: string;
            function: string;
            typeArguments: Iterable<string | import("./types.js").TypeTag> & {
                length: number;
            };
            arguments: Iterable<import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>> & {
                length: number;
            };
        };
        TransferObjects: {
            objects: Iterable<import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>> & {
                length: number;
            };
            address: import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>;
        };
        SplitCoins: {
            coin: import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>;
            amounts: Iterable<import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>> & {
                length: number;
            };
        };
        MergeCoins: {
            destination: import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>;
            sources: Iterable<import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>> & {
                length: number;
            };
        };
        Publish: {
            modules: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                length: number;
            };
            dependencies: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                length: number;
            };
        };
        MakeMoveVec: {
            type: string | null;
            elements: Iterable<import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>> & {
                length: number;
            };
        };
        Upgrade: {
            modules: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                length: number;
            };
            dependencies: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                length: number;
            };
            package: string | Uint8Array<ArrayBufferLike>;
            ticket: import("@mysten/bcs").EnumInputShape<{
                GasCoin: boolean | object | null;
                Input: number;
                Result: number;
                NestedResult: readonly [number, number];
            }>;
        };
    }>>;
    CompressedSignature: import("@mysten/bcs").BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
        ED25519: number[];
        Secp256k1: number[];
        Secp256r1: number[];
        ZkLogin: number[];
    }, "ED25519" | "Secp256k1" | "Secp256r1" | "ZkLogin">, import("@mysten/bcs").EnumInputShape<{
        ED25519: Iterable<number> & {
            length: number;
        };
        Secp256k1: Iterable<number> & {
            length: number;
        };
        Secp256r1: Iterable<number> & {
            length: number;
        };
        ZkLogin: Iterable<number> & {
            length: number;
        };
    }>>;
    GasData: import("@mysten/bcs").BcsType<{
        payment: {
            objectId: string;
            version: string;
            digest: string;
        }[];
        owner: string;
        price: string;
        budget: string;
    }, {
        payment: Iterable<{
            objectId: string | Uint8Array<ArrayBufferLike>;
            version: string | number | bigint;
            digest: string;
        }> & {
            length: number;
        };
        owner: string | Uint8Array<ArrayBufferLike>;
        price: string | number | bigint;
        budget: string | number | bigint;
    }>;
    Intent: import("@mysten/bcs").BcsType<{
        scope: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            TransactionData: true;
            TransactionEffects: true;
            CheckpointSummary: true;
            PersonalMessage: true;
        }, "TransactionData" | "TransactionEffects" | "CheckpointSummary" | "PersonalMessage">;
        version: {
            V0: true;
            $kind: "V0";
        };
        appId: {
            Sui: true;
            $kind: "Sui";
        };
    }, {
        scope: import("@mysten/bcs").EnumInputShape<{
            TransactionData: boolean | object | null;
            TransactionEffects: boolean | object | null;
            CheckpointSummary: boolean | object | null;
            PersonalMessage: boolean | object | null;
        }>;
        version: {
            V0: boolean | object | null;
        };
        appId: {
            Sui: boolean | object | null;
        };
    }>;
    IntentMessage: typeof IntentMessage;
    IntentScope: import("@mysten/bcs").BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
        TransactionData: true;
        TransactionEffects: true;
        CheckpointSummary: true;
        PersonalMessage: true;
    }, "TransactionData" | "TransactionEffects" | "CheckpointSummary" | "PersonalMessage">, import("@mysten/bcs").EnumInputShape<{
        TransactionData: boolean | object | null;
        TransactionEffects: boolean | object | null;
        CheckpointSummary: boolean | object | null;
        PersonalMessage: boolean | object | null;
    }>>;
    IntentVersion: import("@mysten/bcs").BcsType<{
        V0: true;
        $kind: "V0";
    }, {
        V0: boolean | object | null;
    }>;
    MultiSig: import("@mysten/bcs").BcsType<{
        sigs: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            ED25519: number[];
            Secp256k1: number[];
            Secp256r1: number[];
            ZkLogin: number[];
        }, "ED25519" | "Secp256k1" | "Secp256r1" | "ZkLogin">[];
        bitmap: number;
        multisig_pk: {
            pk_map: {
                pubKey: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    ED25519: number[];
                    Secp256k1: number[];
                    Secp256r1: number[];
                    ZkLogin: number[];
                }, "ED25519" | "Secp256k1" | "Secp256r1" | "ZkLogin">;
                weight: number;
            }[];
            threshold: number;
        };
    }, {
        sigs: Iterable<import("@mysten/bcs").EnumInputShape<{
            ED25519: Iterable<number> & {
                length: number;
            };
            Secp256k1: Iterable<number> & {
                length: number;
            };
            Secp256r1: Iterable<number> & {
                length: number;
            };
            ZkLogin: Iterable<number> & {
                length: number;
            };
        }>> & {
            length: number;
        };
        bitmap: number;
        multisig_pk: {
            pk_map: Iterable<{
                pubKey: import("@mysten/bcs").EnumInputShape<{
                    ED25519: Iterable<number> & {
                        length: number;
                    };
                    Secp256k1: Iterable<number> & {
                        length: number;
                    };
                    Secp256r1: Iterable<number> & {
                        length: number;
                    };
                    ZkLogin: Iterable<number> & {
                        length: number;
                    };
                }>;
                weight: number;
            }> & {
                length: number;
            };
            threshold: number;
        };
    }>;
    MultiSigPkMap: import("@mysten/bcs").BcsType<{
        pubKey: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            ED25519: number[];
            Secp256k1: number[];
            Secp256r1: number[];
            ZkLogin: number[];
        }, "ED25519" | "Secp256k1" | "Secp256r1" | "ZkLogin">;
        weight: number;
    }, {
        pubKey: import("@mysten/bcs").EnumInputShape<{
            ED25519: Iterable<number> & {
                length: number;
            };
            Secp256k1: Iterable<number> & {
                length: number;
            };
            Secp256r1: Iterable<number> & {
                length: number;
            };
            ZkLogin: Iterable<number> & {
                length: number;
            };
        }>;
        weight: number;
    }>;
    MultiSigPublicKey: import("@mysten/bcs").BcsType<{
        pk_map: {
            pubKey: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                ED25519: number[];
                Secp256k1: number[];
                Secp256r1: number[];
                ZkLogin: number[];
            }, "ED25519" | "Secp256k1" | "Secp256r1" | "ZkLogin">;
            weight: number;
        }[];
        threshold: number;
    }, {
        pk_map: Iterable<{
            pubKey: import("@mysten/bcs").EnumInputShape<{
                ED25519: Iterable<number> & {
                    length: number;
                };
                Secp256k1: Iterable<number> & {
                    length: number;
                };
                Secp256r1: Iterable<number> & {
                    length: number;
                };
                ZkLogin: Iterable<number> & {
                    length: number;
                };
            }>;
            weight: number;
        }> & {
            length: number;
        };
        threshold: number;
    }>;
    ObjectArg: import("@mysten/bcs").BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
        ImmOrOwnedObject: {
            objectId: string;
            version: string;
            digest: string;
        };
        SharedObject: {
            objectId: string;
            initialSharedVersion: string;
            mutable: boolean;
        };
        Receiving: {
            objectId: string;
            version: string;
            digest: string;
        };
    }, "ImmOrOwnedObject" | "SharedObject" | "Receiving">, import("@mysten/bcs").EnumInputShape<{
        ImmOrOwnedObject: {
            objectId: string | Uint8Array<ArrayBufferLike>;
            version: string | number | bigint;
            digest: string;
        };
        SharedObject: {
            objectId: string | Uint8Array<ArrayBufferLike>;
            initialSharedVersion: string | number | bigint;
            mutable: boolean;
        };
        Receiving: {
            objectId: string | Uint8Array<ArrayBufferLike>;
            version: string | number | bigint;
            digest: string;
        };
    }>>;
    ObjectDigest: import("@mysten/bcs").BcsType<string, string>;
    Owner: import("@mysten/bcs").BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
        AddressOwner: string;
        ObjectOwner: string;
        Shared: {
            initialSharedVersion: string;
        };
        Immutable: true;
        ConsensusAddressOwner: {
            owner: string;
            startVersion: string;
        };
    }, "AddressOwner" | "ObjectOwner" | "Shared" | "Immutable" | "ConsensusAddressOwner">, import("@mysten/bcs").EnumInputShape<{
        AddressOwner: string | Uint8Array<ArrayBufferLike>;
        ObjectOwner: string | Uint8Array<ArrayBufferLike>;
        Shared: {
            initialSharedVersion: string | number | bigint;
        };
        Immutable: boolean | object | null;
        ConsensusAddressOwner: {
            owner: string | Uint8Array<ArrayBufferLike>;
            startVersion: string | number | bigint;
        };
    }>>;
    PasskeyAuthenticator: import("@mysten/bcs").BcsType<{
        authenticatorData: number[];
        clientDataJson: string;
        userSignature: number[];
    }, {
        authenticatorData: Iterable<number> & {
            length: number;
        };
        clientDataJson: string;
        userSignature: Iterable<number> & {
            length: number;
        };
    }>;
    ProgrammableMoveCall: import("@mysten/bcs").BcsType<{
        package: string;
        module: string;
        function: string;
        typeArguments: string[];
        arguments: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            GasCoin: true;
            Input: number;
            Result: number;
            NestedResult: [number, number];
        }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
    }, {
        package: string | Uint8Array<ArrayBufferLike>;
        module: string;
        function: string;
        typeArguments: Iterable<string | import("./types.js").TypeTag> & {
            length: number;
        };
        arguments: Iterable<import("@mysten/bcs").EnumInputShape<{
            GasCoin: boolean | object | null;
            Input: number;
            Result: number;
            NestedResult: readonly [number, number];
        }>> & {
            length: number;
        };
    }>;
    ProgrammableTransaction: import("@mysten/bcs").BcsType<{
        inputs: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            Pure: {
                bytes: string;
            };
            Object: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                ImmOrOwnedObject: {
                    objectId: string;
                    version: string;
                    digest: string;
                };
                SharedObject: {
                    objectId: string;
                    initialSharedVersion: string;
                    mutable: boolean;
                };
                Receiving: {
                    objectId: string;
                    version: string;
                    digest: string;
                };
            }, "ImmOrOwnedObject" | "SharedObject" | "Receiving">;
        }, "Pure" | "Object">[];
        commands: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            MoveCall: {
                package: string;
                module: string;
                function: string;
                typeArguments: string[];
                arguments: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
            };
            TransferObjects: {
                objects: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                address: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">;
            };
            SplitCoins: {
                coin: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                amounts: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
            };
            MergeCoins: {
                destination: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                sources: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
            };
            Publish: {
                modules: string[];
                dependencies: string[];
            };
            MakeMoveVec: {
                type: string | null;
                elements: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
            };
            Upgrade: {
                modules: string[];
                dependencies: string[];
                package: string;
                ticket: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    GasCoin: true;
                    Input: number;
                    Result: number;
                    NestedResult: [number, number];
                }, "GasCoin" | "Input" | "Result" | "NestedResult">;
            };
        }, "MoveCall" | "TransferObjects" | "SplitCoins" | "MergeCoins" | "Publish" | "MakeMoveVec" | "Upgrade">[];
    }, {
        inputs: Iterable<import("@mysten/bcs").EnumInputShape<{
            Pure: {
                bytes: string | Uint8Array<ArrayBufferLike>;
            };
            Object: import("@mysten/bcs").EnumInputShape<{
                ImmOrOwnedObject: {
                    objectId: string | Uint8Array<ArrayBufferLike>;
                    version: string | number | bigint;
                    digest: string;
                };
                SharedObject: {
                    objectId: string | Uint8Array<ArrayBufferLike>;
                    initialSharedVersion: string | number | bigint;
                    mutable: boolean;
                };
                Receiving: {
                    objectId: string | Uint8Array<ArrayBufferLike>;
                    version: string | number | bigint;
                    digest: string;
                };
            }>;
        }>> & {
            length: number;
        };
        commands: Iterable<import("@mysten/bcs").EnumInputShape<{
            MoveCall: {
                package: string | Uint8Array<ArrayBufferLike>;
                module: string;
                function: string;
                typeArguments: Iterable<string | import("./types.js").TypeTag> & {
                    length: number;
                };
                arguments: Iterable<import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>> & {
                    length: number;
                };
            };
            TransferObjects: {
                objects: Iterable<import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>> & {
                    length: number;
                };
                address: import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>;
            };
            SplitCoins: {
                coin: import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>;
                amounts: Iterable<import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>> & {
                    length: number;
                };
            };
            MergeCoins: {
                destination: import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>;
                sources: Iterable<import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>> & {
                    length: number;
                };
            };
            Publish: {
                modules: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                    length: number;
                };
                dependencies: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                    length: number;
                };
            };
            MakeMoveVec: {
                type: string | null;
                elements: Iterable<import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>> & {
                    length: number;
                };
            };
            Upgrade: {
                modules: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                    length: number;
                };
                dependencies: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                    length: number;
                };
                package: string | Uint8Array<ArrayBufferLike>;
                ticket: import("@mysten/bcs").EnumInputShape<{
                    GasCoin: boolean | object | null;
                    Input: number;
                    Result: number;
                    NestedResult: readonly [number, number];
                }>;
            };
        }>> & {
            length: number;
        };
    }>;
    PublicKey: import("@mysten/bcs").BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
        ED25519: number[];
        Secp256k1: number[];
        Secp256r1: number[];
        ZkLogin: number[];
    }, "ED25519" | "Secp256k1" | "Secp256r1" | "ZkLogin">, import("@mysten/bcs").EnumInputShape<{
        ED25519: Iterable<number> & {
            length: number;
        };
        Secp256k1: Iterable<number> & {
            length: number;
        };
        Secp256r1: Iterable<number> & {
            length: number;
        };
        ZkLogin: Iterable<number> & {
            length: number;
        };
    }>>;
    SenderSignedData: import("@mysten/bcs").BcsType<{
        intentMessage: {
            intent: {
                scope: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    TransactionData: true;
                    TransactionEffects: true;
                    CheckpointSummary: true;
                    PersonalMessage: true;
                }, "TransactionData" | "TransactionEffects" | "CheckpointSummary" | "PersonalMessage">;
                version: {
                    V0: true;
                    $kind: "V0";
                };
                appId: {
                    Sui: true;
                    $kind: "Sui";
                };
            };
            value: {
                V1: {
                    kind: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        ProgrammableTransaction: {
                            inputs: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                Pure: {
                                    bytes: string;
                                };
                                Object: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    ImmOrOwnedObject: {
                                        objectId: string;
                                        version: string;
                                        digest: string;
                                    };
                                    SharedObject: {
                                        objectId: string;
                                        initialSharedVersion: string;
                                        mutable: boolean;
                                    };
                                    Receiving: {
                                        objectId: string;
                                        version: string;
                                        digest: string;
                                    };
                                }, "ImmOrOwnedObject" | "SharedObject" | "Receiving">;
                            }, "Pure" | "Object">[];
                            commands: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                MoveCall: {
                                    package: string;
                                    module: string;
                                    function: string;
                                    typeArguments: string[];
                                    arguments: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                                };
                                TransferObjects: {
                                    objects: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                                    address: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                                };
                                SplitCoins: {
                                    coin: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                                    amounts: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                                };
                                MergeCoins: {
                                    destination: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                                    sources: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                                };
                                Publish: {
                                    modules: string[];
                                    dependencies: string[];
                                };
                                MakeMoveVec: {
                                    type: string | null;
                                    elements: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                                };
                                Upgrade: {
                                    modules: string[];
                                    dependencies: string[];
                                    package: string;
                                    ticket: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                                };
                            }, "MoveCall" | "TransferObjects" | "SplitCoins" | "MergeCoins" | "Publish" | "MakeMoveVec" | "Upgrade">[];
                        };
                        ChangeEpoch: true;
                        Genesis: true;
                        ConsensusCommitPrologue: true;
                    }, "ProgrammableTransaction" | "ChangeEpoch" | "Genesis" | "ConsensusCommitPrologue">;
                    sender: string;
                    gasData: {
                        payment: {
                            objectId: string;
                            version: string;
                            digest: string;
                        }[];
                        owner: string;
                        price: string;
                        budget: string;
                    };
                    expiration: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        None: true;
                        Epoch: number;
                    }, "None" | "Epoch">;
                };
                $kind: "V1";
            };
        };
        txSignatures: string[];
    }[], Iterable<{
        intentMessage: {
            intent: {
                scope: import("@mysten/bcs").EnumInputShape<{
                    TransactionData: boolean | object | null;
                    TransactionEffects: boolean | object | null;
                    CheckpointSummary: boolean | object | null;
                    PersonalMessage: boolean | object | null;
                }>;
                version: {
                    V0: boolean | object | null;
                };
                appId: {
                    Sui: boolean | object | null;
                };
            };
            value: {
                V1: {
                    kind: import("@mysten/bcs").EnumInputShape<{
                        ProgrammableTransaction: {
                            inputs: Iterable<import("@mysten/bcs").EnumInputShape<{
                                Pure: {
                                    bytes: string | Uint8Array<ArrayBufferLike>;
                                };
                                Object: import("@mysten/bcs").EnumInputShape<{
                                    ImmOrOwnedObject: {
                                        objectId: string | Uint8Array<ArrayBufferLike>;
                                        version: string | number | bigint;
                                        digest: string;
                                    };
                                    SharedObject: {
                                        objectId: string | Uint8Array<ArrayBufferLike>;
                                        initialSharedVersion: string | number | bigint;
                                        mutable: boolean;
                                    };
                                    Receiving: {
                                        objectId: string | Uint8Array<ArrayBufferLike>;
                                        version: string | number | bigint;
                                        digest: string;
                                    };
                                }>;
                            }>> & {
                                length: number;
                            };
                            commands: Iterable<import("@mysten/bcs").EnumInputShape<{
                                MoveCall: {
                                    package: string | Uint8Array<ArrayBufferLike>;
                                    module: string;
                                    function: string;
                                    typeArguments: Iterable<string | import("./types.js").TypeTag> & {
                                        length: number;
                                    };
                                    arguments: Iterable<import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>> & {
                                        length: number;
                                    };
                                };
                                TransferObjects: {
                                    objects: Iterable<import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>> & {
                                        length: number;
                                    };
                                    address: import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>;
                                };
                                SplitCoins: {
                                    coin: import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>;
                                    amounts: Iterable<import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>> & {
                                        length: number;
                                    };
                                };
                                MergeCoins: {
                                    destination: import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>;
                                    sources: Iterable<import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>> & {
                                        length: number;
                                    };
                                };
                                Publish: {
                                    modules: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                                        length: number;
                                    };
                                    dependencies: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                                        length: number;
                                    };
                                };
                                MakeMoveVec: {
                                    type: string | null;
                                    elements: Iterable<import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>> & {
                                        length: number;
                                    };
                                };
                                Upgrade: {
                                    modules: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                                        length: number;
                                    };
                                    dependencies: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                                        length: number;
                                    };
                                    package: string | Uint8Array<ArrayBufferLike>;
                                    ticket: import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>;
                                };
                            }>> & {
                                length: number;
                            };
                        };
                        ChangeEpoch: boolean | object | null;
                        Genesis: boolean | object | null;
                        ConsensusCommitPrologue: boolean | object | null;
                    }>;
                    sender: string | Uint8Array<ArrayBufferLike>;
                    gasData: {
                        payment: Iterable<{
                            objectId: string | Uint8Array<ArrayBufferLike>;
                            version: string | number | bigint;
                            digest: string;
                        }> & {
                            length: number;
                        };
                        owner: string | Uint8Array<ArrayBufferLike>;
                        price: string | number | bigint;
                        budget: string | number | bigint;
                    };
                    expiration: import("@mysten/bcs").EnumInputShape<{
                        None: boolean | object | null;
                        Epoch: string | number;
                    }>;
                };
            };
        };
        txSignatures: Iterable<string | Uint8Array<ArrayBufferLike>> & {
            length: number;
        };
    }> & {
        length: number;
    }>;
    SenderSignedTransaction: import("@mysten/bcs").BcsType<{
        intentMessage: {
            intent: {
                scope: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    TransactionData: true;
                    TransactionEffects: true;
                    CheckpointSummary: true;
                    PersonalMessage: true;
                }, "TransactionData" | "TransactionEffects" | "CheckpointSummary" | "PersonalMessage">;
                version: {
                    V0: true;
                    $kind: "V0";
                };
                appId: {
                    Sui: true;
                    $kind: "Sui";
                };
            };
            value: {
                V1: {
                    kind: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        ProgrammableTransaction: {
                            inputs: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                Pure: {
                                    bytes: string;
                                };
                                Object: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                    ImmOrOwnedObject: {
                                        objectId: string;
                                        version: string;
                                        digest: string;
                                    };
                                    SharedObject: {
                                        objectId: string;
                                        initialSharedVersion: string;
                                        mutable: boolean;
                                    };
                                    Receiving: {
                                        objectId: string;
                                        version: string;
                                        digest: string;
                                    };
                                }, "ImmOrOwnedObject" | "SharedObject" | "Receiving">;
                            }, "Pure" | "Object">[];
                            commands: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                MoveCall: {
                                    package: string;
                                    module: string;
                                    function: string;
                                    typeArguments: string[];
                                    arguments: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                                };
                                TransferObjects: {
                                    objects: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                                    address: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                                };
                                SplitCoins: {
                                    coin: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                                    amounts: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                                };
                                MergeCoins: {
                                    destination: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                                    sources: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                                };
                                Publish: {
                                    modules: string[];
                                    dependencies: string[];
                                };
                                MakeMoveVec: {
                                    type: string | null;
                                    elements: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                                };
                                Upgrade: {
                                    modules: string[];
                                    dependencies: string[];
                                    package: string;
                                    ticket: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                        GasCoin: true;
                                        Input: number;
                                        Result: number;
                                        NestedResult: [number, number];
                                    }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                                };
                            }, "MoveCall" | "TransferObjects" | "SplitCoins" | "MergeCoins" | "Publish" | "MakeMoveVec" | "Upgrade">[];
                        };
                        ChangeEpoch: true;
                        Genesis: true;
                        ConsensusCommitPrologue: true;
                    }, "ProgrammableTransaction" | "ChangeEpoch" | "Genesis" | "ConsensusCommitPrologue">;
                    sender: string;
                    gasData: {
                        payment: {
                            objectId: string;
                            version: string;
                            digest: string;
                        }[];
                        owner: string;
                        price: string;
                        budget: string;
                    };
                    expiration: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        None: true;
                        Epoch: number;
                    }, "None" | "Epoch">;
                };
                $kind: "V1";
            };
        };
        txSignatures: string[];
    }, {
        intentMessage: {
            intent: {
                scope: import("@mysten/bcs").EnumInputShape<{
                    TransactionData: boolean | object | null;
                    TransactionEffects: boolean | object | null;
                    CheckpointSummary: boolean | object | null;
                    PersonalMessage: boolean | object | null;
                }>;
                version: {
                    V0: boolean | object | null;
                };
                appId: {
                    Sui: boolean | object | null;
                };
            };
            value: {
                V1: {
                    kind: import("@mysten/bcs").EnumInputShape<{
                        ProgrammableTransaction: {
                            inputs: Iterable<import("@mysten/bcs").EnumInputShape<{
                                Pure: {
                                    bytes: string | Uint8Array<ArrayBufferLike>;
                                };
                                Object: import("@mysten/bcs").EnumInputShape<{
                                    ImmOrOwnedObject: {
                                        objectId: string | Uint8Array<ArrayBufferLike>;
                                        version: string | number | bigint;
                                        digest: string;
                                    };
                                    SharedObject: {
                                        objectId: string | Uint8Array<ArrayBufferLike>;
                                        initialSharedVersion: string | number | bigint;
                                        mutable: boolean;
                                    };
                                    Receiving: {
                                        objectId: string | Uint8Array<ArrayBufferLike>;
                                        version: string | number | bigint;
                                        digest: string;
                                    };
                                }>;
                            }>> & {
                                length: number;
                            };
                            commands: Iterable<import("@mysten/bcs").EnumInputShape<{
                                MoveCall: {
                                    package: string | Uint8Array<ArrayBufferLike>;
                                    module: string;
                                    function: string;
                                    typeArguments: Iterable<string | import("./types.js").TypeTag> & {
                                        length: number;
                                    };
                                    arguments: Iterable<import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>> & {
                                        length: number;
                                    };
                                };
                                TransferObjects: {
                                    objects: Iterable<import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>> & {
                                        length: number;
                                    };
                                    address: import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>;
                                };
                                SplitCoins: {
                                    coin: import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>;
                                    amounts: Iterable<import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>> & {
                                        length: number;
                                    };
                                };
                                MergeCoins: {
                                    destination: import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>;
                                    sources: Iterable<import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>> & {
                                        length: number;
                                    };
                                };
                                Publish: {
                                    modules: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                                        length: number;
                                    };
                                    dependencies: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                                        length: number;
                                    };
                                };
                                MakeMoveVec: {
                                    type: string | null;
                                    elements: Iterable<import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>> & {
                                        length: number;
                                    };
                                };
                                Upgrade: {
                                    modules: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                                        length: number;
                                    };
                                    dependencies: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                                        length: number;
                                    };
                                    package: string | Uint8Array<ArrayBufferLike>;
                                    ticket: import("@mysten/bcs").EnumInputShape<{
                                        GasCoin: boolean | object | null;
                                        Input: number;
                                        Result: number;
                                        NestedResult: readonly [number, number];
                                    }>;
                                };
                            }>> & {
                                length: number;
                            };
                        };
                        ChangeEpoch: boolean | object | null;
                        Genesis: boolean | object | null;
                        ConsensusCommitPrologue: boolean | object | null;
                    }>;
                    sender: string | Uint8Array<ArrayBufferLike>;
                    gasData: {
                        payment: Iterable<{
                            objectId: string | Uint8Array<ArrayBufferLike>;
                            version: string | number | bigint;
                            digest: string;
                        }> & {
                            length: number;
                        };
                        owner: string | Uint8Array<ArrayBufferLike>;
                        price: string | number | bigint;
                        budget: string | number | bigint;
                    };
                    expiration: import("@mysten/bcs").EnumInputShape<{
                        None: boolean | object | null;
                        Epoch: string | number;
                    }>;
                };
            };
        };
        txSignatures: Iterable<string | Uint8Array<ArrayBufferLike>> & {
            length: number;
        };
    }>;
    SharedObjectRef: import("@mysten/bcs").BcsType<{
        objectId: string;
        initialSharedVersion: string;
        mutable: boolean;
    }, {
        objectId: string | Uint8Array<ArrayBufferLike>;
        initialSharedVersion: string | number | bigint;
        mutable: boolean;
    }>;
    StructTag: import("@mysten/bcs").BcsType<{
        address: string;
        module: string;
        name: string;
        typeParams: import("./types.js").TypeTag[];
    }, {
        address: string | Uint8Array<ArrayBufferLike>;
        module: string;
        name: string;
        typeParams: Iterable<import("./types.js").TypeTag> & {
            length: number;
        };
    }>;
    SuiObjectRef: import("@mysten/bcs").BcsType<{
        objectId: string;
        version: string;
        digest: string;
    }, {
        objectId: string | Uint8Array<ArrayBufferLike>;
        version: string | number | bigint;
        digest: string;
    }>;
    TransactionData: import("@mysten/bcs").BcsType<{
        V1: {
            kind: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                ProgrammableTransaction: {
                    inputs: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        Pure: {
                            bytes: string;
                        };
                        Object: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            ImmOrOwnedObject: {
                                objectId: string;
                                version: string;
                                digest: string;
                            };
                            SharedObject: {
                                objectId: string;
                                initialSharedVersion: string;
                                mutable: boolean;
                            };
                            Receiving: {
                                objectId: string;
                                version: string;
                                digest: string;
                            };
                        }, "ImmOrOwnedObject" | "SharedObject" | "Receiving">;
                    }, "Pure" | "Object">[];
                    commands: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        MoveCall: {
                            package: string;
                            module: string;
                            function: string;
                            typeArguments: string[];
                            arguments: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                GasCoin: true;
                                Input: number;
                                Result: number;
                                NestedResult: [number, number];
                            }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                        };
                        TransferObjects: {
                            objects: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                GasCoin: true;
                                Input: number;
                                Result: number;
                                NestedResult: [number, number];
                            }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                            address: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                GasCoin: true;
                                Input: number;
                                Result: number;
                                NestedResult: [number, number];
                            }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                        };
                        SplitCoins: {
                            coin: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                GasCoin: true;
                                Input: number;
                                Result: number;
                                NestedResult: [number, number];
                            }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                            amounts: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                GasCoin: true;
                                Input: number;
                                Result: number;
                                NestedResult: [number, number];
                            }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                        };
                        MergeCoins: {
                            destination: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                GasCoin: true;
                                Input: number;
                                Result: number;
                                NestedResult: [number, number];
                            }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                            sources: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                GasCoin: true;
                                Input: number;
                                Result: number;
                                NestedResult: [number, number];
                            }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                        };
                        Publish: {
                            modules: string[];
                            dependencies: string[];
                        };
                        MakeMoveVec: {
                            type: string | null;
                            elements: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                GasCoin: true;
                                Input: number;
                                Result: number;
                                NestedResult: [number, number];
                            }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                        };
                        Upgrade: {
                            modules: string[];
                            dependencies: string[];
                            package: string;
                            ticket: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                GasCoin: true;
                                Input: number;
                                Result: number;
                                NestedResult: [number, number];
                            }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                        };
                    }, "MoveCall" | "TransferObjects" | "SplitCoins" | "MergeCoins" | "Publish" | "MakeMoveVec" | "Upgrade">[];
                };
                ChangeEpoch: true;
                Genesis: true;
                ConsensusCommitPrologue: true;
            }, "ProgrammableTransaction" | "ChangeEpoch" | "Genesis" | "ConsensusCommitPrologue">;
            sender: string;
            gasData: {
                payment: {
                    objectId: string;
                    version: string;
                    digest: string;
                }[];
                owner: string;
                price: string;
                budget: string;
            };
            expiration: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                None: true;
                Epoch: number;
            }, "None" | "Epoch">;
        };
        $kind: "V1";
    }, {
        V1: {
            kind: import("@mysten/bcs").EnumInputShape<{
                ProgrammableTransaction: {
                    inputs: Iterable<import("@mysten/bcs").EnumInputShape<{
                        Pure: {
                            bytes: string | Uint8Array<ArrayBufferLike>;
                        };
                        Object: import("@mysten/bcs").EnumInputShape<{
                            ImmOrOwnedObject: {
                                objectId: string | Uint8Array<ArrayBufferLike>;
                                version: string | number | bigint;
                                digest: string;
                            };
                            SharedObject: {
                                objectId: string | Uint8Array<ArrayBufferLike>;
                                initialSharedVersion: string | number | bigint;
                                mutable: boolean;
                            };
                            Receiving: {
                                objectId: string | Uint8Array<ArrayBufferLike>;
                                version: string | number | bigint;
                                digest: string;
                            };
                        }>;
                    }>> & {
                        length: number;
                    };
                    commands: Iterable<import("@mysten/bcs").EnumInputShape<{
                        MoveCall: {
                            package: string | Uint8Array<ArrayBufferLike>;
                            module: string;
                            function: string;
                            typeArguments: Iterable<string | import("./types.js").TypeTag> & {
                                length: number;
                            };
                            arguments: Iterable<import("@mysten/bcs").EnumInputShape<{
                                GasCoin: boolean | object | null;
                                Input: number;
                                Result: number;
                                NestedResult: readonly [number, number];
                            }>> & {
                                length: number;
                            };
                        };
                        TransferObjects: {
                            objects: Iterable<import("@mysten/bcs").EnumInputShape<{
                                GasCoin: boolean | object | null;
                                Input: number;
                                Result: number;
                                NestedResult: readonly [number, number];
                            }>> & {
                                length: number;
                            };
                            address: import("@mysten/bcs").EnumInputShape<{
                                GasCoin: boolean | object | null;
                                Input: number;
                                Result: number;
                                NestedResult: readonly [number, number];
                            }>;
                        };
                        SplitCoins: {
                            coin: import("@mysten/bcs").EnumInputShape<{
                                GasCoin: boolean | object | null;
                                Input: number;
                                Result: number;
                                NestedResult: readonly [number, number];
                            }>;
                            amounts: Iterable<import("@mysten/bcs").EnumInputShape<{
                                GasCoin: boolean | object | null;
                                Input: number;
                                Result: number;
                                NestedResult: readonly [number, number];
                            }>> & {
                                length: number;
                            };
                        };
                        MergeCoins: {
                            destination: import("@mysten/bcs").EnumInputShape<{
                                GasCoin: boolean | object | null;
                                Input: number;
                                Result: number;
                                NestedResult: readonly [number, number];
                            }>;
                            sources: Iterable<import("@mysten/bcs").EnumInputShape<{
                                GasCoin: boolean | object | null;
                                Input: number;
                                Result: number;
                                NestedResult: readonly [number, number];
                            }>> & {
                                length: number;
                            };
                        };
                        Publish: {
                            modules: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                                length: number;
                            };
                            dependencies: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                                length: number;
                            };
                        };
                        MakeMoveVec: {
                            type: string | null;
                            elements: Iterable<import("@mysten/bcs").EnumInputShape<{
                                GasCoin: boolean | object | null;
                                Input: number;
                                Result: number;
                                NestedResult: readonly [number, number];
                            }>> & {
                                length: number;
                            };
                        };
                        Upgrade: {
                            modules: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                                length: number;
                            };
                            dependencies: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                                length: number;
                            };
                            package: string | Uint8Array<ArrayBufferLike>;
                            ticket: import("@mysten/bcs").EnumInputShape<{
                                GasCoin: boolean | object | null;
                                Input: number;
                                Result: number;
                                NestedResult: readonly [number, number];
                            }>;
                        };
                    }>> & {
                        length: number;
                    };
                };
                ChangeEpoch: boolean | object | null;
                Genesis: boolean | object | null;
                ConsensusCommitPrologue: boolean | object | null;
            }>;
            sender: string | Uint8Array<ArrayBufferLike>;
            gasData: {
                payment: Iterable<{
                    objectId: string | Uint8Array<ArrayBufferLike>;
                    version: string | number | bigint;
                    digest: string;
                }> & {
                    length: number;
                };
                owner: string | Uint8Array<ArrayBufferLike>;
                price: string | number | bigint;
                budget: string | number | bigint;
            };
            expiration: import("@mysten/bcs").EnumInputShape<{
                None: boolean | object | null;
                Epoch: string | number;
            }>;
        };
    }>;
    TransactionDataV1: import("@mysten/bcs").BcsType<{
        kind: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            ProgrammableTransaction: {
                inputs: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    Pure: {
                        bytes: string;
                    };
                    Object: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        ImmOrOwnedObject: {
                            objectId: string;
                            version: string;
                            digest: string;
                        };
                        SharedObject: {
                            objectId: string;
                            initialSharedVersion: string;
                            mutable: boolean;
                        };
                        Receiving: {
                            objectId: string;
                            version: string;
                            digest: string;
                        };
                    }, "ImmOrOwnedObject" | "SharedObject" | "Receiving">;
                }, "Pure" | "Object">[];
                commands: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    MoveCall: {
                        package: string;
                        module: string;
                        function: string;
                        typeArguments: string[];
                        arguments: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                    };
                    TransferObjects: {
                        objects: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                        address: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                    };
                    SplitCoins: {
                        coin: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                        amounts: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                    };
                    MergeCoins: {
                        destination: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                        sources: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                    };
                    Publish: {
                        modules: string[];
                        dependencies: string[];
                    };
                    MakeMoveVec: {
                        type: string | null;
                        elements: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                    };
                    Upgrade: {
                        modules: string[];
                        dependencies: string[];
                        package: string;
                        ticket: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            GasCoin: true;
                            Input: number;
                            Result: number;
                            NestedResult: [number, number];
                        }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                    };
                }, "MoveCall" | "TransferObjects" | "SplitCoins" | "MergeCoins" | "Publish" | "MakeMoveVec" | "Upgrade">[];
            };
            ChangeEpoch: true;
            Genesis: true;
            ConsensusCommitPrologue: true;
        }, "ProgrammableTransaction" | "ChangeEpoch" | "Genesis" | "ConsensusCommitPrologue">;
        sender: string;
        gasData: {
            payment: {
                objectId: string;
                version: string;
                digest: string;
            }[];
            owner: string;
            price: string;
            budget: string;
        };
        expiration: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            None: true;
            Epoch: number;
        }, "None" | "Epoch">;
    }, {
        kind: import("@mysten/bcs").EnumInputShape<{
            ProgrammableTransaction: {
                inputs: Iterable<import("@mysten/bcs").EnumInputShape<{
                    Pure: {
                        bytes: string | Uint8Array<ArrayBufferLike>;
                    };
                    Object: import("@mysten/bcs").EnumInputShape<{
                        ImmOrOwnedObject: {
                            objectId: string | Uint8Array<ArrayBufferLike>;
                            version: string | number | bigint;
                            digest: string;
                        };
                        SharedObject: {
                            objectId: string | Uint8Array<ArrayBufferLike>;
                            initialSharedVersion: string | number | bigint;
                            mutable: boolean;
                        };
                        Receiving: {
                            objectId: string | Uint8Array<ArrayBufferLike>;
                            version: string | number | bigint;
                            digest: string;
                        };
                    }>;
                }>> & {
                    length: number;
                };
                commands: Iterable<import("@mysten/bcs").EnumInputShape<{
                    MoveCall: {
                        package: string | Uint8Array<ArrayBufferLike>;
                        module: string;
                        function: string;
                        typeArguments: Iterable<string | import("./types.js").TypeTag> & {
                            length: number;
                        };
                        arguments: Iterable<import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>> & {
                            length: number;
                        };
                    };
                    TransferObjects: {
                        objects: Iterable<import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>> & {
                            length: number;
                        };
                        address: import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>;
                    };
                    SplitCoins: {
                        coin: import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>;
                        amounts: Iterable<import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>> & {
                            length: number;
                        };
                    };
                    MergeCoins: {
                        destination: import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>;
                        sources: Iterable<import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>> & {
                            length: number;
                        };
                    };
                    Publish: {
                        modules: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                            length: number;
                        };
                        dependencies: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                            length: number;
                        };
                    };
                    MakeMoveVec: {
                        type: string | null;
                        elements: Iterable<import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>> & {
                            length: number;
                        };
                    };
                    Upgrade: {
                        modules: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                            length: number;
                        };
                        dependencies: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                            length: number;
                        };
                        package: string | Uint8Array<ArrayBufferLike>;
                        ticket: import("@mysten/bcs").EnumInputShape<{
                            GasCoin: boolean | object | null;
                            Input: number;
                            Result: number;
                            NestedResult: readonly [number, number];
                        }>;
                    };
                }>> & {
                    length: number;
                };
            };
            ChangeEpoch: boolean | object | null;
            Genesis: boolean | object | null;
            ConsensusCommitPrologue: boolean | object | null;
        }>;
        sender: string | Uint8Array<ArrayBufferLike>;
        gasData: {
            payment: Iterable<{
                objectId: string | Uint8Array<ArrayBufferLike>;
                version: string | number | bigint;
                digest: string;
            }> & {
                length: number;
            };
            owner: string | Uint8Array<ArrayBufferLike>;
            price: string | number | bigint;
            budget: string | number | bigint;
        };
        expiration: import("@mysten/bcs").EnumInputShape<{
            None: boolean | object | null;
            Epoch: string | number;
        }>;
    }>;
    TransactionEffects: import("@mysten/bcs").BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
        V1: {
            status: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                Success: true;
                Failed: {
                    error: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        InsufficientGas: true;
                        InvalidGasObject: true;
                        InvariantViolation: true;
                        FeatureNotYetSupported: true;
                        MoveObjectTooBig: {
                            objectSize: string;
                            maxObjectSize: string;
                        };
                        MovePackageTooBig: {
                            objectSize: string;
                            maxObjectSize: string;
                        };
                        CircularObjectOwnership: {
                            object: string;
                        };
                        InsufficientCoinBalance: true;
                        CoinBalanceOverflow: true;
                        PublishErrorNonZeroAddress: true;
                        SuiMoveVerificationError: true;
                        MovePrimitiveRuntimeError: {
                            module: {
                                address: string;
                                name: string;
                            };
                            function: number;
                            instruction: number;
                            functionName: string | null;
                        } | null;
                        MoveAbort: [{
                            module: {
                                address: string;
                                name: string;
                            };
                            function: number;
                            instruction: number;
                            functionName: string | null;
                        }, string];
                        VMVerificationOrDeserializationError: true;
                        VMInvariantViolation: true;
                        FunctionNotFound: true;
                        ArityMismatch: true;
                        TypeArityMismatch: true;
                        NonEntryFunctionInvoked: true;
                        CommandArgumentError: {
                            argIdx: number;
                            kind: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                TypeMismatch: true;
                                InvalidBCSBytes: true;
                                InvalidUsageOfPureArg: true;
                                InvalidArgumentToPrivateEntryFunction: true;
                                IndexOutOfBounds: {
                                    idx: number;
                                };
                                SecondaryIndexOutOfBounds: {
                                    resultIdx: number;
                                    secondaryIdx: number;
                                };
                                InvalidResultArity: {
                                    resultIdx: number;
                                };
                                InvalidGasCoinUsage: true;
                                InvalidValueUsage: true;
                                InvalidObjectByValue: true;
                                InvalidObjectByMutRef: true;
                                SharedObjectOperationNotAllowed: true;
                            }, "TypeMismatch" | "InvalidBCSBytes" | "InvalidUsageOfPureArg" | "InvalidArgumentToPrivateEntryFunction" | "IndexOutOfBounds" | "SecondaryIndexOutOfBounds" | "InvalidResultArity" | "InvalidGasCoinUsage" | "InvalidValueUsage" | "InvalidObjectByValue" | "InvalidObjectByMutRef" | "SharedObjectOperationNotAllowed">;
                        };
                        TypeArgumentError: {
                            argumentIdx: number;
                            kind: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                TypeNotFound: true;
                                ConstraintNotSatisfied: true;
                            }, "TypeNotFound" | "ConstraintNotSatisfied">;
                        };
                        UnusedValueWithoutDrop: {
                            resultIdx: number;
                            secondaryIdx: number;
                        };
                        InvalidPublicFunctionReturnType: {
                            idx: number;
                        };
                        InvalidTransferObject: true;
                        EffectsTooLarge: {
                            currentSize: string;
                            maxSize: string;
                        };
                        PublishUpgradeMissingDependency: true;
                        PublishUpgradeDependencyDowngrade: true;
                        PackageUpgradeError: {
                            upgradeError: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                UnableToFetchPackage: {
                                    packageId: string;
                                };
                                NotAPackage: {
                                    objectId: string;
                                };
                                IncompatibleUpgrade: true;
                                DigestDoesNotMatch: {
                                    digest: number[];
                                };
                                UnknownUpgradePolicy: {
                                    policy: number;
                                };
                                PackageIDDoesNotMatch: {
                                    packageId: string;
                                    ticketId: string;
                                };
                            }, "UnableToFetchPackage" | "NotAPackage" | "IncompatibleUpgrade" | "DigestDoesNotMatch" | "UnknownUpgradePolicy" | "PackageIDDoesNotMatch">;
                        };
                        WrittenObjectsTooLarge: {
                            currentSize: string;
                            maxSize: string;
                        };
                        CertificateDenied: true;
                        SuiMoveVerificationTimedout: true;
                        SharedObjectOperationNotAllowed: true;
                        InputObjectDeleted: true;
                        ExecutionCancelledDueToSharedObjectCongestion: {
                            congestedObjects: string[];
                        };
                        AddressDeniedForCoin: {
                            address: string;
                            coinType: string;
                        };
                        CoinTypeGlobalPause: {
                            coinType: string;
                        };
                        ExecutionCancelledDueToRandomnessUnavailable: true;
                    }, "PackageUpgradeError" | "SharedObjectOperationNotAllowed" | "CommandArgumentError" | "TypeArgumentError" | "InsufficientGas" | "InvalidGasObject" | "InvariantViolation" | "FeatureNotYetSupported" | "MoveObjectTooBig" | "MovePackageTooBig" | "CircularObjectOwnership" | "InsufficientCoinBalance" | "CoinBalanceOverflow" | "PublishErrorNonZeroAddress" | "SuiMoveVerificationError" | "MovePrimitiveRuntimeError" | "MoveAbort" | "VMVerificationOrDeserializationError" | "VMInvariantViolation" | "FunctionNotFound" | "ArityMismatch" | "TypeArityMismatch" | "NonEntryFunctionInvoked" | "UnusedValueWithoutDrop" | "InvalidPublicFunctionReturnType" | "InvalidTransferObject" | "EffectsTooLarge" | "PublishUpgradeMissingDependency" | "PublishUpgradeDependencyDowngrade" | "WrittenObjectsTooLarge" | "CertificateDenied" | "SuiMoveVerificationTimedout" | "InputObjectDeleted" | "ExecutionCancelledDueToSharedObjectCongestion" | "AddressDeniedForCoin" | "CoinTypeGlobalPause" | "ExecutionCancelledDueToRandomnessUnavailable">;
                    command: string | null;
                };
            }, "Success" | "Failed">;
            executedEpoch: string;
            gasUsed: {
                computationCost: string;
                storageCost: string;
                storageRebate: string;
                nonRefundableStorageFee: string;
            };
            modifiedAtVersions: [string, string][];
            sharedObjects: {
                objectId: string;
                version: string;
                digest: string;
            }[];
            transactionDigest: string;
            created: [{
                objectId: string;
                version: string;
                digest: string;
            }, import("@mysten/bcs").EnumOutputShapeWithKeys<{
                AddressOwner: string;
                ObjectOwner: string;
                Shared: {
                    initialSharedVersion: string;
                };
                Immutable: true;
                ConsensusAddressOwner: {
                    owner: string;
                    startVersion: string;
                };
            }, "AddressOwner" | "ObjectOwner" | "Shared" | "Immutable" | "ConsensusAddressOwner">][];
            mutated: [{
                objectId: string;
                version: string;
                digest: string;
            }, import("@mysten/bcs").EnumOutputShapeWithKeys<{
                AddressOwner: string;
                ObjectOwner: string;
                Shared: {
                    initialSharedVersion: string;
                };
                Immutable: true;
                ConsensusAddressOwner: {
                    owner: string;
                    startVersion: string;
                };
            }, "AddressOwner" | "ObjectOwner" | "Shared" | "Immutable" | "ConsensusAddressOwner">][];
            unwrapped: [{
                objectId: string;
                version: string;
                digest: string;
            }, import("@mysten/bcs").EnumOutputShapeWithKeys<{
                AddressOwner: string;
                ObjectOwner: string;
                Shared: {
                    initialSharedVersion: string;
                };
                Immutable: true;
                ConsensusAddressOwner: {
                    owner: string;
                    startVersion: string;
                };
            }, "AddressOwner" | "ObjectOwner" | "Shared" | "Immutable" | "ConsensusAddressOwner">][];
            deleted: {
                objectId: string;
                version: string;
                digest: string;
            }[];
            unwrappedThenDeleted: {
                objectId: string;
                version: string;
                digest: string;
            }[];
            wrapped: {
                objectId: string;
                version: string;
                digest: string;
            }[];
            gasObject: [{
                objectId: string;
                version: string;
                digest: string;
            }, import("@mysten/bcs").EnumOutputShapeWithKeys<{
                AddressOwner: string;
                ObjectOwner: string;
                Shared: {
                    initialSharedVersion: string;
                };
                Immutable: true;
                ConsensusAddressOwner: {
                    owner: string;
                    startVersion: string;
                };
            }, "AddressOwner" | "ObjectOwner" | "Shared" | "Immutable" | "ConsensusAddressOwner">];
            eventsDigest: string | null;
            dependencies: string[];
        };
        V2: {
            status: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                Success: true;
                Failed: {
                    error: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        InsufficientGas: true;
                        InvalidGasObject: true;
                        InvariantViolation: true;
                        FeatureNotYetSupported: true;
                        MoveObjectTooBig: {
                            objectSize: string;
                            maxObjectSize: string;
                        };
                        MovePackageTooBig: {
                            objectSize: string;
                            maxObjectSize: string;
                        };
                        CircularObjectOwnership: {
                            object: string;
                        };
                        InsufficientCoinBalance: true;
                        CoinBalanceOverflow: true;
                        PublishErrorNonZeroAddress: true;
                        SuiMoveVerificationError: true;
                        MovePrimitiveRuntimeError: {
                            module: {
                                address: string;
                                name: string;
                            };
                            function: number;
                            instruction: number;
                            functionName: string | null;
                        } | null;
                        MoveAbort: [{
                            module: {
                                address: string;
                                name: string;
                            };
                            function: number;
                            instruction: number;
                            functionName: string | null;
                        }, string];
                        VMVerificationOrDeserializationError: true;
                        VMInvariantViolation: true;
                        FunctionNotFound: true;
                        ArityMismatch: true;
                        TypeArityMismatch: true;
                        NonEntryFunctionInvoked: true;
                        CommandArgumentError: {
                            argIdx: number;
                            kind: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                TypeMismatch: true;
                                InvalidBCSBytes: true;
                                InvalidUsageOfPureArg: true;
                                InvalidArgumentToPrivateEntryFunction: true;
                                IndexOutOfBounds: {
                                    idx: number;
                                };
                                SecondaryIndexOutOfBounds: {
                                    resultIdx: number;
                                    secondaryIdx: number;
                                };
                                InvalidResultArity: {
                                    resultIdx: number;
                                };
                                InvalidGasCoinUsage: true;
                                InvalidValueUsage: true;
                                InvalidObjectByValue: true;
                                InvalidObjectByMutRef: true;
                                SharedObjectOperationNotAllowed: true;
                            }, "TypeMismatch" | "InvalidBCSBytes" | "InvalidUsageOfPureArg" | "InvalidArgumentToPrivateEntryFunction" | "IndexOutOfBounds" | "SecondaryIndexOutOfBounds" | "InvalidResultArity" | "InvalidGasCoinUsage" | "InvalidValueUsage" | "InvalidObjectByValue" | "InvalidObjectByMutRef" | "SharedObjectOperationNotAllowed">;
                        };
                        TypeArgumentError: {
                            argumentIdx: number;
                            kind: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                TypeNotFound: true;
                                ConstraintNotSatisfied: true;
                            }, "TypeNotFound" | "ConstraintNotSatisfied">;
                        };
                        UnusedValueWithoutDrop: {
                            resultIdx: number;
                            secondaryIdx: number;
                        };
                        InvalidPublicFunctionReturnType: {
                            idx: number;
                        };
                        InvalidTransferObject: true;
                        EffectsTooLarge: {
                            currentSize: string;
                            maxSize: string;
                        };
                        PublishUpgradeMissingDependency: true;
                        PublishUpgradeDependencyDowngrade: true;
                        PackageUpgradeError: {
                            upgradeError: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                                UnableToFetchPackage: {
                                    packageId: string;
                                };
                                NotAPackage: {
                                    objectId: string;
                                };
                                IncompatibleUpgrade: true;
                                DigestDoesNotMatch: {
                                    digest: number[];
                                };
                                UnknownUpgradePolicy: {
                                    policy: number;
                                };
                                PackageIDDoesNotMatch: {
                                    packageId: string;
                                    ticketId: string;
                                };
                            }, "UnableToFetchPackage" | "NotAPackage" | "IncompatibleUpgrade" | "DigestDoesNotMatch" | "UnknownUpgradePolicy" | "PackageIDDoesNotMatch">;
                        };
                        WrittenObjectsTooLarge: {
                            currentSize: string;
                            maxSize: string;
                        };
                        CertificateDenied: true;
                        SuiMoveVerificationTimedout: true;
                        SharedObjectOperationNotAllowed: true;
                        InputObjectDeleted: true;
                        ExecutionCancelledDueToSharedObjectCongestion: {
                            congestedObjects: string[];
                        };
                        AddressDeniedForCoin: {
                            address: string;
                            coinType: string;
                        };
                        CoinTypeGlobalPause: {
                            coinType: string;
                        };
                        ExecutionCancelledDueToRandomnessUnavailable: true;
                    }, "PackageUpgradeError" | "SharedObjectOperationNotAllowed" | "CommandArgumentError" | "TypeArgumentError" | "InsufficientGas" | "InvalidGasObject" | "InvariantViolation" | "FeatureNotYetSupported" | "MoveObjectTooBig" | "MovePackageTooBig" | "CircularObjectOwnership" | "InsufficientCoinBalance" | "CoinBalanceOverflow" | "PublishErrorNonZeroAddress" | "SuiMoveVerificationError" | "MovePrimitiveRuntimeError" | "MoveAbort" | "VMVerificationOrDeserializationError" | "VMInvariantViolation" | "FunctionNotFound" | "ArityMismatch" | "TypeArityMismatch" | "NonEntryFunctionInvoked" | "UnusedValueWithoutDrop" | "InvalidPublicFunctionReturnType" | "InvalidTransferObject" | "EffectsTooLarge" | "PublishUpgradeMissingDependency" | "PublishUpgradeDependencyDowngrade" | "WrittenObjectsTooLarge" | "CertificateDenied" | "SuiMoveVerificationTimedout" | "InputObjectDeleted" | "ExecutionCancelledDueToSharedObjectCongestion" | "AddressDeniedForCoin" | "CoinTypeGlobalPause" | "ExecutionCancelledDueToRandomnessUnavailable">;
                    command: string | null;
                };
            }, "Success" | "Failed">;
            executedEpoch: string;
            gasUsed: {
                computationCost: string;
                storageCost: string;
                storageRebate: string;
                nonRefundableStorageFee: string;
            };
            transactionDigest: string;
            gasObjectIndex: number | null;
            eventsDigest: string | null;
            dependencies: string[];
            lamportVersion: string;
            changedObjects: [string, {
                inputState: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    NotExist: true;
                    Exist: [[string, string], import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        AddressOwner: string;
                        ObjectOwner: string;
                        Shared: {
                            initialSharedVersion: string;
                        };
                        Immutable: true;
                        ConsensusAddressOwner: {
                            owner: string;
                            startVersion: string;
                        };
                    }, "AddressOwner" | "ObjectOwner" | "Shared" | "Immutable" | "ConsensusAddressOwner">];
                }, "NotExist" | "Exist">;
                outputState: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    NotExist: true;
                    ObjectWrite: [string, import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        AddressOwner: string;
                        ObjectOwner: string;
                        Shared: {
                            initialSharedVersion: string;
                        };
                        Immutable: true;
                        ConsensusAddressOwner: {
                            owner: string;
                            startVersion: string;
                        };
                    }, "AddressOwner" | "ObjectOwner" | "Shared" | "Immutable" | "ConsensusAddressOwner">];
                    PackageWrite: [string, string];
                }, "NotExist" | "ObjectWrite" | "PackageWrite">;
                idOperation: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    None: true;
                    Created: true;
                    Deleted: true;
                }, "None" | "Created" | "Deleted">;
            }][];
            unchangedSharedObjects: [string, import("@mysten/bcs").EnumOutputShapeWithKeys<{
                ReadOnlyRoot: [string, string];
                MutateDeleted: string;
                ReadDeleted: string;
                Cancelled: string;
                PerEpochConfig: true;
            }, "ReadOnlyRoot" | "MutateDeleted" | "ReadDeleted" | "Cancelled" | "PerEpochConfig">][];
            auxDataDigest: string | null;
        };
    }, "V1" | "V2">, import("@mysten/bcs").EnumInputShape<{
        V1: {
            status: import("@mysten/bcs").EnumInputShape<{
                Success: boolean | object | null;
                Failed: {
                    error: import("@mysten/bcs").EnumInputShape<{
                        InsufficientGas: boolean | object | null;
                        InvalidGasObject: boolean | object | null;
                        InvariantViolation: boolean | object | null;
                        FeatureNotYetSupported: boolean | object | null;
                        MoveObjectTooBig: {
                            objectSize: string | number | bigint;
                            maxObjectSize: string | number | bigint;
                        };
                        MovePackageTooBig: {
                            objectSize: string | number | bigint;
                            maxObjectSize: string | number | bigint;
                        };
                        CircularObjectOwnership: {
                            object: string | Uint8Array<ArrayBufferLike>;
                        };
                        InsufficientCoinBalance: boolean | object | null;
                        CoinBalanceOverflow: boolean | object | null;
                        PublishErrorNonZeroAddress: boolean | object | null;
                        SuiMoveVerificationError: boolean | object | null;
                        MovePrimitiveRuntimeError: {
                            module: {
                                address: string | Uint8Array<ArrayBufferLike>;
                                name: string;
                            };
                            function: number;
                            instruction: number;
                            functionName: string | null | undefined;
                        } | null | undefined;
                        MoveAbort: readonly [{
                            module: {
                                address: string | Uint8Array<ArrayBufferLike>;
                                name: string;
                            };
                            function: number;
                            instruction: number;
                            functionName: string | null | undefined;
                        }, string | number | bigint];
                        VMVerificationOrDeserializationError: boolean | object | null;
                        VMInvariantViolation: boolean | object | null;
                        FunctionNotFound: boolean | object | null;
                        ArityMismatch: boolean | object | null;
                        TypeArityMismatch: boolean | object | null;
                        NonEntryFunctionInvoked: boolean | object | null;
                        CommandArgumentError: {
                            argIdx: number;
                            kind: import("@mysten/bcs").EnumInputShape<{
                                TypeMismatch: boolean | object | null;
                                InvalidBCSBytes: boolean | object | null;
                                InvalidUsageOfPureArg: boolean | object | null;
                                InvalidArgumentToPrivateEntryFunction: boolean | object | null;
                                IndexOutOfBounds: {
                                    idx: number;
                                };
                                SecondaryIndexOutOfBounds: {
                                    resultIdx: number;
                                    secondaryIdx: number;
                                };
                                InvalidResultArity: {
                                    resultIdx: number;
                                };
                                InvalidGasCoinUsage: boolean | object | null;
                                InvalidValueUsage: boolean | object | null;
                                InvalidObjectByValue: boolean | object | null;
                                InvalidObjectByMutRef: boolean | object | null;
                                SharedObjectOperationNotAllowed: boolean | object | null;
                            }>;
                        };
                        TypeArgumentError: {
                            argumentIdx: number;
                            kind: import("@mysten/bcs").EnumInputShape<{
                                TypeNotFound: boolean | object | null;
                                ConstraintNotSatisfied: boolean | object | null;
                            }>;
                        };
                        UnusedValueWithoutDrop: {
                            resultIdx: number;
                            secondaryIdx: number;
                        };
                        InvalidPublicFunctionReturnType: {
                            idx: number;
                        };
                        InvalidTransferObject: boolean | object | null;
                        EffectsTooLarge: {
                            currentSize: string | number | bigint;
                            maxSize: string | number | bigint;
                        };
                        PublishUpgradeMissingDependency: boolean | object | null;
                        PublishUpgradeDependencyDowngrade: boolean | object | null;
                        PackageUpgradeError: {
                            upgradeError: import("@mysten/bcs").EnumInputShape<{
                                UnableToFetchPackage: {
                                    packageId: string | Uint8Array<ArrayBufferLike>;
                                };
                                NotAPackage: {
                                    objectId: string | Uint8Array<ArrayBufferLike>;
                                };
                                IncompatibleUpgrade: boolean | object | null;
                                DigestDoesNotMatch: {
                                    digest: Iterable<number> & {
                                        length: number;
                                    };
                                };
                                UnknownUpgradePolicy: {
                                    policy: number;
                                };
                                PackageIDDoesNotMatch: {
                                    packageId: string | Uint8Array<ArrayBufferLike>;
                                    ticketId: string | Uint8Array<ArrayBufferLike>;
                                };
                            }>;
                        };
                        WrittenObjectsTooLarge: {
                            currentSize: string | number | bigint;
                            maxSize: string | number | bigint;
                        };
                        CertificateDenied: boolean | object | null;
                        SuiMoveVerificationTimedout: boolean | object | null;
                        SharedObjectOperationNotAllowed: boolean | object | null;
                        InputObjectDeleted: boolean | object | null;
                        ExecutionCancelledDueToSharedObjectCongestion: {
                            congestedObjects: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                                length: number;
                            };
                        };
                        AddressDeniedForCoin: {
                            address: string | Uint8Array<ArrayBufferLike>;
                            coinType: string;
                        };
                        CoinTypeGlobalPause: {
                            coinType: string;
                        };
                        ExecutionCancelledDueToRandomnessUnavailable: boolean | object | null;
                    }>;
                    command: string | number | bigint | null | undefined;
                };
            }>;
            executedEpoch: string | number | bigint;
            gasUsed: {
                computationCost: string | number | bigint;
                storageCost: string | number | bigint;
                storageRebate: string | number | bigint;
                nonRefundableStorageFee: string | number | bigint;
            };
            modifiedAtVersions: Iterable<readonly [string | Uint8Array<ArrayBufferLike>, string | number | bigint]> & {
                length: number;
            };
            sharedObjects: Iterable<{
                objectId: string | Uint8Array<ArrayBufferLike>;
                version: string | number | bigint;
                digest: string;
            }> & {
                length: number;
            };
            transactionDigest: string;
            created: Iterable<readonly [{
                objectId: string | Uint8Array<ArrayBufferLike>;
                version: string | number | bigint;
                digest: string;
            }, import("@mysten/bcs").EnumInputShape<{
                AddressOwner: string | Uint8Array<ArrayBufferLike>;
                ObjectOwner: string | Uint8Array<ArrayBufferLike>;
                Shared: {
                    initialSharedVersion: string | number | bigint;
                };
                Immutable: boolean | object | null;
                ConsensusAddressOwner: {
                    owner: string | Uint8Array<ArrayBufferLike>;
                    startVersion: string | number | bigint;
                };
            }>]> & {
                length: number;
            };
            mutated: Iterable<readonly [{
                objectId: string | Uint8Array<ArrayBufferLike>;
                version: string | number | bigint;
                digest: string;
            }, import("@mysten/bcs").EnumInputShape<{
                AddressOwner: string | Uint8Array<ArrayBufferLike>;
                ObjectOwner: string | Uint8Array<ArrayBufferLike>;
                Shared: {
                    initialSharedVersion: string | number | bigint;
                };
                Immutable: boolean | object | null;
                ConsensusAddressOwner: {
                    owner: string | Uint8Array<ArrayBufferLike>;
                    startVersion: string | number | bigint;
                };
            }>]> & {
                length: number;
            };
            unwrapped: Iterable<readonly [{
                objectId: string | Uint8Array<ArrayBufferLike>;
                version: string | number | bigint;
                digest: string;
            }, import("@mysten/bcs").EnumInputShape<{
                AddressOwner: string | Uint8Array<ArrayBufferLike>;
                ObjectOwner: string | Uint8Array<ArrayBufferLike>;
                Shared: {
                    initialSharedVersion: string | number | bigint;
                };
                Immutable: boolean | object | null;
                ConsensusAddressOwner: {
                    owner: string | Uint8Array<ArrayBufferLike>;
                    startVersion: string | number | bigint;
                };
            }>]> & {
                length: number;
            };
            deleted: Iterable<{
                objectId: string | Uint8Array<ArrayBufferLike>;
                version: string | number | bigint;
                digest: string;
            }> & {
                length: number;
            };
            unwrappedThenDeleted: Iterable<{
                objectId: string | Uint8Array<ArrayBufferLike>;
                version: string | number | bigint;
                digest: string;
            }> & {
                length: number;
            };
            wrapped: Iterable<{
                objectId: string | Uint8Array<ArrayBufferLike>;
                version: string | number | bigint;
                digest: string;
            }> & {
                length: number;
            };
            gasObject: readonly [{
                objectId: string | Uint8Array<ArrayBufferLike>;
                version: string | number | bigint;
                digest: string;
            }, import("@mysten/bcs").EnumInputShape<{
                AddressOwner: string | Uint8Array<ArrayBufferLike>;
                ObjectOwner: string | Uint8Array<ArrayBufferLike>;
                Shared: {
                    initialSharedVersion: string | number | bigint;
                };
                Immutable: boolean | object | null;
                ConsensusAddressOwner: {
                    owner: string | Uint8Array<ArrayBufferLike>;
                    startVersion: string | number | bigint;
                };
            }>];
            eventsDigest: string | null | undefined;
            dependencies: Iterable<string> & {
                length: number;
            };
        };
        V2: {
            status: import("@mysten/bcs").EnumInputShape<{
                Success: boolean | object | null;
                Failed: {
                    error: import("@mysten/bcs").EnumInputShape<{
                        InsufficientGas: boolean | object | null;
                        InvalidGasObject: boolean | object | null;
                        InvariantViolation: boolean | object | null;
                        FeatureNotYetSupported: boolean | object | null;
                        MoveObjectTooBig: {
                            objectSize: string | number | bigint;
                            maxObjectSize: string | number | bigint;
                        };
                        MovePackageTooBig: {
                            objectSize: string | number | bigint;
                            maxObjectSize: string | number | bigint;
                        };
                        CircularObjectOwnership: {
                            object: string | Uint8Array<ArrayBufferLike>;
                        };
                        InsufficientCoinBalance: boolean | object | null;
                        CoinBalanceOverflow: boolean | object | null;
                        PublishErrorNonZeroAddress: boolean | object | null;
                        SuiMoveVerificationError: boolean | object | null;
                        MovePrimitiveRuntimeError: {
                            module: {
                                address: string | Uint8Array<ArrayBufferLike>;
                                name: string;
                            };
                            function: number;
                            instruction: number;
                            functionName: string | null | undefined;
                        } | null | undefined;
                        MoveAbort: readonly [{
                            module: {
                                address: string | Uint8Array<ArrayBufferLike>;
                                name: string;
                            };
                            function: number;
                            instruction: number;
                            functionName: string | null | undefined;
                        }, string | number | bigint];
                        VMVerificationOrDeserializationError: boolean | object | null;
                        VMInvariantViolation: boolean | object | null;
                        FunctionNotFound: boolean | object | null;
                        ArityMismatch: boolean | object | null;
                        TypeArityMismatch: boolean | object | null;
                        NonEntryFunctionInvoked: boolean | object | null;
                        CommandArgumentError: {
                            argIdx: number;
                            kind: import("@mysten/bcs").EnumInputShape<{
                                TypeMismatch: boolean | object | null;
                                InvalidBCSBytes: boolean | object | null;
                                InvalidUsageOfPureArg: boolean | object | null;
                                InvalidArgumentToPrivateEntryFunction: boolean | object | null;
                                IndexOutOfBounds: {
                                    idx: number;
                                };
                                SecondaryIndexOutOfBounds: {
                                    resultIdx: number;
                                    secondaryIdx: number;
                                };
                                InvalidResultArity: {
                                    resultIdx: number;
                                };
                                InvalidGasCoinUsage: boolean | object | null;
                                InvalidValueUsage: boolean | object | null;
                                InvalidObjectByValue: boolean | object | null;
                                InvalidObjectByMutRef: boolean | object | null;
                                SharedObjectOperationNotAllowed: boolean | object | null;
                            }>;
                        };
                        TypeArgumentError: {
                            argumentIdx: number;
                            kind: import("@mysten/bcs").EnumInputShape<{
                                TypeNotFound: boolean | object | null;
                                ConstraintNotSatisfied: boolean | object | null;
                            }>;
                        };
                        UnusedValueWithoutDrop: {
                            resultIdx: number;
                            secondaryIdx: number;
                        };
                        InvalidPublicFunctionReturnType: {
                            idx: number;
                        };
                        InvalidTransferObject: boolean | object | null;
                        EffectsTooLarge: {
                            currentSize: string | number | bigint;
                            maxSize: string | number | bigint;
                        };
                        PublishUpgradeMissingDependency: boolean | object | null;
                        PublishUpgradeDependencyDowngrade: boolean | object | null;
                        PackageUpgradeError: {
                            upgradeError: import("@mysten/bcs").EnumInputShape<{
                                UnableToFetchPackage: {
                                    packageId: string | Uint8Array<ArrayBufferLike>;
                                };
                                NotAPackage: {
                                    objectId: string | Uint8Array<ArrayBufferLike>;
                                };
                                IncompatibleUpgrade: boolean | object | null;
                                DigestDoesNotMatch: {
                                    digest: Iterable<number> & {
                                        length: number;
                                    };
                                };
                                UnknownUpgradePolicy: {
                                    policy: number;
                                };
                                PackageIDDoesNotMatch: {
                                    packageId: string | Uint8Array<ArrayBufferLike>;
                                    ticketId: string | Uint8Array<ArrayBufferLike>;
                                };
                            }>;
                        };
                        WrittenObjectsTooLarge: {
                            currentSize: string | number | bigint;
                            maxSize: string | number | bigint;
                        };
                        CertificateDenied: boolean | object | null;
                        SuiMoveVerificationTimedout: boolean | object | null;
                        SharedObjectOperationNotAllowed: boolean | object | null;
                        InputObjectDeleted: boolean | object | null;
                        ExecutionCancelledDueToSharedObjectCongestion: {
                            congestedObjects: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                                length: number;
                            };
                        };
                        AddressDeniedForCoin: {
                            address: string | Uint8Array<ArrayBufferLike>;
                            coinType: string;
                        };
                        CoinTypeGlobalPause: {
                            coinType: string;
                        };
                        ExecutionCancelledDueToRandomnessUnavailable: boolean | object | null;
                    }>;
                    command: string | number | bigint | null | undefined;
                };
            }>;
            executedEpoch: string | number | bigint;
            gasUsed: {
                computationCost: string | number | bigint;
                storageCost: string | number | bigint;
                storageRebate: string | number | bigint;
                nonRefundableStorageFee: string | number | bigint;
            };
            transactionDigest: string;
            gasObjectIndex: number | null | undefined;
            eventsDigest: string | null | undefined;
            dependencies: Iterable<string> & {
                length: number;
            };
            lamportVersion: string | number | bigint;
            changedObjects: Iterable<readonly [string | Uint8Array<ArrayBufferLike>, {
                inputState: import("@mysten/bcs").EnumInputShape<{
                    NotExist: boolean | object | null;
                    Exist: readonly [readonly [string | number | bigint, string], import("@mysten/bcs").EnumInputShape<{
                        AddressOwner: string | Uint8Array<ArrayBufferLike>;
                        ObjectOwner: string | Uint8Array<ArrayBufferLike>;
                        Shared: {
                            initialSharedVersion: string | number | bigint;
                        };
                        Immutable: boolean | object | null;
                        ConsensusAddressOwner: {
                            owner: string | Uint8Array<ArrayBufferLike>;
                            startVersion: string | number | bigint;
                        };
                    }>];
                }>;
                outputState: import("@mysten/bcs").EnumInputShape<{
                    NotExist: boolean | object | null;
                    ObjectWrite: readonly [string, import("@mysten/bcs").EnumInputShape<{
                        AddressOwner: string | Uint8Array<ArrayBufferLike>;
                        ObjectOwner: string | Uint8Array<ArrayBufferLike>;
                        Shared: {
                            initialSharedVersion: string | number | bigint;
                        };
                        Immutable: boolean | object | null;
                        ConsensusAddressOwner: {
                            owner: string | Uint8Array<ArrayBufferLike>;
                            startVersion: string | number | bigint;
                        };
                    }>];
                    PackageWrite: readonly [string | number | bigint, string];
                }>;
                idOperation: import("@mysten/bcs").EnumInputShape<{
                    None: boolean | object | null;
                    Created: boolean | object | null;
                    Deleted: boolean | object | null;
                }>;
            }]> & {
                length: number;
            };
            unchangedSharedObjects: Iterable<readonly [string | Uint8Array<ArrayBufferLike>, import("@mysten/bcs").EnumInputShape<{
                ReadOnlyRoot: readonly [string | number | bigint, string];
                MutateDeleted: string | number | bigint;
                ReadDeleted: string | number | bigint;
                Cancelled: string | number | bigint;
                PerEpochConfig: boolean | object | null;
            }>]> & {
                length: number;
            };
            auxDataDigest: string | null | undefined;
        };
    }>>;
    TransactionExpiration: import("@mysten/bcs").BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
        None: true;
        Epoch: number;
    }, "None" | "Epoch">, import("@mysten/bcs").EnumInputShape<{
        None: boolean | object | null;
        Epoch: string | number;
    }>>;
    TransactionKind: import("@mysten/bcs").BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
        ProgrammableTransaction: {
            inputs: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                Pure: {
                    bytes: string;
                };
                Object: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    ImmOrOwnedObject: {
                        objectId: string;
                        version: string;
                        digest: string;
                    };
                    SharedObject: {
                        objectId: string;
                        initialSharedVersion: string;
                        mutable: boolean;
                    };
                    Receiving: {
                        objectId: string;
                        version: string;
                        digest: string;
                    };
                }, "ImmOrOwnedObject" | "SharedObject" | "Receiving">;
            }, "Pure" | "Object">[];
            commands: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                MoveCall: {
                    package: string;
                    module: string;
                    function: string;
                    typeArguments: string[];
                    arguments: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                };
                TransferObjects: {
                    objects: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                    address: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                };
                SplitCoins: {
                    coin: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                    amounts: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                };
                MergeCoins: {
                    destination: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                    sources: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                };
                Publish: {
                    modules: string[];
                    dependencies: string[];
                };
                MakeMoveVec: {
                    type: string | null;
                    elements: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">[];
                };
                Upgrade: {
                    modules: string[];
                    dependencies: string[];
                    package: string;
                    ticket: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                        GasCoin: true;
                        Input: number;
                        Result: number;
                        NestedResult: [number, number];
                    }, "GasCoin" | "Input" | "Result" | "NestedResult">;
                };
            }, "MoveCall" | "TransferObjects" | "SplitCoins" | "MergeCoins" | "Publish" | "MakeMoveVec" | "Upgrade">[];
        };
        ChangeEpoch: true;
        Genesis: true;
        ConsensusCommitPrologue: true;
    }, "ProgrammableTransaction" | "ChangeEpoch" | "Genesis" | "ConsensusCommitPrologue">, import("@mysten/bcs").EnumInputShape<{
        ProgrammableTransaction: {
            inputs: Iterable<import("@mysten/bcs").EnumInputShape<{
                Pure: {
                    bytes: string | Uint8Array<ArrayBufferLike>;
                };
                Object: import("@mysten/bcs").EnumInputShape<{
                    ImmOrOwnedObject: {
                        objectId: string | Uint8Array<ArrayBufferLike>;
                        version: string | number | bigint;
                        digest: string;
                    };
                    SharedObject: {
                        objectId: string | Uint8Array<ArrayBufferLike>;
                        initialSharedVersion: string | number | bigint;
                        mutable: boolean;
                    };
                    Receiving: {
                        objectId: string | Uint8Array<ArrayBufferLike>;
                        version: string | number | bigint;
                        digest: string;
                    };
                }>;
            }>> & {
                length: number;
            };
            commands: Iterable<import("@mysten/bcs").EnumInputShape<{
                MoveCall: {
                    package: string | Uint8Array<ArrayBufferLike>;
                    module: string;
                    function: string;
                    typeArguments: Iterable<string | import("./types.js").TypeTag> & {
                        length: number;
                    };
                    arguments: Iterable<import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>> & {
                        length: number;
                    };
                };
                TransferObjects: {
                    objects: Iterable<import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>> & {
                        length: number;
                    };
                    address: import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>;
                };
                SplitCoins: {
                    coin: import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>;
                    amounts: Iterable<import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>> & {
                        length: number;
                    };
                };
                MergeCoins: {
                    destination: import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>;
                    sources: Iterable<import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>> & {
                        length: number;
                    };
                };
                Publish: {
                    modules: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                        length: number;
                    };
                    dependencies: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                        length: number;
                    };
                };
                MakeMoveVec: {
                    type: string | null;
                    elements: Iterable<import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>> & {
                        length: number;
                    };
                };
                Upgrade: {
                    modules: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                        length: number;
                    };
                    dependencies: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                        length: number;
                    };
                    package: string | Uint8Array<ArrayBufferLike>;
                    ticket: import("@mysten/bcs").EnumInputShape<{
                        GasCoin: boolean | object | null;
                        Input: number;
                        Result: number;
                        NestedResult: readonly [number, number];
                    }>;
                };
            }>> & {
                length: number;
            };
        };
        ChangeEpoch: boolean | object | null;
        Genesis: boolean | object | null;
        ConsensusCommitPrologue: boolean | object | null;
    }>>;
    TypeTag: import("@mysten/bcs").BcsType<string, string | import("./types.js").TypeTag>;
    u8(options?: import("@mysten/bcs").BcsTypeOptions<number>): import("@mysten/bcs").BcsType<number, number>;
    u16(options?: import("@mysten/bcs").BcsTypeOptions<number>): import("@mysten/bcs").BcsType<number, number>;
    u32(options?: import("@mysten/bcs").BcsTypeOptions<number>): import("@mysten/bcs").BcsType<number, number>;
    u64(options?: import("@mysten/bcs").BcsTypeOptions<string, number | bigint | string>): import("@mysten/bcs").BcsType<string, string | number | bigint>;
    u128(options?: import("@mysten/bcs").BcsTypeOptions<string, number | bigint | string>): import("@mysten/bcs").BcsType<string, string | number | bigint>;
    u256(options?: import("@mysten/bcs").BcsTypeOptions<string, number | bigint | string>): import("@mysten/bcs").BcsType<string, string | number | bigint>;
    bool(options?: import("@mysten/bcs").BcsTypeOptions<boolean>): import("@mysten/bcs").BcsType<boolean, boolean>;
    uleb128(options?: import("@mysten/bcs").BcsTypeOptions<number>): import("@mysten/bcs").BcsType<number, number>;
    bytes<T extends number>(size: T, options?: import("@mysten/bcs").BcsTypeOptions<Uint8Array, Iterable<number>>): import("@mysten/bcs").BcsType<Uint8Array<ArrayBufferLike>, Iterable<number>>;
    byteVector(options?: import("@mysten/bcs").BcsTypeOptions<Uint8Array, Iterable<number>>): import("@mysten/bcs").BcsType<Uint8Array<ArrayBufferLike>, Iterable<number>>;
    string(options?: import("@mysten/bcs").BcsTypeOptions<string>): import("@mysten/bcs").BcsType<string, string>;
    fixedArray<T, Input>(size: number, type: import("@mysten/bcs").BcsType<T, Input>, options?: import("@mysten/bcs").BcsTypeOptions<T[], Iterable<Input> & {
        length: number;
    }>): import("@mysten/bcs").BcsType<T[], Iterable<Input> & {
        length: number;
    }>;
    option<T, Input>(type: import("@mysten/bcs").BcsType<T, Input>): import("@mysten/bcs").BcsType<T | null, Input | null | undefined>;
    vector<T, Input>(type: import("@mysten/bcs").BcsType<T, Input>, options?: import("@mysten/bcs").BcsTypeOptions<T[], Iterable<Input> & {
        length: number;
    }>): import("@mysten/bcs").BcsType<T[], Iterable<Input> & {
        length: number;
    }>;
    tuple<const Types extends readonly import("@mysten/bcs").BcsType<any>[]>(types: Types, options?: import("@mysten/bcs").BcsTypeOptions<{ -readonly [K in keyof Types]: Types[K] extends import("@mysten/bcs").BcsType<infer T, any> ? T : never; }, { [K in keyof Types]: Types[K] extends import("@mysten/bcs").BcsType<any, infer T> ? T : never; }>): import("@mysten/bcs").BcsType<{ -readonly [K in keyof Types]: Types[K] extends import("@mysten/bcs").BcsType<infer T, any> ? T : never; }, { [K_1 in keyof Types]: Types[K_1] extends import("@mysten/bcs").BcsType<any, infer T_1> ? T_1 : never; }>;
    struct<T extends Record<string, import("@mysten/bcs").BcsType<any>>>(name: string, fields: T, options?: Omit<import("@mysten/bcs").BcsTypeOptions<{ [K in keyof T]: T[K] extends import("@mysten/bcs").BcsType<infer U, any> ? U : never; }, { [K in keyof T]: T[K] extends import("@mysten/bcs").BcsType<any, infer U> ? U : never; }>, "name">): import("@mysten/bcs").BcsType<{ [K in keyof T]: T[K] extends import("@mysten/bcs").BcsType<infer U, any> ? U : never; }, { [K_1 in keyof T]: T[K_1] extends import("@mysten/bcs").BcsType<any, infer U_1> ? U_1 : never; }>;
    enum<T extends Record<string, import("@mysten/bcs").BcsType<any> | null>>(name: string, values: T, options?: Omit<import("@mysten/bcs").BcsTypeOptions<import("@mysten/bcs").EnumOutputShape<{ [K in keyof T]: T[K] extends import("@mysten/bcs").BcsType<infer U, any> ? U : true; }>, import("@mysten/bcs").EnumInputShape<{ [K in keyof T]: T[K] extends import("@mysten/bcs").BcsType<any, infer U> ? U : boolean | object | null; }>>, "name">): import("@mysten/bcs").BcsType<import("@mysten/bcs").EnumOutputShape<{ [K in keyof T]: T[K] extends import("@mysten/bcs").BcsType<infer U, any> ? U : true; }>, import("@mysten/bcs").EnumInputShape<{ [K_1 in keyof T]: T[K_1] extends import("@mysten/bcs").BcsType<any, infer U_1> ? U_1 : boolean | object | null; }>>;
    map<K, V, InputK = K, InputV = V>(keyType: import("@mysten/bcs").BcsType<K, InputK>, valueType: import("@mysten/bcs").BcsType<V, InputV>): import("@mysten/bcs").BcsType<Map<K, V>, Map<InputK, InputV>>;
    lazy<T extends import("@mysten/bcs").BcsType<any>>(cb: () => T): T;
};
export { pureBcsSchemaFromTypeName, type ShapeFromPureTypeName, type PureTypeName, } from './pure.js';
export { suiBcs as bcs };
