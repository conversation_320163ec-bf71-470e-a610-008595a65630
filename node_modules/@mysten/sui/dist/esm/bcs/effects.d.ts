export declare const TransactionEffects: import("@mysten/bcs").BcsType<import("@mysten/bcs").EnumOutputShapeWithKeys<{
    V1: {
        status: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            Success: true;
            Failed: {
                error: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    InsufficientGas: true;
                    InvalidGasObject: true;
                    InvariantViolation: true;
                    FeatureNotYetSupported: true;
                    MoveObjectTooBig: {
                        objectSize: string;
                        maxObjectSize: string;
                    };
                    MovePackageTooBig: {
                        objectSize: string;
                        maxObjectSize: string;
                    };
                    CircularObjectOwnership: {
                        object: string;
                    };
                    InsufficientCoinBalance: true;
                    CoinBalanceOverflow: true;
                    PublishErrorNonZeroAddress: true;
                    SuiMoveVerificationError: true;
                    MovePrimitiveRuntimeError: {
                        module: {
                            address: string;
                            name: string;
                        };
                        function: number;
                        instruction: number;
                        functionName: string | null;
                    } | null;
                    MoveAbort: [{
                        module: {
                            address: string;
                            name: string;
                        };
                        function: number;
                        instruction: number;
                        functionName: string | null;
                    }, string];
                    VMVerificationOrDeserializationError: true;
                    VMInvariantViolation: true;
                    FunctionNotFound: true;
                    ArityMismatch: true;
                    TypeArityMismatch: true;
                    NonEntryFunctionInvoked: true;
                    CommandArgumentError: {
                        argIdx: number;
                        kind: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            TypeMismatch: true;
                            InvalidBCSBytes: true;
                            InvalidUsageOfPureArg: true;
                            InvalidArgumentToPrivateEntryFunction: true;
                            IndexOutOfBounds: {
                                idx: number;
                            };
                            SecondaryIndexOutOfBounds: {
                                resultIdx: number;
                                secondaryIdx: number;
                            };
                            InvalidResultArity: {
                                resultIdx: number;
                            };
                            InvalidGasCoinUsage: true;
                            InvalidValueUsage: true;
                            InvalidObjectByValue: true;
                            InvalidObjectByMutRef: true;
                            SharedObjectOperationNotAllowed: true;
                        }, "TypeMismatch" | "InvalidBCSBytes" | "InvalidUsageOfPureArg" | "InvalidArgumentToPrivateEntryFunction" | "IndexOutOfBounds" | "SecondaryIndexOutOfBounds" | "InvalidResultArity" | "InvalidGasCoinUsage" | "InvalidValueUsage" | "InvalidObjectByValue" | "InvalidObjectByMutRef" | "SharedObjectOperationNotAllowed">;
                    };
                    TypeArgumentError: {
                        argumentIdx: number;
                        kind: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            TypeNotFound: true;
                            ConstraintNotSatisfied: true;
                        }, "TypeNotFound" | "ConstraintNotSatisfied">;
                    };
                    UnusedValueWithoutDrop: {
                        resultIdx: number;
                        secondaryIdx: number;
                    };
                    InvalidPublicFunctionReturnType: {
                        idx: number;
                    };
                    InvalidTransferObject: true;
                    EffectsTooLarge: {
                        currentSize: string;
                        maxSize: string;
                    };
                    PublishUpgradeMissingDependency: true;
                    PublishUpgradeDependencyDowngrade: true;
                    PackageUpgradeError: {
                        upgradeError: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            UnableToFetchPackage: {
                                packageId: string;
                            };
                            NotAPackage: {
                                objectId: string;
                            };
                            IncompatibleUpgrade: true;
                            DigestDoesNotMatch: {
                                digest: number[];
                            };
                            UnknownUpgradePolicy: {
                                policy: number;
                            };
                            PackageIDDoesNotMatch: {
                                packageId: string;
                                ticketId: string;
                            };
                        }, "UnableToFetchPackage" | "NotAPackage" | "IncompatibleUpgrade" | "DigestDoesNotMatch" | "UnknownUpgradePolicy" | "PackageIDDoesNotMatch">;
                    };
                    WrittenObjectsTooLarge: {
                        currentSize: string;
                        maxSize: string;
                    };
                    CertificateDenied: true;
                    SuiMoveVerificationTimedout: true;
                    SharedObjectOperationNotAllowed: true;
                    InputObjectDeleted: true;
                    ExecutionCancelledDueToSharedObjectCongestion: {
                        congestedObjects: string[];
                    };
                    AddressDeniedForCoin: {
                        address: string;
                        coinType: string;
                    };
                    CoinTypeGlobalPause: {
                        coinType: string;
                    };
                    ExecutionCancelledDueToRandomnessUnavailable: true;
                }, "PackageUpgradeError" | "SharedObjectOperationNotAllowed" | "CommandArgumentError" | "TypeArgumentError" | "InsufficientGas" | "InvalidGasObject" | "InvariantViolation" | "FeatureNotYetSupported" | "MoveObjectTooBig" | "MovePackageTooBig" | "CircularObjectOwnership" | "InsufficientCoinBalance" | "CoinBalanceOverflow" | "PublishErrorNonZeroAddress" | "SuiMoveVerificationError" | "MovePrimitiveRuntimeError" | "MoveAbort" | "VMVerificationOrDeserializationError" | "VMInvariantViolation" | "FunctionNotFound" | "ArityMismatch" | "TypeArityMismatch" | "NonEntryFunctionInvoked" | "UnusedValueWithoutDrop" | "InvalidPublicFunctionReturnType" | "InvalidTransferObject" | "EffectsTooLarge" | "PublishUpgradeMissingDependency" | "PublishUpgradeDependencyDowngrade" | "WrittenObjectsTooLarge" | "CertificateDenied" | "SuiMoveVerificationTimedout" | "InputObjectDeleted" | "ExecutionCancelledDueToSharedObjectCongestion" | "AddressDeniedForCoin" | "CoinTypeGlobalPause" | "ExecutionCancelledDueToRandomnessUnavailable">;
                command: string | null;
            };
        }, "Success" | "Failed">;
        executedEpoch: string;
        gasUsed: {
            computationCost: string;
            storageCost: string;
            storageRebate: string;
            nonRefundableStorageFee: string;
        };
        modifiedAtVersions: [string, string][];
        sharedObjects: {
            objectId: string;
            version: string;
            digest: string;
        }[];
        transactionDigest: string;
        created: [{
            objectId: string;
            version: string;
            digest: string;
        }, import("@mysten/bcs").EnumOutputShapeWithKeys<{
            AddressOwner: string;
            ObjectOwner: string;
            Shared: {
                initialSharedVersion: string;
            };
            Immutable: true;
            ConsensusAddressOwner: {
                owner: string;
                startVersion: string;
            };
        }, "AddressOwner" | "ObjectOwner" | "Shared" | "Immutable" | "ConsensusAddressOwner">][];
        mutated: [{
            objectId: string;
            version: string;
            digest: string;
        }, import("@mysten/bcs").EnumOutputShapeWithKeys<{
            AddressOwner: string;
            ObjectOwner: string;
            Shared: {
                initialSharedVersion: string;
            };
            Immutable: true;
            ConsensusAddressOwner: {
                owner: string;
                startVersion: string;
            };
        }, "AddressOwner" | "ObjectOwner" | "Shared" | "Immutable" | "ConsensusAddressOwner">][];
        unwrapped: [{
            objectId: string;
            version: string;
            digest: string;
        }, import("@mysten/bcs").EnumOutputShapeWithKeys<{
            AddressOwner: string;
            ObjectOwner: string;
            Shared: {
                initialSharedVersion: string;
            };
            Immutable: true;
            ConsensusAddressOwner: {
                owner: string;
                startVersion: string;
            };
        }, "AddressOwner" | "ObjectOwner" | "Shared" | "Immutable" | "ConsensusAddressOwner">][];
        deleted: {
            objectId: string;
            version: string;
            digest: string;
        }[];
        unwrappedThenDeleted: {
            objectId: string;
            version: string;
            digest: string;
        }[];
        wrapped: {
            objectId: string;
            version: string;
            digest: string;
        }[];
        gasObject: [{
            objectId: string;
            version: string;
            digest: string;
        }, import("@mysten/bcs").EnumOutputShapeWithKeys<{
            AddressOwner: string;
            ObjectOwner: string;
            Shared: {
                initialSharedVersion: string;
            };
            Immutable: true;
            ConsensusAddressOwner: {
                owner: string;
                startVersion: string;
            };
        }, "AddressOwner" | "ObjectOwner" | "Shared" | "Immutable" | "ConsensusAddressOwner">];
        eventsDigest: string | null;
        dependencies: string[];
    };
    V2: {
        status: import("@mysten/bcs").EnumOutputShapeWithKeys<{
            Success: true;
            Failed: {
                error: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    InsufficientGas: true;
                    InvalidGasObject: true;
                    InvariantViolation: true;
                    FeatureNotYetSupported: true;
                    MoveObjectTooBig: {
                        objectSize: string;
                        maxObjectSize: string;
                    };
                    MovePackageTooBig: {
                        objectSize: string;
                        maxObjectSize: string;
                    };
                    CircularObjectOwnership: {
                        object: string;
                    };
                    InsufficientCoinBalance: true;
                    CoinBalanceOverflow: true;
                    PublishErrorNonZeroAddress: true;
                    SuiMoveVerificationError: true;
                    MovePrimitiveRuntimeError: {
                        module: {
                            address: string;
                            name: string;
                        };
                        function: number;
                        instruction: number;
                        functionName: string | null;
                    } | null;
                    MoveAbort: [{
                        module: {
                            address: string;
                            name: string;
                        };
                        function: number;
                        instruction: number;
                        functionName: string | null;
                    }, string];
                    VMVerificationOrDeserializationError: true;
                    VMInvariantViolation: true;
                    FunctionNotFound: true;
                    ArityMismatch: true;
                    TypeArityMismatch: true;
                    NonEntryFunctionInvoked: true;
                    CommandArgumentError: {
                        argIdx: number;
                        kind: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            TypeMismatch: true;
                            InvalidBCSBytes: true;
                            InvalidUsageOfPureArg: true;
                            InvalidArgumentToPrivateEntryFunction: true;
                            IndexOutOfBounds: {
                                idx: number;
                            };
                            SecondaryIndexOutOfBounds: {
                                resultIdx: number;
                                secondaryIdx: number;
                            };
                            InvalidResultArity: {
                                resultIdx: number;
                            };
                            InvalidGasCoinUsage: true;
                            InvalidValueUsage: true;
                            InvalidObjectByValue: true;
                            InvalidObjectByMutRef: true;
                            SharedObjectOperationNotAllowed: true;
                        }, "TypeMismatch" | "InvalidBCSBytes" | "InvalidUsageOfPureArg" | "InvalidArgumentToPrivateEntryFunction" | "IndexOutOfBounds" | "SecondaryIndexOutOfBounds" | "InvalidResultArity" | "InvalidGasCoinUsage" | "InvalidValueUsage" | "InvalidObjectByValue" | "InvalidObjectByMutRef" | "SharedObjectOperationNotAllowed">;
                    };
                    TypeArgumentError: {
                        argumentIdx: number;
                        kind: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            TypeNotFound: true;
                            ConstraintNotSatisfied: true;
                        }, "TypeNotFound" | "ConstraintNotSatisfied">;
                    };
                    UnusedValueWithoutDrop: {
                        resultIdx: number;
                        secondaryIdx: number;
                    };
                    InvalidPublicFunctionReturnType: {
                        idx: number;
                    };
                    InvalidTransferObject: true;
                    EffectsTooLarge: {
                        currentSize: string;
                        maxSize: string;
                    };
                    PublishUpgradeMissingDependency: true;
                    PublishUpgradeDependencyDowngrade: true;
                    PackageUpgradeError: {
                        upgradeError: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                            UnableToFetchPackage: {
                                packageId: string;
                            };
                            NotAPackage: {
                                objectId: string;
                            };
                            IncompatibleUpgrade: true;
                            DigestDoesNotMatch: {
                                digest: number[];
                            };
                            UnknownUpgradePolicy: {
                                policy: number;
                            };
                            PackageIDDoesNotMatch: {
                                packageId: string;
                                ticketId: string;
                            };
                        }, "UnableToFetchPackage" | "NotAPackage" | "IncompatibleUpgrade" | "DigestDoesNotMatch" | "UnknownUpgradePolicy" | "PackageIDDoesNotMatch">;
                    };
                    WrittenObjectsTooLarge: {
                        currentSize: string;
                        maxSize: string;
                    };
                    CertificateDenied: true;
                    SuiMoveVerificationTimedout: true;
                    SharedObjectOperationNotAllowed: true;
                    InputObjectDeleted: true;
                    ExecutionCancelledDueToSharedObjectCongestion: {
                        congestedObjects: string[];
                    };
                    AddressDeniedForCoin: {
                        address: string;
                        coinType: string;
                    };
                    CoinTypeGlobalPause: {
                        coinType: string;
                    };
                    ExecutionCancelledDueToRandomnessUnavailable: true;
                }, "PackageUpgradeError" | "SharedObjectOperationNotAllowed" | "CommandArgumentError" | "TypeArgumentError" | "InsufficientGas" | "InvalidGasObject" | "InvariantViolation" | "FeatureNotYetSupported" | "MoveObjectTooBig" | "MovePackageTooBig" | "CircularObjectOwnership" | "InsufficientCoinBalance" | "CoinBalanceOverflow" | "PublishErrorNonZeroAddress" | "SuiMoveVerificationError" | "MovePrimitiveRuntimeError" | "MoveAbort" | "VMVerificationOrDeserializationError" | "VMInvariantViolation" | "FunctionNotFound" | "ArityMismatch" | "TypeArityMismatch" | "NonEntryFunctionInvoked" | "UnusedValueWithoutDrop" | "InvalidPublicFunctionReturnType" | "InvalidTransferObject" | "EffectsTooLarge" | "PublishUpgradeMissingDependency" | "PublishUpgradeDependencyDowngrade" | "WrittenObjectsTooLarge" | "CertificateDenied" | "SuiMoveVerificationTimedout" | "InputObjectDeleted" | "ExecutionCancelledDueToSharedObjectCongestion" | "AddressDeniedForCoin" | "CoinTypeGlobalPause" | "ExecutionCancelledDueToRandomnessUnavailable">;
                command: string | null;
            };
        }, "Success" | "Failed">;
        executedEpoch: string;
        gasUsed: {
            computationCost: string;
            storageCost: string;
            storageRebate: string;
            nonRefundableStorageFee: string;
        };
        transactionDigest: string;
        gasObjectIndex: number | null;
        eventsDigest: string | null;
        dependencies: string[];
        lamportVersion: string;
        changedObjects: [string, {
            inputState: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                NotExist: true;
                Exist: [[string, string], import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    AddressOwner: string;
                    ObjectOwner: string;
                    Shared: {
                        initialSharedVersion: string;
                    };
                    Immutable: true;
                    ConsensusAddressOwner: {
                        owner: string;
                        startVersion: string;
                    };
                }, "AddressOwner" | "ObjectOwner" | "Shared" | "Immutable" | "ConsensusAddressOwner">];
            }, "NotExist" | "Exist">;
            outputState: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                NotExist: true;
                ObjectWrite: [string, import("@mysten/bcs").EnumOutputShapeWithKeys<{
                    AddressOwner: string;
                    ObjectOwner: string;
                    Shared: {
                        initialSharedVersion: string;
                    };
                    Immutable: true;
                    ConsensusAddressOwner: {
                        owner: string;
                        startVersion: string;
                    };
                }, "AddressOwner" | "ObjectOwner" | "Shared" | "Immutable" | "ConsensusAddressOwner">];
                PackageWrite: [string, string];
            }, "NotExist" | "ObjectWrite" | "PackageWrite">;
            idOperation: import("@mysten/bcs").EnumOutputShapeWithKeys<{
                None: true;
                Created: true;
                Deleted: true;
            }, "None" | "Created" | "Deleted">;
        }][];
        unchangedSharedObjects: [string, import("@mysten/bcs").EnumOutputShapeWithKeys<{
            ReadOnlyRoot: [string, string];
            MutateDeleted: string;
            ReadDeleted: string;
            Cancelled: string;
            PerEpochConfig: true;
        }, "ReadOnlyRoot" | "MutateDeleted" | "ReadDeleted" | "Cancelled" | "PerEpochConfig">][];
        auxDataDigest: string | null;
    };
}, "V1" | "V2">, import("@mysten/bcs").EnumInputShape<{
    V1: {
        status: import("@mysten/bcs").EnumInputShape<{
            Success: boolean | object | null;
            Failed: {
                error: import("@mysten/bcs").EnumInputShape<{
                    InsufficientGas: boolean | object | null;
                    InvalidGasObject: boolean | object | null;
                    InvariantViolation: boolean | object | null;
                    FeatureNotYetSupported: boolean | object | null;
                    MoveObjectTooBig: {
                        objectSize: string | number | bigint;
                        maxObjectSize: string | number | bigint;
                    };
                    MovePackageTooBig: {
                        objectSize: string | number | bigint;
                        maxObjectSize: string | number | bigint;
                    };
                    CircularObjectOwnership: {
                        object: string | Uint8Array<ArrayBufferLike>;
                    };
                    InsufficientCoinBalance: boolean | object | null;
                    CoinBalanceOverflow: boolean | object | null;
                    PublishErrorNonZeroAddress: boolean | object | null;
                    SuiMoveVerificationError: boolean | object | null;
                    MovePrimitiveRuntimeError: {
                        module: {
                            address: string | Uint8Array<ArrayBufferLike>;
                            name: string;
                        };
                        function: number;
                        instruction: number;
                        functionName: string | null | undefined;
                    } | null | undefined;
                    MoveAbort: readonly [{
                        module: {
                            address: string | Uint8Array<ArrayBufferLike>;
                            name: string;
                        };
                        function: number;
                        instruction: number;
                        functionName: string | null | undefined;
                    }, string | number | bigint];
                    VMVerificationOrDeserializationError: boolean | object | null;
                    VMInvariantViolation: boolean | object | null;
                    FunctionNotFound: boolean | object | null;
                    ArityMismatch: boolean | object | null;
                    TypeArityMismatch: boolean | object | null;
                    NonEntryFunctionInvoked: boolean | object | null;
                    CommandArgumentError: {
                        argIdx: number;
                        kind: import("@mysten/bcs").EnumInputShape<{
                            TypeMismatch: boolean | object | null;
                            InvalidBCSBytes: boolean | object | null;
                            InvalidUsageOfPureArg: boolean | object | null;
                            InvalidArgumentToPrivateEntryFunction: boolean | object | null;
                            IndexOutOfBounds: {
                                idx: number;
                            };
                            SecondaryIndexOutOfBounds: {
                                resultIdx: number;
                                secondaryIdx: number;
                            };
                            InvalidResultArity: {
                                resultIdx: number;
                            };
                            InvalidGasCoinUsage: boolean | object | null;
                            InvalidValueUsage: boolean | object | null;
                            InvalidObjectByValue: boolean | object | null;
                            InvalidObjectByMutRef: boolean | object | null;
                            SharedObjectOperationNotAllowed: boolean | object | null;
                        }>;
                    };
                    TypeArgumentError: {
                        argumentIdx: number;
                        kind: import("@mysten/bcs").EnumInputShape<{
                            TypeNotFound: boolean | object | null;
                            ConstraintNotSatisfied: boolean | object | null;
                        }>;
                    };
                    UnusedValueWithoutDrop: {
                        resultIdx: number;
                        secondaryIdx: number;
                    };
                    InvalidPublicFunctionReturnType: {
                        idx: number;
                    };
                    InvalidTransferObject: boolean | object | null;
                    EffectsTooLarge: {
                        currentSize: string | number | bigint;
                        maxSize: string | number | bigint;
                    };
                    PublishUpgradeMissingDependency: boolean | object | null;
                    PublishUpgradeDependencyDowngrade: boolean | object | null;
                    PackageUpgradeError: {
                        upgradeError: import("@mysten/bcs").EnumInputShape<{
                            UnableToFetchPackage: {
                                packageId: string | Uint8Array<ArrayBufferLike>;
                            };
                            NotAPackage: {
                                objectId: string | Uint8Array<ArrayBufferLike>;
                            };
                            IncompatibleUpgrade: boolean | object | null;
                            DigestDoesNotMatch: {
                                digest: Iterable<number> & {
                                    length: number;
                                };
                            };
                            UnknownUpgradePolicy: {
                                policy: number;
                            };
                            PackageIDDoesNotMatch: {
                                packageId: string | Uint8Array<ArrayBufferLike>;
                                ticketId: string | Uint8Array<ArrayBufferLike>;
                            };
                        }>;
                    };
                    WrittenObjectsTooLarge: {
                        currentSize: string | number | bigint;
                        maxSize: string | number | bigint;
                    };
                    CertificateDenied: boolean | object | null;
                    SuiMoveVerificationTimedout: boolean | object | null;
                    SharedObjectOperationNotAllowed: boolean | object | null;
                    InputObjectDeleted: boolean | object | null;
                    ExecutionCancelledDueToSharedObjectCongestion: {
                        congestedObjects: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                            length: number;
                        };
                    };
                    AddressDeniedForCoin: {
                        address: string | Uint8Array<ArrayBufferLike>;
                        coinType: string;
                    };
                    CoinTypeGlobalPause: {
                        coinType: string;
                    };
                    ExecutionCancelledDueToRandomnessUnavailable: boolean | object | null;
                }>;
                command: string | number | bigint | null | undefined;
            };
        }>;
        executedEpoch: string | number | bigint;
        gasUsed: {
            computationCost: string | number | bigint;
            storageCost: string | number | bigint;
            storageRebate: string | number | bigint;
            nonRefundableStorageFee: string | number | bigint;
        };
        modifiedAtVersions: Iterable<readonly [string | Uint8Array<ArrayBufferLike>, string | number | bigint]> & {
            length: number;
        };
        sharedObjects: Iterable<{
            objectId: string | Uint8Array<ArrayBufferLike>;
            version: string | number | bigint;
            digest: string;
        }> & {
            length: number;
        };
        transactionDigest: string;
        created: Iterable<readonly [{
            objectId: string | Uint8Array<ArrayBufferLike>;
            version: string | number | bigint;
            digest: string;
        }, import("@mysten/bcs").EnumInputShape<{
            AddressOwner: string | Uint8Array<ArrayBufferLike>;
            ObjectOwner: string | Uint8Array<ArrayBufferLike>;
            Shared: {
                initialSharedVersion: string | number | bigint;
            };
            Immutable: boolean | object | null;
            ConsensusAddressOwner: {
                owner: string | Uint8Array<ArrayBufferLike>;
                startVersion: string | number | bigint;
            };
        }>]> & {
            length: number;
        };
        mutated: Iterable<readonly [{
            objectId: string | Uint8Array<ArrayBufferLike>;
            version: string | number | bigint;
            digest: string;
        }, import("@mysten/bcs").EnumInputShape<{
            AddressOwner: string | Uint8Array<ArrayBufferLike>;
            ObjectOwner: string | Uint8Array<ArrayBufferLike>;
            Shared: {
                initialSharedVersion: string | number | bigint;
            };
            Immutable: boolean | object | null;
            ConsensusAddressOwner: {
                owner: string | Uint8Array<ArrayBufferLike>;
                startVersion: string | number | bigint;
            };
        }>]> & {
            length: number;
        };
        unwrapped: Iterable<readonly [{
            objectId: string | Uint8Array<ArrayBufferLike>;
            version: string | number | bigint;
            digest: string;
        }, import("@mysten/bcs").EnumInputShape<{
            AddressOwner: string | Uint8Array<ArrayBufferLike>;
            ObjectOwner: string | Uint8Array<ArrayBufferLike>;
            Shared: {
                initialSharedVersion: string | number | bigint;
            };
            Immutable: boolean | object | null;
            ConsensusAddressOwner: {
                owner: string | Uint8Array<ArrayBufferLike>;
                startVersion: string | number | bigint;
            };
        }>]> & {
            length: number;
        };
        deleted: Iterable<{
            objectId: string | Uint8Array<ArrayBufferLike>;
            version: string | number | bigint;
            digest: string;
        }> & {
            length: number;
        };
        unwrappedThenDeleted: Iterable<{
            objectId: string | Uint8Array<ArrayBufferLike>;
            version: string | number | bigint;
            digest: string;
        }> & {
            length: number;
        };
        wrapped: Iterable<{
            objectId: string | Uint8Array<ArrayBufferLike>;
            version: string | number | bigint;
            digest: string;
        }> & {
            length: number;
        };
        gasObject: readonly [{
            objectId: string | Uint8Array<ArrayBufferLike>;
            version: string | number | bigint;
            digest: string;
        }, import("@mysten/bcs").EnumInputShape<{
            AddressOwner: string | Uint8Array<ArrayBufferLike>;
            ObjectOwner: string | Uint8Array<ArrayBufferLike>;
            Shared: {
                initialSharedVersion: string | number | bigint;
            };
            Immutable: boolean | object | null;
            ConsensusAddressOwner: {
                owner: string | Uint8Array<ArrayBufferLike>;
                startVersion: string | number | bigint;
            };
        }>];
        eventsDigest: string | null | undefined;
        dependencies: Iterable<string> & {
            length: number;
        };
    };
    V2: {
        status: import("@mysten/bcs").EnumInputShape<{
            Success: boolean | object | null;
            Failed: {
                error: import("@mysten/bcs").EnumInputShape<{
                    InsufficientGas: boolean | object | null;
                    InvalidGasObject: boolean | object | null;
                    InvariantViolation: boolean | object | null;
                    FeatureNotYetSupported: boolean | object | null;
                    MoveObjectTooBig: {
                        objectSize: string | number | bigint;
                        maxObjectSize: string | number | bigint;
                    };
                    MovePackageTooBig: {
                        objectSize: string | number | bigint;
                        maxObjectSize: string | number | bigint;
                    };
                    CircularObjectOwnership: {
                        object: string | Uint8Array<ArrayBufferLike>;
                    };
                    InsufficientCoinBalance: boolean | object | null;
                    CoinBalanceOverflow: boolean | object | null;
                    PublishErrorNonZeroAddress: boolean | object | null;
                    SuiMoveVerificationError: boolean | object | null;
                    MovePrimitiveRuntimeError: {
                        module: {
                            address: string | Uint8Array<ArrayBufferLike>;
                            name: string;
                        };
                        function: number;
                        instruction: number;
                        functionName: string | null | undefined;
                    } | null | undefined;
                    MoveAbort: readonly [{
                        module: {
                            address: string | Uint8Array<ArrayBufferLike>;
                            name: string;
                        };
                        function: number;
                        instruction: number;
                        functionName: string | null | undefined;
                    }, string | number | bigint];
                    VMVerificationOrDeserializationError: boolean | object | null;
                    VMInvariantViolation: boolean | object | null;
                    FunctionNotFound: boolean | object | null;
                    ArityMismatch: boolean | object | null;
                    TypeArityMismatch: boolean | object | null;
                    NonEntryFunctionInvoked: boolean | object | null;
                    CommandArgumentError: {
                        argIdx: number;
                        kind: import("@mysten/bcs").EnumInputShape<{
                            TypeMismatch: boolean | object | null;
                            InvalidBCSBytes: boolean | object | null;
                            InvalidUsageOfPureArg: boolean | object | null;
                            InvalidArgumentToPrivateEntryFunction: boolean | object | null;
                            IndexOutOfBounds: {
                                idx: number;
                            };
                            SecondaryIndexOutOfBounds: {
                                resultIdx: number;
                                secondaryIdx: number;
                            };
                            InvalidResultArity: {
                                resultIdx: number;
                            };
                            InvalidGasCoinUsage: boolean | object | null;
                            InvalidValueUsage: boolean | object | null;
                            InvalidObjectByValue: boolean | object | null;
                            InvalidObjectByMutRef: boolean | object | null;
                            SharedObjectOperationNotAllowed: boolean | object | null;
                        }>;
                    };
                    TypeArgumentError: {
                        argumentIdx: number;
                        kind: import("@mysten/bcs").EnumInputShape<{
                            TypeNotFound: boolean | object | null;
                            ConstraintNotSatisfied: boolean | object | null;
                        }>;
                    };
                    UnusedValueWithoutDrop: {
                        resultIdx: number;
                        secondaryIdx: number;
                    };
                    InvalidPublicFunctionReturnType: {
                        idx: number;
                    };
                    InvalidTransferObject: boolean | object | null;
                    EffectsTooLarge: {
                        currentSize: string | number | bigint;
                        maxSize: string | number | bigint;
                    };
                    PublishUpgradeMissingDependency: boolean | object | null;
                    PublishUpgradeDependencyDowngrade: boolean | object | null;
                    PackageUpgradeError: {
                        upgradeError: import("@mysten/bcs").EnumInputShape<{
                            UnableToFetchPackage: {
                                packageId: string | Uint8Array<ArrayBufferLike>;
                            };
                            NotAPackage: {
                                objectId: string | Uint8Array<ArrayBufferLike>;
                            };
                            IncompatibleUpgrade: boolean | object | null;
                            DigestDoesNotMatch: {
                                digest: Iterable<number> & {
                                    length: number;
                                };
                            };
                            UnknownUpgradePolicy: {
                                policy: number;
                            };
                            PackageIDDoesNotMatch: {
                                packageId: string | Uint8Array<ArrayBufferLike>;
                                ticketId: string | Uint8Array<ArrayBufferLike>;
                            };
                        }>;
                    };
                    WrittenObjectsTooLarge: {
                        currentSize: string | number | bigint;
                        maxSize: string | number | bigint;
                    };
                    CertificateDenied: boolean | object | null;
                    SuiMoveVerificationTimedout: boolean | object | null;
                    SharedObjectOperationNotAllowed: boolean | object | null;
                    InputObjectDeleted: boolean | object | null;
                    ExecutionCancelledDueToSharedObjectCongestion: {
                        congestedObjects: Iterable<string | Uint8Array<ArrayBufferLike>> & {
                            length: number;
                        };
                    };
                    AddressDeniedForCoin: {
                        address: string | Uint8Array<ArrayBufferLike>;
                        coinType: string;
                    };
                    CoinTypeGlobalPause: {
                        coinType: string;
                    };
                    ExecutionCancelledDueToRandomnessUnavailable: boolean | object | null;
                }>;
                command: string | number | bigint | null | undefined;
            };
        }>;
        executedEpoch: string | number | bigint;
        gasUsed: {
            computationCost: string | number | bigint;
            storageCost: string | number | bigint;
            storageRebate: string | number | bigint;
            nonRefundableStorageFee: string | number | bigint;
        };
        transactionDigest: string;
        gasObjectIndex: number | null | undefined;
        eventsDigest: string | null | undefined;
        dependencies: Iterable<string> & {
            length: number;
        };
        lamportVersion: string | number | bigint;
        changedObjects: Iterable<readonly [string | Uint8Array<ArrayBufferLike>, {
            inputState: import("@mysten/bcs").EnumInputShape<{
                NotExist: boolean | object | null;
                Exist: readonly [readonly [string | number | bigint, string], import("@mysten/bcs").EnumInputShape<{
                    AddressOwner: string | Uint8Array<ArrayBufferLike>;
                    ObjectOwner: string | Uint8Array<ArrayBufferLike>;
                    Shared: {
                        initialSharedVersion: string | number | bigint;
                    };
                    Immutable: boolean | object | null;
                    ConsensusAddressOwner: {
                        owner: string | Uint8Array<ArrayBufferLike>;
                        startVersion: string | number | bigint;
                    };
                }>];
            }>;
            outputState: import("@mysten/bcs").EnumInputShape<{
                NotExist: boolean | object | null;
                ObjectWrite: readonly [string, import("@mysten/bcs").EnumInputShape<{
                    AddressOwner: string | Uint8Array<ArrayBufferLike>;
                    ObjectOwner: string | Uint8Array<ArrayBufferLike>;
                    Shared: {
                        initialSharedVersion: string | number | bigint;
                    };
                    Immutable: boolean | object | null;
                    ConsensusAddressOwner: {
                        owner: string | Uint8Array<ArrayBufferLike>;
                        startVersion: string | number | bigint;
                    };
                }>];
                PackageWrite: readonly [string | number | bigint, string];
            }>;
            idOperation: import("@mysten/bcs").EnumInputShape<{
                None: boolean | object | null;
                Created: boolean | object | null;
                Deleted: boolean | object | null;
            }>;
        }]> & {
            length: number;
        };
        unchangedSharedObjects: Iterable<readonly [string | Uint8Array<ArrayBufferLike>, import("@mysten/bcs").EnumInputShape<{
            ReadOnlyRoot: readonly [string | number | bigint, string];
            MutateDeleted: string | number | bigint;
            ReadDeleted: string | number | bigint;
            Cancelled: string | number | bigint;
            PerEpochConfig: boolean | object | null;
        }>]> & {
            length: number;
        };
        auxDataDigest: string | null | undefined;
    };
}>>;
