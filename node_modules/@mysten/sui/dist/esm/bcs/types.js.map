{"version": 3, "sources": ["../../../src/bcs/types.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nexport type SharedObjectRef = {\n\t/** Hex code as string representing the object id */\n\tobjectId: string;\n\n\t/** The version the object was shared at */\n\tinitialSharedVersion: number | string;\n\n\t/** Whether reference is mutable */\n\tmutable: boolean;\n};\n\nexport type SuiObjectRef = {\n\t/** Base64 string representing the object digest */\n\tobjectId: string;\n\t/** Object version */\n\tversion: number | string;\n\t/** Hex code as string representing the object id */\n\tdigest: string;\n};\n\n/**\n * An object argument.\n */\nexport type ObjectArg =\n\t| { ImmOrOwnedObject: SuiObjectRef }\n\t| { SharedObject: SharedObjectRef }\n\t| { Receiving: SuiObjectRef };\n\nexport type ObjectCallArg = {\n\tObject: ObjectArg;\n};\n\n/**\n * A pure argument.\n */\nexport type PureArg = { Pure: Array<number> };\n\nexport function isPureArg(arg: any): arg is PureArg {\n\treturn (arg as PureArg).Pure !== undefined;\n}\n\n/**\n * An argument for the transaction. It is a 'meant' enum which expects to have\n * one of the optional properties. If not, the BCS error will be thrown while\n * attempting to form a transaction.\n *\n * Example:\n * ```js\n * let arg1: CallArg = { Object: { Shared: {\n *   objectId: '5460cf92b5e3e7067aaace60d88324095fd22944',\n *   initialSharedVersion: 1,\n *   mutable: true,\n * } } };\n * let arg2: CallArg = { Pure: bcs.ser(BCS.STRING, 100000).toBytes() };\n * let arg3: CallArg = { Object: { ImmOrOwned: {\n *   objectId: '4047d2e25211d87922b6650233bd0503a6734279',\n *   version: 1,\n *   digest: 'bCiANCht4O9MEUhuYjdRCqRPZjr2rJ8MfqNiwyhmRgA='\n * } } };\n * ```\n *\n * For `Pure` arguments BCS is required. You must encode the values with BCS according\n * to the type required by the called function. Pure accepts only serialized values\n */\nexport type CallArg = PureArg | ObjectCallArg;\n\n/**\n * Kind of a TypeTag which is represented by a Move type identifier.\n */\nexport type StructTag = {\n\taddress: string;\n\tmodule: string;\n\tname: string;\n\ttypeParams: TypeTag[];\n};\n\n/**\n * Sui TypeTag object. A decoupled `0x...::module::Type<???>` parameter.\n */\nexport type TypeTag =\n\t| { bool: null | true }\n\t| { u8: null | true }\n\t| { u64: null | true }\n\t| { u128: null | true }\n\t| { address: null | true }\n\t| { signer: null | true }\n\t| { vector: TypeTag }\n\t| { struct: StructTag }\n\t| { u16: null | true }\n\t| { u32: null | true }\n\t| { u256: null | true };\n\n// ========== TransactionData ===========\n\n/**\n * The GasData to be used in the transaction.\n */\nexport type GasData = {\n\tpayment: SuiObjectRef[];\n\towner: string; // Gas Object's owner\n\tprice: number;\n\tbudget: number;\n};\n\n/**\n * TransactionExpiration\n *\n * Indications the expiration time for a transaction.\n */\nexport type TransactionExpiration = { None: null } | { Epoch: number };\n"], "mappings": "AAwCO,SAAS,UAAU,KAA0B;AACnD,SAAQ,IAAgB,SAAS;AAClC;", "names": []}