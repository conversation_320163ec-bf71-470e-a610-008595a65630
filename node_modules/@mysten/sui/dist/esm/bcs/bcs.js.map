{"version": 3, "sources": ["../../../src/bcs/bcs.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { BcsType, BcsTypeOptions } from '@mysten/bcs';\nimport { bcs, fromBase58, fromBase64, fromHex, toBase58, toBase64, toHex } from '@mysten/bcs';\n\nimport { isValidSuiAddress, normalizeSuiAddress, SUI_ADDRESS_LENGTH } from '../utils/sui-types.js';\nimport { TypeTagSerializer } from './type-tag-serializer.js';\nimport type { TypeTag as TypeTagType } from './types.js';\n\nfunction unsafe_u64(options?: BcsTypeOptions<number>) {\n\treturn bcs\n\t\t.u64({\n\t\t\tname: 'unsafe_u64',\n\t\t\t...(options as object),\n\t\t})\n\t\t.transform({\n\t\t\tinput: (val: number | string) => val,\n\t\t\toutput: (val) => Number(val),\n\t\t});\n}\n\nfunction optionEnum<T extends BcsType<any, any>>(type: T) {\n\treturn bcs.enum('Option', {\n\t\tNone: null,\n\t\tSome: type,\n\t});\n}\n\nexport const Address = bcs.bytes(SUI_ADDRESS_LENGTH).transform({\n\tvalidate: (val) => {\n\t\tconst address = typeof val === 'string' ? val : toHex(val);\n\t\tif (!address || !isValidSuiAddress(normalizeSuiAddress(address))) {\n\t\t\tthrow new Error(`Invalid Sui address ${address}`);\n\t\t}\n\t},\n\tinput: (val: string | Uint8Array) =>\n\t\ttypeof val === 'string' ? fromHex(normalizeSuiAddress(val)) : val,\n\toutput: (val) => normalizeSuiAddress(toHex(val)),\n});\n\nexport const ObjectDigest = bcs.vector(bcs.u8()).transform({\n\tname: 'ObjectDigest',\n\tinput: (value: string) => fromBase58(value),\n\toutput: (value) => toBase58(new Uint8Array(value)),\n\tvalidate: (value) => {\n\t\tif (fromBase58(value).length !== 32) {\n\t\t\tthrow new Error('ObjectDigest must be 32 bytes');\n\t\t}\n\t},\n});\n\nexport const SuiObjectRef = bcs.struct('SuiObjectRef', {\n\tobjectId: Address,\n\tversion: bcs.u64(),\n\tdigest: ObjectDigest,\n});\n\nexport const SharedObjectRef = bcs.struct('SharedObjectRef', {\n\tobjectId: Address,\n\tinitialSharedVersion: bcs.u64(),\n\tmutable: bcs.bool(),\n});\n\nexport const ObjectArg = bcs.enum('ObjectArg', {\n\tImmOrOwnedObject: SuiObjectRef,\n\tSharedObject: SharedObjectRef,\n\tReceiving: SuiObjectRef,\n});\n\nexport const Owner = bcs.enum('Owner', {\n\tAddressOwner: Address,\n\tObjectOwner: Address,\n\tShared: bcs.struct('Shared', {\n\t\tinitialSharedVersion: bcs.u64(),\n\t}),\n\tImmutable: null,\n\tConsensusAddressOwner: bcs.struct('ConsensusAddressOwner', {\n\t\towner: Address,\n\t\tstartVersion: bcs.u64(),\n\t}),\n});\n\nexport const CallArg = bcs.enum('CallArg', {\n\tPure: bcs.struct('Pure', {\n\t\tbytes: bcs.vector(bcs.u8()).transform({\n\t\t\tinput: (val: string | Uint8Array) => (typeof val === 'string' ? fromBase64(val) : val),\n\t\t\toutput: (val) => toBase64(new Uint8Array(val)),\n\t\t}),\n\t}),\n\tObject: ObjectArg,\n});\n\nconst InnerTypeTag: BcsType<TypeTagType, TypeTagType> = bcs.enum('TypeTag', {\n\tbool: null,\n\tu8: null,\n\tu64: null,\n\tu128: null,\n\taddress: null,\n\tsigner: null,\n\tvector: bcs.lazy(() => InnerTypeTag),\n\tstruct: bcs.lazy(() => StructTag),\n\tu16: null,\n\tu32: null,\n\tu256: null,\n}) as BcsType<TypeTagType>;\n\nexport const TypeTag = InnerTypeTag.transform({\n\tinput: (typeTag: string | TypeTagType) =>\n\t\ttypeof typeTag === 'string' ? TypeTagSerializer.parseFromStr(typeTag, true) : typeTag,\n\toutput: (typeTag: TypeTagType) => TypeTagSerializer.tagToString(typeTag),\n});\n\nexport const Argument = bcs.enum('Argument', {\n\tGasCoin: null,\n\tInput: bcs.u16(),\n\tResult: bcs.u16(),\n\tNestedResult: bcs.tuple([bcs.u16(), bcs.u16()]),\n});\n\nexport const ProgrammableMoveCall = bcs.struct('ProgrammableMoveCall', {\n\tpackage: Address,\n\tmodule: bcs.string(),\n\tfunction: bcs.string(),\n\ttypeArguments: bcs.vector(TypeTag),\n\targuments: bcs.vector(Argument),\n});\n\nexport const Command = bcs.enum('Command', {\n\t/**\n\t * A Move Call - any public Move function can be called via\n\t * this transaction. The results can be used that instant to pass\n\t * into the next transaction.\n\t */\n\tMoveCall: ProgrammableMoveCall,\n\t/**\n\t * Transfer vector of objects to a receiver.\n\t */\n\tTransferObjects: bcs.struct('TransferObjects', {\n\t\tobjects: bcs.vector(Argument),\n\t\taddress: Argument,\n\t}),\n\t// /**\n\t//  * Split `amount` from a `coin`.\n\t//  */\n\tSplitCoins: bcs.struct('SplitCoins', {\n\t\tcoin: Argument,\n\t\tamounts: bcs.vector(Argument),\n\t}),\n\t// /**\n\t//  * Merge Vector of Coins (`sources`) into a `destination`.\n\t//  */\n\tMergeCoins: bcs.struct('MergeCoins', {\n\t\tdestination: Argument,\n\t\tsources: bcs.vector(Argument),\n\t}),\n\t// /**\n\t//  * Publish a Move module.\n\t//  */\n\tPublish: bcs.struct('Publish', {\n\t\tmodules: bcs.vector(\n\t\t\tbcs.vector(bcs.u8()).transform({\n\t\t\t\tinput: (val: string | Uint8Array) => (typeof val === 'string' ? fromBase64(val) : val),\n\t\t\t\toutput: (val) => toBase64(new Uint8Array(val)),\n\t\t\t}),\n\t\t),\n\t\tdependencies: bcs.vector(Address),\n\t}),\n\t// /**\n\t//  * Build a vector of objects using the input arguments.\n\t//  * It is impossible to export construct a `vector<T: key>` otherwise,\n\t//  * so this call serves a utility function.\n\t//  */\n\tMakeMoveVec: bcs.struct('MakeMoveVec', {\n\t\ttype: optionEnum(TypeTag).transform({\n\t\t\tinput: (val: string | null) =>\n\t\t\t\tval === null\n\t\t\t\t\t? {\n\t\t\t\t\t\t\tNone: true,\n\t\t\t\t\t\t}\n\t\t\t\t\t: {\n\t\t\t\t\t\t\tSome: val,\n\t\t\t\t\t\t},\n\t\t\toutput: (val) => val.Some ?? null,\n\t\t}),\n\t\telements: bcs.vector(Argument),\n\t}),\n\tUpgrade: bcs.struct('Upgrade', {\n\t\tmodules: bcs.vector(\n\t\t\tbcs.vector(bcs.u8()).transform({\n\t\t\t\tinput: (val: string | Uint8Array) => (typeof val === 'string' ? fromBase64(val) : val),\n\t\t\t\toutput: (val) => toBase64(new Uint8Array(val)),\n\t\t\t}),\n\t\t),\n\t\tdependencies: bcs.vector(Address),\n\t\tpackage: Address,\n\t\tticket: Argument,\n\t}),\n});\n\nexport const ProgrammableTransaction = bcs.struct('ProgrammableTransaction', {\n\tinputs: bcs.vector(CallArg),\n\tcommands: bcs.vector(Command),\n});\n\nexport const TransactionKind = bcs.enum('TransactionKind', {\n\tProgrammableTransaction: ProgrammableTransaction,\n\tChangeEpoch: null,\n\tGenesis: null,\n\tConsensusCommitPrologue: null,\n});\n\nexport const TransactionExpiration = bcs.enum('TransactionExpiration', {\n\tNone: null,\n\tEpoch: unsafe_u64(),\n});\n\nexport const StructTag = bcs.struct('StructTag', {\n\taddress: Address,\n\tmodule: bcs.string(),\n\tname: bcs.string(),\n\ttypeParams: bcs.vector(InnerTypeTag),\n});\n\nexport const GasData = bcs.struct('GasData', {\n\tpayment: bcs.vector(SuiObjectRef),\n\towner: Address,\n\tprice: bcs.u64(),\n\tbudget: bcs.u64(),\n});\n\nexport const TransactionDataV1 = bcs.struct('TransactionDataV1', {\n\tkind: TransactionKind,\n\tsender: Address,\n\tgasData: GasData,\n\texpiration: TransactionExpiration,\n});\n\nexport const TransactionData = bcs.enum('TransactionData', {\n\tV1: TransactionDataV1,\n});\n\nexport const IntentScope = bcs.enum('IntentScope', {\n\tTransactionData: null,\n\tTransactionEffects: null,\n\tCheckpointSummary: null,\n\tPersonalMessage: null,\n});\n\nexport const IntentVersion = bcs.enum('IntentVersion', {\n\tV0: null,\n});\n\nexport const AppId = bcs.enum('AppId', {\n\tSui: null,\n});\n\nexport const Intent = bcs.struct('Intent', {\n\tscope: IntentScope,\n\tversion: IntentVersion,\n\tappId: AppId,\n});\n\nexport function IntentMessage<T extends BcsType<any>>(T: T) {\n\treturn bcs.struct(`IntentMessage<${T.name}>`, {\n\t\tintent: Intent,\n\t\tvalue: T,\n\t});\n}\n\nexport const CompressedSignature = bcs.enum('CompressedSignature', {\n\tED25519: bcs.fixedArray(64, bcs.u8()),\n\tSecp256k1: bcs.fixedArray(64, bcs.u8()),\n\tSecp256r1: bcs.fixedArray(64, bcs.u8()),\n\tZkLogin: bcs.vector(bcs.u8()),\n});\n\nexport const PublicKey = bcs.enum('PublicKey', {\n\tED25519: bcs.fixedArray(32, bcs.u8()),\n\tSecp256k1: bcs.fixedArray(33, bcs.u8()),\n\tSecp256r1: bcs.fixedArray(33, bcs.u8()),\n\tZkLogin: bcs.vector(bcs.u8()),\n});\n\nexport const MultiSigPkMap = bcs.struct('MultiSigPkMap', {\n\tpubKey: PublicKey,\n\tweight: bcs.u8(),\n});\n\nexport const MultiSigPublicKey = bcs.struct('MultiSigPublicKey', {\n\tpk_map: bcs.vector(MultiSigPkMap),\n\tthreshold: bcs.u16(),\n});\n\nexport const MultiSig = bcs.struct('MultiSig', {\n\tsigs: bcs.vector(CompressedSignature),\n\tbitmap: bcs.u16(),\n\tmultisig_pk: MultiSigPublicKey,\n});\n\nexport const base64String = bcs.vector(bcs.u8()).transform({\n\tinput: (val: string | Uint8Array) => (typeof val === 'string' ? fromBase64(val) : val),\n\toutput: (val) => toBase64(new Uint8Array(val)),\n});\n\nexport const SenderSignedTransaction = bcs.struct('SenderSignedTransaction', {\n\tintentMessage: IntentMessage(TransactionData),\n\ttxSignatures: bcs.vector(base64String),\n});\n\nexport const SenderSignedData = bcs.vector(SenderSignedTransaction, {\n\tname: 'SenderSignedData',\n});\n\nexport const PasskeyAuthenticator = bcs.struct('PasskeyAuthenticator', {\n\tauthenticatorData: bcs.vector(bcs.u8()),\n\tclientDataJson: bcs.string(),\n\tuserSignature: bcs.vector(bcs.u8()),\n});\n"], "mappings": "AAIA,SAAS,KAAK,YAAY,YAAY,SAAS,UAAU,UAAU,aAAa;AAEhF,SAAS,mBAAmB,qBAAqB,0BAA0B;AAC3E,SAAS,yBAAyB;AAGlC,SAAS,WAAW,SAAkC;AACrD,SAAO,IACL,IAAI;AAAA,IACJ,MAAM;AAAA,IACN,GAAI;AAAA,EACL,CAAC,EACA,UAAU;AAAA,IACV,OAAO,CAAC,QAAyB;AAAA,IACjC,QAAQ,CAAC,QAAQ,OAAO,GAAG;AAAA,EAC5B,CAAC;AACH;AAEA,SAAS,WAAwC,MAAS;AACzD,SAAO,IAAI,KAAK,UAAU;AAAA,IACzB,MAAM;AAAA,IACN,MAAM;AAAA,EACP,CAAC;AACF;AAEO,MAAM,UAAU,IAAI,MAAM,kBAAkB,EAAE,UAAU;AAAA,EAC9D,UAAU,CAAC,QAAQ;AAClB,UAAM,UAAU,OAAO,QAAQ,WAAW,MAAM,MAAM,GAAG;AACzD,QAAI,CAAC,WAAW,CAAC,kBAAkB,oBAAoB,OAAO,CAAC,GAAG;AACjE,YAAM,IAAI,MAAM,uBAAuB,OAAO,EAAE;AAAA,IACjD;AAAA,EACD;AAAA,EACA,OAAO,CAAC,QACP,OAAO,QAAQ,WAAW,QAAQ,oBAAoB,GAAG,CAAC,IAAI;AAAA,EAC/D,QAAQ,CAAC,QAAQ,oBAAoB,MAAM,GAAG,CAAC;AAChD,CAAC;AAEM,MAAM,eAAe,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,UAAU;AAAA,EAC1D,MAAM;AAAA,EACN,OAAO,CAAC,UAAkB,WAAW,KAAK;AAAA,EAC1C,QAAQ,CAAC,UAAU,SAAS,IAAI,WAAW,KAAK,CAAC;AAAA,EACjD,UAAU,CAAC,UAAU;AACpB,QAAI,WAAW,KAAK,EAAE,WAAW,IAAI;AACpC,YAAM,IAAI,MAAM,+BAA+B;AAAA,IAChD;AAAA,EACD;AACD,CAAC;AAEM,MAAM,eAAe,IAAI,OAAO,gBAAgB;AAAA,EACtD,UAAU;AAAA,EACV,SAAS,IAAI,IAAI;AAAA,EACjB,QAAQ;AACT,CAAC;AAEM,MAAM,kBAAkB,IAAI,OAAO,mBAAmB;AAAA,EAC5D,UAAU;AAAA,EACV,sBAAsB,IAAI,IAAI;AAAA,EAC9B,SAAS,IAAI,KAAK;AACnB,CAAC;AAEM,MAAM,YAAY,IAAI,KAAK,aAAa;AAAA,EAC9C,kBAAkB;AAAA,EAClB,cAAc;AAAA,EACd,WAAW;AACZ,CAAC;AAEM,MAAM,QAAQ,IAAI,KAAK,SAAS;AAAA,EACtC,cAAc;AAAA,EACd,aAAa;AAAA,EACb,QAAQ,IAAI,OAAO,UAAU;AAAA,IAC5B,sBAAsB,IAAI,IAAI;AAAA,EAC/B,CAAC;AAAA,EACD,WAAW;AAAA,EACX,uBAAuB,IAAI,OAAO,yBAAyB;AAAA,IAC1D,OAAO;AAAA,IACP,cAAc,IAAI,IAAI;AAAA,EACvB,CAAC;AACF,CAAC;AAEM,MAAM,UAAU,IAAI,KAAK,WAAW;AAAA,EAC1C,MAAM,IAAI,OAAO,QAAQ;AAAA,IACxB,OAAO,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,UAAU;AAAA,MACrC,OAAO,CAAC,QAA8B,OAAO,QAAQ,WAAW,WAAW,GAAG,IAAI;AAAA,MAClF,QAAQ,CAAC,QAAQ,SAAS,IAAI,WAAW,GAAG,CAAC;AAAA,IAC9C,CAAC;AAAA,EACF,CAAC;AAAA,EACD,QAAQ;AACT,CAAC;AAED,MAAM,eAAkD,IAAI,KAAK,WAAW;AAAA,EAC3E,MAAM;AAAA,EACN,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,MAAM;AAAA,EACN,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ,IAAI,KAAK,MAAM,YAAY;AAAA,EACnC,QAAQ,IAAI,KAAK,MAAM,SAAS;AAAA,EAChC,KAAK;AAAA,EACL,KAAK;AAAA,EACL,MAAM;AACP,CAAC;AAEM,MAAM,UAAU,aAAa,UAAU;AAAA,EAC7C,OAAO,CAAC,YACP,OAAO,YAAY,WAAW,kBAAkB,aAAa,SAAS,IAAI,IAAI;AAAA,EAC/E,QAAQ,CAAC,YAAyB,kBAAkB,YAAY,OAAO;AACxE,CAAC;AAEM,MAAM,WAAW,IAAI,KAAK,YAAY;AAAA,EAC5C,SAAS;AAAA,EACT,OAAO,IAAI,IAAI;AAAA,EACf,QAAQ,IAAI,IAAI;AAAA,EAChB,cAAc,IAAI,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC;AAC/C,CAAC;AAEM,MAAM,uBAAuB,IAAI,OAAO,wBAAwB;AAAA,EACtE,SAAS;AAAA,EACT,QAAQ,IAAI,OAAO;AAAA,EACnB,UAAU,IAAI,OAAO;AAAA,EACrB,eAAe,IAAI,OAAO,OAAO;AAAA,EACjC,WAAW,IAAI,OAAO,QAAQ;AAC/B,CAAC;AAEM,MAAM,UAAU,IAAI,KAAK,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1C,UAAU;AAAA;AAAA;AAAA;AAAA,EAIV,iBAAiB,IAAI,OAAO,mBAAmB;AAAA,IAC9C,SAAS,IAAI,OAAO,QAAQ;AAAA,IAC5B,SAAS;AAAA,EACV,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,YAAY,IAAI,OAAO,cAAc;AAAA,IACpC,MAAM;AAAA,IACN,SAAS,IAAI,OAAO,QAAQ;AAAA,EAC7B,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,YAAY,IAAI,OAAO,cAAc;AAAA,IACpC,aAAa;AAAA,IACb,SAAS,IAAI,OAAO,QAAQ;AAAA,EAC7B,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,SAAS,IAAI,OAAO,WAAW;AAAA,IAC9B,SAAS,IAAI;AAAA,MACZ,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,UAAU;AAAA,QAC9B,OAAO,CAAC,QAA8B,OAAO,QAAQ,WAAW,WAAW,GAAG,IAAI;AAAA,QAClF,QAAQ,CAAC,QAAQ,SAAS,IAAI,WAAW,GAAG,CAAC;AAAA,MAC9C,CAAC;AAAA,IACF;AAAA,IACA,cAAc,IAAI,OAAO,OAAO;AAAA,EACjC,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,aAAa,IAAI,OAAO,eAAe;AAAA,IACtC,MAAM,WAAW,OAAO,EAAE,UAAU;AAAA,MACnC,OAAO,CAAC,QACP,QAAQ,OACL;AAAA,QACA,MAAM;AAAA,MACP,IACC;AAAA,QACA,MAAM;AAAA,MACP;AAAA,MACH,QAAQ,CAAC,QAAQ,IAAI,QAAQ;AAAA,IAC9B,CAAC;AAAA,IACD,UAAU,IAAI,OAAO,QAAQ;AAAA,EAC9B,CAAC;AAAA,EACD,SAAS,IAAI,OAAO,WAAW;AAAA,IAC9B,SAAS,IAAI;AAAA,MACZ,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,UAAU;AAAA,QAC9B,OAAO,CAAC,QAA8B,OAAO,QAAQ,WAAW,WAAW,GAAG,IAAI;AAAA,QAClF,QAAQ,CAAC,QAAQ,SAAS,IAAI,WAAW,GAAG,CAAC;AAAA,MAC9C,CAAC;AAAA,IACF;AAAA,IACA,cAAc,IAAI,OAAO,OAAO;AAAA,IAChC,SAAS;AAAA,IACT,QAAQ;AAAA,EACT,CAAC;AACF,CAAC;AAEM,MAAM,0BAA0B,IAAI,OAAO,2BAA2B;AAAA,EAC5E,QAAQ,IAAI,OAAO,OAAO;AAAA,EAC1B,UAAU,IAAI,OAAO,OAAO;AAC7B,CAAC;AAEM,MAAM,kBAAkB,IAAI,KAAK,mBAAmB;AAAA,EAC1D;AAAA,EACA,aAAa;AAAA,EACb,SAAS;AAAA,EACT,yBAAyB;AAC1B,CAAC;AAEM,MAAM,wBAAwB,IAAI,KAAK,yBAAyB;AAAA,EACtE,MAAM;AAAA,EACN,OAAO,WAAW;AACnB,CAAC;AAEM,MAAM,YAAY,IAAI,OAAO,aAAa;AAAA,EAChD,SAAS;AAAA,EACT,QAAQ,IAAI,OAAO;AAAA,EACnB,MAAM,IAAI,OAAO;AAAA,EACjB,YAAY,IAAI,OAAO,YAAY;AACpC,CAAC;AAEM,MAAM,UAAU,IAAI,OAAO,WAAW;AAAA,EAC5C,SAAS,IAAI,OAAO,YAAY;AAAA,EAChC,OAAO;AAAA,EACP,OAAO,IAAI,IAAI;AAAA,EACf,QAAQ,IAAI,IAAI;AACjB,CAAC;AAEM,MAAM,oBAAoB,IAAI,OAAO,qBAAqB;AAAA,EAChE,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,YAAY;AACb,CAAC;AAEM,MAAM,kBAAkB,IAAI,KAAK,mBAAmB;AAAA,EAC1D,IAAI;AACL,CAAC;AAEM,MAAM,cAAc,IAAI,KAAK,eAAe;AAAA,EAClD,iBAAiB;AAAA,EACjB,oBAAoB;AAAA,EACpB,mBAAmB;AAAA,EACnB,iBAAiB;AAClB,CAAC;AAEM,MAAM,gBAAgB,IAAI,KAAK,iBAAiB;AAAA,EACtD,IAAI;AACL,CAAC;AAEM,MAAM,QAAQ,IAAI,KAAK,SAAS;AAAA,EACtC,KAAK;AACN,CAAC;AAEM,MAAM,SAAS,IAAI,OAAO,UAAU;AAAA,EAC1C,OAAO;AAAA,EACP,SAAS;AAAA,EACT,OAAO;AACR,CAAC;AAEM,SAAS,cAAsC,GAAM;AAC3D,SAAO,IAAI,OAAO,iBAAiB,EAAE,IAAI,KAAK;AAAA,IAC7C,QAAQ;AAAA,IACR,OAAO;AAAA,EACR,CAAC;AACF;AAEO,MAAM,sBAAsB,IAAI,KAAK,uBAAuB;AAAA,EAClE,SAAS,IAAI,WAAW,IAAI,IAAI,GAAG,CAAC;AAAA,EACpC,WAAW,IAAI,WAAW,IAAI,IAAI,GAAG,CAAC;AAAA,EACtC,WAAW,IAAI,WAAW,IAAI,IAAI,GAAG,CAAC;AAAA,EACtC,SAAS,IAAI,OAAO,IAAI,GAAG,CAAC;AAC7B,CAAC;AAEM,MAAM,YAAY,IAAI,KAAK,aAAa;AAAA,EAC9C,SAAS,IAAI,WAAW,IAAI,IAAI,GAAG,CAAC;AAAA,EACpC,WAAW,IAAI,WAAW,IAAI,IAAI,GAAG,CAAC;AAAA,EACtC,WAAW,IAAI,WAAW,IAAI,IAAI,GAAG,CAAC;AAAA,EACtC,SAAS,IAAI,OAAO,IAAI,GAAG,CAAC;AAC7B,CAAC;AAEM,MAAM,gBAAgB,IAAI,OAAO,iBAAiB;AAAA,EACxD,QAAQ;AAAA,EACR,QAAQ,IAAI,GAAG;AAChB,CAAC;AAEM,MAAM,oBAAoB,IAAI,OAAO,qBAAqB;AAAA,EAChE,QAAQ,IAAI,OAAO,aAAa;AAAA,EAChC,WAAW,IAAI,IAAI;AACpB,CAAC;AAEM,MAAM,WAAW,IAAI,OAAO,YAAY;AAAA,EAC9C,MAAM,IAAI,OAAO,mBAAmB;AAAA,EACpC,QAAQ,IAAI,IAAI;AAAA,EAChB,aAAa;AACd,CAAC;AAEM,MAAM,eAAe,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,UAAU;AAAA,EAC1D,OAAO,CAAC,QAA8B,OAAO,QAAQ,WAAW,WAAW,GAAG,IAAI;AAAA,EAClF,QAAQ,CAAC,QAAQ,SAAS,IAAI,WAAW,GAAG,CAAC;AAC9C,CAAC;AAEM,MAAM,0BAA0B,IAAI,OAAO,2BAA2B;AAAA,EAC5E,eAAe,cAAc,eAAe;AAAA,EAC5C,cAAc,IAAI,OAAO,YAAY;AACtC,CAAC;AAEM,MAAM,mBAAmB,IAAI,OAAO,yBAAyB;AAAA,EACnE,MAAM;AACP,CAAC;AAEM,MAAM,uBAAuB,IAAI,OAAO,wBAAwB;AAAA,EACtE,mBAAmB,IAAI,OAAO,IAAI,GAAG,CAAC;AAAA,EACtC,gBAAgB,IAAI,OAAO;AAAA,EAC3B,eAAe,IAAI,OAAO,IAAI,GAAG,CAAC;AACnC,CAAC;", "names": []}