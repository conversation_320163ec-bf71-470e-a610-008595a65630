{"version": 3, "sources": ["../../../src/cryptography/keypair.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { bcs, toBase64 } from '@mysten/bcs';\nimport { blake2b } from '@noble/hashes/blake2b';\nimport { bech32 } from '@scure/base';\n\nimport type { IntentScope } from './intent.js';\nimport { messageWithIntent } from './intent.js';\nimport type { PublicKey } from './publickey.js';\nimport { SIGNATURE_FLAG_TO_SCHEME, SIGNATURE_SCHEME_TO_FLAG } from './signature-scheme.js';\nimport type { SignatureScheme } from './signature-scheme.js';\nimport { toSerializedSignature } from './signature.js';\nimport type { Transaction } from '../transactions/Transaction.js';\nimport type { ClientWithCoreApi, Experimental_SuiClientTypes } from '../experimental/index.js';\n\nexport const PRIVATE_KEY_SIZE = 32;\nexport const LEGACY_PRIVATE_KEY_SIZE = 64;\nexport const SUI_PRIVATE_KEY_PREFIX = 'suiprivkey';\n\nexport type ParsedKeypair = {\n\tscheme: SignatureScheme;\n\t/** @deprecated use `scheme` instead */\n\tschema: SignatureScheme;\n\tsecretKey: Uint8Array;\n};\n\nexport interface SignatureWithBytes {\n\tbytes: string;\n\tsignature: string;\n}\n\nexport interface SignAndExecuteOptions {\n\ttransaction: Transaction;\n\tclient: ClientWithCoreApi;\n}\n\n/**\n * TODO: Document\n */\nexport abstract class Signer {\n\tabstract sign(bytes: Uint8Array): Promise<Uint8Array>;\n\t/**\n\t * Sign messages with a specific intent. By combining the message bytes with the intent before hashing and signing,\n\t * it ensures that a signed message is tied to a specific purpose and domain separator is provided\n\t */\n\tasync signWithIntent(bytes: Uint8Array, intent: IntentScope): Promise<SignatureWithBytes> {\n\t\tconst intentMessage = messageWithIntent(intent, bytes);\n\t\tconst digest = blake2b(intentMessage, { dkLen: 32 });\n\n\t\tconst signature = toSerializedSignature({\n\t\t\tsignature: await this.sign(digest),\n\t\t\tsignatureScheme: this.getKeyScheme(),\n\t\t\tpublicKey: this.getPublicKey(),\n\t\t});\n\n\t\treturn {\n\t\t\tsignature,\n\t\t\tbytes: toBase64(bytes),\n\t\t};\n\t}\n\t/**\n\t * Signs provided transaction by calling `signWithIntent()` with a `TransactionData` provided as intent scope\n\t */\n\tasync signTransaction(bytes: Uint8Array) {\n\t\treturn this.signWithIntent(bytes, 'TransactionData');\n\t}\n\t/**\n\t * Signs provided personal message by calling `signWithIntent()` with a `PersonalMessage` provided as intent scope\n\t */\n\tasync signPersonalMessage(bytes: Uint8Array) {\n\t\tconst { signature } = await this.signWithIntent(\n\t\t\tbcs.vector(bcs.u8()).serialize(bytes).toBytes(),\n\t\t\t'PersonalMessage',\n\t\t);\n\n\t\treturn {\n\t\t\tbytes: toBase64(bytes),\n\t\t\tsignature,\n\t\t};\n\t}\n\n\tasync signAndExecuteTransaction({\n\t\ttransaction,\n\t\tclient,\n\t}: SignAndExecuteOptions): Promise<Experimental_SuiClientTypes.TransactionResponse> {\n\t\tconst bytes = await transaction.build({ client });\n\t\tconst { signature } = await this.signTransaction(bytes);\n\t\tconst response = await client.core.executeTransaction({\n\t\t\ttransaction: bytes,\n\t\t\tsignatures: [signature],\n\t\t});\n\n\t\treturn response.transaction;\n\t}\n\n\ttoSuiAddress(): string {\n\t\treturn this.getPublicKey().toSuiAddress();\n\t}\n\n\t/**\n\t * Get the key scheme of the keypair: Secp256k1 or ED25519\n\t */\n\tabstract getKeyScheme(): SignatureScheme;\n\n\t/**\n\t * The public key for this keypair\n\t */\n\tabstract getPublicKey(): PublicKey;\n}\n\nexport abstract class Keypair extends Signer {\n\t/**\n\t * This returns the Bech32 secret key string for this keypair.\n\t */\n\tabstract getSecretKey(): string;\n}\n\n/**\n * This returns an ParsedKeypair object based by validating the\n * 33-byte Bech32 encoded string starting with `suiprivkey`, and\n * parse out the signature scheme and the private key in bytes.\n */\nexport function decodeSuiPrivateKey(value: string): ParsedKeypair {\n\tconst { prefix, words } = bech32.decode(value as `${string}1${string}`);\n\tif (prefix !== SUI_PRIVATE_KEY_PREFIX) {\n\t\tthrow new Error('invalid private key prefix');\n\t}\n\tconst extendedSecretKey = new Uint8Array(bech32.fromWords(words));\n\tconst secretKey = extendedSecretKey.slice(1);\n\tconst signatureScheme =\n\t\tSIGNATURE_FLAG_TO_SCHEME[extendedSecretKey[0] as keyof typeof SIGNATURE_FLAG_TO_SCHEME];\n\n\treturn {\n\t\tscheme: signatureScheme,\n\t\tschema: signatureScheme,\n\t\tsecretKey: secretKey,\n\t};\n}\n\n/**\n * This returns a Bech32 encoded string starting with `suiprivkey`,\n * encoding 33-byte `flag || bytes` for the given the 32-byte private\n * key and its signature scheme.\n */\nexport function encodeSuiPrivateKey(bytes: Uint8Array, scheme: SignatureScheme): string {\n\tif (bytes.length !== PRIVATE_KEY_SIZE) {\n\t\tthrow new Error('Invalid bytes length');\n\t}\n\tconst flag = SIGNATURE_SCHEME_TO_FLAG[scheme];\n\tconst privKeyBytes = new Uint8Array(bytes.length + 1);\n\tprivKeyBytes.set([flag]);\n\tprivKeyBytes.set(bytes, 1);\n\treturn bech32.encode(SUI_PRIVATE_KEY_PREFIX, bech32.toWords(privKeyBytes));\n}\n"], "mappings": "AAGA,SAAS,KAAK,gBAAgB;AAC9B,SAAS,eAAe;AACxB,SAAS,cAAc;AAGvB,SAAS,yBAAyB;AAElC,SAAS,0BAA0B,gCAAgC;AAEnE,SAAS,6BAA6B;AAI/B,MAAM,mBAAmB;AACzB,MAAM,0BAA0B;AAChC,MAAM,yBAAyB;AAsB/B,MAAe,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAM5B,MAAM,eAAe,OAAmB,QAAkD;AACzF,UAAM,gBAAgB,kBAAkB,QAAQ,KAAK;AACrD,UAAM,SAAS,QAAQ,eAAe,EAAE,OAAO,GAAG,CAAC;AAEnD,UAAM,YAAY,sBAAsB;AAAA,MACvC,WAAW,MAAM,KAAK,KAAK,MAAM;AAAA,MACjC,iBAAiB,KAAK,aAAa;AAAA,MACnC,WAAW,KAAK,aAAa;AAAA,IAC9B,CAAC;AAED,WAAO;AAAA,MACN;AAAA,MACA,OAAO,SAAS,KAAK;AAAA,IACtB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,gBAAgB,OAAmB;AACxC,WAAO,KAAK,eAAe,OAAO,iBAAiB;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,oBAAoB,OAAmB;AAC5C,UAAM,EAAE,UAAU,IAAI,MAAM,KAAK;AAAA,MAChC,IAAI,OAAO,IAAI,GAAG,CAAC,EAAE,UAAU,KAAK,EAAE,QAAQ;AAAA,MAC9C;AAAA,IACD;AAEA,WAAO;AAAA,MACN,OAAO,SAAS,KAAK;AAAA,MACrB;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,0BAA0B;AAAA,IAC/B;AAAA,IACA;AAAA,EACD,GAAoF;AACnF,UAAM,QAAQ,MAAM,YAAY,MAAM,EAAE,OAAO,CAAC;AAChD,UAAM,EAAE,UAAU,IAAI,MAAM,KAAK,gBAAgB,KAAK;AACtD,UAAM,WAAW,MAAM,OAAO,KAAK,mBAAmB;AAAA,MACrD,aAAa;AAAA,MACb,YAAY,CAAC,SAAS;AAAA,IACvB,CAAC;AAED,WAAO,SAAS;AAAA,EACjB;AAAA,EAEA,eAAuB;AACtB,WAAO,KAAK,aAAa,EAAE,aAAa;AAAA,EACzC;AAWD;AAEO,MAAe,gBAAgB,OAAO;AAK7C;AAOO,SAAS,oBAAoB,OAA8B;AACjE,QAAM,EAAE,QAAQ,MAAM,IAAI,OAAO,OAAO,KAA8B;AACtE,MAAI,WAAW,wBAAwB;AACtC,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC7C;AACA,QAAM,oBAAoB,IAAI,WAAW,OAAO,UAAU,KAAK,CAAC;AAChE,QAAM,YAAY,kBAAkB,MAAM,CAAC;AAC3C,QAAM,kBACL,yBAAyB,kBAAkB,CAAC,CAA0C;AAEvF,SAAO;AAAA,IACN,QAAQ;AAAA,IACR,QAAQ;AAAA,IACR;AAAA,EACD;AACD;AAOO,SAAS,oBAAoB,OAAmB,QAAiC;AACvF,MAAI,MAAM,WAAW,kBAAkB;AACtC,UAAM,IAAI,MAAM,sBAAsB;AAAA,EACvC;AACA,QAAM,OAAO,yBAAyB,MAAM;AAC5C,QAAM,eAAe,IAAI,WAAW,MAAM,SAAS,CAAC;AACpD,eAAa,IAAI,CAAC,IAAI,CAAC;AACvB,eAAa,IAAI,OAAO,CAAC;AACzB,SAAO,OAAO,OAAO,wBAAwB,OAAO,QAAQ,YAAY,CAAC;AAC1E;", "names": []}