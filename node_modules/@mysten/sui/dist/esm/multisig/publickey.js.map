{"version": 3, "sources": ["../../../src/multisig/publickey.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { fromBase64, toBase64 } from '@mysten/bcs';\nimport { blake2b } from '@noble/hashes/blake2b';\nimport { bytesToHex } from '@noble/hashes/utils';\n\nimport { bcs } from '../bcs/index.js';\nimport type { Signer } from '../cryptography/keypair.js';\nimport { bytesEqual, PublicKey } from '../cryptography/publickey.js';\nimport {\n\tSIGNATURE_FLAG_TO_SCHEME,\n\tSIGNATURE_SCHEME_TO_FLAG,\n} from '../cryptography/signature-scheme.js';\nimport type { SignatureFlag, SignatureScheme } from '../cryptography/signature-scheme.js';\nimport { parseSerializedSignature } from '../cryptography/signature.js';\nimport { normalizeSuiAddress } from '../utils/sui-types.js';\n// eslint-disable-next-line import/no-cycle\nimport { publicKeyFromRawBytes } from '../verify/index.js';\nimport type { ZkLoginCompatibleClient } from '../zklogin/publickey.js';\nimport { toZkLoginPublicIdentifier } from '../zklogin/publickey.js';\nimport { MultiSigSigner } from './signer.js';\n\ntype CompressedSignature =\n\t| { ED25519: number[] }\n\t| { Secp256k1: number[] }\n\t| { Secp256r1: number[] }\n\t| { ZkLogin: number[] };\n\ntype PublicKeyEnum =\n\t| { ED25519: number[] }\n\t| { Secp256k1: number[] }\n\t| { Secp256r1: number[] }\n\t| { ZkLogin: number[] };\n\ntype PubkeyEnumWeightPair = {\n\tpubKey: PublicKeyEnum;\n\tweight: number;\n};\n\ntype MultiSigPublicKeyStruct = {\n\tpk_map: PubkeyEnumWeightPair[];\n\tthreshold: number;\n};\n\nexport type MultiSigStruct = {\n\tsigs: CompressedSignature[];\n\tbitmap: number;\n\tmultisig_pk: MultiSigPublicKeyStruct;\n};\n\ntype ParsedPartialMultiSigSignature = {\n\tsignatureScheme: SignatureScheme;\n\tsignature: Uint8Array;\n\tpublicKey: PublicKey;\n\tweight: number;\n};\n\nexport const MAX_SIGNER_IN_MULTISIG = 10;\nexport const MIN_SIGNER_IN_MULTISIG = 1;\n/**\n * A MultiSig public key\n */\nexport class MultiSigPublicKey extends PublicKey {\n\tprivate rawBytes: Uint8Array;\n\tprivate multisigPublicKey: MultiSigPublicKeyStruct;\n\tprivate publicKeys: {\n\t\tweight: number;\n\t\tpublicKey: PublicKey;\n\t}[];\n\t/**\n\t * Create a new MultiSigPublicKey object\n\t */\n\tconstructor(\n\t\t/**\n\t\t *  MultiSig public key as buffer or base-64 encoded string\n\t\t */\n\t\tvalue: string | Uint8Array | MultiSigPublicKeyStruct,\n\t\toptions: { client?: ZkLoginCompatibleClient } = {},\n\t) {\n\t\tsuper();\n\n\t\tif (typeof value === 'string') {\n\t\t\tthis.rawBytes = fromBase64(value);\n\n\t\t\tthis.multisigPublicKey = bcs.MultiSigPublicKey.parse(this.rawBytes);\n\t\t} else if (value instanceof Uint8Array) {\n\t\t\tthis.rawBytes = value;\n\t\t\tthis.multisigPublicKey = bcs.MultiSigPublicKey.parse(this.rawBytes);\n\t\t} else {\n\t\t\tthis.multisigPublicKey = value;\n\t\t\tthis.rawBytes = bcs.MultiSigPublicKey.serialize(value).toBytes();\n\t\t}\n\t\tif (this.multisigPublicKey.threshold < 1) {\n\t\t\tthrow new Error('Invalid threshold');\n\t\t}\n\n\t\tconst seenPublicKeys = new Set<string>();\n\n\t\tthis.publicKeys = this.multisigPublicKey.pk_map.map(({ pubKey, weight }) => {\n\t\t\tconst [scheme, bytes] = Object.entries(pubKey).filter(([name]) => name !== '$kind')[0] as [\n\t\t\t\tSignatureScheme,\n\t\t\t\tnumber[],\n\t\t\t];\n\t\t\tconst publicKeyStr = Uint8Array.from(bytes).toString();\n\n\t\t\tif (seenPublicKeys.has(publicKeyStr)) {\n\t\t\t\tthrow new Error(`Multisig does not support duplicate public keys`);\n\t\t\t}\n\t\t\tseenPublicKeys.add(publicKeyStr);\n\n\t\t\tif (weight < 1) {\n\t\t\t\tthrow new Error(`Invalid weight`);\n\t\t\t}\n\n\t\t\treturn {\n\t\t\t\tpublicKey: publicKeyFromRawBytes(scheme, Uint8Array.from(bytes), options),\n\t\t\t\tweight,\n\t\t\t};\n\t\t});\n\n\t\tconst totalWeight = this.publicKeys.reduce((sum, { weight }) => sum + weight, 0);\n\n\t\tif (this.multisigPublicKey.threshold > totalWeight) {\n\t\t\tthrow new Error(`Unreachable threshold`);\n\t\t}\n\n\t\tif (this.publicKeys.length > MAX_SIGNER_IN_MULTISIG) {\n\t\t\tthrow new Error(`Max number of signers in a multisig is ${MAX_SIGNER_IN_MULTISIG}`);\n\t\t}\n\n\t\tif (this.publicKeys.length < MIN_SIGNER_IN_MULTISIG) {\n\t\t\tthrow new Error(`Min number of signers in a multisig is ${MIN_SIGNER_IN_MULTISIG}`);\n\t\t}\n\t}\n\t/**\n\t * \tA static method to create a new MultiSig publickey instance from a set of public keys and their associated weights pairs and threshold.\n\t */\n\n\tstatic fromPublicKeys({\n\t\tthreshold,\n\t\tpublicKeys,\n\t}: {\n\t\tthreshold: number;\n\t\tpublicKeys: { publicKey: PublicKey; weight: number }[];\n\t}) {\n\t\treturn new MultiSigPublicKey({\n\t\t\tpk_map: publicKeys.map(({ publicKey, weight }) => {\n\t\t\t\tconst scheme = SIGNATURE_FLAG_TO_SCHEME[publicKey.flag() as SignatureFlag];\n\n\t\t\t\treturn {\n\t\t\t\t\tpubKey: { [scheme]: Array.from(publicKey.toRawBytes()) } as PublicKeyEnum,\n\t\t\t\t\tweight,\n\t\t\t\t};\n\t\t\t}),\n\t\t\tthreshold,\n\t\t});\n\t}\n\n\t/**\n\t * Checks if two MultiSig public keys are equal\n\t */\n\toverride equals(publicKey: MultiSigPublicKey): boolean {\n\t\treturn super.equals(publicKey);\n\t}\n\n\t/**\n\t * Return the byte array representation of the MultiSig public key\n\t */\n\ttoRawBytes(): Uint8Array {\n\t\treturn this.rawBytes;\n\t}\n\n\tgetPublicKeys() {\n\t\treturn this.publicKeys;\n\t}\n\n\tgetThreshold() {\n\t\treturn this.multisigPublicKey.threshold;\n\t}\n\n\tgetSigner(...signers: [signer: Signer]) {\n\t\treturn new MultiSigSigner(this, signers);\n\t}\n\n\t/**\n\t * Return the Sui address associated with this MultiSig public key\n\t */\n\toverride toSuiAddress(): string {\n\t\t// max length = 1 flag byte + (max pk size + max weight size (u8)) * max signer size + 2 threshold bytes (u16)\n\t\tconst maxLength = 1 + (64 + 1) * MAX_SIGNER_IN_MULTISIG + 2;\n\t\tconst tmp = new Uint8Array(maxLength);\n\t\ttmp.set([SIGNATURE_SCHEME_TO_FLAG['MultiSig']]);\n\n\t\ttmp.set(bcs.u16().serialize(this.multisigPublicKey.threshold).toBytes(), 1);\n\t\t// The initial value 3 ensures that following data will be after the flag byte and threshold bytes\n\t\tlet i = 3;\n\t\tfor (const { publicKey, weight } of this.publicKeys) {\n\t\t\tconst bytes = publicKey.toSuiBytes();\n\t\t\ttmp.set(bytes, i);\n\t\t\ti += bytes.length;\n\t\t\ttmp.set([weight], i++);\n\t\t}\n\t\treturn normalizeSuiAddress(bytesToHex(blake2b(tmp.slice(0, i), { dkLen: 32 })));\n\t}\n\n\t/**\n\t * Return the Sui address associated with this MultiSig public key\n\t */\n\tflag(): number {\n\t\treturn SIGNATURE_SCHEME_TO_FLAG['MultiSig'];\n\t}\n\n\t/**\n\t * Verifies that the signature is valid for for the provided message\n\t */\n\tasync verify(message: Uint8Array, multisigSignature: string): Promise<boolean> {\n\t\t// Multisig verification only supports serialized signature\n\t\tconst parsed = parseSerializedSignature(multisigSignature);\n\n\t\tif (parsed.signatureScheme !== 'MultiSig') {\n\t\t\tthrow new Error('Invalid signature scheme');\n\t\t}\n\n\t\tconst { multisig } = parsed;\n\n\t\tlet signatureWeight = 0;\n\n\t\tif (\n\t\t\t!bytesEqual(\n\t\t\t\tbcs.MultiSigPublicKey.serialize(this.multisigPublicKey).toBytes(),\n\t\t\t\tbcs.MultiSigPublicKey.serialize(multisig.multisig_pk).toBytes(),\n\t\t\t)\n\t\t) {\n\t\t\treturn false;\n\t\t}\n\n\t\tfor (const { publicKey, weight, signature } of parsePartialSignatures(multisig)) {\n\t\t\tif (!(await publicKey.verify(message, signature))) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\tsignatureWeight += weight;\n\t\t}\n\n\t\treturn signatureWeight >= this.multisigPublicKey.threshold;\n\t}\n\n\t/**\n\t * Combines multiple partial signatures into a single multisig, ensuring that each public key signs only once\n\t * and that all the public keys involved are known and valid, and then serializes multisig into the standard format\n\t */\n\tcombinePartialSignatures(signatures: string[]): string {\n\t\tif (signatures.length > MAX_SIGNER_IN_MULTISIG) {\n\t\t\tthrow new Error(`Max number of signatures in a multisig is ${MAX_SIGNER_IN_MULTISIG}`);\n\t\t}\n\n\t\tlet bitmap = 0;\n\t\tconst compressedSignatures: CompressedSignature[] = new Array(signatures.length);\n\n\t\tfor (let i = 0; i < signatures.length; i++) {\n\t\t\tconst parsed = parseSerializedSignature(signatures[i]);\n\t\t\tif (parsed.signatureScheme === 'MultiSig') {\n\t\t\t\tthrow new Error('MultiSig is not supported inside MultiSig');\n\t\t\t}\n\n\t\t\tlet publicKey;\n\t\t\tif (parsed.signatureScheme === 'ZkLogin') {\n\t\t\t\tpublicKey = toZkLoginPublicIdentifier(\n\t\t\t\t\tparsed.zkLogin?.addressSeed,\n\t\t\t\t\tparsed.zkLogin?.iss,\n\t\t\t\t).toRawBytes();\n\t\t\t} else {\n\t\t\t\tpublicKey = parsed.publicKey;\n\t\t\t}\n\n\t\t\tcompressedSignatures[i] = {\n\t\t\t\t[parsed.signatureScheme]: Array.from(parsed.signature.map((x: number) => Number(x))),\n\t\t\t} as CompressedSignature;\n\n\t\t\tlet publicKeyIndex;\n\t\t\tfor (let j = 0; j < this.publicKeys.length; j++) {\n\t\t\t\tif (bytesEqual(publicKey, this.publicKeys[j].publicKey.toRawBytes())) {\n\t\t\t\t\tif (bitmap & (1 << j)) {\n\t\t\t\t\t\tthrow new Error('Received multiple signatures from the same public key');\n\t\t\t\t\t}\n\n\t\t\t\t\tpublicKeyIndex = j;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (publicKeyIndex === undefined) {\n\t\t\t\tthrow new Error('Received signature from unknown public key');\n\t\t\t}\n\n\t\t\tbitmap |= 1 << publicKeyIndex;\n\t\t}\n\n\t\tconst multisig: MultiSigStruct = {\n\t\t\tsigs: compressedSignatures,\n\t\t\tbitmap,\n\t\t\tmultisig_pk: this.multisigPublicKey,\n\t\t};\n\t\tconst bytes = bcs.MultiSig.serialize(multisig, { maxSize: 8192 }).toBytes();\n\t\tconst tmp = new Uint8Array(bytes.length + 1);\n\t\ttmp.set([SIGNATURE_SCHEME_TO_FLAG['MultiSig']]);\n\t\ttmp.set(bytes, 1);\n\t\treturn toBase64(tmp);\n\t}\n}\n\n/**\n * Parse multisig structure into an array of individual signatures: signature scheme, the actual individual signature, public key and its weight.\n */\nexport function parsePartialSignatures(\n\tmultisig: MultiSigStruct,\n\toptions: { client?: ZkLoginCompatibleClient } = {},\n): ParsedPartialMultiSigSignature[] {\n\tconst res: ParsedPartialMultiSigSignature[] = new Array(multisig.sigs.length);\n\tfor (let i = 0; i < multisig.sigs.length; i++) {\n\t\tconst [signatureScheme, signature] = Object.entries(multisig.sigs[i]).filter(\n\t\t\t([name]) => name !== '$kind',\n\t\t)[0] as [SignatureScheme, number[]];\n\t\tconst pkIndex = asIndices(multisig.bitmap).at(i)!;\n\t\tconst pair = multisig.multisig_pk.pk_map[pkIndex];\n\t\tconst pkBytes = Uint8Array.from(Object.values(pair.pubKey)[0]);\n\n\t\tif (signatureScheme === 'MultiSig') {\n\t\t\tthrow new Error('MultiSig is not supported inside MultiSig');\n\t\t}\n\n\t\tconst publicKey = publicKeyFromRawBytes(signatureScheme, pkBytes, options);\n\n\t\tres[i] = {\n\t\t\tsignatureScheme,\n\t\t\tsignature: Uint8Array.from(signature),\n\t\t\tpublicKey: publicKey,\n\t\t\tweight: pair.weight,\n\t\t};\n\t}\n\treturn res;\n}\n\nfunction asIndices(bitmap: number): Uint8Array {\n\tif (bitmap < 0 || bitmap > 1024) {\n\t\tthrow new Error('Invalid bitmap');\n\t}\n\tconst res: number[] = [];\n\tfor (let i = 0; i < 10; i++) {\n\t\tif ((bitmap & (1 << i)) !== 0) {\n\t\t\tres.push(i);\n\t\t}\n\t}\n\treturn Uint8Array.from(res);\n}\n"], "mappings": "AAGA,SAAS,YAAY,gBAAgB;AACrC,SAAS,eAAe;AACxB,SAAS,kBAAkB;AAE3B,SAAS,WAAW;AAEpB,SAAS,YAAY,iBAAiB;AACtC;AAAA,EACC;AAAA,EACA;AAAA,OACM;AAEP,SAAS,gCAAgC;AACzC,SAAS,2BAA2B;AAEpC,SAAS,6BAA6B;AAEtC,SAAS,iCAAiC;AAC1C,SAAS,sBAAsB;AAqCxB,MAAM,yBAAyB;AAC/B,MAAM,yBAAyB;AAI/B,MAAM,0BAA0B,UAAU;AAAA;AAAA;AAAA;AAAA,EAUhD,YAIC,OACA,UAAgD,CAAC,GAChD;AACD,UAAM;AAEN,QAAI,OAAO,UAAU,UAAU;AAC9B,WAAK,WAAW,WAAW,KAAK;AAEhC,WAAK,oBAAoB,IAAI,kBAAkB,MAAM,KAAK,QAAQ;AAAA,IACnE,WAAW,iBAAiB,YAAY;AACvC,WAAK,WAAW;AAChB,WAAK,oBAAoB,IAAI,kBAAkB,MAAM,KAAK,QAAQ;AAAA,IACnE,OAAO;AACN,WAAK,oBAAoB;AACzB,WAAK,WAAW,IAAI,kBAAkB,UAAU,KAAK,EAAE,QAAQ;AAAA,IAChE;AACA,QAAI,KAAK,kBAAkB,YAAY,GAAG;AACzC,YAAM,IAAI,MAAM,mBAAmB;AAAA,IACpC;AAEA,UAAM,iBAAiB,oBAAI,IAAY;AAEvC,SAAK,aAAa,KAAK,kBAAkB,OAAO,IAAI,CAAC,EAAE,QAAQ,OAAO,MAAM;AAC3E,YAAM,CAAC,QAAQ,KAAK,IAAI,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,CAAC,IAAI,MAAM,SAAS,OAAO,EAAE,CAAC;AAIrF,YAAM,eAAe,WAAW,KAAK,KAAK,EAAE,SAAS;AAErD,UAAI,eAAe,IAAI,YAAY,GAAG;AACrC,cAAM,IAAI,MAAM,iDAAiD;AAAA,MAClE;AACA,qBAAe,IAAI,YAAY;AAE/B,UAAI,SAAS,GAAG;AACf,cAAM,IAAI,MAAM,gBAAgB;AAAA,MACjC;AAEA,aAAO;AAAA,QACN,WAAW,sBAAsB,QAAQ,WAAW,KAAK,KAAK,GAAG,OAAO;AAAA,QACxE;AAAA,MACD;AAAA,IACD,CAAC;AAED,UAAM,cAAc,KAAK,WAAW,OAAO,CAAC,KAAK,EAAE,OAAO,MAAM,MAAM,QAAQ,CAAC;AAE/E,QAAI,KAAK,kBAAkB,YAAY,aAAa;AACnD,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACxC;AAEA,QAAI,KAAK,WAAW,SAAS,wBAAwB;AACpD,YAAM,IAAI,MAAM,0CAA0C,sBAAsB,EAAE;AAAA,IACnF;AAEA,QAAI,KAAK,WAAW,SAAS,wBAAwB;AACpD,YAAM,IAAI,MAAM,0CAA0C,sBAAsB,EAAE;AAAA,IACnF;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,eAAe;AAAA,IACrB;AAAA,IACA;AAAA,EACD,GAGG;AACF,WAAO,IAAI,kBAAkB;AAAA,MAC5B,QAAQ,WAAW,IAAI,CAAC,EAAE,WAAW,OAAO,MAAM;AACjD,cAAM,SAAS,yBAAyB,UAAU,KAAK,CAAkB;AAEzE,eAAO;AAAA,UACN,QAAQ,EAAE,CAAC,MAAM,GAAG,MAAM,KAAK,UAAU,WAAW,CAAC,EAAE;AAAA,UACvD;AAAA,QACD;AAAA,MACD,CAAC;AAAA,MACD;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKS,OAAO,WAAuC;AACtD,WAAO,MAAM,OAAO,SAAS;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,aAAyB;AACxB,WAAO,KAAK;AAAA,EACb;AAAA,EAEA,gBAAgB;AACf,WAAO,KAAK;AAAA,EACb;AAAA,EAEA,eAAe;AACd,WAAO,KAAK,kBAAkB;AAAA,EAC/B;AAAA,EAEA,aAAa,SAA2B;AACvC,WAAO,IAAI,eAAe,MAAM,OAAO;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAKS,eAAuB;AAE/B,UAAM,YAAY,KAAK,KAAK,KAAK,yBAAyB;AAC1D,UAAM,MAAM,IAAI,WAAW,SAAS;AACpC,QAAI,IAAI,CAAC,yBAAyB,UAAU,CAAC,CAAC;AAE9C,QAAI,IAAI,IAAI,IAAI,EAAE,UAAU,KAAK,kBAAkB,SAAS,EAAE,QAAQ,GAAG,CAAC;AAE1E,QAAI,IAAI;AACR,eAAW,EAAE,WAAW,OAAO,KAAK,KAAK,YAAY;AACpD,YAAM,QAAQ,UAAU,WAAW;AACnC,UAAI,IAAI,OAAO,CAAC;AAChB,WAAK,MAAM;AACX,UAAI,IAAI,CAAC,MAAM,GAAG,GAAG;AAAA,IACtB;AACA,WAAO,oBAAoB,WAAW,QAAQ,IAAI,MAAM,GAAG,CAAC,GAAG,EAAE,OAAO,GAAG,CAAC,CAAC,CAAC;AAAA,EAC/E;AAAA;AAAA;AAAA;AAAA,EAKA,OAAe;AACd,WAAO,yBAAyB,UAAU;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO,SAAqB,mBAA6C;AAE9E,UAAM,SAAS,yBAAyB,iBAAiB;AAEzD,QAAI,OAAO,oBAAoB,YAAY;AAC1C,YAAM,IAAI,MAAM,0BAA0B;AAAA,IAC3C;AAEA,UAAM,EAAE,SAAS,IAAI;AAErB,QAAI,kBAAkB;AAEtB,QACC,CAAC;AAAA,MACA,IAAI,kBAAkB,UAAU,KAAK,iBAAiB,EAAE,QAAQ;AAAA,MAChE,IAAI,kBAAkB,UAAU,SAAS,WAAW,EAAE,QAAQ;AAAA,IAC/D,GACC;AACD,aAAO;AAAA,IACR;AAEA,eAAW,EAAE,WAAW,QAAQ,UAAU,KAAK,uBAAuB,QAAQ,GAAG;AAChF,UAAI,CAAE,MAAM,UAAU,OAAO,SAAS,SAAS,GAAI;AAClD,eAAO;AAAA,MACR;AAEA,yBAAmB;AAAA,IACpB;AAEA,WAAO,mBAAmB,KAAK,kBAAkB;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,yBAAyB,YAA8B;AACtD,QAAI,WAAW,SAAS,wBAAwB;AAC/C,YAAM,IAAI,MAAM,6CAA6C,sBAAsB,EAAE;AAAA,IACtF;AAEA,QAAI,SAAS;AACb,UAAM,uBAA8C,IAAI,MAAM,WAAW,MAAM;AAE/E,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AAC3C,YAAM,SAAS,yBAAyB,WAAW,CAAC,CAAC;AACrD,UAAI,OAAO,oBAAoB,YAAY;AAC1C,cAAM,IAAI,MAAM,2CAA2C;AAAA,MAC5D;AAEA,UAAI;AACJ,UAAI,OAAO,oBAAoB,WAAW;AACzC,oBAAY;AAAA,UACX,OAAO,SAAS;AAAA,UAChB,OAAO,SAAS;AAAA,QACjB,EAAE,WAAW;AAAA,MACd,OAAO;AACN,oBAAY,OAAO;AAAA,MACpB;AAEA,2BAAqB,CAAC,IAAI;AAAA,QACzB,CAAC,OAAO,eAAe,GAAG,MAAM,KAAK,OAAO,UAAU,IAAI,CAAC,MAAc,OAAO,CAAC,CAAC,CAAC;AAAA,MACpF;AAEA,UAAI;AACJ,eAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAChD,YAAI,WAAW,WAAW,KAAK,WAAW,CAAC,EAAE,UAAU,WAAW,CAAC,GAAG;AACrE,cAAI,SAAU,KAAK,GAAI;AACtB,kBAAM,IAAI,MAAM,uDAAuD;AAAA,UACxE;AAEA,2BAAiB;AACjB;AAAA,QACD;AAAA,MACD;AAEA,UAAI,mBAAmB,QAAW;AACjC,cAAM,IAAI,MAAM,4CAA4C;AAAA,MAC7D;AAEA,gBAAU,KAAK;AAAA,IAChB;AAEA,UAAM,WAA2B;AAAA,MAChC,MAAM;AAAA,MACN;AAAA,MACA,aAAa,KAAK;AAAA,IACnB;AACA,UAAM,QAAQ,IAAI,SAAS,UAAU,UAAU,EAAE,SAAS,KAAK,CAAC,EAAE,QAAQ;AAC1E,UAAM,MAAM,IAAI,WAAW,MAAM,SAAS,CAAC;AAC3C,QAAI,IAAI,CAAC,yBAAyB,UAAU,CAAC,CAAC;AAC9C,QAAI,IAAI,OAAO,CAAC;AAChB,WAAO,SAAS,GAAG;AAAA,EACpB;AACD;AAKO,SAAS,uBACf,UACA,UAAgD,CAAC,GACd;AACnC,QAAM,MAAwC,IAAI,MAAM,SAAS,KAAK,MAAM;AAC5E,WAAS,IAAI,GAAG,IAAI,SAAS,KAAK,QAAQ,KAAK;AAC9C,UAAM,CAAC,iBAAiB,SAAS,IAAI,OAAO,QAAQ,SAAS,KAAK,CAAC,CAAC,EAAE;AAAA,MACrE,CAAC,CAAC,IAAI,MAAM,SAAS;AAAA,IACtB,EAAE,CAAC;AACH,UAAM,UAAU,UAAU,SAAS,MAAM,EAAE,GAAG,CAAC;AAC/C,UAAM,OAAO,SAAS,YAAY,OAAO,OAAO;AAChD,UAAM,UAAU,WAAW,KAAK,OAAO,OAAO,KAAK,MAAM,EAAE,CAAC,CAAC;AAE7D,QAAI,oBAAoB,YAAY;AACnC,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC5D;AAEA,UAAM,YAAY,sBAAsB,iBAAiB,SAAS,OAAO;AAEzE,QAAI,CAAC,IAAI;AAAA,MACR;AAAA,MACA,WAAW,WAAW,KAAK,SAAS;AAAA,MACpC;AAAA,MACA,QAAQ,KAAK;AAAA,IACd;AAAA,EACD;AACA,SAAO;AACR;AAEA,SAAS,UAAU,QAA4B;AAC9C,MAAI,SAAS,KAAK,SAAS,MAAM;AAChC,UAAM,IAAI,MAAM,gBAAgB;AAAA,EACjC;AACA,QAAM,MAAgB,CAAC;AACvB,WAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,SAAK,SAAU,KAAK,OAAQ,GAAG;AAC9B,UAAI,KAAK,CAAC;AAAA,IACX;AAAA,EACD;AACA,SAAO,WAAW,KAAK,GAAG;AAC3B;", "names": []}