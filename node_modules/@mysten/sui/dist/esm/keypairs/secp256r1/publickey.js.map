{"version": 3, "sources": ["../../../../src/keypairs/secp256r1/publickey.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { fromBase64 } from '@mysten/bcs';\nimport { secp256r1 } from '@noble/curves/p256';\nimport { sha256 } from '@noble/hashes/sha256';\n\nimport { bytesEqual, PublicKey } from '../../cryptography/publickey.js';\nimport type { PublicKeyInitData } from '../../cryptography/publickey.js';\nimport { SIGNATURE_SCHEME_TO_FLAG } from '../../cryptography/signature-scheme.js';\nimport { parseSerializedSignature } from '../../cryptography/signature.js';\n\nconst SECP256R1_PUBLIC_KEY_SIZE = 33;\n\n/**\n * A Secp256r1 public key\n */\nexport class Secp256r1PublicKey extends PublicKey {\n\tstatic SIZE = SECP256R1_PUBLIC_KEY_SIZE;\n\tprivate data: Uint8Array;\n\n\t/**\n\t * Create a new Secp256r1PublicKey object\n\t * @param value secp256r1 public key as buffer or base-64 encoded string\n\t */\n\tconstructor(value: PublicKeyInitData) {\n\t\tsuper();\n\n\t\tif (typeof value === 'string') {\n\t\t\tthis.data = fromBase64(value);\n\t\t} else if (value instanceof Uint8Array) {\n\t\t\tthis.data = value;\n\t\t} else {\n\t\t\tthis.data = Uint8Array.from(value);\n\t\t}\n\n\t\tif (this.data.length !== SECP256R1_PUBLIC_KEY_SIZE) {\n\t\t\tthrow new Error(\n\t\t\t\t`Invalid public key input. Expected ${SECP256R1_PUBLIC_KEY_SIZE} bytes, got ${this.data.length}`,\n\t\t\t);\n\t\t}\n\t}\n\n\t/**\n\t * Checks if two Secp256r1 public keys are equal\n\t */\n\toverride equals(publicKey: Secp256r1PublicKey): boolean {\n\t\treturn super.equals(publicKey);\n\t}\n\n\t/**\n\t * Return the byte array representation of the Secp256r1 public key\n\t */\n\ttoRawBytes(): Uint8Array {\n\t\treturn this.data;\n\t}\n\n\t/**\n\t * Return the Sui address associated with this Secp256r1 public key\n\t */\n\tflag(): number {\n\t\treturn SIGNATURE_SCHEME_TO_FLAG['Secp256r1'];\n\t}\n\n\t/**\n\t * Verifies that the signature is valid for for the provided message\n\t */\n\tasync verify(message: Uint8Array, signature: Uint8Array | string): Promise<boolean> {\n\t\tlet bytes;\n\t\tif (typeof signature === 'string') {\n\t\t\tconst parsed = parseSerializedSignature(signature);\n\t\t\tif (parsed.signatureScheme !== 'Secp256r1') {\n\t\t\t\tthrow new Error('Invalid signature scheme');\n\t\t\t}\n\n\t\t\tif (!bytesEqual(this.toRawBytes(), parsed.publicKey)) {\n\t\t\t\tthrow new Error('Signature does not match public key');\n\t\t\t}\n\n\t\t\tbytes = parsed.signature;\n\t\t} else {\n\t\t\tbytes = signature;\n\t\t}\n\n\t\treturn secp256r1.verify(\n\t\t\tsecp256r1.Signature.fromCompact(bytes),\n\t\t\tsha256(message),\n\t\t\tthis.toRawBytes(),\n\t\t);\n\t}\n}\n"], "mappings": "AAGA,SAAS,kBAAkB;AAC3B,SAAS,iBAAiB;AAC1B,SAAS,cAAc;AAEvB,SAAS,YAAY,iBAAiB;AAEtC,SAAS,gCAAgC;AACzC,SAAS,gCAAgC;AAEzC,MAAM,4BAA4B;AAK3B,MAAM,2BAA2B,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAQjD,YAAY,OAA0B;AACrC,UAAM;AAEN,QAAI,OAAO,UAAU,UAAU;AAC9B,WAAK,OAAO,WAAW,KAAK;AAAA,IAC7B,WAAW,iBAAiB,YAAY;AACvC,WAAK,OAAO;AAAA,IACb,OAAO;AACN,WAAK,OAAO,WAAW,KAAK,KAAK;AAAA,IAClC;AAEA,QAAI,KAAK,KAAK,WAAW,2BAA2B;AACnD,YAAM,IAAI;AAAA,QACT,sCAAsC,yBAAyB,eAAe,KAAK,KAAK,MAAM;AAAA,MAC/F;AAAA,IACD;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKS,OAAO,WAAwC;AACvD,WAAO,MAAM,OAAO,SAAS;AAAA,EAC9B;AAAA;AAAA;AAAA;AAAA,EAKA,aAAyB;AACxB,WAAO,KAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAKA,OAAe;AACd,WAAO,yBAAyB,WAAW;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO,SAAqB,WAAkD;AACnF,QAAI;AACJ,QAAI,OAAO,cAAc,UAAU;AAClC,YAAM,SAAS,yBAAyB,SAAS;AACjD,UAAI,OAAO,oBAAoB,aAAa;AAC3C,cAAM,IAAI,MAAM,0BAA0B;AAAA,MAC3C;AAEA,UAAI,CAAC,WAAW,KAAK,WAAW,GAAG,OAAO,SAAS,GAAG;AACrD,cAAM,IAAI,MAAM,qCAAqC;AAAA,MACtD;AAEA,cAAQ,OAAO;AAAA,IAChB,OAAO;AACN,cAAQ;AAAA,IACT;AAEA,WAAO,UAAU;AAAA,MAChB,UAAU,UAAU,YAAY,KAAK;AAAA,MACrC,OAAO,OAAO;AAAA,MACd,KAAK,WAAW;AAAA,IACjB;AAAA,EACD;AACD;AAzEa,mBACL,OAAO;", "names": []}