{"version": 3, "sources": ["../../../src/faucet/faucet.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nexport class FaucetRateLimitError extends Error {}\n\ntype FaucetCoinInfo = {\n\tamount: number;\n\tid: string;\n\ttransferTxDigest: string;\n};\n\ntype FaucetResponse = {\n\ttransferredGasObjects: FaucetCoinInfo[];\n\terror?: string | null;\n};\n\ntype BatchFaucetResponse = {\n\ttask?: string | null;\n\terror?: string | null;\n};\n\ntype BatchSendStatusType = {\n\tstatus: 'INPROGRESS' | 'SUCCEEDED' | 'DISCARDED';\n\ttransferred_gas_objects: { sent: FaucetCoinInfo[] };\n};\n\ntype BatchStatusFaucetResponse = {\n\tstatus: BatchSendStatusType;\n\terror?: string | null;\n};\n\ntype FaucetResponseV2 = {\n\tstatus: 'Success' | FaucetFailure;\n\tcoins_sent: FaucetCoinInfo[] | null;\n};\n\ntype FaucetFailure = {\n\tFailure: {\n\t\tinternal: string;\n\t};\n};\n\ntype FaucetRequest = {\n\thost: string;\n\tpath: string;\n\tbody?: Record<string, any>;\n\theaders?: HeadersInit;\n\tmethod: 'GET' | 'POST';\n};\n\nasync function faucetRequest<T>({ host, path, body, headers, method }: FaucetRequest): Promise<T> {\n\tconst endpoint = new URL(path, host).toString();\n\tconst res = await fetch(endpoint, {\n\t\tmethod,\n\t\tbody: body ? JSON.stringify(body) : undefined,\n\t\theaders: {\n\t\t\t'Content-Type': 'application/json',\n\t\t\t...(headers || {}),\n\t\t},\n\t});\n\n\tif (res.status === 429) {\n\t\tthrow new FaucetRateLimitError(\n\t\t\t`Too many requests from this client have been sent to the faucet. Please retry later`,\n\t\t);\n\t}\n\n\ttry {\n\t\tconst parsed = await res.json();\n\t\treturn parsed as T;\n\t} catch (e) {\n\t\tthrow new Error(\n\t\t\t`Encountered error when parsing response from faucet, error: ${e}, status ${res.status}, response ${res}`,\n\t\t);\n\t}\n}\n\n/**\n * @deprecated(\"Use requestSuiFromFaucetV2 instead\")\n */\nexport async function requestSuiFromFaucetV0(input: {\n\thost: string;\n\trecipient: string;\n\theaders?: HeadersInit;\n}): Promise<FaucetResponse> {\n\tconst response = await faucetRequest<FaucetResponse>({\n\t\thost: input.host,\n\t\tpath: '/gas',\n\t\tbody: {\n\t\t\tFixedAmountRequest: {\n\t\t\t\trecipient: input.recipient,\n\t\t\t},\n\t\t},\n\t\theaders: input.headers,\n\t\tmethod: 'POST',\n\t});\n\n\tif (response.error) {\n\t\tthrow new Error(`Faucet request failed: ${response.error}`);\n\t}\n\n\treturn response;\n}\n\n/**\n * @deprecated(\"Use requestSuiFromFaucetV2 instead\")\n */\nexport async function requestSuiFromFaucetV1(input: {\n\thost: string;\n\trecipient: string;\n\theaders?: HeadersInit;\n}): Promise<BatchFaucetResponse> {\n\tconst response = await faucetRequest<BatchFaucetResponse>({\n\t\thost: input.host,\n\t\tpath: '/v1/gas',\n\t\tbody: {\n\t\t\tFixedAmountRequest: {\n\t\t\t\trecipient: input.recipient,\n\t\t\t},\n\t\t},\n\t\theaders: input.headers,\n\t\tmethod: 'POST',\n\t});\n\n\tif (response.error) {\n\t\tthrow new Error(`Faucet request failed: ${response.error}`);\n\t}\n\n\treturn response;\n}\n\nexport async function requestSuiFromFaucetV2(input: {\n\thost: string;\n\trecipient: string;\n\theaders?: HeadersInit;\n}) {\n\tconst response = await faucetRequest<FaucetResponseV2>({\n\t\thost: input.host,\n\t\tpath: '/v2/gas',\n\t\tbody: {\n\t\t\tFixedAmountRequest: {\n\t\t\t\trecipient: input.recipient,\n\t\t\t},\n\t\t},\n\t\theaders: input.headers,\n\t\tmethod: 'POST',\n\t});\n\n\tif (response.status !== 'Success') {\n\t\tthrow new Error(`Faucet request failed: ${response.status.Failure.internal}`);\n\t}\n\n\treturn response;\n}\n\n/**\n * @deprecated(\"Use requestSuiFromFaucetV2 which returns directly a success or failure status\")\n */\nexport async function getFaucetRequestStatus(input: {\n\thost: string;\n\ttaskId: string;\n\theaders?: HeadersInit;\n}) {\n\tconst response = await faucetRequest<BatchStatusFaucetResponse>({\n\t\thost: input.host,\n\t\tpath: `/v1/status/${input.taskId}`,\n\t\theaders: input.headers,\n\t\tmethod: 'GET',\n\t});\n\n\tif (response.error) {\n\t\tthrow new Error(`Faucet request failed: ${response.error}`);\n\t}\n\n\treturn response;\n}\n\nexport function getFaucetHost(network: 'testnet' | 'devnet' | 'localnet') {\n\tswitch (network) {\n\t\tcase 'testnet':\n\t\t\treturn 'https://faucet.testnet.sui.io';\n\t\tcase 'devnet':\n\t\t\treturn 'https://faucet.devnet.sui.io';\n\t\tcase 'localnet':\n\t\t\treturn 'http://127.0.0.1:9123';\n\t\tdefault:\n\t\t\tthrow new Error(`Unknown network: ${network}`);\n\t}\n}\n"], "mappings": "AAGO,MAAM,6BAA6B,MAAM;AAAC;AA+CjD,eAAe,cAAiB,EAAE,MAAM,MAAM,MAAM,SAAS,OAAO,GAA8B;AACjG,QAAM,WAAW,IAAI,IAAI,MAAM,IAAI,EAAE,SAAS;AAC9C,QAAM,MAAM,MAAM,MAAM,UAAU;AAAA,IACjC;AAAA,IACA,MAAM,OAAO,KAAK,UAAU,IAAI,IAAI;AAAA,IACpC,SAAS;AAAA,MACR,gBAAgB;AAAA,MAChB,GAAI,WAAW,CAAC;AAAA,IACjB;AAAA,EACD,CAAC;AAED,MAAI,IAAI,WAAW,KAAK;AACvB,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,MAAI;AACH,UAAM,SAAS,MAAM,IAAI,KAAK;AAC9B,WAAO;AAAA,EACR,SAAS,GAAG;AACX,UAAM,IAAI;AAAA,MACT,+DAA+D,CAAC,YAAY,IAAI,MAAM,cAAc,GAAG;AAAA,IACxG;AAAA,EACD;AACD;AAKA,eAAsB,uBAAuB,OAIjB;AAC3B,QAAM,WAAW,MAAM,cAA8B;AAAA,IACpD,MAAM,MAAM;AAAA,IACZ,MAAM;AAAA,IACN,MAAM;AAAA,MACL,oBAAoB;AAAA,QACnB,WAAW,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS,MAAM;AAAA,IACf,QAAQ;AAAA,EACT,CAAC;AAED,MAAI,SAAS,OAAO;AACnB,UAAM,IAAI,MAAM,0BAA0B,SAAS,KAAK,EAAE;AAAA,EAC3D;AAEA,SAAO;AACR;AAKA,eAAsB,uBAAuB,OAIZ;AAChC,QAAM,WAAW,MAAM,cAAmC;AAAA,IACzD,MAAM,MAAM;AAAA,IACZ,MAAM;AAAA,IACN,MAAM;AAAA,MACL,oBAAoB;AAAA,QACnB,WAAW,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS,MAAM;AAAA,IACf,QAAQ;AAAA,EACT,CAAC;AAED,MAAI,SAAS,OAAO;AACnB,UAAM,IAAI,MAAM,0BAA0B,SAAS,KAAK,EAAE;AAAA,EAC3D;AAEA,SAAO;AACR;AAEA,eAAsB,uBAAuB,OAI1C;AACF,QAAM,WAAW,MAAM,cAAgC;AAAA,IACtD,MAAM,MAAM;AAAA,IACZ,MAAM;AAAA,IACN,MAAM;AAAA,MACL,oBAAoB;AAAA,QACnB,WAAW,MAAM;AAAA,MAClB;AAAA,IACD;AAAA,IACA,SAAS,MAAM;AAAA,IACf,QAAQ;AAAA,EACT,CAAC;AAED,MAAI,SAAS,WAAW,WAAW;AAClC,UAAM,IAAI,MAAM,0BAA0B,SAAS,OAAO,QAAQ,QAAQ,EAAE;AAAA,EAC7E;AAEA,SAAO;AACR;AAKA,eAAsB,uBAAuB,OAI1C;AACF,QAAM,WAAW,MAAM,cAAyC;AAAA,IAC/D,MAAM,MAAM;AAAA,IACZ,MAAM,cAAc,MAAM,MAAM;AAAA,IAChC,SAAS,MAAM;AAAA,IACf,QAAQ;AAAA,EACT,CAAC;AAED,MAAI,SAAS,OAAO;AACnB,UAAM,IAAI,MAAM,0BAA0B,SAAS,KAAK,EAAE;AAAA,EAC3D;AAEA,SAAO;AACR;AAEO,SAAS,cAAc,SAA4C;AACzE,UAAQ,SAAS;AAAA,IAChB,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO;AAAA,IACR,KAAK;AACJ,aAAO;AAAA,IACR;AACC,YAAM,IAAI,MAAM,oBAAoB,OAAO,EAAE;AAAA,EAC/C;AACD;", "names": []}