import type { TransactionPlugin } from '../transactions/index.js';
import { Experimental_BaseClient } from './client.js';
import type { ClientWithExtensions, Experimental_SuiClientTypes } from './types.js';
export type ClientWithCoreApi = ClientWithExtensions<{
    core: Experimental_CoreClient;
}>;
export interface Experimental_CoreClientOptions extends Experimental_SuiClientTypes.SuiClientOptions {
    base: Experimental_BaseClient;
    mvr?: Experimental_SuiClientTypes.MvrOptions;
}
export declare abstract class Experimental_CoreClient extends Experimental_BaseClient implements Experimental_SuiClientTypes.TransportMethods {
    core: this;
    mvr: Experimental_SuiClientTypes.MvrMethods;
    constructor(options: Experimental_CoreClientOptions);
    abstract getObjects(options: Experimental_SuiClientTypes.GetObjectsOptions): Promise<Experimental_SuiClientTypes.GetObjectsResponse>;
    getObject(options: Experimental_SuiClientTypes.GetObjectOptions): Promise<Experimental_SuiClientTypes.GetObjectResponse>;
    abstract getCoins(options: Experimental_SuiClientTypes.GetCoinsOptions): Promise<Experimental_SuiClientTypes.GetCoinsResponse>;
    abstract getOwnedObjects(options: Experimental_SuiClientTypes.GetOwnedObjectsOptions): Promise<Experimental_SuiClientTypes.GetOwnedObjectsResponse>;
    abstract getBalance(options: Experimental_SuiClientTypes.GetBalanceOptions): Promise<Experimental_SuiClientTypes.GetBalanceResponse>;
    abstract getAllBalances(options: Experimental_SuiClientTypes.GetAllBalancesOptions): Promise<Experimental_SuiClientTypes.GetAllBalancesResponse>;
    abstract getTransaction(options: Experimental_SuiClientTypes.GetTransactionOptions): Promise<Experimental_SuiClientTypes.GetTransactionResponse>;
    abstract executeTransaction(options: Experimental_SuiClientTypes.ExecuteTransactionOptions): Promise<Experimental_SuiClientTypes.ExecuteTransactionResponse>;
    abstract dryRunTransaction(options: Experimental_SuiClientTypes.DryRunTransactionOptions): Promise<Experimental_SuiClientTypes.DryRunTransactionResponse>;
    abstract getReferenceGasPrice(options?: Experimental_SuiClientTypes.GetReferenceGasPriceOptions): Promise<Experimental_SuiClientTypes.GetReferenceGasPriceResponse>;
    abstract getDynamicFields(options: Experimental_SuiClientTypes.GetDynamicFieldsOptions): Promise<Experimental_SuiClientTypes.GetDynamicFieldsResponse>;
    abstract resolveTransactionPlugin(): TransactionPlugin;
    abstract verifyZkLoginSignature(options: Experimental_SuiClientTypes.VerifyZkLoginSignatureOptions): Promise<Experimental_SuiClientTypes.ZkLoginVerifyResponse>;
    getDynamicField(options: Experimental_SuiClientTypes.GetDynamicFieldOptions): Promise<Experimental_SuiClientTypes.GetDynamicFieldResponse>;
    waitForTransaction({ signal, timeout, ...input }: {
        /** An optional abort signal that can be used to cancel the wait. */
        signal?: AbortSignal;
        /** The amount of time to wait for transaction. Defaults to one minute. */
        timeout?: number;
    } & Experimental_SuiClientTypes.GetTransactionOptions): Promise<Experimental_SuiClientTypes.GetTransactionResponse>;
}
