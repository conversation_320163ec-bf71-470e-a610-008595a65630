import type { SerializedTransactionDataV2, TransactionPlugin } from '../transactions/index.js';
import type { ClientCache } from './cache.js';
import type { Experimental_BaseClient } from './client.js';
export type SuiClientRegistration<T extends Experimental_BaseClient = Experimental_BaseClient, Name extends string = string, Extension = unknown> = {
    readonly name: Name;
    readonly register: (client: T) => Extension;
} | SelfRegisteringClientExtension<T, Name, Extension>;
export interface SelfRegisteringClientExtension<T extends Experimental_BaseClient = Experimental_BaseClient, Name extends string = string, Extension = unknown> {
    experimental_asClientExtension: () => {
        readonly name: Name;
        readonly register: (client: T) => Extension;
    };
}
export type ClientWithExtensions<T, Base extends Experimental_BaseClient = Experimental_BaseClient> = Base & T;
export declare namespace Experimental_SuiClientTypes {
    type Network = 'mainnet' | 'testnet' | 'devnet' | 'localnet' | (string & {});
    interface SuiClientOptions {
        network: Network;
        base?: Experimental_BaseClient;
        cache?: ClientCache;
    }
    interface MvrOptions {
        url?: string;
        pageSize?: number;
        overrides?: {
            packages?: Record<string, string>;
            types?: Record<string, string>;
        };
    }
    interface CoreClientMethodOptions {
        signal?: AbortSignal;
    }
    /** Object methods */
    interface TransportMethods {
        getObjects: (options: GetObjectsOptions) => Promise<GetObjectsResponse>;
        getOwnedObjects: (options: GetOwnedObjectsOptions) => Promise<GetOwnedObjectsResponse>;
        getCoins: (options: GetCoinsOptions) => Promise<GetCoinsResponse>;
        getDynamicFields: (options: GetDynamicFieldsOptions) => Promise<GetDynamicFieldsResponse>;
        getDynamicField: (options: GetDynamicFieldOptions) => Promise<GetDynamicFieldResponse>;
    }
    interface GetObjectsOptions extends CoreClientMethodOptions {
        objectIds: string[];
    }
    interface GetObjectOptions extends CoreClientMethodOptions {
        objectId: string;
    }
    interface GetOwnedObjectsOptions extends CoreClientMethodOptions {
        address: string;
        limit?: number;
        cursor?: string | null;
        type?: string;
    }
    interface GetCoinsOptions extends CoreClientMethodOptions {
        address: string;
        coinType: string;
        limit?: number;
        cursor?: string | null;
    }
    interface GetDynamicFieldsOptions extends CoreClientMethodOptions {
        parentId: string;
        limit?: number;
        cursor?: string | null;
    }
    interface GetDynamicFieldOptions extends CoreClientMethodOptions {
        parentId: string;
        name: DynamicFieldName;
    }
    interface GetObjectsResponse {
        objects: (ObjectResponse | Error)[];
    }
    interface GetObjectResponse {
        object: ObjectResponse;
    }
    interface GetOwnedObjectsResponse {
        objects: ObjectResponse[];
        hasNextPage: boolean;
        cursor: string | null;
    }
    interface GetCoinsResponse {
        objects: CoinResponse[];
        hasNextPage: boolean;
        cursor: string | null;
    }
    interface ObjectResponse {
        id: string;
        version: string;
        digest: string;
        owner: ObjectOwner;
        type: string;
        content: PromiseLike<Uint8Array>;
    }
    interface CoinResponse extends ObjectResponse {
        balance: string;
    }
    interface GetDynamicFieldsResponse {
        hasNextPage: boolean;
        cursor: string | null;
        dynamicFields: {
            id: string;
            type: string;
            name: DynamicFieldName;
        }[];
    }
    interface GetDynamicFieldResponse {
        dynamicField: {
            name: DynamicFieldName;
            value: DynamicFieldValue;
            id: string;
            version: string;
            digest: string;
            type: string;
        };
    }
    interface DynamicFieldName {
        type: string;
        bcs: Uint8Array;
    }
    interface DynamicFieldValue {
        type: string;
        bcs: Uint8Array;
    }
    /** Balance methods */
    interface TransportMethods {
        getBalance: (options: GetBalanceOptions) => Promise<GetBalanceResponse>;
        getAllBalances: (options: GetAllBalancesOptions) => Promise<GetAllBalancesResponse>;
    }
    interface GetBalanceOptions extends CoreClientMethodOptions {
        address: string;
        coinType: string;
    }
    interface CoinBalance {
        coinType: string;
        balance: string;
    }
    interface GetBalanceResponse {
        balance: CoinBalance;
    }
    interface GetAllBalancesOptions extends CoreClientMethodOptions {
        address: string;
        limit?: number;
        cursor?: string | null;
    }
    interface GetAllBalancesResponse {
        balances: CoinBalance[];
        hasNextPage: boolean;
        cursor: string | null;
    }
    /** Transaction methods */
    interface TransportMethods {
        getTransaction: (options: GetTransactionOptions) => Promise<GetTransactionResponse>;
        executeTransaction: (options: ExecuteTransactionOptions) => Promise<ExecuteTransactionResponse>;
        dryRunTransaction: (options: DryRunTransactionOptions) => Promise<DryRunTransactionResponse>;
        resolveTransactionPlugin: () => TransactionPlugin;
    }
    interface TransactionResponse {
        digest: string;
        signatures: string[];
        epoch: string | null;
        effects: TransactionEffects;
        objectTypes: PromiseLike<Record<string, string>>;
        transaction: TransactionData;
    }
    interface TransactionData extends SerializedTransactionDataV2 {
        bcs: Uint8Array;
    }
    interface GetTransactionOptions extends CoreClientMethodOptions {
        digest: string;
    }
    interface GetTransactionResponse {
        transaction: TransactionResponse;
    }
    interface ExecuteTransactionOptions extends CoreClientMethodOptions {
        transaction: Uint8Array;
        signatures: string[];
    }
    interface DryRunTransactionOptions extends CoreClientMethodOptions {
        transaction: Uint8Array;
    }
    interface DryRunTransactionResponse {
        transaction: TransactionResponse;
    }
    interface ExecuteTransactionResponse {
        transaction: TransactionResponse;
    }
    interface GetReferenceGasPriceOptions extends CoreClientMethodOptions {
    }
    interface TransportMethods {
        getReferenceGasPrice?: (options?: GetReferenceGasPriceOptions) => Promise<GetReferenceGasPriceResponse>;
    }
    interface GetReferenceGasPriceResponse {
        referenceGasPrice: string;
    }
    /** ZkLogin methods */
    interface VerifyZkLoginSignatureOptions extends CoreClientMethodOptions {
        bytes: string;
        signature: string;
        intentScope: 'TransactionData' | 'PersonalMessage';
        author: string;
    }
    interface ZkLoginVerifyResponse {
        success: boolean;
        errors: string[];
    }
    interface TransportMethods {
        verifyZkLoginSignature: (options: VerifyZkLoginSignatureOptions) => Promise<ZkLoginVerifyResponse>;
    }
    /** Name service methods */
    interface ResolveNameServiceNamesOptions extends CoreClientMethodOptions {
        address: string;
        cursor?: string | null | undefined;
        limit?: number | null | undefined;
    }
    interface ResolveNameServiceNamesResponse {
        data: string[];
        hasNextPage: boolean;
        nextCursor: string | null;
    }
    interface TransportMethods {
        resolveNameServiceNames?: (options: ResolveNameServiceNamesOptions) => Promise<ResolveNameServiceNamesResponse>;
    }
    /** MVR methods */
    interface TransportMethods {
        mvr: MvrMethods;
    }
    interface MvrMethods {
        resolvePackage: (options: MvrResolvePackageOptions) => Promise<MvrResolvePackageResponse>;
        resolveType: (options: MvrResolveTypeOptions) => Promise<MvrResolveTypeResponse>;
        resolve: (options: MvrResolveOptions) => Promise<MvrResolveResponse>;
    }
    interface MvrResolvePackageOptions extends CoreClientMethodOptions {
        package: string;
    }
    interface MvrResolveTypeOptions extends CoreClientMethodOptions {
        type: string;
    }
    interface MvrResolveOptions extends CoreClientMethodOptions {
        packages?: string[];
        types?: string[];
    }
    interface MvrResolvePackageResponse {
        package: string;
    }
    interface MvrResolveTypeResponse {
        type: string;
    }
    interface MvrResolveResponse {
        packages: Record<string, {
            package: string;
        }>;
        types: Record<string, {
            type: string;
        }>;
    }
    /** ObjectOwner types */
    interface AddressOwner {
        $kind: 'AddressOwner';
        AddressOwner: string;
    }
    interface ParentOwner {
        $kind: 'ObjectOwner';
        ObjectOwner: string;
    }
    interface SharedOwner {
        $kind: 'Shared';
        Shared: {
            initialSharedVersion: string;
        };
    }
    interface ImmutableOwner {
        $kind: 'Immutable';
        Immutable: true;
    }
    interface ConsensusAddressOwner {
        $kind: 'ConsensusAddressOwner';
        ConsensusAddressOwner: {
            owner: string;
            startVersion: string;
        };
    }
    type ObjectOwner = AddressOwner | ParentOwner | SharedOwner | ImmutableOwner | ConsensusAddressOwner;
    /** Effects */
    interface TransactionEffects {
        bcs: Uint8Array | null;
        digest: string;
        version: number;
        status: ExecutionStatus;
        gasUsed: GasCostSummary;
        transactionDigest: string;
        gasObject: ChangedObject | null;
        eventsDigest: string | null;
        dependencies: string[];
        lamportVersion: string | null;
        changedObjects: ChangedObject[];
        unchangedSharedObjects: UnchangedSharedObject[];
        auxiliaryDataDigest: string | null;
    }
    interface ChangedObject {
        id: string;
        inputState: 'Unknown' | 'DoesNotExist' | 'Exists';
        inputVersion: string | null;
        inputDigest: string | null;
        inputOwner: ObjectOwner | null;
        outputState: 'Unknown' | 'DoesNotExist' | 'ObjectWrite' | 'PackageWrite';
        outputVersion: string | null;
        outputDigest: string | null;
        outputOwner: ObjectOwner | null;
        idOperation: 'Unknown' | 'None' | 'Created' | 'Deleted';
    }
    interface GasCostSummary {
        computationCost: string;
        storageCost: string;
        storageRebate: string;
        nonRefundableStorageFee: string;
    }
    type ExecutionStatus = {
        success: true;
        error: null;
    } | {
        success: false;
        error: string;
    };
    interface UnchangedSharedObject {
        kind: 'Unknown' | 'ReadOnlyRoot' | 'MutateDeleted' | 'ReadDeleted' | 'Cancelled' | 'PerEpochConfig';
        objectId: string;
        version: string | null;
        digest: string | null;
    }
}
