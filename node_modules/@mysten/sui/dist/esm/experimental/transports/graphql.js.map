{"version": 3, "sources": ["../../../../src/experimental/transports/graphql.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { Experimental_CoreClient } from '../core.js';\nimport type { Experimental_SuiClientTypes } from '../types.js';\nimport type { GraphQLQueryOptions, SuiGraphQLClient } from '../../graphql/client.js';\nimport type {\n\tObject_Owner_FieldsFragment,\n\tObject_FieldsFragment,\n\tTransaction_FieldsFragment,\n} from '../../graphql/generated/queries.js';\nimport {\n\tDryRunTransactionBlockDocument,\n\tExecuteTransactionBlockDocument,\n\tGetAllBalancesDocument,\n\tGetBalanceDocument,\n\tGetCoinsDocument,\n\tGetDynamicFieldsDocument,\n\tGetOwnedObjectsDocument,\n\tGetReferenceGasPriceDocument,\n\tGetTransactionBlockDocument,\n\tMultiGetObjectsDocument,\n\tResolveNameServiceNamesDocument,\n\tVerifyZkLoginSignatureDocument,\n\tZkLoginIntentScope,\n} from '../../graphql/generated/queries.js';\nimport { ObjectError } from '../errors.js';\nimport { fromBase64, toBase64 } from '@mysten/utils';\nimport { normalizeStructTag, normalizeSuiAddress } from '../../utils/sui-types.js';\nimport { deriveDynamicFieldID } from '../../utils/dynamic-fields.js';\nimport { parseTransactionBcs, parseTransactionEffectsBcs } from './utils.js';\n\nexport class GraphQLTransport extends Experimental_CoreClient {\n\t#graphqlClient: SuiGraphQLClient;\n\n\tconstructor({\n\t\tgraphqlClient,\n\t\tmvr,\n\t}: {\n\t\tgraphqlClient: SuiGraphQLClient;\n\t\tmvr?: Experimental_SuiClientTypes.MvrOptions;\n\t}) {\n\t\tsuper({ network: graphqlClient.network, base: graphqlClient, mvr });\n\t\tthis.#graphqlClient = graphqlClient;\n\t}\n\n\tasync #graphqlQuery<\n\t\tResult = Record<string, unknown>,\n\t\tVariables = Record<string, unknown>,\n\t\tData = Result,\n\t>(\n\t\toptions: GraphQLQueryOptions<Result, Variables>,\n\t\tgetData?: (result: Result) => Data,\n\t): Promise<NonNullable<Data>> {\n\t\tconst { data, errors } = await this.#graphqlClient.query(options);\n\n\t\thandleGraphQLErrors(errors);\n\n\t\tconst extractedData = data && (getData ? getData(data) : data);\n\n\t\tif (extractedData == null) {\n\t\t\tthrow new Error('Missing response data');\n\t\t}\n\n\t\treturn extractedData as NonNullable<Data>;\n\t}\n\n\tasync getObjects(\n\t\toptions: Experimental_SuiClientTypes.GetObjectsOptions,\n\t): Promise<Experimental_SuiClientTypes.GetObjectsResponse> {\n\t\tconst objects: Object_FieldsFragment[] = [];\n\n\t\tlet hasNextPage = true;\n\t\tlet cursor: string | null = null;\n\n\t\twhile (hasNextPage) {\n\t\t\tconst objectsPage = await this.#graphqlQuery(\n\t\t\t\t{\n\t\t\t\t\tquery: MultiGetObjectsDocument,\n\t\t\t\t\tvariables: {\n\t\t\t\t\t\tobjectIds: options.objectIds,\n\t\t\t\t\t\tcursor,\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t\t(result) => result.objects,\n\t\t\t);\n\n\t\t\tobjects.push(...objectsPage.nodes);\n\t\t\thasNextPage = objectsPage.pageInfo.hasNextPage;\n\t\t\tcursor = (objectsPage.pageInfo.endCursor ?? null) as string | null;\n\t\t}\n\n\t\treturn {\n\t\t\tobjects: options.objectIds\n\t\t\t\t.map((id) => normalizeSuiAddress(id))\n\t\t\t\t.map(\n\t\t\t\t\t(id) =>\n\t\t\t\t\t\tobjects.find((obj) => obj.address === id) ??\n\t\t\t\t\t\tnew ObjectError('notFound', `Object ${id} not found`),\n\t\t\t\t)\n\t\t\t\t.map((obj) => {\n\t\t\t\t\tif (obj instanceof ObjectError) {\n\t\t\t\t\t\treturn obj;\n\t\t\t\t\t}\n\t\t\t\t\treturn {\n\t\t\t\t\t\tid: obj.address,\n\t\t\t\t\t\tversion: obj.version.toString(),\n\t\t\t\t\t\tdigest: obj.digest!,\n\t\t\t\t\t\towner: mapOwner(obj.owner!),\n\t\t\t\t\t\ttype: obj.asMoveObject?.contents?.type?.repr!,\n\t\t\t\t\t\tcontent: Promise.resolve(\n\t\t\t\t\t\t\tobj.asMoveObject?.contents?.bcs\n\t\t\t\t\t\t\t\t? fromBase64(obj.asMoveObject.contents.bcs)\n\t\t\t\t\t\t\t\t: new Uint8Array(),\n\t\t\t\t\t\t),\n\t\t\t\t\t};\n\t\t\t\t}),\n\t\t};\n\t}\n\tasync getOwnedObjects(\n\t\toptions: Experimental_SuiClientTypes.GetOwnedObjectsOptions,\n\t): Promise<Experimental_SuiClientTypes.GetOwnedObjectsResponse> {\n\t\tconst objects = await this.#graphqlQuery(\n\t\t\t{\n\t\t\t\tquery: GetOwnedObjectsDocument,\n\t\t\t\tvariables: {\n\t\t\t\t\towner: options.address,\n\t\t\t\t\tlimit: options.limit,\n\t\t\t\t\tcursor: options.cursor,\n\t\t\t\t\tfilter: options.type ? { type: options.type } : undefined,\n\t\t\t\t},\n\t\t\t},\n\t\t\t(result) => result.address?.objects,\n\t\t);\n\n\t\treturn {\n\t\t\tobjects: objects.nodes.map((obj) => ({\n\t\t\t\tid: obj.address,\n\t\t\t\tversion: obj.version.toString(),\n\t\t\t\tdigest: obj.digest!,\n\t\t\t\towner: mapOwner(obj.owner!),\n\t\t\t\ttype: obj.contents?.type?.repr!,\n\t\t\t\tcontent: Promise.resolve(\n\t\t\t\t\tobj.contents?.bcs ? fromBase64(obj.contents.bcs) : new Uint8Array(),\n\t\t\t\t),\n\t\t\t})),\n\t\t\thasNextPage: objects.pageInfo.hasNextPage,\n\t\t\tcursor: objects.pageInfo.endCursor ?? null,\n\t\t};\n\t}\n\tasync getCoins(\n\t\toptions: Experimental_SuiClientTypes.GetCoinsOptions,\n\t): Promise<Experimental_SuiClientTypes.GetCoinsResponse> {\n\t\tconst coins = await this.#graphqlQuery(\n\t\t\t{\n\t\t\t\tquery: GetCoinsDocument,\n\t\t\t\tvariables: {\n\t\t\t\t\towner: options.address,\n\t\t\t\t\tcursor: options.cursor,\n\t\t\t\t\tfirst: options.limit,\n\t\t\t\t\ttype: options.coinType,\n\t\t\t\t},\n\t\t\t},\n\t\t\t(result) => result.address?.coins,\n\t\t);\n\n\t\treturn {\n\t\t\tcursor: coins.pageInfo.endCursor ?? null,\n\t\t\thasNextPage: coins.pageInfo.hasNextPage,\n\t\t\tobjects: coins.nodes.map((coin) => ({\n\t\t\t\tid: coin.address,\n\t\t\t\tversion: coin.version.toString(),\n\t\t\t\tdigest: coin.digest!,\n\t\t\t\towner: mapOwner(coin.owner!),\n\t\t\t\ttype: coin.contents?.type?.repr!,\n\t\t\t\tbalance: coin.coinBalance,\n\t\t\t\tcontent: Promise.resolve(\n\t\t\t\t\tcoin.contents?.bcs ? fromBase64(coin.contents.bcs) : new Uint8Array(),\n\t\t\t\t),\n\t\t\t})),\n\t\t};\n\t}\n\n\tasync getBalance(\n\t\toptions: Experimental_SuiClientTypes.GetBalanceOptions,\n\t): Promise<Experimental_SuiClientTypes.GetBalanceResponse> {\n\t\tconst result = await this.#graphqlQuery(\n\t\t\t{\n\t\t\t\tquery: GetBalanceDocument,\n\t\t\t\tvariables: { owner: options.address, type: options.coinType },\n\t\t\t},\n\t\t\t(result) => result.address?.balance,\n\t\t);\n\n\t\treturn {\n\t\t\tbalance: {\n\t\t\t\tcoinType: result.coinType.repr,\n\t\t\t\tbalance: result.totalBalance,\n\t\t\t},\n\t\t};\n\t}\n\tasync getAllBalances(\n\t\toptions: Experimental_SuiClientTypes.GetAllBalancesOptions,\n\t): Promise<Experimental_SuiClientTypes.GetAllBalancesResponse> {\n\t\tconst balances = await this.#graphqlQuery(\n\t\t\t{\n\t\t\t\tquery: GetAllBalancesDocument,\n\t\t\t\tvariables: { owner: options.address },\n\t\t\t},\n\t\t\t(result) => result.address?.balances,\n\t\t);\n\n\t\treturn {\n\t\t\tcursor: balances.pageInfo.endCursor ?? null,\n\t\t\thasNextPage: balances.pageInfo.hasNextPage,\n\t\t\tbalances: balances.nodes.map((balance) => ({\n\t\t\t\tcoinType: balance.coinType.repr,\n\t\t\t\tbalance: balance.totalBalance,\n\t\t\t})),\n\t\t};\n\t}\n\tasync getTransaction(\n\t\toptions: Experimental_SuiClientTypes.GetTransactionOptions,\n\t): Promise<Experimental_SuiClientTypes.GetTransactionResponse> {\n\t\tconst result = await this.#graphqlQuery(\n\t\t\t{\n\t\t\t\tquery: GetTransactionBlockDocument,\n\t\t\t\tvariables: { digest: options.digest },\n\t\t\t},\n\t\t\t(result) => result.transactionBlock,\n\t\t);\n\n\t\treturn {\n\t\t\ttransaction: parseTransaction(result),\n\t\t};\n\t}\n\tasync executeTransaction(\n\t\toptions: Experimental_SuiClientTypes.ExecuteTransactionOptions,\n\t): Promise<Experimental_SuiClientTypes.ExecuteTransactionResponse> {\n\t\tconst result = await this.#graphqlQuery(\n\t\t\t{\n\t\t\t\tquery: ExecuteTransactionBlockDocument,\n\t\t\t\tvariables: { txBytes: toBase64(options.transaction), signatures: options.signatures },\n\t\t\t},\n\t\t\t(result) => result.executeTransactionBlock,\n\t\t);\n\n\t\tif (result.errors) {\n\t\t\tif (result.errors.length === 1) {\n\t\t\t\tthrow new Error(result.errors[0]);\n\t\t\t}\n\t\t\tthrow new AggregateError(result.errors.map((error) => new Error(error)));\n\t\t}\n\n\t\treturn {\n\t\t\ttransaction: parseTransaction(result.effects.transactionBlock!),\n\t\t};\n\t}\n\tasync dryRunTransaction(\n\t\toptions: Experimental_SuiClientTypes.DryRunTransactionOptions,\n\t): Promise<Experimental_SuiClientTypes.DryRunTransactionResponse> {\n\t\tconst result = await this.#graphqlQuery(\n\t\t\t{\n\t\t\t\tquery: DryRunTransactionBlockDocument,\n\t\t\t\tvariables: { txBytes: toBase64(options.transaction) },\n\t\t\t},\n\t\t\t(result) => result.dryRunTransactionBlock,\n\t\t);\n\n\t\tif (result.error) {\n\t\t\tthrow new Error(result.error);\n\t\t}\n\n\t\treturn {\n\t\t\ttransaction: parseTransaction(result.transaction!),\n\t\t};\n\t}\n\tasync getReferenceGasPrice(): Promise<Experimental_SuiClientTypes.GetReferenceGasPriceResponse> {\n\t\tconst result = await this.#graphqlQuery(\n\t\t\t{\n\t\t\t\tquery: GetReferenceGasPriceDocument,\n\t\t\t},\n\t\t\t(result) => result.epoch?.referenceGasPrice,\n\t\t);\n\n\t\treturn {\n\t\t\treferenceGasPrice: result.referenceGasPrice,\n\t\t};\n\t}\n\n\tasync getDynamicFields(\n\t\toptions: Experimental_SuiClientTypes.GetDynamicFieldsOptions,\n\t): Promise<Experimental_SuiClientTypes.GetDynamicFieldsResponse> {\n\t\tconst result = await this.#graphqlQuery(\n\t\t\t{\n\t\t\t\tquery: GetDynamicFieldsDocument,\n\t\t\t\tvariables: { parentId: options.parentId },\n\t\t\t},\n\t\t\t(result) => result.owner?.dynamicFields,\n\t\t);\n\n\t\treturn {\n\t\t\tdynamicFields: result.nodes.map((dynamicField) => {\n\t\t\t\tconst valueType =\n\t\t\t\t\tdynamicField.value?.__typename === 'MoveObject'\n\t\t\t\t\t\t? dynamicField.value.contents?.type?.repr!\n\t\t\t\t\t\t: dynamicField.value?.type.repr!;\n\t\t\t\treturn {\n\t\t\t\t\tid: deriveDynamicFieldID(\n\t\t\t\t\t\toptions.parentId,\n\t\t\t\t\t\tdynamicField.name?.type.repr!,\n\t\t\t\t\t\tdynamicField.name?.bcs!,\n\t\t\t\t\t),\n\t\t\t\t\ttype: normalizeStructTag(\n\t\t\t\t\t\tdynamicField.value?.__typename === 'MoveObject'\n\t\t\t\t\t\t\t? `0x2::dynamic_field::Field<0x2::dynamic_object_field::Wrapper<${dynamicField.name?.type.repr}>,0x2::object::ID>`\n\t\t\t\t\t\t\t: `0x2::dynamic_field::Field<${dynamicField.name?.type.repr},${valueType}>`,\n\t\t\t\t\t),\n\t\t\t\t\tname: {\n\t\t\t\t\t\ttype: dynamicField.name?.type.repr!,\n\t\t\t\t\t\tbcs: fromBase64(dynamicField.name?.bcs!),\n\t\t\t\t\t},\n\t\t\t\t\tvalueType,\n\t\t\t\t};\n\t\t\t}),\n\t\t\tcursor: result.pageInfo.endCursor ?? null,\n\t\t\thasNextPage: result.pageInfo.hasNextPage,\n\t\t};\n\t}\n\n\tasync verifyZkLoginSignature(\n\t\toptions: Experimental_SuiClientTypes.VerifyZkLoginSignatureOptions,\n\t): Promise<Experimental_SuiClientTypes.ZkLoginVerifyResponse> {\n\t\tconst intentScope =\n\t\t\toptions.intentScope === 'TransactionData'\n\t\t\t\t? ZkLoginIntentScope.TransactionData\n\t\t\t\t: ZkLoginIntentScope.PersonalMessage;\n\n\t\tconst result = await this.#graphqlQuery(\n\t\t\t{\n\t\t\t\tquery: VerifyZkLoginSignatureDocument,\n\t\t\t\tvariables: {\n\t\t\t\t\tbytes: options.bytes,\n\t\t\t\t\tsignature: options.signature,\n\t\t\t\t\tintentScope,\n\t\t\t\t\tauthor: options.author,\n\t\t\t\t},\n\t\t\t},\n\t\t\t(result) => result.verifyZkloginSignature,\n\t\t);\n\n\t\treturn {\n\t\t\tsuccess: result.success,\n\t\t\terrors: result.errors,\n\t\t};\n\t}\n\n\tasync resolveNameServiceNames(\n\t\toptions: Experimental_SuiClientTypes.ResolveNameServiceNamesOptions,\n\t): Promise<Experimental_SuiClientTypes.ResolveNameServiceNamesResponse> {\n\t\tconst suinsRegistrations = await this.#graphqlQuery(\n\t\t\t{\n\t\t\t\tquery: ResolveNameServiceNamesDocument,\n\t\t\t\tsignal: options.signal,\n\t\t\t\tvariables: {\n\t\t\t\t\taddress: options.address,\n\t\t\t\t\tcursor: options.cursor,\n\t\t\t\t\tlimit: options.limit,\n\t\t\t\t},\n\t\t\t},\n\t\t\t(result) => result.address?.suinsRegistrations,\n\t\t);\n\n\t\treturn {\n\t\t\thasNextPage: suinsRegistrations.pageInfo.hasNextPage,\n\t\t\tnextCursor: suinsRegistrations.pageInfo.endCursor ?? null,\n\t\t\tdata: suinsRegistrations.nodes.map((node) => node.domain) ?? [],\n\t\t};\n\t}\n\n\tresolveTransactionPlugin(): never {\n\t\tthrow new Error('GraphQL client does not support transaction resolution yet');\n\t}\n}\nexport type GraphQLResponseErrors = Array<{\n\tmessage: string;\n\tlocations?: { line: number; column: number }[];\n\tpath?: (string | number)[];\n}>;\n\nfunction handleGraphQLErrors(errors: GraphQLResponseErrors | undefined): void {\n\tif (!errors || errors.length === 0) return;\n\n\tconst errorInstances = errors.map((error) => new GraphQLResponseError(error));\n\n\tif (errorInstances.length === 1) {\n\t\tthrow errorInstances[0];\n\t}\n\n\tthrow new AggregateError(errorInstances);\n}\n\nclass GraphQLResponseError extends Error {\n\tlocations?: Array<{ line: number; column: number }>;\n\n\tconstructor(error: GraphQLResponseErrors[0]) {\n\t\tsuper(error.message);\n\t\tthis.locations = error.locations;\n\t}\n}\n\nfunction mapOwner(owner: Object_Owner_FieldsFragment): Experimental_SuiClientTypes.ObjectOwner {\n\tswitch (owner.__typename) {\n\t\tcase 'AddressOwner':\n\t\t\treturn { $kind: 'AddressOwner', AddressOwner: owner.owner?.asAddress?.address };\n\t\tcase 'ConsensusAddressOwner':\n\t\t\treturn {\n\t\t\t\t$kind: 'ConsensusAddressOwner',\n\t\t\t\tConsensusAddressOwner: {\n\t\t\t\t\towner: owner.owner?.address,\n\t\t\t\t\tstartVersion: owner.startVersion,\n\t\t\t\t},\n\t\t\t};\n\t\tcase 'Immutable':\n\t\t\treturn { $kind: 'Immutable', Immutable: true };\n\t\tcase 'Parent':\n\t\t\treturn { $kind: 'ObjectOwner', ObjectOwner: owner.parent?.address };\n\t\tcase 'Shared':\n\t\t\treturn { $kind: 'Shared', Shared: owner.initialSharedVersion };\n\t}\n}\n\nfunction parseTransaction(\n\ttransaction: Transaction_FieldsFragment,\n): Experimental_SuiClientTypes.TransactionResponse {\n\tconst objectTypes: Record<string, string> = {};\n\n\ttransaction.effects?.unchangedSharedObjects.nodes.forEach((node) => {\n\t\tif (node.__typename === 'SharedObjectRead') {\n\t\t\tconst type = node.object?.asMoveObject?.contents?.type.repr;\n\t\t\tconst address = node.object?.asMoveObject?.address;\n\n\t\t\tif (type && address) {\n\t\t\t\tobjectTypes[address] = type;\n\t\t\t}\n\t\t}\n\t});\n\n\ttransaction.effects?.objectChanges.nodes.forEach((node) => {\n\t\tconst address = node.address;\n\t\tconst type =\n\t\t\tnode.inputState?.asMoveObject?.contents?.type.repr ??\n\t\t\tnode.outputState?.asMoveObject?.contents?.type.repr;\n\n\t\tif (address && type) {\n\t\t\tobjectTypes[address] = type;\n\t\t}\n\t});\n\n\treturn {\n\t\tdigest: transaction.digest!,\n\t\teffects: parseTransactionEffectsBcs(new Uint8Array(transaction.effects?.bcs!)),\n\t\tepoch: transaction.effects?.epoch?.epochId ?? null,\n\t\tobjectTypes: Promise.resolve(objectTypes),\n\t\ttransaction: parseTransactionBcs(transaction.bcs!),\n\t\tsignatures: transaction.signatures!,\n\t};\n}\n"], "mappings": ";;;;;;;;AAAA;AAGA,SAAS,+BAA+B;AAQxC;AAAA,EACC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACM;AACP,SAAS,mBAAmB;AAC5B,SAAS,YAAY,gBAAgB;AACrC,SAAS,oBAAoB,2BAA2B;AACxD,SAAS,4BAA4B;AACrC,SAAS,qBAAqB,kCAAkC;AAEzD,MAAM,yBAAyB,wBAAwB;AAAA,EAG7D,YAAY;AAAA,IACX;AAAA,IACA;AAAA,EACD,GAGG;AACF,UAAM,EAAE,SAAS,cAAc,SAAS,MAAM,eAAe,IAAI,CAAC;AAV7D;AACN;AAUC,uBAAK,gBAAiB;AAAA,EACvB;AAAA,EAuBA,MAAM,WACL,SAC0D;AAC1D,UAAM,UAAmC,CAAC;AAE1C,QAAI,cAAc;AAClB,QAAI,SAAwB;AAE5B,WAAO,aAAa;AACnB,YAAM,cAAc,MAAM,sBAAK,8CAAL,WACzB;AAAA,QACC,OAAO;AAAA,QACP,WAAW;AAAA,UACV,WAAW,QAAQ;AAAA,UACnB;AAAA,QACD;AAAA,MACD,GACA,CAAC,WAAW,OAAO;AAGpB,cAAQ,KAAK,GAAG,YAAY,KAAK;AACjC,oBAAc,YAAY,SAAS;AACnC,eAAU,YAAY,SAAS,aAAa;AAAA,IAC7C;AAEA,WAAO;AAAA,MACN,SAAS,QAAQ,UACf,IAAI,CAAC,OAAO,oBAAoB,EAAE,CAAC,EACnC;AAAA,QACA,CAAC,OACA,QAAQ,KAAK,CAAC,QAAQ,IAAI,YAAY,EAAE,KACxC,IAAI,YAAY,YAAY,UAAU,EAAE,YAAY;AAAA,MACtD,EACC,IAAI,CAAC,QAAQ;AACb,YAAI,eAAe,aAAa;AAC/B,iBAAO;AAAA,QACR;AACA,eAAO;AAAA,UACN,IAAI,IAAI;AAAA,UACR,SAAS,IAAI,QAAQ,SAAS;AAAA,UAC9B,QAAQ,IAAI;AAAA,UACZ,OAAO,SAAS,IAAI,KAAM;AAAA,UAC1B,MAAM,IAAI,cAAc,UAAU,MAAM;AAAA,UACxC,SAAS,QAAQ;AAAA,YAChB,IAAI,cAAc,UAAU,MACzB,WAAW,IAAI,aAAa,SAAS,GAAG,IACxC,IAAI,WAAW;AAAA,UACnB;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACH;AAAA,EACD;AAAA,EACA,MAAM,gBACL,SAC+D;AAC/D,UAAM,UAAU,MAAM,sBAAK,8CAAL,WACrB;AAAA,MACC,OAAO;AAAA,MACP,WAAW;AAAA,QACV,OAAO,QAAQ;AAAA,QACf,OAAO,QAAQ;AAAA,QACf,QAAQ,QAAQ;AAAA,QAChB,QAAQ,QAAQ,OAAO,EAAE,MAAM,QAAQ,KAAK,IAAI;AAAA,MACjD;AAAA,IACD,GACA,CAAC,WAAW,OAAO,SAAS;AAG7B,WAAO;AAAA,MACN,SAAS,QAAQ,MAAM,IAAI,CAAC,SAAS;AAAA,QACpC,IAAI,IAAI;AAAA,QACR,SAAS,IAAI,QAAQ,SAAS;AAAA,QAC9B,QAAQ,IAAI;AAAA,QACZ,OAAO,SAAS,IAAI,KAAM;AAAA,QAC1B,MAAM,IAAI,UAAU,MAAM;AAAA,QAC1B,SAAS,QAAQ;AAAA,UAChB,IAAI,UAAU,MAAM,WAAW,IAAI,SAAS,GAAG,IAAI,IAAI,WAAW;AAAA,QACnE;AAAA,MACD,EAAE;AAAA,MACF,aAAa,QAAQ,SAAS;AAAA,MAC9B,QAAQ,QAAQ,SAAS,aAAa;AAAA,IACvC;AAAA,EACD;AAAA,EACA,MAAM,SACL,SACwD;AACxD,UAAM,QAAQ,MAAM,sBAAK,8CAAL,WACnB;AAAA,MACC,OAAO;AAAA,MACP,WAAW;AAAA,QACV,OAAO,QAAQ;AAAA,QACf,QAAQ,QAAQ;AAAA,QAChB,OAAO,QAAQ;AAAA,QACf,MAAM,QAAQ;AAAA,MACf;AAAA,IACD,GACA,CAAC,WAAW,OAAO,SAAS;AAG7B,WAAO;AAAA,MACN,QAAQ,MAAM,SAAS,aAAa;AAAA,MACpC,aAAa,MAAM,SAAS;AAAA,MAC5B,SAAS,MAAM,MAAM,IAAI,CAAC,UAAU;AAAA,QACnC,IAAI,KAAK;AAAA,QACT,SAAS,KAAK,QAAQ,SAAS;AAAA,QAC/B,QAAQ,KAAK;AAAA,QACb,OAAO,SAAS,KAAK,KAAM;AAAA,QAC3B,MAAM,KAAK,UAAU,MAAM;AAAA,QAC3B,SAAS,KAAK;AAAA,QACd,SAAS,QAAQ;AAAA,UAChB,KAAK,UAAU,MAAM,WAAW,KAAK,SAAS,GAAG,IAAI,IAAI,WAAW;AAAA,QACrE;AAAA,MACD,EAAE;AAAA,IACH;AAAA,EACD;AAAA,EAEA,MAAM,WACL,SAC0D;AAC1D,UAAM,SAAS,MAAM,sBAAK,8CAAL,WACpB;AAAA,MACC,OAAO;AAAA,MACP,WAAW,EAAE,OAAO,QAAQ,SAAS,MAAM,QAAQ,SAAS;AAAA,IAC7D,GACA,CAACA,YAAWA,QAAO,SAAS;AAG7B,WAAO;AAAA,MACN,SAAS;AAAA,QACR,UAAU,OAAO,SAAS;AAAA,QAC1B,SAAS,OAAO;AAAA,MACjB;AAAA,IACD;AAAA,EACD;AAAA,EACA,MAAM,eACL,SAC8D;AAC9D,UAAM,WAAW,MAAM,sBAAK,8CAAL,WACtB;AAAA,MACC,OAAO;AAAA,MACP,WAAW,EAAE,OAAO,QAAQ,QAAQ;AAAA,IACrC,GACA,CAAC,WAAW,OAAO,SAAS;AAG7B,WAAO;AAAA,MACN,QAAQ,SAAS,SAAS,aAAa;AAAA,MACvC,aAAa,SAAS,SAAS;AAAA,MAC/B,UAAU,SAAS,MAAM,IAAI,CAAC,aAAa;AAAA,QAC1C,UAAU,QAAQ,SAAS;AAAA,QAC3B,SAAS,QAAQ;AAAA,MAClB,EAAE;AAAA,IACH;AAAA,EACD;AAAA,EACA,MAAM,eACL,SAC8D;AAC9D,UAAM,SAAS,MAAM,sBAAK,8CAAL,WACpB;AAAA,MACC,OAAO;AAAA,MACP,WAAW,EAAE,QAAQ,QAAQ,OAAO;AAAA,IACrC,GACA,CAACA,YAAWA,QAAO;AAGpB,WAAO;AAAA,MACN,aAAa,iBAAiB,MAAM;AAAA,IACrC;AAAA,EACD;AAAA,EACA,MAAM,mBACL,SACkE;AAClE,UAAM,SAAS,MAAM,sBAAK,8CAAL,WACpB;AAAA,MACC,OAAO;AAAA,MACP,WAAW,EAAE,SAAS,SAAS,QAAQ,WAAW,GAAG,YAAY,QAAQ,WAAW;AAAA,IACrF,GACA,CAACA,YAAWA,QAAO;AAGpB,QAAI,OAAO,QAAQ;AAClB,UAAI,OAAO,OAAO,WAAW,GAAG;AAC/B,cAAM,IAAI,MAAM,OAAO,OAAO,CAAC,CAAC;AAAA,MACjC;AACA,YAAM,IAAI,eAAe,OAAO,OAAO,IAAI,CAAC,UAAU,IAAI,MAAM,KAAK,CAAC,CAAC;AAAA,IACxE;AAEA,WAAO;AAAA,MACN,aAAa,iBAAiB,OAAO,QAAQ,gBAAiB;AAAA,IAC/D;AAAA,EACD;AAAA,EACA,MAAM,kBACL,SACiE;AACjE,UAAM,SAAS,MAAM,sBAAK,8CAAL,WACpB;AAAA,MACC,OAAO;AAAA,MACP,WAAW,EAAE,SAAS,SAAS,QAAQ,WAAW,EAAE;AAAA,IACrD,GACA,CAACA,YAAWA,QAAO;AAGpB,QAAI,OAAO,OAAO;AACjB,YAAM,IAAI,MAAM,OAAO,KAAK;AAAA,IAC7B;AAEA,WAAO;AAAA,MACN,aAAa,iBAAiB,OAAO,WAAY;AAAA,IAClD;AAAA,EACD;AAAA,EACA,MAAM,uBAA0F;AAC/F,UAAM,SAAS,MAAM,sBAAK,8CAAL,WACpB;AAAA,MACC,OAAO;AAAA,IACR,GACA,CAACA,YAAWA,QAAO,OAAO;AAG3B,WAAO;AAAA,MACN,mBAAmB,OAAO;AAAA,IAC3B;AAAA,EACD;AAAA,EAEA,MAAM,iBACL,SACgE;AAChE,UAAM,SAAS,MAAM,sBAAK,8CAAL,WACpB;AAAA,MACC,OAAO;AAAA,MACP,WAAW,EAAE,UAAU,QAAQ,SAAS;AAAA,IACzC,GACA,CAACA,YAAWA,QAAO,OAAO;AAG3B,WAAO;AAAA,MACN,eAAe,OAAO,MAAM,IAAI,CAAC,iBAAiB;AACjD,cAAM,YACL,aAAa,OAAO,eAAe,eAChC,aAAa,MAAM,UAAU,MAAM,OACnC,aAAa,OAAO,KAAK;AAC7B,eAAO;AAAA,UACN,IAAI;AAAA,YACH,QAAQ;AAAA,YACR,aAAa,MAAM,KAAK;AAAA,YACxB,aAAa,MAAM;AAAA,UACpB;AAAA,UACA,MAAM;AAAA,YACL,aAAa,OAAO,eAAe,eAChC,gEAAgE,aAAa,MAAM,KAAK,IAAI,uBAC5F,6BAA6B,aAAa,MAAM,KAAK,IAAI,IAAI,SAAS;AAAA,UAC1E;AAAA,UACA,MAAM;AAAA,YACL,MAAM,aAAa,MAAM,KAAK;AAAA,YAC9B,KAAK,WAAW,aAAa,MAAM,GAAI;AAAA,UACxC;AAAA,UACA;AAAA,QACD;AAAA,MACD,CAAC;AAAA,MACD,QAAQ,OAAO,SAAS,aAAa;AAAA,MACrC,aAAa,OAAO,SAAS;AAAA,IAC9B;AAAA,EACD;AAAA,EAEA,MAAM,uBACL,SAC6D;AAC7D,UAAM,cACL,QAAQ,gBAAgB,oBACrB,mBAAmB,kBACnB,mBAAmB;AAEvB,UAAM,SAAS,MAAM,sBAAK,8CAAL,WACpB;AAAA,MACC,OAAO;AAAA,MACP,WAAW;AAAA,QACV,OAAO,QAAQ;AAAA,QACf,WAAW,QAAQ;AAAA,QACnB;AAAA,QACA,QAAQ,QAAQ;AAAA,MACjB;AAAA,IACD,GACA,CAACA,YAAWA,QAAO;AAGpB,WAAO;AAAA,MACN,SAAS,OAAO;AAAA,MAChB,QAAQ,OAAO;AAAA,IAChB;AAAA,EACD;AAAA,EAEA,MAAM,wBACL,SACuE;AACvE,UAAM,qBAAqB,MAAM,sBAAK,8CAAL,WAChC;AAAA,MACC,OAAO;AAAA,MACP,QAAQ,QAAQ;AAAA,MAChB,WAAW;AAAA,QACV,SAAS,QAAQ;AAAA,QACjB,QAAQ,QAAQ;AAAA,QAChB,OAAO,QAAQ;AAAA,MAChB;AAAA,IACD,GACA,CAAC,WAAW,OAAO,SAAS;AAG7B,WAAO;AAAA,MACN,aAAa,mBAAmB,SAAS;AAAA,MACzC,YAAY,mBAAmB,SAAS,aAAa;AAAA,MACrD,MAAM,mBAAmB,MAAM,IAAI,CAAC,SAAS,KAAK,MAAM,KAAK,CAAC;AAAA,IAC/D;AAAA,EACD;AAAA,EAEA,2BAAkC;AACjC,UAAM,IAAI,MAAM,4DAA4D;AAAA,EAC7E;AACD;AA9VC;AADM;AAcA,kBAIL,eACA,SACA,SAC6B;AAC7B,QAAM,EAAE,MAAM,OAAO,IAAI,MAAM,mBAAK,gBAAe,MAAM,OAAO;AAEhE,sBAAoB,MAAM;AAE1B,QAAM,gBAAgB,SAAS,UAAU,QAAQ,IAAI,IAAI;AAEzD,MAAI,iBAAiB,MAAM;AAC1B,UAAM,IAAI,MAAM,uBAAuB;AAAA,EACxC;AAEA,SAAO;AACR;AAqUD,SAAS,oBAAoB,QAAiD;AAC7E,MAAI,CAAC,UAAU,OAAO,WAAW,EAAG;AAEpC,QAAM,iBAAiB,OAAO,IAAI,CAAC,UAAU,IAAI,qBAAqB,KAAK,CAAC;AAE5E,MAAI,eAAe,WAAW,GAAG;AAChC,UAAM,eAAe,CAAC;AAAA,EACvB;AAEA,QAAM,IAAI,eAAe,cAAc;AACxC;AAEA,MAAM,6BAA6B,MAAM;AAAA,EAGxC,YAAY,OAAiC;AAC5C,UAAM,MAAM,OAAO;AACnB,SAAK,YAAY,MAAM;AAAA,EACxB;AACD;AAEA,SAAS,SAAS,OAA6E;AAC9F,UAAQ,MAAM,YAAY;AAAA,IACzB,KAAK;AACJ,aAAO,EAAE,OAAO,gBAAgB,cAAc,MAAM,OAAO,WAAW,QAAQ;AAAA,IAC/E,KAAK;AACJ,aAAO;AAAA,QACN,OAAO;AAAA,QACP,uBAAuB;AAAA,UACtB,OAAO,MAAM,OAAO;AAAA,UACpB,cAAc,MAAM;AAAA,QACrB;AAAA,MACD;AAAA,IACD,KAAK;AACJ,aAAO,EAAE,OAAO,aAAa,WAAW,KAAK;AAAA,IAC9C,KAAK;AACJ,aAAO,EAAE,OAAO,eAAe,aAAa,MAAM,QAAQ,QAAQ;AAAA,IACnE,KAAK;AACJ,aAAO,EAAE,OAAO,UAAU,QAAQ,MAAM,qBAAqB;AAAA,EAC/D;AACD;AAEA,SAAS,iBACR,aACkD;AAClD,QAAM,cAAsC,CAAC;AAE7C,cAAY,SAAS,uBAAuB,MAAM,QAAQ,CAAC,SAAS;AACnE,QAAI,KAAK,eAAe,oBAAoB;AAC3C,YAAM,OAAO,KAAK,QAAQ,cAAc,UAAU,KAAK;AACvD,YAAM,UAAU,KAAK,QAAQ,cAAc;AAE3C,UAAI,QAAQ,SAAS;AACpB,oBAAY,OAAO,IAAI;AAAA,MACxB;AAAA,IACD;AAAA,EACD,CAAC;AAED,cAAY,SAAS,cAAc,MAAM,QAAQ,CAAC,SAAS;AAC1D,UAAM,UAAU,KAAK;AACrB,UAAM,OACL,KAAK,YAAY,cAAc,UAAU,KAAK,QAC9C,KAAK,aAAa,cAAc,UAAU,KAAK;AAEhD,QAAI,WAAW,MAAM;AACpB,kBAAY,OAAO,IAAI;AAAA,IACxB;AAAA,EACD,CAAC;AAED,SAAO;AAAA,IACN,QAAQ,YAAY;AAAA,IACpB,SAAS,2BAA2B,IAAI,WAAW,YAAY,SAAS,GAAI,CAAC;AAAA,IAC7E,OAAO,YAAY,SAAS,OAAO,WAAW;AAAA,IAC9C,aAAa,QAAQ,QAAQ,WAAW;AAAA,IACxC,aAAa,oBAAoB,YAAY,GAAI;AAAA,IACjD,YAAY,YAAY;AAAA,EACzB;AACD;", "names": ["result"]}