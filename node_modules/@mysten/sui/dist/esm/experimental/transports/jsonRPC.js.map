{"version": 3, "sources": ["../../../../src/experimental/transports/jsonRPC.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { fromBase64 } from '@mysten/bcs';\n\nimport { bcs } from '../../bcs/index.js';\nimport type {\n\tObjectOwner,\n\tSuiClient,\n\tSuiObjectChange,\n\tSuiObjectData,\n\tSuiTransactionBlockResponse,\n\tTransactionEffects,\n} from '../../client/index.js';\nimport { Transaction } from '../../transactions/Transaction.js';\nimport { Experimental_CoreClient } from '../core.js';\nimport { ObjectError } from '../errors.js';\nimport type { Experimental_SuiClientTypes } from '../types.js';\nimport { parseTransactionBcs, parseTransactionEffectsBcs } from './utils.js';\nimport { suiClientResolveTransactionPlugin } from './json-rpc-resolver.js';\nimport { TransactionDataBuilder } from '../../transactions/TransactionData.js';\nimport { chunk } from '@mysten/utils';\n\nexport class JSONRpcTransport extends Experimental_CoreClient {\n\t#jsonRpcClient: SuiClient;\n\n\tconstructor({\n\t\tjsonRpcClient,\n\t\tmvr,\n\t}: {\n\t\tjsonRpcClient: SuiClient;\n\t\tmvr?: Experimental_SuiClientTypes.MvrOptions;\n\t}) {\n\t\tsuper({ network: jsonRpcClient.network, base: jsonRpcClient, mvr });\n\t\tthis.#jsonRpcClient = jsonRpcClient;\n\t}\n\n\tasync getObjects(options: Experimental_SuiClientTypes.GetObjectsOptions) {\n\t\tconst batches = chunk(options.objectIds, 50);\n\t\tconst results: Experimental_SuiClientTypes.GetObjectsResponse['objects'] = [];\n\n\t\tfor (const batch of batches) {\n\t\t\tconst objects = await this.#jsonRpcClient.multiGetObjects({\n\t\t\t\tids: batch,\n\t\t\t\toptions: {\n\t\t\t\t\tshowOwner: true,\n\t\t\t\t\tshowType: true,\n\t\t\t\t\tshowBcs: true,\n\t\t\t\t},\n\t\t\t\tsignal: options.signal,\n\t\t\t});\n\n\t\t\tfor (const [idx, object] of objects.entries()) {\n\t\t\t\tif (object.error) {\n\t\t\t\t\tresults.push(ObjectError.fromResponse(object.error, batch[idx]));\n\t\t\t\t} else {\n\t\t\t\t\tresults.push(parseObject(object.data!));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn {\n\t\t\tobjects: results,\n\t\t};\n\t}\n\tasync getOwnedObjects(options: Experimental_SuiClientTypes.GetOwnedObjectsOptions) {\n\t\tconst objects = await this.#jsonRpcClient.getOwnedObjects({\n\t\t\towner: options.address,\n\t\t\tlimit: options.limit,\n\t\t\tcursor: options.cursor,\n\t\t\toptions: {\n\t\t\t\tshowOwner: true,\n\t\t\t\tshowType: true,\n\t\t\t\tshowBcs: true,\n\t\t\t},\n\t\t\tfilter: options.type ? { StructType: options.type } : null,\n\t\t\tsignal: options.signal,\n\t\t});\n\n\t\treturn {\n\t\t\tobjects: objects.data.map((result) => {\n\t\t\t\tif (result.error) {\n\t\t\t\t\tthrow ObjectError.fromResponse(result.error);\n\t\t\t\t}\n\n\t\t\t\treturn parseObject(result.data!);\n\t\t\t}),\n\t\t\thasNextPage: objects.hasNextPage,\n\t\t\tcursor: objects.nextCursor ?? null,\n\t\t};\n\t}\n\n\tasync getCoins(options: Experimental_SuiClientTypes.GetCoinsOptions) {\n\t\tconst coins = await this.#jsonRpcClient.getCoins({\n\t\t\towner: options.address,\n\t\t\tcoinType: options.coinType,\n\t\t\tlimit: options.limit,\n\t\t\tcursor: options.cursor,\n\t\t\tsignal: options.signal,\n\t\t});\n\n\t\treturn {\n\t\t\tobjects: coins.data.map((coin) => {\n\t\t\t\treturn {\n\t\t\t\t\tid: coin.coinObjectId,\n\t\t\t\t\tversion: coin.version,\n\t\t\t\t\tdigest: coin.digest,\n\t\t\t\t\tbalance: coin.balance,\n\t\t\t\t\ttype: `0x2::coin::Coin<${coin.coinType}>`,\n\t\t\t\t\tcontent: Promise.resolve(\n\t\t\t\t\t\tCoin.serialize({\n\t\t\t\t\t\t\tid: coin.coinObjectId,\n\t\t\t\t\t\t\tbalance: {\n\t\t\t\t\t\t\t\tvalue: coin.balance,\n\t\t\t\t\t\t\t},\n\t\t\t\t\t\t}).toBytes(),\n\t\t\t\t\t),\n\t\t\t\t\towner: {\n\t\t\t\t\t\t$kind: 'ObjectOwner' as const,\n\t\t\t\t\t\tObjectOwner: options.address,\n\t\t\t\t\t},\n\t\t\t\t};\n\t\t\t}),\n\t\t\thasNextPage: coins.hasNextPage,\n\t\t\tcursor: coins.nextCursor ?? null,\n\t\t};\n\t}\n\n\tasync getBalance(options: Experimental_SuiClientTypes.GetBalanceOptions) {\n\t\tconst balance = await this.#jsonRpcClient.getBalance({\n\t\t\towner: options.address,\n\t\t\tcoinType: options.coinType,\n\t\t\tsignal: options.signal,\n\t\t});\n\n\t\treturn {\n\t\t\tbalance: {\n\t\t\t\tcoinType: balance.coinType,\n\t\t\t\tbalance: balance.totalBalance,\n\t\t\t},\n\t\t};\n\t}\n\tasync getAllBalances(options: Experimental_SuiClientTypes.GetAllBalancesOptions) {\n\t\tconst balances = await this.#jsonRpcClient.getAllBalances({\n\t\t\towner: options.address,\n\t\t\tsignal: options.signal,\n\t\t});\n\n\t\treturn {\n\t\t\tbalances: balances.map((balance) => ({\n\t\t\t\tcoinType: balance.coinType,\n\t\t\t\tbalance: balance.totalBalance,\n\t\t\t})),\n\t\t\thasNextPage: false,\n\t\t\tcursor: null,\n\t\t};\n\t}\n\tasync getTransaction(options: Experimental_SuiClientTypes.GetTransactionOptions) {\n\t\tconst transaction = await this.#jsonRpcClient.getTransactionBlock({\n\t\t\tdigest: options.digest,\n\t\t\toptions: {\n\t\t\t\tshowRawInput: true,\n\t\t\t\tshowObjectChanges: true,\n\t\t\t\tshowRawEffects: true,\n\t\t\t\tshowEvents: true,\n\t\t\t\tshowEffects: true,\n\t\t\t},\n\t\t\tsignal: options.signal,\n\t\t});\n\n\t\treturn {\n\t\t\ttransaction: parseTransaction(transaction),\n\t\t};\n\t}\n\tasync executeTransaction(options: Experimental_SuiClientTypes.ExecuteTransactionOptions) {\n\t\tconst transaction = await this.#jsonRpcClient.executeTransactionBlock({\n\t\t\ttransactionBlock: options.transaction,\n\t\t\tsignature: options.signatures,\n\t\t\toptions: {\n\t\t\t\tshowRawEffects: true,\n\t\t\t\tshowEvents: true,\n\t\t\t\tshowObjectChanges: true,\n\t\t\t\tshowRawInput: true,\n\t\t\t\tshowEffects: true,\n\t\t\t},\n\t\t\tsignal: options.signal,\n\t\t});\n\n\t\treturn {\n\t\t\ttransaction: parseTransaction(transaction),\n\t\t};\n\t}\n\tasync dryRunTransaction(options: Experimental_SuiClientTypes.DryRunTransactionOptions) {\n\t\tconst tx = Transaction.from(options.transaction);\n\t\tconst result = await this.#jsonRpcClient.dryRunTransactionBlock({\n\t\t\ttransactionBlock: options.transaction,\n\t\t\tsignal: options.signal,\n\t\t});\n\n\t\tconst { effects, objectTypes } = parseTransactionEffectsJson({\n\t\t\teffects: result.effects,\n\t\t\tobjectChanges: result.objectChanges,\n\t\t});\n\n\t\treturn {\n\t\t\ttransaction: {\n\t\t\t\tdigest: await tx.getDigest(),\n\t\t\t\tepoch: null,\n\t\t\t\teffects,\n\t\t\t\tobjectTypes: Promise.resolve(objectTypes),\n\t\t\t\tsignatures: [],\n\t\t\t\ttransaction: parseTransactionBcs(options.transaction),\n\t\t\t},\n\t\t};\n\t}\n\tasync getReferenceGasPrice(options?: Experimental_SuiClientTypes.GetReferenceGasPriceOptions) {\n\t\tconst referenceGasPrice = await this.#jsonRpcClient.getReferenceGasPrice({\n\t\t\tsignal: options?.signal,\n\t\t});\n\t\treturn {\n\t\t\treferenceGasPrice: String(referenceGasPrice),\n\t\t};\n\t}\n\n\tasync getDynamicFields(options: Experimental_SuiClientTypes.GetDynamicFieldsOptions) {\n\t\tconst dynamicFields = await this.#jsonRpcClient.getDynamicFields({\n\t\t\tparentId: options.parentId,\n\t\t\tlimit: options.limit,\n\t\t\tcursor: options.cursor,\n\t\t});\n\n\t\treturn {\n\t\t\tdynamicFields: dynamicFields.data.map((dynamicField) => {\n\t\t\t\treturn {\n\t\t\t\t\tid: dynamicField.objectId,\n\t\t\t\t\ttype: dynamicField.objectType,\n\t\t\t\t\tname: {\n\t\t\t\t\t\ttype: dynamicField.name.type,\n\t\t\t\t\t\tbcs: fromBase64(dynamicField.bcsName),\n\t\t\t\t\t},\n\t\t\t\t};\n\t\t\t}),\n\t\t\thasNextPage: dynamicFields.hasNextPage,\n\t\t\tcursor: dynamicFields.nextCursor,\n\t\t};\n\t}\n\n\tasync verifyZkLoginSignature(options: Experimental_SuiClientTypes.VerifyZkLoginSignatureOptions) {\n\t\tconst result = await this.#jsonRpcClient.verifyZkLoginSignature({\n\t\t\tbytes: options.bytes,\n\t\t\tsignature: options.signature,\n\t\t\tintentScope: options.intentScope,\n\t\t\tauthor: options.author,\n\t\t});\n\n\t\treturn {\n\t\t\tsuccess: result.success,\n\t\t\terrors: result.errors,\n\t\t};\n\t}\n\n\tresolveNameServiceNames(\n\t\toptions: Experimental_SuiClientTypes.ResolveNameServiceNamesOptions,\n\t): Promise<Experimental_SuiClientTypes.ResolveNameServiceNamesResponse> {\n\t\treturn this.#jsonRpcClient.resolveNameServiceNames(options);\n\t}\n\n\tresolveTransactionPlugin() {\n\t\treturn suiClientResolveTransactionPlugin(this.#jsonRpcClient);\n\t}\n}\n\nfunction parseObject(object: SuiObjectData): Experimental_SuiClientTypes.ObjectResponse {\n\treturn {\n\t\tid: object.objectId,\n\t\tversion: object.version,\n\t\tdigest: object.digest,\n\t\ttype: object.type!,\n\t\tcontent: Promise.resolve(\n\t\t\tobject.bcs?.dataType === 'moveObject' ? fromBase64(object.bcs.bcsBytes) : new Uint8Array(),\n\t\t),\n\t\towner: parseOwner(object.owner!),\n\t};\n}\n\nfunction parseOwner(owner: ObjectOwner): Experimental_SuiClientTypes.ObjectOwner {\n\tif (owner === 'Immutable') {\n\t\treturn {\n\t\t\t$kind: 'Immutable',\n\t\t\tImmutable: true,\n\t\t};\n\t}\n\n\tif ('ConsensusAddressOwner' in owner) {\n\t\treturn {\n\t\t\t$kind: 'ConsensusAddressOwner',\n\t\t\tConsensusAddressOwner: {\n\t\t\t\towner: owner.ConsensusAddressOwner.owner,\n\t\t\t\tstartVersion: owner.ConsensusAddressOwner.start_version,\n\t\t\t},\n\t\t};\n\t}\n\n\tif ('AddressOwner' in owner) {\n\t\treturn {\n\t\t\t$kind: 'AddressOwner',\n\t\t\tAddressOwner: owner.AddressOwner,\n\t\t};\n\t}\n\n\tif ('ObjectOwner' in owner) {\n\t\treturn {\n\t\t\t$kind: 'ObjectOwner',\n\t\t\tObjectOwner: owner.ObjectOwner,\n\t\t};\n\t}\n\n\tif ('Shared' in owner) {\n\t\treturn {\n\t\t\t$kind: 'Shared',\n\t\t\tShared: {\n\t\t\t\tinitialSharedVersion: owner.Shared.initial_shared_version,\n\t\t\t},\n\t\t};\n\t}\n\n\tthrow new Error(`Unknown owner type: ${JSON.stringify(owner)}`);\n}\n\nfunction parseTransaction(\n\ttransaction: SuiTransactionBlockResponse,\n): Experimental_SuiClientTypes.TransactionResponse {\n\tconst parsedTx = bcs.SenderSignedData.parse(fromBase64(transaction.rawTransaction!))[0];\n\tconst objectTypes: Record<string, string> = {};\n\n\ttransaction.objectChanges?.forEach((change) => {\n\t\tif (change.type !== 'published') {\n\t\t\tobjectTypes[change.objectId] = change.objectType;\n\t\t}\n\t});\n\n\tconst bytes = bcs.TransactionData.serialize(parsedTx.intentMessage.value).toBytes();\n\n\tconst data = TransactionDataBuilder.restore({\n\t\tversion: 2,\n\t\tsender: parsedTx.intentMessage.value.V1.sender,\n\t\texpiration: parsedTx.intentMessage.value.V1.expiration,\n\t\tgasData: parsedTx.intentMessage.value.V1.gasData,\n\t\tinputs: parsedTx.intentMessage.value.V1.kind.ProgrammableTransaction!.inputs,\n\t\tcommands: parsedTx.intentMessage.value.V1.kind.ProgrammableTransaction!.commands,\n\t});\n\n\treturn {\n\t\tdigest: transaction.digest,\n\t\tepoch: transaction.effects?.executedEpoch ?? null,\n\t\teffects: parseTransactionEffectsBcs(new Uint8Array(transaction.rawEffects!)),\n\t\tobjectTypes: Promise.resolve(objectTypes),\n\t\ttransaction: {\n\t\t\t...data,\n\t\t\tbcs: bytes,\n\t\t},\n\t\tsignatures: parsedTx.txSignatures,\n\t};\n}\n\nfunction parseTransactionEffectsJson({\n\tbytes,\n\teffects,\n\tobjectChanges,\n}: {\n\tbytes?: Uint8Array;\n\teffects: TransactionEffects;\n\tobjectChanges: SuiObjectChange[] | null;\n}): {\n\teffects: Experimental_SuiClientTypes.TransactionEffects;\n\tobjectTypes: Record<string, string>;\n} {\n\tconst changedObjects: Experimental_SuiClientTypes.ChangedObject[] = [];\n\tconst unchangedSharedObjects: Experimental_SuiClientTypes.UnchangedSharedObject[] = [];\n\tconst objectTypes: Record<string, string> = {};\n\n\tobjectChanges?.forEach((change) => {\n\t\tswitch (change.type) {\n\t\t\tcase 'published':\n\t\t\t\tchangedObjects.push({\n\t\t\t\t\tid: change.packageId,\n\t\t\t\t\tinputState: 'DoesNotExist',\n\t\t\t\t\tinputVersion: null,\n\t\t\t\t\tinputDigest: null,\n\t\t\t\t\tinputOwner: null,\n\t\t\t\t\toutputState: 'PackageWrite',\n\t\t\t\t\toutputVersion: change.version,\n\t\t\t\t\toutputDigest: change.digest,\n\t\t\t\t\toutputOwner: null,\n\t\t\t\t\tidOperation: 'Created',\n\t\t\t\t});\n\t\t\t\tbreak;\n\t\t\tcase 'transferred':\n\t\t\t\tchangedObjects.push({\n\t\t\t\t\tid: change.objectId,\n\t\t\t\t\tinputState: 'Exists',\n\t\t\t\t\tinputVersion: change.version,\n\t\t\t\t\tinputDigest: change.digest,\n\t\t\t\t\tinputOwner: {\n\t\t\t\t\t\t$kind: 'AddressOwner' as const,\n\t\t\t\t\t\tAddressOwner: change.sender,\n\t\t\t\t\t},\n\t\t\t\t\toutputState: 'ObjectWrite',\n\t\t\t\t\toutputVersion: change.version,\n\t\t\t\t\toutputDigest: change.digest,\n\t\t\t\t\toutputOwner: parseOwner(change.recipient),\n\t\t\t\t\tidOperation: 'None',\n\t\t\t\t});\n\t\t\t\tobjectTypes[change.objectId] = change.objectType;\n\t\t\t\tbreak;\n\t\t\tcase 'mutated':\n\t\t\t\tchangedObjects.push({\n\t\t\t\t\tid: change.objectId,\n\t\t\t\t\tinputState: 'Exists',\n\t\t\t\t\tinputVersion: change.previousVersion,\n\t\t\t\t\tinputDigest: null,\n\t\t\t\t\tinputOwner: parseOwner(change.owner),\n\t\t\t\t\toutputState: 'ObjectWrite',\n\t\t\t\t\toutputVersion: change.version,\n\t\t\t\t\toutputDigest: change.digest,\n\t\t\t\t\toutputOwner: parseOwner(change.owner),\n\t\t\t\t\tidOperation: 'None',\n\t\t\t\t});\n\t\t\t\tobjectTypes[change.objectId] = change.objectType;\n\t\t\t\tbreak;\n\t\t\tcase 'deleted':\n\t\t\t\tchangedObjects.push({\n\t\t\t\t\tid: change.objectId,\n\t\t\t\t\tinputState: 'Exists',\n\t\t\t\t\tinputVersion: change.version,\n\t\t\t\t\tinputDigest: effects.deleted?.find((d) => d.objectId === change.objectId)?.digest ?? null,\n\t\t\t\t\tinputOwner: null,\n\t\t\t\t\toutputState: 'DoesNotExist',\n\t\t\t\t\toutputVersion: null,\n\t\t\t\t\toutputDigest: null,\n\t\t\t\t\toutputOwner: null,\n\t\t\t\t\tidOperation: 'Deleted',\n\t\t\t\t});\n\t\t\t\tobjectTypes[change.objectId] = change.objectType;\n\t\t\t\tbreak;\n\t\t\tcase 'wrapped':\n\t\t\t\tchangedObjects.push({\n\t\t\t\t\tid: change.objectId,\n\t\t\t\t\tinputState: 'Exists',\n\t\t\t\t\tinputVersion: change.version,\n\t\t\t\t\tinputDigest: null,\n\t\t\t\t\tinputOwner: {\n\t\t\t\t\t\t$kind: 'AddressOwner' as const,\n\t\t\t\t\t\tAddressOwner: change.sender,\n\t\t\t\t\t},\n\t\t\t\t\toutputState: 'ObjectWrite',\n\t\t\t\t\toutputVersion: change.version,\n\t\t\t\t\toutputDigest:\n\t\t\t\t\t\teffects.wrapped?.find((w) => w.objectId === change.objectId)?.digest ?? null,\n\t\t\t\t\toutputOwner: {\n\t\t\t\t\t\t$kind: 'ObjectOwner' as const,\n\t\t\t\t\t\tObjectOwner: change.sender,\n\t\t\t\t\t},\n\t\t\t\t\tidOperation: 'None',\n\t\t\t\t});\n\t\t\t\tobjectTypes[change.objectId] = change.objectType;\n\t\t\t\tbreak;\n\t\t\tcase 'created':\n\t\t\t\tchangedObjects.push({\n\t\t\t\t\tid: change.objectId,\n\t\t\t\t\tinputState: 'DoesNotExist',\n\t\t\t\t\tinputVersion: null,\n\t\t\t\t\tinputDigest: null,\n\t\t\t\t\tinputOwner: null,\n\t\t\t\t\toutputState: 'ObjectWrite',\n\t\t\t\t\toutputVersion: change.version,\n\t\t\t\t\toutputDigest: change.digest,\n\t\t\t\t\toutputOwner: parseOwner(change.owner),\n\t\t\t\t\tidOperation: 'Created',\n\t\t\t\t});\n\t\t\t\tobjectTypes[change.objectId] = change.objectType;\n\t\t\t\tbreak;\n\t\t}\n\t});\n\n\treturn {\n\t\tobjectTypes,\n\t\teffects: {\n\t\t\tbcs: bytes ?? null,\n\t\t\tdigest: effects.transactionDigest,\n\t\t\tversion: 2,\n\t\t\tstatus:\n\t\t\t\teffects.status.status === 'success'\n\t\t\t\t\t? { success: true, error: null }\n\t\t\t\t\t: { success: false, error: effects.status.error! },\n\t\t\tgasUsed: effects.gasUsed,\n\t\t\ttransactionDigest: effects.transactionDigest,\n\t\t\tgasObject: {\n\t\t\t\tid: effects.gasObject?.reference.objectId,\n\t\t\t\tinputState: 'Exists',\n\t\t\t\tinputVersion: null,\n\t\t\t\tinputDigest: null,\n\t\t\t\tinputOwner: null,\n\t\t\t\toutputState: 'ObjectWrite',\n\t\t\t\toutputVersion: effects.gasObject.reference.version,\n\t\t\t\toutputDigest: effects.gasObject.reference.digest,\n\t\t\t\toutputOwner: parseOwner(effects.gasObject.owner),\n\t\t\t\tidOperation: 'None',\n\t\t\t},\n\t\t\teventsDigest: effects.eventsDigest ?? null,\n\t\t\tdependencies: effects.dependencies ?? [],\n\t\t\tlamportVersion: effects.gasObject.reference.version,\n\t\t\tchangedObjects,\n\t\t\tunchangedSharedObjects,\n\t\t\tauxiliaryDataDigest: null,\n\t\t},\n\t};\n}\n\nconst Balance = bcs.struct('Balance', {\n\tvalue: bcs.u64(),\n});\n\nconst Coin = bcs.struct('Coin', {\n\tid: bcs.Address,\n\tbalance: Balance,\n});\n"], "mappings": ";;;;;;;AAAA;AAGA,SAAS,kBAAkB;AAE3B,SAAS,WAAW;AASpB,SAAS,mBAAmB;AAC5B,SAAS,+BAA+B;AACxC,SAAS,mBAAmB;AAE5B,SAAS,qBAAqB,kCAAkC;AAChE,SAAS,yCAAyC;AAClD,SAAS,8BAA8B;AACvC,SAAS,aAAa;AAEf,MAAM,yBAAyB,wBAAwB;AAAA,EAG7D,YAAY;AAAA,IACX;AAAA,IACA;AAAA,EACD,GAGG;AACF,UAAM,EAAE,SAAS,cAAc,SAAS,MAAM,eAAe,IAAI,CAAC;AATnE;AAUC,uBAAK,gBAAiB;AAAA,EACvB;AAAA,EAEA,MAAM,WAAW,SAAwD;AACxE,UAAM,UAAU,MAAM,QAAQ,WAAW,EAAE;AAC3C,UAAM,UAAqE,CAAC;AAE5E,eAAW,SAAS,SAAS;AAC5B,YAAM,UAAU,MAAM,mBAAK,gBAAe,gBAAgB;AAAA,QACzD,KAAK;AAAA,QACL,SAAS;AAAA,UACR,WAAW;AAAA,UACX,UAAU;AAAA,UACV,SAAS;AAAA,QACV;AAAA,QACA,QAAQ,QAAQ;AAAA,MACjB,CAAC;AAED,iBAAW,CAAC,KAAK,MAAM,KAAK,QAAQ,QAAQ,GAAG;AAC9C,YAAI,OAAO,OAAO;AACjB,kBAAQ,KAAK,YAAY,aAAa,OAAO,OAAO,MAAM,GAAG,CAAC,CAAC;AAAA,QAChE,OAAO;AACN,kBAAQ,KAAK,YAAY,OAAO,IAAK,CAAC;AAAA,QACvC;AAAA,MACD;AAAA,IACD;AAEA,WAAO;AAAA,MACN,SAAS;AAAA,IACV;AAAA,EACD;AAAA,EACA,MAAM,gBAAgB,SAA6D;AAClF,UAAM,UAAU,MAAM,mBAAK,gBAAe,gBAAgB;AAAA,MACzD,OAAO,QAAQ;AAAA,MACf,OAAO,QAAQ;AAAA,MACf,QAAQ,QAAQ;AAAA,MAChB,SAAS;AAAA,QACR,WAAW;AAAA,QACX,UAAU;AAAA,QACV,SAAS;AAAA,MACV;AAAA,MACA,QAAQ,QAAQ,OAAO,EAAE,YAAY,QAAQ,KAAK,IAAI;AAAA,MACtD,QAAQ,QAAQ;AAAA,IACjB,CAAC;AAED,WAAO;AAAA,MACN,SAAS,QAAQ,KAAK,IAAI,CAAC,WAAW;AACrC,YAAI,OAAO,OAAO;AACjB,gBAAM,YAAY,aAAa,OAAO,KAAK;AAAA,QAC5C;AAEA,eAAO,YAAY,OAAO,IAAK;AAAA,MAChC,CAAC;AAAA,MACD,aAAa,QAAQ;AAAA,MACrB,QAAQ,QAAQ,cAAc;AAAA,IAC/B;AAAA,EACD;AAAA,EAEA,MAAM,SAAS,SAAsD;AACpE,UAAM,QAAQ,MAAM,mBAAK,gBAAe,SAAS;AAAA,MAChD,OAAO,QAAQ;AAAA,MACf,UAAU,QAAQ;AAAA,MAClB,OAAO,QAAQ;AAAA,MACf,QAAQ,QAAQ;AAAA,MAChB,QAAQ,QAAQ;AAAA,IACjB,CAAC;AAED,WAAO;AAAA,MACN,SAAS,MAAM,KAAK,IAAI,CAAC,SAAS;AACjC,eAAO;AAAA,UACN,IAAI,KAAK;AAAA,UACT,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,SAAS,KAAK;AAAA,UACd,MAAM,mBAAmB,KAAK,QAAQ;AAAA,UACtC,SAAS,QAAQ;AAAA,YAChB,KAAK,UAAU;AAAA,cACd,IAAI,KAAK;AAAA,cACT,SAAS;AAAA,gBACR,OAAO,KAAK;AAAA,cACb;AAAA,YACD,CAAC,EAAE,QAAQ;AAAA,UACZ;AAAA,UACA,OAAO;AAAA,YACN,OAAO;AAAA,YACP,aAAa,QAAQ;AAAA,UACtB;AAAA,QACD;AAAA,MACD,CAAC;AAAA,MACD,aAAa,MAAM;AAAA,MACnB,QAAQ,MAAM,cAAc;AAAA,IAC7B;AAAA,EACD;AAAA,EAEA,MAAM,WAAW,SAAwD;AACxE,UAAM,UAAU,MAAM,mBAAK,gBAAe,WAAW;AAAA,MACpD,OAAO,QAAQ;AAAA,MACf,UAAU,QAAQ;AAAA,MAClB,QAAQ,QAAQ;AAAA,IACjB,CAAC;AAED,WAAO;AAAA,MACN,SAAS;AAAA,QACR,UAAU,QAAQ;AAAA,QAClB,SAAS,QAAQ;AAAA,MAClB;AAAA,IACD;AAAA,EACD;AAAA,EACA,MAAM,eAAe,SAA4D;AAChF,UAAM,WAAW,MAAM,mBAAK,gBAAe,eAAe;AAAA,MACzD,OAAO,QAAQ;AAAA,MACf,QAAQ,QAAQ;AAAA,IACjB,CAAC;AAED,WAAO;AAAA,MACN,UAAU,SAAS,IAAI,CAAC,aAAa;AAAA,QACpC,UAAU,QAAQ;AAAA,QAClB,SAAS,QAAQ;AAAA,MAClB,EAAE;AAAA,MACF,aAAa;AAAA,MACb,QAAQ;AAAA,IACT;AAAA,EACD;AAAA,EACA,MAAM,eAAe,SAA4D;AAChF,UAAM,cAAc,MAAM,mBAAK,gBAAe,oBAAoB;AAAA,MACjE,QAAQ,QAAQ;AAAA,MAChB,SAAS;AAAA,QACR,cAAc;AAAA,QACd,mBAAmB;AAAA,QACnB,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,aAAa;AAAA,MACd;AAAA,MACA,QAAQ,QAAQ;AAAA,IACjB,CAAC;AAED,WAAO;AAAA,MACN,aAAa,iBAAiB,WAAW;AAAA,IAC1C;AAAA,EACD;AAAA,EACA,MAAM,mBAAmB,SAAgE;AACxF,UAAM,cAAc,MAAM,mBAAK,gBAAe,wBAAwB;AAAA,MACrE,kBAAkB,QAAQ;AAAA,MAC1B,WAAW,QAAQ;AAAA,MACnB,SAAS;AAAA,QACR,gBAAgB;AAAA,QAChB,YAAY;AAAA,QACZ,mBAAmB;AAAA,QACnB,cAAc;AAAA,QACd,aAAa;AAAA,MACd;AAAA,MACA,QAAQ,QAAQ;AAAA,IACjB,CAAC;AAED,WAAO;AAAA,MACN,aAAa,iBAAiB,WAAW;AAAA,IAC1C;AAAA,EACD;AAAA,EACA,MAAM,kBAAkB,SAA+D;AACtF,UAAM,KAAK,YAAY,KAAK,QAAQ,WAAW;AAC/C,UAAM,SAAS,MAAM,mBAAK,gBAAe,uBAAuB;AAAA,MAC/D,kBAAkB,QAAQ;AAAA,MAC1B,QAAQ,QAAQ;AAAA,IACjB,CAAC;AAED,UAAM,EAAE,SAAS,YAAY,IAAI,4BAA4B;AAAA,MAC5D,SAAS,OAAO;AAAA,MAChB,eAAe,OAAO;AAAA,IACvB,CAAC;AAED,WAAO;AAAA,MACN,aAAa;AAAA,QACZ,QAAQ,MAAM,GAAG,UAAU;AAAA,QAC3B,OAAO;AAAA,QACP;AAAA,QACA,aAAa,QAAQ,QAAQ,WAAW;AAAA,QACxC,YAAY,CAAC;AAAA,QACb,aAAa,oBAAoB,QAAQ,WAAW;AAAA,MACrD;AAAA,IACD;AAAA,EACD;AAAA,EACA,MAAM,qBAAqB,SAAmE;AAC7F,UAAM,oBAAoB,MAAM,mBAAK,gBAAe,qBAAqB;AAAA,MACxE,QAAQ,SAAS;AAAA,IAClB,CAAC;AACD,WAAO;AAAA,MACN,mBAAmB,OAAO,iBAAiB;AAAA,IAC5C;AAAA,EACD;AAAA,EAEA,MAAM,iBAAiB,SAA8D;AACpF,UAAM,gBAAgB,MAAM,mBAAK,gBAAe,iBAAiB;AAAA,MAChE,UAAU,QAAQ;AAAA,MAClB,OAAO,QAAQ;AAAA,MACf,QAAQ,QAAQ;AAAA,IACjB,CAAC;AAED,WAAO;AAAA,MACN,eAAe,cAAc,KAAK,IAAI,CAAC,iBAAiB;AACvD,eAAO;AAAA,UACN,IAAI,aAAa;AAAA,UACjB,MAAM,aAAa;AAAA,UACnB,MAAM;AAAA,YACL,MAAM,aAAa,KAAK;AAAA,YACxB,KAAK,WAAW,aAAa,OAAO;AAAA,UACrC;AAAA,QACD;AAAA,MACD,CAAC;AAAA,MACD,aAAa,cAAc;AAAA,MAC3B,QAAQ,cAAc;AAAA,IACvB;AAAA,EACD;AAAA,EAEA,MAAM,uBAAuB,SAAoE;AAChG,UAAM,SAAS,MAAM,mBAAK,gBAAe,uBAAuB;AAAA,MAC/D,OAAO,QAAQ;AAAA,MACf,WAAW,QAAQ;AAAA,MACnB,aAAa,QAAQ;AAAA,MACrB,QAAQ,QAAQ;AAAA,IACjB,CAAC;AAED,WAAO;AAAA,MACN,SAAS,OAAO;AAAA,MAChB,QAAQ,OAAO;AAAA,IAChB;AAAA,EACD;AAAA,EAEA,wBACC,SACuE;AACvE,WAAO,mBAAK,gBAAe,wBAAwB,OAAO;AAAA,EAC3D;AAAA,EAEA,2BAA2B;AAC1B,WAAO,kCAAkC,mBAAK,eAAc;AAAA,EAC7D;AACD;AAtPC;AAwPD,SAAS,YAAY,QAAmE;AACvF,SAAO;AAAA,IACN,IAAI,OAAO;AAAA,IACX,SAAS,OAAO;AAAA,IAChB,QAAQ,OAAO;AAAA,IACf,MAAM,OAAO;AAAA,IACb,SAAS,QAAQ;AAAA,MAChB,OAAO,KAAK,aAAa,eAAe,WAAW,OAAO,IAAI,QAAQ,IAAI,IAAI,WAAW;AAAA,IAC1F;AAAA,IACA,OAAO,WAAW,OAAO,KAAM;AAAA,EAChC;AACD;AAEA,SAAS,WAAW,OAA6D;AAChF,MAAI,UAAU,aAAa;AAC1B,WAAO;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,IACZ;AAAA,EACD;AAEA,MAAI,2BAA2B,OAAO;AACrC,WAAO;AAAA,MACN,OAAO;AAAA,MACP,uBAAuB;AAAA,QACtB,OAAO,MAAM,sBAAsB;AAAA,QACnC,cAAc,MAAM,sBAAsB;AAAA,MAC3C;AAAA,IACD;AAAA,EACD;AAEA,MAAI,kBAAkB,OAAO;AAC5B,WAAO;AAAA,MACN,OAAO;AAAA,MACP,cAAc,MAAM;AAAA,IACrB;AAAA,EACD;AAEA,MAAI,iBAAiB,OAAO;AAC3B,WAAO;AAAA,MACN,OAAO;AAAA,MACP,aAAa,MAAM;AAAA,IACpB;AAAA,EACD;AAEA,MAAI,YAAY,OAAO;AACtB,WAAO;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,QACP,sBAAsB,MAAM,OAAO;AAAA,MACpC;AAAA,IACD;AAAA,EACD;AAEA,QAAM,IAAI,MAAM,uBAAuB,KAAK,UAAU,KAAK,CAAC,EAAE;AAC/D;AAEA,SAAS,iBACR,aACkD;AAClD,QAAM,WAAW,IAAI,iBAAiB,MAAM,WAAW,YAAY,cAAe,CAAC,EAAE,CAAC;AACtF,QAAM,cAAsC,CAAC;AAE7C,cAAY,eAAe,QAAQ,CAAC,WAAW;AAC9C,QAAI,OAAO,SAAS,aAAa;AAChC,kBAAY,OAAO,QAAQ,IAAI,OAAO;AAAA,IACvC;AAAA,EACD,CAAC;AAED,QAAM,QAAQ,IAAI,gBAAgB,UAAU,SAAS,cAAc,KAAK,EAAE,QAAQ;AAElF,QAAM,OAAO,uBAAuB,QAAQ;AAAA,IAC3C,SAAS;AAAA,IACT,QAAQ,SAAS,cAAc,MAAM,GAAG;AAAA,IACxC,YAAY,SAAS,cAAc,MAAM,GAAG;AAAA,IAC5C,SAAS,SAAS,cAAc,MAAM,GAAG;AAAA,IACzC,QAAQ,SAAS,cAAc,MAAM,GAAG,KAAK,wBAAyB;AAAA,IACtE,UAAU,SAAS,cAAc,MAAM,GAAG,KAAK,wBAAyB;AAAA,EACzE,CAAC;AAED,SAAO;AAAA,IACN,QAAQ,YAAY;AAAA,IACpB,OAAO,YAAY,SAAS,iBAAiB;AAAA,IAC7C,SAAS,2BAA2B,IAAI,WAAW,YAAY,UAAW,CAAC;AAAA,IAC3E,aAAa,QAAQ,QAAQ,WAAW;AAAA,IACxC,aAAa;AAAA,MACZ,GAAG;AAAA,MACH,KAAK;AAAA,IACN;AAAA,IACA,YAAY,SAAS;AAAA,EACtB;AACD;AAEA,SAAS,4BAA4B;AAAA,EACpC;AAAA,EACA;AAAA,EACA;AACD,GAOE;AACD,QAAM,iBAA8D,CAAC;AACrE,QAAM,yBAA8E,CAAC;AACrF,QAAM,cAAsC,CAAC;AAE7C,iBAAe,QAAQ,CAAC,WAAW;AAClC,YAAQ,OAAO,MAAM;AAAA,MACpB,KAAK;AACJ,uBAAe,KAAK;AAAA,UACnB,IAAI,OAAO;AAAA,UACX,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,eAAe,OAAO;AAAA,UACtB,cAAc,OAAO;AAAA,UACrB,aAAa;AAAA,UACb,aAAa;AAAA,QACd,CAAC;AACD;AAAA,MACD,KAAK;AACJ,uBAAe,KAAK;AAAA,UACnB,IAAI,OAAO;AAAA,UACX,YAAY;AAAA,UACZ,cAAc,OAAO;AAAA,UACrB,aAAa,OAAO;AAAA,UACpB,YAAY;AAAA,YACX,OAAO;AAAA,YACP,cAAc,OAAO;AAAA,UACtB;AAAA,UACA,aAAa;AAAA,UACb,eAAe,OAAO;AAAA,UACtB,cAAc,OAAO;AAAA,UACrB,aAAa,WAAW,OAAO,SAAS;AAAA,UACxC,aAAa;AAAA,QACd,CAAC;AACD,oBAAY,OAAO,QAAQ,IAAI,OAAO;AACtC;AAAA,MACD,KAAK;AACJ,uBAAe,KAAK;AAAA,UACnB,IAAI,OAAO;AAAA,UACX,YAAY;AAAA,UACZ,cAAc,OAAO;AAAA,UACrB,aAAa;AAAA,UACb,YAAY,WAAW,OAAO,KAAK;AAAA,UACnC,aAAa;AAAA,UACb,eAAe,OAAO;AAAA,UACtB,cAAc,OAAO;AAAA,UACrB,aAAa,WAAW,OAAO,KAAK;AAAA,UACpC,aAAa;AAAA,QACd,CAAC;AACD,oBAAY,OAAO,QAAQ,IAAI,OAAO;AACtC;AAAA,MACD,KAAK;AACJ,uBAAe,KAAK;AAAA,UACnB,IAAI,OAAO;AAAA,UACX,YAAY;AAAA,UACZ,cAAc,OAAO;AAAA,UACrB,aAAa,QAAQ,SAAS,KAAK,CAAC,MAAM,EAAE,aAAa,OAAO,QAAQ,GAAG,UAAU;AAAA,UACrF,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,eAAe;AAAA,UACf,cAAc;AAAA,UACd,aAAa;AAAA,UACb,aAAa;AAAA,QACd,CAAC;AACD,oBAAY,OAAO,QAAQ,IAAI,OAAO;AACtC;AAAA,MACD,KAAK;AACJ,uBAAe,KAAK;AAAA,UACnB,IAAI,OAAO;AAAA,UACX,YAAY;AAAA,UACZ,cAAc,OAAO;AAAA,UACrB,aAAa;AAAA,UACb,YAAY;AAAA,YACX,OAAO;AAAA,YACP,cAAc,OAAO;AAAA,UACtB;AAAA,UACA,aAAa;AAAA,UACb,eAAe,OAAO;AAAA,UACtB,cACC,QAAQ,SAAS,KAAK,CAAC,MAAM,EAAE,aAAa,OAAO,QAAQ,GAAG,UAAU;AAAA,UACzE,aAAa;AAAA,YACZ,OAAO;AAAA,YACP,aAAa,OAAO;AAAA,UACrB;AAAA,UACA,aAAa;AAAA,QACd,CAAC;AACD,oBAAY,OAAO,QAAQ,IAAI,OAAO;AACtC;AAAA,MACD,KAAK;AACJ,uBAAe,KAAK;AAAA,UACnB,IAAI,OAAO;AAAA,UACX,YAAY;AAAA,UACZ,cAAc;AAAA,UACd,aAAa;AAAA,UACb,YAAY;AAAA,UACZ,aAAa;AAAA,UACb,eAAe,OAAO;AAAA,UACtB,cAAc,OAAO;AAAA,UACrB,aAAa,WAAW,OAAO,KAAK;AAAA,UACpC,aAAa;AAAA,QACd,CAAC;AACD,oBAAY,OAAO,QAAQ,IAAI,OAAO;AACtC;AAAA,IACF;AAAA,EACD,CAAC;AAED,SAAO;AAAA,IACN;AAAA,IACA,SAAS;AAAA,MACR,KAAK,SAAS;AAAA,MACd,QAAQ,QAAQ;AAAA,MAChB,SAAS;AAAA,MACT,QACC,QAAQ,OAAO,WAAW,YACvB,EAAE,SAAS,MAAM,OAAO,KAAK,IAC7B,EAAE,SAAS,OAAO,OAAO,QAAQ,OAAO,MAAO;AAAA,MACnD,SAAS,QAAQ;AAAA,MACjB,mBAAmB,QAAQ;AAAA,MAC3B,WAAW;AAAA,QACV,IAAI,QAAQ,WAAW,UAAU;AAAA,QACjC,YAAY;AAAA,QACZ,cAAc;AAAA,QACd,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,eAAe,QAAQ,UAAU,UAAU;AAAA,QAC3C,cAAc,QAAQ,UAAU,UAAU;AAAA,QAC1C,aAAa,WAAW,QAAQ,UAAU,KAAK;AAAA,QAC/C,aAAa;AAAA,MACd;AAAA,MACA,cAAc,QAAQ,gBAAgB;AAAA,MACtC,cAAc,QAAQ,gBAAgB,CAAC;AAAA,MACvC,gBAAgB,QAAQ,UAAU,UAAU;AAAA,MAC5C;AAAA,MACA;AAAA,MACA,qBAAqB;AAAA,IACtB;AAAA,EACD;AACD;AAEA,MAAM,UAAU,IAAI,OAAO,WAAW;AAAA,EACrC,OAAO,IAAI,IAAI;AAChB,CAAC;AAED,MAAM,OAAO,IAAI,OAAO,QAAQ;AAAA,EAC/B,IAAI,IAAI;AAAA,EACR,SAAS;AACV,CAAC;", "names": []}