{"version": 3, "sources": ["../../../src/experimental/cache.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nexport interface ClientCacheOptions {\n\tprefix?: string[];\n\tcache?: Map<string, unknown>;\n}\n\nexport class ClientCache {\n\t#prefix: string[];\n\t#cache: Map<string, unknown>;\n\n\tconstructor({ prefix, cache }: ClientCacheOptions = {}) {\n\t\tthis.#prefix = prefix ?? [];\n\t\tthis.#cache = cache ?? new Map();\n\t}\n\n\tread<T>(key: [string, ...string[]], load: () => T | Promise<T>): T | Promise<T> {\n\t\tconst cacheKey = [this.#prefix, ...key].join(':');\n\n\t\tif (this.#cache.has(cacheKey)) {\n\t\t\treturn this.#cache.get(cacheKey) as T;\n\t\t}\n\n\t\tconst result = load();\n\n\t\tthis.#cache.set(cacheKey, result);\n\n\t\tif (typeof result === 'object' && result !== null && 'then' in result) {\n\t\t\treturn Promise.resolve(result)\n\t\t\t\t.then((v) => {\n\t\t\t\t\tthis.#cache.set(cacheKey, v);\n\t\t\t\t\treturn v as T;\n\t\t\t\t})\n\t\t\t\t.catch((err) => {\n\t\t\t\t\tthis.#cache.delete(cacheKey);\n\t\t\t\t\tthrow err;\n\t\t\t\t});\n\t\t}\n\n\t\treturn result as T;\n\t}\n\n\treadSync<T>(key: [string, ...string[]], load: () => T): T {\n\t\tconst cacheKey = [this.#prefix, ...key].join(':');\n\n\t\tif (this.#cache.has(cacheKey)) {\n\t\t\treturn this.#cache.get(cacheKey) as T;\n\t\t}\n\n\t\tconst result = load();\n\n\t\tthis.#cache.set(cacheKey, result);\n\n\t\treturn result as T;\n\t}\n\n\tclear(prefix?: string[]) {\n\t\tconst prefixKey = [...this.#prefix, ...(prefix ?? [])].join(':');\n\t\tif (!prefixKey) {\n\t\t\tthis.#cache.clear();\n\t\t\treturn;\n\t\t}\n\n\t\tfor (const key of this.#cache.keys()) {\n\t\t\tif (key.startsWith(prefixKey)) {\n\t\t\t\tthis.#cache.delete(key);\n\t\t\t}\n\t\t}\n\t}\n\n\tscope(prefix: string | string[]) {\n\t\treturn new ClientCache({\n\t\t\tprefix: [...this.#prefix, ...(Array.isArray(prefix) ? prefix : [prefix])],\n\t\t\tcache: this.#cache,\n\t\t});\n\t}\n}\n"], "mappings": ";;;;;;;AAAA;AAQO,MAAM,eAAN,MAAM,aAAY;AAAA,EAIxB,YAAY,EAAE,QAAQ,MAAM,IAAwB,CAAC,GAAG;AAHxD;AACA;AAGC,uBAAK,SAAU,UAAU,CAAC;AAC1B,uBAAK,QAAS,SAAS,oBAAI,IAAI;AAAA,EAChC;AAAA,EAEA,KAAQ,KAA4B,MAA4C;AAC/E,UAAM,WAAW,CAAC,mBAAK,UAAS,GAAG,GAAG,EAAE,KAAK,GAAG;AAEhD,QAAI,mBAAK,QAAO,IAAI,QAAQ,GAAG;AAC9B,aAAO,mBAAK,QAAO,IAAI,QAAQ;AAAA,IAChC;AAEA,UAAM,SAAS,KAAK;AAEpB,uBAAK,QAAO,IAAI,UAAU,MAAM;AAEhC,QAAI,OAAO,WAAW,YAAY,WAAW,QAAQ,UAAU,QAAQ;AACtE,aAAO,QAAQ,QAAQ,MAAM,EAC3B,KAAK,CAAC,MAAM;AACZ,2BAAK,QAAO,IAAI,UAAU,CAAC;AAC3B,eAAO;AAAA,MACR,CAAC,EACA,MAAM,CAAC,QAAQ;AACf,2BAAK,QAAO,OAAO,QAAQ;AAC3B,cAAM;AAAA,MACP,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,SAAY,KAA4B,MAAkB;AACzD,UAAM,WAAW,CAAC,mBAAK,UAAS,GAAG,GAAG,EAAE,KAAK,GAAG;AAEhD,QAAI,mBAAK,QAAO,IAAI,QAAQ,GAAG;AAC9B,aAAO,mBAAK,QAAO,IAAI,QAAQ;AAAA,IAChC;AAEA,UAAM,SAAS,KAAK;AAEpB,uBAAK,QAAO,IAAI,UAAU,MAAM;AAEhC,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,QAAmB;AACxB,UAAM,YAAY,CAAC,GAAG,mBAAK,UAAS,GAAI,UAAU,CAAC,CAAE,EAAE,KAAK,GAAG;AAC/D,QAAI,CAAC,WAAW;AACf,yBAAK,QAAO,MAAM;AAClB;AAAA,IACD;AAEA,eAAW,OAAO,mBAAK,QAAO,KAAK,GAAG;AACrC,UAAI,IAAI,WAAW,SAAS,GAAG;AAC9B,2BAAK,QAAO,OAAO,GAAG;AAAA,MACvB;AAAA,IACD;AAAA,EACD;AAAA,EAEA,MAAM,QAA2B;AAChC,WAAO,IAAI,aAAY;AAAA,MACtB,QAAQ,CAAC,GAAG,mBAAK,UAAS,GAAI,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM,CAAE;AAAA,MACxE,OAAO,mBAAK;AAAA,IACb,CAAC;AAAA,EACF;AACD;AApEC;AACA;AAFM,IAAM,cAAN;", "names": []}