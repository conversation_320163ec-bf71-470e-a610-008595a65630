{"version": 3, "sources": ["../../../src/experimental/errors.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { ObjectResponseError } from '../client/index.js';\n\nexport class SuiClientError extends Error {}\n\nexport class ObjectError extends SuiClientError {\n\tcode: string;\n\n\tconstructor(code: string, message: string) {\n\t\tsuper(message);\n\t\tthis.code = code;\n\t}\n\n\tstatic fromResponse(response: ObjectResponseError, objectId?: string): ObjectError {\n\t\tswitch (response.code) {\n\t\t\tcase 'notExists':\n\t\t\t\treturn new ObjectError(response.code, `Object ${response.object_id} does not exist`);\n\t\t\tcase 'dynamicFieldNotFound':\n\t\t\t\treturn new ObjectError(\n\t\t\t\t\tresponse.code,\n\t\t\t\t\t`Dynamic field not found for object ${response.parent_object_id}`,\n\t\t\t\t);\n\t\t\tcase 'deleted':\n\t\t\t\treturn new ObjectError(response.code, `Object ${response.object_id} has been deleted`);\n\t\t\tcase 'displayError':\n\t\t\t\treturn new ObjectError(response.code, `Display error: ${response.error}`);\n\t\t\tcase 'unknown':\n\t\t\tdefault:\n\t\t\t\treturn new ObjectError(\n\t\t\t\t\tresponse.code,\n\t\t\t\t\t`Unknown error while loading object${objectId ? ` ${objectId}` : ''}`,\n\t\t\t\t);\n\t\t}\n\t}\n}\n"], "mappings": "AAKO,MAAM,uBAAuB,MAAM;AAAC;AAEpC,MAAM,oBAAoB,eAAe;AAAA,EAG/C,YAAY,MAAc,SAAiB;AAC1C,UAAM,OAAO;AACb,SAAK,OAAO;AAAA,EACb;AAAA,EAEA,OAAO,aAAa,UAA+B,UAAgC;AAClF,YAAQ,SAAS,MAAM;AAAA,MACtB,KAAK;AACJ,eAAO,IAAI,YAAY,SAAS,MAAM,UAAU,SAAS,SAAS,iBAAiB;AAAA,MACpF,KAAK;AACJ,eAAO,IAAI;AAAA,UACV,SAAS;AAAA,UACT,sCAAsC,SAAS,gBAAgB;AAAA,QAChE;AAAA,MACD,KAAK;AACJ,eAAO,IAAI,YAAY,SAAS,MAAM,UAAU,SAAS,SAAS,mBAAmB;AAAA,MACtF,KAAK;AACJ,eAAO,IAAI,YAAY,SAAS,MAAM,kBAAkB,SAAS,KAAK,EAAE;AAAA,MACzE,KAAK;AAAA,MACL;AACC,eAAO,IAAI;AAAA,UACV,SAAS;AAAA,UACT,qCAAqC,WAAW,IAAI,QAAQ,KAAK,EAAE;AAAA,QACpE;AAAA,IACF;AAAA,EACD;AACD;", "names": []}