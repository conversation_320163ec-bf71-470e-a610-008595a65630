{"version": 3, "sources": ["../../../src/experimental/client.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n/* eslint-disable @typescript-eslint/ban-types */\n\nimport type { Simplify, UnionToIntersection } from '@mysten/utils';\nimport { ClientCache } from './cache.js';\nimport type { Experimental_CoreClient } from './core.js';\nimport type {\n\tClientWithExtensions,\n\tExperimental_SuiClientTypes,\n\tSuiClientRegistration,\n} from './types.js';\n\nexport abstract class Experimental_BaseClient {\n\tnetwork: Experimental_SuiClientTypes.Network;\n\tcache: ClientCache;\n\tbase: Experimental_BaseClient;\n\n\tconstructor({\n\t\tnetwork,\n\t\tbase,\n\t\tcache = base?.cache ?? new ClientCache(),\n\t}: Experimental_SuiClientTypes.SuiClientOptions) {\n\t\tthis.network = network;\n\t\tthis.base = base ?? this;\n\t\tthis.cache = cache;\n\t}\n\n\tabstract core: Experimental_CoreClient;\n\n\t$extend<const Registrations extends SuiClientRegistration<this>[]>(\n\t\t...registrations: Registrations\n\t) {\n\t\treturn Object.create(\n\t\t\tthis,\n\t\t\tObject.fromEntries(\n\t\t\t\tregistrations.map((registration) => {\n\t\t\t\t\tif ('experimental_asClientExtension' in registration) {\n\t\t\t\t\t\tconst { name, register } = registration.experimental_asClientExtension();\n\t\t\t\t\t\treturn [name, { value: register(this) }];\n\t\t\t\t\t}\n\t\t\t\t\treturn [registration.name, { value: registration.register(this) }];\n\t\t\t\t}),\n\t\t\t),\n\t\t) as ClientWithExtensions<\n\t\t\tSimplify<\n\t\t\t\tUnionToIntersection<\n\t\t\t\t\t{\n\t\t\t\t\t\t[K in keyof Registrations]: Registrations[K] extends SuiClientRegistration<\n\t\t\t\t\t\t\tthis,\n\t\t\t\t\t\t\tinfer Name extends string,\n\t\t\t\t\t\t\tinfer Extension\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\t[K2 in Name]: Extension;\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t: never;\n\t\t\t\t\t}[number]\n\t\t\t\t>\n\t\t\t>,\n\t\t\tthis\n\t\t>;\n\t}\n}\n"], "mappings": "AAKA,SAAS,mBAAmB;AAQrB,MAAe,wBAAwB;AAAA,EAK7C,YAAY;AAAA,IACX;AAAA,IACA;AAAA,IACA,QAAQ,MAAM,SAAS,IAAI,YAAY;AAAA,EACxC,GAAiD;AAChD,SAAK,UAAU;AACf,SAAK,OAAO,QAAQ;AACpB,SAAK,QAAQ;AAAA,EACd;AAAA,EAIA,WACI,eACF;AACD,WAAO,OAAO;AAAA,MACb;AAAA,MACA,OAAO;AAAA,QACN,cAAc,IAAI,CAAC,iBAAiB;AACnC,cAAI,oCAAoC,cAAc;AACrD,kBAAM,EAAE,MAAM,SAAS,IAAI,aAAa,+BAA+B;AACvE,mBAAO,CAAC,MAAM,EAAE,OAAO,SAAS,IAAI,EAAE,CAAC;AAAA,UACxC;AACA,iBAAO,CAAC,aAAa,MAAM,EAAE,OAAO,aAAa,SAAS,IAAI,EAAE,CAAC;AAAA,QAClE,CAAC;AAAA,MACF;AAAA,IACD;AAAA,EAkBD;AACD;", "names": []}