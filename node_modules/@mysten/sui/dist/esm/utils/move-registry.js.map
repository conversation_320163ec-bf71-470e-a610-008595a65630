{"version": 3, "sources": ["../../../src/utils/move-registry.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { isValidSuiNSName } from './suins.js';\n\n/** The pattern to find an optionally versioned name */\nconst NAME_PATTERN = /^([a-z0-9]+(?:-[a-z0-9]+)*)$/;\n/** The pattern for a valid version number */\nconst VERSION_REGEX = /^\\d+$/;\n/** The maximum size for an app */\nconst MAX_APP_SIZE = 64;\n/** The separator for the name */\nconst NAME_SEPARATOR = '/';\n\nexport const isValidNamedPackage = (name: string): boolean => {\n\tconst parts = name.split(NAME_SEPARATOR);\n\t// The name has to have 2 parts (without-version), or 3 parts (with version).\n\tif (parts.length < 2 || parts.length > 3) return false;\n\n\tconst [org, app, version] = parts; // split by {org} {app} {optional version}\n\n\t// If the version exists, it must be a number.\n\tif (version !== undefined && !VERSION_REGEX.test(version)) return false;\n\t// Check if the org is a valid SuiNS name.\n\tif (!isValidSuiNSName(org)) return false;\n\n\t// Check if the app is a valid name.\n\treturn NAME_PATTERN.test(app) && app.length < MAX_APP_SIZE;\n};\n\n/**\n * Checks if a type contains valid named packages.\n * This DOES NOT check if the type is a valid Move type.\n */\nexport const isValidNamedType = (type: string): boolean => {\n\t// split our type by all possible type delimeters.\n\tconst splitType = type.split(/::|<|>|,/);\n\tfor (const t of splitType) {\n\t\tif (t.includes(NAME_SEPARATOR) && !isValidNamedPackage(t)) return false;\n\t}\n\t// TODO: Add `isValidStructTag` check once it's introduced.\n\treturn true;\n};\n"], "mappings": "AAGA,SAAS,wBAAwB;AAGjC,MAAM,eAAe;AAErB,MAAM,gBAAgB;AAEtB,MAAM,eAAe;AAErB,MAAM,iBAAiB;AAEhB,MAAM,sBAAsB,CAAC,SAA0B;AAC7D,QAAM,QAAQ,KAAK,MAAM,cAAc;AAEvC,MAAI,MAAM,SAAS,KAAK,MAAM,SAAS,EAAG,QAAO;AAEjD,QAAM,CAAC,KAAK,KAAK,OAAO,IAAI;AAG5B,MAAI,YAAY,UAAa,CAAC,cAAc,KAAK,OAAO,EAAG,QAAO;AAElE,MAAI,CAAC,iBAAiB,GAAG,EAAG,QAAO;AAGnC,SAAO,aAAa,KAAK,GAAG,KAAK,IAAI,SAAS;AAC/C;AAMO,MAAM,mBAAmB,CAAC,SAA0B;AAE1D,QAAM,YAAY,KAAK,MAAM,UAAU;AACvC,aAAW,KAAK,WAAW;AAC1B,QAAI,EAAE,SAAS,cAAc,KAAK,CAAC,oBAAoB,CAAC,EAAG,QAAO;AAAA,EACnE;AAEA,SAAO;AACR;", "names": []}