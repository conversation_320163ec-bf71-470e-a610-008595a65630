{"version": 3, "sources": ["../../../src/client/client.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\nimport { fromBase58, toBase64, toHex } from '@mysten/bcs';\n\nimport type { Signer } from '../cryptography/index.js';\nimport { Experimental_BaseClient } from '../experimental/client.js';\nimport { JSONRpcTransport } from '../experimental/transports/jsonRPC.js';\nimport type {\n\tExperimental_SuiClientTypes,\n\tSelfRegisteringClientExtension,\n} from '../experimental/types.js';\nimport type { Transaction } from '../transactions/Transaction.js';\nimport { isTransaction } from '../transactions/Transaction.js';\nimport {\n\tisValidSuiAddress,\n\tisValidSuiObjectId,\n\tisValidTransactionDigest,\n\tnormalizeSuiAddress,\n\tnormalizeSuiObjectId,\n} from '../utils/sui-types.js';\nimport { normalizeSuiNSName } from '../utils/suins.js';\nimport { SuiHTTPTransport } from './http-transport.js';\nimport type { SuiTransport } from './http-transport.js';\nimport type {\n\tAddressMetrics,\n\tAllEpochsAddressMetrics,\n\tCheckpoint,\n\tCheckpointPage,\n\tCoinBalance,\n\tCoinMetadata,\n\tCoinSupply,\n\tCommitteeInfo,\n\tDelegatedStake,\n\tDevInspectResults,\n\tDevInspectTransactionBlockParams,\n\tDryRunTransactionBlockParams,\n\tDryRunTransactionBlockResponse,\n\tDynamicFieldPage,\n\tEpochInfo,\n\tEpochMetricsPage,\n\tEpochPage,\n\tExecuteTransactionBlockParams,\n\tGetAllBalancesParams,\n\tGetAllCoinsParams,\n\tGetBalanceParams,\n\tGetCheckpointParams,\n\tGetCheckpointsParams,\n\tGetCoinMetadataParams,\n\tGetCoinsParams,\n\tGetCommitteeInfoParams,\n\tGetDynamicFieldObjectParams,\n\tGetDynamicFieldsParams,\n\tGetLatestCheckpointSequenceNumberParams,\n\tGetLatestSuiSystemStateParams,\n\tGetMoveFunctionArgTypesParams,\n\tGetNormalizedMoveFunctionParams,\n\tGetNormalizedMoveModuleParams,\n\tGetNormalizedMoveModulesByPackageParams,\n\tGetNormalizedMoveStructParams,\n\tGetObjectParams,\n\tGetOwnedObjectsParams,\n\tGetProtocolConfigParams,\n\tGetReferenceGasPriceParams,\n\tGetStakesByIdsParams,\n\tGetStakesParams,\n\tGetTotalSupplyParams,\n\tGetTransactionBlockParams,\n\tMoveCallMetrics,\n\tMultiGetObjectsParams,\n\tMultiGetTransactionBlocksParams,\n\tNetworkMetrics,\n\tObjectRead,\n\tOrder,\n\tPaginatedCoins,\n\tPaginatedEvents,\n\tPaginatedObjectsResponse,\n\tPaginatedTransactionResponse,\n\tProtocolConfig,\n\tQueryEventsParams,\n\tQueryTransactionBlocksParams,\n\tResolvedNameServiceNames,\n\tResolveNameServiceAddressParams,\n\tResolveNameServiceNamesParams,\n\tSubscribeEventParams,\n\tSubscribeTransactionParams,\n\tSuiEvent,\n\tSuiMoveFunctionArgType,\n\tSuiMoveNormalizedFunction,\n\tSuiMoveNormalizedModule,\n\tSuiMoveNormalizedModules,\n\tSuiMoveNormalizedStruct,\n\tSuiObjectResponse,\n\tSuiObjectResponseQuery,\n\tSuiSystemStateSummary,\n\tSuiTransactionBlockResponse,\n\tSuiTransactionBlockResponseQuery,\n\tTransactionEffects,\n\tTryGetPastObjectParams,\n\tUnsubscribe,\n\tValidatorsApy,\n\tVerifyZkLoginSignatureParams,\n\tZkLoginVerifyResult,\n} from './types/index.js';\nimport { isValidNamedPackage } from '../utils/move-registry.js';\nimport { hasMvrName } from '../experimental/mvr.js';\n\nexport interface PaginationArguments<Cursor> {\n\t/** Optional paging cursor */\n\tcursor?: Cursor;\n\t/** Maximum item returned per page */\n\tlimit?: number | null;\n}\n\nexport interface OrderArguments {\n\torder?: Order | null;\n}\n\n/**\n * Configuration options for the SuiClient\n * You must provide either a `url` or a `transport`\n */\nexport type SuiClientOptions = NetworkOrTransport & {\n\tnetwork?: Experimental_SuiClientTypes.Network;\n\tmvr?: Experimental_SuiClientTypes.MvrOptions;\n};\n\ntype NetworkOrTransport =\n\t| {\n\t\t\turl: string;\n\t\t\ttransport?: never;\n\t  }\n\t| {\n\t\t\ttransport: SuiTransport;\n\t\t\turl?: never;\n\t  };\n\nconst SUI_CLIENT_BRAND = Symbol.for('@mysten/SuiClient') as never;\n\nexport function isSuiClient(client: unknown): client is SuiClient {\n\treturn (\n\t\ttypeof client === 'object' && client !== null && (client as any)[SUI_CLIENT_BRAND] === true\n\t);\n}\n\nexport class SuiClient extends Experimental_BaseClient implements SelfRegisteringClientExtension {\n\tcore: JSONRpcTransport;\n\tjsonRpc = this;\n\tprotected transport: SuiTransport;\n\n\tget [SUI_CLIENT_BRAND]() {\n\t\treturn true;\n\t}\n\n\t/**\n\t * Establish a connection to a Sui RPC endpoint\n\t *\n\t * @param options configuration options for the API Client\n\t */\n\tconstructor(options: SuiClientOptions) {\n\t\tsuper({ network: options.network ?? 'unknown' });\n\t\tthis.transport = options.transport ?? new SuiHTTPTransport({ url: options.url });\n\t\tthis.core = new JSONRpcTransport({\n\t\t\tjsonRpcClient: this,\n\t\t\tmvr: options.mvr,\n\t\t});\n\t}\n\n\tasync getRpcApiVersion({ signal }: { signal?: AbortSignal } = {}): Promise<string | undefined> {\n\t\tconst resp = await this.transport.request<{ info: { version: string } }>({\n\t\t\tmethod: 'rpc.discover',\n\t\t\tparams: [],\n\t\t\tsignal,\n\t\t});\n\n\t\treturn resp.info.version;\n\t}\n\n\t/**\n\t * Get all Coin<`coin_type`> objects owned by an address.\n\t */\n\tasync getCoins({\n\t\tcoinType,\n\t\towner,\n\t\tcursor,\n\t\tlimit,\n\t\tsignal,\n\t}: GetCoinsParams): Promise<PaginatedCoins> {\n\t\tif (!owner || !isValidSuiAddress(normalizeSuiAddress(owner))) {\n\t\t\tthrow new Error('Invalid Sui address');\n\t\t}\n\n\t\tif (coinType && hasMvrName(coinType)) {\n\t\t\tcoinType = (\n\t\t\t\tawait this.core.mvr.resolveType({\n\t\t\t\t\ttype: coinType,\n\t\t\t\t})\n\t\t\t).type;\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getCoins',\n\t\t\tparams: [owner, coinType, cursor, limit],\n\t\t\tsignal: signal,\n\t\t});\n\t}\n\n\t/**\n\t * Get all Coin objects owned by an address.\n\t */\n\tasync getAllCoins(input: GetAllCoinsParams): Promise<PaginatedCoins> {\n\t\tif (!input.owner || !isValidSuiAddress(normalizeSuiAddress(input.owner))) {\n\t\t\tthrow new Error('Invalid Sui address');\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getAllCoins',\n\t\t\tparams: [input.owner, input.cursor, input.limit],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\t/**\n\t * Get the total coin balance for one coin type, owned by the address owner.\n\t */\n\tasync getBalance({ owner, coinType, signal }: GetBalanceParams): Promise<CoinBalance> {\n\t\tif (!owner || !isValidSuiAddress(normalizeSuiAddress(owner))) {\n\t\t\tthrow new Error('Invalid Sui address');\n\t\t}\n\n\t\tif (coinType && hasMvrName(coinType)) {\n\t\t\tcoinType = (\n\t\t\t\tawait this.core.mvr.resolveType({\n\t\t\t\t\ttype: coinType,\n\t\t\t\t})\n\t\t\t).type;\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getBalance',\n\t\t\tparams: [owner, coinType],\n\t\t\tsignal: signal,\n\t\t});\n\t}\n\n\t/**\n\t * Get the total coin balance for all coin types, owned by the address owner.\n\t */\n\tasync getAllBalances(input: GetAllBalancesParams): Promise<CoinBalance[]> {\n\t\tif (!input.owner || !isValidSuiAddress(normalizeSuiAddress(input.owner))) {\n\t\t\tthrow new Error('Invalid Sui address');\n\t\t}\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getAllBalances',\n\t\t\tparams: [input.owner],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\t/**\n\t * Fetch CoinMetadata for a given coin type\n\t */\n\tasync getCoinMetadata({ coinType, signal }: GetCoinMetadataParams): Promise<CoinMetadata | null> {\n\t\tif (coinType && hasMvrName(coinType)) {\n\t\t\tcoinType = (\n\t\t\t\tawait this.core.mvr.resolveType({\n\t\t\t\t\ttype: coinType,\n\t\t\t\t})\n\t\t\t).type;\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getCoinMetadata',\n\t\t\tparams: [coinType],\n\t\t\tsignal: signal,\n\t\t});\n\t}\n\n\t/**\n\t *  Fetch total supply for a coin\n\t */\n\tasync getTotalSupply({ coinType, signal }: GetTotalSupplyParams): Promise<CoinSupply> {\n\t\tif (coinType && hasMvrName(coinType)) {\n\t\t\tcoinType = (\n\t\t\t\tawait this.core.mvr.resolveType({\n\t\t\t\t\ttype: coinType,\n\t\t\t\t})\n\t\t\t).type;\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getTotalSupply',\n\t\t\tparams: [coinType],\n\t\t\tsignal: signal,\n\t\t});\n\t}\n\n\t/**\n\t * Invoke any RPC method\n\t * @param method the method to be invoked\n\t * @param args the arguments to be passed to the RPC request\n\t */\n\tasync call<T = unknown>(\n\t\tmethod: string,\n\t\tparams: unknown[],\n\t\t{ signal }: { signal?: AbortSignal } = {},\n\t): Promise<T> {\n\t\treturn await this.transport.request({ method, params, signal });\n\t}\n\n\t/**\n\t * Get Move function argument types like read, write and full access\n\t */\n\tasync getMoveFunctionArgTypes({\n\t\tpackage: pkg,\n\t\tmodule,\n\t\tfunction: fn,\n\t\tsignal,\n\t}: GetMoveFunctionArgTypesParams): Promise<SuiMoveFunctionArgType[]> {\n\t\tif (pkg && isValidNamedPackage(pkg)) {\n\t\t\tpkg = (\n\t\t\t\tawait this.core.mvr.resolvePackage({\n\t\t\t\t\tpackage: pkg,\n\t\t\t\t})\n\t\t\t).package;\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getMoveFunctionArgTypes',\n\t\t\tparams: [pkg, module, fn],\n\t\t\tsignal: signal,\n\t\t});\n\t}\n\n\t/**\n\t * Get a map from module name to\n\t * structured representations of Move modules\n\t */\n\tasync getNormalizedMoveModulesByPackage({\n\t\tpackage: pkg,\n\t\tsignal,\n\t}: GetNormalizedMoveModulesByPackageParams): Promise<SuiMoveNormalizedModules> {\n\t\tif (pkg && isValidNamedPackage(pkg)) {\n\t\t\tpkg = (\n\t\t\t\tawait this.core.mvr.resolvePackage({\n\t\t\t\t\tpackage: pkg,\n\t\t\t\t})\n\t\t\t).package;\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getNormalizedMoveModulesByPackage',\n\t\t\tparams: [pkg],\n\t\t\tsignal: signal,\n\t\t});\n\t}\n\n\t/**\n\t * Get a structured representation of Move module\n\t */\n\tasync getNormalizedMoveModule({\n\t\tpackage: pkg,\n\t\tmodule,\n\t\tsignal,\n\t}: GetNormalizedMoveModuleParams): Promise<SuiMoveNormalizedModule> {\n\t\tif (pkg && isValidNamedPackage(pkg)) {\n\t\t\tpkg = (\n\t\t\t\tawait this.core.mvr.resolvePackage({\n\t\t\t\t\tpackage: pkg,\n\t\t\t\t})\n\t\t\t).package;\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getNormalizedMoveModule',\n\t\t\tparams: [pkg, module],\n\t\t\tsignal: signal,\n\t\t});\n\t}\n\n\t/**\n\t * Get a structured representation of Move function\n\t */\n\tasync getNormalizedMoveFunction({\n\t\tpackage: pkg,\n\t\tmodule,\n\t\tfunction: fn,\n\t\tsignal,\n\t}: GetNormalizedMoveFunctionParams): Promise<SuiMoveNormalizedFunction> {\n\t\tif (pkg && isValidNamedPackage(pkg)) {\n\t\t\tpkg = (\n\t\t\t\tawait this.core.mvr.resolvePackage({\n\t\t\t\t\tpackage: pkg,\n\t\t\t\t})\n\t\t\t).package;\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getNormalizedMoveFunction',\n\t\t\tparams: [pkg, module, fn],\n\t\t\tsignal: signal,\n\t\t});\n\t}\n\n\t/**\n\t * Get a structured representation of Move struct\n\t */\n\tasync getNormalizedMoveStruct({\n\t\tpackage: pkg,\n\t\tmodule,\n\t\tstruct,\n\t\tsignal,\n\t}: GetNormalizedMoveStructParams): Promise<SuiMoveNormalizedStruct> {\n\t\tif (pkg && isValidNamedPackage(pkg)) {\n\t\t\tpkg = (\n\t\t\t\tawait this.core.mvr.resolvePackage({\n\t\t\t\t\tpackage: pkg,\n\t\t\t\t})\n\t\t\t).package;\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getNormalizedMoveStruct',\n\t\t\tparams: [pkg, module, struct],\n\t\t\tsignal: signal,\n\t\t});\n\t}\n\n\t/**\n\t * Get all objects owned by an address\n\t */\n\tasync getOwnedObjects(input: GetOwnedObjectsParams): Promise<PaginatedObjectsResponse> {\n\t\tif (!input.owner || !isValidSuiAddress(normalizeSuiAddress(input.owner))) {\n\t\t\tthrow new Error('Invalid Sui address');\n\t\t}\n\n\t\tconst filter = input.filter\n\t\t\t? {\n\t\t\t\t\t...input.filter,\n\t\t\t\t}\n\t\t\t: undefined;\n\n\t\tif (filter && 'MoveModule' in filter && isValidNamedPackage(filter.MoveModule.package)) {\n\t\t\tfilter.MoveModule = {\n\t\t\t\tmodule: filter.MoveModule.module,\n\t\t\t\tpackage: (\n\t\t\t\t\tawait this.core.mvr.resolvePackage({\n\t\t\t\t\t\tpackage: filter.MoveModule.package,\n\t\t\t\t\t})\n\t\t\t\t).package,\n\t\t\t};\n\t\t} else if (filter && 'StructType' in filter && hasMvrName(filter.StructType)) {\n\t\t\tfilter.StructType = (\n\t\t\t\tawait this.core.mvr.resolveType({\n\t\t\t\t\ttype: filter.StructType,\n\t\t\t\t})\n\t\t\t).type;\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getOwnedObjects',\n\t\t\tparams: [\n\t\t\t\tinput.owner,\n\t\t\t\t{\n\t\t\t\t\tfilter,\n\t\t\t\t\toptions: input.options,\n\t\t\t\t} as SuiObjectResponseQuery,\n\t\t\t\tinput.cursor,\n\t\t\t\tinput.limit,\n\t\t\t],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\t/**\n\t * Get details about an object\n\t */\n\tasync getObject(input: GetObjectParams): Promise<SuiObjectResponse> {\n\t\tif (!input.id || !isValidSuiObjectId(normalizeSuiObjectId(input.id))) {\n\t\t\tthrow new Error('Invalid Sui Object id');\n\t\t}\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getObject',\n\t\t\tparams: [input.id, input.options],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\tasync tryGetPastObject(input: TryGetPastObjectParams): Promise<ObjectRead> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_tryGetPastObject',\n\t\t\tparams: [input.id, input.version, input.options],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\t/**\n\t * Batch get details about a list of objects. If any of the object ids are duplicates the call will fail\n\t */\n\tasync multiGetObjects(input: MultiGetObjectsParams): Promise<SuiObjectResponse[]> {\n\t\tinput.ids.forEach((id) => {\n\t\t\tif (!id || !isValidSuiObjectId(normalizeSuiObjectId(id))) {\n\t\t\t\tthrow new Error(`Invalid Sui Object id ${id}`);\n\t\t\t}\n\t\t});\n\t\tconst hasDuplicates = input.ids.length !== new Set(input.ids).size;\n\t\tif (hasDuplicates) {\n\t\t\tthrow new Error(`Duplicate object ids in batch call ${input.ids}`);\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_multiGetObjects',\n\t\t\tparams: [input.ids, input.options],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\t/**\n\t * Get transaction blocks for a given query criteria\n\t */\n\tasync queryTransactionBlocks({\n\t\tfilter,\n\t\toptions,\n\t\tcursor,\n\t\tlimit,\n\t\torder,\n\t\tsignal,\n\t}: QueryTransactionBlocksParams): Promise<PaginatedTransactionResponse> {\n\t\tif (filter && 'MoveFunction' in filter && isValidNamedPackage(filter.MoveFunction.package)) {\n\t\t\tfilter = {\n\t\t\t\t...filter,\n\t\t\t\tMoveFunction: {\n\t\t\t\t\tpackage: (\n\t\t\t\t\t\tawait this.core.mvr.resolvePackage({\n\t\t\t\t\t\t\tpackage: filter.MoveFunction.package,\n\t\t\t\t\t\t})\n\t\t\t\t\t).package,\n\t\t\t\t},\n\t\t\t};\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_queryTransactionBlocks',\n\t\t\tparams: [\n\t\t\t\t{\n\t\t\t\t\tfilter,\n\t\t\t\t\toptions,\n\t\t\t\t} as SuiTransactionBlockResponseQuery,\n\t\t\t\tcursor,\n\t\t\t\tlimit,\n\t\t\t\t(order || 'descending') === 'descending',\n\t\t\t],\n\t\t\tsignal,\n\t\t});\n\t}\n\n\tasync getTransactionBlock(\n\t\tinput: GetTransactionBlockParams,\n\t): Promise<SuiTransactionBlockResponse> {\n\t\tif (!isValidTransactionDigest(input.digest)) {\n\t\t\tthrow new Error('Invalid Transaction digest');\n\t\t}\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getTransactionBlock',\n\t\t\tparams: [input.digest, input.options],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\tasync multiGetTransactionBlocks(\n\t\tinput: MultiGetTransactionBlocksParams,\n\t): Promise<SuiTransactionBlockResponse[]> {\n\t\tinput.digests.forEach((d) => {\n\t\t\tif (!isValidTransactionDigest(d)) {\n\t\t\t\tthrow new Error(`Invalid Transaction digest ${d}`);\n\t\t\t}\n\t\t});\n\n\t\tconst hasDuplicates = input.digests.length !== new Set(input.digests).size;\n\t\tif (hasDuplicates) {\n\t\t\tthrow new Error(`Duplicate digests in batch call ${input.digests}`);\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_multiGetTransactionBlocks',\n\t\t\tparams: [input.digests, input.options],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\tasync executeTransactionBlock({\n\t\ttransactionBlock,\n\t\tsignature,\n\t\toptions,\n\t\trequestType,\n\t\tsignal,\n\t}: ExecuteTransactionBlockParams): Promise<SuiTransactionBlockResponse> {\n\t\tconst result: SuiTransactionBlockResponse = await this.transport.request({\n\t\t\tmethod: 'sui_executeTransactionBlock',\n\t\t\tparams: [\n\t\t\t\ttypeof transactionBlock === 'string' ? transactionBlock : toBase64(transactionBlock),\n\t\t\t\tArray.isArray(signature) ? signature : [signature],\n\t\t\t\toptions,\n\t\t\t],\n\t\t\tsignal,\n\t\t});\n\n\t\tif (requestType === 'WaitForLocalExecution') {\n\t\t\ttry {\n\t\t\t\tawait this.waitForTransaction({\n\t\t\t\t\tdigest: result.digest,\n\t\t\t\t});\n\t\t\t} catch (_) {\n\t\t\t\t// Ignore error while waiting for transaction\n\t\t\t}\n\t\t}\n\n\t\treturn result;\n\t}\n\n\tasync signAndExecuteTransaction({\n\t\ttransaction,\n\t\tsigner,\n\t\t...input\n\t}: {\n\t\ttransaction: Uint8Array | Transaction;\n\t\tsigner: Signer;\n\t} & Omit<\n\t\tExecuteTransactionBlockParams,\n\t\t'transactionBlock' | 'signature'\n\t>): Promise<SuiTransactionBlockResponse> {\n\t\tlet transactionBytes;\n\n\t\tif (transaction instanceof Uint8Array) {\n\t\t\ttransactionBytes = transaction;\n\t\t} else {\n\t\t\ttransaction.setSenderIfNotSet(signer.toSuiAddress());\n\t\t\ttransactionBytes = await transaction.build({ client: this });\n\t\t}\n\n\t\tconst { signature, bytes } = await signer.signTransaction(transactionBytes);\n\n\t\treturn this.executeTransactionBlock({\n\t\t\ttransactionBlock: bytes,\n\t\t\tsignature,\n\t\t\t...input,\n\t\t});\n\t}\n\n\t/**\n\t * Get total number of transactions\n\t */\n\n\tasync getTotalTransactionBlocks({ signal }: { signal?: AbortSignal } = {}): Promise<bigint> {\n\t\tconst resp = await this.transport.request<string>({\n\t\t\tmethod: 'sui_getTotalTransactionBlocks',\n\t\t\tparams: [],\n\t\t\tsignal,\n\t\t});\n\t\treturn BigInt(resp);\n\t}\n\n\t/**\n\t * Getting the reference gas price for the network\n\t */\n\tasync getReferenceGasPrice({ signal }: GetReferenceGasPriceParams = {}): Promise<bigint> {\n\t\tconst resp = await this.transport.request<string>({\n\t\t\tmethod: 'suix_getReferenceGasPrice',\n\t\t\tparams: [],\n\t\t\tsignal,\n\t\t});\n\t\treturn BigInt(resp);\n\t}\n\n\t/**\n\t * Return the delegated stakes for an address\n\t */\n\tasync getStakes(input: GetStakesParams): Promise<DelegatedStake[]> {\n\t\tif (!input.owner || !isValidSuiAddress(normalizeSuiAddress(input.owner))) {\n\t\t\tthrow new Error('Invalid Sui address');\n\t\t}\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getStakes',\n\t\t\tparams: [input.owner],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\t/**\n\t * Return the delegated stakes queried by id.\n\t */\n\tasync getStakesByIds(input: GetStakesByIdsParams): Promise<DelegatedStake[]> {\n\t\tinput.stakedSuiIds.forEach((id) => {\n\t\t\tif (!id || !isValidSuiObjectId(normalizeSuiObjectId(id))) {\n\t\t\t\tthrow new Error(`Invalid Sui Stake id ${id}`);\n\t\t\t}\n\t\t});\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getStakesByIds',\n\t\t\tparams: [input.stakedSuiIds],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\t/**\n\t * Return the latest system state content.\n\t */\n\tasync getLatestSuiSystemState({\n\t\tsignal,\n\t}: GetLatestSuiSystemStateParams = {}): Promise<SuiSystemStateSummary> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getLatestSuiSystemState',\n\t\t\tparams: [],\n\t\t\tsignal,\n\t\t});\n\t}\n\n\t/**\n\t * Get events for a given query criteria\n\t */\n\tasync queryEvents({\n\t\tquery,\n\t\tcursor,\n\t\tlimit,\n\t\torder,\n\t\tsignal,\n\t}: QueryEventsParams): Promise<PaginatedEvents> {\n\t\tif (query && 'MoveEventType' in query && hasMvrName(query.MoveEventType)) {\n\t\t\tquery = {\n\t\t\t\t...query,\n\t\t\t\tMoveEventType: (\n\t\t\t\t\tawait this.core.mvr.resolveType({\n\t\t\t\t\t\ttype: query.MoveEventType,\n\t\t\t\t\t})\n\t\t\t\t).type,\n\t\t\t};\n\t\t}\n\n\t\tif (query && 'MoveEventModule' in query && isValidNamedPackage(query.MoveEventModule.package)) {\n\t\t\tquery = {\n\t\t\t\t...query,\n\t\t\t\tMoveEventModule: {\n\t\t\t\t\tmodule: query.MoveEventModule.module,\n\t\t\t\t\tpackage: (\n\t\t\t\t\t\tawait this.core.mvr.resolvePackage({\n\t\t\t\t\t\t\tpackage: query.MoveEventModule.package,\n\t\t\t\t\t\t})\n\t\t\t\t\t).package,\n\t\t\t\t},\n\t\t\t};\n\t\t}\n\n\t\tif ('MoveModule' in query && isValidNamedPackage(query.MoveModule.package)) {\n\t\t\tquery = {\n\t\t\t\t...query,\n\t\t\t\tMoveModule: {\n\t\t\t\t\tmodule: query.MoveModule.module,\n\t\t\t\t\tpackage: (\n\t\t\t\t\t\tawait this.core.mvr.resolvePackage({\n\t\t\t\t\t\t\tpackage: query.MoveModule.package,\n\t\t\t\t\t\t})\n\t\t\t\t\t).package,\n\t\t\t\t},\n\t\t\t};\n\t\t}\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_queryEvents',\n\t\t\tparams: [query, cursor, limit, (order || 'descending') === 'descending'],\n\t\t\tsignal,\n\t\t});\n\t}\n\n\t/**\n\t * Subscribe to get notifications whenever an event matching the filter occurs\n\t *\n\t * @deprecated\n\t */\n\tasync subscribeEvent(\n\t\tinput: SubscribeEventParams & {\n\t\t\t/** function to run when we receive a notification of a new event matching the filter */\n\t\t\tonMessage: (event: SuiEvent) => void;\n\t\t},\n\t): Promise<Unsubscribe> {\n\t\treturn this.transport.subscribe({\n\t\t\tmethod: 'suix_subscribeEvent',\n\t\t\tunsubscribe: 'suix_unsubscribeEvent',\n\t\t\tparams: [input.filter],\n\t\t\tonMessage: input.onMessage,\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\t/**\n\t * @deprecated\n\t */\n\tasync subscribeTransaction(\n\t\tinput: SubscribeTransactionParams & {\n\t\t\t/** function to run when we receive a notification of a new event matching the filter */\n\t\t\tonMessage: (event: TransactionEffects) => void;\n\t\t},\n\t): Promise<Unsubscribe> {\n\t\treturn this.transport.subscribe({\n\t\t\tmethod: 'suix_subscribeTransaction',\n\t\t\tunsubscribe: 'suix_unsubscribeTransaction',\n\t\t\tparams: [input.filter],\n\t\t\tonMessage: input.onMessage,\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\t/**\n\t * Runs the transaction block in dev-inspect mode. Which allows for nearly any\n\t * transaction (or Move call) with any arguments. Detailed results are\n\t * provided, including both the transaction effects and any return values.\n\t */\n\tasync devInspectTransactionBlock(\n\t\tinput: DevInspectTransactionBlockParams,\n\t): Promise<DevInspectResults> {\n\t\tlet devInspectTxBytes;\n\t\tif (isTransaction(input.transactionBlock)) {\n\t\t\tinput.transactionBlock.setSenderIfNotSet(input.sender);\n\t\t\tdevInspectTxBytes = toBase64(\n\t\t\t\tawait input.transactionBlock.build({\n\t\t\t\t\tclient: this,\n\t\t\t\t\tonlyTransactionKind: true,\n\t\t\t\t}),\n\t\t\t);\n\t\t} else if (typeof input.transactionBlock === 'string') {\n\t\t\tdevInspectTxBytes = input.transactionBlock;\n\t\t} else if (input.transactionBlock instanceof Uint8Array) {\n\t\t\tdevInspectTxBytes = toBase64(input.transactionBlock);\n\t\t} else {\n\t\t\tthrow new Error('Unknown transaction block format.');\n\t\t}\n\n\t\tinput.signal?.throwIfAborted();\n\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_devInspectTransactionBlock',\n\t\t\tparams: [input.sender, devInspectTxBytes, input.gasPrice?.toString(), input.epoch],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\t/**\n\t * Dry run a transaction block and return the result.\n\t */\n\tasync dryRunTransactionBlock(\n\t\tinput: DryRunTransactionBlockParams,\n\t): Promise<DryRunTransactionBlockResponse> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_dryRunTransactionBlock',\n\t\t\tparams: [\n\t\t\t\ttypeof input.transactionBlock === 'string'\n\t\t\t\t\t? input.transactionBlock\n\t\t\t\t\t: toBase64(input.transactionBlock),\n\t\t\t],\n\t\t});\n\t}\n\n\t/**\n\t * Return the list of dynamic field objects owned by an object\n\t */\n\tasync getDynamicFields(input: GetDynamicFieldsParams): Promise<DynamicFieldPage> {\n\t\tif (!input.parentId || !isValidSuiObjectId(normalizeSuiObjectId(input.parentId))) {\n\t\t\tthrow new Error('Invalid Sui Object id');\n\t\t}\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getDynamicFields',\n\t\t\tparams: [input.parentId, input.cursor, input.limit],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\t/**\n\t * Return the dynamic field object information for a specified object\n\t */\n\tasync getDynamicFieldObject(input: GetDynamicFieldObjectParams): Promise<SuiObjectResponse> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getDynamicFieldObject',\n\t\t\tparams: [input.parentId, input.name],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\t/**\n\t * Get the sequence number of the latest checkpoint that has been executed\n\t */\n\tasync getLatestCheckpointSequenceNumber({\n\t\tsignal,\n\t}: GetLatestCheckpointSequenceNumberParams = {}): Promise<string> {\n\t\tconst resp = await this.transport.request({\n\t\t\tmethod: 'sui_getLatestCheckpointSequenceNumber',\n\t\t\tparams: [],\n\t\t\tsignal,\n\t\t});\n\t\treturn String(resp);\n\t}\n\n\t/**\n\t * Returns information about a given checkpoint\n\t */\n\tasync getCheckpoint(input: GetCheckpointParams): Promise<Checkpoint> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getCheckpoint',\n\t\t\tparams: [input.id],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\t/**\n\t * Returns historical checkpoints paginated\n\t */\n\tasync getCheckpoints(\n\t\tinput: PaginationArguments<CheckpointPage['nextCursor']> & GetCheckpointsParams,\n\t): Promise<CheckpointPage> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getCheckpoints',\n\t\t\tparams: [input.cursor, input?.limit, input.descendingOrder],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\t/**\n\t * Return the committee information for the asked epoch\n\t */\n\tasync getCommitteeInfo(input?: GetCommitteeInfoParams): Promise<CommitteeInfo> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getCommitteeInfo',\n\t\t\tparams: [input?.epoch],\n\t\t\tsignal: input?.signal,\n\t\t});\n\t}\n\n\tasync getNetworkMetrics({ signal }: { signal?: AbortSignal } = {}): Promise<NetworkMetrics> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getNetworkMetrics',\n\t\t\tparams: [],\n\t\t\tsignal,\n\t\t});\n\t}\n\n\tasync getAddressMetrics({ signal }: { signal?: AbortSignal } = {}): Promise<AddressMetrics> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getLatestAddressMetrics',\n\t\t\tparams: [],\n\t\t\tsignal,\n\t\t});\n\t}\n\n\tasync getEpochMetrics(\n\t\tinput?: {\n\t\t\tdescendingOrder?: boolean;\n\t\t\tsignal?: AbortSignal;\n\t\t} & PaginationArguments<EpochMetricsPage['nextCursor']>,\n\t): Promise<EpochMetricsPage> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getEpochMetrics',\n\t\t\tparams: [input?.cursor, input?.limit, input?.descendingOrder],\n\t\t\tsignal: input?.signal,\n\t\t});\n\t}\n\n\tasync getAllEpochAddressMetrics(input?: {\n\t\tdescendingOrder?: boolean;\n\t\tsignal?: AbortSignal;\n\t}): Promise<AllEpochsAddressMetrics> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getAllEpochAddressMetrics',\n\t\t\tparams: [input?.descendingOrder],\n\t\t\tsignal: input?.signal,\n\t\t});\n\t}\n\n\t/**\n\t * Return the committee information for the asked epoch\n\t */\n\tasync getEpochs(\n\t\tinput?: {\n\t\t\tdescendingOrder?: boolean;\n\t\t\tsignal?: AbortSignal;\n\t\t} & PaginationArguments<EpochPage['nextCursor']>,\n\t): Promise<EpochPage> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getEpochs',\n\t\t\tparams: [input?.cursor, input?.limit, input?.descendingOrder],\n\t\t\tsignal: input?.signal,\n\t\t});\n\t}\n\n\t/**\n\t * Returns list of top move calls by usage\n\t */\n\tasync getMoveCallMetrics({ signal }: { signal?: AbortSignal } = {}): Promise<MoveCallMetrics> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getMoveCallMetrics',\n\t\t\tparams: [],\n\t\t\tsignal,\n\t\t});\n\t}\n\n\t/**\n\t * Return the committee information for the asked epoch\n\t */\n\tasync getCurrentEpoch({ signal }: { signal?: AbortSignal } = {}): Promise<EpochInfo> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getCurrentEpoch',\n\t\t\tparams: [],\n\t\t\tsignal,\n\t\t});\n\t}\n\n\t/**\n\t * Return the Validators APYs\n\t */\n\tasync getValidatorsApy({ signal }: { signal?: AbortSignal } = {}): Promise<ValidatorsApy> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_getValidatorsApy',\n\t\t\tparams: [],\n\t\t\tsignal,\n\t\t});\n\t}\n\n\t// TODO: Migrate this to `sui_getChainIdentifier` once it is widely available.\n\tasync getChainIdentifier({ signal }: { signal?: AbortSignal } = {}): Promise<string> {\n\t\tconst checkpoint = await this.getCheckpoint({ id: '0', signal });\n\t\tconst bytes = fromBase58(checkpoint.digest);\n\t\treturn toHex(bytes.slice(0, 4));\n\t}\n\n\tasync resolveNameServiceAddress(input: ResolveNameServiceAddressParams): Promise<string | null> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'suix_resolveNameServiceAddress',\n\t\t\tparams: [input.name],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\tasync resolveNameServiceNames({\n\t\tformat = 'dot',\n\t\t...input\n\t}: ResolveNameServiceNamesParams & {\n\t\tformat?: 'at' | 'dot';\n\t}): Promise<ResolvedNameServiceNames> {\n\t\tconst { nextCursor, hasNextPage, data }: ResolvedNameServiceNames =\n\t\t\tawait this.transport.request({\n\t\t\t\tmethod: 'suix_resolveNameServiceNames',\n\t\t\t\tparams: [input.address, input.cursor, input.limit],\n\t\t\t\tsignal: input.signal,\n\t\t\t});\n\n\t\treturn {\n\t\t\thasNextPage,\n\t\t\tnextCursor,\n\t\t\tdata: data.map((name) => normalizeSuiNSName(name, format)),\n\t\t};\n\t}\n\n\tasync getProtocolConfig(input?: GetProtocolConfigParams): Promise<ProtocolConfig> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_getProtocolConfig',\n\t\t\tparams: [input?.version],\n\t\t\tsignal: input?.signal,\n\t\t});\n\t}\n\n\tasync verifyZkLoginSignature(input: VerifyZkLoginSignatureParams): Promise<ZkLoginVerifyResult> {\n\t\treturn await this.transport.request({\n\t\t\tmethod: 'sui_verifyZkLoginSignature',\n\t\t\tparams: [input.bytes, input.signature, input.intentScope, input.author],\n\t\t\tsignal: input.signal,\n\t\t});\n\t}\n\n\t/**\n\t * Wait for a transaction block result to be available over the API.\n\t * This can be used in conjunction with `executeTransactionBlock` to wait for the transaction to\n\t * be available via the API.\n\t * This currently polls the `getTransactionBlock` API to check for the transaction.\n\t */\n\tasync waitForTransaction({\n\t\tsignal,\n\t\ttimeout = 60 * 1000,\n\t\tpollInterval = 2 * 1000,\n\t\t...input\n\t}: {\n\t\t/** An optional abort signal that can be used to cancel */\n\t\tsignal?: AbortSignal;\n\t\t/** The amount of time to wait for a transaction block. Defaults to one minute. */\n\t\ttimeout?: number;\n\t\t/** The amount of time to wait between checks for the transaction block. Defaults to 2 seconds. */\n\t\tpollInterval?: number;\n\t} & Parameters<SuiClient['getTransactionBlock']>[0]): Promise<SuiTransactionBlockResponse> {\n\t\tconst timeoutSignal = AbortSignal.timeout(timeout);\n\t\tconst timeoutPromise = new Promise((_, reject) => {\n\t\t\ttimeoutSignal.addEventListener('abort', () => reject(timeoutSignal.reason));\n\t\t});\n\n\t\ttimeoutPromise.catch(() => {\n\t\t\t// Swallow unhandled rejections that might be thrown after early return\n\t\t});\n\n\t\twhile (!timeoutSignal.aborted) {\n\t\t\tsignal?.throwIfAborted();\n\t\t\ttry {\n\t\t\t\treturn await this.getTransactionBlock(input);\n\t\t\t} catch (e) {\n\t\t\t\t// Wait for either the next poll interval, or the timeout.\n\t\t\t\tawait Promise.race([\n\t\t\t\t\tnew Promise((resolve) => setTimeout(resolve, pollInterval)),\n\t\t\t\t\ttimeoutPromise,\n\t\t\t\t]);\n\t\t\t}\n\t\t}\n\n\t\ttimeoutSignal.throwIfAborted();\n\n\t\t// This should never happen, because the above case should always throw, but just adding it in the event that something goes horribly wrong.\n\t\tthrow new Error('Unexpected error while waiting for transaction block.');\n\t}\n\n\texperimental_asClientExtension(this: SuiClient) {\n\t\treturn {\n\t\t\tname: 'jsonRPC',\n\t\t\tregister: () => {\n\t\t\t\treturn this;\n\t\t\t},\n\t\t} as const;\n\t}\n}\n"], "mappings": "AAEA,SAAS,YAAY,UAAU,aAAa;AAG5C,SAAS,+BAA+B;AACxC,SAAS,wBAAwB;AAMjC,SAAS,qBAAqB;AAC9B;AAAA,EACC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACM;AACP,SAAS,0BAA0B;AACnC,SAAS,wBAAwB;AAkFjC,SAAS,2BAA2B;AACpC,SAAS,kBAAkB;AAgC3B,MAAM,mBAAmB,OAAO,IAAI,mBAAmB;AAEhD,SAAS,YAAY,QAAsC;AACjE,SACC,OAAO,WAAW,YAAY,WAAW,QAAS,OAAe,gBAAgB,MAAM;AAEzF;AAEO,MAAM,kBAAkB,wBAAkE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAchG,YAAY,SAA2B;AACtC,UAAM,EAAE,SAAS,QAAQ,WAAW,UAAU,CAAC;AAbhD,mBAAU;AAcT,SAAK,YAAY,QAAQ,aAAa,IAAI,iBAAiB,EAAE,KAAK,QAAQ,IAAI,CAAC;AAC/E,SAAK,OAAO,IAAI,iBAAiB;AAAA,MAChC,eAAe;AAAA,MACf,KAAK,QAAQ;AAAA,IACd,CAAC;AAAA,EACF;AAAA,EAhBA,KAAK,gBAAgB,IAAI;AACxB,WAAO;AAAA,EACR;AAAA,EAgBA,MAAM,iBAAiB,EAAE,OAAO,IAA8B,CAAC,GAAgC;AAC9F,UAAM,OAAO,MAAM,KAAK,UAAU,QAAuC;AAAA,MACxE,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,MACT;AAAA,IACD,CAAC;AAED,WAAO,KAAK,KAAK;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,SAAS;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAA4C;AAC3C,QAAI,CAAC,SAAS,CAAC,kBAAkB,oBAAoB,KAAK,CAAC,GAAG;AAC7D,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACtC;AAEA,QAAI,YAAY,WAAW,QAAQ,GAAG;AACrC,kBACC,MAAM,KAAK,KAAK,IAAI,YAAY;AAAA,QAC/B,MAAM;AAAA,MACP,CAAC,GACA;AAAA,IACH;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,OAAO,UAAU,QAAQ,KAAK;AAAA,MACvC;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,YAAY,OAAmD;AACpE,QAAI,CAAC,MAAM,SAAS,CAAC,kBAAkB,oBAAoB,MAAM,KAAK,CAAC,GAAG;AACzE,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACtC;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,OAAO,MAAM,QAAQ,MAAM,KAAK;AAAA,MAC/C,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,WAAW,EAAE,OAAO,UAAU,OAAO,GAA2C;AACrF,QAAI,CAAC,SAAS,CAAC,kBAAkB,oBAAoB,KAAK,CAAC,GAAG;AAC7D,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACtC;AAEA,QAAI,YAAY,WAAW,QAAQ,GAAG;AACrC,kBACC,MAAM,KAAK,KAAK,IAAI,YAAY;AAAA,QAC/B,MAAM;AAAA,MACP,CAAC,GACA;AAAA,IACH;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,OAAO,QAAQ;AAAA,MACxB;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe,OAAqD;AACzE,QAAI,CAAC,MAAM,SAAS,CAAC,kBAAkB,oBAAoB,MAAM,KAAK,CAAC,GAAG;AACzE,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACtC;AACA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,KAAK;AAAA,MACpB,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAgB,EAAE,UAAU,OAAO,GAAwD;AAChG,QAAI,YAAY,WAAW,QAAQ,GAAG;AACrC,kBACC,MAAM,KAAK,KAAK,IAAI,YAAY;AAAA,QAC/B,MAAM;AAAA,MACP,CAAC,GACA;AAAA,IACH;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,QAAQ;AAAA,MACjB;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe,EAAE,UAAU,OAAO,GAA8C;AACrF,QAAI,YAAY,WAAW,QAAQ,GAAG;AACrC,kBACC,MAAM,KAAK,KAAK,IAAI,YAAY;AAAA,QAC/B,MAAM;AAAA,MACP,CAAC,GACA;AAAA,IACH;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,QAAQ;AAAA,MACjB;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,KACL,QACA,QACA,EAAE,OAAO,IAA8B,CAAC,GAC3B;AACb,WAAO,MAAM,KAAK,UAAU,QAAQ,EAAE,QAAQ,QAAQ,OAAO,CAAC;AAAA,EAC/D;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,wBAAwB;AAAA,IAC7B,SAAS;AAAA,IACT;AAAA,IACA,UAAU;AAAA,IACV;AAAA,EACD,GAAqE;AACpE,QAAI,OAAO,oBAAoB,GAAG,GAAG;AACpC,aACC,MAAM,KAAK,KAAK,IAAI,eAAe;AAAA,QAClC,SAAS;AAAA,MACV,CAAC,GACA;AAAA,IACH;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,KAAK,QAAQ,EAAE;AAAA,MACxB;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,kCAAkC;AAAA,IACvC,SAAS;AAAA,IACT;AAAA,EACD,GAA+E;AAC9E,QAAI,OAAO,oBAAoB,GAAG,GAAG;AACpC,aACC,MAAM,KAAK,KAAK,IAAI,eAAe;AAAA,QAClC,SAAS;AAAA,MACV,CAAC,GACA;AAAA,IACH;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,GAAG;AAAA,MACZ;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,wBAAwB;AAAA,IAC7B,SAAS;AAAA,IACT;AAAA,IACA;AAAA,EACD,GAAoE;AACnE,QAAI,OAAO,oBAAoB,GAAG,GAAG;AACpC,aACC,MAAM,KAAK,KAAK,IAAI,eAAe;AAAA,QAClC,SAAS;AAAA,MACV,CAAC,GACA;AAAA,IACH;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,KAAK,MAAM;AAAA,MACpB;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,0BAA0B;AAAA,IAC/B,SAAS;AAAA,IACT;AAAA,IACA,UAAU;AAAA,IACV;AAAA,EACD,GAAwE;AACvE,QAAI,OAAO,oBAAoB,GAAG,GAAG;AACpC,aACC,MAAM,KAAK,KAAK,IAAI,eAAe;AAAA,QAClC,SAAS;AAAA,MACV,CAAC,GACA;AAAA,IACH;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,KAAK,QAAQ,EAAE;AAAA,MACxB;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,wBAAwB;AAAA,IAC7B,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAAoE;AACnE,QAAI,OAAO,oBAAoB,GAAG,GAAG;AACpC,aACC,MAAM,KAAK,KAAK,IAAI,eAAe;AAAA,QAClC,SAAS;AAAA,MACV,CAAC,GACA;AAAA,IACH;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,KAAK,QAAQ,MAAM;AAAA,MAC5B;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAgB,OAAiE;AACtF,QAAI,CAAC,MAAM,SAAS,CAAC,kBAAkB,oBAAoB,MAAM,KAAK,CAAC,GAAG;AACzE,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACtC;AAEA,UAAM,SAAS,MAAM,SAClB;AAAA,MACA,GAAG,MAAM;AAAA,IACV,IACC;AAEH,QAAI,UAAU,gBAAgB,UAAU,oBAAoB,OAAO,WAAW,OAAO,GAAG;AACvF,aAAO,aAAa;AAAA,QACnB,QAAQ,OAAO,WAAW;AAAA,QAC1B,UACC,MAAM,KAAK,KAAK,IAAI,eAAe;AAAA,UAClC,SAAS,OAAO,WAAW;AAAA,QAC5B,CAAC,GACA;AAAA,MACH;AAAA,IACD,WAAW,UAAU,gBAAgB,UAAU,WAAW,OAAO,UAAU,GAAG;AAC7E,aAAO,cACN,MAAM,KAAK,KAAK,IAAI,YAAY;AAAA,QAC/B,MAAM,OAAO;AAAA,MACd,CAAC,GACA;AAAA,IACH;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,MAAM;AAAA,QACN;AAAA,UACC;AAAA,UACA,SAAS,MAAM;AAAA,QAChB;AAAA,QACA,MAAM;AAAA,QACN,MAAM;AAAA,MACP;AAAA,MACA,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,UAAU,OAAoD;AACnE,QAAI,CAAC,MAAM,MAAM,CAAC,mBAAmB,qBAAqB,MAAM,EAAE,CAAC,GAAG;AACrE,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACxC;AACA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,IAAI,MAAM,OAAO;AAAA,MAChC,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,iBAAiB,OAAoD;AAC1E,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,IAAI,MAAM,SAAS,MAAM,OAAO;AAAA,MAC/C,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAgB,OAA4D;AACjF,UAAM,IAAI,QAAQ,CAAC,OAAO;AACzB,UAAI,CAAC,MAAM,CAAC,mBAAmB,qBAAqB,EAAE,CAAC,GAAG;AACzD,cAAM,IAAI,MAAM,yBAAyB,EAAE,EAAE;AAAA,MAC9C;AAAA,IACD,CAAC;AACD,UAAM,gBAAgB,MAAM,IAAI,WAAW,IAAI,IAAI,MAAM,GAAG,EAAE;AAC9D,QAAI,eAAe;AAClB,YAAM,IAAI,MAAM,sCAAsC,MAAM,GAAG,EAAE;AAAA,IAClE;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,KAAK,MAAM,OAAO;AAAA,MACjC,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,uBAAuB;AAAA,IAC5B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAAwE;AACvE,QAAI,UAAU,kBAAkB,UAAU,oBAAoB,OAAO,aAAa,OAAO,GAAG;AAC3F,eAAS;AAAA,QACR,GAAG;AAAA,QACH,cAAc;AAAA,UACb,UACC,MAAM,KAAK,KAAK,IAAI,eAAe;AAAA,YAClC,SAAS,OAAO,aAAa;AAAA,UAC9B,CAAC,GACA;AAAA,QACH;AAAA,MACD;AAAA,IACD;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP;AAAA,UACC;AAAA,UACA;AAAA,QACD;AAAA,QACA;AAAA,QACA;AAAA,SACC,SAAS,kBAAkB;AAAA,MAC7B;AAAA,MACA;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,oBACL,OACuC;AACvC,QAAI,CAAC,yBAAyB,MAAM,MAAM,GAAG;AAC5C,YAAM,IAAI,MAAM,4BAA4B;AAAA,IAC7C;AACA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,QAAQ,MAAM,OAAO;AAAA,MACpC,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,0BACL,OACyC;AACzC,UAAM,QAAQ,QAAQ,CAAC,MAAM;AAC5B,UAAI,CAAC,yBAAyB,CAAC,GAAG;AACjC,cAAM,IAAI,MAAM,8BAA8B,CAAC,EAAE;AAAA,MAClD;AAAA,IACD,CAAC;AAED,UAAM,gBAAgB,MAAM,QAAQ,WAAW,IAAI,IAAI,MAAM,OAAO,EAAE;AACtE,QAAI,eAAe;AAClB,YAAM,IAAI,MAAM,mCAAmC,MAAM,OAAO,EAAE;AAAA,IACnE;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,SAAS,MAAM,OAAO;AAAA,MACrC,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,wBAAwB;AAAA,IAC7B;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAAwE;AACvE,UAAM,SAAsC,MAAM,KAAK,UAAU,QAAQ;AAAA,MACxE,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,OAAO,qBAAqB,WAAW,mBAAmB,SAAS,gBAAgB;AAAA,QACnF,MAAM,QAAQ,SAAS,IAAI,YAAY,CAAC,SAAS;AAAA,QACjD;AAAA,MACD;AAAA,MACA;AAAA,IACD,CAAC;AAED,QAAI,gBAAgB,yBAAyB;AAC5C,UAAI;AACH,cAAM,KAAK,mBAAmB;AAAA,UAC7B,QAAQ,OAAO;AAAA,QAChB,CAAC;AAAA,MACF,SAAS,GAAG;AAAA,MAEZ;AAAA,IACD;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,MAAM,0BAA0B;AAAA,IAC/B;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACJ,GAMyC;AACxC,QAAI;AAEJ,QAAI,uBAAuB,YAAY;AACtC,yBAAmB;AAAA,IACpB,OAAO;AACN,kBAAY,kBAAkB,OAAO,aAAa,CAAC;AACnD,yBAAmB,MAAM,YAAY,MAAM,EAAE,QAAQ,KAAK,CAAC;AAAA,IAC5D;AAEA,UAAM,EAAE,WAAW,MAAM,IAAI,MAAM,OAAO,gBAAgB,gBAAgB;AAE1E,WAAO,KAAK,wBAAwB;AAAA,MACnC,kBAAkB;AAAA,MAClB;AAAA,MACA,GAAG;AAAA,IACJ,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,0BAA0B,EAAE,OAAO,IAA8B,CAAC,GAAoB;AAC3F,UAAM,OAAO,MAAM,KAAK,UAAU,QAAgB;AAAA,MACjD,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,MACT;AAAA,IACD,CAAC;AACD,WAAO,OAAO,IAAI;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,qBAAqB,EAAE,OAAO,IAAgC,CAAC,GAAoB;AACxF,UAAM,OAAO,MAAM,KAAK,UAAU,QAAgB;AAAA,MACjD,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,MACT;AAAA,IACD,CAAC;AACD,WAAO,OAAO,IAAI;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,UAAU,OAAmD;AAClE,QAAI,CAAC,MAAM,SAAS,CAAC,kBAAkB,oBAAoB,MAAM,KAAK,CAAC,GAAG;AACzE,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACtC;AACA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,KAAK;AAAA,MACpB,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eAAe,OAAwD;AAC5E,UAAM,aAAa,QAAQ,CAAC,OAAO;AAClC,UAAI,CAAC,MAAM,CAAC,mBAAmB,qBAAqB,EAAE,CAAC,GAAG;AACzD,cAAM,IAAI,MAAM,wBAAwB,EAAE,EAAE;AAAA,MAC7C;AAAA,IACD,CAAC;AACD,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,YAAY;AAAA,MAC3B,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,wBAAwB;AAAA,IAC7B;AAAA,EACD,IAAmC,CAAC,GAAmC;AACtE,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,MACT;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,YAAY;AAAA,IACjB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAAgD;AAC/C,QAAI,SAAS,mBAAmB,SAAS,WAAW,MAAM,aAAa,GAAG;AACzE,cAAQ;AAAA,QACP,GAAG;AAAA,QACH,gBACC,MAAM,KAAK,KAAK,IAAI,YAAY;AAAA,UAC/B,MAAM,MAAM;AAAA,QACb,CAAC,GACA;AAAA,MACH;AAAA,IACD;AAEA,QAAI,SAAS,qBAAqB,SAAS,oBAAoB,MAAM,gBAAgB,OAAO,GAAG;AAC9F,cAAQ;AAAA,QACP,GAAG;AAAA,QACH,iBAAiB;AAAA,UAChB,QAAQ,MAAM,gBAAgB;AAAA,UAC9B,UACC,MAAM,KAAK,KAAK,IAAI,eAAe;AAAA,YAClC,SAAS,MAAM,gBAAgB;AAAA,UAChC,CAAC,GACA;AAAA,QACH;AAAA,MACD;AAAA,IACD;AAEA,QAAI,gBAAgB,SAAS,oBAAoB,MAAM,WAAW,OAAO,GAAG;AAC3E,cAAQ;AAAA,QACP,GAAG;AAAA,QACH,YAAY;AAAA,UACX,QAAQ,MAAM,WAAW;AAAA,UACzB,UACC,MAAM,KAAK,KAAK,IAAI,eAAe;AAAA,YAClC,SAAS,MAAM,WAAW;AAAA,UAC3B,CAAC,GACA;AAAA,QACH;AAAA,MACD;AAAA,IACD;AAEA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,OAAO,QAAQ,QAAQ,SAAS,kBAAkB,YAAY;AAAA,MACvE;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,eACL,OAIuB;AACvB,WAAO,KAAK,UAAU,UAAU;AAAA,MAC/B,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,QAAQ,CAAC,MAAM,MAAM;AAAA,MACrB,WAAW,MAAM;AAAA,MACjB,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,qBACL,OAIuB;AACvB,WAAO,KAAK,UAAU,UAAU;AAAA,MAC/B,QAAQ;AAAA,MACR,aAAa;AAAA,MACb,QAAQ,CAAC,MAAM,MAAM;AAAA,MACrB,WAAW,MAAM;AAAA,MACjB,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,2BACL,OAC6B;AAC7B,QAAI;AACJ,QAAI,cAAc,MAAM,gBAAgB,GAAG;AAC1C,YAAM,iBAAiB,kBAAkB,MAAM,MAAM;AACrD,0BAAoB;AAAA,QACnB,MAAM,MAAM,iBAAiB,MAAM;AAAA,UAClC,QAAQ;AAAA,UACR,qBAAqB;AAAA,QACtB,CAAC;AAAA,MACF;AAAA,IACD,WAAW,OAAO,MAAM,qBAAqB,UAAU;AACtD,0BAAoB,MAAM;AAAA,IAC3B,WAAW,MAAM,4BAA4B,YAAY;AACxD,0BAAoB,SAAS,MAAM,gBAAgB;AAAA,IACpD,OAAO;AACN,YAAM,IAAI,MAAM,mCAAmC;AAAA,IACpD;AAEA,UAAM,QAAQ,eAAe;AAE7B,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,QAAQ,mBAAmB,MAAM,UAAU,SAAS,GAAG,MAAM,KAAK;AAAA,MACjF,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,uBACL,OAC0C;AAC1C,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ;AAAA,QACP,OAAO,MAAM,qBAAqB,WAC/B,MAAM,mBACN,SAAS,MAAM,gBAAgB;AAAA,MACnC;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,iBAAiB,OAA0D;AAChF,QAAI,CAAC,MAAM,YAAY,CAAC,mBAAmB,qBAAqB,MAAM,QAAQ,CAAC,GAAG;AACjF,YAAM,IAAI,MAAM,uBAAuB;AAAA,IACxC;AACA,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,UAAU,MAAM,QAAQ,MAAM,KAAK;AAAA,MAClD,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,sBAAsB,OAAgE;AAC3F,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,UAAU,MAAM,IAAI;AAAA,MACnC,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,kCAAkC;AAAA,IACvC;AAAA,EACD,IAA6C,CAAC,GAAoB;AACjE,UAAM,OAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACzC,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,MACT;AAAA,IACD,CAAC;AACD,WAAO,OAAO,IAAI;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,cAAc,OAAiD;AACpE,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,EAAE;AAAA,MACjB,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,eACL,OAC0B;AAC1B,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,QAAQ,OAAO,OAAO,MAAM,eAAe;AAAA,MAC1D,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,iBAAiB,OAAwD;AAC9E,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,OAAO,KAAK;AAAA,MACrB,QAAQ,OAAO;AAAA,IAChB,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,kBAAkB,EAAE,OAAO,IAA8B,CAAC,GAA4B;AAC3F,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,MACT;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,kBAAkB,EAAE,OAAO,IAA8B,CAAC,GAA4B;AAC3F,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,MACT;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,gBACL,OAI4B;AAC5B,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,OAAO,QAAQ,OAAO,OAAO,OAAO,eAAe;AAAA,MAC5D,QAAQ,OAAO;AAAA,IAChB,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,0BAA0B,OAGK;AACpC,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,OAAO,eAAe;AAAA,MAC/B,QAAQ,OAAO;AAAA,IAChB,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,UACL,OAIqB;AACrB,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,OAAO,QAAQ,OAAO,OAAO,OAAO,eAAe;AAAA,MAC5D,QAAQ,OAAO;AAAA,IAChB,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,mBAAmB,EAAE,OAAO,IAA8B,CAAC,GAA6B;AAC7F,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,MACT;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,gBAAgB,EAAE,OAAO,IAA8B,CAAC,GAAuB;AACpF,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,MACT;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,iBAAiB,EAAE,OAAO,IAA8B,CAAC,GAA2B;AACzF,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC;AAAA,MACT;AAAA,IACD,CAAC;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,mBAAmB,EAAE,OAAO,IAA8B,CAAC,GAAoB;AACpF,UAAM,aAAa,MAAM,KAAK,cAAc,EAAE,IAAI,KAAK,OAAO,CAAC;AAC/D,UAAM,QAAQ,WAAW,WAAW,MAAM;AAC1C,WAAO,MAAM,MAAM,MAAM,GAAG,CAAC,CAAC;AAAA,EAC/B;AAAA,EAEA,MAAM,0BAA0B,OAAgE;AAC/F,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,IAAI;AAAA,MACnB,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,wBAAwB;AAAA,IAC7B,SAAS;AAAA,IACT,GAAG;AAAA,EACJ,GAEsC;AACrC,UAAM,EAAE,YAAY,aAAa,KAAK,IACrC,MAAM,KAAK,UAAU,QAAQ;AAAA,MAC5B,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,SAAS,MAAM,QAAQ,MAAM,KAAK;AAAA,MACjD,QAAQ,MAAM;AAAA,IACf,CAAC;AAEF,WAAO;AAAA,MACN;AAAA,MACA;AAAA,MACA,MAAM,KAAK,IAAI,CAAC,SAAS,mBAAmB,MAAM,MAAM,CAAC;AAAA,IAC1D;AAAA,EACD;AAAA,EAEA,MAAM,kBAAkB,OAA0D;AACjF,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,OAAO,OAAO;AAAA,MACvB,QAAQ,OAAO;AAAA,IAChB,CAAC;AAAA,EACF;AAAA,EAEA,MAAM,uBAAuB,OAAmE;AAC/F,WAAO,MAAM,KAAK,UAAU,QAAQ;AAAA,MACnC,QAAQ;AAAA,MACR,QAAQ,CAAC,MAAM,OAAO,MAAM,WAAW,MAAM,aAAa,MAAM,MAAM;AAAA,MACtE,QAAQ,MAAM;AAAA,IACf,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,mBAAmB;AAAA,IACxB;AAAA,IACA,UAAU,KAAK;AAAA,IACf,eAAe,IAAI;AAAA,IACnB,GAAG;AAAA,EACJ,GAO2F;AAC1F,UAAM,gBAAgB,YAAY,QAAQ,OAAO;AACjD,UAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG,WAAW;AACjD,oBAAc,iBAAiB,SAAS,MAAM,OAAO,cAAc,MAAM,CAAC;AAAA,IAC3E,CAAC;AAED,mBAAe,MAAM,MAAM;AAAA,IAE3B,CAAC;AAED,WAAO,CAAC,cAAc,SAAS;AAC9B,cAAQ,eAAe;AACvB,UAAI;AACH,eAAO,MAAM,KAAK,oBAAoB,KAAK;AAAA,MAC5C,SAAS,GAAG;AAEX,cAAM,QAAQ,KAAK;AAAA,UAClB,IAAI,QAAQ,CAAC,YAAY,WAAW,SAAS,YAAY,CAAC;AAAA,UAC1D;AAAA,QACD,CAAC;AAAA,MACF;AAAA,IACD;AAEA,kBAAc,eAAe;AAG7B,UAAM,IAAI,MAAM,uDAAuD;AAAA,EACxE;AAAA,EAEA,iCAAgD;AAC/C,WAAO;AAAA,MACN,MAAM;AAAA,MACN,UAAU,MAAM;AACf,eAAO;AAAA,MACR;AAAA,IACD;AAAA,EACD;AACD;", "names": []}