{"version": 3, "sources": ["../../../src/zklogin/poseidon.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport {\n\tposeidon1,\n\tposeidon2,\n\tposeidon3,\n\tposeidon4,\n\tposeidon5,\n\tposeidon6,\n\tposeidon7,\n\tposeidon8,\n\tposeidon9,\n\tposeidon10,\n\tposeidon11,\n\tposeidon12,\n\tposeidon13,\n\tposeidon14,\n\tposeidon15,\n\tposeidon16,\n} from 'poseidon-lite';\n\nconst poseidonNumToHashFN = [\n\tposeidon1,\n\tposeidon2,\n\tposeidon3,\n\tposeidon4,\n\tposeidon5,\n\tposeidon6,\n\tposeidon7,\n\tposeidon8,\n\tposeidon9,\n\tposeidon10,\n\tposeidon11,\n\tposeidon12,\n\tposeidon13,\n\tposeidon14,\n\tposeidon15,\n\tposeidon16,\n];\n\nexport const BN254_FIELD_SIZE =\n\t21888242871839275222246405745257275088548364400416034343698204186575808495617n;\n\nexport function poseidonHash(inputs: (number | bigint | string)[]): bigint {\n\tinputs.forEach((x) => {\n\t\tconst b = BigInt(x);\n\t\tif (b < 0 || b >= BN254_FIELD_SIZE) {\n\t\t\tthrow new Error(`Element ${b} not in the BN254 field`);\n\t\t}\n\t});\n\n\tconst hashFN = poseidonNumToHashFN[inputs.length - 1];\n\n\tif (hashFN) {\n\t\treturn hashFN(inputs);\n\t} else if (inputs.length <= 32) {\n\t\tconst hash1 = poseidonHash(inputs.slice(0, 16));\n\t\tconst hash2 = poseidonHash(inputs.slice(16));\n\t\treturn poseidonHash([hash1, hash2]);\n\t} else {\n\t\tthrow new Error(`Yet to implement: Unable to hash a vector of length ${inputs.length}`);\n\t}\n}\n"], "mappings": "AAGA;AAAA,EACC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,OACM;AAEP,MAAM,sBAAsB;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD;AAEO,MAAM,mBACZ;AAEM,SAAS,aAAa,QAA8C;AAC1E,SAAO,QAAQ,CAAC,MAAM;AACrB,UAAM,IAAI,OAAO,CAAC;AAClB,QAAI,IAAI,KAAK,KAAK,kBAAkB;AACnC,YAAM,IAAI,MAAM,WAAW,CAAC,yBAAyB;AAAA,IACtD;AAAA,EACD,CAAC;AAED,QAAM,SAAS,oBAAoB,OAAO,SAAS,CAAC;AAEpD,MAAI,QAAQ;AACX,WAAO,OAAO,MAAM;AAAA,EACrB,WAAW,OAAO,UAAU,IAAI;AAC/B,UAAM,QAAQ,aAAa,OAAO,MAAM,GAAG,EAAE,CAAC;AAC9C,UAAM,QAAQ,aAAa,OAAO,MAAM,EAAE,CAAC;AAC3C,WAAO,aAAa,CAAC,OAAO,KAAK,CAAC;AAAA,EACnC,OAAO;AACN,UAAM,IAAI,MAAM,uDAAuD,OAAO,MAAM,EAAE;AAAA,EACvF;AACD;", "names": []}