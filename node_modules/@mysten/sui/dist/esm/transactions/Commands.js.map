{"version": 3, "sources": ["../../../src/transactions/Commands.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { toBase64 } from '@mysten/bcs';\nimport type { InferInput } from 'valibot';\nimport { parse } from 'valibot';\n\nimport { normalizeSuiObjectId } from '../utils/sui-types.js';\nimport { Argument } from './data/internal.js';\nimport type { CallArg, Command } from './data/internal.js';\nimport type { AsyncTransactionThunk, Transaction } from './Transaction.js';\n\nexport type TransactionArgument =\n\t| InferInput<typeof Argument>\n\t| ((tx: Transaction) => InferInput<typeof Argument>)\n\t| AsyncTransactionThunk;\nexport type TransactionInput = CallArg;\n\n// Keep in sync with constants in\n// crates/sui-framework/packages/sui-framework/sources/package.move\nexport enum UpgradePolicy {\n\tCOMPATIBLE = 0,\n\tADDITIVE = 128,\n\tDEP_ONLY = 192,\n}\n\ntype TransactionShape<T extends Command['$kind']> = { $kind: T } & {\n\t[K in T]: Extract<Command, { [K in T]: any }>[T];\n};\n\n/**\n * Simple helpers used to construct transactions:\n */\nexport const Commands = {\n\tMoveCall(\n\t\tinput:\n\t\t\t| {\n\t\t\t\t\tpackage: string;\n\t\t\t\t\tmodule: string;\n\t\t\t\t\tfunction: string;\n\t\t\t\t\targuments?: Argument[];\n\t\t\t\t\ttypeArguments?: string[];\n\t\t\t  }\n\t\t\t| {\n\t\t\t\t\ttarget: string;\n\t\t\t\t\targuments?: Argument[];\n\t\t\t\t\ttypeArguments?: string[];\n\t\t\t  },\n\t): TransactionShape<'MoveCall'> {\n\t\tconst [pkg, mod = '', fn = ''] =\n\t\t\t'target' in input ? input.target.split('::') : [input.package, input.module, input.function];\n\n\t\treturn {\n\t\t\t$kind: 'MoveCall',\n\t\t\tMoveCall: {\n\t\t\t\tpackage: pkg,\n\t\t\t\tmodule: mod,\n\t\t\t\tfunction: fn,\n\t\t\t\ttypeArguments: input.typeArguments ?? [],\n\t\t\t\targuments: input.arguments ?? [],\n\t\t\t},\n\t\t};\n\t},\n\n\tTransferObjects(\n\t\tobjects: InferInput<typeof Argument>[],\n\t\taddress: InferInput<typeof Argument>,\n\t): TransactionShape<'TransferObjects'> {\n\t\treturn {\n\t\t\t$kind: 'TransferObjects',\n\t\t\tTransferObjects: {\n\t\t\t\tobjects: objects.map((o) => parse(Argument, o)),\n\t\t\t\taddress: parse(Argument, address),\n\t\t\t},\n\t\t};\n\t},\n\tSplitCoins(\n\t\tcoin: InferInput<typeof Argument>,\n\t\tamounts: InferInput<typeof Argument>[],\n\t): TransactionShape<'SplitCoins'> {\n\t\treturn {\n\t\t\t$kind: 'SplitCoins',\n\t\t\tSplitCoins: {\n\t\t\t\tcoin: parse(Argument, coin),\n\t\t\t\tamounts: amounts.map((o) => parse(Argument, o)),\n\t\t\t},\n\t\t};\n\t},\n\tMergeCoins(\n\t\tdestination: InferInput<typeof Argument>,\n\t\tsources: InferInput<typeof Argument>[],\n\t): TransactionShape<'MergeCoins'> {\n\t\treturn {\n\t\t\t$kind: 'MergeCoins',\n\t\t\tMergeCoins: {\n\t\t\t\tdestination: parse(Argument, destination),\n\t\t\t\tsources: sources.map((o) => parse(Argument, o)),\n\t\t\t},\n\t\t};\n\t},\n\tPublish({\n\t\tmodules,\n\t\tdependencies,\n\t}: {\n\t\tmodules: number[][] | string[];\n\t\tdependencies: string[];\n\t}): TransactionShape<'Publish'> {\n\t\treturn {\n\t\t\t$kind: 'Publish',\n\t\t\tPublish: {\n\t\t\t\tmodules: modules.map((module) =>\n\t\t\t\t\ttypeof module === 'string' ? module : toBase64(new Uint8Array(module)),\n\t\t\t\t),\n\t\t\t\tdependencies: dependencies.map((dep) => normalizeSuiObjectId(dep)),\n\t\t\t},\n\t\t};\n\t},\n\tUpgrade({\n\t\tmodules,\n\t\tdependencies,\n\t\tpackage: packageId,\n\t\tticket,\n\t}: {\n\t\tmodules: number[][] | string[];\n\t\tdependencies: string[];\n\t\tpackage: string;\n\t\tticket: InferInput<typeof Argument>;\n\t}): TransactionShape<'Upgrade'> {\n\t\treturn {\n\t\t\t$kind: 'Upgrade',\n\t\t\tUpgrade: {\n\t\t\t\tmodules: modules.map((module) =>\n\t\t\t\t\ttypeof module === 'string' ? module : toBase64(new Uint8Array(module)),\n\t\t\t\t),\n\t\t\t\tdependencies: dependencies.map((dep) => normalizeSuiObjectId(dep)),\n\t\t\t\tpackage: packageId,\n\t\t\t\tticket: parse(Argument, ticket),\n\t\t\t},\n\t\t};\n\t},\n\tMakeMoveVec({\n\t\ttype,\n\t\telements,\n\t}: {\n\t\ttype?: string;\n\t\telements: InferInput<typeof Argument>[];\n\t}): TransactionShape<'MakeMoveVec'> {\n\t\treturn {\n\t\t\t$kind: 'MakeMoveVec',\n\t\t\tMakeMoveVec: {\n\t\t\t\ttype: type ?? null,\n\t\t\t\telements: elements.map((o) => parse(Argument, o)),\n\t\t\t},\n\t\t};\n\t},\n\tIntent({\n\t\tname,\n\t\tinputs = {},\n\t\tdata = {},\n\t}: {\n\t\tname: string;\n\t\tinputs?: Record<string, InferInput<typeof Argument> | InferInput<typeof Argument>[]>;\n\t\tdata?: Record<string, unknown>;\n\t}): TransactionShape<'$Intent'> {\n\t\treturn {\n\t\t\t$kind: '$Intent',\n\t\t\t$Intent: {\n\t\t\t\tname,\n\t\t\t\tinputs: Object.fromEntries(\n\t\t\t\t\tObject.entries(inputs).map(([key, value]) => [\n\t\t\t\t\t\tkey,\n\t\t\t\t\t\tArray.isArray(value) ? value.map((o) => parse(Argument, o)) : parse(Argument, value),\n\t\t\t\t\t]),\n\t\t\t\t),\n\t\t\t\tdata,\n\t\t\t},\n\t\t};\n\t},\n};\n"], "mappings": "AAGA,SAAS,gBAAgB;AAEzB,SAAS,aAAa;AAEtB,SAAS,4BAA4B;AACrC,SAAS,gBAAgB;AAYlB,IAAK,gBAAL,kBAAKA,mBAAL;AACN,EAAAA,8BAAA,gBAAa,KAAb;AACA,EAAAA,8BAAA,cAAW,OAAX;AACA,EAAAA,8BAAA,cAAW,OAAX;AAHW,SAAAA;AAAA,GAAA;AAaL,MAAM,WAAW;AAAA,EACvB,SACC,OAa+B;AAC/B,UAAM,CAAC,KAAK,MAAM,IAAI,KAAK,EAAE,IAC5B,YAAY,QAAQ,MAAM,OAAO,MAAM,IAAI,IAAI,CAAC,MAAM,SAAS,MAAM,QAAQ,MAAM,QAAQ;AAE5F,WAAO;AAAA,MACN,OAAO;AAAA,MACP,UAAU;AAAA,QACT,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,eAAe,MAAM,iBAAiB,CAAC;AAAA,QACvC,WAAW,MAAM,aAAa,CAAC;AAAA,MAChC;AAAA,IACD;AAAA,EACD;AAAA,EAEA,gBACC,SACA,SACsC;AACtC,WAAO;AAAA,MACN,OAAO;AAAA,MACP,iBAAiB;AAAA,QAChB,SAAS,QAAQ,IAAI,CAAC,MAAM,MAAM,UAAU,CAAC,CAAC;AAAA,QAC9C,SAAS,MAAM,UAAU,OAAO;AAAA,MACjC;AAAA,IACD;AAAA,EACD;AAAA,EACA,WACC,MACA,SACiC;AACjC,WAAO;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,QACX,MAAM,MAAM,UAAU,IAAI;AAAA,QAC1B,SAAS,QAAQ,IAAI,CAAC,MAAM,MAAM,UAAU,CAAC,CAAC;AAAA,MAC/C;AAAA,IACD;AAAA,EACD;AAAA,EACA,WACC,aACA,SACiC;AACjC,WAAO;AAAA,MACN,OAAO;AAAA,MACP,YAAY;AAAA,QACX,aAAa,MAAM,UAAU,WAAW;AAAA,QACxC,SAAS,QAAQ,IAAI,CAAC,MAAM,MAAM,UAAU,CAAC,CAAC;AAAA,MAC/C;AAAA,IACD;AAAA,EACD;AAAA,EACA,QAAQ;AAAA,IACP;AAAA,IACA;AAAA,EACD,GAGgC;AAC/B,WAAO;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,QACR,SAAS,QAAQ;AAAA,UAAI,CAAC,WACrB,OAAO,WAAW,WAAW,SAAS,SAAS,IAAI,WAAW,MAAM,CAAC;AAAA,QACtE;AAAA,QACA,cAAc,aAAa,IAAI,CAAC,QAAQ,qBAAqB,GAAG,CAAC;AAAA,MAClE;AAAA,IACD;AAAA,EACD;AAAA,EACA,QAAQ;AAAA,IACP;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,EACD,GAKgC;AAC/B,WAAO;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,QACR,SAAS,QAAQ;AAAA,UAAI,CAAC,WACrB,OAAO,WAAW,WAAW,SAAS,SAAS,IAAI,WAAW,MAAM,CAAC;AAAA,QACtE;AAAA,QACA,cAAc,aAAa,IAAI,CAAC,QAAQ,qBAAqB,GAAG,CAAC;AAAA,QACjE,SAAS;AAAA,QACT,QAAQ,MAAM,UAAU,MAAM;AAAA,MAC/B;AAAA,IACD;AAAA,EACD;AAAA,EACA,YAAY;AAAA,IACX;AAAA,IACA;AAAA,EACD,GAGoC;AACnC,WAAO;AAAA,MACN,OAAO;AAAA,MACP,aAAa;AAAA,QACZ,MAAM,QAAQ;AAAA,QACd,UAAU,SAAS,IAAI,CAAC,MAAM,MAAM,UAAU,CAAC,CAAC;AAAA,MACjD;AAAA,IACD;AAAA,EACD;AAAA,EACA,OAAO;AAAA,IACN;AAAA,IACA,SAAS,CAAC;AAAA,IACV,OAAO,CAAC;AAAA,EACT,GAIgC;AAC/B,WAAO;AAAA,MACN,OAAO;AAAA,MACP,SAAS;AAAA,QACR;AAAA,QACA,QAAQ,OAAO;AAAA,UACd,OAAO,QAAQ,MAAM,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM;AAAA,YAC5C;AAAA,YACA,MAAM,QAAQ,KAAK,IAAI,MAAM,IAAI,CAAC,MAAM,MAAM,UAAU,CAAC,CAAC,IAAI,MAAM,UAAU,KAAK;AAAA,UACpF,CAAC;AAAA,QACF;AAAA,QACA;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;", "names": ["UpgradePolicy"]}