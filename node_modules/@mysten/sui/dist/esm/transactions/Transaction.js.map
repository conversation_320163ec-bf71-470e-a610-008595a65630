{"version": 3, "sources": ["../../../src/transactions/Transaction.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SerializedBcs } from '@mysten/bcs';\nimport { fromBase64, isSerializedBcs } from '@mysten/bcs';\nimport type { InferInput } from 'valibot';\nimport { is, parse } from 'valibot';\n\nimport type { SuiClient } from '../client/index.js';\nimport type { SignatureWithBytes, Signer } from '../cryptography/index.js';\nimport { normalizeSuiAddress } from '../utils/sui-types.js';\nimport type { TransactionArgument } from './Commands.js';\nimport { Commands } from './Commands.js';\nimport type { CallArg, Command } from './data/internal.js';\nimport { Argument, NormalizedCallArg, ObjectRef, TransactionExpiration } from './data/internal.js';\nimport { serializeV1TransactionData } from './data/v1.js';\nimport { SerializedTransactionDataV2 } from './data/v2.js';\nimport { Inputs } from './Inputs.js';\nimport { needsTransactionResolution, resolveTransactionPlugin } from './resolve.js';\nimport type {\n\tBuildTransactionOptions,\n\tSerializeTransactionOptions,\n\tTransactionPlugin,\n} from './resolve.js';\nimport { createObjectMethods } from './object.js';\nimport { createPure } from './pure.js';\nimport { TransactionDataBuilder } from './TransactionData.js';\nimport { getIdFromCallArg } from './utils.js';\nimport { namedPackagesPlugin } from './plugins/NamedPackagesPlugin.js';\n\nexport type TransactionObjectArgument =\n\t| Exclude<InferInput<typeof Argument>, { Input: unknown; type?: 'pure' }>\n\t| ((tx: Transaction) => Exclude<InferInput<typeof Argument>, { Input: unknown; type?: 'pure' }>)\n\t| AsyncTransactionThunk<TransactionResultArgument>;\n\nexport type TransactionResult = Extract<Argument, { Result: unknown }> &\n\tExtract<Argument, { NestedResult: unknown }>[];\n\nexport type TransactionResultArgument =\n\t| Extract<Argument, { Result: unknown }>\n\t| readonly Extract<Argument, { NestedResult: unknown }>[];\n\nexport type AsyncTransactionThunk<\n\tT extends TransactionResultArgument | void = TransactionResultArgument | void,\n> = (tx: Transaction) => Promise<T | void>;\n\nfunction createTransactionResult(\n\tindex: number | (() => number),\n\tlength = Infinity,\n): TransactionResult {\n\tconst baseResult = {\n\t\t$kind: 'Result' as const,\n\t\tget Result() {\n\t\t\treturn typeof index === 'function' ? index() : index;\n\t\t},\n\t};\n\n\tconst nestedResults: {\n\t\t$kind: 'NestedResult';\n\t\tNestedResult: [number, number];\n\t}[] = [];\n\tconst nestedResultFor = (\n\t\tresultIndex: number,\n\t): {\n\t\t$kind: 'NestedResult';\n\t\tNestedResult: [number, number];\n\t} =>\n\t\t(nestedResults[resultIndex] ??= {\n\t\t\t$kind: 'NestedResult' as const,\n\t\t\tget NestedResult() {\n\t\t\t\treturn [typeof index === 'function' ? index() : index, resultIndex] as [number, number];\n\t\t\t},\n\t\t});\n\n\treturn new Proxy(baseResult, {\n\t\tset() {\n\t\t\tthrow new Error(\n\t\t\t\t'The transaction result is a proxy, and does not support setting properties directly',\n\t\t\t);\n\t\t},\n\t\t// TODO: Instead of making this return a concrete argument, we should ideally\n\t\t// make it reference-based (so that this gets resolved at build-time), which\n\t\t// allows re-ordering transactions.\n\t\tget(target, property) {\n\t\t\t// This allows this transaction argument to be used in the singular form:\n\t\t\tif (property in target) {\n\t\t\t\treturn Reflect.get(target, property);\n\t\t\t}\n\n\t\t\t// Support destructuring:\n\t\t\tif (property === Symbol.iterator) {\n\t\t\t\treturn function* () {\n\t\t\t\t\tlet i = 0;\n\t\t\t\t\twhile (i < length) {\n\t\t\t\t\t\tyield nestedResultFor(i);\n\t\t\t\t\t\ti++;\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (typeof property === 'symbol') return;\n\n\t\t\tconst resultIndex = parseInt(property, 10);\n\t\t\tif (Number.isNaN(resultIndex) || resultIndex < 0) return;\n\t\t\treturn nestedResultFor(resultIndex);\n\t\t},\n\t}) as TransactionResult;\n}\n\nconst TRANSACTION_BRAND = Symbol.for('@mysten/transaction') as never;\n\ninterface SignOptions extends BuildTransactionOptions {\n\tsigner: Signer;\n}\n\nexport function isTransaction(obj: unknown): obj is Transaction {\n\treturn !!obj && typeof obj === 'object' && (obj as any)[TRANSACTION_BRAND] === true;\n}\n\nexport type TransactionObjectInput = string | CallArg | TransactionObjectArgument;\n\ninterface TransactionPluginRegistry {\n\t// eslint-disable-next-line @typescript-eslint/ban-types\n\tbuildPlugins: Map<string | Function, TransactionPlugin>;\n\t// eslint-disable-next-line @typescript-eslint/ban-types\n\tserializationPlugins: Map<string | Function, TransactionPlugin>;\n}\n\nconst modulePluginRegistry: TransactionPluginRegistry = {\n\tbuildPlugins: new Map(),\n\tserializationPlugins: new Map(),\n};\n\nconst TRANSACTION_REGISTRY_KEY = Symbol.for('@mysten/transaction/registry');\nfunction getGlobalPluginRegistry() {\n\ttry {\n\t\tconst target = globalThis as {\n\t\t\t[TRANSACTION_REGISTRY_KEY]?: TransactionPluginRegistry;\n\t\t};\n\n\t\tif (!target[TRANSACTION_REGISTRY_KEY]) {\n\t\t\ttarget[TRANSACTION_REGISTRY_KEY] = modulePluginRegistry;\n\t\t}\n\n\t\treturn target[TRANSACTION_REGISTRY_KEY];\n\t} catch (e) {\n\t\treturn modulePluginRegistry;\n\t}\n}\n\ntype InputSection = (CallArg | InputSection)[];\ntype CommandSection = (Command | CommandSection)[];\n\n/**\n * Transaction Builder\n */\nexport class Transaction {\n\t#serializationPlugins: TransactionPlugin[];\n\t#buildPlugins: TransactionPlugin[];\n\t#intentResolvers = new Map<string, TransactionPlugin>();\n\t#inputSection: InputSection = [];\n\t#commandSection: CommandSection = [];\n\t#availableResults: Set<number> = new Set();\n\t#pendingPromises = new Set<Promise<unknown>>();\n\t#added = new Map<(...args: any[]) => unknown, unknown>();\n\n\t/**\n\t * Converts from a serialize transaction kind (built with `build({ onlyTransactionKind: true })`) to a `Transaction` class.\n\t * Supports either a byte array, or base64-encoded bytes.\n\t */\n\tstatic fromKind(serialized: string | Uint8Array) {\n\t\tconst tx = new Transaction();\n\n\t\ttx.#data = TransactionDataBuilder.fromKindBytes(\n\t\t\ttypeof serialized === 'string' ? fromBase64(serialized) : serialized,\n\t\t);\n\n\t\ttx.#inputSection = tx.#data.inputs.slice();\n\t\ttx.#commandSection = tx.#data.commands.slice();\n\t\ttx.#availableResults = new Set(tx.#commandSection.map((_, i) => i));\n\n\t\treturn tx;\n\t}\n\n\t/**\n\t * Converts from a serialized transaction format to a `Transaction` class.\n\t * There are two supported serialized formats:\n\t * - A string returned from `Transaction#serialize`. The serialized format must be compatible, or it will throw an error.\n\t * - A byte array (or base64-encoded bytes) containing BCS transaction data.\n\t */\n\tstatic from(transaction: string | Uint8Array | Transaction) {\n\t\tconst newTransaction = new Transaction();\n\n\t\tif (isTransaction(transaction)) {\n\t\t\tnewTransaction.#data = new TransactionDataBuilder(transaction.getData());\n\t\t} else if (typeof transaction !== 'string' || !transaction.startsWith('{')) {\n\t\t\tnewTransaction.#data = TransactionDataBuilder.fromBytes(\n\t\t\t\ttypeof transaction === 'string' ? fromBase64(transaction) : transaction,\n\t\t\t);\n\t\t} else {\n\t\t\tnewTransaction.#data = TransactionDataBuilder.restore(JSON.parse(transaction));\n\t\t}\n\n\t\tnewTransaction.#inputSection = newTransaction.#data.inputs.slice();\n\t\tnewTransaction.#commandSection = newTransaction.#data.commands.slice();\n\t\tnewTransaction.#availableResults = new Set(newTransaction.#commandSection.map((_, i) => i));\n\n\t\treturn newTransaction;\n\t}\n\n\t/** @deprecated global plugins should be registered with a name */\n\tstatic registerGlobalSerializationPlugin(step: TransactionPlugin): void;\n\tstatic registerGlobalSerializationPlugin(name: string, step: TransactionPlugin): void;\n\tstatic registerGlobalSerializationPlugin(\n\t\tstepOrStep: TransactionPlugin | string,\n\t\tstep?: TransactionPlugin,\n\t) {\n\t\tgetGlobalPluginRegistry().serializationPlugins.set(\n\t\t\tstepOrStep,\n\t\t\tstep ?? (stepOrStep as TransactionPlugin),\n\t\t);\n\t}\n\n\tstatic unregisterGlobalSerializationPlugin(name: string) {\n\t\tgetGlobalPluginRegistry().serializationPlugins.delete(name);\n\t}\n\n\t/** @deprecated global plugins should be registered with a name */\n\tstatic registerGlobalBuildPlugin(step: TransactionPlugin): void;\n\tstatic registerGlobalBuildPlugin(name: string, step: TransactionPlugin): void;\n\tstatic registerGlobalBuildPlugin(\n\t\tstepOrStep: TransactionPlugin | string,\n\t\tstep?: TransactionPlugin,\n\t) {\n\t\tgetGlobalPluginRegistry().buildPlugins.set(\n\t\t\tstepOrStep,\n\t\t\tstep ?? (stepOrStep as TransactionPlugin),\n\t\t);\n\t}\n\n\tstatic unregisterGlobalBuildPlugin(name: string) {\n\t\tgetGlobalPluginRegistry().buildPlugins.delete(name);\n\t}\n\n\taddSerializationPlugin(step: TransactionPlugin) {\n\t\tthis.#serializationPlugins.push(step);\n\t}\n\n\taddBuildPlugin(step: TransactionPlugin) {\n\t\tthis.#buildPlugins.push(step);\n\t}\n\n\taddIntentResolver(intent: string, resolver: TransactionPlugin) {\n\t\tif (this.#intentResolvers.has(intent) && this.#intentResolvers.get(intent) !== resolver) {\n\t\t\tthrow new Error(`Intent resolver for ${intent} already exists`);\n\t\t}\n\n\t\tthis.#intentResolvers.set(intent, resolver);\n\t}\n\n\tsetSender(sender: string) {\n\t\tthis.#data.sender = sender;\n\t}\n\t/**\n\t * Sets the sender only if it has not already been set.\n\t * This is useful for sponsored transaction flows where the sender may not be the same as the signer address.\n\t */\n\tsetSenderIfNotSet(sender: string) {\n\t\tif (!this.#data.sender) {\n\t\t\tthis.#data.sender = sender;\n\t\t}\n\t}\n\tsetExpiration(expiration?: InferInput<typeof TransactionExpiration> | null) {\n\t\tthis.#data.expiration = expiration ? parse(TransactionExpiration, expiration) : null;\n\t}\n\tsetGasPrice(price: number | bigint) {\n\t\tthis.#data.gasConfig.price = String(price);\n\t}\n\tsetGasBudget(budget: number | bigint) {\n\t\tthis.#data.gasConfig.budget = String(budget);\n\t}\n\n\tsetGasBudgetIfNotSet(budget: number | bigint) {\n\t\tif (this.#data.gasData.budget == null) {\n\t\t\tthis.#data.gasConfig.budget = String(budget);\n\t\t}\n\t}\n\n\tsetGasOwner(owner: string) {\n\t\tthis.#data.gasConfig.owner = owner;\n\t}\n\tsetGasPayment(payments: ObjectRef[]) {\n\t\tthis.#data.gasConfig.payment = payments.map((payment) => parse(ObjectRef, payment));\n\t}\n\n\t#data: TransactionDataBuilder;\n\n\t/** @deprecated Use `getData()` instead. */\n\tget blockData() {\n\t\treturn serializeV1TransactionData(this.#data.snapshot());\n\t}\n\n\t/** Get a snapshot of the transaction data, in JSON form: */\n\tgetData() {\n\t\treturn this.#data.snapshot();\n\t}\n\n\t// Used to brand transaction classes so that they can be identified, even between multiple copies\n\t// of the builder.\n\tget [TRANSACTION_BRAND]() {\n\t\treturn true;\n\t}\n\n\t// Temporary workaround for the wallet interface accidentally serializing transactions via postMessage\n\tget pure(): ReturnType<typeof createPure<Argument>> {\n\t\tObject.defineProperty(this, 'pure', {\n\t\t\tenumerable: false,\n\t\t\tvalue: createPure<Argument>((value): Argument => {\n\t\t\t\tif (isSerializedBcs(value)) {\n\t\t\t\t\treturn this.#addInput('pure', {\n\t\t\t\t\t\t$kind: 'Pure',\n\t\t\t\t\t\tPure: {\n\t\t\t\t\t\t\tbytes: value.toBase64(),\n\t\t\t\t\t\t},\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\t// TODO: we can also do some deduplication here\n\t\t\t\treturn this.#addInput(\n\t\t\t\t\t'pure',\n\t\t\t\t\tis(NormalizedCallArg, value)\n\t\t\t\t\t\t? parse(NormalizedCallArg, value)\n\t\t\t\t\t\t: value instanceof Uint8Array\n\t\t\t\t\t\t\t? Inputs.Pure(value)\n\t\t\t\t\t\t\t: { $kind: 'UnresolvedPure', UnresolvedPure: { value } },\n\t\t\t\t);\n\t\t\t}),\n\t\t});\n\n\t\treturn this.pure;\n\t}\n\n\tconstructor() {\n\t\tconst globalPlugins = getGlobalPluginRegistry();\n\t\tthis.#data = new TransactionDataBuilder();\n\t\tthis.#buildPlugins = [...globalPlugins.buildPlugins.values()];\n\t\tthis.#serializationPlugins = [...globalPlugins.serializationPlugins.values()];\n\t}\n\n\t/** Returns an argument for the gas coin, to be used in a transaction. */\n\tget gas() {\n\t\treturn { $kind: 'GasCoin' as const, GasCoin: true as const };\n\t}\n\n\t/**\n\t * Add a new object input to the transaction.\n\t */\n\tobject: ReturnType<\n\t\ttypeof createObjectMethods<{ $kind: 'Input'; Input: number; type?: 'object' }>\n\t> = createObjectMethods(\n\t\t(value: TransactionObjectInput): { $kind: 'Input'; Input: number; type?: 'object' } => {\n\t\t\tif (typeof value === 'function') {\n\t\t\t\treturn this.object(this.add(value as (tx: Transaction) => TransactionObjectArgument));\n\t\t\t}\n\n\t\t\tif (typeof value === 'object' && is(Argument, value)) {\n\t\t\t\treturn value as { $kind: 'Input'; Input: number; type?: 'object' };\n\t\t\t}\n\n\t\t\tconst id = getIdFromCallArg(value);\n\n\t\t\tconst inserted = this.#data.inputs.find((i) => id === getIdFromCallArg(i));\n\n\t\t\t// Upgrade shared object inputs to mutable if needed:\n\t\t\tif (\n\t\t\t\tinserted?.Object?.SharedObject &&\n\t\t\t\ttypeof value === 'object' &&\n\t\t\t\tvalue.Object?.SharedObject\n\t\t\t) {\n\t\t\t\tinserted.Object.SharedObject.mutable =\n\t\t\t\t\tinserted.Object.SharedObject.mutable || value.Object.SharedObject.mutable;\n\t\t\t}\n\n\t\t\treturn inserted\n\t\t\t\t? { $kind: 'Input', Input: this.#data.inputs.indexOf(inserted), type: 'object' }\n\t\t\t\t: this.#addInput(\n\t\t\t\t\t\t'object',\n\t\t\t\t\t\ttypeof value === 'string'\n\t\t\t\t\t\t\t? {\n\t\t\t\t\t\t\t\t\t$kind: 'UnresolvedObject',\n\t\t\t\t\t\t\t\t\tUnresolvedObject: { objectId: normalizeSuiAddress(value) },\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t: value,\n\t\t\t\t\t);\n\t\t},\n\t);\n\n\t/**\n\t * Add a new object input to the transaction using the fully-resolved object reference.\n\t * If you only have an object ID, use `builder.object(id)` instead.\n\t */\n\tobjectRef(...args: Parameters<(typeof Inputs)['ObjectRef']>) {\n\t\treturn this.object(Inputs.ObjectRef(...args));\n\t}\n\n\t/**\n\t * Add a new receiving input to the transaction using the fully-resolved object reference.\n\t * If you only have an object ID, use `builder.object(id)` instead.\n\t */\n\treceivingRef(...args: Parameters<(typeof Inputs)['ReceivingRef']>) {\n\t\treturn this.object(Inputs.ReceivingRef(...args));\n\t}\n\n\t/**\n\t * Add a new shared object input to the transaction using the fully-resolved shared object reference.\n\t * If you only have an object ID, use `builder.object(id)` instead.\n\t */\n\tsharedObjectRef(...args: Parameters<(typeof Inputs)['SharedObjectRef']>) {\n\t\treturn this.object(Inputs.SharedObjectRef(...args));\n\t}\n\n\t#fork() {\n\t\tconst fork = new Transaction();\n\n\t\tfork.#data = this.#data;\n\t\tfork.#serializationPlugins = this.#serializationPlugins;\n\t\tfork.#buildPlugins = this.#buildPlugins;\n\t\tfork.#intentResolvers = this.#intentResolvers;\n\t\tfork.#pendingPromises = this.#pendingPromises;\n\t\tfork.#availableResults = new Set(this.#availableResults);\n\t\tfork.#added = this.#added;\n\t\tthis.#inputSection.push(fork.#inputSection);\n\t\tthis.#commandSection.push(fork.#commandSection);\n\n\t\treturn fork;\n\t}\n\n\t/** Add a transaction to the transaction */\n\n\tadd<T extends Command>(command: T): TransactionResult;\n\tadd<T extends void | TransactionResultArgument | TransactionArgument | Command>(\n\t\tthunk: (tx: Transaction) => T,\n\t): T;\n\tadd<T extends TransactionResultArgument | void>(\n\t\tasyncTransactionThunk: AsyncTransactionThunk<T>,\n\t): T;\n\tadd(command: Command | AsyncTransactionThunk | ((tx: Transaction) => unknown)): unknown {\n\t\tif (typeof command === 'function') {\n\t\t\tif (this.#added.has(command)) {\n\t\t\t\treturn this.#added.get(command);\n\t\t\t}\n\n\t\t\tconst fork = this.#fork();\n\t\t\tconst result = command(fork);\n\n\t\t\tif (!(result && typeof result === 'object' && 'then' in result)) {\n\t\t\t\tthis.#availableResults = fork.#availableResults;\n\t\t\t\tthis.#added.set(command, result);\n\t\t\t\treturn result;\n\t\t\t}\n\n\t\t\tconst placeholder = this.#addCommand({\n\t\t\t\t$kind: '$Intent',\n\t\t\t\t$Intent: {\n\t\t\t\t\tname: 'AsyncTransactionThunk',\n\t\t\t\t\tinputs: {},\n\t\t\t\t\tdata: {\n\t\t\t\t\t\tresultIndex: this.#data.commands.length,\n\t\t\t\t\t\tresult: null as TransactionResult | null,\n\t\t\t\t\t},\n\t\t\t\t},\n\t\t\t});\n\n\t\t\tthis.#pendingPromises.add(\n\t\t\t\tPromise.resolve(result as Promise<TransactionResult>).then((result) => {\n\t\t\t\t\tplaceholder.$Intent.data.result = result;\n\t\t\t\t}),\n\t\t\t);\n\t\t\tconst txResult = createTransactionResult(() => placeholder.$Intent.data.resultIndex);\n\t\t\tthis.#added.set(command, txResult);\n\t\t\treturn txResult;\n\t\t} else {\n\t\t\tthis.#addCommand(command);\n\t\t}\n\n\t\treturn createTransactionResult(this.#data.commands.length - 1);\n\t}\n\n\t#addCommand<T extends Command>(command: T) {\n\t\tconst resultIndex = this.#data.commands.length;\n\t\tthis.#commandSection.push(command);\n\t\tthis.#availableResults.add(resultIndex);\n\t\tthis.#data.commands.push(command);\n\n\t\tthis.#data.mapCommandArguments(resultIndex, (arg) => {\n\t\t\tif (arg.$kind === 'Result' && !this.#availableResults.has(arg.Result)) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t`Result { Result: ${arg.Result} } is not available to use the current transaction`,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tif (arg.$kind === 'NestedResult' && !this.#availableResults.has(arg.NestedResult[0])) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t`Result { NestedResult: [${arg.NestedResult[0]}, ${arg.NestedResult[1]}] } is not available to use the current transaction`,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tif (arg.$kind === 'Input' && arg.Input >= this.#data.inputs.length) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t`Input { Input: ${arg.Input} } references an input that does not exist in the current transaction`,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\treturn arg;\n\t\t});\n\n\t\treturn command;\n\t}\n\n\t#addInput<T extends 'pure' | 'object'>(type: T, input: CallArg) {\n\t\tthis.#inputSection.push(input);\n\t\treturn this.#data.addInput(type, input);\n\t}\n\n\t#normalizeTransactionArgument(arg: TransactionArgument | SerializedBcs<any>) {\n\t\tif (isSerializedBcs(arg)) {\n\t\t\treturn this.pure(arg);\n\t\t}\n\n\t\treturn this.#resolveArgument(arg as TransactionArgument);\n\t}\n\n\t#resolveArgument(arg: TransactionArgument): Argument {\n\t\tif (typeof arg === 'function') {\n\t\t\tconst resolved = this.add(arg as never);\n\n\t\t\tif (typeof resolved === 'function') {\n\t\t\t\treturn this.#resolveArgument(resolved);\n\t\t\t}\n\n\t\t\treturn parse(Argument, resolved);\n\t\t}\n\n\t\treturn parse(Argument, arg);\n\t}\n\n\t// Method shorthands:\n\n\tsplitCoins<\n\t\tconst Amounts extends (TransactionArgument | SerializedBcs<any> | number | string | bigint)[],\n\t>(coin: TransactionObjectArgument | string, amounts: Amounts) {\n\t\tconst command = Commands.SplitCoins(\n\t\t\ttypeof coin === 'string' ? this.object(coin) : this.#resolveArgument(coin),\n\t\t\tamounts.map((amount) =>\n\t\t\t\ttypeof amount === 'number' || typeof amount === 'bigint' || typeof amount === 'string'\n\t\t\t\t\t? this.pure.u64(amount)\n\t\t\t\t\t: this.#normalizeTransactionArgument(amount),\n\t\t\t),\n\t\t);\n\t\tthis.#addCommand(command);\n\t\treturn createTransactionResult(this.#data.commands.length - 1, amounts.length) as Extract<\n\t\t\tArgument,\n\t\t\t{ Result: unknown }\n\t\t> & {\n\t\t\t[K in keyof Amounts]: Extract<Argument, { NestedResult: unknown }>;\n\t\t};\n\t}\n\tmergeCoins(\n\t\tdestination: TransactionObjectArgument | string,\n\t\tsources: (TransactionObjectArgument | string)[],\n\t) {\n\t\treturn this.add(\n\t\t\tCommands.MergeCoins(\n\t\t\t\tthis.object(destination),\n\t\t\t\tsources.map((src) => this.object(src)),\n\t\t\t),\n\t\t);\n\t}\n\tpublish({ modules, dependencies }: { modules: number[][] | string[]; dependencies: string[] }) {\n\t\treturn this.add(\n\t\t\tCommands.Publish({\n\t\t\t\tmodules,\n\t\t\t\tdependencies,\n\t\t\t}),\n\t\t);\n\t}\n\tupgrade({\n\t\tmodules,\n\t\tdependencies,\n\t\tpackage: packageId,\n\t\tticket,\n\t}: {\n\t\tmodules: number[][] | string[];\n\t\tdependencies: string[];\n\t\tpackage: string;\n\t\tticket: TransactionObjectArgument | string;\n\t}) {\n\t\treturn this.add(\n\t\t\tCommands.Upgrade({\n\t\t\t\tmodules,\n\t\t\t\tdependencies,\n\t\t\t\tpackage: packageId,\n\t\t\t\tticket: this.object(ticket),\n\t\t\t}),\n\t\t);\n\t}\n\tmoveCall({\n\t\targuments: args,\n\t\t...input\n\t}:\n\t\t| {\n\t\t\t\tpackage: string;\n\t\t\t\tmodule: string;\n\t\t\t\tfunction: string;\n\t\t\t\targuments?: (TransactionArgument | SerializedBcs<any>)[];\n\t\t\t\ttypeArguments?: string[];\n\t\t  }\n\t\t| {\n\t\t\t\ttarget: string;\n\t\t\t\targuments?: (TransactionArgument | SerializedBcs<any>)[];\n\t\t\t\ttypeArguments?: string[];\n\t\t  }) {\n\t\treturn this.add(\n\t\t\tCommands.MoveCall({\n\t\t\t\t...input,\n\t\t\t\targuments: args?.map((arg) => this.#normalizeTransactionArgument(arg)),\n\t\t\t} as Parameters<typeof Commands.MoveCall>[0]),\n\t\t);\n\t}\n\ttransferObjects(\n\t\tobjects: (TransactionObjectArgument | string)[],\n\t\taddress: TransactionArgument | SerializedBcs<any> | string,\n\t) {\n\t\treturn this.add(\n\t\t\tCommands.TransferObjects(\n\t\t\t\tobjects.map((obj) => this.object(obj)),\n\t\t\t\ttypeof address === 'string'\n\t\t\t\t\t? this.pure.address(address)\n\t\t\t\t\t: this.#normalizeTransactionArgument(address),\n\t\t\t),\n\t\t);\n\t}\n\tmakeMoveVec({\n\t\ttype,\n\t\telements,\n\t}: {\n\t\telements: (TransactionObjectArgument | string)[];\n\t\ttype?: string;\n\t}) {\n\t\treturn this.add(\n\t\t\tCommands.MakeMoveVec({\n\t\t\t\ttype,\n\t\t\t\telements: elements.map((obj) => this.object(obj)),\n\t\t\t}),\n\t\t);\n\t}\n\n\t/**\n\t * @deprecated Use toJSON instead.\n\t * For synchronous serialization, you can use `getData()`\n\t * */\n\tserialize() {\n\t\treturn JSON.stringify(serializeV1TransactionData(this.#data.snapshot()));\n\t}\n\n\tasync toJSON(options: SerializeTransactionOptions = {}): Promise<string> {\n\t\tawait this.prepareForSerialization(options);\n\t\tconst fullyResolved = this.isFullyResolved();\n\t\treturn JSON.stringify(\n\t\t\tparse(\n\t\t\t\tSerializedTransactionDataV2,\n\t\t\t\tfullyResolved\n\t\t\t\t\t? {\n\t\t\t\t\t\t\t...this.#data.snapshot(),\n\t\t\t\t\t\t\tdigest: this.#data.getDigest(),\n\t\t\t\t\t\t}\n\t\t\t\t\t: this.#data.snapshot(),\n\t\t\t),\n\t\t\t(_key, value) => (typeof value === 'bigint' ? value.toString() : value),\n\t\t\t2,\n\t\t);\n\t}\n\n\t/** Build the transaction to BCS bytes, and sign it with the provided keypair. */\n\tasync sign(options: SignOptions): Promise<SignatureWithBytes> {\n\t\tconst { signer, ...buildOptions } = options;\n\t\tconst bytes = await this.build(buildOptions);\n\t\treturn signer.signTransaction(bytes);\n\t}\n\n\t/**\n\t *  Ensures that:\n\t *  - All objects have been fully resolved to a specific version\n\t *  - All pure inputs have been serialized to bytes\n\t *  - All async thunks have been fully resolved\n\t *  - All transaction intents have been resolved\n\t * \t- The gas payment, budget, and price have been set\n\t *  - The transaction sender has been set\n\t *\n\t *  When true, the transaction will always be built to the same bytes and digest (unless the transaction is mutated)\n\t */\n\tisFullyResolved() {\n\t\tif (!this.#data.sender) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (this.#pendingPromises.size > 0) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (this.#data.commands.some((cmd) => cmd.$Intent)) {\n\t\t\treturn false;\n\t\t}\n\n\t\tif (needsTransactionResolution(this.#data, {})) {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t}\n\n\t/** Build the transaction to BCS bytes. */\n\tasync build(options: BuildTransactionOptions = {}): Promise<Uint8Array> {\n\t\tawait this.prepareForSerialization(options);\n\t\tawait this.#prepareBuild(options);\n\t\treturn this.#data.build({\n\t\t\tonlyTransactionKind: options.onlyTransactionKind,\n\t\t});\n\t}\n\n\t/** Derive transaction digest */\n\tasync getDigest(\n\t\toptions: {\n\t\t\tclient?: SuiClient;\n\t\t} = {},\n\t): Promise<string> {\n\t\tawait this.prepareForSerialization(options);\n\t\tawait this.#prepareBuild(options);\n\t\treturn this.#data.getDigest();\n\t}\n\n\t/**\n\t * Prepare the transaction by validating the transaction data and resolving all inputs\n\t * so that it can be built into bytes.\n\t */\n\tasync #prepareBuild(options: BuildTransactionOptions) {\n\t\tif (!options.onlyTransactionKind && !this.#data.sender) {\n\t\t\tthrow new Error('Missing transaction sender');\n\t\t}\n\n\t\tawait this.#runPlugins([...this.#buildPlugins, resolveTransactionPlugin], options);\n\t}\n\n\tasync #runPlugins(plugins: TransactionPlugin[], options: SerializeTransactionOptions) {\n\t\tconst createNext = (i: number) => {\n\t\t\tif (i >= plugins.length) {\n\t\t\t\treturn () => {};\n\t\t\t}\n\t\t\tconst plugin = plugins[i];\n\n\t\t\treturn async () => {\n\t\t\t\tconst next = createNext(i + 1);\n\t\t\t\tlet calledNext = false;\n\t\t\t\tlet nextResolved = false;\n\n\t\t\t\tawait plugin(this.#data, options, async () => {\n\t\t\t\t\tif (calledNext) {\n\t\t\t\t\t\tthrow new Error(`next() was call multiple times in TransactionPlugin ${i}`);\n\t\t\t\t\t}\n\n\t\t\t\t\tcalledNext = true;\n\n\t\t\t\t\tawait next();\n\n\t\t\t\t\tnextResolved = true;\n\t\t\t\t});\n\n\t\t\t\tif (!calledNext) {\n\t\t\t\t\tthrow new Error(`next() was not called in TransactionPlugin ${i}`);\n\t\t\t\t}\n\n\t\t\t\tif (!nextResolved) {\n\t\t\t\t\tthrow new Error(`next() was not awaited in TransactionPlugin ${i}`);\n\t\t\t\t}\n\t\t\t};\n\t\t};\n\n\t\tawait createNext(0)();\n\n\t\tthis.#inputSection = this.#data.inputs.slice();\n\t\tthis.#commandSection = this.#data.commands.slice();\n\t}\n\n\tasync #waitForPendingTasks() {\n\t\twhile (this.#pendingPromises.size > 0) {\n\t\t\tconst newPromise = Promise.all(this.#pendingPromises);\n\t\t\tthis.#pendingPromises.clear();\n\t\t\tthis.#pendingPromises.add(newPromise);\n\t\t\tawait newPromise;\n\t\t\tthis.#pendingPromises.delete(newPromise);\n\t\t}\n\t}\n\n\t#sortCommandsAndInputs() {\n\t\tconst unorderedCommands = this.#data.commands;\n\t\tconst unorderedInputs = this.#data.inputs;\n\n\t\tconst orderedCommands = (this.#commandSection as Command[]).flat(Infinity);\n\t\tconst orderedInputs = (this.#inputSection as CallArg[]).flat(Infinity);\n\n\t\tif (orderedCommands.length !== unorderedCommands.length) {\n\t\t\tthrow new Error('Unexpected number of commands found in transaction data');\n\t\t}\n\n\t\tif (orderedInputs.length !== unorderedInputs.length) {\n\t\t\tthrow new Error('Unexpected number of inputs found in transaction data');\n\t\t}\n\n\t\tconst filteredCommands = orderedCommands.filter(\n\t\t\t(cmd) => cmd.$Intent?.name !== 'AsyncTransactionThunk',\n\t\t);\n\n\t\tthis.#data.commands = filteredCommands;\n\t\tthis.#data.inputs = orderedInputs;\n\t\tthis.#commandSection = filteredCommands;\n\t\tthis.#inputSection = orderedInputs;\n\t\tthis.#availableResults = new Set(filteredCommands.map((_, i) => i));\n\n\t\tfunction getOriginalIndex(index: number): number {\n\t\t\tconst command = unorderedCommands[index];\n\t\t\tif (command.$Intent?.name === 'AsyncTransactionThunk') {\n\t\t\t\tconst result = command.$Intent.data.result as TransactionResult | null;\n\n\t\t\t\tif (result == null) {\n\t\t\t\t\tthrow new Error('AsyncTransactionThunk has not been resolved');\n\t\t\t\t}\n\n\t\t\t\treturn getOriginalIndex(result.Result);\n\t\t\t}\n\n\t\t\tconst updated = filteredCommands.indexOf(command);\n\n\t\t\tif (updated === -1) {\n\t\t\t\tthrow new Error('Unable to find original index for command');\n\t\t\t}\n\n\t\t\treturn updated;\n\t\t}\n\n\t\tthis.#data.mapArguments((arg) => {\n\t\t\tif (arg.$kind === 'Input') {\n\t\t\t\tconst updated = orderedInputs.indexOf(unorderedInputs[arg.Input]);\n\n\t\t\t\tif (updated === -1) {\n\t\t\t\t\tthrow new Error('Input has not been resolved');\n\t\t\t\t}\n\n\t\t\t\treturn { ...arg, Input: updated };\n\t\t\t} else if (arg.$kind === 'Result') {\n\t\t\t\tconst updated = getOriginalIndex(arg.Result);\n\n\t\t\t\treturn { ...arg, Result: updated };\n\t\t\t} else if (arg.$kind === 'NestedResult') {\n\t\t\t\tconst updated = getOriginalIndex(arg.NestedResult[0]);\n\n\t\t\t\treturn { ...arg, NestedResult: [updated, arg.NestedResult[1]] };\n\t\t\t}\n\n\t\t\treturn arg;\n\t\t});\n\n\t\tfor (const [i, cmd] of unorderedCommands.entries()) {\n\t\t\tif (cmd.$Intent?.name === 'AsyncTransactionThunk') {\n\t\t\t\ttry {\n\t\t\t\t\tcmd.$Intent.data.resultIndex = getOriginalIndex(i);\n\t\t\t\t} catch (e) {\n\t\t\t\t\t// If async thunk did not return a result, this will error, but is safe to ignore\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tasync prepareForSerialization(options: SerializeTransactionOptions) {\n\t\tawait this.#waitForPendingTasks();\n\t\tthis.#sortCommandsAndInputs();\n\t\tconst intents = new Set<string>();\n\t\tfor (const command of this.#data.commands) {\n\t\t\tif (command.$Intent) {\n\t\t\t\tintents.add(command.$Intent.name);\n\t\t\t}\n\t\t}\n\n\t\tconst steps = [...this.#serializationPlugins];\n\n\t\tfor (const intent of intents) {\n\t\t\tif (options.supportedIntents?.includes(intent)) {\n\t\t\t\tcontinue;\n\t\t\t}\n\n\t\t\tif (!this.#intentResolvers.has(intent)) {\n\t\t\t\tthrow new Error(`Missing intent resolver for ${intent}`);\n\t\t\t}\n\n\t\t\tsteps.push(this.#intentResolvers.get(intent)!);\n\t\t}\n\n\t\tsteps.push(namedPackagesPlugin());\n\n\t\tawait this.#runPlugins(steps, options);\n\t}\n}\n"], "mappings": ";;;;;;;;AAAA;AAIA,SAAS,YAAY,uBAAuB;AAE5C,SAAS,IAAI,aAAa;AAI1B,SAAS,2BAA2B;AAEpC,SAAS,gBAAgB;AAEzB,SAAS,UAAU,mBAAmB,WAAW,6BAA6B;AAC9E,SAAS,kCAAkC;AAC3C,SAAS,mCAAmC;AAC5C,SAAS,cAAc;AACvB,SAAS,4BAA4B,gCAAgC;AAMrE,SAAS,2BAA2B;AACpC,SAAS,kBAAkB;AAC3B,SAAS,8BAA8B;AACvC,SAAS,wBAAwB;AACjC,SAAS,2BAA2B;AAkBpC,SAAS,wBACR,OACA,SAAS,UACW;AACpB,QAAM,aAAa;AAAA,IAClB,OAAO;AAAA,IACP,IAAI,SAAS;AACZ,aAAO,OAAO,UAAU,aAAa,MAAM,IAAI;AAAA,IAChD;AAAA,EACD;AAEA,QAAM,gBAGA,CAAC;AACP,QAAM,kBAAkB,CACvB,gBAKC,4DAA+B;AAAA,IAC/B,OAAO;AAAA,IACP,IAAI,eAAe;AAClB,aAAO,CAAC,OAAO,UAAU,aAAa,MAAM,IAAI,OAAO,WAAW;AAAA,IACnE;AAAA,EACD;AAED,SAAO,IAAI,MAAM,YAAY;AAAA,IAC5B,MAAM;AACL,YAAM,IAAI;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAIA,IAAI,QAAQ,UAAU;AAErB,UAAI,YAAY,QAAQ;AACvB,eAAO,QAAQ,IAAI,QAAQ,QAAQ;AAAA,MACpC;AAGA,UAAI,aAAa,OAAO,UAAU;AACjC,eAAO,aAAa;AACnB,cAAI,IAAI;AACR,iBAAO,IAAI,QAAQ;AAClB,kBAAM,gBAAgB,CAAC;AACvB;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,UAAI,OAAO,aAAa,SAAU;AAElC,YAAM,cAAc,SAAS,UAAU,EAAE;AACzC,UAAI,OAAO,MAAM,WAAW,KAAK,cAAc,EAAG;AAClD,aAAO,gBAAgB,WAAW;AAAA,IACnC;AAAA,EACD,CAAC;AACF;AAEA,MAAM,oBAAoB,OAAO,IAAI,qBAAqB;AAMnD,SAAS,cAAc,KAAkC;AAC/D,SAAO,CAAC,CAAC,OAAO,OAAO,QAAQ,YAAa,IAAY,iBAAiB,MAAM;AAChF;AAWA,MAAM,uBAAkD;AAAA,EACvD,cAAc,oBAAI,IAAI;AAAA,EACtB,sBAAsB,oBAAI,IAAI;AAC/B;AAEA,MAAM,2BAA2B,OAAO,IAAI,8BAA8B;AAC1E,SAAS,0BAA0B;AAClC,MAAI;AACH,UAAM,SAAS;AAIf,QAAI,CAAC,OAAO,wBAAwB,GAAG;AACtC,aAAO,wBAAwB,IAAI;AAAA,IACpC;AAEA,WAAO,OAAO,wBAAwB;AAAA,EACvC,SAAS,GAAG;AACX,WAAO;AAAA,EACR;AACD;AAQO,MAAM,eAAN,MAAM,aAAY;AAAA,EA0LxB,cAAc;AA1LR;AACN;AACA;AACA,yCAAmB,oBAAI,IAA+B;AACtD,sCAA8B,CAAC;AAC/B,wCAAkC,CAAC;AACnC,0CAAiC,oBAAI,IAAI;AACzC,yCAAmB,oBAAI,IAAsB;AAC7C,+BAAS,oBAAI,IAA0C;AAmIvD;AA8DA;AAAA;AAAA;AAAA,kBAEI;AAAA,MACH,CAAC,UAAsF;AACtF,YAAI,OAAO,UAAU,YAAY;AAChC,iBAAO,KAAK,OAAO,KAAK,IAAI,KAAuD,CAAC;AAAA,QACrF;AAEA,YAAI,OAAO,UAAU,YAAY,GAAG,UAAU,KAAK,GAAG;AACrD,iBAAO;AAAA,QACR;AAEA,cAAM,KAAK,iBAAiB,KAAK;AAEjC,cAAM,WAAW,mBAAK,OAAM,OAAO,KAAK,CAAC,MAAM,OAAO,iBAAiB,CAAC,CAAC;AAGzE,YACC,UAAU,QAAQ,gBAClB,OAAO,UAAU,YACjB,MAAM,QAAQ,cACb;AACD,mBAAS,OAAO,aAAa,UAC5B,SAAS,OAAO,aAAa,WAAW,MAAM,OAAO,aAAa;AAAA,QACpE;AAEA,eAAO,WACJ,EAAE,OAAO,SAAS,OAAO,mBAAK,OAAM,OAAO,QAAQ,QAAQ,GAAG,MAAM,SAAS,IAC7E,sBAAK,qCAAL,WACA,UACA,OAAO,UAAU,WACd;AAAA,UACA,OAAO;AAAA,UACP,kBAAkB,EAAE,UAAU,oBAAoB,KAAK,EAAE;AAAA,QAC1D,IACC;AAAA,MAEP;AAAA,IACD;AApDC,UAAM,gBAAgB,wBAAwB;AAC9C,uBAAK,OAAQ,IAAI,uBAAuB;AACxC,uBAAK,eAAgB,CAAC,GAAG,cAAc,aAAa,OAAO,CAAC;AAC5D,uBAAK,uBAAwB,CAAC,GAAG,cAAc,qBAAqB,OAAO,CAAC;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA;AAAA,EAjLA,OAAO,SAAS,YAAiC;AAChD,UAAM,KAAK,IAAI,aAAY;AAE3B,qBAAG,OAAQ,uBAAuB;AAAA,MACjC,OAAO,eAAe,WAAW,WAAW,UAAU,IAAI;AAAA,IAC3D;AAEA,qBAAG,eAAgB,iBAAG,OAAM,OAAO,MAAM;AACzC,qBAAG,iBAAkB,iBAAG,OAAM,SAAS,MAAM;AAC7C,qBAAG,mBAAoB,IAAI,IAAI,iBAAG,iBAAgB,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AAElE,WAAO;AAAA,EACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,KAAK,aAAgD;AAC3D,UAAM,iBAAiB,IAAI,aAAY;AAEvC,QAAI,cAAc,WAAW,GAAG;AAC/B,mCAAe,OAAQ,IAAI,uBAAuB,YAAY,QAAQ,CAAC;AAAA,IACxE,WAAW,OAAO,gBAAgB,YAAY,CAAC,YAAY,WAAW,GAAG,GAAG;AAC3E,mCAAe,OAAQ,uBAAuB;AAAA,QAC7C,OAAO,gBAAgB,WAAW,WAAW,WAAW,IAAI;AAAA,MAC7D;AAAA,IACD,OAAO;AACN,mCAAe,OAAQ,uBAAuB,QAAQ,KAAK,MAAM,WAAW,CAAC;AAAA,IAC9E;AAEA,iCAAe,eAAgB,6BAAe,OAAM,OAAO,MAAM;AACjE,iCAAe,iBAAkB,6BAAe,OAAM,SAAS,MAAM;AACrE,iCAAe,mBAAoB,IAAI,IAAI,6BAAe,iBAAgB,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AAE1F,WAAO;AAAA,EACR;AAAA,EAKA,OAAO,kCACN,YACA,MACC;AACD,4BAAwB,EAAE,qBAAqB;AAAA,MAC9C;AAAA,MACA,QAAS;AAAA,IACV;AAAA,EACD;AAAA,EAEA,OAAO,oCAAoC,MAAc;AACxD,4BAAwB,EAAE,qBAAqB,OAAO,IAAI;AAAA,EAC3D;AAAA,EAKA,OAAO,0BACN,YACA,MACC;AACD,4BAAwB,EAAE,aAAa;AAAA,MACtC;AAAA,MACA,QAAS;AAAA,IACV;AAAA,EACD;AAAA,EAEA,OAAO,4BAA4B,MAAc;AAChD,4BAAwB,EAAE,aAAa,OAAO,IAAI;AAAA,EACnD;AAAA,EAEA,uBAAuB,MAAyB;AAC/C,uBAAK,uBAAsB,KAAK,IAAI;AAAA,EACrC;AAAA,EAEA,eAAe,MAAyB;AACvC,uBAAK,eAAc,KAAK,IAAI;AAAA,EAC7B;AAAA,EAEA,kBAAkB,QAAgB,UAA6B;AAC9D,QAAI,mBAAK,kBAAiB,IAAI,MAAM,KAAK,mBAAK,kBAAiB,IAAI,MAAM,MAAM,UAAU;AACxF,YAAM,IAAI,MAAM,uBAAuB,MAAM,iBAAiB;AAAA,IAC/D;AAEA,uBAAK,kBAAiB,IAAI,QAAQ,QAAQ;AAAA,EAC3C;AAAA,EAEA,UAAU,QAAgB;AACzB,uBAAK,OAAM,SAAS;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,QAAgB;AACjC,QAAI,CAAC,mBAAK,OAAM,QAAQ;AACvB,yBAAK,OAAM,SAAS;AAAA,IACrB;AAAA,EACD;AAAA,EACA,cAAc,YAA8D;AAC3E,uBAAK,OAAM,aAAa,aAAa,MAAM,uBAAuB,UAAU,IAAI;AAAA,EACjF;AAAA,EACA,YAAY,OAAwB;AACnC,uBAAK,OAAM,UAAU,QAAQ,OAAO,KAAK;AAAA,EAC1C;AAAA,EACA,aAAa,QAAyB;AACrC,uBAAK,OAAM,UAAU,SAAS,OAAO,MAAM;AAAA,EAC5C;AAAA,EAEA,qBAAqB,QAAyB;AAC7C,QAAI,mBAAK,OAAM,QAAQ,UAAU,MAAM;AACtC,yBAAK,OAAM,UAAU,SAAS,OAAO,MAAM;AAAA,IAC5C;AAAA,EACD;AAAA,EAEA,YAAY,OAAe;AAC1B,uBAAK,OAAM,UAAU,QAAQ;AAAA,EAC9B;AAAA,EACA,cAAc,UAAuB;AACpC,uBAAK,OAAM,UAAU,UAAU,SAAS,IAAI,CAAC,YAAY,MAAM,WAAW,OAAO,CAAC;AAAA,EACnF;AAAA;AAAA,EAKA,IAAI,YAAY;AACf,WAAO,2BAA2B,mBAAK,OAAM,SAAS,CAAC;AAAA,EACxD;AAAA;AAAA,EAGA,UAAU;AACT,WAAO,mBAAK,OAAM,SAAS;AAAA,EAC5B;AAAA;AAAA;AAAA,EAIA,KAAK,iBAAiB,IAAI;AACzB,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,IAAI,OAAgD;AACnD,WAAO,eAAe,MAAM,QAAQ;AAAA,MACnC,YAAY;AAAA,MACZ,OAAO,WAAqB,CAAC,UAAoB;AAChD,YAAI,gBAAgB,KAAK,GAAG;AAC3B,iBAAO,sBAAK,qCAAL,WAAe,QAAQ;AAAA,YAC7B,OAAO;AAAA,YACP,MAAM;AAAA,cACL,OAAO,MAAM,SAAS;AAAA,YACvB;AAAA,UACD;AAAA,QACD;AAGA,eAAO,sBAAK,qCAAL,WACN,QACA,GAAG,mBAAmB,KAAK,IACxB,MAAM,mBAAmB,KAAK,IAC9B,iBAAiB,aAChB,OAAO,KAAK,KAAK,IACjB,EAAE,OAAO,kBAAkB,gBAAgB,EAAE,MAAM,EAAE;AAAA,MAE3D,CAAC;AAAA,IACF,CAAC;AAED,WAAO,KAAK;AAAA,EACb;AAAA;AAAA,EAUA,IAAI,MAAM;AACT,WAAO,EAAE,OAAO,WAAoB,SAAS,KAAc;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA,EAiDA,aAAa,MAAgD;AAC5D,WAAO,KAAK,OAAO,OAAO,UAAU,GAAG,IAAI,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgB,MAAmD;AAClE,WAAO,KAAK,OAAO,OAAO,aAAa,GAAG,IAAI,CAAC;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB,MAAsD;AACxE,WAAO,KAAK,OAAO,OAAO,gBAAgB,GAAG,IAAI,CAAC;AAAA,EACnD;AAAA,EA2BA,IAAI,SAAoF;AACvF,QAAI,OAAO,YAAY,YAAY;AAClC,UAAI,mBAAK,QAAO,IAAI,OAAO,GAAG;AAC7B,eAAO,mBAAK,QAAO,IAAI,OAAO;AAAA,MAC/B;AAEA,YAAM,OAAO,sBAAK,iCAAL;AACb,YAAM,SAAS,QAAQ,IAAI;AAE3B,UAAI,EAAE,UAAU,OAAO,WAAW,YAAY,UAAU,SAAS;AAChE,2BAAK,mBAAoB,mBAAK;AAC9B,2BAAK,QAAO,IAAI,SAAS,MAAM;AAC/B,eAAO;AAAA,MACR;AAEA,YAAM,cAAc,sBAAK,uCAAL,WAAiB;AAAA,QACpC,OAAO;AAAA,QACP,SAAS;AAAA,UACR,MAAM;AAAA,UACN,QAAQ,CAAC;AAAA,UACT,MAAM;AAAA,YACL,aAAa,mBAAK,OAAM,SAAS;AAAA,YACjC,QAAQ;AAAA,UACT;AAAA,QACD;AAAA,MACD;AAEA,yBAAK,kBAAiB;AAAA,QACrB,QAAQ,QAAQ,MAAoC,EAAE,KAAK,CAACA,YAAW;AACtE,sBAAY,QAAQ,KAAK,SAASA;AAAA,QACnC,CAAC;AAAA,MACF;AACA,YAAM,WAAW,wBAAwB,MAAM,YAAY,QAAQ,KAAK,WAAW;AACnF,yBAAK,QAAO,IAAI,SAAS,QAAQ;AACjC,aAAO;AAAA,IACR,OAAO;AACN,4BAAK,uCAAL,WAAiB;AAAA,IAClB;AAEA,WAAO,wBAAwB,mBAAK,OAAM,SAAS,SAAS,CAAC;AAAA,EAC9D;AAAA;AAAA,EA8DA,WAEE,MAA0C,SAAkB;AAC7D,UAAM,UAAU,SAAS;AAAA,MACxB,OAAO,SAAS,WAAW,KAAK,OAAO,IAAI,IAAI,sBAAK,4CAAL,WAAsB;AAAA,MACrE,QAAQ;AAAA,QAAI,CAAC,WACZ,OAAO,WAAW,YAAY,OAAO,WAAW,YAAY,OAAO,WAAW,WAC3E,KAAK,KAAK,IAAI,MAAM,IACpB,sBAAK,yDAAL,WAAmC;AAAA,MACvC;AAAA,IACD;AACA,0BAAK,uCAAL,WAAiB;AACjB,WAAO,wBAAwB,mBAAK,OAAM,SAAS,SAAS,GAAG,QAAQ,MAAM;AAAA,EAM9E;AAAA,EACA,WACC,aACA,SACC;AACD,WAAO,KAAK;AAAA,MACX,SAAS;AAAA,QACR,KAAK,OAAO,WAAW;AAAA,QACvB,QAAQ,IAAI,CAAC,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MACtC;AAAA,IACD;AAAA,EACD;AAAA,EACA,QAAQ,EAAE,SAAS,aAAa,GAA+D;AAC9F,WAAO,KAAK;AAAA,MACX,SAAS,QAAQ;AAAA,QAChB;AAAA,QACA;AAAA,MACD,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EACA,QAAQ;AAAA,IACP;AAAA,IACA;AAAA,IACA,SAAS;AAAA,IACT;AAAA,EACD,GAKG;AACF,WAAO,KAAK;AAAA,MACX,SAAS,QAAQ;AAAA,QAChB;AAAA,QACA;AAAA,QACA,SAAS;AAAA,QACT,QAAQ,KAAK,OAAO,MAAM;AAAA,MAC3B,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EACA,SAAS;AAAA,IACR,WAAW;AAAA,IACX,GAAG;AAAA,EACJ,GAYM;AACL,WAAO,KAAK;AAAA,MACX,SAAS,SAAS;AAAA,QACjB,GAAG;AAAA,QACH,WAAW,MAAM,IAAI,CAAC,QAAQ,sBAAK,yDAAL,WAAmC,IAAI;AAAA,MACtE,CAA4C;AAAA,IAC7C;AAAA,EACD;AAAA,EACA,gBACC,SACA,SACC;AACD,WAAO,KAAK;AAAA,MACX,SAAS;AAAA,QACR,QAAQ,IAAI,CAAC,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,QACrC,OAAO,YAAY,WAChB,KAAK,KAAK,QAAQ,OAAO,IACzB,sBAAK,yDAAL,WAAmC;AAAA,MACvC;AAAA,IACD;AAAA,EACD;AAAA,EACA,YAAY;AAAA,IACX;AAAA,IACA;AAAA,EACD,GAGG;AACF,WAAO,KAAK;AAAA,MACX,SAAS,YAAY;AAAA,QACpB;AAAA,QACA,UAAU,SAAS,IAAI,CAAC,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MACjD,CAAC;AAAA,IACF;AAAA,EACD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY;AACX,WAAO,KAAK,UAAU,2BAA2B,mBAAK,OAAM,SAAS,CAAC,CAAC;AAAA,EACxE;AAAA,EAEA,MAAM,OAAO,UAAuC,CAAC,GAAoB;AACxE,UAAM,KAAK,wBAAwB,OAAO;AAC1C,UAAM,gBAAgB,KAAK,gBAAgB;AAC3C,WAAO,KAAK;AAAA,MACX;AAAA,QACC;AAAA,QACA,gBACG;AAAA,UACA,GAAG,mBAAK,OAAM,SAAS;AAAA,UACvB,QAAQ,mBAAK,OAAM,UAAU;AAAA,QAC9B,IACC,mBAAK,OAAM,SAAS;AAAA,MACxB;AAAA,MACA,CAAC,MAAM,UAAW,OAAO,UAAU,WAAW,MAAM,SAAS,IAAI;AAAA,MACjE;AAAA,IACD;AAAA,EACD;AAAA;AAAA,EAGA,MAAM,KAAK,SAAmD;AAC7D,UAAM,EAAE,QAAQ,GAAG,aAAa,IAAI;AACpC,UAAM,QAAQ,MAAM,KAAK,MAAM,YAAY;AAC3C,WAAO,OAAO,gBAAgB,KAAK;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,kBAAkB;AACjB,QAAI,CAAC,mBAAK,OAAM,QAAQ;AACvB,aAAO;AAAA,IACR;AAEA,QAAI,mBAAK,kBAAiB,OAAO,GAAG;AACnC,aAAO;AAAA,IACR;AAEA,QAAI,mBAAK,OAAM,SAAS,KAAK,CAAC,QAAQ,IAAI,OAAO,GAAG;AACnD,aAAO;AAAA,IACR;AAEA,QAAI,2BAA2B,mBAAK,QAAO,CAAC,CAAC,GAAG;AAC/C,aAAO;AAAA,IACR;AAEA,WAAO;AAAA,EACR;AAAA;AAAA,EAGA,MAAM,MAAM,UAAmC,CAAC,GAAwB;AACvE,UAAM,KAAK,wBAAwB,OAAO;AAC1C,UAAM,sBAAK,yCAAL,WAAmB;AACzB,WAAO,mBAAK,OAAM,MAAM;AAAA,MACvB,qBAAqB,QAAQ;AAAA,IAC9B,CAAC;AAAA,EACF;AAAA;AAAA,EAGA,MAAM,UACL,UAEI,CAAC,GACa;AAClB,UAAM,KAAK,wBAAwB,OAAO;AAC1C,UAAM,sBAAK,yCAAL,WAAmB;AACzB,WAAO,mBAAK,OAAM,UAAU;AAAA,EAC7B;AAAA,EA+IA,MAAM,wBAAwB,SAAsC;AACnE,UAAM,sBAAK,gDAAL;AACN,0BAAK,kDAAL;AACA,UAAM,UAAU,oBAAI,IAAY;AAChC,eAAW,WAAW,mBAAK,OAAM,UAAU;AAC1C,UAAI,QAAQ,SAAS;AACpB,gBAAQ,IAAI,QAAQ,QAAQ,IAAI;AAAA,MACjC;AAAA,IACD;AAEA,UAAM,QAAQ,CAAC,GAAG,mBAAK,sBAAqB;AAE5C,eAAW,UAAU,SAAS;AAC7B,UAAI,QAAQ,kBAAkB,SAAS,MAAM,GAAG;AAC/C;AAAA,MACD;AAEA,UAAI,CAAC,mBAAK,kBAAiB,IAAI,MAAM,GAAG;AACvC,cAAM,IAAI,MAAM,+BAA+B,MAAM,EAAE;AAAA,MACxD;AAEA,YAAM,KAAK,mBAAK,kBAAiB,IAAI,MAAM,CAAE;AAAA,IAC9C;AAEA,UAAM,KAAK,oBAAoB,CAAC;AAEhC,UAAM,sBAAK,uCAAL,WAAiB,OAAO;AAAA,EAC/B;AACD;AAjvBC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAmIA;AA3IM;AAyQN,UAAK,WAAG;AACP,QAAM,OAAO,IAAI,aAAY;AAE7B,qBAAK,OAAQ,mBAAK;AAClB,qBAAK,uBAAwB,mBAAK;AAClC,qBAAK,eAAgB,mBAAK;AAC1B,qBAAK,kBAAmB,mBAAK;AAC7B,qBAAK,kBAAmB,mBAAK;AAC7B,qBAAK,mBAAoB,IAAI,IAAI,mBAAK,kBAAiB;AACvD,qBAAK,QAAS,mBAAK;AACnB,qBAAK,eAAc,KAAK,mBAAK,cAAa;AAC1C,qBAAK,iBAAgB,KAAK,mBAAK,gBAAe;AAE9C,SAAO;AACR;AAqDA,gBAA8B,SAAC,SAAY;AAC1C,QAAM,cAAc,mBAAK,OAAM,SAAS;AACxC,qBAAK,iBAAgB,KAAK,OAAO;AACjC,qBAAK,mBAAkB,IAAI,WAAW;AACtC,qBAAK,OAAM,SAAS,KAAK,OAAO;AAEhC,qBAAK,OAAM,oBAAoB,aAAa,CAAC,QAAQ;AACpD,QAAI,IAAI,UAAU,YAAY,CAAC,mBAAK,mBAAkB,IAAI,IAAI,MAAM,GAAG;AACtE,YAAM,IAAI;AAAA,QACT,oBAAoB,IAAI,MAAM;AAAA,MAC/B;AAAA,IACD;AAEA,QAAI,IAAI,UAAU,kBAAkB,CAAC,mBAAK,mBAAkB,IAAI,IAAI,aAAa,CAAC,CAAC,GAAG;AACrF,YAAM,IAAI;AAAA,QACT,2BAA2B,IAAI,aAAa,CAAC,CAAC,KAAK,IAAI,aAAa,CAAC,CAAC;AAAA,MACvE;AAAA,IACD;AAEA,QAAI,IAAI,UAAU,WAAW,IAAI,SAAS,mBAAK,OAAM,OAAO,QAAQ;AACnE,YAAM,IAAI;AAAA,QACT,kBAAkB,IAAI,KAAK;AAAA,MAC5B;AAAA,IACD;AAEA,WAAO;AAAA,EACR,CAAC;AAED,SAAO;AACR;AAEA,cAAsC,SAAC,MAAS,OAAgB;AAC/D,qBAAK,eAAc,KAAK,KAAK;AAC7B,SAAO,mBAAK,OAAM,SAAS,MAAM,KAAK;AACvC;AAEA,kCAA6B,SAAC,KAA+C;AAC5E,MAAI,gBAAgB,GAAG,GAAG;AACzB,WAAO,KAAK,KAAK,GAAG;AAAA,EACrB;AAEA,SAAO,sBAAK,4CAAL,WAAsB;AAC9B;AAEA,qBAAgB,SAAC,KAAoC;AACpD,MAAI,OAAO,QAAQ,YAAY;AAC9B,UAAM,WAAW,KAAK,IAAI,GAAY;AAEtC,QAAI,OAAO,aAAa,YAAY;AACnC,aAAO,sBAAK,4CAAL,WAAsB;AAAA,IAC9B;AAEA,WAAO,MAAM,UAAU,QAAQ;AAAA,EAChC;AAEA,SAAO,MAAM,UAAU,GAAG;AAC3B;AAyMM,kBAAa,eAAC,SAAkC;AACrD,MAAI,CAAC,QAAQ,uBAAuB,CAAC,mBAAK,OAAM,QAAQ;AACvD,UAAM,IAAI,MAAM,4BAA4B;AAAA,EAC7C;AAEA,QAAM,sBAAK,uCAAL,WAAiB,CAAC,GAAG,mBAAK,gBAAe,wBAAwB,GAAG;AAC3E;AAEM,gBAAW,eAAC,SAA8B,SAAsC;AACrF,QAAM,aAAa,CAAC,MAAc;AACjC,QAAI,KAAK,QAAQ,QAAQ;AACxB,aAAO,MAAM;AAAA,MAAC;AAAA,IACf;AACA,UAAM,SAAS,QAAQ,CAAC;AAExB,WAAO,YAAY;AAClB,YAAM,OAAO,WAAW,IAAI,CAAC;AAC7B,UAAI,aAAa;AACjB,UAAI,eAAe;AAEnB,YAAM,OAAO,mBAAK,QAAO,SAAS,YAAY;AAC7C,YAAI,YAAY;AACf,gBAAM,IAAI,MAAM,uDAAuD,CAAC,EAAE;AAAA,QAC3E;AAEA,qBAAa;AAEb,cAAM,KAAK;AAEX,uBAAe;AAAA,MAChB,CAAC;AAED,UAAI,CAAC,YAAY;AAChB,cAAM,IAAI,MAAM,8CAA8C,CAAC,EAAE;AAAA,MAClE;AAEA,UAAI,CAAC,cAAc;AAClB,cAAM,IAAI,MAAM,+CAA+C,CAAC,EAAE;AAAA,MACnE;AAAA,IACD;AAAA,EACD;AAEA,QAAM,WAAW,CAAC,EAAE;AAEpB,qBAAK,eAAgB,mBAAK,OAAM,OAAO,MAAM;AAC7C,qBAAK,iBAAkB,mBAAK,OAAM,SAAS,MAAM;AAClD;AAEM,yBAAoB,iBAAG;AAC5B,SAAO,mBAAK,kBAAiB,OAAO,GAAG;AACtC,UAAM,aAAa,QAAQ,IAAI,mBAAK,iBAAgB;AACpD,uBAAK,kBAAiB,MAAM;AAC5B,uBAAK,kBAAiB,IAAI,UAAU;AACpC,UAAM;AACN,uBAAK,kBAAiB,OAAO,UAAU;AAAA,EACxC;AACD;AAEA,2BAAsB,WAAG;AACxB,QAAM,oBAAoB,mBAAK,OAAM;AACrC,QAAM,kBAAkB,mBAAK,OAAM;AAEnC,QAAM,kBAAmB,mBAAK,iBAA8B,KAAK,QAAQ;AACzE,QAAM,gBAAiB,mBAAK,eAA4B,KAAK,QAAQ;AAErE,MAAI,gBAAgB,WAAW,kBAAkB,QAAQ;AACxD,UAAM,IAAI,MAAM,yDAAyD;AAAA,EAC1E;AAEA,MAAI,cAAc,WAAW,gBAAgB,QAAQ;AACpD,UAAM,IAAI,MAAM,uDAAuD;AAAA,EACxE;AAEA,QAAM,mBAAmB,gBAAgB;AAAA,IACxC,CAAC,QAAQ,IAAI,SAAS,SAAS;AAAA,EAChC;AAEA,qBAAK,OAAM,WAAW;AACtB,qBAAK,OAAM,SAAS;AACpB,qBAAK,iBAAkB;AACvB,qBAAK,eAAgB;AACrB,qBAAK,mBAAoB,IAAI,IAAI,iBAAiB,IAAI,CAAC,GAAG,MAAM,CAAC,CAAC;AAElE,WAAS,iBAAiB,OAAuB;AAChD,UAAM,UAAU,kBAAkB,KAAK;AACvC,QAAI,QAAQ,SAAS,SAAS,yBAAyB;AACtD,YAAM,SAAS,QAAQ,QAAQ,KAAK;AAEpC,UAAI,UAAU,MAAM;AACnB,cAAM,IAAI,MAAM,6CAA6C;AAAA,MAC9D;AAEA,aAAO,iBAAiB,OAAO,MAAM;AAAA,IACtC;AAEA,UAAM,UAAU,iBAAiB,QAAQ,OAAO;AAEhD,QAAI,YAAY,IAAI;AACnB,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC5D;AAEA,WAAO;AAAA,EACR;AAEA,qBAAK,OAAM,aAAa,CAAC,QAAQ;AAChC,QAAI,IAAI,UAAU,SAAS;AAC1B,YAAM,UAAU,cAAc,QAAQ,gBAAgB,IAAI,KAAK,CAAC;AAEhE,UAAI,YAAY,IAAI;AACnB,cAAM,IAAI,MAAM,6BAA6B;AAAA,MAC9C;AAEA,aAAO,EAAE,GAAG,KAAK,OAAO,QAAQ;AAAA,IACjC,WAAW,IAAI,UAAU,UAAU;AAClC,YAAM,UAAU,iBAAiB,IAAI,MAAM;AAE3C,aAAO,EAAE,GAAG,KAAK,QAAQ,QAAQ;AAAA,IAClC,WAAW,IAAI,UAAU,gBAAgB;AACxC,YAAM,UAAU,iBAAiB,IAAI,aAAa,CAAC,CAAC;AAEpD,aAAO,EAAE,GAAG,KAAK,cAAc,CAAC,SAAS,IAAI,aAAa,CAAC,CAAC,EAAE;AAAA,IAC/D;AAEA,WAAO;AAAA,EACR,CAAC;AAED,aAAW,CAAC,GAAG,GAAG,KAAK,kBAAkB,QAAQ,GAAG;AACnD,QAAI,IAAI,SAAS,SAAS,yBAAyB;AAClD,UAAI;AACH,YAAI,QAAQ,KAAK,cAAc,iBAAiB,CAAC;AAAA,MAClD,SAAS,GAAG;AAAA,MAEZ;AAAA,IACD;AAAA,EACD;AACD;AAptBM,IAAM,cAAN;", "names": ["result"]}