{"version": 3, "sources": ["../../../src/transactions/Inputs.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { toBase64 } from '@mysten/bcs';\nimport type { SerializedBcs } from '@mysten/bcs';\n\nimport { normalizeSuiAddress } from '../utils/sui-types.js';\nimport type { CallArg, ObjectRef } from './data/internal.js';\n\nfunction Pure(data: Uint8Array | SerializedBcs<any>): Extract<CallArg, { Pure: unknown }> {\n\treturn {\n\t\t$kind: 'Pure',\n\t\tPure: {\n\t\t\tbytes: data instanceof Uint8Array ? toBase64(data) : data.toBase64(),\n\t\t},\n\t};\n}\n\nexport const Inputs = {\n\tPure,\n\tObjectRef({ objectId, digest, version }: ObjectRef): Extract<CallArg, { Object: unknown }> {\n\t\treturn {\n\t\t\t$kind: 'Object',\n\t\t\tObject: {\n\t\t\t\t$kind: 'ImmOrOwnedObject',\n\t\t\t\tImmOrOwnedObject: {\n\t\t\t\t\tdigest,\n\t\t\t\t\tversion,\n\t\t\t\t\tobjectId: normalizeSuiAddress(objectId),\n\t\t\t\t},\n\t\t\t},\n\t\t};\n\t},\n\tSharedObjectRef({\n\t\tobjectId,\n\t\tmutable,\n\t\tinitialSharedVersion,\n\t}: {\n\t\tobjectId: string;\n\t\tmutable: boolean;\n\t\tinitialSharedVersion: number | string;\n\t}): Extract<CallArg, { Object: unknown }> {\n\t\treturn {\n\t\t\t$kind: 'Object',\n\t\t\tObject: {\n\t\t\t\t$kind: 'SharedObject',\n\t\t\t\tSharedObject: {\n\t\t\t\t\tmutable,\n\t\t\t\t\tinitialSharedVersion,\n\t\t\t\t\tobjectId: normalizeSuiAddress(objectId),\n\t\t\t\t},\n\t\t\t},\n\t\t};\n\t},\n\tReceivingRef({ objectId, digest, version }: ObjectRef): Extract<CallArg, { Object: unknown }> {\n\t\treturn {\n\t\t\t$kind: 'Object',\n\t\t\tObject: {\n\t\t\t\t$kind: 'Receiving',\n\t\t\t\tReceiving: {\n\t\t\t\t\tdigest,\n\t\t\t\t\tversion,\n\t\t\t\t\tobjectId: normalizeSuiAddress(objectId),\n\t\t\t\t},\n\t\t\t},\n\t\t};\n\t},\n};\n"], "mappings": "AAGA,SAAS,gBAAgB;AAGzB,SAAS,2BAA2B;AAGpC,SAAS,KAAK,MAA4E;AACzF,SAAO;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,MACL,OAAO,gBAAgB,aAAa,SAAS,IAAI,IAAI,KAAK,SAAS;AAAA,IACpE;AAAA,EACD;AACD;AAEO,MAAM,SAAS;AAAA,EACrB;AAAA,EACA,UAAU,EAAE,UAAU,QAAQ,QAAQ,GAAqD;AAC1F,WAAO;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,QACP,OAAO;AAAA,QACP,kBAAkB;AAAA,UACjB;AAAA,UACA;AAAA,UACA,UAAU,oBAAoB,QAAQ;AAAA,QACvC;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA,gBAAgB;AAAA,IACf;AAAA,IACA;AAAA,IACA;AAAA,EACD,GAI0C;AACzC,WAAO;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,QACP,OAAO;AAAA,QACP,cAAc;AAAA,UACb;AAAA,UACA;AAAA,UACA,UAAU,oBAAoB,QAAQ;AAAA,QACvC;AAAA,MACD;AAAA,IACD;AAAA,EACD;AAAA,EACA,aAAa,EAAE,UAAU,QAAQ,QAAQ,GAAqD;AAC7F,WAAO;AAAA,MACN,OAAO;AAAA,MACP,QAAQ;AAAA,QACP,OAAO;AAAA,QACP,WAAW;AAAA,UACV;AAAA,UACA;AAAA,UACA,UAAU,oBAAoB,QAAQ;AAAA,QACvC;AAAA,MACD;AAAA,IACD;AAAA,EACD;AACD;", "names": []}