{"version": 3, "sources": ["../../../../src/transactions/executor/queue.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nexport class SerialQueue {\n\t#queue: Array<() => void> = [];\n\n\tasync runTask<T>(task: () => Promise<T>): Promise<T> {\n\t\treturn new Promise((resolve, reject) => {\n\t\t\tthis.#queue.push(() => {\n\t\t\t\ttask()\n\t\t\t\t\t.finally(() => {\n\t\t\t\t\t\tthis.#queue.shift();\n\t\t\t\t\t\tif (this.#queue.length > 0) {\n\t\t\t\t\t\t\tthis.#queue[0]();\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t.then(resolve, reject);\n\t\t\t});\n\n\t\t\tif (this.#queue.length === 1) {\n\t\t\t\tthis.#queue[0]();\n\t\t\t}\n\t\t});\n\t}\n}\n\nexport class ParallelQueue {\n\t#queue: Array<() => void> = [];\n\tactiveTasks = 0;\n\tmaxTasks: number;\n\n\tconstructor(maxTasks: number) {\n\t\tthis.maxTasks = maxTasks;\n\t}\n\n\trunTask<T>(task: () => Promise<T>): Promise<T> {\n\t\treturn new Promise<T>((resolve, reject) => {\n\t\t\tif (this.activeTasks < this.maxTasks) {\n\t\t\t\tthis.activeTasks++;\n\n\t\t\t\ttask()\n\t\t\t\t\t.finally(() => {\n\t\t\t\t\t\tif (this.#queue.length > 0) {\n\t\t\t\t\t\t\tthis.#queue.shift()!();\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.activeTasks--;\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t\t.then(resolve, reject);\n\t\t\t} else {\n\t\t\t\tthis.#queue.push(() => {\n\t\t\t\t\ttask()\n\t\t\t\t\t\t.finally(() => {\n\t\t\t\t\t\t\tif (this.#queue.length > 0) {\n\t\t\t\t\t\t\t\tthis.#queue.shift()!();\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis.activeTasks--;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\t.then(resolve, reject);\n\t\t\t\t});\n\t\t\t}\n\t\t});\n\t}\n}\n"], "mappings": ";;;;;;AAAA,YAAAA;AAGO,MAAM,YAAY;AAAA,EAAlB;AACN,+BAA4B,CAAC;AAAA;AAAA,EAE7B,MAAM,QAAW,MAAoC;AACpD,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,yBAAK,QAAO,KAAK,MAAM;AACtB,aAAK,EACH,QAAQ,MAAM;AACd,6BAAK,QAAO,MAAM;AAClB,cAAI,mBAAK,QAAO,SAAS,GAAG;AAC3B,+BAAK,QAAO,CAAC,EAAE;AAAA,UAChB;AAAA,QACD,CAAC,EACA,KAAK,SAAS,MAAM;AAAA,MACvB,CAAC;AAED,UAAI,mBAAK,QAAO,WAAW,GAAG;AAC7B,2BAAK,QAAO,CAAC,EAAE;AAAA,MAChB;AAAA,IACD,CAAC;AAAA,EACF;AACD;AApBC;AAsBM,MAAM,cAAc;AAAA,EAK1B,YAAY,UAAkB;AAJ9B,uBAAAA,SAA4B,CAAC;AAC7B,uBAAc;AAIb,SAAK,WAAW;AAAA,EACjB;AAAA,EAEA,QAAW,MAAoC;AAC9C,WAAO,IAAI,QAAW,CAAC,SAAS,WAAW;AAC1C,UAAI,KAAK,cAAc,KAAK,UAAU;AACrC,aAAK;AAEL,aAAK,EACH,QAAQ,MAAM;AACd,cAAI,mBAAKA,SAAO,SAAS,GAAG;AAC3B,+BAAKA,SAAO,MAAM,EAAG;AAAA,UACtB,OAAO;AACN,iBAAK;AAAA,UACN;AAAA,QACD,CAAC,EACA,KAAK,SAAS,MAAM;AAAA,MACvB,OAAO;AACN,2BAAKA,SAAO,KAAK,MAAM;AACtB,eAAK,EACH,QAAQ,MAAM;AACd,gBAAI,mBAAKA,SAAO,SAAS,GAAG;AAC3B,iCAAKA,SAAO,MAAM,EAAG;AAAA,YACtB,OAAO;AACN,mBAAK;AAAA,YACN;AAAA,UACD,CAAC,EACA,KAAK,SAAS,MAAM;AAAA,QACvB,CAAC;AAAA,MACF;AAAA,IACD,CAAC;AAAA,EACF;AACD;AArCCA,UAAA;", "names": ["_queue"]}