{"version": 3, "sources": ["../../../../src/transactions/plugins/NamedPackagesPlugin.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { ClientCache } from '../../experimental/cache.js';\nimport { MvrClient } from '../../experimental/mvr.js';\nimport type { BuildTransactionOptions } from '../resolve.js';\nimport type { TransactionDataBuilder } from '../TransactionData.js';\nimport { findNamesInTransaction, replaceNames } from '../../experimental/mvr.js';\nimport type { NamedPackagesOverrides } from '../../experimental/mvr.js';\n\nexport type NamedPackagesPluginOptions = {\n\t/**\n\t * The URL of the MVR API to use for resolving names.\n\t */\n\turl: string;\n\t/**\n\t * The number of names to resolve in each batch request.\n\t * Needs to be calculated based on the GraphQL query limits.\n\t */\n\tpageSize?: number;\n\t/**\n\t * Local overrides for the resolution plugin. Pass this to pre-populate\n\t * the cache with known packages / types (especially useful for local or CI testing).\n\t *\n\t * The type cache expects ONLY first-level types to ensure the cache is more composable.\n\t *\n\t * \tExpected format example:\n\t *  {\n\t * \t\tpackages: {\n\t * \t\t\t'@framework/std': '0x1234',\n\t * \t\t},\n\t * \t\ttypes: {\n\t * \t\t\t'@framework/std::string::String': '0x1234::string::String',\n\t * \t\t},\n\t * \t}\n\t *\n\t */\n\toverrides?: NamedPackagesOverrides;\n};\n\n// The original versions of the mvr plugin cached lookups by mutating overrides.\n// We don't want to mutate the options, but we can link our cache to the provided overrides object\n// This preserves the caching across transactions while removing the mutation side effects\nconst cacheMap = new WeakMap<object, ClientCache>();\n\n/**\n * @experimental This plugin is in experimental phase and there might be breaking changes in the future\n *\n * Adds named resolution so that you can use .move names in your transactions.\n * e.g. `@org/app::type::Type` will be resolved to `0x1234::type::Type`.\n * This plugin will resolve all names & types in the transaction block.\n *\n * To install this plugin globally in your app, use:\n * ```\n * Transaction.registerGlobalSerializationPlugin(\"namedPackagesPlugin\", namedPackagesPlugin({ suiGraphQLClient }));\n * ```\n *\n * You can also define `overrides` to pre-populate name resolutions locally (removes the GraphQL request).\n */\nexport const namedPackagesPlugin = (options?: NamedPackagesPluginOptions) => {\n\tlet mvrClient: MvrClient | undefined;\n\n\tif (options) {\n\t\tconst overrides = options.overrides ?? {\n\t\t\tpackages: {},\n\t\t\ttypes: {},\n\t\t};\n\n\t\tif (!cacheMap.has(overrides)) {\n\t\t\tcacheMap.set(overrides, new ClientCache());\n\t\t}\n\n\t\tmvrClient = new MvrClient({\n\t\t\tcache: cacheMap.get(overrides)!,\n\t\t\turl: options.url,\n\t\t\tpageSize: options.pageSize,\n\t\t\toverrides: overrides,\n\t\t});\n\t}\n\n\treturn async (\n\t\ttransactionData: TransactionDataBuilder,\n\t\tbuildOptions: BuildTransactionOptions,\n\t\tnext: () => Promise<void>,\n\t) => {\n\t\tconst names = findNamesInTransaction(transactionData);\n\n\t\tif (names.types.length === 0 && names.packages.length === 0) {\n\t\t\treturn next();\n\t\t}\n\n\t\tconst resolved = await (mvrClient || getClient(buildOptions).core.mvr).resolve({\n\t\t\ttypes: names.types,\n\t\t\tpackages: names.packages,\n\t\t});\n\n\t\treplaceNames(transactionData, resolved);\n\n\t\tawait next();\n\t};\n};\n\nexport function getClient(options: BuildTransactionOptions) {\n\tif (!options.client) {\n\t\tthrow new Error(\n\t\t\t`No sui client passed to Transaction#build, but transaction data was not sufficient to build offline.`,\n\t\t);\n\t}\n\n\treturn options.client;\n}\n"], "mappings": "AAGA,SAAS,mBAAmB;AAC5B,SAAS,iBAAiB;AAG1B,SAAS,wBAAwB,oBAAoB;AAoCrD,MAAM,WAAW,oBAAI,QAA6B;AAgB3C,MAAM,sBAAsB,CAAC,YAAyC;AAC5E,MAAI;AAEJ,MAAI,SAAS;AACZ,UAAM,YAAY,QAAQ,aAAa;AAAA,MACtC,UAAU,CAAC;AAAA,MACX,OAAO,CAAC;AAAA,IACT;AAEA,QAAI,CAAC,SAAS,IAAI,SAAS,GAAG;AAC7B,eAAS,IAAI,WAAW,IAAI,YAAY,CAAC;AAAA,IAC1C;AAEA,gBAAY,IAAI,UAAU;AAAA,MACzB,OAAO,SAAS,IAAI,SAAS;AAAA,MAC7B,KAAK,QAAQ;AAAA,MACb,UAAU,QAAQ;AAAA,MAClB;AAAA,IACD,CAAC;AAAA,EACF;AAEA,SAAO,OACN,iBACA,cACA,SACI;AACJ,UAAM,QAAQ,uBAAuB,eAAe;AAEpD,QAAI,MAAM,MAAM,WAAW,KAAK,MAAM,SAAS,WAAW,GAAG;AAC5D,aAAO,KAAK;AAAA,IACb;AAEA,UAAM,WAAW,OAAO,aAAa,UAAU,YAAY,EAAE,KAAK,KAAK,QAAQ;AAAA,MAC9E,OAAO,MAAM;AAAA,MACb,UAAU,MAAM;AAAA,IACjB,CAAC;AAED,iBAAa,iBAAiB,QAAQ;AAEtC,UAAM,KAAK;AAAA,EACZ;AACD;AAEO,SAAS,UAAU,SAAkC;AAC3D,MAAI,CAAC,QAAQ,QAAQ;AACpB,UAAM,IAAI;AAAA,MACT;AAAA,IACD;AAAA,EACD;AAEA,SAAO,QAAQ;AAChB;", "names": []}