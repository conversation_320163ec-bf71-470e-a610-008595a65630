{"name": "@mysten/sui", "author": "Mysten Labs <<EMAIL>>", "description": "Sui TypeScript API", "homepage": "https://sdk.mystenlabs.com", "version": "1.36.0", "license": "Apache-2.0", "sideEffects": false, "files": ["CHANGELOG.md", "LICENSE", "README.md", "bcs", "builder", "client", "cryptography", "dist", "experimental", "faucet", "graphql", "keypairs", "multisig", "src", "transactions", "utils", "verify", "zklogin"], "engines": {"node": ">=18"}, "type": "commonjs", "exports": {"./bcs": {"import": "./dist/esm/bcs/index.js", "require": "./dist/cjs/bcs/index.js"}, "./client": {"import": "./dist/esm/client/index.js", "require": "./dist/cjs/client/index.js"}, "./cryptography": {"import": "./dist/esm/cryptography/index.js", "require": "./dist/cjs/cryptography/index.js"}, "./experimental": {"import": "./dist/esm/experimental/index.js", "require": "./dist/cjs/experimental/index.js"}, "./faucet": {"import": "./dist/esm/faucet/index.js", "require": "./dist/cjs/faucet/index.js"}, "./graphql": {"import": "./dist/esm/graphql/index.js", "require": "./dist/cjs/graphql/index.js"}, "./keypairs/ed25519": {"import": "./dist/esm/keypairs/ed25519/index.js", "require": "./dist/cjs/keypairs/ed25519/index.js"}, "./keypairs/secp256k1": {"import": "./dist/esm/keypairs/secp256k1/index.js", "require": "./dist/cjs/keypairs/secp256k1/index.js"}, "./keypairs/secp256r1": {"import": "./dist/esm/keypairs/secp256r1/index.js", "require": "./dist/cjs/keypairs/secp256r1/index.js"}, "./keypairs/passkey": {"import": "./dist/esm/keypairs/passkey/index.js", "require": "./dist/cjs/keypairs/passkey/index.js"}, "./multisig": {"import": "./dist/esm/multisig/index.js", "require": "./dist/cjs/multisig/index.js"}, "./transactions": {"import": "./dist/esm/transactions/index.js", "require": "./dist/cjs/transactions/index.js"}, "./utils": {"import": "./dist/esm/utils/index.js", "require": "./dist/cjs/utils/index.js"}, "./verify": {"import": "./dist/esm/verify/index.js", "require": "./dist/cjs/verify/index.js"}, "./zklogin": {"import": "./dist/esm/zklogin/index.js", "require": "./dist/cjs/zklogin/index.js"}, "./graphql/schemas/2024.1": {"import": "./dist/esm/graphql/schemas/2024.1/index.js", "require": "./dist/cjs/graphql/schemas/2024.1/index.js"}, "./graphql/schemas/2024.4": {"import": "./dist/esm/graphql/schemas/2024.4/index.js", "require": "./dist/cjs/graphql/schemas/2024.4/index.js"}, "./graphql/schemas/latest": {"import": "./dist/esm/graphql/schemas/latest/index.js", "require": "./dist/cjs/graphql/schemas/latest/index.js"}}, "bugs": {"url": "https://github.com/MystenLabs/ts-sdks/issues/new"}, "publishConfig": {"access": "public"}, "devDependencies": {"@0no-co/graphqlsp": "^1.12.11", "@graphql-codegen/add": "^5.0.3", "@graphql-codegen/cli": "^5.0.6", "@graphql-codegen/typed-document-node": "^5.1.1", "@graphql-codegen/typescript": "4.1.6", "@graphql-codegen/typescript-document-nodes": "4.0.16", "@graphql-codegen/typescript-operations": "^4.6.1", "@iarna/toml": "^2.2.5", "@parcel/watcher": "^2.5.1", "@types/node": "^22.15.29", "@types/tmp": "^0.2.6", "@types/ws": "^8.18.1", "cross-env": "^7.0.3", "graphql-config": "^5.1.5", "msw": "^2.9.0", "tmp": "^0.2.3", "ts-retry-promise": "^0.8.1", "typescript": "^5.8.3", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.1", "wait-on": "^8.0.3", "ws": "^8.18.2", "@mysten/build-scripts": "^0.0.0"}, "dependencies": {"@graphql-typed-document-node/core": "^3.2.0", "@noble/curves": "^1.9.1", "@noble/hashes": "^1.8.0", "@scure/base": "^1.2.6", "@scure/bip32": "^1.7.0", "@scure/bip39": "^1.6.0", "gql.tada": "^1.8.2", "graphql": "^16.11.0", "poseidon-lite": "^0.2.0", "valibot": "^0.36.0", "@mysten/bcs": "1.6.4", "@mysten/utils": "0.1.1"}, "scripts": {"clean": "rm -rf tsconfig.tsbuildinfo ./dist", "codegen:graphql": "graphql-codegen --config graphql-codegen.ts", "codegen:version": "node genversion.mjs", "build": "node genversion.mjs && pnpm build:package", "build:package": "build-package", "vitest": "vitest", "test": "pnpm test:typecheck && pnpm test:unit", "test:typecheck": "tsc -p ./test", "test:unit": "vitest run unit __tests__", "test:e2e": "vitest run --config test/e2e/vitest.config.mts", "size": "size-limit", "analyze": "size-limit --why", "prettier:check": "prettier -c --ignore-unknown .", "prettier:fix": "prettier -w --ignore-unknown .", "eslint:check": "eslint --max-warnings=0 .", "eslint:fix": "pnpm run eslint:check --fix", "lint": "pnpm run eslint:check && pnpm run prettier:check", "lint:fix": "pnpm run eslint:fix && pnpm run prettier:fix", "update-schemas": "pnpm tsx scripts/update-graphql-schemas.ts", "generate-schema": "gql.tada generate-output"}}