{"version": 3, "sources": ["../../../src/jwt-session/index.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport * as v from 'valibot';\nimport { SignJWT, decodeJwt, jwtVerify } from 'jose';\n\nconst AccountSchema = v.object({\n\taddress: v.string(),\n\tpublicKey: v.string(),\n});\n\nconst JwtSessionSchema = v.object({\n\texp: v.number(), // Expiration Time\n\tiat: v.number(), // Issued At\n\tiss: v.string(), // Issuer\n\taud: v.string(), // Audience (the dapp origin)\n\tpayload: v.object({\n\t\taccounts: v.array(AccountSchema),\n\t}),\n});\n\ntype JwtSessionPayload = v.InferOutput<typeof JwtSessionSchema>;\n\nexport async function createJwtSession(\n\tpayload: JwtSessionPayload['payload'],\n\toptions: {\n\t\tsecretKey: Parameters<SignJWT['sign']>[0];\n\t\texpirationTime: Parameters<SignJWT['setExpirationTime']>[0];\n\t\tissuer: Parameters<SignJWT['setIssuer']>[0];\n\t\taudience: Parameters<SignJWT['setAudience']>[0];\n\t},\n) {\n\tconst token = await new SignJWT({ payload })\n\t\t.setProtectedHeader({ alg: 'HS256' })\n\t\t.setExpirationTime(options.expirationTime)\n\t\t.setIssuedAt()\n\t\t.setIssuer(options.issuer)\n\t\t.setAudience(options.audience)\n\t\t.sign(options.secretKey);\n\n\treturn token;\n}\n\nexport function decodeJwtSession(jwt: string) {\n\tconst decodedJwt = decodeJwt(jwt);\n\n\treturn v.parse(JwtSessionSchema, decodedJwt);\n}\n\nexport async function verifyJwtSession(jwt: string, secretKey: CryptoKey | Uint8Array) {\n\tconst verified = await jwtVerify(jwt, secretKey, { algorithms: ['HS256'] });\n\n\treturn v.parse(JwtSessionSchema, verified.payload);\n}\n"], "mappings": "AAGA,YAAY,OAAO;AACnB,SAAS,SAAS,WAAW,iBAAiB;AAE9C,MAAM,gBAAgB,EAAE,OAAO;AAAA,EAC9B,SAAS,EAAE,OAAO;AAAA,EAClB,WAAW,EAAE,OAAO;AACrB,CAAC;AAED,MAAM,mBAAmB,EAAE,OAAO;AAAA,EACjC,KAAK,EAAE,OAAO;AAAA;AAAA,EACd,KAAK,EAAE,OAAO;AAAA;AAAA,EACd,KAAK,EAAE,OAAO;AAAA;AAAA,EACd,KAAK,EAAE,OAAO;AAAA;AAAA,EACd,SAAS,EAAE,OAAO;AAAA,IACjB,UAAU,EAAE,MAAM,aAAa;AAAA,EAChC,CAAC;AACF,CAAC;AAID,eAAsB,iBACrB,SACA,SAMC;AACD,QAAM,QAAQ,MAAM,IAAI,QAAQ,EAAE,QAAQ,CAAC,EACzC,mBAAmB,EAAE,KAAK,QAAQ,CAAC,EACnC,kBAAkB,QAAQ,cAAc,EACxC,YAAY,EACZ,UAAU,QAAQ,MAAM,EACxB,YAAY,QAAQ,QAAQ,EAC5B,KAAK,QAAQ,SAAS;AAExB,SAAO;AACR;AAEO,SAAS,iBAAiB,KAAa;AAC7C,QAAM,aAAa,UAAU,GAAG;AAEhC,SAAO,EAAE,MAAM,kBAAkB,UAAU;AAC5C;AAEA,eAAsB,iBAAiB,KAAa,WAAmC;AACtF,QAAM,WAAW,MAAM,UAAU,KAAK,WAAW,EAAE,YAAY,CAAC,OAAO,EAAE,CAAC;AAE1E,SAAO,EAAE,MAAM,kBAAkB,SAAS,OAAO;AAClD;", "names": []}