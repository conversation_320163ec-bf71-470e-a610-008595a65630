{"version": 3, "sources": ["../../../src/web-wallet-channel/wallet-post-message-channel.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { parse } from 'valibot';\nimport type { RequestType } from './requests.js';\nimport { Request } from './requests.js';\nimport type { ResponsePayloadType, ResponseType } from './responses.js';\nimport { verifyJwtSession } from '../jwt-session/index.js';\n\nexport class WalletPostMessageChannel {\n\t#request: RequestType;\n\t#isSendCalled: boolean = false;\n\n\tconstructor(request: RequestType) {\n\t\tif (typeof window === 'undefined' || !window.opener) {\n\t\t\tthrow new Error(\n\t\t\t\t'This functionality requires a window opened through `window.open`. `window.opener` is not available.',\n\t\t\t);\n\t\t}\n\n\t\tthis.#request = request;\n\t}\n\n\tstatic fromPayload(payload: RequestType) {\n\t\tconst request = parse(Request, payload);\n\n\t\treturn new WalletPostMessageChannel(request);\n\t}\n\n\tstatic fromUrlHash(hash: string = window.location.hash.slice(1)) {\n\t\tconst decoded = atob(decodeURIComponent(hash));\n\t\tconst request = parse(Request, JSON.parse(decoded));\n\n\t\treturn new WalletPostMessageChannel(request);\n\t}\n\n\tgetRequestData() {\n\t\treturn this.#request;\n\t}\n\n\tasync verifyJwtSession(secretKey: Parameters<typeof verifyJwtSession>[1]) {\n\t\tif (!('session' in this.#request.payload)) {\n\t\t\treturn null;\n\t\t}\n\n\t\tconst session = await verifyJwtSession(this.#request.payload.session, secretKey);\n\n\t\tif (session.aud !== new URL(this.#request.appUrl).origin) {\n\t\t\tthrow new Error('App and session origin mismatch');\n\t\t}\n\n\t\tconst requestAddress = this.#request.payload.address;\n\t\tconst addressInSession = session.payload.accounts.find(\n\t\t\t(account) => account.address === requestAddress,\n\t\t);\n\n\t\tif (!addressInSession) {\n\t\t\tthrow new Error('Requested account not found in session');\n\t\t}\n\n\t\treturn session;\n\t}\n\n\tsendMessage(payload: ResponsePayloadType) {\n\t\tif (this.#isSendCalled) {\n\t\t\tthrow new Error('sendMessage() can only be called once');\n\t\t}\n\n\t\tthis.#isSendCalled = true;\n\n\t\twindow.opener.postMessage(\n\t\t\t{\n\t\t\t\tid: this.#request.requestId,\n\t\t\t\tsource: 'web-wallet-channel',\n\t\t\t\tpayload,\n\t\t\t\tversion: this.#request.version,\n\t\t\t} satisfies ResponseType,\n\t\t\tthis.#request.appUrl,\n\t\t);\n\t}\n\n\tclose(payload?: ResponsePayloadType) {\n\t\tif (payload) {\n\t\t\tthis.sendMessage(payload);\n\t\t}\n\t\twindow.close();\n\t}\n}\n"], "mappings": ";;;;;;;AAAA;AAGA,SAAS,aAAa;AAEtB,SAAS,eAAe;AAExB,SAAS,wBAAwB;AAE1B,MAAM,4BAAN,MAAM,0BAAyB;AAAA,EAIrC,YAAY,SAAsB;AAHlC;AACA,sCAAyB;AAGxB,QAAI,OAAO,WAAW,eAAe,CAAC,OAAO,QAAQ;AACpD,YAAM,IAAI;AAAA,QACT;AAAA,MACD;AAAA,IACD;AAEA,uBAAK,UAAW;AAAA,EACjB;AAAA,EAEA,OAAO,YAAY,SAAsB;AACxC,UAAM,UAAU,MAAM,SAAS,OAAO;AAEtC,WAAO,IAAI,0BAAyB,OAAO;AAAA,EAC5C;AAAA,EAEA,OAAO,YAAY,OAAe,OAAO,SAAS,KAAK,MAAM,CAAC,GAAG;AAChE,UAAM,UAAU,KAAK,mBAAmB,IAAI,CAAC;AAC7C,UAAM,UAAU,MAAM,SAAS,KAAK,MAAM,OAAO,CAAC;AAElD,WAAO,IAAI,0BAAyB,OAAO;AAAA,EAC5C;AAAA,EAEA,iBAAiB;AAChB,WAAO,mBAAK;AAAA,EACb;AAAA,EAEA,MAAM,iBAAiB,WAAmD;AACzE,QAAI,EAAE,aAAa,mBAAK,UAAS,UAAU;AAC1C,aAAO;AAAA,IACR;AAEA,UAAM,UAAU,MAAM,iBAAiB,mBAAK,UAAS,QAAQ,SAAS,SAAS;AAE/E,QAAI,QAAQ,QAAQ,IAAI,IAAI,mBAAK,UAAS,MAAM,EAAE,QAAQ;AACzD,YAAM,IAAI,MAAM,iCAAiC;AAAA,IAClD;AAEA,UAAM,iBAAiB,mBAAK,UAAS,QAAQ;AAC7C,UAAM,mBAAmB,QAAQ,QAAQ,SAAS;AAAA,MACjD,CAAC,YAAY,QAAQ,YAAY;AAAA,IAClC;AAEA,QAAI,CAAC,kBAAkB;AACtB,YAAM,IAAI,MAAM,wCAAwC;AAAA,IACzD;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,YAAY,SAA8B;AACzC,QAAI,mBAAK,gBAAe;AACvB,YAAM,IAAI,MAAM,uCAAuC;AAAA,IACxD;AAEA,uBAAK,eAAgB;AAErB,WAAO,OAAO;AAAA,MACb;AAAA,QACC,IAAI,mBAAK,UAAS;AAAA,QAClB,QAAQ;AAAA,QACR;AAAA,QACA,SAAS,mBAAK,UAAS;AAAA,MACxB;AAAA,MACA,mBAAK,UAAS;AAAA,IACf;AAAA,EACD;AAAA,EAEA,MAAM,SAA+B;AACpC,QAAI,SAAS;AACZ,WAAK,YAAY,OAAO;AAAA,IACzB;AACA,WAAO,MAAM;AAAA,EACd;AACD;AA7EC;AACA;AAFM,IAAM,2BAAN;", "names": []}