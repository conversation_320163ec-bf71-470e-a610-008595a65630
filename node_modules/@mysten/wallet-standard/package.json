{"name": "@mysten/wallet-standard", "version": "0.16.5", "description": "A suite of standard utilities for implementing wallets based on the Wallet Standard.", "license": "Apache-2.0", "author": "Mysten Labs <<EMAIL>>", "type": "commonjs", "main": "./dist/cjs/index.js", "module": "./dist/esm/index.js", "types": "./dist/cjs/index.d.ts", "exports": {".": {"import": "./dist/esm/index.js", "require": "./dist/cjs/index.js"}}, "sideEffects": false, "files": ["CHANGELOG.md", "dist", "src"], "dependencies": {"@wallet-standard/core": "1.1.1", "@mysten/sui": "1.36.0"}, "devDependencies": {"typescript": "^5.8.3", "typescript-json-schema": "^0.65.1", "@mysten/build-scripts": "0.0.0"}, "scripts": {"clean": "rm -rf tsconfig.tsbuildinfo ./dist", "build": "build-package", "prettier:check": "prettier -c --ignore-unknown .", "prettier:fix": "prettier -w --ignore-unknown .", "eslint:check": "eslint --max-warnings=0 .", "eslint:fix": "pnpm run eslint:check --fix", "lint": "pnpm run eslint:check && pnpm run prettier:check", "lint:fix": "pnpm run eslint:fix && pnpm run prettier:fix", "json-schema": "typescript-json-schema src/types.ts SerializedTransactionDataV2 --required --strictNullChecks > serialized-transaction-data-v2.schema.json && prettier -w serialized-transaction-data-v2.schema.json"}}