# @mysten/wallet-standard

## 0.16.5

### Patch Changes

- Updated dependencies [783bb9e]
- Updated dependencies [783bb9e]
- Updated dependencies [5cbbb21]
  - @mysten/sui@1.36.0

## 0.16.4

### Patch Changes

- Updated dependencies [888afe6]
  - @mysten/sui@1.35.0

## 0.16.3

### Patch Changes

- Updated dependencies [3fb7a83]
  - @mysten/sui@1.34.0

## 0.16.2

### Patch Changes

- Updated dependencies [a00522b]
  - @mysten/sui@1.33.0

## 0.16.1

### Patch Changes

- Updated dependencies [6b7deb8]
  - @mysten/sui@1.32.0

## 0.16.0

### Minor Changes

- 550e2e3: Added option digest to TransactionDataV2 for transaction data that has been fully
  resolved

### Patch Changes

- Updated dependencies [1ff4e57]
- Updated dependencies [550e2e3]
- Updated dependencies [550e2e3]
  - @mysten/sui@1.31.0

## 0.15.6

### Patch Changes

- Updated dependencies [5bd6ca3]
  - @mysten/sui@1.30.5

## 0.15.5

### Patch Changes

- Updated dependencies [5dce590]
- Updated dependencies [4a5aef6]
  - @mysten/sui@1.30.4

## 0.15.4

### Patch Changes

- bb7c03a: Update dependencies
- Updated dependencies [4457f10]
- Updated dependencies [bb7c03a]
  - @mysten/sui@1.30.3

## 0.15.3

### Patch Changes

- Updated dependencies [b265f7e]
  - @mysten/sui@1.30.2

## 0.15.2

### Patch Changes

- Updated dependencies [ec519fc]
  - @mysten/sui@1.30.1

## 0.15.1

### Patch Changes

- Updated dependencies [2456052]
- Updated dependencies [5264038]
- Updated dependencies [2456052]
- Updated dependencies [2456052]
- Updated dependencies [2456052]
- Updated dependencies [2456052]
  - @mysten/sui@1.30.0

## 0.15.0

### Minor Changes

- afbbb80: Expose variables for feature names

## 0.14.9

### Patch Changes

- @mysten/sui@1.29.1

## 0.14.8

### Patch Changes

- Updated dependencies [7d66a32]
- Updated dependencies [eb91fba]
- Updated dependencies [19a8045]
  - @mysten/sui@1.29.0

## 0.14.7

### Patch Changes

- Updated dependencies [9a94aea]
  - @mysten/sui@1.28.2

## 0.14.6

### Patch Changes

- Updated dependencies [3cd4e53]
  - @mysten/sui@1.28.1

## 0.14.5

### Patch Changes

- Updated dependencies [2705dc8]
  - @mysten/sui@1.28.0

## 0.14.4

### Patch Changes

- Updated dependencies [5cea435]
  - @mysten/sui@1.27.1

## 0.14.3

### Patch Changes

- Updated dependencies [4d13ef8]
- Updated dependencies [4d13ef8]
  - @mysten/sui@1.27.0

## 0.14.2

### Patch Changes

- 7ba32a4: update dependencies
- Updated dependencies [7ba32a4]
  - @mysten/sui@1.26.1

## 0.14.1

### Patch Changes

- Updated dependencies [906dd14]
  - @mysten/sui@1.26.0

## 0.14.0

### Minor Changes

- 68a9ecd: Add an optional `chain` parameter to the `signPersonalMessage` feature

### Patch Changes

- Updated dependencies [e8b5d04]
  - @mysten/sui@1.25.0

## 0.13.29

### Patch Changes

- Updated dependencies [cf3d12d]
  - @mysten/sui@1.24.0

## 0.13.28

### Patch Changes

- Updated dependencies [8baac61]
- Updated dependencies [8baac61]
  - @mysten/sui@1.23.0

## 0.13.27

### Patch Changes

- Updated dependencies [03975f4]
  - @mysten/sui@1.22.0

## 0.13.26

### Patch Changes

- @mysten/sui@1.21.2

## 0.13.25

### Patch Changes

- @mysten/sui@1.21.1

## 0.13.24

### Patch Changes

- Updated dependencies [3d8a0d9]
- Updated dependencies [20a5aaa]
  - @mysten/sui@1.21.0

## 0.13.23

### Patch Changes

- Updated dependencies [827a200]
  - @mysten/sui@1.20.0

## 0.13.22

### Patch Changes

- Updated dependencies [c39f32f]
- Updated dependencies [539168a]
  - @mysten/sui@1.19.0

## 0.13.21

### Patch Changes

- 7abd243: Update repo links
- Updated dependencies [7abd243]
  - @mysten/sui@1.18.1

## 0.13.20

### Patch Changes

- Updated dependencies [4f012b9]
- Updated dependencies [85bd9e4]
- Updated dependencies [5e3709d]
- Updated dependencies [b2928a9]
- Updated dependencies [dc0e21e]
- Updated dependencies [85bd9e4]
- Updated dependencies [a872b97]
  - @mysten/sui@1.18.0

## 0.13.19

### Patch Changes

- Updated dependencies [20af12d]
  - @mysten/sui@1.17.0

## 0.13.18

### Patch Changes

- Updated dependencies [100207f]
  - @mysten/sui@1.16.2

## 0.13.17

### Patch Changes

- @mysten/sui@1.16.1

## 0.13.16

### Patch Changes

- Updated dependencies [ec2dc7f]
- Updated dependencies [ec2dc7f]
  - @mysten/sui@1.16.0

## 0.13.15

### Patch Changes

- @mysten/sui@1.15.1

## 0.13.14

### Patch Changes

- Updated dependencies [6460e45]
  - @mysten/sui@1.15.0

## 0.13.13

### Patch Changes

- Updated dependencies [938fb6e]
  - @mysten/sui@1.14.4

## 0.13.12

### Patch Changes

- Updated dependencies [d5a23d7]
  - @mysten/sui@1.14.3

## 0.13.11

### Patch Changes

- Updated dependencies [e7bc63e]
  - @mysten/sui@1.14.2

## 0.13.10

### Patch Changes

- Updated dependencies [69ef100]
  - @mysten/sui@1.14.1

## 0.13.9

### Patch Changes

- Updated dependencies [c24814b]
  - @mysten/sui@1.14.0

## 0.13.8

### Patch Changes

- Updated dependencies [477d2a4]
  - @mysten/sui@1.13.0

## 0.13.7

### Patch Changes

- Updated dependencies [5436a90]
- Updated dependencies [5436a90]
  - @mysten/sui@1.12.0

## 0.13.6

### Patch Changes

- Updated dependencies [489f421]
- Updated dependencies [489f421]
  - @mysten/sui@1.11.0

## 0.13.5

### Patch Changes

- Updated dependencies [830b8d8]
  - @mysten/sui@1.10.0

## 0.13.4

### Patch Changes

- Updated dependencies [2c96b06]
- Updated dependencies [1fd22cc]
  - @mysten/sui@1.9.0

## 0.13.3

### Patch Changes

- Updated dependencies [569511a]
  - @mysten/sui@1.8.0

## 0.13.2

### Patch Changes

- Updated dependencies [143cd9d]
- Updated dependencies [4357ac6]
- Updated dependencies [4019dd7]
- Updated dependencies [4019dd7]
- Updated dependencies [00a974d]
  - @mysten/sui@1.7.0

## 0.13.1

### Patch Changes

- Updated dependencies [a3e32fe]
  - @mysten/sui@1.6.0

## 0.13.0

### Minor Changes

- 0851b31: Deprecate requestType option when executing transactions

### Patch Changes

- Updated dependencies [0851b31]
- Updated dependencies [f37b3c2]
  - @mysten/sui@1.5.0

## 0.12.14

### Patch Changes

- Updated dependencies [4419234]
  - @mysten/sui@1.4.0

## 0.12.13

### Patch Changes

- Updated dependencies [a45f461]
  - @mysten/sui@1.3.1

## 0.12.12

### Patch Changes

- 0f27a97: Update dependencies
- Updated dependencies [7fc464a]
- Updated dependencies [086b2bc]
- Updated dependencies [0fb0628]
- Updated dependencies [cdedf69]
- Updated dependencies [0f27a97]
- Updated dependencies [beed646]
  - @mysten/sui@1.3.0

## 0.12.11

### Patch Changes

- Updated dependencies [06a900c1ab]
- Updated dependencies [45877014d1]
- Updated dependencies [87d6f75403]
  - @mysten/sui@1.2.1

## 0.12.10

### Patch Changes

- Updated dependencies [fef99d377f]
  - @mysten/sui@1.2.0

## 0.12.9

### Patch Changes

- 805ff4d4c2: Fix bug where transaction was passed as both transaction and transactionBlock to
  sui:signTransactionBlock

## 0.12.8

### Patch Changes

- Updated dependencies [0dfff33b95]
  - @mysten/sui@1.1.2

## 0.12.7

### Patch Changes

- Updated dependencies [101f1ff4b8]
  - @mysten/sui@1.1.1

## 0.12.6

### Patch Changes

- Updated dependencies [bae8f9683c]
  - @mysten/sui@1.1.0

## 0.12.5

### Patch Changes

- Updated dependencies [369b924343]
  - @mysten/sui@1.0.5

## 0.12.4

### Patch Changes

- Updated dependencies [f1e828f557]
  - @mysten/sui@1.0.4

## 0.12.3

### Patch Changes

- Updated dependencies [1f20580841]
  - @mysten/sui@1.0.3

## 0.12.2

### Patch Changes

- Updated dependencies [f0a839f874]
  - @mysten/sui@1.0.2

## 0.12.1

### Patch Changes

- Updated dependencies [6fc6235984]
  - @mysten/sui@1.0.1

## 0.12.0

### Minor Changes

- a92b03de42: The Typescript SDK has been renamed to `@mysten/sui` and includes many new features
  and breaking changes. See the
  [full migration guide](https://sdk.mystenlabs.com/typescript/migrations/sui-1.0) for details on
  how to upgrade.

### Patch Changes

- Updated dependencies [ebdfe7cf21]
- Updated dependencies [a92b03de42]
  - @mysten/sui@1.0.0

## 0.11.6

### Patch Changes

- Updated dependencies [99b112178c]
  - @mysten/sui.js@0.54.1

## 0.11.5

### Patch Changes

- Updated dependencies [b7f673dbd9]
- Updated dependencies [123b42c75c]
  - @mysten/sui.js@0.54.0

## 0.11.4

### Patch Changes

- Updated dependencies [774bfb41a8]
  - @mysten/sui.js@0.53.0

## 0.11.3

### Patch Changes

- Updated dependencies [929db4976a]
  - @mysten/sui.js@0.52.0

## 0.11.2

### Patch Changes

- Updated dependencies [b4ecdb5860]
  - @mysten/sui.js@0.51.2

## 0.11.1

### Patch Changes

- Updated dependencies [6984dd1e38]
  - @mysten/sui.js@0.51.1

## 0.11.0

### Minor Changes

- 437f0ca2ef: Add isSuiChain utility which is useful for type-safe dApp interfaces in wallets

### Patch Changes

- Updated dependencies [0cafa94027]
  - @mysten/sui.js@0.51.0

## 0.10.3

### Patch Changes

- 4830361fa4: Updated typescript version
- Updated dependencies [4830361fa4]
  - @mysten/sui.js@0.50.1

## 0.10.2

### Patch Changes

- Updated dependencies [a34f1cb67d]
- Updated dependencies [c08e3569ef]
- Updated dependencies [9a14e61db4]
- Updated dependencies [13e922d9b1]
- Updated dependencies [a34f1cb67d]
- Updated dependencies [220a766d86]
  - @mysten/sui.js@0.50.0

## 0.10.1

### Patch Changes

- 9ac0a4ec01: Add extensions to all sdk import paths
- Updated dependencies [9ac0a4ec01]
  - @mysten/sui.js@0.49.1

## 0.10.0

### Minor Changes

- e5f9e3ba21: Replace tsup based build to fix issues with esm/cjs dual publishing

### Patch Changes

- Updated dependencies [e5f9e3ba21]
  - @mysten/sui.js@0.49.0

## 0.9.0

### Minor Changes

- 165ad6b21d: Introduce new optional `id` property, which wallets can specify as a unique
  identifier, separate from the wallet name.

### Patch Changes

- dd362ec1d6: Update docs url to sdk.mystenlabs.com
- Updated dependencies [dd362ec1d6]
  - @mysten/sui.js@0.48.1

## 0.8.11

### Patch Changes

- Updated dependencies [cdcfa76c43]
  - @mysten/sui.js@0.48.0

## 0.8.10

### Patch Changes

- Updated dependencies [194c980cb]
- Updated dependencies [9ac7e2f3d]
- Updated dependencies [0259aec82]
- Updated dependencies [64d45ba27]
  - @mysten/sui.js@0.47.0

## 0.8.9

### Patch Changes

- Updated dependencies [652bcdd92]
  - @mysten/sui.js@0.46.1

## 0.8.8

### Patch Changes

- Updated dependencies [28c2c3330]
- Updated dependencies [43444c58f]
- Updated dependencies [8d1e74e52]
- Updated dependencies [093554a0d]
- Updated dependencies [3718a230b]
  - @mysten/sui.js@0.46.0

## 0.8.7

### Patch Changes

- Updated dependencies [30b47b758]
  - @mysten/sui.js@0.45.1

## 0.8.6

### Patch Changes

- Updated dependencies [b9afb5567]
  - @mysten/sui.js@0.45.0

## 0.8.5

### Patch Changes

- b48289346: Mark packages as being side-effect free.
- Updated dependencies [b48289346]
- Updated dependencies [11cf4e68b]
  - @mysten/sui.js@0.44.0

## 0.8.4

### Patch Changes

- Updated dependencies [004fb1991]
  - @mysten/sui.js@0.43.3

## 0.8.3

### Patch Changes

- Updated dependencies [9b052166d]
  - @mysten/sui.js@0.43.2

## 0.8.2

### Patch Changes

- Updated dependencies [faa13ded9]
- Updated dependencies [c5684bb52]
  - @mysten/sui.js@0.43.1

## 0.8.1

### Patch Changes

- Updated dependencies [781d073d9]
- Updated dependencies [3764c464f]
- Updated dependencies [e4484852b]
- Updated dependencies [71e0a3197]
- Updated dependencies [1bc430161]
  - @mysten/sui.js@0.43.0

## 0.8.0

### Minor Changes

- fd8589806: Remove uses of deprecated imports from @mysten/sui.js
- 8b9e5f737: Added new isWalletWithRequiredFeatureSet utility and accompanying type

### Patch Changes

- Updated dependencies [fd8589806]
  - @mysten/sui.js@0.42.0

## 0.7.2

### Patch Changes

- @mysten/sui.js@0.41.2

## 0.7.1

### Patch Changes

- 189e02aba: Fix broken documentation link for the wallet-standard SDK
- Updated dependencies [24c21e1f0]
  - @mysten/sui.js@0.41.1

## 0.7.0

### Minor Changes

- 85f785c97: Rebuild wallet kit and wallet standard to no longer use wallet adapters.

### Patch Changes

- Updated dependencies [ba8e3b857]
- Updated dependencies [f4b7b3474]
  - @mysten/sui.js@0.41.0

## 0.6.0

### Minor Changes

- 8281e3d25: Deprecate `signMessage` method, and introduce the new `signPersonalMessage` method.

### Patch Changes

- Updated dependencies [a503cad34]
- Updated dependencies [8281e3d25]
  - @mysten/sui.js@0.40.0

## 0.5.14

### Patch Changes

- Updated dependencies [47ea5ec7c]
  - @mysten/sui.js@0.39.0

## 0.5.13

### Patch Changes

- Updated dependencies [ad46f9f2f]
- Updated dependencies [67e581a5a]
- Updated dependencies [34242be56]
- Updated dependencies [4e2a150a1]
- Updated dependencies [cce6ffbcc]
- Updated dependencies [0f06d593a]
- Updated dependencies [83d0fb734]
- Updated dependencies [09f4ed3fc]
- Updated dependencies [6d41059c7]
- Updated dependencies [cc6441f46]
- Updated dependencies [001148443]
  - @mysten/sui.js@0.38.0

## 0.5.12

### Patch Changes

- Updated dependencies [34cc7d610]
  - @mysten/sui.js@0.37.1

## 0.5.11

### Patch Changes

- Updated dependencies [36f2edff3]
- Updated dependencies [75d1a190d]
- Updated dependencies [93794f9f2]
- Updated dependencies [c3a4ec57c]
- Updated dependencies [a17d3678a]
- Updated dependencies [2f37537d5]
- Updated dependencies [00484bcc3]
  - @mysten/sui.js@0.37.0

## 0.5.10

### Patch Changes

- Updated dependencies [3ea9adb71a]
- Updated dependencies [1cfb1c9da3]
- Updated dependencies [1cfb1c9da3]
- Updated dependencies [fb3bb9118a]
  - @mysten/sui.js@0.36.0

## 0.5.9

### Patch Changes

- Updated dependencies [09d77325a9]
  - @mysten/sui.js@0.35.1

## 0.5.8

### Patch Changes

- Updated dependencies [4ea96d909a]
- Updated dependencies [bcbb178c44]
- Updated dependencies [470c27af50]
- Updated dependencies [03828224c9]
- Updated dependencies [671faefe3c]
- Updated dependencies [9ce7e051b4]
- Updated dependencies [9ce7e051b4]
- Updated dependencies [bb50698551]
  - @mysten/sui.js@0.35.0

## 0.5.7

### Patch Changes

- Updated dependencies [85719ac933]
- Updated dependencies [c3d9cc87f3]
- Updated dependencies [02a6063f82]
  - @mysten/sui.js@0.34.1

## 0.5.6

### Patch Changes

- Updated dependencies [280821e0ab]
- Updated dependencies [6a9abe9e38]
  - @mysten/sui.js@0.34.0

## 0.5.5

### Patch Changes

- 44e76bbd2: Expose mainnet chain.
- Updated dependencies [7915de531]
- Updated dependencies [6f9fc94ca]
- Updated dependencies [605eac8c6]
- Updated dependencies [262e3dfdd]
- Updated dependencies [91c63e4f8]
- Updated dependencies [e61ed2bac]
- Updated dependencies [5053a8dc8]
  - @mysten/sui.js@0.33.0

## 0.5.4

### Patch Changes

- b4f0bfc76: Fix type definitions for package exports.
- Updated dependencies [4ae3cbea3]
- Updated dependencies [d2755a496]
- Updated dependencies [f612dac98]
- Updated dependencies [c219e7470]
- Updated dependencies [59ae0e7d6]
- Updated dependencies [c219e7470]
- Updated dependencies [4e463c691]
- Updated dependencies [b4f0bfc76]
  - @mysten/sui.js@0.32.2

## 0.5.3

### Patch Changes

- Updated dependencies [3224ffcd0]
  - @mysten/sui.js@0.32.1

## 0.5.2

### Patch Changes

- Updated dependencies [9b42d0ada]
  - @mysten/sui.js@0.32.0

## 0.5.1

### Patch Changes

- Updated dependencies [976d3e1fe]
- Updated dependencies [0419b7c53]
- Updated dependencies [f3c096e3a]
- Updated dependencies [5a4e3e416]
- Updated dependencies [27dec39eb]
  - @mysten/sui.js@0.31.0

## 0.5.0

### Minor Changes

- 956ec28eb: Change `signMessage` to return message bytes. Add support for sui:signMessage in the
  wallet standard
- 19b567f21: Unified self- and delegated staking flows. Removed fields from `Validator`
  (`stake_amount`, `pending_stake`, and `pending_withdraw`) and renamed `delegation_staking_pool` to
  `staking_pool`. Additionally removed the `validator_stake` and `delegated_stake` fields in the
  `ValidatorSet` type and replaced them with a `total_stake` field.
- 5c3b00cde: Add object id to staking pool and pool id to staked sui.
- 3d9a04648: Adds `deactivation_epoch` to staking pool object, and adds `inactive_pools` to the
  validator set object.
- da72e73a9: Change the address of Move package for staking and validator related Move modules.
- 0672b5990: The Wallet Standard now only supports the `Transaction` type, instead of the previous
  `SignableTransaction` type.
- 0a7b42a6d: This changes almost all occurences of "delegate", "delegation" (and various
  capitalizations/forms) to their equivalent "stake"-based name. Function names, function argument
  names, RPC endpoints, Move functions, and object fields have been updated with this new naming
  convention.
- c718deef4: wallet-standard: changes sui:signAndExecuteTransaction and sui:signTransaction features
  to support account and chain options wallet-adapter-wallet-standard: change
  signAndExecuteTransaction and signTransaction signatures to support account and chain options
  wallet-adapter-wallet-standard: ensure version compatibility for of the wallet
  signAndExecuteTransaction and signTransaction features before using them (same major version)
  wallet-kit-core/wallet-kit: expose accounts as ReadonlyWalletAccount instead of only the address
  wallet-kit-core: signTransaction and signAndExecuteTransaction methods mirror the ones in standard
  adapter
- 68e60b02c: Changed where the options and requestType for signAndExecuteTransaction are.
- dbe73d5a4: Add an optional `contentOptions` field to `SuiSignAndExecuteTransactionOptions` to
  specify which fields to include in `SuiTransactionBlockResponse` (e.g., transaction, effects,
  events, etc). By default, only the transaction digest will be included.

### Patch Changes

- bf545c7d0: Add `features` prop to wallet kit that allows dapps to define which features they
  require to function properly.
- Updated dependencies [956ec28eb]
- Updated dependencies [4adfbff73]
- Updated dependencies [4c4573ebe]
- Updated dependencies [acc2edb31]
- Updated dependencies [941b03af1]
- Updated dependencies [a6690ac7d]
- Updated dependencies [a211dc03a]
- Updated dependencies [4c1e331b8]
- Updated dependencies [19b567f21]
- Updated dependencies [7659e2e91]
- Updated dependencies [0d3cb44d9]
- Updated dependencies [00bb9bb66]
- Updated dependencies [36c264ebb]
- Updated dependencies [891abf5ed]
- Updated dependencies [2e0ef59fa]
- Updated dependencies [33cb357e1]
- Updated dependencies [6bd88570c]
- Updated dependencies [f1e42f792]
- Updated dependencies [272389c20]
- Updated dependencies [3de8de361]
- Updated dependencies [be3c4f51e]
- Updated dependencies [dbe73d5a4]
- Updated dependencies [14ba89144]
- Updated dependencies [c82e4b454]
- Updated dependencies [7a2eaf4a3]
- Updated dependencies [2ef2bb59e]
- Updated dependencies [9b29bef37]
- Updated dependencies [8700809b5]
- Updated dependencies [5c3b00cde]
- Updated dependencies [01272ab7d]
- Updated dependencies [9822357d6]
- Updated dependencies [3d9a04648]
- Updated dependencies [da72e73a9]
- Updated dependencies [a0955c479]
- Updated dependencies [3eb3a1de8]
- Updated dependencies [0c9047698]
- Updated dependencies [4593333bd]
- Updated dependencies [d5ef1b6e5]
- Updated dependencies [0a7b42a6d]
- Updated dependencies [3de8de361]
- Updated dependencies [dd348cf03]
- Updated dependencies [57c17e02a]
- Updated dependencies [65f1372dd]
- Updated dependencies [a09239308]
- Updated dependencies [fe335e6ba]
- Updated dependencies [5dc25faad]
- Updated dependencies [64234baaf]
- Updated dependencies [79c2165cb]
- Updated dependencies [d3170ba41]
- Updated dependencies [a6ffb8088]
- Updated dependencies [3304eb83b]
- Updated dependencies [4189171ef]
- Updated dependencies [210840114]
- Updated dependencies [77bdf907f]
- Updated dependencies [a74df16ec]
- Updated dependencies [0f7aa6507]
- Updated dependencies [9b60bf700]
- Updated dependencies [64fb649eb]
- Updated dependencies [a6b0c4e5f]
  - @mysten/sui.js@0.30.0

## 0.4.3

### Patch Changes

- Updated dependencies [31bfcae6a]
  - @mysten/sui.js@0.29.1

## 0.4.2

### Patch Changes

- 0e202a543: Remove pending delegation switches.
- Updated dependencies [f1e3a0373]
- Updated dependencies [f2e713bd0]
- Updated dependencies [0e202a543]
- Updated dependencies [67e503c7c]
- Updated dependencies [4baf554f1]
- Updated dependencies [aa650aa3b]
- Updated dependencies [6ff0c785f]
  - @mysten/sui.js@0.29.0

## 0.4.1

### Patch Changes

- Updated dependencies [a67cc044b]
- Updated dependencies [24bdb66c6]
- Updated dependencies [a67cc044b]
- Updated dependencies [a67cc044b]
  - @mysten/sui.js@0.28.0

## 0.4.0

### Minor Changes

- 473005d8f: Add protocol_version to CheckpointSummary and SuiSystemObject. Consolidate end-of-epoch
  information in CheckpointSummary.

### Patch Changes

- Updated dependencies [473005d8f]
- Updated dependencies [fcba70206]
- Updated dependencies [59641dc29]
- Updated dependencies [ebe6c3945]
- Updated dependencies [629804d26]
- Updated dependencies [f51c85e85]
- Updated dependencies [e630f6832]
  - @mysten/sui.js@0.27.0

## 0.3.1

### Patch Changes

- Updated dependencies [97c46ca9d]
  - @mysten/sui.js@0.26.1

## 0.3.0

### Minor Changes

- 96e883fc1: Update wallet adapter and wallet standard to support passing through the desired
  request type.

### Patch Changes

- a8746d4e9: update SuiExecuteTransactionResponse
- Updated dependencies [034158656]
- Updated dependencies [a8746d4e9]
- Updated dependencies [57fc4dedd]
- Updated dependencies [e6a71882f]
- Updated dependencies [e6a71882f]
- Updated dependencies [21781ba52]
- Updated dependencies [b3ba6dfbc]
  - @mysten/sui.js@0.26.0

## 0.2.11

### Patch Changes

- Updated dependencies [ebfdd5c56]
- Updated dependencies [7b4bf43bc]
- Updated dependencies [72481e759]
- Updated dependencies [969a88669]
  - @mysten/sui.js@0.25.0

## 0.2.10

### Patch Changes

- Updated dependencies [01458ffd5]
- Updated dependencies [a274ecfc7]
- Updated dependencies [88a687834]
- Updated dependencies [89091ddab]
- Updated dependencies [71bee7563]
  - @mysten/sui.js@0.24.0

## 0.2.9

### Patch Changes

- Updated dependencies [f3444bdf2]
- Updated dependencies [e26f47cbf]
- Updated dependencies [b745cde24]
- Updated dependencies [01efa8bc6]
- Updated dependencies [35e0df780]
- Updated dependencies [5cd51dd38]
- Updated dependencies [8474242af]
- Updated dependencies [01efa8bc6]
- Updated dependencies [f74181212]
  - @mysten/sui.js@0.23.0

## 0.2.8

### Patch Changes

- Updated dependencies [a55236e48]
- Updated dependencies [8ae226dae]
  - @mysten/sui.js@0.22.0

## 0.2.7

### Patch Changes

- Updated dependencies [4fb12ac6d]
- Updated dependencies [bb14ffdc5]
- Updated dependencies [9fbe2714b]
- Updated dependencies [d2015f815]
- Updated dependencies [7d0f25b61]
  - @mysten/sui.js@0.21.0

## 0.2.6

### Patch Changes

- Updated dependencies [f93b59f3a]
- Updated dependencies [ea71d8216]
  - @mysten/sui.js@0.20.0

## 0.2.5

### Patch Changes

- Updated dependencies [b8257cecb]
- Updated dependencies [6c1f81228]
- Updated dependencies [519e11551]
- Updated dependencies [b03bfaec2]
- Updated dependencies [f9be28a42]
- Updated dependencies [24987df35]
  - @mysten/sui.js@0.19.0

## 0.2.4

### Patch Changes

- Updated dependencies [66021884e]
- Updated dependencies [7a67d61e2]
- Updated dependencies [45293b6ff]
- Updated dependencies [7a67d61e2]
- Updated dependencies [2a0b8e85d]
  - @mysten/sui.js@0.18.0

## 0.2.3

### Patch Changes

- Updated dependencies [623505886]
  - @mysten/sui.js@0.17.1

## 0.2.2

### Patch Changes

- Updated dependencies [a9602e533]
- Updated dependencies [db22728c1]
- Updated dependencies [3b510d0fc]
  - @mysten/sui.js@0.17.0

## 0.2.1

### Patch Changes

- Updated dependencies [01989d3d5]
- Updated dependencies [5e20e6569]
  - @mysten/sui.js@0.16.0

## 0.2.0

### Minor Changes

- e97d280d7: Update to 1.0 release of wallet standard
- 56de8448f: Update wallet standard adapters to use new wallet registration logic.

### Patch Changes

- Updated dependencies [c27933292]
- Updated dependencies [90898d366]
- Updated dependencies [c27933292]
- Updated dependencies [c27933292]
  - @mysten/sui.js@0.15.0

## 0.1.2

### Patch Changes

- Updated dependencies [e86f8bc5e]
- Updated dependencies [b4a8ee9bf]
- Updated dependencies [ef3571dc8]
- Updated dependencies [cccfe9315]
- Updated dependencies [8b4bea5e2]
- Updated dependencies [e45b188a8]
- Updated dependencies [2dc594ef7]
- Updated dependencies [4f0c611ff]
  - @mysten/sui.js@0.14.0

## 0.1.1

### Patch Changes

- Updated dependencies [1d036d459]
- Updated dependencies [b11b69262]
- Updated dependencies [b11b69262]
- Updated dependencies [b11b69262]
  - @mysten/sui.js@0.13.0

## 0.1.0

### Minor Changes

- 5ac98bc9a: Introduce new wallet adapter based on the Wallet Standard. This wallet adapter
  automatically detects wallets that adhere to the standard interface.
- 5ac98bc9a: Introduce new "wallet-standard" package which can be used to build wallets that are
  compatible with the Wallet Standard.

### Patch Changes

- Updated dependencies [e0b173b9e]
- Updated dependencies [059ede517]
- Updated dependencies [03e6b552b]
- Updated dependencies [4575c0a02]
- Updated dependencies [e0b173b9e]
- Updated dependencies [ccf7f148d]
  - @mysten/sui.js@0.12.0
