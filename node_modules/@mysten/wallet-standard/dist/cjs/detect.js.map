{"version": 3, "sources": ["../../src/detect.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport { StandardConnect, StandardEvents } from '@wallet-standard/core';\nimport type { Wallet, WalletWithFeatures } from '@wallet-standard/core';\n\nimport type { MinimallyRequiredFeatures } from './features/index.js';\n\n// These features are absolutely required for wallets to function in the Sui ecosystem.\n// Eventually, as wallets have more consistent support of features, we may want to extend this list.\nconst REQUIRED_FEATURES: (keyof MinimallyRequiredFeatures)[] = [StandardConnect, StandardEvents];\n\nexport function isWalletWithRequiredFeatureSet<AdditionalFeatures extends Wallet['features']>(\n\twallet: Wallet,\n\tadditionalFeatures: (keyof AdditionalFeatures)[] = [],\n): wallet is WalletWithFeatures<MinimallyRequiredFeatures & AdditionalFeatures> {\n\treturn [...REQUIRED_FEATURES, ...additionalFeatures].every(\n\t\t(feature) => feature in wallet.features,\n\t);\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAGA,kBAAgD;AAOhD,MAAM,oBAAyD,CAAC,6BAAiB,0BAAc;AAExF,SAAS,+BACf,QACA,qBAAmD,CAAC,GAC2B;AAC/E,SAAO,CAAC,GAAG,mBAAmB,GAAG,kBAAkB,EAAE;AAAA,IACpD,CAAC,YAAY,WAAW,OAAO;AAAA,EAChC;AACD;", "names": []}