{"version": 3, "sources": ["../../../src/features/suiSignPersonalMessage.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { IdentifierString, WalletAccount } from '@wallet-standard/core';\n\n/** Name of the feature. */\nexport const SuiSignPersonalMessage = 'sui:signPersonalMessage';\n\n/** The latest API version of the signPersonalMessage API. */\nexport type SuiSignPersonalMessageVersion = '1.1.0';\n\n/**\n * A Wallet Standard feature for signing a personal message, and returning the\n * message bytes that were signed, and message signature.\n */\nexport type SuiSignPersonalMessageFeature = {\n\t/** Namespace for the feature. */\n\t[SuiSignPersonalMessage]: {\n\t\t/** Version of the feature API. */\n\t\tversion: SuiSignPersonalMessageVersion;\n\t\tsignPersonalMessage: SuiSignPersonalMessageMethod;\n\t};\n};\n\nexport type SuiSignPersonalMessageMethod = (\n\tinput: SuiSignPersonalMessageInput,\n) => Promise<SuiSignPersonalMessageOutput>;\n\n/** Input for signing personal messages. */\nexport interface SuiSignPersonalMessageInput {\n\tmessage: Uint8Array;\n\taccount: WalletAccount;\n\tchain?: IdentifierString;\n}\n\n/** Output of signing personal messages. */\nexport interface SuiSignPersonalMessageOutput extends SignedPersonalMessage {}\n\nexport interface SignedPersonalMessage {\n\t/** Base64 encoded message bytes */\n\tbytes: string;\n\t/** Base64 encoded signature */\n\tsignature: string;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAMO,MAAM,yBAAyB;", "names": []}