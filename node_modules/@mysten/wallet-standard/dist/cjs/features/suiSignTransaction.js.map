{"version": 3, "sources": ["../../../src/features/suiSignTransaction.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { IdentifierString, WalletAccount } from '@wallet-standard/core';\n\n/** Name of the feature. */\nexport const SuiSignTransaction = 'sui:signTransaction';\n\n/** The latest API version of the signTransaction API. */\nexport type SuiSignTransactionVersion = '2.0.0';\n\n/**\n * A Wallet Standard feature for signing a transaction, and returning the\n * serialized transaction and transaction signature.\n */\nexport type SuiSignTransactionFeature = {\n\t/** Namespace for the feature. */\n\t[SuiSignTransaction]: {\n\t\t/** Version of the feature API. */\n\t\tversion: SuiSignTransactionVersion;\n\t\tsignTransaction: SuiSignTransactionMethod;\n\t};\n};\n\nexport type SuiSignTransactionMethod = (\n\tinput: SuiSignTransactionInput,\n) => Promise<SignedTransaction>;\n\n/** Input for signing transactions. */\nexport interface SuiSignTransactionInput {\n\ttransaction: { toJSON: () => Promise<string> };\n\taccount: WalletAccount;\n\tchain: IdentifierString;\n\tsignal?: AbortSignal;\n}\n\n/** Output of signing transactions. */\n\nexport interface SignedTransaction {\n\t/** Transaction as base64 encoded bcs. */\n\tbytes: string;\n\t/** Base64 encoded signature */\n\tsignature: string;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAMO,MAAM,qBAAqB;", "names": []}