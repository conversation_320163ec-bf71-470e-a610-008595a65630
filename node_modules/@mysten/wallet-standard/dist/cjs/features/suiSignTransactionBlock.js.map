{"version": 3, "sources": ["../../../src/features/suiSignTransactionBlock.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { Transaction } from '@mysten/sui/transactions';\nimport type { IdentifierString, WalletAccount } from '@wallet-standard/core';\n\n/** Name of the feature. */\nexport const SuiSignTransactionBlock = 'sui:signTransactionBlock';\n\n/** The latest API version of the signTransactionBlock API. */\nexport type SuiSignTransactionBlockVersion = '1.0.0';\n\n/**\n * @deprecated Use `sui:signTransaction` instead.\n *\n * A Wallet Standard feature for signing a transaction, and returning the\n * serialized transaction and transaction signature.\n */\nexport type SuiSignTransactionBlockFeature = {\n\t/** Namespace for the feature. */\n\t[SuiSignTransactionBlock]: {\n\t\t/** Version of the feature API. */\n\t\tversion: SuiSignTransactionBlockVersion;\n\t\t/** @deprecated Use `sui:signTransaction` instead. */\n\t\tsignTransactionBlock: SuiSignTransactionBlockMethod;\n\t};\n};\n\n/** @deprecated Use `sui:signTransaction` instead. */\nexport type SuiSignTransactionBlockMethod = (\n\tinput: SuiSignTransactionBlockInput,\n) => Promise<SuiSignTransactionBlockOutput>;\n\n/** Input for signing transactions. */\nexport interface SuiSignTransactionBlockInput {\n\ttransactionBlock: Transaction;\n\taccount: WalletAccount;\n\tchain: IdentifierString;\n}\n\n/** Output of signing transactions. */\nexport interface SuiSignTransactionBlockOutput extends SignedTransactionBlock {}\n\nexport interface SignedTransactionBlock {\n\t/** Transaction as base64 encoded bcs. */\n\ttransactionBlockBytes: string;\n\t/** Base64 encoded signature */\n\tsignature: string;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAOO,MAAM,0BAA0B;", "names": []}