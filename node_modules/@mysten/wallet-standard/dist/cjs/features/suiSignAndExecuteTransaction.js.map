{"version": 3, "sources": ["../../../src/features/suiSignAndExecuteTransaction.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { SignedTransaction, SuiSignTransactionInput } from './suiSignTransaction.js';\n\n/** Name of the feature. */\nexport const SuiSignAndExecuteTransaction = 'sui:signAndExecuteTransaction';\n\n/** The latest API version of the signAndExecuteTransactionBlock API. */\nexport type SuiSignAndExecuteTransactionVersion = '2.0.0';\n\n/**\n * A Wallet Standard feature for signing a transaction, and submitting it to the\n * network. The wallet is expected to submit the transaction to the network via RPC,\n * and return the transaction response.\n */\nexport type SuiSignAndExecuteTransactionFeature = {\n\t/** Namespace for the feature. */\n\t[SuiSignAndExecuteTransaction]: {\n\t\t/** Version of the feature API. */\n\t\tversion: SuiSignAndExecuteTransactionVersion;\n\t\tsignAndExecuteTransaction: SuiSignAndExecuteTransactionMethod;\n\t};\n};\n\nexport type SuiSignAndExecuteTransactionMethod = (\n\tinput: SuiSignAndExecuteTransactionInput,\n) => Promise<SuiSignAndExecuteTransactionOutput>;\n\n/** Input for signing and sending transactions. */\nexport interface SuiSignAndExecuteTransactionInput extends SuiSignTransactionInput {}\n\n/** Output of signing and sending transactions. */\nexport interface SuiSignAndExecuteTransactionOutput extends SignedTransaction {\n\tdigest: string;\n\t/** Transaction effects as base64 encoded bcs. */\n\teffects: string;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAMO,MAAM,+BAA+B;", "names": []}