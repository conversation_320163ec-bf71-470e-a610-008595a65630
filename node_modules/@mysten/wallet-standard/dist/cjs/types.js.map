{"version": 3, "sources": ["../../src/types.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\n/** Contains data related to the gas payment for a Transaction */\nexport interface GasData {\n\t/** The budget set for this transaction */\n\tbudget: string | number | null;\n\t/** The gas price used for this transaction */\n\tprice: string | number | null;\n\t/** The owner of the gas coins used to fund the transactions, this is either the sender or the gas sponsor */\n\towner: string | null;\n\t/** The list of SUI coins to fund the transaction */\n\tpayment: { objectId: string; version: string; digest: string }[] | null;\n}\n\n/**\n * Represent the serialized state of a partially built Transaction\n * This format is designed to support transactions that have not been fully build\n * allowing most properties to be omitted or set to null.  It also supports\n * unresolved object references, unresolved pure values, and Transaction Intents.\n */\nexport interface SerializedTransactionDataV2 {\n\tversion: 2;\n\t/** The sender of the transaction */\n\tsender: string | null | undefined;\n\t/** The expiration of the transaction */\n\texpiration: { Epoch: number } | { None: true } | null | undefined;\n\t/** The gas data */\n\tgasData: GasData;\n\t/** The inputs to the transaction */\n\tinputs: CallArg[];\n\t/** The commands to execute */\n\tcommands: Command[];\n\t/** Extra metadata for implementation specific use-cases */\n\textensions?: { [key: string]: unknown };\n\t/** The digest of the transaction, may be set when the transaction is fully resolved */\n\tdigest: string | null | undefined;\n}\n\n/**\n * Represents an input to a Transaction, either as a fully resolved Object or Pure input\n * or as an unresolved partial reference which needs to be resolved before the transaction\n * can be serialized to bcs and executed.\n */\nexport type CallArg =\n\t| {\n\t\t\tObject: ObjectArg;\n\t  }\n\t| {\n\t\t\tPure: PureArg;\n\t  }\n\t| {\n\t\t\tUnresolvedPure: UnresolvedPureArg;\n\t  }\n\t| {\n\t\t\tUnresolvedObject: UnresolvedObjectArg;\n\t  };\n\nexport type ObjectArg =\n\t| {\n\t\t\tImmOrOwnedObject: {\n\t\t\t\tobjectId: string;\n\t\t\t\tversion: string | number;\n\t\t\t\tdigest: string;\n\t\t\t};\n\t  }\n\t| {\n\t\t\tSharedObject: {\n\t\t\t\tobjectId: string;\n\t\t\t\tinitialSharedVersion: string;\n\t\t\t\tmutable: boolean;\n\t\t\t};\n\t  }\n\t| {\n\t\t\tReceiving: {\n\t\t\t\tobjectId: string;\n\t\t\t\tversion: string | number;\n\t\t\t\tdigest: string;\n\t\t\t};\n\t  };\n\nexport interface PureArg {\n\tbytes: string;\n}\n\n/**\n * Represents an un-serialized pure value.\n * The correct bcs schema will need to be determined before this value can be serialized to bcs */\nexport interface UnresolvedPureArg {\n\tvalue: unknown;\n}\n\n/**\n * Represents an unresolved object reference.  This allows objects to be referenced by only their ID.\n * version and digest details may also be added to unresolved object references.\n * To fully resolve a reference, the correct ObjectArg type needs to be determined based on the type of object,\n * and how it used in the transaction (eg, is it used mutably if it's shared, and is it a receiving object if it's not shared)\n */\nexport interface UnresolvedObjectArg {\n\tobjectId: string;\n\tversion?: string | null | undefined;\n\tdigest?: string | null | undefined;\n\tinitialSharedVersion?: string | null | undefined;\n}\n\nexport type Argument =\n\t| {\n\t\t\tGasCoin: true;\n\t  }\n\t| {\n\t\t\tInput: number;\n\t  }\n\t| {\n\t\t\tResult: number;\n\t  }\n\t| {\n\t\t\tNestedResult: [number, number];\n\t  };\n\nexport type Command =\n\t| {\n\t\t\tMoveCall: {\n\t\t\t\tpackage: string;\n\t\t\t\tmodule: string;\n\t\t\t\tfunction: string;\n\t\t\t\ttypeArguments: string[];\n\t\t\t\targuments: Argument[];\n\t\t\t};\n\t  }\n\t| {\n\t\t\tTransferObjects: {\n\t\t\t\tobjects: Argument[];\n\t\t\t\taddress: Argument;\n\t\t\t};\n\t  }\n\t| {\n\t\t\tSplitCoins: {\n\t\t\t\tcoin: Argument;\n\t\t\t\tamounts: Argument[];\n\t\t\t};\n\t  }\n\t| {\n\t\t\tMergeCoins: {\n\t\t\t\tdestination: Argument;\n\t\t\t\tsources: Argument[];\n\t\t\t};\n\t  }\n\t| {\n\t\t\tPublish: {\n\t\t\t\tmodules: string[];\n\t\t\t\tdependencies: string[];\n\t\t\t};\n\t  }\n\t| {\n\t\t\tMakeMoveVec: {\n\t\t\t\ttype: string | null;\n\t\t\t\telements: Argument[];\n\t\t\t};\n\t  }\n\t| {\n\t\t\tUpgrade: {\n\t\t\t\tmodules: string[];\n\t\t\t\tdependencies: string[];\n\t\t\t\tpackage: string;\n\t\t\t\tticket: Argument;\n\t\t\t};\n\t  }\n\t| {\n\t\t\t$Intent: {\n\t\t\t\tname: string;\n\t\t\t\tinputs: { [key: string]: Argument | Argument[] };\n\t\t\t\tdata: { [key: string]: unknown };\n\t\t\t};\n\t  };\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}