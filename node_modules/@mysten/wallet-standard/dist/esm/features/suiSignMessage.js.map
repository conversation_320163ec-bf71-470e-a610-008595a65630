{"version": 3, "sources": ["../../../src/features/suiSignMessage.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { WalletAccount } from '@wallet-standard/core';\n\n/**\n * Name of the feature.\n * @deprecated Wallets can still implement this method for compatibility, but this has been replaced by the `sui:signPersonalMessage` feature\n **/\nexport const SuiSignMessage = 'sui:signMessage';\n\n/**\n * The latest API version of the signMessage API.\n * @deprecated Wallets can still implement this method for compatibility, but this has been replaced by the `sui:signPersonalMessage` feature\n */\nexport type SuiSignMessageVersion = '1.0.0';\n\n/**\n * A Wallet Standard feature for signing a personal message, and returning the\n * message bytes that were signed, and message signature.\n *\n * @deprecated Wallets can still implement this method for compatibility, but this has been replaced by the `sui:signPersonalMessage` feature\n */\nexport type SuiSignMessageFeature = {\n\t/** Namespace for the feature. */\n\t[SuiSignMessage]: {\n\t\t/** Version of the feature API. */\n\t\tversion: SuiSignMessageVersion;\n\t\tsignMessage: SuiSignMessageMethod;\n\t};\n};\n\n/** @deprecated Wallets can still implement this method for compatibility, but this has been replaced by the `sui:signPersonalMessage` feature */\nexport type SuiSignMessageMethod = (input: SuiSignMessageInput) => Promise<SuiSignMessageOutput>;\n\n/**\n * Input for signing messages.\n * @deprecated Wallets can still implement this method for compatibility, but this has been replaced by the `sui:signPersonalMessage` feature\n */\nexport interface SuiSignMessageInput {\n\tmessage: Uint8Array;\n\taccount: WalletAccount;\n}\n\n/**\n * Output of signing messages.\n * @deprecated Wallets can still implement this method for compatibility, but this has been replaced by the `sui:signPersonalMessage` feature\n */\nexport interface SuiSignMessageOutput {\n\t/** Base64 message bytes. */\n\tmessageBytes: string;\n\t/** Base64 encoded signature */\n\tsignature: string;\n}\n"], "mappings": "AASO,MAAM,iBAAiB;", "names": []}