{"version": 3, "sources": ["../../../src/features/suiSignAndExecuteTransactionBlock.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type {\n\tExecuteTransactionRequestType,\n\tSuiTransactionBlockResponse,\n\tSuiTransactionBlockResponseOptions,\n} from '@mysten/sui/client';\n\nimport type { SuiSignTransactionBlockInput } from './suiSignTransactionBlock.js';\n\n/** Name of the feature. */\nexport const SuiSignAndExecuteTransactionBlock = 'sui:signAndExecuteTransactionBlock';\n\n/** The latest API version of the signAndExecuteTransactionBlock API. */\nexport type SuiSignAndExecuteTransactionBlockVersion = '1.0.0';\n\n/**\n * @deprecated Use `sui:signAndExecuteTransaction` instead.\n *\n * A Wallet Standard feature for signing a transaction, and submitting it to the\n * network. The wallet is expected to submit the transaction to the network via RPC,\n * and return the transaction response.\n */\nexport type SuiSignAndExecuteTransactionBlockFeature = {\n\t/** Namespace for the feature. */\n\t[SuiSignAndExecuteTransactionBlock]: {\n\t\t/** Version of the feature API. */\n\t\tversion: SuiSignAndExecuteTransactionBlockVersion;\n\t\t/** @deprecated Use `sui:signAndExecuteTransaction` instead. */\n\t\tsignAndExecuteTransactionBlock: SuiSignAndExecuteTransactionBlockMethod;\n\t};\n};\n\n/** @deprecated Use `sui:signAndExecuteTransaction` instead. */\nexport type SuiSignAndExecuteTransactionBlockMethod = (\n\tinput: SuiSignAndExecuteTransactionBlockInput,\n) => Promise<SuiSignAndExecuteTransactionBlockOutput>;\n\n/** Input for signing and sending transactions. */\nexport interface SuiSignAndExecuteTransactionBlockInput extends SuiSignTransactionBlockInput {\n\t/**\n\t * @deprecated requestType will be ignored by JSON RPC in the future\n\t */\n\trequestType?: ExecuteTransactionRequestType;\n\t/** specify which fields to return (e.g., transaction, effects, events, etc). By default, only the transaction digest will be returned. */\n\toptions?: SuiTransactionBlockResponseOptions;\n}\n\n/** Output of signing and sending transactions. */\nexport interface SuiSignAndExecuteTransactionBlockOutput extends SuiTransactionBlockResponse {}\n"], "mappings": "AAYO,MAAM,oCAAoC;", "names": []}