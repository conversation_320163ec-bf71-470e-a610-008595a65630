{"version": 3, "file": "interactions.d.ts", "sourceRoot": "", "sources": ["interactions.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACX,qBAAqB,EACrB,+BAA+B,EAC/B,qCAAqC,EACrC,sBAAsB,EACtB,kCAAkC,EAClC,sBAAsB,EACtB,MAAM,yBAAyB,CAAC;AACjC,OAAO,KAAK,EAAE,qDAAqD,EAAE,cAAc,EAAE,MAAM,uBAAuB,CAAC;AACnH,OAAO,KAAK,EACX,0CAA0C,EAC1C,uCAAuC,EACvC,+CAA+C,EAC/C,2CAA2C,EAC3C,yCAAyC,EACzC,qCAAqC,EACrC,MAAM,WAAW,CAAC;AAEnB;;;;GAIG;AACH,MAAM,MAAM,mCAAmC,GAAG,qBAAqB,EAAE,CAAC;AAE1E;;;;GAIG;AACH,MAAM,MAAM,kCAAkC,GAAG,qBAAqB,CAAC;AAEvE,KAAK,0CAA0C,GAAG,qDAAqD,CACtG,IAAI,CAAC,qBAAqB,EAAE,gBAAgB,GAAG,aAAa,GAAG,UAAU,GAAG,IAAI,GAAG,MAAM,GAAG,SAAS,CAAC,CACtG,CAAC;AAEF;;;;GAIG;AACH,MAAM,WAAW,+CAAgD,SAAQ,0CAA0C;IAClH,IAAI,CAAC,EAAE,sBAAsB,CAAC,SAAS,GAAG,SAAS,CAAC;IACpD,WAAW,EAAE,MAAM,CAAC;CACpB;AAED;;;;GAIG;AACH,MAAM,WAAW,iDAAkD,SAAQ,0CAA0C;IACpH,IAAI,EAAE,sBAAsB,CAAC,OAAO,GAAG,sBAAsB,CAAC,IAAI,CAAC;CACnE;AAED;;;;GAIG;AACH,MAAM,MAAM,sCAAsC,GAC/C,+CAA+C,GAC/C,iDAAiD,CAAC;AAErD;;;;GAIG;AACH,MAAM,MAAM,oCAAoC,GAAG,qBAAqB,CAAC;AAEzE;;;;GAIG;AACH,MAAM,MAAM,sCAAsC,GAAG,cAAc,CAAC,sCAAsC,CAAC,CAAC;AAE5G;;;;GAIG;AACH,MAAM,MAAM,oCAAoC,GAAG,qBAAqB,CAAC;AAEzE;;;;GAIG;AACH,MAAM,MAAM,qCAAqC,GAAG,sCAAsC,EAAE,CAAC;AAE7F;;;;GAIG;AACH,MAAM,MAAM,mCAAmC,GAAG,qBAAqB,EAAE,CAAC;AAE1E;;;;GAIG;AACH,MAAM,MAAM,wCAAwC,GAAG,qBAAqB,EAAE,CAAC;AAE/E;;;;GAIG;AACH,MAAM,MAAM,uCAAuC,GAAG,qBAAqB,CAAC;AAE5E;;;;GAIG;AACH,MAAM,MAAM,2CAA2C,GAAG,sCAAsC,CAAC;AAEjG;;;;GAIG;AACH,MAAM,MAAM,yCAAyC,GAAG,qBAAqB,CAAC;AAE9E;;;;GAIG;AACH,MAAM,MAAM,2CAA2C,GAAG,cAAc,CAAC,sCAAsC,CAAC,CAAC;AAEjH;;;;GAIG;AACH,MAAM,MAAM,yCAAyC,GAAG,qBAAqB,CAAC;AAE9E;;;;GAIG;AACH,MAAM,MAAM,0CAA0C,GAAG,sCAAsC,EAAE,CAAC;AAElG;;;;GAIG;AACH,MAAM,MAAM,wCAAwC,GAAG,qBAAqB,EAAE,CAAC;AAE/E;;;;GAIG;AACH,MAAM,MAAM,sCAAsC,GAAG,sBAAsB,CAAC;AAE5E;;;;GAIG;AACH,MAAM,MAAM,0CAA0C,GACnD,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG;IACvC;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CACjC,CAAC,GACF,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,sCAAsC,CAAC,CAAC;AAElF;;;;GAIG;AACH,MAAM,MAAM,2CAA2C,GAAG,uCAAuC,CAAC;AAElG;;;;GAIG;AACH,MAAM,MAAM,+CAA+C,GAAG,2CAA2C,CAAC;AAE1G;;;;GAIG;AACH,MAAM,MAAM,mDAAmD,GAAG,+CAA+C,CAAC;AAElH;;;;GAIG;AACH,MAAM,MAAM,6CAA6C,GAAG,yCAAyC,CAAC;AAEtG;;;;GAIG;AACH,MAAM,MAAM,8CAA8C,GAAG,0CAA0C,CAAC;AAExG;;;;GAIG;AACH,MAAM,MAAM,sCAAsC,GAAG,kCAAkC,CAAC;AAExF;;;;GAIG;AACH,MAAM,MAAM,0CAA0C,GACnD,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG;IACvC;;OAEG;IACH,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC;CACjC,CAAC,GACF,CAAC,MAAM,CAAC,SAAS,MAAM,GAAG,EAAE,OAAO,CAAC,GAAG,sCAAsC,CAAC,CAAC;AAElF;;;;GAIG;AACH,MAAM,MAAM,oCAAoC,GAAG,qCAAqC,CAAC;AAEzF;;;;GAIG;AACH,MAAM,MAAM,mCAAmC,GAAG,uCAAuC,CAAC;AAE1F;;;;GAIG;AACH,MAAM,MAAM,uCAAuC,GAAG,2CAA2C,CAAC;AAElG;;;;GAIG;AACH,MAAM,MAAM,2CAA2C,GAAG,+CAA+C,CAAC;AAE1G;;;;GAIG;AACH,MAAM,MAAM,qCAAqC,GAAG,yCAAyC,CAAC;AAE9F;;;;GAIG;AACH,MAAM,MAAM,sCAAsC,GAAG,0CAA0C,CAAC;AAEhG;;;;GAIG;AACH,MAAM,MAAM,mDAAmD,GAAG,qCAAqC,EAAE,CAAC;AAE1G;;;;GAIG;AACH,MAAM,MAAM,6CAA6C,GAAG,qCAAqC,CAAC;AAElG;;;;GAIG;AACH,MAAM,WAAW,+CAA+C;IAC/D,WAAW,EAAE,+BAA+B,EAAE,CAAC;CAC/C;AAED;;;;GAIG;AACH,MAAM,MAAM,6CAA6C,GAAG,qCAAqC,CAAC;AAElG;;;;GAIG;AACH,MAAM,MAAM,qDAAqD,GAAG,IAAI,CACvE,qCAAqC,EACrC,IAAI,GAAG,aAAa,CACpB,EAAE,CAAC;AAEJ;;;;GAIG;AACH,MAAM,MAAM,mDAAmD,GAAG,qCAAqC,EAAE,CAAC"}