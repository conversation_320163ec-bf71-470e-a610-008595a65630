{"name": "<PERSON>cusaurus", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids", "lint": "eslint ."}, "dependencies": {"@docusaurus/core": "2.0.1", "@docusaurus/preset-classic": "2.0.1", "@mdx-js/react": "^1.6.22", "clsx": "^1.2.1", "prism-react-renderer": "^1.3.5", "react": "^17.0.2", "react-dom": "^17.0.2"}, "devDependencies": {"@docusaurus/eslint-plugin": "^2.0.1", "@docusaurus/module-type-aliases": "2.0.1", "eslint": "^8.21.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.30.1", "prettier": "^2.7.1"}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=16.14"}}