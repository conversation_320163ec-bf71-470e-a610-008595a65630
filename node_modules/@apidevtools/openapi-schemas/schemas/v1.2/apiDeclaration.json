{"id": "https://raw.githubusercontent.com/OAI/OpenAPI-Specification/master/schemas/v1.2/apiDeclaration.json#", "$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "required": ["swaggerVersion", "basePath", "apis"], "properties": {"swaggerVersion": {"enum": ["1.2"]}, "apiVersion": {"type": "string"}, "basePath": {"type": "string", "format": "uri", "pattern": "^https?://"}, "resourcePath": {"type": "string", "format": "uri", "pattern": "^/"}, "apis": {"type": "array", "items": {"$ref": "#/definitions/apiObject"}}, "models": {"type": "object", "additionalProperties": {"$ref": "modelsObject.json#"}}, "produces": {"$ref": "#/definitions/mimeTypeArray"}, "consumes": {"$ref": "#/definitions/mimeTypeArray"}, "authorizations": {"$ref": "authorizationObject.json#"}}, "additionalProperties": false, "definitions": {"apiObject": {"type": "object", "required": ["path", "operations"], "properties": {"path": {"type": "string", "format": "uri-template", "pattern": "^/"}, "description": {"type": "string"}, "operations": {"type": "array", "items": {"$ref": "operationObject.json#"}}}, "additionalProperties": false}, "mimeTypeArray": {"type": "array", "items": {"type": "string", "format": "mime-type"}, "uniqueItems": true}}}