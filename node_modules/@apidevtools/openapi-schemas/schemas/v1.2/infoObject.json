{"id": "https://raw.githubusercontent.com/OAI/OpenAPI-Specification/master/schemas/v1.2/infoObject.json#", "$schema": "http://json-schema.org/draft-04/schema#", "description": "info object (section 5.1.3)", "type": "object", "required": ["title", "description"], "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "termsOfServiceUrl": {"type": "string", "format": "uri"}, "contact": {"type": "string", "format": "email"}, "license": {"type": "string"}, "licenseUrl": {"type": "string", "format": "uri"}}, "additionalProperties": false}