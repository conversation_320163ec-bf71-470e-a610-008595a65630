{"name": "jose", "version": "6.0.12", "description": "JWA, JWS, JWE, JWT, JWK, JWKS for Node.js, Browser, Cloudflare Workers, Deno, Bun, and other Web-interoperable runtimes", "keywords": ["browser", "bun", "cloudflare", "compact", "decode", "decrypt", "deno", "detached", "ec", "ecdsa", "ed25519", "eddsa", "edge", "electron", "embedded", "encrypt", "flattened", "general", "jose", "json web token", "jsonwebtoken", "jwa", "jwe", "jwk", "jwks", "jws", "jwt", "jwt-decode", "netlify", "next", "nextjs", "oct", "okp", "payload", "pem", "pkcs8", "rsa", "sign", "signature", "spki", "validate", "vercel", "verify", "webcrypto", "workerd", "workers", "x509"], "homepage": "https://github.com/panva/jose", "repository": "panva/jose", "funding": {"url": "https://github.com/sponsors/panva"}, "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {".": {"types": "./dist/types/index.d.ts", "default": "./dist/webapi/index.js"}, "./jwk/embedded": {"types": "./dist/types/jwk/embedded.d.ts", "default": "./dist/webapi/jwk/embedded.js"}, "./jwk/thumbprint": {"types": "./dist/types/jwk/thumbprint.d.ts", "default": "./dist/webapi/jwk/thumbprint.js"}, "./key/import": {"types": "./dist/types/key/import.d.ts", "default": "./dist/webapi/key/import.js"}, "./key/export": {"types": "./dist/types/key/export.d.ts", "default": "./dist/webapi/key/export.js"}, "./key/generate/keypair": {"types": "./dist/types/key/generate_key_pair.d.ts", "default": "./dist/webapi/key/generate_key_pair.js"}, "./key/generate/secret": {"types": "./dist/types/key/generate_secret.d.ts", "default": "./dist/webapi/key/generate_secret.js"}, "./jwks/remote": {"types": "./dist/types/jwks/remote.d.ts", "default": "./dist/webapi/jwks/remote.js"}, "./jwks/local": {"types": "./dist/types/jwks/local.d.ts", "default": "./dist/webapi/jwks/local.js"}, "./jwt/sign": {"types": "./dist/types/jwt/sign.d.ts", "default": "./dist/webapi/jwt/sign.js"}, "./jwt/verify": {"types": "./dist/types/jwt/verify.d.ts", "default": "./dist/webapi/jwt/verify.js"}, "./jwt/encrypt": {"types": "./dist/types/jwt/encrypt.d.ts", "default": "./dist/webapi/jwt/encrypt.js"}, "./jwt/decrypt": {"types": "./dist/types/jwt/decrypt.d.ts", "default": "./dist/webapi/jwt/decrypt.js"}, "./jwt/unsecured": {"types": "./dist/types/jwt/unsecured.d.ts", "default": "./dist/webapi/jwt/unsecured.js"}, "./jwt/decode": {"types": "./dist/types/util/decode_jwt.d.ts", "default": "./dist/webapi/util/decode_jwt.js"}, "./decode/protected_header": {"types": "./dist/types/util/decode_protected_header.d.ts", "default": "./dist/webapi/util/decode_protected_header.js"}, "./jws/compact/sign": {"types": "./dist/types/jws/compact/sign.d.ts", "default": "./dist/webapi/jws/compact/sign.js"}, "./jws/compact/verify": {"types": "./dist/types/jws/compact/verify.d.ts", "default": "./dist/webapi/jws/compact/verify.js"}, "./jws/flattened/sign": {"types": "./dist/types/jws/flattened/sign.d.ts", "default": "./dist/webapi/jws/flattened/sign.js"}, "./jws/flattened/verify": {"types": "./dist/types/jws/flattened/verify.d.ts", "default": "./dist/webapi/jws/flattened/verify.js"}, "./jws/general/sign": {"types": "./dist/types/jws/general/sign.d.ts", "default": "./dist/webapi/jws/general/sign.js"}, "./jws/general/verify": {"types": "./dist/types/jws/general/verify.d.ts", "default": "./dist/webapi/jws/general/verify.js"}, "./jwe/compact/encrypt": {"types": "./dist/types/jwe/compact/encrypt.d.ts", "default": "./dist/webapi/jwe/compact/encrypt.js"}, "./jwe/compact/decrypt": {"types": "./dist/types/jwe/compact/decrypt.d.ts", "default": "./dist/webapi/jwe/compact/decrypt.js"}, "./jwe/flattened/encrypt": {"types": "./dist/types/jwe/flattened/encrypt.d.ts", "default": "./dist/webapi/jwe/flattened/encrypt.js"}, "./jwe/flattened/decrypt": {"types": "./dist/types/jwe/flattened/decrypt.d.ts", "default": "./dist/webapi/jwe/flattened/decrypt.js"}, "./jwe/general/encrypt": {"types": "./dist/types/jwe/general/encrypt.d.ts", "default": "./dist/webapi/jwe/general/encrypt.js"}, "./jwe/general/decrypt": {"types": "./dist/types/jwe/general/decrypt.d.ts", "default": "./dist/webapi/jwe/general/decrypt.js"}, "./errors": {"types": "./dist/types/util/errors.d.ts", "default": "./dist/webapi/util/errors.js"}, "./base64url": {"types": "./dist/types/util/base64url.d.ts", "default": "./dist/webapi/util/base64url.js"}, "./package.json": "./package.json"}, "main": "./dist/webapi/index.js", "types": "./dist/types/index.d.ts", "files": ["dist/webapi/**/*.js", "dist/types/**/*.d.ts", "!dist/**/*.bundle.js", "!dist/**/*.umd.js", "!dist/**/*.min.js", "!dist/types/runtime/*", "!dist/types/lib/*", "!dist/deno/**/*"]}