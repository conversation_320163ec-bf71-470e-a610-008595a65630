{"version": 3, "file": "secp256k1.js", "sourceRoot": "", "sources": ["src/secp256k1.ts"], "names": [], "mappings": ";;;AAAA;;;;;;GAMG;AACH,sEAAsE;AACtE,mDAA+C;AAC/C,qDAAqD;AACrD,yDAAyE;AAEzE,kEAKqC;AACrC,sDAAyE;AACzE,8DAOmC;AAEnC,yCAOoB;AAEpB,oDAAoD;AACpD,0DAA0D;AAC1D,iEAAiE;AACjE,MAAM,eAAe,GAA4B;IAC/C,CAAC,EAAE,MAAM,CAAC,oEAAoE,CAAC;IAC/E,CAAC,EAAE,MAAM,CAAC,oEAAoE,CAAC;IAC/E,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;IACZ,EAAE,EAAE,MAAM,CAAC,oEAAoE,CAAC;IAChF,EAAE,EAAE,MAAM,CAAC,oEAAoE,CAAC;CACjF,CAAC;AAEF,MAAM,cAAc,GAAqB;IACvC,IAAI,EAAE,MAAM,CAAC,oEAAoE,CAAC;IAClF,OAAO,EAAE;QACP,CAAC,MAAM,CAAC,oCAAoC,CAAC,EAAE,CAAC,MAAM,CAAC,oCAAoC,CAAC,CAAC;QAC7F,CAAC,MAAM,CAAC,qCAAqC,CAAC,EAAE,MAAM,CAAC,oCAAoC,CAAC,CAAC;KAC9F;CACF,CAAC;AAEF,MAAM,GAAG,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACtC,MAAM,GAAG,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACtC,MAAM,GAAG,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAEtC;;;GAGG;AACH,SAAS,OAAO,CAAC,CAAS;IACxB,MAAM,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC;IAC5B,kBAAkB;IAClB,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;IAC7E,kBAAkB;IAClB,MAAM,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,EAAE,CAAC,CAAC;IAC9D,MAAM,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU;IACtC,MAAM,EAAE,GAAG,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM;IACpC,MAAM,EAAE,GAAG,CAAC,IAAA,iBAAI,EAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IACvC,MAAM,EAAE,GAAG,CAAC,IAAA,iBAAI,EAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IACvC,MAAM,GAAG,GAAG,CAAC,IAAA,iBAAI,EAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IACxC,MAAM,GAAG,GAAG,CAAC,IAAA,iBAAI,EAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,GAAG,GAAG,CAAC,IAAA,iBAAI,EAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,GAAG,GAAG,CAAC,IAAA,iBAAI,EAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,IAAI,GAAG,CAAC,IAAA,iBAAI,EAAC,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5C,MAAM,IAAI,GAAG,CAAC,IAAA,iBAAI,EAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC7C,MAAM,IAAI,GAAG,CAAC,IAAA,iBAAI,EAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,EAAE,GAAG,CAAC,IAAA,iBAAI,EAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3C,MAAM,EAAE,GAAG,CAAC,IAAA,iBAAI,EAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;IACvC,MAAM,IAAI,GAAG,IAAA,iBAAI,EAAC,EAAE,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;IAC9B,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7E,OAAO,IAAI,CAAC;AACd,CAAC;AAED,MAAM,IAAI,GAAG,IAAA,kBAAK,EAAC,eAAe,CAAC,CAAC,EAAE,SAAS,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;AAE/E;;;;;;;;;;;;;GAaG;AACU,QAAA,SAAS,GAAsB,IAAA,8BAAW,EACrD,EAAE,GAAG,eAAe,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,EAAE,EAClE,gBAAM,CACP,CAAC;AAEF,+FAA+F;AAC/F,iEAAiE;AACjE,wFAAwF;AACxF,MAAM,oBAAoB,GAAkC,EAAE,CAAC;AAC/D,SAAS,UAAU,CAAC,GAAW,EAAE,GAAG,QAAsB;IACxD,IAAI,IAAI,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAC;IACrC,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;QACvB,MAAM,IAAI,GAAG,IAAA,gBAAM,EAAC,UAAU,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClE,IAAI,GAAG,IAAA,sBAAW,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC/B,oBAAoB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;IACnC,CAAC;IACD,OAAO,IAAA,gBAAM,EAAC,IAAA,sBAAW,EAAC,IAAI,EAAE,GAAG,QAAQ,CAAC,CAAC,CAAC;AAChD,CAAC;AAED,oFAAoF;AACpF,MAAM,YAAY,GAAG,CAAC,KAAwB,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChF,MAAM,QAAQ,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,IAAA,0BAAe,EAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACvD,MAAM,IAAI,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,IAAA,gBAAG,EAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;AACtD,MAAM,IAAI,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,IAAA,gBAAG,EAAC,CAAC,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;AACtD,MAAM,KAAK,GAAG,eAAe,CAAC,CAAC,GAAG,EAAE,CAAC,iBAAS,CAAC,KAAK,CAAC,EAAE,CAAC;AACxD,MAAM,OAAO,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC;AAE/C,oCAAoC;AACpC,SAAS,mBAAmB,CAAC,IAAa;IACxC,8CAA8C;IAC9C,IAAI,EAAE,GAAG,IAAA,+BAAc,EAAC,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;IACxC,IAAI,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,4CAA4C;IAC7E,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC7C,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC;AAC5C,CAAC;AACD;;;GAGG;AACH,SAAS,MAAM,CAAC,CAAS;IACvB,IAAA,mBAAQ,EAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,iBAAiB;IAC3D,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACvB,MAAM,CAAC,GAAG,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,wBAAwB;IAC5D,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,2BAA2B;IAC/C,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAAE,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,mDAAmD;IAClF,MAAM,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,mDAAmD;IACzF,CAAC,CAAC,cAAc,EAAE,CAAC;IACnB,OAAO,CAAC,CAAC;AACX,CAAC;AACD,MAAM,GAAG,GAAG,0BAAe,CAAC;AAC5B;;GAEG;AACH,SAAS,SAAS,CAAC,GAAG,IAAkB;IACtC,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,mBAAmB,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7D,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,SAAc;IACzC,OAAO,mBAAmB,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC,oDAAoD;AACnG,CAAC;AAED;;;GAGG;AACH,SAAS,WAAW,CAAC,OAAY,EAAE,SAAkB,EAAE,UAAe,IAAA,sBAAW,EAAC,EAAE,CAAC;IACnF,MAAM,CAAC,GAAG,IAAA,sBAAW,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC1C,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,mBAAmB,CAAC,SAAS,CAAC,CAAC,CAAC,gCAAgC;IACjG,MAAM,CAAC,GAAG,IAAA,sBAAW,EAAC,SAAS,EAAE,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC,2CAA2C;IAC1F,MAAM,CAAC,GAAG,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,yDAAyD;IACpH,MAAM,IAAI,GAAG,UAAU,CAAC,eAAe,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,4CAA4C;IAChG,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,2BAA2B;IACvD,IAAI,EAAE,KAAK,GAAG;QAAE,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC,CAAC,kBAAkB;IAC7E,MAAM,EAAE,KAAK,EAAE,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,GAAG,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC,gBAAgB;IAC1E,MAAM,CAAC,GAAG,SAAS,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,gEAAgE;IAChG,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,+CAA+C;IAC/E,GAAG,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACf,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACvC,iEAAiE;IACjE,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;QAAE,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;IACpF,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;GAGG;AACH,SAAS,aAAa,CAAC,SAAc,EAAE,OAAY,EAAE,SAAc;IACjE,MAAM,GAAG,GAAG,IAAA,sBAAW,EAAC,WAAW,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;IACpD,MAAM,CAAC,GAAG,IAAA,sBAAW,EAAC,SAAS,EAAE,OAAO,CAAC,CAAC;IAC1C,MAAM,GAAG,GAAG,IAAA,sBAAW,EAAC,WAAW,EAAE,SAAS,EAAE,EAAE,CAAC,CAAC;IACpD,IAAI,CAAC;QACH,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,0CAA0C;QACtE,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,yCAAyC;QAC7E,IAAI,CAAC,IAAA,kBAAO,EAAC,CAAC,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;QACtD,MAAM,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,0CAA0C;QAC/E,IAAI,CAAC,IAAA,kBAAO,EAAC,CAAC,EAAE,GAAG,EAAE,eAAe,CAAC,CAAC,CAAC;YAAE,OAAO,KAAK,CAAC;QACtD,MAAM,CAAC,GAAG,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,0CAA0C;QAChG,qCAAqC;QACrC,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC9B,yDAAyD;QACzD,IAAI,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC;YAAE,OAAO,KAAK,CAAC;QACpD,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAyBD;;;;;;;;;;;;GAYG;AACU,QAAA,OAAO,GAAgC,CAAC,GAAG,EAAE;IACxD,MAAM,IAAI,GAAG,EAAE,CAAC;IAChB,MAAM,UAAU,GAAG,EAAE,CAAC;IACtB,MAAM,eAAe,GAAG,CAAC,IAAI,GAAG,IAAA,sBAAW,EAAC,UAAU,CAAC,EAAc,EAAE;QACrE,OAAO,IAAA,2BAAc,EAAC,IAAI,EAAE,eAAe,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC,CAAC;IACF,eAAe;IACf,iBAAS,CAAC,KAAK,CAAC,eAAe,CAAC;IAChC,SAAS,MAAM,CAAC,IAAiB;QAC/B,MAAM,SAAS,GAAG,eAAe,CAAC,IAAI,CAAC,CAAC;QACxC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,mBAAmB,CAAC,SAAS,CAAC,EAAE,CAAC;IAClE,CAAC;IACD,OAAO;QACL,MAAM;QACN,YAAY,EAAE,mBAAmB;QACjC,IAAI,EAAE,WAAW;QACjB,MAAM,EAAE,aAAa;QACrB,KAAK;QACL,KAAK,EAAE;YACL,eAAe,EAAE,eAAe;YAChC,gBAAgB,EAAE,eAAe;YACjC,UAAU;YAEV,eAAe;YACf,MAAM;YACN,YAAY;YACZ,eAAe,EAAf,0BAAe;YACf,eAAe,EAAf,0BAAe;YACf,GAAG,EAAH,gBAAG;SACJ;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,aAAa;YACnB,kBAAkB,EAAE,KAAK;YACzB,OAAO,EAAE;gBACP,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE,IAAI;gBACZ,SAAS,EAAE,IAAI,GAAG,CAAC;gBACnB,IAAI,EAAE,UAAU;aACjB;SACF;KACF,CAAC;AACJ,CAAC,CAAC,EAAE,CAAC;AAEL,MAAM,MAAM,GAAG,eAAe,CAAC,CAAC,GAAG,EAAE,CACnC,IAAA,6BAAU,EACR,IAAI,EACJ;IACE,OAAO;IACP;QACE,oEAAoE;QACpE,mEAAmE;QACnE,oEAAoE;QACpE,oEAAoE;KACrE;IACD,OAAO;IACP;QACE,oEAAoE;QACpE,oEAAoE;QACpE,oEAAoE,EAAE,SAAS;KAChF;IACD,OAAO;IACP;QACE,oEAAoE;QACpE,oEAAoE;QACpE,oEAAoE;QACpE,oEAAoE;KACrE;IACD,OAAO;IACP;QACE,oEAAoE;QACpE,oEAAoE;QACpE,oEAAoE;QACpE,oEAAoE,EAAE,SAAS;KAChF;CACF,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAA6C,CAClF,CAAC,EAAE,CAAC;AACP,MAAM,MAAM,GAAG,eAAe,CAAC,CAAC,GAAG,EAAE,CACnC,IAAA,oCAAmB,EAAC,IAAI,EAAE;IACxB,CAAC,EAAE,MAAM,CAAC,oEAAoE,CAAC;IAC/E,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC;IACjB,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;CAC9B,CAAC,CAAC,EAAE,CAAC;AAER,wEAAwE;AAC3D,QAAA,gBAAgB,GAAsC,CAAC,GAAG,EAAE,CACvE,IAAA,+BAAY,EACV,iBAAS,CAAC,KAAK,EACf,CAAC,OAAiB,EAAE,EAAE;IACpB,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,OAAO,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACtB,CAAC,EACD;IACE,GAAG,EAAE,gCAAgC;IACrC,SAAS,EAAE,gCAAgC;IAC3C,CAAC,EAAE,IAAI,CAAC,KAAK;IACb,CAAC,EAAE,CAAC;IACJ,CAAC,EAAE,GAAG;IACN,MAAM,EAAE,KAAK;IACb,IAAI,EAAE,gBAAM;CACb,CACF,CAAC,EAAE,CAAC;AAEP,uFAAuF;AAC1E,QAAA,WAAW,GAAsC,CAAC,GAAG,EAAE,CAClE,wBAAgB,CAAC,WAAW,CAAC,EAAE,CAAC;AAElC,uFAAuF;AAC1E,QAAA,aAAa,GAAsC,CAAC,GAAG,EAAE,CACpE,wBAAgB,CAAC,aAAa,CAAC,EAAE,CAAC"}