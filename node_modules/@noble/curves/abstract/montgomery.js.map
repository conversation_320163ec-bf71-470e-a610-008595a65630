{"version": 3, "file": "montgomery.js", "sourceRoot": "", "sources": ["../src/abstract/montgomery.ts"], "names": [], "mappings": ";;AA2DA,gCAsIC;AAjMD;;;;;GAKG;AACH,sEAAsE;AACtE,0CAOqB;AAErB,6CAAmC;AAEnC,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACtB,MAAM,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AA+BtB,SAAS,YAAY,CAAC,KAAgB;IACpC,IAAA,0BAAe,EAAC,KAAK,EAAE;QACrB,iBAAiB,EAAE,UAAU;QAC7B,UAAU,EAAE,UAAU;KACvB,CAAC,CAAC;IACH,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG,KAAK,EAAW,CAAC,CAAC;AAC9C,CAAC;AAED,SAAgB,UAAU,CAAC,QAAmB;IAC5C,MAAM,KAAK,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;IACrC,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,iBAAiB,EAAE,UAAU,EAAE,WAAW,EAAE,IAAI,EAAE,GAAG,KAAK,CAAC;IAC5E,MAAM,OAAO,GAAG,IAAI,KAAK,QAAQ,CAAC;IAClC,IAAI,CAAC,OAAO,IAAI,IAAI,KAAK,MAAM;QAAE,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;IACjE,MAAM,YAAY,GAAG,IAAI,IAAI,sBAAW,CAAC;IAEzC,MAAM,cAAc,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;IAC3C,MAAM,QAAQ,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;IACnC,MAAM,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAC3C,eAAe;IACf,0EAA0E;IAC1E,6CAA6C;IAC7C,yCAAyC;IACzC,MAAM,GAAG,GAAG,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;IACrD,+DAA+D;IAC/D,2DAA2D;IAC3D,4EAA4E;IAC5E,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC;IACpE,MAAM,QAAQ,GAAG,OAAO;QACtB,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG;QACtC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,CAAC;IACzC,MAAM,SAAS,GAAG,SAAS,GAAG,QAAQ,GAAG,GAAG,CAAC,CAAC,cAAc;IAC5D,MAAM,IAAI,GAAG,CAAC,CAAS,EAAE,EAAE,CAAC,IAAA,gBAAG,EAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtC,MAAM,OAAO,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;IAC5B,SAAS,OAAO,CAAC,CAAS;QACxB,OAAO,IAAA,0BAAe,EAAC,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC;IAC5C,CAAC;IACD,SAAS,OAAO,CAAC,CAAM;QACrB,MAAM,EAAE,GAAG,IAAA,sBAAW,EAAC,cAAc,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC;QACpD,+DAA+D;QAC/D,uEAAuE;QACvE,IAAI,OAAO;YAAE,EAAE,CAAC,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,cAAc;QAC1C,4EAA4E;QAC5E,sEAAsE;QACtE,uEAAuE;QACvE,kCAAkC;QAClC,OAAO,IAAI,CAAC,IAAA,0BAAe,EAAC,EAAE,CAAC,CAAC,CAAC;IACnC,CAAC;IACD,SAAS,YAAY,CAAC,MAAW;QAC/B,OAAO,IAAA,0BAAe,EAAC,iBAAiB,CAAC,IAAA,sBAAW,EAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC;IACrF,CAAC;IACD,SAAS,UAAU,CAAC,MAAW,EAAE,CAAM;QACrC,MAAM,EAAE,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC,CAAC;QAC9D,yEAAyE;QACzE,sDAAsD;QACtD,sCAAsC;QACtC,IAAI,EAAE,KAAK,GAAG;YAAE,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC1E,OAAO,OAAO,CAAC,EAAE,CAAC,CAAC;IACrB,CAAC;IACD,kFAAkF;IAClF,SAAS,cAAc,CAAC,MAAW;QACjC,OAAO,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;IACrC,CAAC;IAED,oCAAoC;IACpC,SAAS,KAAK,CAAC,IAAY,EAAE,GAAW,EAAE,GAAW;QACnD,uCAAuC;QACvC,wEAAwE;QACxE,qDAAqD;QACrD,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC;QACvC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,sBAAsB;QAC/C,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,sBAAsB;QAC/C,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACH,SAAS,gBAAgB,CAAC,CAAS,EAAE,MAAc;QACjD,IAAA,mBAAQ,EAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QACzB,IAAA,mBAAQ,EAAC,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;QACjD,MAAM,CAAC,GAAG,MAAM,CAAC;QACjB,MAAM,GAAG,GAAG,CAAC,CAAC;QACd,IAAI,GAAG,GAAG,GAAG,CAAC;QACd,IAAI,GAAG,GAAG,GAAG,CAAC;QACd,IAAI,GAAG,GAAG,CAAC,CAAC;QACZ,IAAI,GAAG,GAAG,GAAG,CAAC;QACd,IAAI,IAAI,GAAG,GAAG,CAAC;QACf,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;YACvD,MAAM,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,CAAC;YAC3B,IAAI,IAAI,GAAG,CAAC;YACZ,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YACvC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;YACjD,IAAI,GAAG,GAAG,CAAC;YAEX,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;YACpB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACvB,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;YACpB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACvB,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;YAClB,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;YACpB,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;YACpB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACvB,MAAM,EAAE,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;YACvB,MAAM,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;YACrB,MAAM,KAAK,GAAG,EAAE,GAAG,EAAE,CAAC;YACtB,GAAG,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;YACxB,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC;YACtC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;YACpB,GAAG,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC,CAAC;QACD,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACvC,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,KAAK,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;QACjD,MAAM,EAAE,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,iDAAiD;QAC7E,OAAO,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,6BAA6B;IACtD,CAAC;IACD,MAAM,eAAe,GAAG,CAAC,IAAI,GAAG,YAAY,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC;IAChE,MAAM,KAAK,GAAG;QACZ,eAAe;QACf,gBAAgB,EAAE,eAAe;KAClC,CAAC;IACF,SAAS,MAAM,CAAC,IAAiB;QAC/B,MAAM,SAAS,GAAG,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QAC9C,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC;IAC7D,CAAC;IACD,MAAM,OAAO,GAAG;QACd,MAAM,EAAE,QAAQ;QAChB,MAAM,EAAE,QAAQ;QAChB,IAAI,EAAE,QAAQ;KACf,CAAC;IACF,OAAO;QACL,MAAM;QACN,eAAe,EAAE,CAAC,SAAc,EAAE,SAAc,EAAE,EAAE,CAAC,UAAU,CAAC,SAAS,EAAE,SAAS,CAAC;QACrF,YAAY,EAAE,CAAC,SAAc,EAAc,EAAE,CAAC,cAAc,CAAC,SAAS,CAAC;QACvE,UAAU;QACV,cAAc;QACd,KAAK;QACL,OAAO,EAAE,OAAO,CAAC,KAAK,EAAE;QACxB,IAAI,EAAE,EAAE,IAAI,EAAE,YAAqB,EAAE,OAAO,EAAE;KAC/C,CAAC;AACJ,CAAC"}