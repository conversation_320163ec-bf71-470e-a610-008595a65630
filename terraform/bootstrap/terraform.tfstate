{"version": 4, "terraform_version": "1.12.2", "serial": 8, "lineage": "b03fa217-fb4a-ddc9-f838-6d473d0ea0fe", "outputs": {"backend_config_production": {"value": {"bucket": "sui-faucet-terraform-state", "dynamodb_table": "sui-faucet-terraform-locks", "encrypt": true, "key": "sui-faucet/production/terraform.tfstate", "region": "us-west-2"}, "type": ["object", {"bucket": "string", "dynamodb_table": "string", "encrypt": "bool", "key": "string", "region": "string"}]}, "backend_config_staging": {"value": {"bucket": "sui-faucet-terraform-state", "dynamodb_table": "sui-faucet-terraform-locks", "encrypt": true, "key": "sui-faucet/staging/terraform.tfstate", "region": "us-west-2"}, "type": ["object", {"bucket": "string", "dynamodb_table": "string", "encrypt": "bool", "key": "string", "region": "string"}]}, "lock_table_arn": {"value": "arn:aws:dynamodb:us-west-2:730335299438:table/sui-faucet-terraform-locks", "type": "string"}, "lock_table_name": {"value": "sui-faucet-terraform-locks", "type": "string"}, "state_bucket_arn": {"value": "arn:aws:s3:::sui-faucet-terraform-state", "type": "string"}, "state_bucket_name": {"value": "sui-faucet-terraform-state", "type": "string"}, "terraform_state_policy_arn": {"value": "arn:aws:iam::730335299438:policy/TerraformStateAccess", "type": "string"}}, "resources": [{"mode": "managed", "type": "aws_dynamodb_table", "name": "terraform_locks", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"arn": "arn:aws:dynamodb:us-west-2:730335299438:table/sui-faucet-terraform-locks", "attribute": [{"name": "LockID", "type": "S"}], "billing_mode": "PAY_PER_REQUEST", "deletion_protection_enabled": false, "global_secondary_index": [], "hash_key": "LockID", "id": "sui-faucet-terraform-locks", "import_table": [], "local_secondary_index": [], "name": "sui-faucet-terraform-locks", "on_demand_throughput": [], "point_in_time_recovery": [{"enabled": true, "recovery_period_in_days": 35}], "range_key": null, "read_capacity": 0, "replica": [], "restore_date_time": null, "restore_source_name": null, "restore_source_table_arn": null, "restore_to_latest_time": null, "server_side_encryption": [{"enabled": true, "kms_key_arn": ""}], "stream_arn": "", "stream_enabled": false, "stream_label": "", "stream_view_type": "", "table_class": "STANDARD", "tags": {"Description": "Terraform state locking for Sui Faucet", "Name": "sui-faucet-terraform-locks"}, "tags_all": {"Description": "Terraform state locking for Sui Faucet", "ManagedBy": "terraform", "Name": "sui-faucet-terraform-locks", "Project": "sui-faucet", "Purpose": "terraform-state-management"}, "timeouts": null, "ttl": [{"attribute_name": "", "enabled": false}], "write_capacity": 0}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjYwMDAwMDAwMDAwMCwidXBkYXRlIjozNjAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0="}]}, {"mode": "managed", "type": "aws_iam_policy", "name": "terraform_state_access", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::730335299438:policy/TerraformStateAccess", "attachment_count": 0, "description": "Policy for accessing Terraform state resources", "id": "arn:aws:iam::730335299438:policy/TerraformStateAccess", "name": "TerraformStateAccess", "name_prefix": "", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"s3:ListBucket\",\"s3:GetBucketVersioning\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:s3:::sui-faucet-terraform-state\"},{\"Action\":[\"s3:GetObject\",\"s3:PutObject\",\"s3:DeleteObject\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:s3:::sui-faucet-terraform-state/*\"},{\"Action\":[\"dynamodb:GetItem\",\"dynamodb:PutItem\",\"dynamodb:DeleteItem\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:dynamodb:us-west-2:730335299438:table/sui-faucet-terraform-locks\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPA2UC3AI5XENLUDIAJC", "tags": null, "tags_all": {"ManagedBy": "terraform", "Project": "sui-faucet", "Purpose": "terraform-state-management"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_dynamodb_table.terraform_locks", "aws_s3_bucket.terraform_state"]}]}, {"mode": "managed", "type": "aws_s3_bucket", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::sui-faucet-terraform-state", "bucket": "sui-faucet-terraform-state", "bucket_domain_name": "sui-faucet-terraform-state.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "sui-faucet-terraform-state.s3.us-west-2.amazonaws.com", "cors_rule": [], "force_destroy": false, "grant": [{"id": "04e7a14f60ea7e11698c57f5225f2cb9e5f9a408399b09154465a957a2004ae1", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3BJ6K6RIION7M", "id": "sui-faucet-terraform-state", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "", "region": "us-west-2", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {"Description": "Terraform state storage for Sui Faucet", "Name": "sui-faucet-terraform-state"}, "tags_all": {"Description": "Terraform state storage for Sui Faucet", "ManagedBy": "terraform", "Name": "sui-faucet-terraform-state", "Project": "sui-faucet", "Purpose": "terraform-state-management"}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}, {"mode": "managed", "type": "aws_s3_bucket_lifecycle_configuration", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 1, "attributes": {"bucket": "sui-faucet-terraform-state", "expected_bucket_owner": "", "id": "sui-faucet-terraform-state", "rule": [{"abort_incomplete_multipart_upload": [{"days_after_initiation": 7}], "expiration": [], "filter": [{"and": [], "object_size_greater_than": null, "object_size_less_than": null, "prefix": "", "tag": []}], "id": "terraform_state_lifecycle", "noncurrent_version_expiration": [{"newer_noncurrent_versions": null, "noncurrent_days": 30}], "noncurrent_version_transition": [], "prefix": "", "status": "Enabled", "transition": []}], "timeouts": null, "transition_default_minimum_object_size": "all_storage_classes_128K"}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["aws_s3_bucket.terraform_state"]}]}, {"mode": "managed", "type": "aws_s3_bucket_public_access_block", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"block_public_acls": true, "block_public_policy": true, "bucket": "sui-faucet-terraform-state", "id": "sui-faucet-terraform-state", "ignore_public_acls": true, "restrict_public_buckets": true}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.terraform_state"]}]}, {"mode": "managed", "type": "aws_s3_bucket_server_side_encryption_configuration", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "sui-faucet-terraform-state", "expected_bucket_owner": "", "id": "sui-faucet-terraform-state", "rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": true}]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.terraform_state"]}]}, {"mode": "managed", "type": "aws_s3_bucket_versioning", "name": "terraform_state", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"bucket": "sui-faucet-terraform-state", "expected_bucket_owner": "", "id": "sui-faucet-terraform-state", "mfa": null, "versioning_configuration": [{"mfa_delete": "", "status": "Enabled"}]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_s3_bucket.terraform_state"]}]}], "check_results": null}