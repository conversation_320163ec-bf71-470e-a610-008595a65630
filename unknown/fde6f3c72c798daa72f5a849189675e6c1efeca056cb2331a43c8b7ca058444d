{"version": 3, "sources": ["../../../src/cryptography/signature-scheme.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nexport const SIGNATURE_SCHEME_TO_FLAG = {\n\tED25519: 0x00,\n\tSecp256k1: 0x01,\n\tSecp256r1: 0x02,\n\tMultiSig: 0x03,\n\tZk<PERSON>ogin: 0x05,\n\tPasskey: 0x06,\n} as const;\n\nexport const SIGNATURE_SCHEME_TO_SIZE = {\n\tED25519: 32,\n\tSecp256k1: 33,\n\tSecp256r1: 33,\n};\n\nexport const SIGNATURE_FLAG_TO_SCHEME = {\n\t0x00: 'ED25519',\n\t0x01: 'Secp256k1',\n\t0x02: 'Secp256r1',\n\t0x03: 'MultiSig',\n\t0x05: 'ZkLogin',\n\t0x06: 'Passkey',\n} as const;\n\nexport type SignatureScheme =\n\t| 'ED25519'\n\t| 'Secp256k1'\n\t| 'Secp256r1'\n\t| 'MultiSig'\n\t| 'ZkLogin'\n\t| 'Passkey';\n\nexport type SignatureFlag = keyof typeof SIGNATURE_FLAG_TO_SCHEME;\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAGO,MAAM,2BAA2B;AAAA,EACvC,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AAAA,EACX,UAAU;AAAA,EACV,SAAS;AAAA,EACT,SAAS;AACV;AAEO,MAAM,2BAA2B;AAAA,EACvC,SAAS;AAAA,EACT,WAAW;AAAA,EACX,WAAW;AACZ;AAEO,MAAM,2BAA2B;AAAA,EACvC,GAAM;AAAA,EACN,GAAM;AAAA,EACN,GAAM;AAAA,EACN,GAAM;AAAA,EACN,GAAM;AAAA,EACN,GAAM;AACP;", "names": []}