{"version": 3, "sources": ["../../../src/graphql/types.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nexport type MoveData =\n\t| { Address: string }\n\t| { UID: string }\n\t| { ID: string }\n\t| { Bool: boolean }\n\t| { Number: string }\n\t| { String: string }\n\t| { Vector: [MoveData] }\n\t| { Option: MoveData | null }\n\t| { Struct: [{ name: string; value: MoveData }] };\n\nexport type MoveTypeLayout =\n\t| 'address'\n\t| 'bool'\n\t| 'u8'\n\t| 'u16'\n\t| 'u32'\n\t| 'u64'\n\t| 'u128'\n\t| 'u256'\n\t| { vector: MoveTypeLayout }\n\t| {\n\t\t\tstruct: {\n\t\t\t\ttype: string;\n\t\t\t\tfields: [{ name: string; layout: MoveTypeLayout }];\n\t\t\t};\n\t  };\n\nexport type MoveTypeSignature =\n\t| 'address'\n\t| 'bool'\n\t| 'u8'\n\t| 'u16'\n\t| 'u32'\n\t| 'u64'\n\t| 'u128'\n\t| 'u256'\n\t| { vector: MoveTypeSignature }\n\t| {\n\t\t\tdatatype: {\n\t\t\t\tpackage: string;\n\t\t\t\tmodule: string;\n\t\t\t\ttype: string;\n\t\t\t\ttypeParameters: [MoveTypeSignature];\n\t\t\t};\n\t  };\n\nexport type OpenMoveTypeSignature = {\n\tref?: ('&' | '&mut') | null;\n\tbody: OpenMoveTypeSignatureBody;\n};\n\nexport type OpenMoveTypeSignatureBody =\n\t| 'address'\n\t| 'bool'\n\t| 'u8'\n\t| 'u16'\n\t| 'u32'\n\t| 'u64'\n\t| 'u128'\n\t| 'u256'\n\t| { vector: OpenMoveTypeSignatureBody }\n\t| {\n\t\t\tdatatype: {\n\t\t\t\tpackage: string;\n\t\t\t\tmodule: string;\n\t\t\t\ttype: string;\n\t\t\t\ttypeParameters: [OpenMoveTypeSignatureBody];\n\t\t\t};\n\t  }\n\t| { typeParameter: number };\n\nexport interface CustomScalars {\n\tBigInt: string;\n\tBase64: string;\n\tDateTime: string;\n\tJSON: unknown;\n\tMoveData: MoveData;\n\tMoveTypeLayout: MoveTypeLayout;\n\tMoveTypeSignature: MoveTypeSignature;\n\tOpenMoveTypeSignature: OpenMoveTypeSignature;\n\tSuiAddress: string;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA;AAAA;", "names": []}