{"version": 3, "sources": ["../../../src/zklogin/bcs.ts"], "sourcesContent": ["// Copyright (c) Mysten Labs, Inc.\n// SPDX-License-Identifier: Apache-2.0\n\nimport type { InferBcsInput } from '@mysten/bcs';\nimport { bcs } from '@mysten/bcs';\n\nexport const zkLoginSignature = bcs.struct('ZkLoginSignature', {\n\tinputs: bcs.struct('ZkLoginSignatureInputs', {\n\t\tproofPoints: bcs.struct('ZkLoginSignatureInputsProofPoints', {\n\t\t\ta: bcs.vector(bcs.string()),\n\t\t\tb: bcs.vector(bcs.vector(bcs.string())),\n\t\t\tc: bcs.vector(bcs.string()),\n\t\t}),\n\t\tissBase64Details: bcs.struct('ZkLoginSignatureInputsClaim', {\n\t\t\tvalue: bcs.string(),\n\t\t\tindexMod4: bcs.u8(),\n\t\t}),\n\t\theaderBase64: bcs.string(),\n\t\taddressSeed: bcs.string(),\n\t}),\n\tmaxEpoch: bcs.u64(),\n\tuserSignature: bcs.vector(bcs.u8()),\n});\n\nexport type ZkLoginSignature = InferBcsInput<typeof zkLoginSignature>;\nexport type ZkLoginSignatureInputs = ZkLoginSignature['inputs'];\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAIA,iBAAoB;AAEb,MAAM,mBAAmB,eAAI,OAAO,oBAAoB;AAAA,EAC9D,QAAQ,eAAI,OAAO,0BAA0B;AAAA,IAC5C,aAAa,eAAI,OAAO,qCAAqC;AAAA,MAC5D,GAAG,eAAI,OAAO,eAAI,OAAO,CAAC;AAAA,MAC1B,GAAG,eAAI,OAAO,eAAI,OAAO,eAAI,OAAO,CAAC,CAAC;AAAA,MACtC,GAAG,eAAI,OAAO,eAAI,OAAO,CAAC;AAAA,IAC3B,CAAC;AAAA,IACD,kBAAkB,eAAI,OAAO,+BAA+B;AAAA,MAC3D,OAAO,eAAI,OAAO;AAAA,MAClB,WAAW,eAAI,GAAG;AAAA,IACnB,CAAC;AAAA,IACD,cAAc,eAAI,OAAO;AAAA,IACzB,aAAa,eAAI,OAAO;AAAA,EACzB,CAAC;AAAA,EACD,UAAU,eAAI,IAAI;AAAA,EAClB,eAAe,eAAI,OAAO,eAAI,GAAG,CAAC;AACnC,CAAC;", "names": []}