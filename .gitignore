# Node modules
node_modules/

# Build output
dist/
build/
out/

# TypeScript cache
*.tsbuildinfo

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment variables
.env
.env.*

# IDE files
.vscode/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln

# Coverage directory
coverage/

# Misc
.DS_Store
*.local

# Terraform
*.tfstate
*.tfstate.*
*.tfvars
.terraform/
.terraform.lock.hcl
terraform.tfplan
terraform.tfplan.*

# Terraform directories (all levels)
**/.terraform/*
**/terraform.tfstate*
**/terraform.tfplan*
**/.terraform.lock.hcl

# Terraform bootstrap artifacts
terraform/bootstrap/.terraform/
terraform/bootstrap/terraform.tfstate*
terraform/bootstrap/terraform.tfplan*
