# Design Document

## Overview

The Sui Testnet Faucet DApp is a complete web application consisting of a React frontend, Node.js/TypeScript backend API, analytics dashboard, and Discord bot integration. The system provides a user-friendly interface for developers to request SUI testnet tokens while maintaining robust security, rate limiting, and monitoring capabilities.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    Users[Web Users] --> Frontend[React Frontend]
    Discord[Discord Users] --> <PERSON><PERSON>[Discord Bot]
    Admins[Administrators] --> Dashboard[Analytics Dashboard]
    
    Frontend --> Nginx[Nginx Reverse Proxy]
    Bot --> Nginx
    Dashboard --> Nginx
    
    Nginx --> API[Express.js API Server]
    API --> RL[Rate Limiter]
    API --> Val[Validator]
    API --> Sui[Sui SDK Client]
    API --> Log[Logger]
    API --> Analytics[Analytics Service]
    
    RL --> Redis[(Redis Cache)]
    Analytics --> Redis
    Sui --> SuiNet[Sui Testnet]
    Log --> Files[Log Files]
    API --> Health[Health Monitor]
```

### Technology Stack

**Backend:**
- **Runtime**: Node.js 18+ or Bun (configurable)
- **Framework**: Express.js with TypeScript
- **Blockchain SDK**: @mysten/sui.js
- **Rate Limiting**: Redis with node-rate-limiter-flexible
- **Logging**: Winston with structured logging
- **Validation**: Joi for request validation
- **Environment**: dotenv for configuration
- **Process Management**: PM2 for production deployment

**Frontend:**
- **Framework**: React 18+ with TypeScript
- **Styling**: Tailwind CSS for responsive design
- **State Management**: React Query for API state
- **Form Handling**: React Hook Form with validation
- **Build Tool**: Vite for fast development and building
- **Wallet Integration**: Sui Wallet Kit for wallet connections

**Discord Bot:**
- **Framework**: Discord.js v14
- **Commands**: Slash commands with autocomplete
- **Permissions**: Role-based access control
- **Deployment**: Separate service or integrated with main API

**Analytics Dashboard:**
- **Framework**: React with Chart.js/Recharts
- **Authentication**: JWT-based admin authentication
- **Real-time Updates**: WebSocket connections for live data
- **Data Visualization**: Interactive charts and metrics

## Components and Interfaces

### 1. API Layer

**Express.js Router Structure:**
```typescript
interface FaucetAPI {
  POST /api/v1/faucet/request
  GET /api/v1/health
  GET /api/v1/status
  GET /api/v1/metrics
}
```

**Request/Response Interfaces:**
```typescript
interface FaucetRequest {
  walletAddress: string;
  amount?: number; // Optional, defaults to configured amount
}

interface FaucetResponse {
  success: boolean;
  transactionHash?: string;
  amount?: string;
  message: string;
  retryAfter?: number; // For rate limited requests
}

interface HealthResponse {
  status: 'healthy' | 'degraded' | 'unhealthy';
  walletBalance: string;
  uptime: number;
  version: string;
}
```

### 2. Sui Integration Layer

**SuiClient Wrapper:**
```typescript
interface SuiService {
  initializeClient(): Promise<void>;
  getWalletBalance(): Promise<bigint>;
  sendTokens(recipientAddress: string, amount: bigint): Promise<string>;
  validateAddress(address: string): boolean;
}
```

**Transaction Management:**
- Uses Sui SDK's transaction building and signing
- Implements retry logic with exponential backoff
- Handles gas estimation and fee management
- Monitors wallet balance and alerts on low funds

### 3. Rate Limiting Layer

**Rate Limiter Configuration:**
```typescript
interface RateLimitConfig {
  perIP: {
    points: number; // Number of requests
    duration: number; // Time window in seconds
  };
  perWallet: {
    points: number;
    duration: number;
  };
  global: {
    points: number;
    duration: number;
  };
}
```

**Implementation Strategy:**
- Redis-backed sliding window rate limiting
- Separate limits for IP addresses and wallet addresses
- Global rate limiting to protect against coordinated attacks
- Configurable limits via environment variables

### 4. Validation Layer

**Address Validation:**
- Sui address format validation using SDK utilities
- Checksum verification
- Network-specific address validation (testnet)

**Request Validation:**
- Input sanitization and validation using Joi schemas
- Rate limit header validation
- Request size limits

### 5. Frontend Components

**React Component Structure:**
```typescript
interface FaucetFormProps {
  onSubmit: (address: string) => Promise<void>;
  isLoading: boolean;
  error?: string;
}

interface TransactionResultProps {
  transactionHash: string;
  amount: string;
  explorerUrl: string;
}

interface WalletConnectProps {
  onConnect: (address: string) => void;
  supportedWallets: string[];
}
```

**Frontend Features:**
- Real-time address validation with visual feedback
- Mobile-responsive design with Tailwind CSS
- Loading states and progress indicators
- Error handling with user-friendly messages
- Wallet integration with Sui Wallet Kit
- Transaction result display with explorer links

### 6. Discord Bot Integration

**Bot Command Structure:**
```typescript
interface DiscordCommands {
  '/faucet': {
    address: string;
    amount?: number;
  };
  '/balance': {}; // Admin only
  '/stats': {}; // Admin only
  '/help': {};
}

interface BotResponse {
  embeds: DiscordEmbed[];
  ephemeral?: boolean;
  components?: ActionRow[];
}
```

**Bot Features:**
- Slash commands with parameter validation
- Rich embed responses with transaction details
- Role-based permission system
- Admin commands for monitoring
- Error handling with helpful messages
- Rate limiting integration with main API

### 7. Analytics Dashboard

**Dashboard Components:**
```typescript
interface DashboardMetrics {
  totalRequests: number;
  successRate: number;
  averageResponseTime: number;
  currentWalletBalance: string;
  rateLimitHits: number;
  topRequestingIPs: Array<{ip: string, count: number}>;
}

interface ChartData {
  requestsOverTime: TimeSeriesData[];
  successRateOverTime: TimeSeriesData[];
  geographicDistribution: GeoData[];
}
```

**Dashboard Features:**
- Real-time metrics with WebSocket updates
- Interactive charts for request patterns
- Wallet balance monitoring with alerts
- Rate limiting statistics and blocked requests
- Geographic distribution of requests
- Admin controls for system management

### 8. Logging and Monitoring

**Structured Logging:**
```typescript
interface LogEntry {
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  metadata: {
    requestId?: string;
    ipAddress?: string;
    walletAddress?: string;
    transactionHash?: string;
    amount?: string;
    duration?: number;
    userAgent?: string;
    source?: 'web' | 'discord' | 'api';
  };
}
```

**Metrics Collection:**
- Request count and success/failure rates by source
- Response time percentiles across all interfaces
- Rate limit hit rates and patterns
- Wallet balance monitoring with alerts
- Error rate tracking by component
- User engagement metrics (web vs Discord usage)

## Data Models

### Configuration Model
```typescript
interface FaucetConfig {
  sui: {
    network: 'testnet' | 'devnet';
    rpcUrl: string;
    privateKey: string; // From environment
    defaultAmount: string; // In MIST (smallest SUI unit)
    maxAmount: string;
    minWalletBalance: string;
  };
  rateLimits: RateLimitConfig;
  server: {
    port: number;
    corsOrigins: string[];
    requestTimeout: number;
  };
  redis: {
    url: string;
    keyPrefix: string;
  };
  logging: {
    level: string;
    format: 'json' | 'simple';
    file?: string;
  };
}
```

### Request Context Model
```typescript
interface RequestContext {
  requestId: string;
  ipAddress: string;
  userAgent: string;
  timestamp: Date;
  walletAddress?: string;
  amount?: string;
}
```

## Error Handling

### Error Categories
1. **Validation Errors** (400): Invalid wallet address, amount out of range
2. **Rate Limit Errors** (429): Too many requests from IP or wallet
3. **Service Errors** (503): Wallet balance too low, Sui network unavailable
4. **Internal Errors** (500): Unexpected application errors

### Error Response Format
```typescript
interface ErrorResponse {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
  retryAfter?: number;
}
```

### Retry Strategy
- Exponential backoff for Sui network calls
- Circuit breaker pattern for external dependencies
- Graceful degradation when Redis is unavailable

## Testing Strategy

### Unit Testing
- **Sui Service**: Mock SDK calls, test transaction building
- **Rate Limiter**: Test various limit scenarios
- **Validators**: Test address validation edge cases
- **API Handlers**: Test request/response handling

### Integration Testing
- **End-to-End API Tests**: Full request flow testing
- **Sui Network Integration**: Test against Sui testnet
- **Redis Integration**: Rate limiting with real Redis instance
- **Error Scenarios**: Network failures, invalid responses

### Load Testing
- **Concurrent Request Handling**: Test multiple simultaneous requests
- **Rate Limit Effectiveness**: Verify limits work under load
- **Performance Benchmarks**: Response time under various loads
- **Memory and CPU Usage**: Resource consumption monitoring

### Test Environment Setup
- Docker containers for Redis and test dependencies
- Sui testnet connection for integration tests
- Mock wallet with test tokens
- Automated test data cleanup

## Security Considerations

### Private Key Management
- Environment variable storage only
- No logging of sensitive data
- Secure key rotation procedures
- Hardware security module support (future)

### Request Security
- Input validation and sanitization
- CORS configuration
- Request size limits
- IP-based blocking for abuse

### Monitoring and Alerting
- Unusual request pattern detection
- Wallet balance monitoring
- Failed transaction alerting
- Performance degradation alerts

## Deployment Architecture

### Production Setup
```mermaid
graph TB
    Internet --> Nginx[Nginx Reverse Proxy]
    Nginx --> PM2[PM2 Process Manager]
    PM2 --> App1[Faucet App Instance 1]
    PM2 --> App2[Faucet App Instance 2]
    App1 --> Redis[(Redis Cluster)]
    App2 --> Redis
    App1 --> Sui[Sui Testnet RPC]
    App2 --> Sui
```

### Environment Configuration
- **Development**: Single instance, local Redis, testnet
- **Staging**: Multi-instance, Redis cluster, testnet
- **Production**: Load balanced, Redis cluster, monitoring

### Monitoring Stack
- Application metrics via Prometheus
- Log aggregation via ELK stack
- Uptime monitoring via external services
- Alert management via PagerDuty/Slack