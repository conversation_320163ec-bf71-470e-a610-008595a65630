import { Ed25519Keypair } from '@mysten/sui/keypairs/ed25519';
import { toB64 } from '@mysten/sui/utils';

// Generate a new keypair
const keypair = new Ed25519Keypair();

// Get the private key in base64 format
const privateKeyBytes = keypair.getSecretKey();
const privateKey = toB64(privateKeyBytes);

// Get the public key and address
const publicKey = keypair.getPublicKey();
const address = publicKey.toSuiAddress();

console.log('🔑 Generated new Sui wallet for faucet:');
console.log('');
console.log('Address:', address);
console.log('Private Key (Base64):', privateKey);
console.log('');
console.log('📝 Copy the private key to your .env file:');
console.log(`SUI_PRIVATE_KEY=${privateKey}`);
console.log('');
console.log('⚠️  IMPORTANT: This wallet has no funds!');
console.log('You need to fund it from the official Sui testnet faucet first:');
console.log('🌐 https://faucet.testnet.sui.io/gas');
console.log('');
console.log('💰 Recommended: Fund with at least 10-20 SUI for testing');
