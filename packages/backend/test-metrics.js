import { databaseService } from './dist/services/database.js';

(async () => {
  try {
    await databaseService.connect();
    
    console.log('📊 Faucet Metrics (Last 7 days):');
    const metrics = await databaseService.getFaucetMetrics(7);
    console.table(metrics);
    
    console.log('\n🔍 Recent Transactions:');
    const recent = await databaseService.getRecentTransactions(5);
    console.table(recent);
    
    await databaseService.disconnect();
  } catch (error) {
    console.error('Error:', error.message);
  }
})();
