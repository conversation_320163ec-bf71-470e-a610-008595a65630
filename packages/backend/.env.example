# Sui Configuration
SUI_NETWORK=testnet
SUI_RPC_URL=https://fullnode.testnet.sui.io/
SUI_PRIVATE_KEY=your_base64_private_key_here
SUI_DEFAULT_AMOUNT=1000000000
SUI_MAX_AMOUNT=5000000000
SUI_MIN_WALLET_BALANCE=10000000000

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=3600000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_MAX_PER_WALLET=1
RATE_LIMIT_MAX_PER_IP=5
RATE_LIMIT_SKIP_SUCCESS=false
RATE_LIMIT_SKIP_FAILED=false

# Server Configuration
PORT=3001
NODE_ENV=development
CORS_ORIGINS=http://localhost:3000,http://localhost:5173
REQUEST_TIMEOUT=30000

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_KEY_PREFIX=sui-faucet:
REDIS_TTL=3600

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/sui_faucet
DATABASE_SSL=true

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE=

# Optional: Discord Bot Configuration (if using Discord integration)
DISCORD_BOT_TOKEN=your_discord_bot_token_here
DISCORD_CLIENT_ID=your_discord_client_id_here
DISCORD_GUILD_ID=your_discord_guild_id_here
