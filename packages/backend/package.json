{"name": "@sui-faucet/backend", "version": "1.0.0", "description": "Backend API server for Sui Testnet Faucet", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx watch src/index.ts", "start": "node dist/index.js", "test": "vitest run", "test:watch": "vitest", "lint": "eslint src --ext .ts", "clean": "rm -rf dist"}, "dependencies": {"@mysten/sui": "^1.0.0", "@types/uuid": "^10.0.0", "cors": "^2.8.5", "dotenv": "^16.3.0", "express": "^4.18.2", "helmet": "^7.1.0", "joi": "^17.11.0", "rate-limiter-flexible": "^4.0.0", "redis": "^4.6.0", "uuid": "^11.1.0", "winston": "^3.11.0"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "vitest": "^1.0.0"}}