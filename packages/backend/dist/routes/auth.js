import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler.js';
import { validate } from '../validation/schemas.js';
import { logger } from '../utils/logger.js';
import { config } from '../config/index.js';
import Joi from 'joi';
const router = Router();
const apiKeySchema = Joi.object({
    apiKey: Joi.string()
        .required()
        .messages({
        'any.required': 'API key is required',
        'string.empty': 'API key cannot be empty',
    }),
});
router.post('/verify', validate(apiKeySchema, 'body'), asyncHandler(async (req, res) => {
    const { apiKey } = req.body;
    const requestId = req.requestId;
    const clientIP = req.ip || 'unknown';
    logger.info(`API key verification attempt`, {
        requestId,
        ip: clientIP,
        timestamp: new Date().toISOString(),
    });
    try {
        if (apiKey === config.auth.apiKey) {
            logger.info(`✅ API key valid`, {
                requestId,
                ip: clientIP,
                timestamp: new Date().toISOString(),
            });
            return res.status(200).json({
                success: true,
                valid: true,
                message: '🎉 API key is valid! Access granted.',
                timestamp: new Date().toISOString(),
            });
        }
        else {
            logger.warn(`❌ API key invalid`, {
                requestId,
                ip: clientIP,
                timestamp: new Date().toISOString(),
            });
            return res.status(401).json({
                success: false,
                valid: false,
                message: '🚫 Invalid API key. Access denied.',
                error: {
                    code: 'INVALID_API_KEY',
                    details: 'The provided API key is incorrect',
                },
            });
        }
    }
    catch (error) {
        logger.error('API key verification error', {
            error: error.message,
            requestId,
            ip: clientIP,
        });
        return res.status(500).json({
            success: false,
            message: '❌ API key verification service error',
            error: {
                code: 'API_KEY_SERVICE_ERROR',
                details: 'Internal server error during API key verification',
            },
        });
    }
}));
router.get('/status', asyncHandler(async (req, res) => {
    const requestId = req.requestId;
    logger.info(`Auth service status check`, {
        requestId,
        ip: req.ip || 'unknown',
    });
    return res.status(200).json({
        success: true,
        message: '🔐 API Key verification service is operational',
        endpoints: {
            verify: 'POST /api/v1/auth/verify',
            status: 'GET /api/v1/auth/status',
        },
        timestamp: new Date().toISOString(),
    });
}));
router.get('/key', asyncHandler(async (req, res) => {
    const requestId = req.requestId;
    logger.info(`API key info request`, {
        requestId,
        ip: req.ip || 'unknown',
    });
    return res.status(200).json({
        success: true,
        message: '🔑 API Key Information',
        info: {
            description: 'Use the API key "" to authenticate faucet requests',
            usage: {
                header: 'Authorization: Bearer ',
                example: 'curl -H "Authorization: Bearer " -X POST /api/v1/faucet/request',
                verification: 'POST /api/v1/auth/verify with {"apiKey": ""}',
            },
            validKey: config.auth.apiKey,
            protectedEndpoints: ['/api/v1/faucet/request'],
        },
        timestamp: new Date().toISOString(),
    });
}));
export { router as authRoutes };
//# sourceMappingURL=auth.js.map