import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler.js';
import { validate, healthQuerySchema } from '../validation/schemas.js';
import { suiService } from '../services/sui.js';
import { redisClient } from '../services/redis.js';
import { config } from '../config/index.js';
import { logHealthCheck, logger } from '../utils/logger.js';
const router = Router();
const processStartTime = Date.now();
router.get('/', validate(healthQuerySchema, 'query'), asyncHandler(async (req, res) => {
    const startTime = Date.now();
    const { detailed } = req.query;
    try {
        const suiHealth = await suiService.healthCheck();
        logHealthCheck('sui', suiHealth.status, suiHealth.details);
        const redisHealth = await redisClient.healthCheck();
        logHealthCheck('redis', redisHealth.status, { latency: redisHealth.latency });
        let overallStatus = 'healthy';
        if (suiHealth.status === 'unhealthy' || redisHealth.status === 'unhealthy') {
            overallStatus = 'unhealthy';
        }
        else if (suiHealth.details?.isLowBalance) {
            overallStatus = 'degraded';
        }
        const healthStatus = {
            status: overallStatus,
            timestamp: new Date().toISOString(),
            uptime: Date.now() - processStartTime,
            version: '1.0.0',
            environment: config.server.environment,
            services: {
                sui: {
                    status: suiHealth.status,
                    ...(detailed && { details: suiHealth.details }),
                },
                redis: {
                    status: redisHealth.status,
                    ...(detailed && { details: { latency: redisHealth.latency } }),
                },
            },
        };
        if (detailed && suiHealth.status === 'healthy' && suiHealth.details) {
            healthStatus.wallet = {
                address: suiHealth.details.walletAddress,
                balance: suiHealth.details.walletBalance,
                balanceSui: suiHealth.details.walletBalanceSui,
                isLowBalance: suiHealth.details.isLowBalance,
            };
        }
        if (detailed) {
            const responseTime = Date.now() - startTime;
            healthStatus.performance = {
                responseTime,
                memoryUsage: process.memoryUsage(),
            };
            if (process.cpuUsage) {
                healthStatus.performance.cpuUsage = process.cpuUsage();
            }
        }
        const httpStatus = overallStatus === 'healthy' ? 200 :
            overallStatus === 'degraded' ? 200 : 503;
        res.status(httpStatus).json({
            success: overallStatus !== 'unhealthy',
            data: healthStatus,
        });
    }
    catch (error) {
        logger.error('Health check failed', {
            error: error.message,
            requestId: req.requestId,
        });
        const errorResponse = {
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            uptime: Date.now() - processStartTime,
            version: '1.0.0',
            environment: config.server.environment,
            services: {
                sui: { status: 'unhealthy' },
                redis: { status: 'unhealthy' },
            },
        };
        res.status(503).json({
            success: false,
            data: errorResponse,
            error: {
                message: 'Health check failed',
                details: detailed ? error.message : undefined,
            },
        });
    }
}));
router.get('/live', asyncHandler(async (req, res) => {
    res.status(200).json({
        status: 'alive',
        timestamp: new Date().toISOString(),
        uptime: Date.now() - processStartTime,
    });
}));
router.get('/ready', asyncHandler(async (req, res) => {
    try {
        const suiReady = suiService.isReady;
        const redisReady = redisClient.isHealthy;
        const isReady = suiReady && redisReady;
        if (isReady) {
            res.status(200).json({
                status: 'ready',
                timestamp: new Date().toISOString(),
                services: {
                    sui: suiReady,
                    redis: redisReady,
                },
            });
        }
        else {
            res.status(503).json({
                status: 'not_ready',
                timestamp: new Date().toISOString(),
                services: {
                    sui: suiReady,
                    redis: redisReady,
                },
            });
        }
    }
    catch (error) {
        logger.error('Readiness check failed', {
            error: error.message,
            requestId: req.requestId,
        });
        res.status(503).json({
            status: 'not_ready',
            timestamp: new Date().toISOString(),
            error: error.message,
        });
    }
}));
router.get('/startup', asyncHandler(async (req, res) => {
    try {
        const suiInitialized = suiService.isReady;
        const redisConnected = redisClient.isHealthy;
        const hasStarted = suiInitialized && redisConnected;
        if (hasStarted) {
            res.status(200).json({
                status: 'started',
                timestamp: new Date().toISOString(),
                startupTime: Date.now() - processStartTime,
            });
        }
        else {
            res.status(503).json({
                status: 'starting',
                timestamp: new Date().toISOString(),
                startupTime: Date.now() - processStartTime,
                services: {
                    sui: suiInitialized,
                    redis: redisConnected,
                },
            });
        }
    }
    catch (error) {
        logger.error('Startup check failed', {
            error: error.message,
            requestId: req.requestId,
        });
        res.status(503).json({
            status: 'startup_failed',
            timestamp: new Date().toISOString(),
            error: error.message,
        });
    }
}));
export { router as healthRoutes };
//# sourceMappingURL=health.js.map