import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler.js';
import { validate, faucetRequestSchema, normalizeSuiAddress } from '../validation/schemas.js';
import { checkWalletRateLimit, trackSuccessfulWalletRequest } from '../middleware/rateLimiter.js';
import { requireApiKey } from '../middleware/apiKeyAuth.js';
import { suiService } from '../services/sui.js';
import { redisClient } from '../services/redis.js';
import { databaseService } from '../services/database.js';
import { config } from '../config/index.js';
import { logFaucetRequest, logger } from '../utils/logger.js';
const router = Router();
router.post('/test', (req, res) => {
    console.log('🔥 DEBUG: Test route hit!');
    console.log('🔥 DEBUG: Request body:', req.body);
    res.json({
        success: true,
        message: 'Test route working!',
        body: req.body
    });
});
router.get('/simple', (req, res) => {
    console.log('🔥 DEBUG: Simple route hit!');
    res.json({ message: 'Simple route OK!' });
});
router.post('/request', requireApiKey, validate(faucetRequestSchema, 'body'), asyncHandler(async (req, res) => {
    console.log('🔥 DEBUG: Faucet request handler started');
    console.log('🔥 DEBUG: Request body:', req.body);
    const { address, walletAddress, amount } = req.body;
    const requestId = req.requestId || 'unknown';
    const clientIP = req.ip || 'unknown';
    console.log('🔥 DEBUG: Extracted values:', { address, walletAddress, amount, requestId, clientIP });
    const inputAddress = address || walletAddress;
    if (!inputAddress) {
        console.log('🔥 DEBUG: No address provided');
        return res.status(400).json({
            success: false,
            message: 'Either "address" or "walletAddress" field is required',
            error: { code: 'MISSING_ADDRESS' }
        });
    }
    console.log('🔥 DEBUG: Input address:', inputAddress);
    logger.info(`🔥 DEBUG: Faucet request started`, {
        requestId,
        address: inputAddress,
        amount,
        ip: clientIP,
    });
    const normalizedAddress = normalizeSuiAddress(inputAddress);
    if (!normalizedAddress) {
        return res.status(400).json({
            success: false,
            message: 'Invalid wallet address format',
            error: { code: 'INVALID_ADDRESS' }
        });
    }
    const validAddress = normalizedAddress;
    logger.info(`🔥 DEBUG: Address normalized`, {
        requestId,
        original: inputAddress,
        normalized: validAddress,
    });
    logger.info(`Faucet request received`, {
        requestId,
        walletAddress: validAddress,
        amount: amount || config.sui.defaultAmount,
        ip: clientIP,
    });
    try {
        logger.info(`🔥 DEBUG: Checking wallet rate limit`, { requestId, normalizedAddress: validAddress });
        await checkWalletRateLimit(validAddress, requestId);
        logger.info(`🔥 DEBUG: Wallet rate limit check passed`, { requestId });
        const amountToSend = amount ? BigInt(amount) : BigInt(config.sui.defaultAmount);
        if (!suiService.isReady) {
            throw new Error('Sui service is not ready');
        }
        const walletInfo = await suiService.getWalletInfo();
        if (walletInfo.isLowBalance) {
            logger.warn('Faucet wallet balance is low', {
                requestId,
                balance: walletInfo.balance.toString(),
                minBalance: config.sui.minWalletBalance,
            });
            return res.status(503).json({
                success: false,
                message: '💰 Faucet is temporarily out of funds. Please try again later.',
                error: {
                    code: 'INSUFFICIENT_FAUCET_BALANCE',
                    details: 'The faucet wallet needs to be refunded',
                },
            });
        }
        const result = await suiService.sendTokens(validAddress, amountToSend, requestId);
        if (result.success && result.transactionHash) {
            await trackSuccessfulWalletRequest(validAddress);
            await redisClient.incrementMetric('requests_total');
            await redisClient.incrementMetric('requests_success');
            await redisClient.trackRequest(requestId, {
                walletAddress: validAddress,
                amount: amountToSend.toString(),
                transactionHash: result.transactionHash,
                timestamp: Date.now(),
                status: 'success',
                ip: clientIP,
            });
            logFaucetRequest(requestId, validAddress, amountToSend.toString(), clientIP, true, result.transactionHash);
            try {
                await databaseService.saveMetrics({
                    date: new Date().toISOString().split('T')[0],
                    amount: amountToSend.toString(),
                    status: 'success',
                    transaction_hash: result.transactionHash || undefined,
                });
            }
            catch (dbError) {
                logger.error('Failed to save metrics to database', {
                    requestId,
                    error: dbError.message,
                });
            }
            const amountInSui = Number(amountToSend) / 1_000_000_000;
            return res.status(200).json({
                success: true,
                transactionHash: result.transactionHash,
                amount: amountToSend.toString(),
                message: `✅ Successfully sent ${amountInSui} SUI to ${validAddress}`,
                walletAddress: validAddress,
                faucetAddress: suiService.faucetAddress,
            });
        }
        else {
            await redisClient.incrementMetric('requests_total');
            await redisClient.incrementMetric('requests_failed');
            await redisClient.trackRequest(requestId, {
                walletAddress: validAddress,
                amount: amountToSend.toString(),
                timestamp: Date.now(),
                status: 'failed',
                error: result.error,
                ip: clientIP,
            });
            logFaucetRequest(requestId, validAddress, amountToSend.toString(), clientIP, false, undefined, result.error);
            try {
                await databaseService.saveMetrics({
                    date: new Date().toISOString().split('T')[0],
                    amount: amountToSend.toString(),
                    status: 'failed',
                    error_type: 'network_error',
                });
            }
            catch (dbError) {
                logger.error('Failed to save failed metrics', {
                    requestId,
                    error: dbError.message,
                });
            }
            return res.status(500).json({
                success: false,
                message: `❌ Faucet request failed: ${result.error || 'Unknown error occurred'}`,
                walletAddress: validAddress,
                error: {
                    code: 'FAUCET_TRANSACTION_FAILED',
                    ...(result.error && { details: result.error }),
                },
            });
        }
    }
    catch (error) {
        await redisClient.incrementMetric('requests_total');
        await redisClient.incrementMetric('requests_failed');
        logFaucetRequest(requestId, validAddress, amount || config.sui.defaultAmount, clientIP, false, undefined, error.message);
        if (error.name === 'RateLimitError') {
            try {
                await databaseService.saveMetrics({
                    date: new Date().toISOString().split('T')[0],
                    amount: (amount || config.sui.defaultAmount).toString(),
                    status: 'failed',
                    error_type: 'rate_limit',
                });
            }
            catch (dbError) {
                logger.error('Failed to save rate limit metrics', {
                    requestId,
                    error: dbError.message,
                });
            }
            return res.status(429).json({
                success: false,
                message: `🚫 ${error.message}`,
                retryAfter: error.retryAfter,
                walletAddress: validAddress,
                error: {
                    code: 'RATE_LIMIT_EXCEEDED',
                    details: `Please wait ${error.retryAfter} seconds before requesting again`,
                },
            });
        }
        logger.error('Faucet request failed', {
            requestId,
            error: error.message,
            stack: error.stack,
            walletAddress: validAddress,
        });
        return res.status(500).json({
            success: false,
            message: 'Internal server error occurred while processing faucet request',
            walletAddress: validAddress,
        });
    }
}));
router.get('/status', asyncHandler(async (req, res) => {
    try {
        const walletInfo = await suiService.getWalletInfo();
        const networkInfo = suiService.networkInfo;
        const status = {
            faucetAddress: walletInfo.address,
            network: networkInfo.network,
            rpcUrl: networkInfo.rpcUrl,
            balance: walletInfo.balance.toString(),
            balanceSui: Number(walletInfo.balance) / 1_000_000_000,
            isLowBalance: walletInfo.isLowBalance,
            defaultAmount: config.sui.defaultAmount,
            defaultAmountSui: Number(config.sui.defaultAmount) / 1_000_000_000,
            maxAmount: config.sui.maxAmount,
            maxAmountSui: Number(config.sui.maxAmount) / 1_000_000_000,
            rateLimits: {
                windowMs: config.rateLimits.windowMs,
                maxRequestsPerWallet: config.rateLimits.maxRequestsPerWallet,
                maxRequestsPerIP: config.rateLimits.maxRequestsPerIP,
            },
            isOperational: suiService.isReady && !walletInfo.isLowBalance,
        };
        res.json({
            success: true,
            data: status,
        });
    }
    catch (error) {
        logger.error('Failed to get faucet status', {
            error: error.message,
            requestId: req.requestId,
        });
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve faucet status',
        });
    }
}));
router.get('/info', asyncHandler(async (req, res) => {
    try {
        const networkInfo = suiService.networkInfo;
        const info = {
            network: networkInfo.network,
            faucetAddress: networkInfo.walletAddress,
            defaultAmountSui: Number(config.sui.defaultAmount) / 1_000_000_000,
            maxAmountSui: Number(config.sui.maxAmount) / 1_000_000_000,
            rateLimitWindowHours: config.rateLimits.windowMs / (1000 * 60 * 60),
            endpoints: {
                request: '/api/v1/faucet/request',
                status: '/api/v1/faucet/status',
                health: '/api/v1/health',
            },
        };
        res.json({
            success: true,
            data: info,
        });
    }
    catch (error) {
        logger.error('Failed to get faucet info', {
            error: error.message,
            requestId: req.requestId,
        });
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve faucet information',
        });
    }
}));
export { router as faucetRoutes };
//# sourceMappingURL=faucet.js.map