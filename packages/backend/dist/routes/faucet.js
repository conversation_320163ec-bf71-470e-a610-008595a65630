import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler.js';
import { validate, faucetRequestSchema, normalizeSuiAddress } from '../validation/schemas.js';
import { checkWalletRateLimit } from '../middleware/rateLimiter.js';
import { suiService } from '../services/sui.js';
import { redisClient } from '../services/redis.js';
import { config } from '../config/index.js';
import { logFaucetRequest, logger } from '../utils/logger.js';
const router = Router();
router.post('/request', validate(faucetRequestSchema, 'body'), asyncHandler(async (req, res) => {
    const { walletAddress, amount } = req.body;
    const requestId = req.requestId;
    const clientIP = req.ip;
    const normalizedAddress = normalizeSuiAddress(walletAddress);
    logger.info(`Faucet request received`, {
        requestId,
        walletAddress: normalizedAddress,
        amount: amount || config.sui.defaultAmount,
        ip: clientIP,
    });
    try {
        await checkWalletRateLimit(normalizedAddress, requestId);
        const amountToSend = amount ? BigInt(amount) : BigInt(config.sui.defaultAmount);
        if (!suiService.isReady) {
            throw new Error('Sui service is not ready');
        }
        const walletInfo = await suiService.getWalletInfo();
        if (walletInfo.isLowBalance) {
            logger.warn('Faucet wallet balance is low', {
                requestId,
                balance: walletInfo.balance.toString(),
                minBalance: config.sui.minWalletBalance,
            });
            return res.status(503).json({
                success: false,
                message: 'Faucet is temporarily out of funds. Please try again later.',
            });
        }
        const result = await suiService.sendTokens(normalizedAddress, amountToSend, requestId);
        if (result.success && result.transactionHash) {
            await redisClient.incrementMetric('requests_total');
            await redisClient.incrementMetric('requests_success');
            await redisClient.trackRequest(requestId, {
                walletAddress: normalizedAddress,
                amount: amountToSend.toString(),
                transactionHash: result.transactionHash,
                timestamp: Date.now(),
                status: 'success',
                ip: clientIP,
            });
            logFaucetRequest(requestId, normalizedAddress, amountToSend.toString(), clientIP, true, result.transactionHash);
            const amountInSui = Number(amountToSend) / 1_000_000_000;
            return res.status(200).json({
                success: true,
                transactionHash: result.transactionHash,
                amount: amountToSend.toString(),
                message: `Successfully sent ${amountInSui} SUI to ${normalizedAddress}`,
                walletAddress: normalizedAddress,
                faucetAddress: suiService.faucetAddress,
            });
        }
        else {
            await redisClient.incrementMetric('requests_total');
            await redisClient.incrementMetric('requests_failed');
            await redisClient.trackRequest(requestId, {
                walletAddress: normalizedAddress,
                amount: amountToSend.toString(),
                timestamp: Date.now(),
                status: 'failed',
                error: result.error,
                ip: clientIP,
            });
            logFaucetRequest(requestId, normalizedAddress, amountToSend.toString(), clientIP, false, undefined, result.error);
            return res.status(500).json({
                success: false,
                message: result.error || 'Failed to send tokens',
                walletAddress: normalizedAddress,
            });
        }
    }
    catch (error) {
        await redisClient.incrementMetric('requests_total');
        await redisClient.incrementMetric('requests_failed');
        logFaucetRequest(requestId, normalizedAddress, amount || config.sui.defaultAmount, clientIP, false, undefined, error.message);
        if (error.name === 'RateLimitError') {
            return res.status(429).json({
                success: false,
                message: error.message,
                retryAfter: error.retryAfter,
                walletAddress: normalizedAddress,
            });
        }
        logger.error('Faucet request failed', {
            requestId,
            error: error.message,
            stack: error.stack,
            walletAddress: normalizedAddress,
        });
        return res.status(500).json({
            success: false,
            message: 'Internal server error occurred while processing faucet request',
            walletAddress: normalizedAddress,
        });
    }
}));
router.get('/status', asyncHandler(async (req, res) => {
    try {
        const walletInfo = await suiService.getWalletInfo();
        const networkInfo = suiService.networkInfo;
        const status = {
            faucetAddress: walletInfo.address,
            network: networkInfo.network,
            rpcUrl: networkInfo.rpcUrl,
            balance: walletInfo.balance.toString(),
            balanceSui: Number(walletInfo.balance) / 1_000_000_000,
            isLowBalance: walletInfo.isLowBalance,
            defaultAmount: config.sui.defaultAmount,
            defaultAmountSui: Number(config.sui.defaultAmount) / 1_000_000_000,
            maxAmount: config.sui.maxAmount,
            maxAmountSui: Number(config.sui.maxAmount) / 1_000_000_000,
            rateLimits: {
                windowMs: config.rateLimits.windowMs,
                maxRequestsPerWallet: config.rateLimits.maxRequestsPerWallet,
                maxRequestsPerIP: config.rateLimits.maxRequestsPerIP,
            },
            isOperational: suiService.isReady && !walletInfo.isLowBalance,
        };
        res.json({
            success: true,
            data: status,
        });
    }
    catch (error) {
        logger.error('Failed to get faucet status', {
            error: error.message,
            requestId: req.requestId,
        });
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve faucet status',
        });
    }
}));
router.get('/info', asyncHandler(async (req, res) => {
    try {
        const networkInfo = suiService.networkInfo;
        const info = {
            network: networkInfo.network,
            faucetAddress: networkInfo.walletAddress,
            defaultAmountSui: Number(config.sui.defaultAmount) / 1_000_000_000,
            maxAmountSui: Number(config.sui.maxAmount) / 1_000_000_000,
            rateLimitWindowHours: config.rateLimits.windowMs / (1000 * 60 * 60),
            endpoints: {
                request: '/api/v1/faucet/request',
                status: '/api/v1/faucet/status',
                health: '/api/v1/health',
            },
        };
        res.json({
            success: true,
            data: info,
        });
    }
    catch (error) {
        logger.error('Failed to get faucet info', {
            error: error.message,
            requestId: req.requestId,
        });
        res.status(500).json({
            success: false,
            message: 'Failed to retrieve faucet information',
        });
    }
}));
export { router as faucetRoutes };
//# sourceMappingURL=faucet.js.map