import winston from 'winston';
import { config } from '../config/index.js';
const developmentFormat = winston.format.combine(winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }), winston.format.errors({ stack: true }), winston.format.colorize(), winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
        log += ` ${JSON.stringify(meta)}`;
    }
    if (stack) {
        log += `\n${stack}`;
    }
    return log;
}));
const productionFormat = winston.format.combine(winston.format.timestamp(), winston.format.errors({ stack: true }), winston.format.json());
const transports = [
    new winston.transports.Console({
        format: config.server.environment === 'production' ? productionFormat : developmentFormat,
    }),
];
if (config.logging.file) {
    transports.push(new winston.transports.File({
        filename: config.logging.file,
        format: productionFormat,
        maxsize: 10 * 1024 * 1024,
        maxFiles: 5,
        tailable: true,
    }));
}
export const logger = winston.createLogger({
    level: config.logging.level,
    transports,
    exitOnError: false,
    handleExceptions: false,
    handleRejections: false,
});
export const createRequestLogger = (requestId) => {
    return logger.child({ requestId });
};
export const logRequest = (requestId, method, url, ip, userAgent) => {
    logger.info('Request received', {
        requestId,
        method,
        url,
        ip,
        userAgent,
        type: 'request',
    });
};
export const logResponse = (requestId, statusCode, responseTime, contentLength) => {
    logger.info('Request completed', {
        requestId,
        statusCode,
        responseTime,
        contentLength,
        type: 'response',
    });
};
export const logFaucetRequest = (requestId, walletAddress, amount, ip, success, transactionHash, error) => {
    const logData = {
        requestId,
        walletAddress,
        amount,
        ip,
        success,
        type: 'faucet_request',
        ...(transactionHash && { transactionHash }),
        ...(error && { error }),
    };
    if (success) {
        logger.info('Faucet request successful', logData);
    }
    else {
        logger.warn('Faucet request failed', logData);
    }
};
export const logRateLimit = (requestId, ip, walletAddress, limitType) => {
    logger.warn('Rate limit exceeded', {
        requestId,
        ip,
        walletAddress,
        limitType,
        type: 'rate_limit',
    });
};
export const logSuiTransaction = (requestId, transactionHash, fromAddress, toAddress, amount, gasUsed) => {
    logger.info('Sui transaction executed', {
        requestId,
        transactionHash,
        fromAddress,
        toAddress,
        amount,
        gasUsed,
        type: 'sui_transaction',
    });
};
export const logWalletBalance = (balance, threshold, isLow) => {
    const logLevel = isLow ? 'warn' : 'info';
    logger[logLevel]('Wallet balance check', {
        balance,
        threshold,
        isLow,
        type: 'wallet_balance',
    });
};
export const logError = (error, context) => {
    logger.error('Application error', {
        message: error.message,
        stack: error.stack,
        name: error.name,
        ...context,
        type: 'error',
    });
};
export const logPerformance = (operation, duration, success, metadata) => {
    logger.info('Performance metric', {
        operation,
        duration,
        success,
        ...metadata,
        type: 'performance',
    });
};
export const logHealthCheck = (component, status, details) => {
    const logLevel = status === 'healthy' ? 'info' : 'error';
    logger[logLevel]('Health check', {
        component,
        status,
        ...details,
        type: 'health_check',
    });
};
export const logSecurityEvent = (event, ip, severity, details) => {
    logger.warn('Security event', {
        event,
        ip,
        severity,
        ...details,
        type: 'security',
    });
};
export default logger;
//# sourceMappingURL=logger.js.map