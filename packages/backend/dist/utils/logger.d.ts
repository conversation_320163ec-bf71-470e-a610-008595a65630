import winston from 'winston';
export declare const logger: winston.Logger;
export declare const createRequestLogger: (requestId: string) => winston.Logger;
export declare const logRequest: (requestId: string, method: string, url: string, ip: string, userAgent?: string) => void;
export declare const logResponse: (requestId: string, statusCode: number, responseTime: number, contentLength?: number) => void;
export declare const logFaucetRequest: (requestId: string, walletAddress: string, amount: string, ip: string, success: boolean, transactionHash?: string, error?: string) => void;
export declare const logRateLimit: (requestId: string, ip: string, walletAddress?: string, limitType: "ip" | "wallet" | "global") => void;
export declare const logSuiTransaction: (requestId: string, transactionHash: string, fromAddress: string, toAddress: string, amount: string, gasUsed?: string) => void;
export declare const logWalletBalance: (balance: string, threshold: string, isLow: boolean) => void;
export declare const logError: (error: Error, context?: Record<string, any>) => void;
export declare const logPerformance: (operation: string, duration: number, success: boolean, metadata?: Record<string, any>) => void;
export declare const logHealthCheck: (component: string, status: "healthy" | "unhealthy", details?: Record<string, any>) => void;
export declare const logSecurityEvent: (event: string, ip: string, severity: "low" | "medium" | "high", details?: Record<string, any>) => void;
export default logger;
//# sourceMappingURL=logger.d.ts.map