import Joi from 'joi';
export declare const faucetRequestSchema: Joi.ObjectSchema<any>;
export declare const queryParamsSchema: Joi.ObjectSchema<any>;
export declare const adminAuthSchema: Joi.ObjectSchema<any>;
export declare const rateLimitResetSchema: Joi.ObjectSchema<any>;
export declare const metricsQuerySchema: Joi.ObjectSchema<any>;
export declare const healthQuerySchema: Joi.ObjectSchema<any>;
export declare const validate: (schema: Joi.ObjectSchema, property?: "body" | "query" | "params") => (req: any, res: any, next: any) => any;
export declare const isValidSuiAddress: (address: string) => boolean;
export declare const normalizeSuiAddress: (address: string) => string;
export declare const validateAmount: (amount: string) => {
    isValid: boolean;
    value?: bigint;
    error?: string;
};
//# sourceMappingURL=schemas.d.ts.map