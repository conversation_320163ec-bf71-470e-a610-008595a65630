export interface FaucetConfig {
    sui: {
        network: 'testnet' | 'devnet';
        rpcUrl: string;
        privateKey: string;
        defaultAmount: string;
        maxAmount: string;
        minWalletBalance: string;
    };
    rateLimits: {
        windowMs: number;
        maxRequestsPerWindow: number;
        maxRequestsPerWallet: number;
        maxRequestsPerIP: number;
        skipSuccessfulRequests: boolean;
        skipFailedRequests: boolean;
    };
    auth: {
        apiKey: string;
        adminUsername: string;
        adminPassword: string;
    };
    server: {
        port: number;
        corsOrigins: string[];
        requestTimeout: number;
        environment: 'development' | 'production' | 'test';
    };
    redis: {
        url: string;
        keyPrefix: string;
        ttl: number;
    };
    database: {
        url: string;
        ssl: boolean;
    };
    logging: {
        level: string;
        format: 'json' | 'simple';
        file?: string;
    };
}
export declare const config: FaucetConfig;
export declare const validateConfig: () => void;
//# sourceMappingURL=index.d.ts.map