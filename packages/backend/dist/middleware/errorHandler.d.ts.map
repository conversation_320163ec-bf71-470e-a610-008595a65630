{"version": 3, "file": "errorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAI1D,MAAM,WAAW,QAAS,SAAQ,KAAK;IACrC,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;CAC/B;AAED,qBAAa,eAAgB,SAAQ,KAAK;IAIJ,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAHjE,UAAU,SAAO;IACjB,IAAI,SAAsB;gBAEd,OAAO,EAAE,MAAM,EAAS,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,YAAA;CAIlE;AAED,qBAAa,cAAe,SAAQ,KAAK;IAIH,UAAU,CAAC,EAAE,MAAM;IAHvD,UAAU,SAAO;IACjB,IAAI,SAAyB;gBAEjB,OAAO,EAAE,MAAM,EAAS,UAAU,CAAC,EAAE,MAAM,YAAA;CAIxD;AAED,qBAAa,QAAS,SAAQ,KAAK;IAIG,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC;IAHjE,UAAU,SAAO;IACjB,IAAI,SAAe;gBAEP,OAAO,EAAE,MAAM,EAAS,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,YAAA;CAIlE;AAED,qBAAa,wBAAyB,SAAQ,KAAK;IACjD,UAAU,SAAO;IACjB,IAAI,SAA0B;gBAElB,OAAO,GAAE,MAA0C;CAIhE;AAED,qBAAa,uBAAwB,SAAQ,KAAK;IAChD,UAAU,SAAO;IACjB,IAAI,SAAyB;gBAEjB,OAAO,GAAE,MAA0C;CAIhE;AAqBD,eAAO,MAAM,YAAY,GACvB,KAAK,QAAQ,EACb,KAAK,OAAO,EACZ,KAAK,QAAQ,EACb,MAAM,YAAY,KACjB,IAsEF,CAAC;AA+FF,eAAO,MAAM,YAAY,GAAI,IAAI,QAAQ,MAC/B,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAGxD,CAAC;AAGF,eAAO,MAAM,eAAe,GAAI,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,KAAG,IAKjF,CAAC"}