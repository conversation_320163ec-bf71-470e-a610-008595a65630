import { logger } from '../utils/logger.js';
import { config } from '../config/index.js';
export const apiKeyAuth = (req, res, next) => {
    const requestId = req.requestId;
    const clientIP = req.ip || 'unknown';
    let apiKey;
    const xApiKey = req.headers['x-api-key'];
    if (xApiKey) {
        apiKey = xApiKey;
    }
    const authHeader = req.headers.authorization;
    if (!apiKey && authHeader && authHeader.startsWith('Bearer ')) {
        apiKey = authHeader.substring(7);
    }
    if (!apiKey && authHeader && !authHeader.startsWith('Bearer ')) {
        apiKey = authHeader;
    }
    if (!apiKey) {
        logger.warn('🚫 Missing API key', {
            requestId,
            ip: clientIP,
            path: req.path,
        });
        res.status(401).json({
            success: false,
            message: '🚫 Missing API key. Please provide API key.',
            error: {
                code: 'MISSING_API_KEY',
                details: 'API key required',
            },
        });
        return;
    }
    if (apiKey !== config.auth.apiKey) {
        logger.warn('🚫 Invalid API key', {
            requestId,
            ip: clientIP,
            path: req.path,
            providedKey: apiKey.substring(0, 3) + '***',
        });
        res.status(401).json({
            success: false,
            message: '🚫 Invalid API key. Access denied.',
            error: {
                code: 'INVALID_API_KEY',
                details: 'The provided API key is incorrect',
            },
        });
        return;
    }
    logger.info('✅ API key validated', {
        requestId,
        ip: clientIP,
        path: req.path,
    });
    next();
};
export const requireApiKey = apiKeyAuth;
//# sourceMappingURL=apiKeyAuth.js.map