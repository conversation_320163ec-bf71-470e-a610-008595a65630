{"version": 3, "file": "apiKeyAuth.js", "sourceRoot": "", "sources": ["../../src/middleware/apiKeyAuth.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAG5C,MAAM,CAAC,MAAM,UAAU,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IAClF,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS,CAAC;IAChC,MAAM,QAAQ,GAAG,GAAG,CAAC,EAAE,IAAI,SAAS,CAAC;IAGrC,IAAI,MAA0B,CAAC;IAG/B,MAAM,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAW,CAAC;IACnD,IAAI,OAAO,EAAE,CAAC;QACZ,MAAM,GAAG,OAAO,CAAC;IACnB,CAAC;IAGD,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;IAC7C,IAAI,CAAC,MAAM,IAAI,UAAU,IAAI,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QAC9D,MAAM,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;IAGD,IAAI,CAAC,MAAM,IAAI,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;QAC/D,MAAM,GAAG,UAAU,CAAC;IACtB,CAAC;IAED,IAAI,CAAC,MAAM,EAAE,CAAC;QACZ,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAChC,SAAS;YACT,EAAE,EAAE,QAAQ;YACZ,IAAI,EAAE,GAAG,CAAC,IAAI;SACf,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,6CAA6C;YACtD,KAAK,EAAE;gBACL,IAAI,EAAE,iBAAiB;gBACvB,OAAO,EAAE,kBAAkB;aAC5B;SACF,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IAGD,IAAI,MAAM,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;QAClC,MAAM,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAChC,SAAS;YACT,EAAE,EAAE,QAAQ;YACZ,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,WAAW,EAAE,MAAM,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK;SAC5C,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,oCAAoC;YAC7C,KAAK,EAAE;gBACL,IAAI,EAAE,iBAAiB;gBACvB,OAAO,EAAE,mCAAmC;aAC7C;SACF,CAAC,CAAC;QACH,OAAO;IACT,CAAC;IAGD,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;QACjC,SAAS;QACT,EAAE,EAAE,QAAQ;QACZ,IAAI,EAAE,GAAG,CAAC,IAAI;KACf,CAAC,CAAC;IAEH,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAGF,MAAM,CAAC,MAAM,aAAa,GAAG,UAAU,CAAC"}