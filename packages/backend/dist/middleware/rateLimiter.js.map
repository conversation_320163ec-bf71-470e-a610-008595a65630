{"version": 3, "file": "rateLimiter.js", "sourceRoot": "", "sources": ["../../src/middleware/rateLimiter.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACnD,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAClD,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAG5C,MAAM,YAAY,GAAG;IACnB,WAAW;IACX,KAAK;IACL,kBAAkB;CAGnB,CAAC;AAGF,MAAM,iBAAiB,GAAG,CAAC,GAAY,EAAW,EAAE;IAClD,MAAM,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,UAAU,CAAC,aAAa,IAAI,EAAE,CAAC;IACxD,MAAM,SAAS,GAAG,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC;IAG9C,IAAI,YAAY,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;QAC9B,OAAO,IAAI,CAAC;IACd,CAAC;IAGD,IAAI,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;QAC/E,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC;AAGF,IAAI,aAAa,GAA4B,IAAI,CAAC;AAClD,IAAI,iBAAiB,GAA4B,IAAI,CAAC;AAMtD,MAAM,WAAW,GAAG,CAAC,GAAY,EAAU,EAAE;IAC3C,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAW,CAAC;IAC3D,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAW,CAAC;IAClD,MAAM,aAAa,GAAG,GAAG,CAAC,UAAU,EAAE,aAAa,IAAI,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC;IAEjF,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,SAAS,CAAC;IACtD,CAAC;IAED,IAAI,MAAM,EAAE,CAAC;QACX,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,OAAO,aAAa,IAAI,SAAS,CAAC;AACpC,CAAC,CAAC;AAGF,MAAM,eAAe,GAAG,IAAI,GAAG,EAAgD,CAAC;AAChF,MAAM,kBAAkB,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC;AAE5F,MAAM,eAAe,GAAG,CAAC,QAAgB,EAA6C,EAAE;IACtF,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IACvB,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;IAG5C,IAAI,GAAG,GAAG,kBAAkB,CAAC,SAAS,EAAE,CAAC;QACvC,kBAAkB,CAAC,KAAK,GAAG,CAAC,CAAC;QAC7B,kBAAkB,CAAC,SAAS,GAAG,GAAG,GAAG,QAAQ,CAAC;IAChD,CAAC;IAGD,IAAI,kBAAkB,CAAC,KAAK,IAAI,MAAM,CAAC,UAAU,CAAC,oBAAoB,EAAE,CAAC;QACvE,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,kBAAkB,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;QAC1E,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;IACxC,CAAC;IAGD,MAAM,MAAM,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC7C,IAAI,CAAC,MAAM,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;QACtC,eAAe,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,GAAG,QAAQ,EAAE,CAAC,CAAC;IACzE,CAAC;IAED,MAAM,aAAa,GAAG,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC;IAGrD,IAAI,aAAa,CAAC,KAAK,IAAI,MAAM,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;QAC9D,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;QACrE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC;IACxC,CAAC;IAGD,kBAAkB,CAAC,KAAK,EAAE,CAAC;IAC3B,aAAa,CAAC,KAAK,EAAE,CAAC;IAEtB,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;AAC3B,CAAC,CAAC;AAGF,MAAM,CAAC,MAAM,WAAW,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IAClG,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;QAClC,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC;QAG7C,IAAI,iBAAiB,CAAC,GAAG,CAAC,EAAE,CAAC;YAC3B,OAAO,CAAC,GAAG,CAAC,2DAA2D,QAAQ,EAAE,CAAC,CAAC;YACnF,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAGD,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC5B,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC1B,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAC3B,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC1B,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YAC9B,GAAG,CAAC,IAAI,KAAK,GAAG;YAChB,GAAG,CAAC,IAAI,KAAK,OAAO,EAAE,CAAC;YACzB,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,qCAAqC,QAAQ,OAAO,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC;QAG5E,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACpD,MAAM,eAAe,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;QAElD,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;YAC7B,MAAM,UAAU,GAAG,eAAe,CAAC,UAAU,IAAI,EAAE,CAAC;YAEpD,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;YAExC,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC9C,GAAG,CAAC,GAAG,CAAC,mBAAmB,EAAE,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC5E,GAAG,CAAC,GAAG,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;YAEtC,MAAM,IAAI,cAAc,CAAC,4CAA4C,UAAU,WAAW,EAAE,UAAU,CAAC,CAAC;QAC1G,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,sCAAsC,QAAQ,EAAE,CAAC,CAAC;QAC9D,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAIF,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAgD,CAAC;AAEpF,MAAM,CAAC,MAAM,oBAAoB,GAAG,KAAK,EAAE,aAAqB,EAAE,SAAiB,EAAiB,EAAE;IACpG,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,8CAA8C,aAAa,EAAE,CAAC,CAAC;QAE3E,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;QAC5C,MAAM,YAAY,GAAG,MAAM,CAAC,UAAU,CAAC,oBAAoB,CAAC;QAE5D,OAAO,CAAC,GAAG,CAAC,iBAAiB,GAAG,cAAc,QAAQ,kBAAkB,YAAY,EAAE,CAAC,CAAC;QAGxF,MAAM,UAAU,GAAG,mBAAmB,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC1D,IAAI,CAAC,UAAU,IAAI,GAAG,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC;YAC9C,mBAAmB,CAAC,GAAG,CAAC,aAAa,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,GAAG,QAAQ,EAAE,CAAC,CAAC;QAClF,CAAC;QAED,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,GAAG,CAAC,aAAa,CAAE,CAAC;QAClE,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,iBAAiB,CAAC,CAAC;QAG/D,IAAI,iBAAiB,CAAC,KAAK,IAAI,YAAY,EAAE,CAAC;YAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,iBAAiB,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;YAEzE,YAAY,CAAC,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;YAE5D,MAAM,IAAI,cAAc,CACtB,UAAU,aAAa,yCAAyC,UAAU,mCAAmC,EAC7G,UAAU,CACX,CAAC;QACJ,CAAC;QAGD,iBAAiB,CAAC,KAAK,EAAE,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAEjF,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;YAC5C,SAAS;YACT,aAAa;YACb,KAAK,EAAE,iBAAiB,CAAC,KAAK;YAC9B,YAAY;SACb,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,cAAc,EAAE,CAAC;YACpC,MAAM,KAAK,CAAC;QACd,CAAC;QAGD,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;AACH,CAAC,CAAC;AAGF,MAAM,CAAC,MAAM,4BAA4B,GAAG,KAAK,EAAE,aAAqB,EAAiB,EAAE;IACzF,IAAI,CAAC;QACH,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,WAAW,CAAC,kBAAkB,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;IAC3D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;IACpE,CAAC;AACH,CAAC,CAAC;AAGF,MAAM,CAAC,MAAM,cAAc,GAAG,KAAK,EAAE,UAAkB,EAAE,IAAgC,EAAiB,EAAE;IAC1G,IAAI,CAAC;QACH,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,IAAI;gBACP,IAAI,aAAa,EAAE,CAAC;oBAClB,MAAM,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBACzC,CAAC;gBACD,MAAM;YACR,KAAK,QAAQ;gBACX,MAAM,WAAW,CAAC,GAAG,CAAC,WAAW,UAAU,EAAE,CAAC,CAAC;gBAC/C,MAAM;YACR,KAAK,QAAQ;gBACX,IAAI,iBAAiB,EAAE,CAAC;oBACtB,MAAM,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC3C,CAAC;gBACD,MAAM;QACV,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,IAAI,mBAAmB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9E,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAGF,MAAM,CAAC,MAAM,kBAAkB,GAAG,KAAK,EAAE,UAAkB,EAAE,IAAgC,EAAE,EAAE;IAC/F,IAAI,CAAC;QACH,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,IAAI;gBACP,IAAI,aAAa,EAAE,CAAC;oBAClB,MAAM,GAAG,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBAChD,OAAO;wBACL,KAAK,EAAE,MAAM,CAAC,UAAU,CAAC,gBAAgB;wBACzC,SAAS,EAAE,GAAG,EAAE,eAAe,IAAI,MAAM,CAAC,UAAU,CAAC,gBAAgB;wBACrE,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI;qBAChE,CAAC;gBACJ,CAAC;gBACD,MAAM;YACR,KAAK,QAAQ;gBACX,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;gBACvE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACvB,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;gBAC5C,MAAM,UAAU,GAAG,CAAC,WAAW,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC,IAAI,QAAQ,CAAC;gBAEnE,OAAO;oBACL,KAAK,EAAE,CAAC;oBACR,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7B,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI;iBACjE,CAAC;YACJ,KAAK,QAAQ;gBACX,IAAI,iBAAiB,EAAE,CAAC;oBACtB,MAAM,GAAG,GAAG,MAAM,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBAClD,OAAO;wBACL,KAAK,EAAE,MAAM,CAAC,UAAU,CAAC,oBAAoB;wBAC7C,SAAS,EAAE,GAAG,EAAE,eAAe,IAAI,MAAM,CAAC,UAAU,CAAC,oBAAoB;wBACzE,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI;qBAChE,CAAC;gBACJ,CAAC;gBACD,MAAM;QACV,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iBAAiB,IAAI,0BAA0B,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;QACnF,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC,CAAC"}