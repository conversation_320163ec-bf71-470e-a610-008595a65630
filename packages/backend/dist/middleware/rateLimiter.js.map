{"version": 3, "file": "rateLimiter.js", "sourceRoot": "", "sources": ["../../src/middleware/rateLimiter.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,gBAAgB,EAAE,MAAM,uBAAuB,CAAC;AACzD,OAAO,EAAE,WAAW,EAAE,MAAM,sBAAsB,CAAC;AACnD,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AACnD,OAAO,EAAE,YAAY,EAAE,MAAM,oBAAoB,CAAC;AAGlD,IAAI,aAA+B,CAAC;AACpC,IAAI,iBAAmC,CAAC;AAGxC,MAAM,sBAAsB,GAAG,GAAG,EAAE;IAElC,aAAa,GAAG,IAAI,gBAAgB,CAAC;QACnC,WAAW,EAAE,WAAW,CAAC,SAAS;QAClC,SAAS,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,WAAW;QAC/C,MAAM,EAAE,MAAM,CAAC,UAAU,CAAC,gBAAgB;QAC1C,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;QACvD,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC5D,UAAU,EAAE,IAAI;KACjB,CAAC,CAAC;IAGH,iBAAiB,GAAG,IAAI,gBAAgB,CAAC;QACvC,WAAW,EAAE,WAAW,CAAC,SAAS;QAClC,SAAS,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,eAAe;QACnD,MAAM,EAAE,MAAM,CAAC,UAAU,CAAC,oBAAoB;QAC9C,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;QACvD,aAAa,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC5D,UAAU,EAAE,IAAI;KACjB,CAAC,CAAC;AACL,CAAC,CAAC;AAGF,MAAM,WAAW,GAAG,CAAC,GAAY,EAAU,EAAE;IAC3C,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAW,CAAC;IAC3D,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,CAAC,WAAW,CAAW,CAAC;IAClD,MAAM,aAAa,GAAG,GAAG,CAAC,UAAU,EAAE,aAAa,IAAI,GAAG,CAAC,MAAM,EAAE,aAAa,CAAC;IAEjF,IAAI,SAAS,EAAE,CAAC;QACd,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;IACxC,CAAC;IAED,IAAI,MAAM,EAAE,CAAC;QACX,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,OAAO,aAAa,IAAI,SAAS,CAAC;AACpC,CAAC,CAAC;AAGF,MAAM,CAAC,MAAM,WAAW,GAAG,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;IAClG,IAAI,CAAC;QAEH,IAAI,CAAC,aAAa,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACzC,sBAAsB,EAAE,CAAC;QAC3B,CAAC;QAED,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC;QAClC,MAAM,SAAS,GAAG,GAAG,CAAC,SAAS,IAAI,SAAS,CAAC;QAG7C,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YAClE,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAGD,IAAI,CAAC;YACH,MAAM,iBAAiB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC5C,CAAC;QAAC,OAAO,cAAc,EAAE,CAAC;YACxB,IAAI,cAAc,YAAY,KAAK,EAAE,CAAC;gBACpC,MAAM,cAAc,CAAC;YACvB,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvE,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YAEvD,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC9C,GAAG,CAAC,GAAG,CAAC,mBAAmB,EAAE,MAAM,CAAC,UAAU,CAAC,oBAAoB,CAAC,QAAQ,EAAE,CAAC,CAAC;YAChF,GAAG,CAAC,GAAG,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;YACtC,GAAG,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;YAE/F,MAAM,IAAI,cAAc,CAAC,qDAAqD,EAAE,UAAU,CAAC,CAAC;QAC9F,CAAC;QAGD,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,MAAM,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;YAG7D,GAAG,CAAC,GAAG,CAAC,mBAAmB,EAAE,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC5E,GAAG,CAAC,GAAG,CAAC,uBAAuB,EAAE,cAAc,CAAC,eAAe,EAAE,QAAQ,EAAE,IAAI,GAAG,CAAC,CAAC;YACpF,GAAG,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;QAEjG,CAAC;QAAC,OAAO,cAAc,EAAE,CAAC;YACxB,IAAI,cAAc,YAAY,KAAK,EAAE,CAAC;gBACpC,MAAM,cAAc,CAAC;YACvB,CAAC;YAED,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvE,YAAY,CAAC,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAEnD,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC9C,GAAG,CAAC,GAAG,CAAC,mBAAmB,EAAE,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC5E,GAAG,CAAC,GAAG,CAAC,uBAAuB,EAAE,GAAG,CAAC,CAAC;YACtC,GAAG,CAAC,GAAG,CAAC,mBAAmB,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,cAAc,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC;YAE/F,MAAM,IAAI,cAAc,CAAC,6BAA6B,QAAQ,2BAA2B,EAAE,UAAU,CAAC,CAAC;QACzG,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAGF,MAAM,CAAC,MAAM,oBAAoB,GAAG,KAAK,EAAE,aAAqB,EAAE,SAAiB,EAAiB,EAAE;IACpG,IAAI,CAAC;QAEH,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC;QAC1E,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;QAE5C,IAAI,WAAW,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC,GAAG,QAAQ,EAAE,CAAC;YAClD,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,QAAQ,GAAG,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;YAEtE,YAAY,CAAC,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;YAE5D,MAAM,IAAI,cAAc,CACtB,UAAU,aAAa,uDAAuD,UAAU,WAAW,EACnG,UAAU,CACX,CAAC;QACJ,CAAC;QAGD,MAAM,WAAW,CAAC,kBAAkB,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;IAE3D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,cAAc,EAAE,CAAC;YACpC,MAAM,KAAK,CAAC;QACd,CAAC;QAGD,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;IAC5D,CAAC;AACH,CAAC,CAAC;AAGF,MAAM,CAAC,MAAM,cAAc,GAAG,KAAK,EAAE,UAAkB,EAAE,IAAgC,EAAiB,EAAE;IAC1G,IAAI,CAAC;QACH,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,IAAI;gBACP,IAAI,aAAa,EAAE,CAAC;oBAClB,MAAM,aAAa,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBACzC,CAAC;gBACD,MAAM;YACR,KAAK,QAAQ;gBACX,MAAM,WAAW,CAAC,GAAG,CAAC,WAAW,UAAU,EAAE,CAAC,CAAC;gBAC/C,MAAM;YACR,KAAK,QAAQ;gBACX,IAAI,iBAAiB,EAAE,CAAC;oBACtB,MAAM,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC3C,CAAC;gBACD,MAAM;QACV,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,IAAI,mBAAmB,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;QAC9E,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAGF,MAAM,CAAC,MAAM,kBAAkB,GAAG,KAAK,EAAE,UAAkB,EAAE,IAAgC,EAAE,EAAE;IAC/F,IAAI,CAAC;QACH,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,IAAI;gBACP,IAAI,aAAa,EAAE,CAAC;oBAClB,MAAM,GAAG,GAAG,MAAM,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;oBAChD,OAAO;wBACL,KAAK,EAAE,MAAM,CAAC,UAAU,CAAC,gBAAgB;wBACzC,SAAS,EAAE,GAAG,EAAE,eAAe,IAAI,MAAM,CAAC,UAAU,CAAC,gBAAgB;wBACrE,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI;qBAChE,CAAC;gBACJ,CAAC;gBACD,MAAM;YACR,KAAK,QAAQ;gBACX,MAAM,WAAW,GAAG,MAAM,WAAW,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC;gBACvE,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACvB,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;gBAC5C,MAAM,UAAU,GAAG,CAAC,WAAW,IAAI,CAAC,GAAG,GAAG,WAAW,CAAC,IAAI,QAAQ,CAAC;gBAEnE,OAAO;oBACL,KAAK,EAAE,CAAC;oBACR,SAAS,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC7B,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI;iBACjE,CAAC;YACJ,KAAK,QAAQ;gBACX,IAAI,iBAAiB,EAAE,CAAC;oBACtB,MAAM,GAAG,GAAG,MAAM,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;oBAClD,OAAO;wBACL,KAAK,EAAE,MAAM,CAAC,UAAU,CAAC,oBAAoB;wBAC7C,SAAS,EAAE,GAAG,EAAE,eAAe,IAAI,MAAM,CAAC,UAAU,CAAC,oBAAoB;wBACzE,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,IAAI;qBAChE,CAAC;gBACJ,CAAC;gBACD,MAAM;QACV,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,iBAAiB,IAAI,0BAA0B,UAAU,GAAG,EAAE,KAAK,CAAC,CAAC;QACnF,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC,CAAC"}