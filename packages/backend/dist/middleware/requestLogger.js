import { v4 as uuidv4 } from 'uuid';
import { logRequest, logResponse } from '../utils/logger.js';
export const requestLogger = (req, res, next) => {
    const requestId = req.headers['x-request-id'] || uuidv4();
    req.requestId = requestId;
    req.startTime = Date.now();
    res.setHeader('X-Request-ID', requestId);
    const getClientIP = (req) => {
        const forwarded = req.headers['x-forwarded-for'];
        const realIP = req.headers['x-real-ip'];
        const remoteAddress = req.connection?.remoteAddress || req.socket?.remoteAddress;
        if (forwarded) {
            return forwarded.split(',')[0].trim();
        }
        if (realIP) {
            return realIP;
        }
        return remoteAddress || 'unknown';
    };
    const clientIP = getClientIP(req);
    const userAgent = req.get('User-Agent');
    logRequest(requestId, req.method, req.originalUrl || req.url, clientIP, userAgent);
    const originalEnd = res.end;
    res.end = function (chunk, encoding, cb) {
        const responseTime = Date.now() - req.startTime;
        const contentLength = res.get('Content-Length');
        const contentLengthNum = contentLength ? parseInt(contentLength) : undefined;
        logResponse(requestId, res.statusCode, responseTime, contentLengthNum);
        originalEnd.call(this, chunk, encoding, cb);
    };
    next();
};
//# sourceMappingURL=requestLogger.js.map