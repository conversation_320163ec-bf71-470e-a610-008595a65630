import { logError } from '../utils/logger.js';
import { config } from '../config/index.js';
export class ValidationError extends Error {
    details;
    statusCode = 400;
    code = 'VALIDATION_ERROR';
    constructor(message, details) {
        super(message);
        this.details = details;
        this.name = 'ValidationError';
    }
}
export class RateLimitError extends Error {
    retryAfter;
    statusCode = 429;
    code = 'RATE_LIMIT_EXCEEDED';
    constructor(message, retryAfter) {
        super(message);
        this.retryAfter = retryAfter;
        this.name = 'RateLimitError';
    }
}
export class SuiError extends Error {
    details;
    statusCode = 500;
    code = 'SUI_ERROR';
    constructor(message, details) {
        super(message);
        this.details = details;
        this.name = 'SuiError';
    }
}
export class InsufficientBalanceError extends Error {
    statusCode = 503;
    code = 'INSUFFICIENT_BALANCE';
    constructor(message = 'Faucet has insufficient balance') {
        super(message);
        this.name = 'InsufficientBalanceError';
    }
}
export class ServiceUnavailableError extends Error {
    statusCode = 503;
    code = 'SERVICE_UNAVAILABLE';
    constructor(message = 'Service temporarily unavailable') {
        super(message);
        this.name = 'ServiceUnavailableError';
    }
}
export const errorHandler = (err, req, res, next) => {
    if (res.headersSent) {
        return next(err);
    }
    const requestId = req.headers['x-request-id'] || 'unknown';
    logError(err, {
        requestId,
        method: req.method,
        url: req.url,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
    });
    let statusCode = err.statusCode || 500;
    if (err.name === 'ValidationError') {
        statusCode = 400;
    }
    else if (err.name === 'UnauthorizedError') {
        statusCode = 401;
    }
    else if (err.name === 'ForbiddenError') {
        statusCode = 403;
    }
    else if (err.name === 'NotFoundError') {
        statusCode = 404;
    }
    else if (err.name === 'RateLimitError') {
        statusCode = 429;
    }
    else if (err.name === 'InsufficientBalanceError' || err.name === 'ServiceUnavailableError') {
        statusCode = 503;
    }
    const errorCode = err.code || getErrorCodeFromStatus(statusCode);
    const errorResponse = {
        success: false,
        error: {
            code: errorCode,
            message: getErrorMessage(err, statusCode),
            requestId,
        },
    };
    if (err.details) {
        errorResponse.error.details = err.details;
    }
    if (err instanceof RateLimitError && err.retryAfter) {
        errorResponse.error.retryAfter = err.retryAfter;
        res.set('Retry-After', err.retryAfter.toString());
    }
    if (config.server.environment === 'development') {
        errorResponse.error.stack = err.stack;
    }
    res.status(statusCode);
    res.set('Content-Type', 'application/json');
    res.json(errorResponse);
};
function getErrorCodeFromStatus(statusCode) {
    switch (statusCode) {
        case 400:
            return 'BAD_REQUEST';
        case 401:
            return 'UNAUTHORIZED';
        case 403:
            return 'FORBIDDEN';
        case 404:
            return 'NOT_FOUND';
        case 409:
            return 'CONFLICT';
        case 422:
            return 'UNPROCESSABLE_ENTITY';
        case 429:
            return 'RATE_LIMIT_EXCEEDED';
        case 500:
            return 'INTERNAL_SERVER_ERROR';
        case 502:
            return 'BAD_GATEWAY';
        case 503:
            return 'SERVICE_UNAVAILABLE';
        case 504:
            return 'GATEWAY_TIMEOUT';
        default:
            return 'UNKNOWN_ERROR';
    }
}
function getErrorMessage(err, statusCode) {
    if (err.message && !isInternalError(statusCode)) {
        return err.message;
    }
    if (config.server.environment === 'production' && isInternalError(statusCode)) {
        switch (statusCode) {
            case 500:
                return 'An internal server error occurred';
            case 502:
                return 'Bad gateway';
            case 503:
                return 'Service temporarily unavailable';
            case 504:
                return 'Gateway timeout';
            default:
                return 'An error occurred while processing your request';
        }
    }
    return err.message || getDefaultMessage(statusCode);
}
function isInternalError(statusCode) {
    return statusCode >= 500;
}
function getDefaultMessage(statusCode) {
    switch (statusCode) {
        case 400:
            return 'Bad request';
        case 401:
            return 'Unauthorized';
        case 403:
            return 'Forbidden';
        case 404:
            return 'Not found';
        case 409:
            return 'Conflict';
        case 422:
            return 'Unprocessable entity';
        case 429:
            return 'Too many requests';
        case 500:
            return 'Internal server error';
        case 502:
            return 'Bad gateway';
        case 503:
            return 'Service unavailable';
        case 504:
            return 'Gateway timeout';
        default:
            return 'An error occurred';
    }
}
export const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
export const notFoundHandler = (req, res, next) => {
    const error = new Error(`Route not found: ${req.method} ${req.path}`);
    error.statusCode = 404;
    error.code = 'NOT_FOUND';
    next(error);
};
//# sourceMappingURL=errorHandler.js.map