import { Request, Response, NextFunction } from 'express';
export interface ApiError extends Error {
    statusCode?: number;
    code?: string;
    details?: Record<string, any>;
}
export declare class ValidationError extends Error {
    details?: Record<string, any>;
    statusCode: number;
    code: string;
    constructor(message: string, details?: Record<string, any>);
}
export declare class RateLimitError extends Error {
    retryAfter?: number;
    statusCode: number;
    code: string;
    constructor(message: string, retryAfter?: number);
}
export declare class SuiError extends Error {
    details?: Record<string, any>;
    statusCode: number;
    code: string;
    constructor(message: string, details?: Record<string, any>);
}
export declare class InsufficientBalanceError extends Error {
    statusCode: number;
    code: string;
    constructor(message?: string);
}
export declare class ServiceUnavailableError extends Error {
    statusCode: number;
    code: string;
    constructor(message?: string);
}
export declare const errorHandler: (err: ApiError, req: Request, res: Response, next: NextFunction) => void;
export declare const asyncHandler: (fn: Function) => (req: Request, res: Response, next: NextFunction) => void;
export declare const notFoundHandler: (req: Request, _res: Response, next: NextFunction) => void;
//# sourceMappingURL=errorHandler.d.ts.map