import { Request, Response, NextFunction } from 'express';
export declare const rateLimiter: (req: Request, res: Response, next: NextFunction) => Promise<void>;
export declare const checkWalletRateLimit: (walletAddress: string, requestId: string) => Promise<void>;
export declare const resetRateLimit: (identifier: string, type: "ip" | "wallet" | "global") => Promise<void>;
export declare const getRateLimitStatus: (identifier: string, type: "ip" | "wallet" | "global") => Promise<{
    limit: number;
    remaining: number;
    resetTime: Date | null;
} | null>;
//# sourceMappingURL=rateLimiter.d.ts.map