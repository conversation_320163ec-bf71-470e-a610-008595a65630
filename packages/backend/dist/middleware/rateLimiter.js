import { RateLimiterRedis } from 'rate-limiter-flexible';
import { redisClient } from '../services/redis.js';
import { config } from '../config/index.js';
import { RateLimitError } from './errorHandler.js';
import { logRateLimit } from '../utils/logger.js';
let ipRateLimiter;
let globalRateLimiter;
const initializeRateLimiters = () => {
    ipRateLimiter = new RateLimiterRedis({
        storeClient: redisClient.rawClient,
        keyPrefix: `${config.redis.keyPrefix}ip_limit:`,
        points: config.rateLimits.maxRequestsPerIP,
        duration: Math.floor(config.rateLimits.windowMs / 1000),
        blockDuration: Math.floor(config.rateLimits.windowMs / 1000),
        execEvenly: true,
    });
    globalRateLimiter = new RateLimiterRedis({
        storeClient: redisClient.rawClient,
        keyPrefix: `${config.redis.keyPrefix}global_limit:`,
        points: config.rateLimits.maxRequestsPerWindow,
        duration: Math.floor(config.rateLimits.windowMs / 1000),
        blockDuration: Math.floor(config.rateLimits.windowMs / 1000),
        execEvenly: true,
    });
};
const getClientIP = (req) => {
    const forwarded = req.headers['x-forwarded-for'];
    const realIP = req.headers['x-real-ip'];
    const remoteAddress = req.connection?.remoteAddress || req.socket?.remoteAddress;
    if (forwarded) {
        return forwarded.split(',')[0].trim();
    }
    if (realIP) {
        return realIP;
    }
    return remoteAddress || 'unknown';
};
export const rateLimiter = async (req, res, next) => {
    try {
        if (!ipRateLimiter || !globalRateLimiter) {
            initializeRateLimiters();
        }
        const clientIP = getClientIP(req);
        const requestId = req.requestId || 'unknown';
        if (req.path.includes('/health') || req.path.includes('/metrics')) {
            return next();
        }
        try {
            await globalRateLimiter.consume('global');
        }
        catch (rateLimiterRes) {
            if (rateLimiterRes instanceof Error) {
                throw rateLimiterRes;
            }
            const retryAfter = Math.round(rateLimiterRes.msBeforeNext / 1000) || 1;
            logRateLimit(requestId, clientIP, undefined, 'global');
            res.set('Retry-After', retryAfter.toString());
            res.set('X-RateLimit-Limit', config.rateLimits.maxRequestsPerWindow.toString());
            res.set('X-RateLimit-Remaining', '0');
            res.set('X-RateLimit-Reset', new Date(Date.now() + rateLimiterRes.msBeforeNext).toISOString());
            throw new RateLimitError('Global rate limit exceeded. Please try again later.', retryAfter);
        }
        try {
            const rateLimiterRes = await ipRateLimiter.consume(clientIP);
            res.set('X-RateLimit-Limit', config.rateLimits.maxRequestsPerIP.toString());
            res.set('X-RateLimit-Remaining', rateLimiterRes.remainingPoints?.toString() || '0');
            res.set('X-RateLimit-Reset', new Date(Date.now() + rateLimiterRes.msBeforeNext).toISOString());
        }
        catch (rateLimiterRes) {
            if (rateLimiterRes instanceof Error) {
                throw rateLimiterRes;
            }
            const retryAfter = Math.round(rateLimiterRes.msBeforeNext / 1000) || 1;
            logRateLimit(requestId, clientIP, undefined, 'ip');
            res.set('Retry-After', retryAfter.toString());
            res.set('X-RateLimit-Limit', config.rateLimits.maxRequestsPerIP.toString());
            res.set('X-RateLimit-Remaining', '0');
            res.set('X-RateLimit-Reset', new Date(Date.now() + rateLimiterRes.msBeforeNext).toISOString());
            throw new RateLimitError(`Too many requests from IP ${clientIP}. Please try again later.`, retryAfter);
        }
        next();
    }
    catch (error) {
        next(error);
    }
};
export const checkWalletRateLimit = async (walletAddress, requestId) => {
    try {
        const lastRequest = await redisClient.getLastWalletRequest(walletAddress);
        const now = Date.now();
        const windowMs = config.rateLimits.windowMs;
        if (lastRequest && (now - lastRequest) < windowMs) {
            const retryAfter = Math.ceil((windowMs - (now - lastRequest)) / 1000);
            logRateLimit(requestId, 'unknown', walletAddress, 'wallet');
            throw new RateLimitError(`Wallet ${walletAddress} has already requested tokens recently. Please wait ${retryAfter} seconds.`, retryAfter);
        }
        await redisClient.trackWalletRequest(walletAddress, now);
    }
    catch (error) {
        if (error instanceof RateLimitError) {
            throw error;
        }
        console.error('Error checking wallet rate limit:', error);
    }
};
export const resetRateLimit = async (identifier, type) => {
    try {
        switch (type) {
            case 'ip':
                if (ipRateLimiter) {
                    await ipRateLimiter.delete(identifier);
                }
                break;
            case 'wallet':
                await redisClient.del(`wallets:${identifier}`);
                break;
            case 'global':
                if (globalRateLimiter) {
                    await globalRateLimiter.delete('global');
                }
                break;
        }
    }
    catch (error) {
        console.error(`Error resetting ${type} rate limit for ${identifier}:`, error);
        throw error;
    }
};
export const getRateLimitStatus = async (identifier, type) => {
    try {
        switch (type) {
            case 'ip':
                if (ipRateLimiter) {
                    const res = await ipRateLimiter.get(identifier);
                    return {
                        limit: config.rateLimits.maxRequestsPerIP,
                        remaining: res?.remainingPoints || config.rateLimits.maxRequestsPerIP,
                        resetTime: res ? new Date(Date.now() + res.msBeforeNext) : null,
                    };
                }
                break;
            case 'wallet':
                const lastRequest = await redisClient.getLastWalletRequest(identifier);
                const now = Date.now();
                const windowMs = config.rateLimits.windowMs;
                const canRequest = !lastRequest || (now - lastRequest) >= windowMs;
                return {
                    limit: 1,
                    remaining: canRequest ? 1 : 0,
                    resetTime: lastRequest ? new Date(lastRequest + windowMs) : null,
                };
            case 'global':
                if (globalRateLimiter) {
                    const res = await globalRateLimiter.get('global');
                    return {
                        limit: config.rateLimits.maxRequestsPerWindow,
                        remaining: res?.remainingPoints || config.rateLimits.maxRequestsPerWindow,
                        resetTime: res ? new Date(Date.now() + res.msBeforeNext) : null,
                    };
                }
                break;
        }
        return null;
    }
    catch (error) {
        console.error(`Error getting ${type} rate limit status for ${identifier}:`, error);
        return null;
    }
};
//# sourceMappingURL=rateLimiter.js.map