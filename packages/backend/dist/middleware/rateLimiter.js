import { redisClient } from '../services/redis.js';
import { config } from '../config/index.js';
import { RateLimitError } from './errorHandler.js';
import { logRateLimit } from '../utils/logger.js';
import { logger } from '../utils/logger.js';
const INTERNAL_IPS = [
    '127.0.0.1',
    '::1',
    '::ffff:127.0.0.1',
];
const isInternalRequest = (req) => {
    const ip = req.ip || req.connection.remoteAddress || '';
    const userAgent = req.get('User-Agent') || '';
    if (INTERNAL_IPS.includes(ip)) {
        return true;
    }
    if (userAgent.includes('axios') && req.get('X-API-Key') === config.auth.apiKey) {
        return true;
    }
    return false;
};
let ipRateLimiter = null;
let globalRateLimiter = null;
const getClientIP = (req) => {
    const forwarded = req.headers['x-forwarded-for'];
    const realIP = req.headers['x-real-ip'];
    const remoteAddress = req.connection?.remoteAddress || req.socket?.remoteAddress;
    if (forwarded) {
        return forwarded.split(',')[0]?.trim() || 'unknown';
    }
    if (realIP) {
        return realIP;
    }
    return remoteAddress || 'unknown';
};
const ipRequestCounts = new Map();
const globalRequestCount = { count: 0, resetTime: Date.now() + config.rateLimits.windowMs };
const simpleRateLimit = (clientIP) => {
    const now = Date.now();
    const windowMs = config.rateLimits.windowMs;
    if (now > globalRequestCount.resetTime) {
        globalRequestCount.count = 0;
        globalRequestCount.resetTime = now + windowMs;
    }
    if (globalRequestCount.count >= config.rateLimits.maxRequestsPerWindow) {
        const retryAfter = Math.ceil((globalRequestCount.resetTime - now) / 1000);
        return { allowed: false, retryAfter };
    }
    const ipData = ipRequestCounts.get(clientIP);
    if (!ipData || now > ipData.resetTime) {
        ipRequestCounts.set(clientIP, { count: 0, resetTime: now + windowMs });
    }
    const currentIpData = ipRequestCounts.get(clientIP);
    if (currentIpData.count >= config.rateLimits.maxRequestsPerIP) {
        const retryAfter = Math.ceil((currentIpData.resetTime - now) / 1000);
        return { allowed: false, retryAfter };
    }
    globalRequestCount.count++;
    currentIpData.count++;
    return { allowed: true };
};
export const rateLimiter = async (req, res, next) => {
    try {
        const clientIP = getClientIP(req);
        const requestId = req.requestId || 'unknown';
        if (isInternalRequest(req)) {
            console.log(`🔥 DEBUG: Skipping rate limit for internal request from ${clientIP}`);
            return next();
        }
        if (req.path.includes('/health') ||
            req.path.includes('/auth') ||
            req.path.includes('/admin') ||
            req.path.includes('/docs') ||
            req.path.includes('/api-docs') ||
            req.path === '/' ||
            req.path === '/test') {
            return next();
        }
        console.log(`🔥 DEBUG: Rate limiting check for ${clientIP} on ${req.path}`);
        console.log(`🔥 DEBUG: Using simple rate limiting`);
        const rateLimitResult = simpleRateLimit(clientIP);
        if (!rateLimitResult.allowed) {
            const retryAfter = rateLimitResult.retryAfter || 60;
            logRateLimit(requestId, clientIP, 'ip');
            res.set('Retry-After', retryAfter.toString());
            res.set('X-RateLimit-Limit', config.rateLimits.maxRequestsPerIP.toString());
            res.set('X-RateLimit-Remaining', '0');
            throw new RateLimitError(`Rate limit exceeded. Please try again in ${retryAfter} seconds.`, retryAfter);
        }
        console.log(`🔥 DEBUG: Rate limiting passed for ${clientIP}`);
        next();
    }
    catch (error) {
        next(error);
    }
};
const walletRequestCounts = new Map();
export const checkWalletRateLimit = async (walletAddress, requestId) => {
    try {
        console.log(`🔥 DEBUG: checkWalletRateLimit started for ${walletAddress}`);
        const now = Date.now();
        const windowMs = config.rateLimits.windowMs;
        const maxPerWallet = config.rateLimits.maxRequestsPerWallet;
        console.log(`🔥 DEBUG: now=${now}, windowMs=${windowMs}, maxPerWallet=${maxPerWallet}`);
        const walletData = walletRequestCounts.get(walletAddress);
        if (!walletData || now > walletData.resetTime) {
            walletRequestCounts.set(walletAddress, { count: 0, resetTime: now + windowMs });
        }
        const currentWalletData = walletRequestCounts.get(walletAddress);
        console.log(`🔥 DEBUG: currentWalletData:`, currentWalletData);
        if (currentWalletData.count >= maxPerWallet) {
            const retryAfter = Math.ceil((currentWalletData.resetTime - now) / 1000);
            logRateLimit(requestId, 'unknown', 'wallet', walletAddress);
            throw new RateLimitError(`Wallet ${walletAddress} has exceeded rate limit. Please wait ${retryAfter} seconds before requesting again.`, retryAfter);
        }
        currentWalletData.count++;
        console.log(`🔥 DEBUG: Wallet counter incremented to:`, currentWalletData.count);
        logger.info(`Wallet rate limit check passed`, {
            requestId,
            walletAddress,
            count: currentWalletData.count,
            maxPerWallet,
        });
    }
    catch (error) {
        if (error instanceof RateLimitError) {
            throw error;
        }
        console.error('Error checking wallet rate limit:', error);
    }
};
export const trackSuccessfulWalletRequest = async (walletAddress) => {
    try {
        const now = Date.now();
        await redisClient.trackWalletRequest(walletAddress, now);
    }
    catch (error) {
        console.error('Error tracking successful wallet request:', error);
    }
};
export const resetRateLimit = async (identifier, type) => {
    try {
        switch (type) {
            case 'ip':
                if (ipRateLimiter) {
                    await ipRateLimiter.delete(identifier);
                }
                break;
            case 'wallet':
                await redisClient.del(`wallets:${identifier}`);
                break;
            case 'global':
                if (globalRateLimiter) {
                    await globalRateLimiter.delete('global');
                }
                break;
        }
    }
    catch (error) {
        console.error(`Error resetting ${type} rate limit for ${identifier}:`, error);
        throw error;
    }
};
export const getRateLimitStatus = async (identifier, type) => {
    try {
        switch (type) {
            case 'ip':
                if (ipRateLimiter) {
                    const res = await ipRateLimiter.get(identifier);
                    return {
                        limit: config.rateLimits.maxRequestsPerIP,
                        remaining: res?.remainingPoints || config.rateLimits.maxRequestsPerIP,
                        resetTime: res ? new Date(Date.now() + res.msBeforeNext) : null,
                    };
                }
                break;
            case 'wallet':
                const lastRequest = await redisClient.getLastWalletRequest(identifier);
                const now = Date.now();
                const windowMs = config.rateLimits.windowMs;
                const canRequest = !lastRequest || (now - lastRequest) >= windowMs;
                return {
                    limit: 1,
                    remaining: canRequest ? 1 : 0,
                    resetTime: lastRequest ? new Date(lastRequest + windowMs) : null,
                };
            case 'global':
                if (globalRateLimiter) {
                    const res = await globalRateLimiter.get('global');
                    return {
                        limit: config.rateLimits.maxRequestsPerWindow,
                        remaining: res?.remainingPoints || config.rateLimits.maxRequestsPerWindow,
                        resetTime: res ? new Date(Date.now() + res.msBeforeNext) : null,
                    };
                }
                break;
        }
        return null;
    }
    catch (error) {
        console.error(`Error getting ${type} rate limit status for ${identifier}:`, error);
        return null;
    }
};
//# sourceMappingURL=rateLimiter.js.map