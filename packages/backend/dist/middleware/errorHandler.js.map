{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": "AACA,OAAO,EAAU,QAAQ,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAQ5C,MAAM,OAAO,eAAgB,SAAQ,KAAK;IAIJ;IAHpC,UAAU,GAAG,GAAG,CAAC;IACjB,IAAI,GAAG,kBAAkB,CAAC;IAE1B,YAAY,OAAe,EAAS,OAA6B;QAC/D,KAAK,CAAC,OAAO,CAAC,CAAC;QADmB,YAAO,GAAP,OAAO,CAAsB;QAE/D,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;IAChC,CAAC;CACF;AAED,MAAM,OAAO,cAAe,SAAQ,KAAK;IAIH;IAHpC,UAAU,GAAG,GAAG,CAAC;IACjB,IAAI,GAAG,qBAAqB,CAAC;IAE7B,YAAY,OAAe,EAAS,UAAmB;QACrD,KAAK,CAAC,OAAO,CAAC,CAAC;QADmB,eAAU,GAAV,UAAU,CAAS;QAErD,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;IAC/B,CAAC;CACF;AAED,MAAM,OAAO,QAAS,SAAQ,KAAK;IAIG;IAHpC,UAAU,GAAG,GAAG,CAAC;IACjB,IAAI,GAAG,WAAW,CAAC;IAEnB,YAAY,OAAe,EAAS,OAA6B;QAC/D,KAAK,CAAC,OAAO,CAAC,CAAC;QADmB,YAAO,GAAP,OAAO,CAAsB;QAE/D,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;IACzB,CAAC;CACF;AAED,MAAM,OAAO,wBAAyB,SAAQ,KAAK;IACjD,UAAU,GAAG,GAAG,CAAC;IACjB,IAAI,GAAG,sBAAsB,CAAC;IAE9B,YAAY,UAAkB,iCAAiC;QAC7D,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC;IACzC,CAAC;CACF;AAED,MAAM,OAAO,uBAAwB,SAAQ,KAAK;IAChD,UAAU,GAAG,GAAG,CAAC;IACjB,IAAI,GAAG,qBAAqB,CAAC;IAE7B,YAAY,UAAkB,iCAAiC;QAC7D,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,yBAAyB,CAAC;IACxC,CAAC;CACF;AAqBD,MAAM,CAAC,MAAM,YAAY,GAAG,CAC1B,GAAa,EACb,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IAER,IAAI,GAAG,CAAC,WAAW,EAAE,CAAC;QACpB,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC;IACnB,CAAC;IAED,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,cAAc,CAAW,IAAI,SAAS,CAAC;IAGrE,QAAQ,CAAC,GAAG,EAAE;QACZ,SAAS;QACT,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,GAAG,EAAE,GAAG,CAAC,GAAG;QACZ,EAAE,EAAE,GAAG,CAAC,EAAE;QACV,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;KACjC,CAAC,CAAC;IAGH,IAAI,UAAU,GAAG,GAAG,CAAC,UAAU,IAAI,GAAG,CAAC;IAGvC,IAAI,GAAG,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;QACnC,UAAU,GAAG,GAAG,CAAC;IACnB,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,mBAAmB,EAAE,CAAC;QAC5C,UAAU,GAAG,GAAG,CAAC;IACnB,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;QACzC,UAAU,GAAG,GAAG,CAAC;IACnB,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;QACxC,UAAU,GAAG,GAAG,CAAC;IACnB,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,gBAAgB,EAAE,CAAC;QACzC,UAAU,GAAG,GAAG,CAAC;IACnB,CAAC;SAAM,IAAI,GAAG,CAAC,IAAI,KAAK,0BAA0B,IAAI,GAAG,CAAC,IAAI,KAAK,yBAAyB,EAAE,CAAC;QAC7F,UAAU,GAAG,GAAG,CAAC;IACnB,CAAC;IAGD,MAAM,SAAS,GAAG,GAAG,CAAC,IAAI,IAAI,sBAAsB,CAAC,UAAU,CAAC,CAAC;IAGjE,MAAM,aAAa,GAAkB;QACnC,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,eAAe,CAAC,GAAG,EAAE,UAAU,CAAC;YACzC,SAAS;SACV;KACF,CAAC;IAGF,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;QAChB,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;IAC5C,CAAC;IAGD,IAAI,GAAG,YAAY,cAAc,IAAI,GAAG,CAAC,UAAU,EAAE,CAAC;QACpD,aAAa,CAAC,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC;QAChD,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;IACpD,CAAC;IAGD,IAAI,MAAM,CAAC,MAAM,CAAC,WAAW,KAAK,aAAa,EAAE,CAAC;QAC/C,aAAkC,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC;IAC9D,CAAC;IAGD,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IACvB,GAAG,CAAC,GAAG,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;IAG5C,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;AAC1B,CAAC,CAAC;AAGF,SAAS,sBAAsB,CAAC,UAAkB;IAChD,QAAQ,UAAU,EAAE,CAAC;QACnB,KAAK,GAAG;YACN,OAAO,aAAa,CAAC;QACvB,KAAK,GAAG;YACN,OAAO,cAAc,CAAC;QACxB,KAAK,GAAG;YACN,OAAO,WAAW,CAAC;QACrB,KAAK,GAAG;YACN,OAAO,WAAW,CAAC;QACrB,KAAK,GAAG;YACN,OAAO,UAAU,CAAC;QACpB,KAAK,GAAG;YACN,OAAO,sBAAsB,CAAC;QAChC,KAAK,GAAG;YACN,OAAO,qBAAqB,CAAC;QAC/B,KAAK,GAAG;YACN,OAAO,uBAAuB,CAAC;QACjC,KAAK,GAAG;YACN,OAAO,aAAa,CAAC;QACvB,KAAK,GAAG;YACN,OAAO,qBAAqB,CAAC;QAC/B,KAAK,GAAG;YACN,OAAO,iBAAiB,CAAC;QAC3B;YACE,OAAO,eAAe,CAAC;IAC3B,CAAC;AACH,CAAC;AAGD,SAAS,eAAe,CAAC,GAAa,EAAE,UAAkB;IAExD,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC;QAChD,OAAO,GAAG,CAAC,OAAO,CAAC;IACrB,CAAC;IAGD,IAAI,MAAM,CAAC,MAAM,CAAC,WAAW,KAAK,YAAY,IAAI,eAAe,CAAC,UAAU,CAAC,EAAE,CAAC;QAC9E,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,GAAG;gBACN,OAAO,mCAAmC,CAAC;YAC7C,KAAK,GAAG;gBACN,OAAO,aAAa,CAAC;YACvB,KAAK,GAAG;gBACN,OAAO,iCAAiC,CAAC;YAC3C,KAAK,GAAG;gBACN,OAAO,iBAAiB,CAAC;YAC3B;gBACE,OAAO,iDAAiD,CAAC;QAC7D,CAAC;IACH,CAAC;IAGD,OAAO,GAAG,CAAC,OAAO,IAAI,iBAAiB,CAAC,UAAU,CAAC,CAAC;AACtD,CAAC;AAGD,SAAS,eAAe,CAAC,UAAkB;IACzC,OAAO,UAAU,IAAI,GAAG,CAAC;AAC3B,CAAC;AAGD,SAAS,iBAAiB,CAAC,UAAkB;IAC3C,QAAQ,UAAU,EAAE,CAAC;QACnB,KAAK,GAAG;YACN,OAAO,aAAa,CAAC;QACvB,KAAK,GAAG;YACN,OAAO,cAAc,CAAC;QACxB,KAAK,GAAG;YACN,OAAO,WAAW,CAAC;QACrB,KAAK,GAAG;YACN,OAAO,WAAW,CAAC;QACrB,KAAK,GAAG;YACN,OAAO,UAAU,CAAC;QACpB,KAAK,GAAG;YACN,OAAO,sBAAsB,CAAC;QAChC,KAAK,GAAG;YACN,OAAO,mBAAmB,CAAC;QAC7B,KAAK,GAAG;YACN,OAAO,uBAAuB,CAAC;QACjC,KAAK,GAAG;YACN,OAAO,aAAa,CAAC;QACvB,KAAK,GAAG;YACN,OAAO,qBAAqB,CAAC;QAC/B,KAAK,GAAG;YACN,OAAO,iBAAiB,CAAC;QAC3B;YACE,OAAO,mBAAmB,CAAC;IAC/B,CAAC;AACH,CAAC;AAGD,MAAM,CAAC,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAGF,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAQ,EAAE;IACvF,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,oBAAoB,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,CAAa,CAAC;IAClF,KAAK,CAAC,UAAU,GAAG,GAAG,CAAC;IACvB,KAAK,CAAC,IAAI,GAAG,WAAW,CAAC;IACzB,IAAI,CAAC,KAAK,CAAC,CAAC;AACd,CAAC,CAAC"}