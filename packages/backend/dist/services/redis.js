import { createClient } from 'redis';
import { config } from '../config/index.js';
import { logger, logError } from '../utils/logger.js';
class RedisService {
    client;
    isConnected = false;
    constructor() {
        this.client = createClient({
            url: config.redis.url,
            socket: {
                reconnectStrategy: (retries) => {
                    if (retries > 10) {
                        logger.error('Redis reconnection failed after 10 attempts');
                        return new Error('Redis reconnection failed');
                    }
                    return Math.min(retries * 100, 3000);
                },
            },
        });
        this.client.on('connect', () => {
            logger.info('Redis client connected');
        });
        this.client.on('ready', () => {
            logger.info('Redis client ready');
            this.isConnected = true;
        });
        this.client.on('error', (error) => {
            logger.error('Redis client error:', error);
            this.isConnected = false;
        });
        this.client.on('end', () => {
            logger.info('Redis client disconnected');
            this.isConnected = false;
        });
        this.client.on('reconnecting', () => {
            logger.info('Redis client reconnecting...');
        });
    }
    async connect() {
        try {
            await this.client.connect();
            logger.info('Redis connection established');
        }
        catch (error) {
            logError(error, { context: 'Redis connection' });
            throw error;
        }
    }
    async disconnect() {
        try {
            if (this.isConnected) {
                await this.client.quit();
                logger.info('Redis connection closed');
            }
        }
        catch (error) {
            logError(error, { context: 'Redis disconnection' });
            throw error;
        }
    }
    async incrementRateLimit(key, windowMs) {
        try {
            const fullKey = `${config.redis.keyPrefix}rate_limit:${key}`;
            const multi = this.client.multi();
            multi.incr(fullKey);
            multi.expire(fullKey, Math.ceil(windowMs / 1000));
            multi.ttl(fullKey);
            const results = await multi.exec();
            if (!results || results.length < 3) {
                throw new Error('Redis multi command failed');
            }
            const count = results[0];
            const ttl = results[2];
            return { count, ttl };
        }
        catch (error) {
            logError(error, { context: 'Rate limit increment', key });
            throw error;
        }
    }
    async getRateLimit(key) {
        try {
            const fullKey = `${config.redis.keyPrefix}rate_limit:${key}`;
            const multi = this.client.multi();
            multi.get(fullKey);
            multi.ttl(fullKey);
            const results = await multi.exec();
            if (!results || results.length < 2) {
                return { count: 0, ttl: 0 };
            }
            const count = parseInt(results[0] || '0');
            const ttl = results[1];
            return { count, ttl };
        }
        catch (error) {
            logError(error, { context: 'Rate limit get', key });
            return { count: 0, ttl: 0 };
        }
    }
    async resetRateLimit(key) {
        try {
            const fullKey = `${config.redis.keyPrefix}rate_limit:${key}`;
            await this.client.del(fullKey);
        }
        catch (error) {
            logError(error, { context: 'Rate limit reset', key });
            throw error;
        }
    }
    async incrementMetric(metric, value = 1) {
        try {
            const key = `${config.redis.keyPrefix}metrics:${metric}`;
            await this.client.incrBy(key, value);
            await this.client.expire(key, 86400);
        }
        catch (error) {
            logError(error, { context: 'Metric increment', metric });
        }
    }
    async getMetric(metric) {
        try {
            const key = `${config.redis.keyPrefix}metrics:${metric}`;
            const value = await this.client.get(key);
            return parseInt(value || '0');
        }
        catch (error) {
            logError(error, { context: 'Metric get', metric });
            return 0;
        }
    }
    async setMetric(metric, value, ttl) {
        try {
            const key = `${config.redis.keyPrefix}metrics:${metric}`;
            await this.client.set(key, value.toString());
            if (ttl) {
                await this.client.expire(key, ttl);
            }
        }
        catch (error) {
            logError(error, { context: 'Metric set', metric });
        }
    }
    async trackRequest(requestId, data) {
        try {
            const key = `${config.redis.keyPrefix}requests:${requestId}`;
            await this.client.setEx(key, 3600, JSON.stringify(data));
        }
        catch (error) {
            logError(error, { context: 'Request tracking', requestId });
        }
    }
    async getRequest(requestId) {
        try {
            const key = `${config.redis.keyPrefix}requests:${requestId}`;
            const data = await this.client.get(key);
            return data ? JSON.parse(data) : null;
        }
        catch (error) {
            logError(error, { context: 'Request get', requestId });
            return null;
        }
    }
    async trackWalletRequest(walletAddress, timestamp) {
        try {
            const key = `${config.redis.keyPrefix}wallets:${walletAddress}`;
            await this.client.setEx(key, config.rateLimits.windowMs / 1000, timestamp.toString());
        }
        catch (error) {
            logError(error, { context: 'Wallet tracking', walletAddress });
        }
    }
    async getLastWalletRequest(walletAddress) {
        try {
            const key = `${config.redis.keyPrefix}wallets:${walletAddress}`;
            const timestamp = await this.client.get(key);
            return timestamp ? parseInt(timestamp) : null;
        }
        catch (error) {
            logError(error, { context: 'Wallet get', walletAddress });
            return null;
        }
    }
    async healthCheck() {
        const start = Date.now();
        try {
            await this.client.ping();
            const latency = Date.now() - start;
            return { status: 'healthy', latency };
        }
        catch (error) {
            const latency = Date.now() - start;
            logError(error, { context: 'Redis health check' });
            return { status: 'unhealthy', latency };
        }
    }
    async set(key, value, ttl) {
        try {
            const fullKey = `${config.redis.keyPrefix}${key}`;
            if (ttl) {
                await this.client.setEx(fullKey, ttl, value);
            }
            else {
                await this.client.set(fullKey, value);
            }
        }
        catch (error) {
            logError(error, { context: 'Cache set', key });
            throw error;
        }
    }
    async get(key) {
        try {
            const fullKey = `${config.redis.keyPrefix}${key}`;
            return await this.client.get(fullKey);
        }
        catch (error) {
            logError(error, { context: 'Cache get', key });
            return null;
        }
    }
    async del(key) {
        try {
            const fullKey = `${config.redis.keyPrefix}${key}`;
            await this.client.del(fullKey);
        }
        catch (error) {
            logError(error, { context: 'Cache delete', key });
            throw error;
        }
    }
    get isHealthy() {
        return this.isConnected;
    }
    get rawClient() {
        return this.client;
    }
}
export const redisClient = new RedisService();
//# sourceMappingURL=redis.js.map