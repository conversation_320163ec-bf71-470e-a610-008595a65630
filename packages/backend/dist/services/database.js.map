{"version": 3, "file": "database.js", "sourceRoot": "", "sources": ["../../src/services/database.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC;AAC1B,OAAO,MAAM,MAAM,QAAQ,CAAC;AAC5B,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAiD5C,MAAM,eAAe;IACX,IAAI,GAAgB,IAAI,CAAC;IAEjC;IAEA,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC;gBACnB,gBAAgB,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG;gBACrC,GAAG,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK;gBAChE,GAAG,EAAE,EAAE;gBACP,iBAAiB,EAAE,KAAK;gBACxB,uBAAuB,EAAE,IAAI;aAC9B,CAAC,CAAC;YAGH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YACzC,MAAM,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;YACnC,MAAM,CAAC,OAAO,EAAE,CAAC;YAEjB,MAAM,CAAC,IAAI,CAAC,4CAA4C,EAAE;gBACxD,QAAQ,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,SAAS;aACxE,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACrE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,IAAI,CAAC,IAAI;YAAE,OAAO;QAEvB,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;YACtB,MAAM,CAAC,IAAI,CAAC,uBAAuB,CAAC,CAAC;YACrC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACrE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,IAAY,EAAE,MAAc;QAC9C,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;QACzC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAChD,OAAO,MAAM,CAAC;QAChB,CAAC;gBAAS,CAAC;YACT,MAAM,CAAC,OAAO,EAAE,CAAC;QACnB,CAAC;IACH,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,KAAK,CAAC;;;;;;;;;;;;;OAahB,CAAC,CAAC;YAGH,MAAM,IAAI,CAAC,KAAK,CAAC;;;;;;;;;;;;OAYhB,CAAC,CAAC;YAGH,MAAM,IAAI,CAAC,KAAK,CAAC;;;;;;;;;OAShB,CAAC,CAAC;YAGH,MAAM,IAAI,CAAC,KAAK,CAAC;;;;;;;;;;;;OAYhB,CAAC,CAAC;YAGH,MAAM,IAAI,CAAC,KAAK,CAAC,oFAAoF,CAAC,CAAC;YACvG,MAAM,IAAI,CAAC,KAAK,CAAC,iFAAiF,CAAC,CAAC;YACpG,MAAM,IAAI,CAAC,KAAK,CAAC,4EAA4E,CAAC,CAAC;YAC/F,MAAM,IAAI,CAAC,KAAK,CAAC,iFAAiF,CAAC,CAAC;YACpG,MAAM,IAAI,CAAC,KAAK,CAAC,yFAAyF,CAAC,CAAC;YAC5G,MAAM,IAAI,CAAC,KAAK,CAAC,8EAA8E,CAAC,CAAC;YACjG,MAAM,IAAI,CAAC,KAAK,CAAC,6EAA6E,CAAC,CAAC;YAGhG,MAAM,IAAI,CAAC,KAAK,CAAC;;;OAGhB,CAAC,CAAC,KAAK,CAAC,GAAG,EAAE;gBAEZ,MAAM,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;YAC/E,CAAC,CAAC,CAAC;YAGH,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAEpC,MAAM,CAAC,IAAI,CAAC,0CAA0C,CAAC,CAAC;QAC1D,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC/E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,eAAe,CAAC,WAA8B;QAClD,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;;;;;;KAM/B,EAAE;YACD,WAAW,CAAC,UAAU;YACtB,WAAW,CAAC,cAAc;YAC1B,WAAW,CAAC,MAAM;YAClB,WAAW,CAAC,gBAAgB;YAC5B,WAAW,CAAC,MAAM;YAClB,WAAW,CAAC,aAAa;YACzB,WAAW,CAAC,UAAU;YACtB,WAAW,CAAC,UAAU;YACtB,WAAW,CAAC,UAAU;SACvB,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,QAAgB,GAAG,EAAE,SAAiB,CAAC;QAC3D,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;;;;KAI/B,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;QAEpB,OAAO,MAAM,CAAC,IAAI,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,mBAAmB;QAMvB,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;;;;;;;KAO/B,CAAC,CAAC;QAEH,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC7B,OAAO;YACL,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;YACjC,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC;YAC3C,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;YACnC,WAAW,EAAE,CAAC,KAAK,CAAC,WAAW,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE;SACnD,CAAC;IACJ,CAAC;IAGD,KAAK,CAAC,WAAW,CAAC,IAMjB;QACC,IAAI,CAAC;YAEH,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;YAGpC,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACvD,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACvE,CAAC;YAED,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBACxC,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBACrC,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,IAAI,EAAE,IAAI,CAAC,IAAI;aAChB,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGO,KAAK,CAAC,kBAAkB,CAAC,IAKhC;QACC,MAAM,KAAK,GAAG;;;;;;;;;;;;;KAab,CAAC;QAEF,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvD,MAAM,WAAW,GAAG,IAAI,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACpE,MAAM,cAAc,GAAG,IAAI,CAAC,UAAU,KAAK,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,MAAM,YAAY,GAAG,IAAI,CAAC,UAAU,KAAK,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEjE,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE;YACtB,IAAI,CAAC,IAAI;YACT,YAAY;YACZ,WAAW;YACX,MAAM,CAAC,QAAQ,EAAE;YACjB,cAAc;YACd,YAAY;SACb,CAAC,CAAC;IACL,CAAC;IAGO,KAAK,CAAC,qBAAqB,CAAC,eAAuB,EAAE,MAAc;QACzE,IAAI,CAAC;YACH,MAAM,KAAK,GAAG;;;;OAIb,CAAC;YAEF,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC;YAGnD,MAAM,YAAY,GAAG;;;OAGpB,CAAC;YAEF,MAAM,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QACjC,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE;gBAC/C,KAAK,EAAE,KAAK,CAAC,OAAO;gBACpB,eAAe;aAChB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,cAAc,CAAC,GAAe;QAClC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;;;;;;KAM/B,EAAE;YACD,GAAG,CAAC,UAAU;YACd,GAAG,CAAC,MAAM;YACV,GAAG,CAAC,GAAG;YACP,GAAG,CAAC,UAAU;YACd,GAAG,CAAC,UAAU;YACd,GAAG,CAAC,WAAW;YACf,GAAG,CAAC,aAAa;YACjB,GAAG,CAAC,UAAU;SACf,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3B,CAAC;IAGD,KAAK,CAAC,iBAAiB,CAAC,QAAuB;QAC7C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;;;;;KAK/B,EAAE;YACD,QAAQ,CAAC,cAAc;YACvB,QAAQ,CAAC,MAAM;YACf,QAAQ,CAAC,OAAO;YAChB,QAAQ,CAAC,UAAU;YACnB,QAAQ,CAAC,UAAU;SACpB,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,QAAgB,EAAE;QACzC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;;;;KAI/B,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QAEZ,OAAO,MAAM,CAAC,IAAI,CAAC;IACrB,CAAC;IAGO,KAAK,CAAC,sBAAsB;QAClC,IAAI,CAAC;YAEH,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,KAAK,CACpC,gDAAgD,EAChD,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAC5B,CAAC;YAEF,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAEpC,MAAM,UAAU,GAAG,EAAE,CAAC;gBACtB,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;gBAG9E,MAAM,IAAI,CAAC,KAAK,CAAC;;;SAGhB,EAAE;oBACD,MAAM,CAAC,IAAI,CAAC,aAAa;oBACzB,YAAY;oBACZ,aAAa;oBACb,IAAI;oBACJ,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACxB,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;iBACzB,CAAC,CAAC;gBAEH,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE,EAAE,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;YACrF,CAAC;iBAAM,CAAC;gBACN,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAE,QAAQ,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC;YACpF,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAC9E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,QAAgB,EAAE,QAAgB;QACxD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAC7B,oEAAoE,EACpE,CAAC,QAAQ,CAAC,CACX,CAAC;YAEF,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7B,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,eAAe,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;YAE3E,IAAI,CAAC,eAAe,EAAE,CAAC;gBACrB,OAAO,IAAI,CAAC;YACd,CAAC;YAGD,MAAM,IAAI,CAAC,KAAK,CACd,uEAAuE,EACvE,CAAC,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,EAAE,CAAC,CAC9D,CAAC;YAEF,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;YACjF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,QAAgB;QACjC,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAC7B,+CAA+C,EAC/C,CAAC,QAAQ,CAAC,CACX,CAAC;YAEF,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7B,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAC5B,OAAO;gBACL,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,aAAa,EAAE,IAAI,CAAC,aAAa;gBACjC,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,IAAI,EAAE,IAAI,CAAC,IAAI;gBACf,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;YAC7E,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,QAKrB;QACC,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,EAAE,CAAC;YACtB,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAEtE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;;;;OAI/B,EAAE;gBACD,QAAQ,CAAC,QAAQ;gBACjB,YAAY;gBACZ,QAAQ,CAAC,KAAK,IAAI,IAAI;gBACtB,QAAQ,CAAC,IAAI,IAAI,OAAO;gBACxB,IAAI;gBACJ,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACxB,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACzB,CAAC,CAAC;YAEH,OAAO,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,QAAQ,EAAE,CAAC,CAAC;YACnG,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,QAAgB,EAAE,WAAmB;QAC7D,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,EAAE,CAAC;YACtB,MAAM,YAAY,GAAG,MAAM,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YAEhE,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAC7B,qGAAqG,EACrG,CAAC,YAAY,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,QAAQ,CAAC,CACnD,CAAC;YAEF,OAAO,MAAM,CAAC,QAAQ,GAAG,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;YACpF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,gBAAgB,CAAC,OAAe,CAAC;QACrC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;;+CAEW,IAAI;;KAE9C,CAAC,CAAC;QACH,OAAO,MAAM,CAAC,IAAI,CAAC;IACrB,CAAC;IAGD,KAAK,CAAC,qBAAqB,CAAC,QAAgB,EAAE;QAC5C,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC;;;;KAI/B,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;QACZ,OAAO,MAAM,CAAC,IAAI,CAAC;IACrB,CAAC;CACF;AAGD,MAAM,CAAC,MAAM,eAAe,GAAG,IAAI,eAAe,EAAE,CAAC"}