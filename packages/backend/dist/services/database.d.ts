export interface TransactionRecord {
    id?: number;
    request_id: string;
    wallet_address: string;
    amount: string;
    transaction_hash: string;
    status: 'success' | 'failed';
    error_message?: string;
    ip_address: string;
    user_agent?: string;
    created_at: string;
}
export interface RequestLog {
    id?: number;
    request_id: string;
    method: string;
    url: string;
    ip_address: string;
    user_agent?: string;
    status_code: number;
    response_time: number;
    created_at: string;
}
export interface AdminActivity {
    id?: number;
    admin_username: string;
    action: string;
    details?: string;
    ip_address: string;
    created_at: string;
}
export interface AdminUser {
    id?: number;
    username: string;
    password_hash: string;
    email?: string;
    role: 'admin' | 'super_admin';
    is_active: boolean;
    last_login?: string;
    created_at: string;
    updated_at: string;
}
declare class DatabaseService {
    private pool;
    constructor();
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    private query;
    initialize(): Promise<void>;
    saveTransaction(transaction: TransactionRecord): Promise<number>;
    getTransactions(limit?: number, offset?: number): Promise<TransactionRecord[]>;
    getTransactionStats(): Promise<{
        total: number;
        successful: number;
        failed: number;
        totalAmount: string;
    }>;
    saveMetrics(data: {
        date: string;
        amount: string;
        status: 'success' | 'failed';
        transaction_hash?: string;
        error_type?: string;
    }): Promise<void>;
    private updateDailyMetrics;
    private saveRecentTransaction;
    saveRequestLog(log: RequestLog): Promise<number>;
    saveAdminActivity(activity: AdminActivity): Promise<number>;
    getAdminActivities(limit?: number): Promise<AdminActivity[]>;
    private createDefaultAdminUser;
    authenticateAdmin(username: string, password: string): Promise<AdminUser | null>;
    getAdminUser(username: string): Promise<AdminUser | null>;
    createAdminUser(userData: {
        username: string;
        password: string;
        email?: string;
        role?: 'admin' | 'super_admin';
    }): Promise<AdminUser>;
    updateAdminPassword(username: string, newPassword: string): Promise<boolean>;
    getFaucetMetrics(days?: number): Promise<any[]>;
    getRecentTransactions(limit?: number): Promise<any[]>;
}
export declare const databaseService: DatabaseService;
export {};
//# sourceMappingURL=database.d.ts.map