import { RedisClientType } from 'redis';
declare class RedisService {
    private client;
    private isConnected;
    constructor();
    connect(): Promise<void>;
    disconnect(): Promise<void>;
    incrementRateLimit(key: string, windowMs: number): Promise<{
        count: number;
        ttl: number;
    }>;
    getRateLimit(key: string): Promise<{
        count: number;
        ttl: number;
    }>;
    resetRateLimit(key: string): Promise<void>;
    incrementMetric(metric: string, value?: number): Promise<void>;
    getMetric(metric: string): Promise<number>;
    setMetric(metric: string, value: number, ttl?: number): Promise<void>;
    trackRequest(requestId: string, data: Record<string, any>): Promise<void>;
    getRequest(requestId: string): Promise<Record<string, any> | null>;
    trackWalletRequest(walletAddress: string, timestamp: number): Promise<void>;
    getLastWalletRequest(walletAddress: string): Promise<number | null>;
    healthCheck(): Promise<{
        status: 'healthy' | 'unhealthy';
        latency: number;
    }>;
    clearAll(): Promise<void>;
    set(key: string, value: string, ttl?: number): Promise<void>;
    get(key: string): Promise<string | null>;
    del(key: string): Promise<void>;
    get isHealthy(): boolean;
    get rawClient(): RedisClientType;
}
export declare const redisClient: RedisService;
export {};
//# sourceMappingURL=redis.d.ts.map