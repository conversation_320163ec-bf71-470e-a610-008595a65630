export interface TransferResult {
    success: boolean;
    transactionHash?: string;
    error?: string;
    gasUsed?: string;
}
export interface WalletInfo {
    address: string;
    balance: bigint;
    isLowBalance: boolean;
}
declare class SuiService {
    private client;
    private keypair;
    private walletAddress;
    private isInitialized;
    constructor();
    initialize(): Promise<void>;
    disconnect(): Promise<void>;
    getWalletBalance(): Promise<bigint>;
    getWalletInfo(): Promise<WalletInfo>;
    validateAddress(address: string): boolean;
    sendTokens(recipientAddress: string, amount: bigint, requestId: string): Promise<TransferResult>;
    healthCheck(): Promise<{
        status: 'healthy' | 'unhealthy';
        details: Record<string, any>;
    }>;
    formatAmount(amount: bigint): string;
    parseAmount(amountSui: string): bigint;
    get isReady(): boolean;
    get faucetAddress(): string;
    get networkInfo(): {
        network: "testnet" | "devnet";
        rpcUrl: string;
        walletAddress: string;
    };
}
export declare const suiService: SuiService;
export {};
//# sourceMappingURL=sui.d.ts.map