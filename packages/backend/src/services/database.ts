import sqlite3 from 'sqlite3';
import { promisify } from 'util';
import path from 'path';
import fs from 'fs';
import { logger } from '../utils/logger.js';

// Database interfaces
export interface TransactionRecord {
  id?: number;
  request_id: string;
  wallet_address: string;
  amount: string;
  transaction_hash: string;
  status: 'success' | 'failed';
  error_message?: string;
  ip_address: string;
  user_agent?: string;
  created_at: string;
}

export interface RequestLog {
  id?: number;
  request_id: string;
  method: string;
  url: string;
  ip_address: string;
  user_agent?: string;
  status_code: number;
  response_time: number;
  created_at: string;
}

export interface AdminActivity {
  id?: number;
  admin_username: string;
  action: string;
  details?: string;
  ip_address: string;
  created_at: string;
}

class DatabaseService {
  private db: sqlite3.Database | null = null;
  private dbPath: string;

  constructor() {
    // Create data directory if it doesn't exist
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    this.dbPath = path.join(dataDir, 'faucet.db');
  }

  async connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          logger.error('Database connection failed', { error: err.message });
          reject(err);
        } else {
          logger.info('Database connected successfully', { path: this.dbPath });
          resolve();
        }
      });
    });
  }

  async disconnect(): Promise<void> {
    if (!this.db) return;
    
    return new Promise((resolve, reject) => {
      this.db!.close((err) => {
        if (err) {
          logger.error('Database disconnect failed', { error: err.message });
          reject(err);
        } else {
          logger.info('Database disconnected');
          this.db = null;
          resolve();
        }
      });
    });
  }

  async initialize(): Promise<void> {
    if (!this.db) {
      throw new Error('Database not connected');
    }

    const run = promisify(this.db.run.bind(this.db));

    // Create transactions table
    await run(`
      CREATE TABLE IF NOT EXISTS transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        request_id TEXT NOT NULL,
        wallet_address TEXT NOT NULL,
        amount TEXT NOT NULL,
        transaction_hash TEXT NOT NULL,
        status TEXT NOT NULL CHECK (status IN ('success', 'failed')),
        error_message TEXT,
        ip_address TEXT NOT NULL,
        user_agent TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create request_logs table
    await run(`
      CREATE TABLE IF NOT EXISTS request_logs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        request_id TEXT NOT NULL,
        method TEXT NOT NULL,
        url TEXT NOT NULL,
        ip_address TEXT NOT NULL,
        user_agent TEXT,
        status_code INTEGER NOT NULL,
        response_time INTEGER NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create admin_activities table
    await run(`
      CREATE TABLE IF NOT EXISTS admin_activities (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        admin_username TEXT NOT NULL,
        action TEXT NOT NULL,
        details TEXT,
        ip_address TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create indexes for better performance
    await run('CREATE INDEX IF NOT EXISTS idx_transactions_wallet ON transactions(wallet_address)');
    await run('CREATE INDEX IF NOT EXISTS idx_transactions_created ON transactions(created_at)');
    await run('CREATE INDEX IF NOT EXISTS idx_request_logs_created ON request_logs(created_at)');
    await run('CREATE INDEX IF NOT EXISTS idx_admin_activities_created ON admin_activities(created_at)');

    logger.info('Database tables initialized successfully');
  }

  // Transaction methods
  async saveTransaction(transaction: TransactionRecord): Promise<number> {
    if (!this.db) throw new Error('Database not connected');

    const run = promisify(this.db.run.bind(this.db));
    const result = await run(`
      INSERT INTO transactions (
        request_id, wallet_address, amount, transaction_hash, 
        status, error_message, ip_address, user_agent, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      transaction.request_id,
      transaction.wallet_address,
      transaction.amount,
      transaction.transaction_hash,
      transaction.status,
      transaction.error_message,
      transaction.ip_address,
      transaction.user_agent,
      transaction.created_at
    ]);

    return (result as any).lastID;
  }

  async getTransactions(limit: number = 100, offset: number = 0): Promise<TransactionRecord[]> {
    if (!this.db) throw new Error('Database not connected');

    const all = promisify(this.db.all.bind(this.db));
    return await all(`
      SELECT * FROM transactions 
      ORDER BY created_at DESC 
      LIMIT ? OFFSET ?
    `, [limit, offset]);
  }

  async getTransactionStats(): Promise<{
    total: number;
    successful: number;
    failed: number;
    totalAmount: string;
  }> {
    if (!this.db) throw new Error('Database not connected');

    const get = promisify(this.db.get.bind(this.db));
    
    const stats = await get(`
      SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful,
        SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
        SUM(CASE WHEN status = 'success' THEN CAST(amount AS INTEGER) ELSE 0 END) as totalAmount
      FROM transactions
    `);

    return {
      total: stats.total || 0,
      successful: stats.successful || 0,
      failed: stats.failed || 0,
      totalAmount: (stats.totalAmount || 0).toString(),
    };
  }

  // Request log methods
  async saveRequestLog(log: RequestLog): Promise<number> {
    if (!this.db) throw new Error('Database not connected');

    const run = promisify(this.db.run.bind(this.db));
    const result = await run(`
      INSERT INTO request_logs (
        request_id, method, url, ip_address, user_agent, 
        status_code, response_time, created_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      log.request_id,
      log.method,
      log.url,
      log.ip_address,
      log.user_agent,
      log.status_code,
      log.response_time,
      log.created_at
    ]);

    return (result as any).lastID;
  }

  // Admin activity methods
  async saveAdminActivity(activity: AdminActivity): Promise<number> {
    if (!this.db) throw new Error('Database not connected');

    const run = promisify(this.db.run.bind(this.db));
    const result = await run(`
      INSERT INTO admin_activities (
        admin_username, action, details, ip_address, created_at
      ) VALUES (?, ?, ?, ?, ?)
    `, [
      activity.admin_username,
      activity.action,
      activity.details,
      activity.ip_address,
      activity.created_at
    ]);

    return (result as any).lastID;
  }

  async getAdminActivities(limit: number = 50): Promise<AdminActivity[]> {
    if (!this.db) throw new Error('Database not connected');

    const all = promisify(this.db.all.bind(this.db));
    return await all(`
      SELECT * FROM admin_activities 
      ORDER BY created_at DESC 
      LIMIT ?
    `, [limit]);
  }
}

// Export singleton instance
export const databaseService = new DatabaseService();
