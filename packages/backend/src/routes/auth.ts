import { Router, Request, Response } from 'express';
import { asyncHandler } from '../middleware/errorHandler.js';
import { validate } from '../validation/schemas.js';
import { logger } from '../utils/logger.js';
import <PERSON><PERSON> from 'joi';

const router = Router();

// Auth request schema
const authSchema = Joi.object({
  password: Joi.string()
    .required()
    .messages({
      'any.required': 'Password is required',
      'string.empty': 'Password cannot be empty',
    }),
});

// Auth response interface
interface AuthResponse {
  success: boolean;
  message: string;
  authenticated?: boolean;
  timestamp?: string;
  error?: {
    code: string;
    details?: string;
  };
}

// POST /api/v1/auth/login - Simple password authentication
router.post('/login', 
  validate(authSchema, 'body'),
  asyncHandler(async (req: Request<{}, AuthResponse, { password: string }>, res: Response<AuthResponse>) => {
    const { password } = req.body;
    const requestId = req.requestId;
    const clientIP = req.ip || 'unknown';

    logger.info(`Auth attempt`, {
      requestId,
      ip: clientIP,
      timestamp: new Date().toISOString(),
    });

    try {
      // Check if password matches "suisuisui"
      if (password === 'suisuisui') {
        logger.info(`✅ Auth successful`, {
          requestId,
          ip: clientIP,
          timestamp: new Date().toISOString(),
        });

        return res.status(200).json({
          success: true,
          authenticated: true,
          message: '🎉 Authentication successful! Welcome to Sui Faucet Admin.',
          timestamp: new Date().toISOString(),
        });

      } else {
        logger.warn(`❌ Auth failed - invalid password`, {
          requestId,
          ip: clientIP,
          timestamp: new Date().toISOString(),
        });

        return res.status(401).json({
          success: false,
          authenticated: false,
          message: '🚫 Authentication failed. Invalid password.',
          error: {
            code: 'INVALID_CREDENTIALS',
            details: 'The provided password is incorrect',
          },
        });
      }

    } catch (error: any) {
      logger.error('Auth endpoint error', {
        error: error.message,
        requestId,
        ip: clientIP,
      });

      return res.status(500).json({
        success: false,
        message: '❌ Authentication service error',
        error: {
          code: 'AUTH_SERVICE_ERROR',
          details: 'Internal server error during authentication',
        },
      });
    }
  })
);

// GET /api/v1/auth/status - Check auth status (for testing)
router.get('/status', 
  asyncHandler(async (req: Request, res: Response) => {
    const requestId = req.requestId;

    logger.info(`Auth status check`, {
      requestId,
      ip: req.ip || 'unknown',
    });

    return res.status(200).json({
      success: true,
      message: '🔐 Auth endpoint is operational',
      endpoints: {
        login: 'POST /api/v1/auth/login',
        status: 'GET /api/v1/auth/status',
      },
      timestamp: new Date().toISOString(),
    });
  })
);

// POST /api/v1/auth/verify - Verify password (alternative endpoint)
router.post('/verify', 
  validate(authSchema, 'body'),
  asyncHandler(async (req: Request<{}, AuthResponse, { password: string }>, res: Response<AuthResponse>) => {
    const { password } = req.body;
    const requestId = req.requestId;
    const clientIP = req.ip || 'unknown';

    logger.info(`Password verification attempt`, {
      requestId,
      ip: clientIP,
    });

    const isValid = password === 'suisuisui';

    if (isValid) {
      logger.info(`✅ Password verification successful`, {
        requestId,
        ip: clientIP,
      });
    } else {
      logger.warn(`❌ Password verification failed`, {
        requestId,
        ip: clientIP,
      });
    }

    return res.status(isValid ? 200 : 401).json({
      success: isValid,
      authenticated: isValid,
      message: isValid 
        ? '✅ Password is correct' 
        : '❌ Password is incorrect',
      timestamp: new Date().toISOString(),
      ...((!isValid) && {
        error: {
          code: 'INVALID_PASSWORD',
          details: 'The provided password does not match',
        },
      }),
    });
  })
);

export { router as authRoutes };
