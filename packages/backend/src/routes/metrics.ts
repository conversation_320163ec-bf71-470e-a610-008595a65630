import { Router, Request, Response } from 'express';
import { asyncHandler } from '../middleware/errorHandler.js';
import { validate, metricsQuerySchema } from '../validation/schemas.js';
import { redisClient } from '../services/redis.js';
import { suiService } from '../services/sui.js';
import { logger } from '../utils/logger.js';

const router = Router();

// Metrics interface
interface MetricsData {
  timestamp: string;
  timeframe: string;
  faucet: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    successRate: number;
    walletBalance: string;
    walletBalanceSui: number;
    isOperational: boolean;
  };
  rateLimits: {
    totalHits: number;
    ipLimitHits: number;
    walletLimitHits: number;
    globalLimitHits: number;
  };
  performance: {
    uptime: number;
    memoryUsage: NodeJS.MemoryUsage;
    averageResponseTime?: number;
  };
  network: {
    name: string;
    rpcUrl: string;
    faucetAddress: string;
  };
}

// Track process start time
const processStartTime = Date.now();

// GET /api/v1/metrics - Get comprehensive metrics
router.get('/', 
  validate(metricsQuerySchema, 'query'),
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const { timeframe, metric } = req.query as { timeframe?: string; metric?: string };

      // Get basic metrics from Redis
      const totalRequests = await redisClient.getMetric('requests_total');
      const successfulRequests = await redisClient.getMetric('requests_success');
      const failedRequests = await redisClient.getMetric('requests_failed');
      const rateLimitHits = await redisClient.getMetric('rate_limit_hits');
      const ipLimitHits = await redisClient.getMetric('ip_limit_hits');
      const walletLimitHits = await redisClient.getMetric('wallet_limit_hits');
      const globalLimitHits = await redisClient.getMetric('global_limit_hits');

      // Calculate success rate
      const successRate = totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0;

      // Get wallet information
      const walletInfo = await suiService.getWalletInfo();
      const networkInfo = suiService.networkInfo;

      // Build metrics response
      const metricsData: MetricsData = {
        timestamp: new Date().toISOString(),
        timeframe: timeframe || '24h',
        faucet: {
          totalRequests,
          successfulRequests,
          failedRequests,
          successRate: Math.round(successRate * 100) / 100,
          walletBalance: walletInfo.balance.toString(),
          walletBalanceSui: Number(walletInfo.balance) / 1_000_000_000,
          isOperational: suiService.isReady && !walletInfo.isLowBalance,
        },
        rateLimits: {
          totalHits: rateLimitHits,
          ipLimitHits,
          walletLimitHits,
          globalLimitHits,
        },
        performance: {
          uptime: Date.now() - processStartTime,
          memoryUsage: process.memoryUsage(),
        },
        network: {
          name: networkInfo.network,
          rpcUrl: networkInfo.rpcUrl,
          faucetAddress: networkInfo.walletAddress,
        },
      };

      // If specific metric requested, return only that metric
      if (metric) {
        let specificMetric: any;
        
        switch (metric) {
          case 'requests':
            specificMetric = {
              total: totalRequests,
              successful: successfulRequests,
              failed: failedRequests,
            };
            break;
          case 'success_rate':
            specificMetric = successRate;
            break;
          case 'response_time':
            specificMetric = await redisClient.getMetric('avg_response_time');
            break;
          case 'errors':
            specificMetric = failedRequests;
            break;
          case 'rate_limits':
            specificMetric = metricsData.rateLimits;
            break;
          default:
            return res.status(400).json({
              success: false,
              error: {
                code: 'INVALID_METRIC',
                message: `Unknown metric: ${metric}`,
              },
            });
        }

        return res.json({
          success: true,
          data: {
            metric,
            value: specificMetric,
            timestamp: new Date().toISOString(),
            timeframe: timeframe || '24h',
          },
        });
      }

      res.json({
        success: true,
        data: metricsData,
      });

    } catch (error: any) {
      logger.error('Failed to get metrics', {
        error: error.message,
        requestId: req.requestId,
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'METRICS_ERROR',
          message: 'Failed to retrieve metrics',
        },
      });
    }
  })
);

// GET /api/v1/metrics/prometheus - Prometheus-compatible metrics
router.get('/prometheus', 
  asyncHandler(async (req: Request, res: Response) => {
    try {
      // Get metrics from Redis
      const totalRequests = await redisClient.getMetric('requests_total');
      const successfulRequests = await redisClient.getMetric('requests_success');
      const failedRequests = await redisClient.getMetric('requests_failed');
      const rateLimitHits = await redisClient.getMetric('rate_limit_hits');

      // Get wallet balance
      const walletInfo = await suiService.getWalletInfo();
      const walletBalanceSui = Number(walletInfo.balance) / 1_000_000_000;

      // Calculate uptime in seconds
      const uptimeSeconds = Math.floor((Date.now() - processStartTime) / 1000);

      // Generate Prometheus metrics format
      const prometheusMetrics = `
# HELP sui_faucet_requests_total Total number of faucet requests
# TYPE sui_faucet_requests_total counter
sui_faucet_requests_total ${totalRequests}

# HELP sui_faucet_requests_successful_total Total number of successful faucet requests
# TYPE sui_faucet_requests_successful_total counter
sui_faucet_requests_successful_total ${successfulRequests}

# HELP sui_faucet_requests_failed_total Total number of failed faucet requests
# TYPE sui_faucet_requests_failed_total counter
sui_faucet_requests_failed_total ${failedRequests}

# HELP sui_faucet_rate_limit_hits_total Total number of rate limit hits
# TYPE sui_faucet_rate_limit_hits_total counter
sui_faucet_rate_limit_hits_total ${rateLimitHits}

# HELP sui_faucet_wallet_balance_sui Current wallet balance in SUI
# TYPE sui_faucet_wallet_balance_sui gauge
sui_faucet_wallet_balance_sui ${walletBalanceSui}

# HELP sui_faucet_uptime_seconds Uptime of the faucet service in seconds
# TYPE sui_faucet_uptime_seconds gauge
sui_faucet_uptime_seconds ${uptimeSeconds}

# HELP sui_faucet_operational Status of the faucet (1 = operational, 0 = not operational)
# TYPE sui_faucet_operational gauge
sui_faucet_operational ${suiService.isReady && !walletInfo.isLowBalance ? 1 : 0}
`.trim();

      res.set('Content-Type', 'text/plain; version=0.0.4; charset=utf-8');
      res.send(prometheusMetrics);

    } catch (error: any) {
      logger.error('Failed to generate Prometheus metrics', {
        error: error.message,
        requestId: req.requestId,
      });

      res.status(500).send('# Error generating metrics\n');
    }
  })
);

// GET /api/v1/metrics/summary - Quick metrics summary
router.get('/summary', 
  asyncHandler(async (req: Request, res: Response) => {
    try {
      const totalRequests = await redisClient.getMetric('requests_total');
      const successfulRequests = await redisClient.getMetric('requests_success');
      const walletInfo = await suiService.getWalletInfo();

      const summary = {
        totalRequests,
        successfulRequests,
        successRate: totalRequests > 0 ? Math.round((successfulRequests / totalRequests) * 100) : 0,
        walletBalanceSui: Math.round((Number(walletInfo.balance) / 1_000_000_000) * 100) / 100,
        isOperational: suiService.isReady && !walletInfo.isLowBalance,
        uptime: Math.floor((Date.now() - processStartTime) / 1000),
      };

      res.json({
        success: true,
        data: summary,
      });

    } catch (error: any) {
      logger.error('Failed to get metrics summary', {
        error: error.message,
        requestId: req.requestId,
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'METRICS_SUMMARY_ERROR',
          message: 'Failed to retrieve metrics summary',
        },
      });
    }
  })
);

// POST /api/v1/metrics/reset - Reset metrics (admin only)
router.post('/reset', 
  asyncHandler(async (req: Request, res: Response) => {
    try {
      // Reset all metrics
      await redisClient.setMetric('requests_total', 0);
      await redisClient.setMetric('requests_success', 0);
      await redisClient.setMetric('requests_failed', 0);
      await redisClient.setMetric('rate_limit_hits', 0);
      await redisClient.setMetric('ip_limit_hits', 0);
      await redisClient.setMetric('wallet_limit_hits', 0);
      await redisClient.setMetric('global_limit_hits', 0);

      logger.info('Metrics reset', {
        requestId: req.requestId,
        ip: req.ip,
      });

      res.json({
        success: true,
        message: 'Metrics reset successfully',
        timestamp: new Date().toISOString(),
      });

    } catch (error: any) {
      logger.error('Failed to reset metrics', {
        error: error.message,
        requestId: req.requestId,
      });

      res.status(500).json({
        success: false,
        error: {
          code: 'METRICS_RESET_ERROR',
          message: 'Failed to reset metrics',
        },
      });
    }
  })
);

export { router as metricsRoutes };
