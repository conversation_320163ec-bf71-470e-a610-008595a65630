import { Router, Request, Response } from 'express';
import { as<PERSON><PERSON>and<PERSON> } from '../middleware/errorHandler.js';
import { validate } from '../validation/schemas.js';
import { logger } from '../utils/logger.js';
import { config } from '../config/index.js';
import { suiService } from '../services/sui.js';
import { redisClient } from '../services/redis.js';
import { databaseService } from '../services/database.js';
import Joi from 'joi';

const router = Router();

// Admin login schema
const adminLoginSchema = Joi.object({
  username: Joi.string().required(),
  password: Joi.string().required(),
});

// Simple token storage (in production, use Redis or JWT)
const adminTokens = new Set<string>();

// Generate admin token
const generateAdminToken = (): string => {
  return Buffer.from(`admin_${Date.now()}_${Math.random()}`).toString('base64');
};

// Admin authentication middleware
const adminAuth = (req: Request, res: Response, next: any) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      message: '🚫 Admin authentication required. Please login first.',
      error: {
        code: 'ADMIN_AUTH_REQUIRED',
        details: 'Use POST /api/v1/admin/login to get access token',
      },
    });
  }

  const token = authHeader.substring(7); // Remove 'Bearer ' prefix

  if (!adminTokens.has(token)) {
    return res.status(401).json({
      success: false,
      message: '🚫 Invalid or expired admin token',
      error: {
        code: 'INVALID_ADMIN_TOKEN',
        details: 'Please login again to get a new token',
      },
    });
  }

  next();
};

// POST /api/v1/admin/login - Admin login
router.post('/login',
  validate(adminLoginSchema, 'body'),
  asyncHandler(async (req: Request, res: Response) => {
    const { username, password } = req.body;
    const requestId = req.requestId;

    if (username === config.auth.adminUsername && password === config.auth.adminPassword) {
      // Generate new admin token
      const token = generateAdminToken();
      adminTokens.add(token);

      logger.info(`✅ Admin login successful`, { requestId, username });

      return res.status(200).json({
        success: true,
        message: '🎉 Admin login successful! Welcome to Sui Faucet Admin.',
        data: {
          token,
          expiresIn: '24h',
          user: {
            username,
            role: 'admin',
          },
        },
        timestamp: new Date().toISOString(),
      });
    } else {
      logger.warn(`❌ Admin login failed`, { requestId, username });

      return res.status(401).json({
        success: false,
        message: '🚫 Invalid admin credentials',
        error: {
          code: 'INVALID_CREDENTIALS',
          details: 'Please check your username and password',
        },
      });
    }
  })
);

// POST /api/v1/admin/logout - Admin logout
router.post('/logout',
  adminAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const authHeader = req.headers.authorization;
    const token = authHeader?.substring(7); // Remove 'Bearer ' prefix
    const requestId = req.requestId;

    if (token) {
      adminTokens.delete(token);
      logger.info(`✅ Admin logout successful`, { requestId });
    }

    return res.status(200).json({
      success: true,
      message: '👋 Admin logout successful',
      timestamp: new Date().toISOString(),
    });
  })
);

// GET /api/v1/admin/me - Get current admin info
router.get('/me',
  adminAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const requestId = req.requestId;

    logger.info(`Admin info requested`, { requestId });

    return res.status(200).json({
      success: true,
      data: {
        user: {
          username: config.auth.adminUsername,
          role: 'admin',
        },
        permissions: [
          'view_dashboard',
          'view_health',
          'clear_cache',
          'view_logs',
        ],
      },
      timestamp: new Date().toISOString(),
    });
  })
);

// GET /api/v1/admin/dashboard - Admin dashboard data
router.get('/dashboard', 
  adminAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const requestId = req.requestId;
    
    try {
      // Get system stats
      const [
        walletBalance,
        totalRequests,
        successfulRequests,
        failedRequests,
      ] = await Promise.all([
        suiService.getWalletBalance(),
        redisClient.getMetric('requests_total'),
        redisClient.getMetric('requests_success'),
        redisClient.getMetric('requests_failed'),
      ]);

      const successRate = totalRequests > 0 ? (successfulRequests / totalRequests * 100).toFixed(2) : '0';
      
      logger.info(`Admin dashboard accessed`, { requestId });
      
      return res.status(200).json({
        success: true,
        data: {
          system: {
            status: 'operational',
            uptime: process.uptime(),
            timestamp: new Date().toISOString(),
          },
          faucet: {
            walletAddress: suiService.faucetAddress,
            balance: {
              mist: walletBalance.toString(),
              sui: (Number(walletBalance) / 1_000_000_000).toFixed(6),
            },
            network: config.sui.network,
            rpcUrl: config.sui.rpcUrl,
          },
          stats: {
            totalRequests: totalRequests || 0,
            successfulRequests: successfulRequests || 0,
            failedRequests: failedRequests || 0,
            successRate: `${successRate}%`,
          },
          config: {
            defaultAmount: {
              mist: config.sui.defaultAmount,
              sui: (Number(config.sui.defaultAmount) / 1_000_000_000).toFixed(1),
            },
            maxAmount: {
              mist: config.sui.maxAmount,
              sui: (Number(config.sui.maxAmount) / 1_000_000_000).toFixed(1),
            },
            rateLimits: {
              windowMs: config.rateLimits.windowMs,
              maxPerWallet: config.rateLimits.maxRequestsPerWallet,
              maxPerIP: config.rateLimits.maxRequestsPerIP,
            },
          },
        },
      });
      
    } catch (error: any) {
      logger.error('Admin dashboard error', { error: error.message, requestId });
      
      return res.status(500).json({
        success: false,
        message: '❌ Failed to load dashboard data',
        error: { code: 'DASHBOARD_ERROR', details: error.message },
      });
    }
  })
);

// GET /api/v1/admin/health - Detailed health check for admin
router.get('/health',
  adminAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const requestId = req.requestId;
    
    try {
      // Check all services
      const suiHealth = await suiService.healthCheck();
      const redisHealth = await redisClient.healthCheck();
      
      const overallStatus = suiHealth.status === 'healthy' && redisHealth.status === 'healthy' 
        ? 'healthy' : 'unhealthy';
      
      logger.info(`Admin health check`, { requestId, status: overallStatus });
      
      return res.status(200).json({
        success: true,
        data: {
          status: overallStatus,
          timestamp: new Date().toISOString(),
          services: {
            sui: suiHealth,
            redis: redisHealth,
          },
          system: {
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            nodeVersion: process.version,
          },
        },
      });
      
    } catch (error: any) {
      logger.error('Admin health check error', { error: error.message, requestId });
      
      return res.status(500).json({
        success: false,
        message: '❌ Health check failed',
        error: { code: 'HEALTH_CHECK_ERROR', details: error.message },
      });
    }
  })
);

// POST /api/v1/admin/clear-cache - Clear Redis cache
router.post('/clear-cache',
  adminAuth,
  asyncHandler(async (req: Request, res: Response) => {
    const requestId = req.requestId;
    
    try {
      await redisClient.clearAll();
      
      logger.info(`✅ Admin cleared cache`, { requestId });
      
      return res.status(200).json({
        success: true,
        message: '✅ Cache cleared successfully',
        timestamp: new Date().toISOString(),
      });
      
    } catch (error: any) {
      logger.error('Admin clear cache error', { error: error.message, requestId });
      
      return res.status(500).json({
        success: false,
        message: '❌ Failed to clear cache',
        error: { code: 'CLEAR_CACHE_ERROR', details: error.message },
      });
    }
  })
);

export { router as adminRoutes };
