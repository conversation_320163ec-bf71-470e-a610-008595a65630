import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { logRequest, logResponse } from '../utils/logger.js';

// Extend Request interface to include custom properties
declare global {
  namespace Express {
    interface Request {
      requestId: string;
      startTime: number;
    }
  }
}

export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  // Generate unique request ID
  const requestId = req.headers['x-request-id'] as string || uuidv4();
  req.requestId = requestId;
  req.startTime = Date.now();

  // Add request ID to response headers
  res.setHeader('X-Request-ID', requestId);

  // Get client IP (considering proxies)
  const getClientIP = (req: Request): string => {
    const forwarded = req.headers['x-forwarded-for'] as string;
    const realIP = req.headers['x-real-ip'] as string;
    const remoteAddress = req.socket?.remoteAddress;

    if (forwarded) {
      // X-Forwarded-For can contain multiple IPs, take the first one
      return forwarded.split(',')[0]?.trim() || 'unknown';
    }

    if (realIP) {
      return realIP;
    }

    return remoteAddress || 'unknown';
  };

  const clientIP = getClientIP(req);
  const userAgent = req.get('User-Agent');

  // Log incoming request
  logRequest(
    requestId,
    req.method,
    req.originalUrl || req.url,
    clientIP,
    userAgent
  );

  // Override res.end to log response
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any, cb?: any): Response {
    // Calculate response time
    const responseTime = Date.now() - req.startTime;

    // Get content length
    const contentLength = res.get('Content-Length');
    const contentLengthNum = contentLength ? parseInt(contentLength) : undefined;

    // Log response
    logResponse(
      requestId,
      res.statusCode,
      responseTime,
      contentLengthNum
    );

    // Call original end method
    return originalEnd.call(this, chunk, encoding, cb);
  };

  next();
};
