{"data":{"error":{"code":"RATE_LIMIT_EXCEEDED","message":"Rate limit exceeded. Please try again in 866 seconds.","requestId":"unknown","retryAfter":866,"stack":"RateLimitError: Rate limit exceeded. Please try again in 866 seconds.\n    at rateLimiter (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/middleware/rateLimiter.ts:126:13)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:346:12)\n    at next (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:280:10)\n    at <anonymous> (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/index.ts:60:3)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9"},"success":false},"level":"error","message":"Faucet API Response Error Request failed with status code 429","service":"discord-bot","status":429,"timestamp":"2025-07-19T09:15:00.040Z"}
{"error":{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"http://localhost:3001/api/v1","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Type":"application/json","User-Agent":"axios/1.10.0","X-API-Key":"suisuisui"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/faucet/status","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"message":"Request failed with status code 429","name":"AxiosError","stack":"AxiosError: Request failed with status code 429\n    at settle (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/adapters/http.js:599:11)\n    at IncomingMessage.emit (node:events:519:35)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FaucetApiService.getFaucetStatus (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/services/faucetApi.ts:116:24)\n    at async Promise.all (index 0)\n    at async updateBotActivity (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/index.ts:100:39)\n    at async Timeout._onTimeout (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/node-cron/src/scheduler/runner.ts:100:30)","status":429},"level":"error","message":"Failed to get faucet status","service":"discord-bot","timestamp":"2025-07-19T09:15:00.046Z"}
{"data":{"error":{"code":"RATE_LIMIT_EXCEEDED","message":"Rate limit exceeded. Please try again in 566 seconds.","requestId":"unknown","retryAfter":566,"stack":"RateLimitError: Rate limit exceeded. Please try again in 566 seconds.\n    at rateLimiter (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/middleware/rateLimiter.ts:126:13)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:346:12)\n    at next (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:280:10)\n    at <anonymous> (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/index.ts:60:3)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9"},"success":false},"level":"error","message":"Faucet API Response Error Request failed with status code 429","service":"discord-bot","status":429,"timestamp":"2025-07-19T09:20:00.045Z"}
{"error":{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"http://localhost:3001/api/v1","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Type":"application/json","User-Agent":"axios/1.10.0","X-API-Key":"suisuisui"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/faucet/status","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"message":"Request failed with status code 429","name":"AxiosError","stack":"AxiosError: Request failed with status code 429\n    at settle (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/adapters/http.js:599:11)\n    at IncomingMessage.emit (node:events:519:35)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FaucetApiService.getFaucetStatus (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/services/faucetApi.ts:116:24)\n    at async Promise.all (index 0)\n    at async updateBotActivity (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/index.ts:100:39)\n    at async Timeout._onTimeout (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/node-cron/src/scheduler/runner.ts:100:30)","status":429},"level":"error","message":"Failed to get faucet status","service":"discord-bot","timestamp":"2025-07-19T09:20:00.049Z"}
{"data":{"error":{"code":"RATE_LIMIT_EXCEEDED","message":"Rate limit exceeded. Please try again in 266 seconds.","requestId":"unknown","retryAfter":266,"stack":"RateLimitError: Rate limit exceeded. Please try again in 266 seconds.\n    at rateLimiter (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/middleware/rateLimiter.ts:126:13)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:346:12)\n    at next (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:280:10)\n    at <anonymous> (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/index.ts:60:3)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9"},"success":false},"level":"error","message":"Faucet API Response Error Request failed with status code 429","service":"discord-bot","status":429,"timestamp":"2025-07-19T09:25:00.039Z"}
{"error":{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"http://localhost:3001/api/v1","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Type":"application/json","User-Agent":"axios/1.10.0","X-API-Key":"suisuisui"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/faucet/status","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"message":"Request failed with status code 429","name":"AxiosError","stack":"AxiosError: Request failed with status code 429\n    at settle (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/adapters/http.js:599:11)\n    at IncomingMessage.emit (node:events:519:35)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FaucetApiService.getFaucetStatus (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/services/faucetApi.ts:116:24)\n    at async Promise.all (index 0)\n    at async updateBotActivity (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/index.ts:100:39)\n    at async Timeout._onTimeout (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/node-cron/src/scheduler/runner.ts:100:30)","status":429},"level":"error","message":"Failed to get faucet status","service":"discord-bot","timestamp":"2025-07-19T09:25:00.040Z"}
{"data":{"error":{"code":"RATE_LIMIT_EXCEEDED","message":"Rate limit exceeded. Please try again in 600 seconds.","requestId":"unknown","retryAfter":600,"stack":"RateLimitError: Rate limit exceeded. Please try again in 600 seconds.\n    at rateLimiter (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/middleware/rateLimiter.ts:126:13)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:346:12)\n    at next (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:280:10)\n    at <anonymous> (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/index.ts:60:3)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9"},"success":false},"level":"error","message":"Faucet API Response Error Request failed with status code 429","service":"discord-bot","status":429,"timestamp":"2025-07-19T10:25:00.093Z"}
{"error":{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"http://localhost:3001/api/v1","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Type":"application/json","User-Agent":"axios/1.10.0","X-API-Key":"suisuisui"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/faucet/status","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"message":"Request failed with status code 429","name":"AxiosError","stack":"AxiosError: Request failed with status code 429\n    at settle (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/adapters/http.js:599:11)\n    at IncomingMessage.emit (node:events:519:35)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FaucetApiService.getFaucetStatus (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/services/faucetApi.ts:116:24)\n    at async Promise.all (index 0)\n    at async updateBotActivity (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/index.ts:100:39)\n    at async Timeout._onTimeout (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/node-cron/src/scheduler/runner.ts:100:30)","status":429},"level":"error","message":"Failed to get faucet status","service":"discord-bot","timestamp":"2025-07-19T10:25:00.097Z"}
{"data":{"error":{"code":"RATE_LIMIT_EXCEEDED","message":"Rate limit exceeded. Please try again in 300 seconds.","requestId":"unknown","retryAfter":300,"stack":"RateLimitError: Rate limit exceeded. Please try again in 300 seconds.\n    at rateLimiter (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/middleware/rateLimiter.ts:126:13)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:346:12)\n    at next (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:280:10)\n    at <anonymous> (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/index.ts:60:3)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9"},"success":false},"level":"error","message":"Faucet API Response Error Request failed with status code 429","service":"discord-bot","status":429,"timestamp":"2025-07-19T10:30:00.050Z"}
{"error":{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"http://localhost:3001/api/v1","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Type":"application/json","User-Agent":"axios/1.10.0","X-API-Key":"suisuisui"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/faucet/status","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"message":"Request failed with status code 429","name":"AxiosError","stack":"AxiosError: Request failed with status code 429\n    at settle (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/adapters/http.js:599:11)\n    at IncomingMessage.emit (node:events:519:35)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FaucetApiService.getFaucetStatus (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/services/faucetApi.ts:116:24)\n    at async Promise.all (index 0)\n    at async updateBotActivity (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/index.ts:100:39)\n    at async Timeout._onTimeout (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/node-cron/src/scheduler/runner.ts:100:30)","status":429},"level":"error","message":"Failed to get faucet status","service":"discord-bot","timestamp":"2025-07-19T10:30:00.051Z"}
{"data":{"error":{"code":"RATE_LIMIT_EXCEEDED","message":"Rate limit exceeded. Please try again in 1 seconds.","requestId":"unknown","retryAfter":1,"stack":"RateLimitError: Rate limit exceeded. Please try again in 1 seconds.\n    at rateLimiter (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/middleware/rateLimiter.ts:126:13)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:346:12)\n    at next (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:280:10)\n    at <anonymous> (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/index.ts:60:3)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9"},"success":false},"level":"error","message":"Faucet API Response Error Request failed with status code 429","service":"discord-bot","status":429,"timestamp":"2025-07-19T10:35:00.026Z"}
{"error":{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"http://localhost:3001/api/v1","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Type":"application/json","User-Agent":"axios/1.10.0","X-API-Key":"suisuisui"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/faucet/status","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"message":"Request failed with status code 429","name":"AxiosError","stack":"AxiosError: Request failed with status code 429\n    at settle (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/adapters/http.js:599:11)\n    at IncomingMessage.emit (node:events:519:35)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FaucetApiService.getFaucetStatus (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/services/faucetApi.ts:116:24)\n    at async Promise.all (index 0)\n    at async updateBotActivity (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/index.ts:100:39)\n    at async Timeout._onTimeout (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/node-cron/src/scheduler/runner.ts:100:30)","status":429},"level":"error","message":"Failed to get faucet status","service":"discord-bot","timestamp":"2025-07-19T10:35:00.027Z"}
