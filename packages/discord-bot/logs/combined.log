{"level":"info","message":"Started refreshing application (/) commands.","service":"discord-bot","timestamp":"2025-07-19T08:39:07.865Z"}
{"level":"info","message":"Successfully reloaded 4 global application (/) commands.","service":"discord-bot","timestamp":"2025-07-19T08:39:08.574Z"}
{"botId":"1396045800798162984","botTag":"sui-faucet-bot#7494","guildCount":0,"level":"info","message":"Discord bot is ready!","service":"discord-bot","timestamp":"2025-07-19T08:46:25.476Z"}
{"channelId":"1122847284032438353","guildId":"1122847283592052776","level":"info","message":"Faucet request received","service":"discord-bot","timestamp":"2025-07-19T08:48:22.606Z","userId":"1094899638269661204","username":"_0xboji","walletAddress":"0x7f67a90d907dc08ba4e411c86494b14386808394a76b9196a783e9c1ee89ba86"}
{"amount":"100000000","level":"info","message":"Faucet request successful","service":"discord-bot","timestamp":"2025-07-19T08:48:27.063Z","transactionHash":"CXWDqKgMKgEVh22tEBeorgjTSkjQyDT2CpYLLn1tY1kX","userId":"1094899638269661204","username":"_0xboji","walletAddress":"0x7f67a90d907dc08ba4e411c86494b14386808394a76b9196a783e9c1ee89ba86"}
{"level":"warn","message":"Health check failed - faucet API is unhealthy","service":"discord-bot","timestamp":"2025-07-19T08:50:00.954Z"}
{"level":"warn","message":"Health check failed - faucet API is unhealthy","service":"discord-bot","timestamp":"2025-07-19T09:00:01.203Z"}
{"level":"warn","message":"Health check failed - faucet API is unhealthy","service":"discord-bot","timestamp":"2025-07-19T09:10:00.175Z"}
{"data":{"error":{"code":"RATE_LIMIT_EXCEEDED","message":"Rate limit exceeded. Please try again in 866 seconds.","requestId":"unknown","retryAfter":866,"stack":"RateLimitError: Rate limit exceeded. Please try again in 866 seconds.\n    at rateLimiter (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/middleware/rateLimiter.ts:126:13)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:346:12)\n    at next (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:280:10)\n    at <anonymous> (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/index.ts:60:3)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9"},"success":false},"level":"error","message":"Faucet API Response Error Request failed with status code 429","service":"discord-bot","status":429,"timestamp":"2025-07-19T09:15:00.040Z"}
{"error":{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"http://localhost:3001/api/v1","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Type":"application/json","User-Agent":"axios/1.10.0","X-API-Key":"suisuisui"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/faucet/status","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"message":"Request failed with status code 429","name":"AxiosError","stack":"AxiosError: Request failed with status code 429\n    at settle (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/adapters/http.js:599:11)\n    at IncomingMessage.emit (node:events:519:35)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FaucetApiService.getFaucetStatus (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/services/faucetApi.ts:116:24)\n    at async Promise.all (index 0)\n    at async updateBotActivity (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/index.ts:100:39)\n    at async Timeout._onTimeout (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/node-cron/src/scheduler/runner.ts:100:30)","status":429},"level":"error","message":"Failed to get faucet status","service":"discord-bot","timestamp":"2025-07-19T09:15:00.046Z"}
{"data":{"error":{"code":"RATE_LIMIT_EXCEEDED","message":"Rate limit exceeded. Please try again in 566 seconds.","requestId":"unknown","retryAfter":566,"stack":"RateLimitError: Rate limit exceeded. Please try again in 566 seconds.\n    at rateLimiter (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/middleware/rateLimiter.ts:126:13)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:346:12)\n    at next (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:280:10)\n    at <anonymous> (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/index.ts:60:3)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9"},"success":false},"level":"error","message":"Faucet API Response Error Request failed with status code 429","service":"discord-bot","status":429,"timestamp":"2025-07-19T09:20:00.045Z"}
{"error":{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"http://localhost:3001/api/v1","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Type":"application/json","User-Agent":"axios/1.10.0","X-API-Key":"suisuisui"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/faucet/status","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"message":"Request failed with status code 429","name":"AxiosError","stack":"AxiosError: Request failed with status code 429\n    at settle (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/adapters/http.js:599:11)\n    at IncomingMessage.emit (node:events:519:35)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FaucetApiService.getFaucetStatus (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/services/faucetApi.ts:116:24)\n    at async Promise.all (index 0)\n    at async updateBotActivity (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/index.ts:100:39)\n    at async Timeout._onTimeout (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/node-cron/src/scheduler/runner.ts:100:30)","status":429},"level":"error","message":"Failed to get faucet status","service":"discord-bot","timestamp":"2025-07-19T09:20:00.049Z"}
{"level":"warn","message":"Health check failed - faucet API is unhealthy","service":"discord-bot","timestamp":"2025-07-19T09:20:00.173Z"}
{"data":{"error":{"code":"RATE_LIMIT_EXCEEDED","message":"Rate limit exceeded. Please try again in 266 seconds.","requestId":"unknown","retryAfter":266,"stack":"RateLimitError: Rate limit exceeded. Please try again in 266 seconds.\n    at rateLimiter (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/middleware/rateLimiter.ts:126:13)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:346:12)\n    at next (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:280:10)\n    at <anonymous> (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/index.ts:60:3)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9"},"success":false},"level":"error","message":"Faucet API Response Error Request failed with status code 429","service":"discord-bot","status":429,"timestamp":"2025-07-19T09:25:00.039Z"}
{"error":{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"http://localhost:3001/api/v1","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Type":"application/json","User-Agent":"axios/1.10.0","X-API-Key":"suisuisui"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/faucet/status","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"message":"Request failed with status code 429","name":"AxiosError","stack":"AxiosError: Request failed with status code 429\n    at settle (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/adapters/http.js:599:11)\n    at IncomingMessage.emit (node:events:519:35)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FaucetApiService.getFaucetStatus (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/services/faucetApi.ts:116:24)\n    at async Promise.all (index 0)\n    at async updateBotActivity (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/index.ts:100:39)\n    at async Timeout._onTimeout (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/node-cron/src/scheduler/runner.ts:100:30)","status":429},"level":"error","message":"Failed to get faucet status","service":"discord-bot","timestamp":"2025-07-19T09:25:00.040Z"}
{"level":"warn","message":"Health check failed - faucet API is unhealthy","service":"discord-bot","timestamp":"2025-07-19T09:40:00.177Z"}
{"level":"warn","message":"Health check failed - faucet API is unhealthy","service":"discord-bot","timestamp":"2025-07-19T09:50:00.222Z"}
{"level":"warn","message":"Health check failed - faucet API is unhealthy","service":"discord-bot","timestamp":"2025-07-19T10:00:00.153Z"}
{"level":"warn","message":"Health check failed - faucet API is unhealthy","service":"discord-bot","timestamp":"2025-07-19T10:10:00.173Z"}
{"level":"warn","message":"Health check failed - faucet API is unhealthy","service":"discord-bot","timestamp":"2025-07-19T10:20:00.186Z"}
{"data":{"error":{"code":"RATE_LIMIT_EXCEEDED","message":"Rate limit exceeded. Please try again in 600 seconds.","requestId":"unknown","retryAfter":600,"stack":"RateLimitError: Rate limit exceeded. Please try again in 600 seconds.\n    at rateLimiter (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/middleware/rateLimiter.ts:126:13)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:346:12)\n    at next (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:280:10)\n    at <anonymous> (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/index.ts:60:3)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9"},"success":false},"level":"error","message":"Faucet API Response Error Request failed with status code 429","service":"discord-bot","status":429,"timestamp":"2025-07-19T10:25:00.093Z"}
{"error":{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"http://localhost:3001/api/v1","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Type":"application/json","User-Agent":"axios/1.10.0","X-API-Key":"suisuisui"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/faucet/status","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"message":"Request failed with status code 429","name":"AxiosError","stack":"AxiosError: Request failed with status code 429\n    at settle (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/adapters/http.js:599:11)\n    at IncomingMessage.emit (node:events:519:35)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FaucetApiService.getFaucetStatus (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/services/faucetApi.ts:116:24)\n    at async Promise.all (index 0)\n    at async updateBotActivity (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/index.ts:100:39)\n    at async Timeout._onTimeout (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/node-cron/src/scheduler/runner.ts:100:30)","status":429},"level":"error","message":"Failed to get faucet status","service":"discord-bot","timestamp":"2025-07-19T10:25:00.097Z"}
{"data":{"error":{"code":"RATE_LIMIT_EXCEEDED","message":"Rate limit exceeded. Please try again in 300 seconds.","requestId":"unknown","retryAfter":300,"stack":"RateLimitError: Rate limit exceeded. Please try again in 300 seconds.\n    at rateLimiter (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/middleware/rateLimiter.ts:126:13)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:346:12)\n    at next (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:280:10)\n    at <anonymous> (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/index.ts:60:3)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9"},"success":false},"level":"error","message":"Faucet API Response Error Request failed with status code 429","service":"discord-bot","status":429,"timestamp":"2025-07-19T10:30:00.050Z"}
{"error":{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"http://localhost:3001/api/v1","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Type":"application/json","User-Agent":"axios/1.10.0","X-API-Key":"suisuisui"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/faucet/status","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"message":"Request failed with status code 429","name":"AxiosError","stack":"AxiosError: Request failed with status code 429\n    at settle (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/adapters/http.js:599:11)\n    at IncomingMessage.emit (node:events:519:35)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FaucetApiService.getFaucetStatus (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/services/faucetApi.ts:116:24)\n    at async Promise.all (index 0)\n    at async updateBotActivity (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/index.ts:100:39)\n    at async Timeout._onTimeout (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/node-cron/src/scheduler/runner.ts:100:30)","status":429},"level":"error","message":"Failed to get faucet status","service":"discord-bot","timestamp":"2025-07-19T10:30:00.051Z"}
{"level":"warn","message":"Health check failed - faucet API is unhealthy","service":"discord-bot","timestamp":"2025-07-19T10:30:00.198Z"}
{"data":{"error":{"code":"RATE_LIMIT_EXCEEDED","message":"Rate limit exceeded. Please try again in 1 seconds.","requestId":"unknown","retryAfter":1,"stack":"RateLimitError: Rate limit exceeded. Please try again in 1 seconds.\n    at rateLimiter (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/middleware/rateLimiter.ts:126:13)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9\n    at router.process_params (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:346:12)\n    at next (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:280:10)\n    at <anonymous> (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/backend/src/index.ts:60:3)\n    at Layer.handle [as handle_request] (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:328:13)\n    at /Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/express/lib/router/index.js:286:9"},"success":false},"level":"error","message":"Faucet API Response Error Request failed with status code 429","service":"discord-bot","status":429,"timestamp":"2025-07-19T10:35:00.026Z"}
{"error":{"code":"ERR_BAD_REQUEST","config":{"adapter":["xhr","http","fetch"],"allowAbsoluteUrls":true,"baseURL":"http://localhost:3001/api/v1","env":{},"headers":{"Accept":"application/json, text/plain, */*","Accept-Encoding":"gzip, compress, deflate, br","Content-Type":"application/json","User-Agent":"axios/1.10.0","X-API-Key":"suisuisui"},"maxBodyLength":-1,"maxContentLength":-1,"method":"get","timeout":30000,"transformRequest":[null],"transformResponse":[null],"transitional":{"clarifyTimeoutError":false,"forcedJSONParsing":true,"silentJSONParsing":true},"url":"/faucet/status","xsrfCookieName":"XSRF-TOKEN","xsrfHeaderName":"X-XSRF-TOKEN"},"message":"Request failed with status code 429","name":"AxiosError","stack":"AxiosError: Request failed with status code 429\n    at settle (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/settle.js:19:12)\n    at IncomingMessage.handleStreamEnd (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/adapters/http.js:599:11)\n    at IncomingMessage.emit (node:events:519:35)\n    at endReadableNT (node:internal/streams/readable:1701:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)\n    at Axios.request (file:///Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/axios/lib/core/Axios.js:45:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async FaucetApiService.getFaucetStatus (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/services/faucetApi.ts:116:24)\n    at async Promise.all (index 0)\n    at async updateBotActivity (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/packages/discord-bot/src/index.ts:100:39)\n    at async Timeout._onTimeout (/Volumes/0xbojissd/work/first-move/sui-faucet-core-node/node_modules/node-cron/src/scheduler/runner.ts:100:30)","status":429},"level":"error","message":"Failed to get faucet status","service":"discord-bot","timestamp":"2025-07-19T10:35:00.027Z"}
{"level":"warn","message":"Health check failed - faucet API is unhealthy","service":"discord-bot","timestamp":"2025-07-19T10:40:00.182Z"}
{"level":"warn","message":"Health check failed - faucet API is unhealthy","service":"discord-bot","timestamp":"2025-07-19T10:50:00.200Z"}
{"level":"warn","message":"Health check failed - faucet API is unhealthy","service":"discord-bot","timestamp":"2025-07-19T11:00:00.356Z"}
{"level":"warn","message":"Health check failed - faucet API is unhealthy","service":"discord-bot","timestamp":"2025-07-19T11:10:00.414Z"}
