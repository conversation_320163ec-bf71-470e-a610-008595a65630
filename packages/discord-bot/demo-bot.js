#!/usr/bin/env node

/**
 * Demo Discord Bot for Sui Faucet
 * This is a simplified version to demonstrate the bot functionality
 */

console.log('🤖 Sui Faucet Discord Bot Demo');
console.log('================================');

// Check if backend is running
import axios from 'axios';

async function checkBackend() {
  try {
    console.log('🔍 Checking backend health...');
    const response = await axios.get('http://localhost:3001/api/v1/health');
    console.log('✅ Backend is healthy:', response.data.status);
    return true;
  } catch (error) {
    console.log('❌ Backend not accessible:', error.message);
    console.log('💡 Please start the backend first:');
    console.log('   cd packages/backend && npm run dev');
    return false;
  }
}

async function testFaucetAPI() {
  try {
    console.log('\n🚰 Testing faucet API...');
    const response = await axios.post('http://localhost:3001/api/v1/faucet/request', {
      address: '0x1234567890abcdef1234567890abcdef12345678901234567890abcdef123456'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'suisuisui'
      }
    });
    
    console.log('✅ Faucet API response:', response.data.message);
    return true;
  } catch (error) {
    if (error.response?.data) {
      console.log('⚠️ Faucet API response:', error.response.data.message);
    } else {
      console.log('❌ Faucet API error:', error.message);
    }
    return false;
  }
}

async function simulateDiscordCommands() {
  console.log('\n🎮 Simulating Discord Bot Commands:');
  console.log('=====================================');
  
  // Simulate /faucet command
  console.log('\n👤 User: /faucet 0x1234567890abcdef1234567890abcdef12345678901234567890abcdef123456');
  
  try {
    const response = await axios.post('http://localhost:3001/api/v1/faucet/request', {
      address: '0x1234567890abcdef1234567890abcdef12345678901234567890abcdef123456'
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': 'suisuisui'
      }
    });
    
    if (response.data.success) {
      console.log('🤖 Bot Response:');
      console.log('   ✅ Tokens Sent Successfully!');
      console.log(`   💰 Amount: ${Number(response.data.amount) / 1_000_000_000} SUI`);
      console.log(`   📝 Transaction: ${response.data.transactionHash}`);
      console.log(`   🔗 Explorer: https://suiscan.xyz/testnet/tx/${response.data.transactionHash}`);
    }
  } catch (error) {
    if (error.response?.data) {
      console.log('🤖 Bot Response:');
      if (error.response.data.error?.code === 'RATE_LIMIT_EXCEEDED') {
        console.log('   ⏰ Rate Limited');
        console.log(`   ⌛ Retry after: ${Math.ceil(error.response.data.retryAfter / 60)} minutes`);
      } else {
        console.log('   ❌ Request Failed');
        console.log(`   📝 Message: ${error.response.data.message}`);
      }
    }
  }
  
  // Simulate /status command
  console.log('\n👤 User: /status');
  
  try {
    const statusResponse = await axios.get('http://localhost:3001/api/v1/faucet/status', {
      headers: { 'X-API-Key': 'suisuisui' }
    });
    
    console.log('🤖 Bot Response:');
    console.log('   🚰 Sui Faucet Status');
    console.log('   ✅ Faucet is online and operational');
    console.log(`   💰 Balance: ${statusResponse.data.balanceInSui.toFixed(4)} SUI`);
    console.log(`   🎁 Default Amount: ${statusResponse.data.defaultAmountInSui} SUI`);
    console.log(`   🌐 Network: ${statusResponse.data.network.toUpperCase()}`);
  } catch (error) {
    console.log('🤖 Bot Response:');
    console.log('   ❌ Failed to get faucet status');
  }
}

async function showBotSetupInstructions() {
  console.log('\n📋 TO SETUP REAL DISCORD BOT:');
  console.log('==============================');
  console.log('1. Go to: https://discord.com/developers/applications');
  console.log('2. Create New Application → Bot');
  console.log('3. Copy Bot Token and Client ID');
  console.log('4. Update packages/discord-bot/.env:');
  console.log('   DISCORD_TOKEN=your_bot_token');
  console.log('   DISCORD_CLIENT_ID=your_client_id');
  console.log('5. Deploy commands: npm run deploy-commands');
  console.log('6. Start bot: npm run dev');
  console.log('7. Invite bot to server with slash commands permission');
  
  console.log('\n🔗 Bot Invite URL Template:');
  console.log('https://discord.com/api/oauth2/authorize?client_id=YOUR_CLIENT_ID&permissions=**********&scope=bot%20applications.commands');
  
  console.log('\n🎮 Available Commands:');
  console.log('• /faucet <address> - Request SUI tokens');
  console.log('• /status - Check faucet status');
  console.log('• /help - Show help information');
  console.log('• /admin stats - Admin statistics (requires admin role)');
}

// Main demo function
async function runDemo() {
  const backendHealthy = await checkBackend();
  
  if (backendHealthy) {
    await testFaucetAPI();
    await simulateDiscordCommands();
  }
  
  await showBotSetupInstructions();
  
  console.log('\n🎉 Demo completed!');
  console.log('💡 Start the real Discord bot with: npm run dev');
}

// Run demo
runDemo().catch(console.error);
