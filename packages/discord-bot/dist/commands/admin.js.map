{"version": 3, "file": "admin.js", "sourceRoot": "", "sources": ["../../src/commands/admin.ts"], "names": [], "mappings": "AAAA,OAAO,EACL,mBAAmB,EAEnB,YAAY,EACZ,MAAM,EACN,mBAAmB,EACpB,MAAM,YAAY,CAAC;AACpB,OAAO,EAAE,SAAS,EAAE,MAAM,0BAA0B,CAAC;AACrD,OAAO,EAAE,WAAW,EAAE,MAAM,4BAA4B,CAAC;AACzD,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAC5C,OAAO,EAAE,MAAM,EAAE,MAAM,oBAAoB,CAAC;AAE5C,MAAM,CAAC,MAAM,IAAI,GAAG,IAAI,mBAAmB,EAAE;KAC1C,OAAO,CAAC,OAAO,CAAC;KAChB,cAAc,CAAC,4CAA4C,CAAC;KAC5D,2BAA2B,CAAC,mBAAmB,CAAC,aAAa,CAAC;KAC9D,aAAa,CAAC,UAAU,CAAC,EAAE,CAC1B,UAAU;KACP,OAAO,CAAC,OAAO,CAAC;KAChB,cAAc,CAAC,yCAAyC,CAAC,CAC7D;KACA,aAAa,CAAC,UAAU,CAAC,EAAE,CAC1B,UAAU;KACP,OAAO,CAAC,cAAc,CAAC;KACvB,cAAc,CAAC,8BAA8B,CAAC;KAC9C,aAAa,CAAC,MAAM,CAAC,EAAE,CACtB,MAAM;KACH,OAAO,CAAC,MAAM,CAAC;KACf,cAAc,CAAC,+BAA+B,CAAC;KAC/C,WAAW,CAAC,IAAI,CAAC,CACrB,CACJ;KACA,aAAa,CAAC,UAAU,CAAC,EAAE,CAC1B,UAAU;KACP,OAAO,CAAC,kBAAkB,CAAC;KAC3B,cAAc,CAAC,0CAA0C,CAAC,CAC9D;KACA,aAAa,CAAC,UAAU,CAAC,EAAE,CAC1B,UAAU;KACP,OAAO,CAAC,QAAQ,CAAC;KACjB,cAAc,CAAC,iDAAiD,CAAC,CACrE,CAAC;AAEJ,MAAM,CAAC,KAAK,UAAU,OAAO,CAAC,WAAwC;IAEpE,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,GAAG,CAAC,mBAAmB,CAAC,aAAa,CAAC,EAAE,CAAC;QAC3E,MAAM,WAAW,GAAG,IAAI,YAAY,EAAE;aACnC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC;aACpB,QAAQ,CAAC,iBAAiB,CAAC;aAC3B,cAAc,CAAC,2DAA2D,CAAC;aAC3E,YAAY,EAAE,CAAC;QAElB,MAAM,WAAW,CAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,WAAW,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;QAC9D,OAAO;IACT,CAAC;IAED,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;IACvD,MAAM,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;IACnC,MAAM,QAAQ,GAAG,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;IAE3C,MAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE;QACpC,MAAM;QACN,QAAQ;QACR,UAAU;QACV,OAAO,EAAE,WAAW,CAAC,OAAO;KAC7B,CAAC,CAAC;IAEH,MAAM,WAAW,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;IAE5C,IAAI,CAAC;QACH,QAAQ,UAAU,EAAE,CAAC;YACnB,KAAK,OAAO;gBACV,MAAM,WAAW,CAAC,WAAW,CAAC,CAAC;gBAC/B,MAAM;YACR,KAAK,cAAc;gBACjB,MAAM,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBACrC,MAAM;YACR,KAAK,kBAAkB;gBACrB,MAAM,oBAAoB,CAAC,WAAW,CAAC,CAAC;gBACxC,MAAM;YACR,KAAK,QAAQ;gBACX,MAAM,YAAY,CAAC,WAAW,CAAC,CAAC;gBAChC,MAAM;YACR;gBACE,MAAM,IAAI,KAAK,CAAC,uBAAuB,UAAU,EAAE,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,UAAU,GAAG,IAAI,YAAY,EAAE;aAClC,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC;aACpB,QAAQ,CAAC,wBAAwB,CAAC;aAClC,cAAc,CAAC,sDAAsD,CAAC;aACtE,SAAS,CAAC;YACT,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;YAC/D,MAAM,EAAE,KAAK;SACd,CAAC;aACD,YAAY,EAAE,CAAC;QAElB,MAAM,WAAW,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;QAEtD,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE;YAClC,MAAM;YACN,QAAQ;YACR,UAAU;YACV,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;YAC/D,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;SACxD,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED,KAAK,UAAU,WAAW,CAAC,WAAwC;IACjE,MAAM,CAAC,YAAY,EAAE,SAAS,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QAClD,SAAS,CAAC,eAAe,EAAE;QAC3B,SAAS,CAAC,SAAS,EAAE;KACtB,CAAC,CAAC;IAGH,IAAI,UAAU,GAAG,IAAI,CAAC;IACtB,IAAI,CAAC;QACH,UAAU,GAAG,MAAM,SAAS,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;IAChD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;IACtD,CAAC;IAED,MAAM,cAAc,GAAG,WAAW,CAAC,QAAQ,EAAE,CAAC;IAE9C,MAAM,UAAU,GAAG,IAAI,YAAY,EAAE;SAClC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC;SACrB,QAAQ,CAAC,qBAAqB,CAAC;SAC/B,cAAc,CAAC,4CAA4C,CAAC;SAC5D,YAAY,EAAE,CAAC;IAGlB,IAAI,YAAY,IAAI,YAAY,CAAC,OAAO,EAAE,CAAC;QACzC,MAAM,OAAO,GAAG,YAAY,CAAC,YAAY,IAAI,CAAC,CAAC;QAC/C,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,IAAI,SAAS,CAAC;QAClD,MAAM,aAAa,GAAG,YAAY,CAAC,kBAAkB,IAAI,CAAC,CAAC;QAE3D,UAAU,CAAC,SAAS,CAClB;YACE,IAAI,EAAE,uBAAuB;YAC7B,KAAK,EAAE,gBAAgB,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ;gBAC1C,gBAAgB,OAAO,CAAC,WAAW,EAAE,IAAI;gBACzC,uBAAuB,aAAa,QAAQ;gBAC5C,eAAe,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc,EAAE;YACjE,MAAM,EAAE,IAAI;SACb,CACF,CAAC;IACJ,CAAC;SAAM,CAAC;QACN,UAAU,CAAC,SAAS,CAClB;YACE,IAAI,EAAE,uBAAuB;YAC7B,KAAK,EAAE,6CAA6C;gBAC7C,eAAe,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc,EAAE;YACjE,MAAM,EAAE,IAAI;SACb,CACF,CAAC;IACJ,CAAC;IAGD,UAAU,CAAC,SAAS,CAClB;QACE,IAAI,EAAE,kBAAkB;QACxB,KAAK,EAAE,eAAe,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY,IAAI;YACtE,qBAAqB,cAAc,CAAC,UAAU,IAAI;YAClD,eAAe,cAAc,CAAC,aAAa,YAAY;YACvD,qBAAqB,cAAc,CAAC,WAAW,EAAE;QACxD,MAAM,EAAE,IAAI;KACb,CACF,CAAC;IAGF,IAAI,UAAU,IAAI,UAAU,CAAC,OAAO,EAAE,CAAC;QACrC,MAAM,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC;QAC9B,UAAU,CAAC,SAAS,CAClB;YACE,IAAI,EAAE,8BAA8B;YACpC,KAAK,EAAE,uBAAuB,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI;gBACnD,mBAAmB,KAAK,CAAC,kBAAkB,IAAI,CAAC,IAAI;gBACpD,eAAe,KAAK,CAAC,cAAc,IAAI,CAAC,IAAI;gBAC5C,qBAAqB,KAAK,CAAC,WAAW,IAAI,CAAC,EAAE;YACpD,MAAM,EAAE,IAAI;SACb,CACF,CAAC;IACJ,CAAC;IAGD,UAAU,CAAC,SAAS,CAClB;QACE,IAAI,EAAE,sBAAsB;QAC5B,KAAK,EAAE,iBAAiB,MAAM,CAAC,GAAG,CAAC,eAAe,YAAY;YACvD,0BAA0B,MAAM,CAAC,GAAG,CAAC,kBAAkB,IAAI;YAC3D,mBAAmB,MAAM,CAAC,GAAG,CAAC,aAAa,EAAE;QACpD,MAAM,EAAE,IAAI;KACb,CACF,CAAC;IAEF,MAAM,WAAW,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;AACxD,CAAC;AAED,KAAK,UAAU,iBAAiB,CAAC,WAAwC;IACvE,MAAM,UAAU,GAAG,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IAC7D,MAAM,OAAO,GAAG,WAAW,CAAC,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;IAE1D,MAAM,KAAK,GAAG,IAAI,YAAY,EAAE;SAC7B,YAAY,EAAE,CAAC;IAElB,IAAI,OAAO,EAAE,CAAC;QACZ,KAAK;aACF,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC;aACtB,QAAQ,CAAC,uBAAuB,CAAC;aACjC,cAAc,CAAC,wCAAwC,UAAU,CAAC,QAAQ,EAAE,CAAC;aAC7E,SAAS,CAAC;YACT,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,KAAK,UAAU,CAAC,EAAE,MAAM,UAAU,CAAC,QAAQ,GAAG;YACrD,MAAM,EAAE,KAAK;SACd,CAAC,CAAC;IACP,CAAC;SAAM,CAAC;QACN,KAAK;aACF,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC;aACvB,QAAQ,CAAC,oBAAoB,CAAC;aAC9B,cAAc,CAAC,mCAAmC,UAAU,CAAC,QAAQ,EAAE,CAAC;aACxE,SAAS,CAAC;YACT,IAAI,EAAE,MAAM;YACZ,KAAK,EAAE,KAAK,UAAU,CAAC,EAAE,MAAM,UAAU,CAAC,QAAQ,GAAG;YACrD,MAAM,EAAE,KAAK;SACd,CAAC,CAAC;IACP,CAAC;IAED,MAAM,WAAW,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAEjD,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;QAC5C,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;QAC5B,aAAa,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ;QACxC,YAAY,EAAE,UAAU,CAAC,EAAE;QAC3B,cAAc,EAAE,UAAU,CAAC,QAAQ;QACnC,OAAO;KACR,CAAC,CAAC;AACL,CAAC;AAED,KAAK,UAAU,oBAAoB,CAAC,WAAwC;IAC1E,MAAM,WAAW,GAAG,WAAW,CAAC,QAAQ,EAAE,CAAC;IAC3C,WAAW,CAAC,cAAc,EAAE,CAAC;IAE7B,MAAM,KAAK,GAAG,IAAI,YAAY,EAAE;SAC7B,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC;SACtB,QAAQ,CAAC,4BAA4B,CAAC;SACtC,cAAc,CAAC,2CAA2C,CAAC;SAC3D,SAAS,CAAC;QACT,IAAI,EAAE,gBAAgB;QACtB,KAAK,EAAE,GAAG,WAAW,CAAC,UAAU,EAAE;QAClC,MAAM,EAAE,IAAI;KACb,CAAC;SACD,YAAY,EAAE,CAAC;IAElB,MAAM,WAAW,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAEjD,MAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;QAC3C,OAAO,EAAE,WAAW,CAAC,IAAI,CAAC,EAAE;QAC5B,aAAa,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ;QACxC,aAAa,EAAE,WAAW,CAAC,UAAU;KACtC,CAAC,CAAC;AACL,CAAC;AAED,KAAK,UAAU,YAAY,CAAC,WAAwC;IAClE,MAAM,CAAC,YAAY,EAAE,SAAS,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;QAClD,SAAS,CAAC,eAAe,EAAE;QAC3B,SAAS,CAAC,SAAS,EAAE;KACtB,CAAC,CAAC;IAEH,MAAM,WAAW,GAAG,IAAI,YAAY,EAAE;SACnC,QAAQ,CAAC,iBAAiB,CAAC;SAC3B,YAAY,EAAE,CAAC;IAElB,IAAI,SAAS,IAAI,YAAY,EAAE,CAAC;QAC9B,WAAW;aACR,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC;aACtB,cAAc,CAAC,2BAA2B,CAAC;aAC3C,SAAS,CACR;YACE,IAAI,EAAE,eAAe;YACrB,KAAK,EAAE,WAAW;YAClB,MAAM,EAAE,IAAI;SACb,EACD;YACE,IAAI,EAAE,kBAAkB;YACxB,KAAK,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc;YAC7D,MAAM,EAAE,IAAI;SACb,EACD;YACE,IAAI,EAAE,mBAAmB;YACzB,KAAK,EAAE,YAAY,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ;YACjE,MAAM,EAAE,IAAI;SACb,CACF,CAAC;IACN,CAAC;SAAM,CAAC;QACN,WAAW;aACR,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC;aACpB,cAAc,CAAC,0BAA0B,CAAC;aAC1C,SAAS,CACR;YACE,IAAI,EAAE,eAAe;YACrB,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,YAAY;YAC7C,MAAM,EAAE,IAAI;SACb,EACD;YACE,IAAI,EAAE,kBAAkB;YACxB,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,gBAAgB;YACvD,MAAM,EAAE,IAAI;SACb,CACF,CAAC;IACN,CAAC;IAED,MAAM,WAAW,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;AACzD,CAAC"}