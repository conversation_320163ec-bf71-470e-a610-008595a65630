export interface FaucetResponse {
    success: boolean;
    transactionHash?: string;
    amount?: string;
    message: string;
    walletAddress?: string;
    faucetAddress?: string;
    error?: {
        code: string;
        details?: string;
    };
    retryAfter?: number;
}
export interface FaucetStatus {
    success: boolean;
    faucetAddress: string;
    balance: string;
    balanceInSui: number;
    network: string;
    rpcUrl: string;
    defaultAmount: string;
    defaultAmountInSui: number;
    isHealthy: boolean;
}
export declare class FaucetApiService {
    private api;
    constructor();
    requestTokens(walletAddress: string): Promise<FaucetResponse>;
    getFaucetStatus(): Promise<FaucetStatus | null>;
    getHealth(): Promise<boolean>;
    getAdminStats(days?: number): Promise<any>;
}
export declare const faucetApi: FaucetApiService;
//# sourceMappingURL=faucetApi.d.ts.map