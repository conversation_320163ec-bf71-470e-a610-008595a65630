export declare const config: {
    discord: {
        token: string;
        clientId: string;
        guildId: string;
        logChannelId: string;
    };
    faucet: {
        apiUrl: string;
        apiKey: string;
    };
    bot: {
        prefix: string;
        cooldownMinutes: number;
        maxRequestsPerUser: number;
        adminRoleName: string;
    };
    rateLimit: {
        enabled: boolean;
        windowMinutes: number;
        maxRequests: number;
    };
    features: {
        enableStatsCommand: boolean;
        enableHelpCommand: boolean;
        enableAdminCommands: boolean;
    };
    logging: {
        level: string;
    };
};
//# sourceMappingURL=index.d.ts.map