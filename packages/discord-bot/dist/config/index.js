import dotenv from 'dotenv';
dotenv.config();
export const config = {
    discord: {
        token: process.env['DISCORD_TOKEN'],
        clientId: process.env['DISCORD_CLIENT_ID'],
        guildId: process.env['DISCORD_GUILD_ID'],
        logChannelId: process.env['LOG_CHANNEL_ID'],
    },
    faucet: {
        apiUrl: process.env['FAUCET_API_URL'] || 'http://localhost:3001/api/v1',
        apiKey: process.env['FAUCET_API_KEY'] || 'suisuisui',
    },
    bot: {
        prefix: process.env['BOT_PREFIX'] || '/',
        cooldownMinutes: parseInt(process.env['COOLDOWN_MINUTES'] || '60'),
        maxRequestsPerUser: parseInt(process.env['MAX_REQUESTS_PER_USER'] || '1'),
        adminRoleName: process.env['ADMIN_ROLE_NAME'] || 'Faucet Admin',
    },
    rateLimit: {
        enabled: process.env['RATE_LIMIT_ENABLED'] === 'true',
        windowMinutes: parseInt(process.env['RATE_LIMIT_WINDOW_MINUTES'] || '60'),
        maxRequests: parseInt(process.env['RATE_LIMIT_MAX_REQUESTS'] || '5'),
    },
    features: {
        enableStatsCommand: process.env['ENABLE_STATS_COMMAND'] !== 'false',
        enableHelpCommand: process.env['ENABLE_HELP_COMMAND'] !== 'false',
        enableAdminCommands: process.env['ENABLE_ADMIN_COMMANDS'] !== 'false',
    },
    logging: {
        level: process.env['LOG_LEVEL'] || 'info',
    },
};
const requiredEnvVars = ['DISCORD_TOKEN', 'DISCORD_CLIENT_ID'];
for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
        throw new Error(`Missing required environment variable: ${envVar}`);
    }
}
//# sourceMappingURL=index.js.map