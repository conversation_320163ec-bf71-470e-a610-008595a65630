# Deploy using AWS CodeDeploy (No SSH required)
name: Deploy with <PERSON>Deploy

on:
  push:
    branches: [ main ]
  workflow_dispatch:

env:
  AWS_REGION: ap-southeast-2
  APPLICATION_NAME: sui-faucet-app
  DEPLOYMENT_GROUP: sui-faucet-deployment-group

jobs:
  deploy:
    name: Deploy to EC2 via CodeDeploy
    runs-on: ubuntu-latest
    
    permissions:
      id-token: write
      contents: read
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies and build
      run: |
        npm ci
        cd packages/backend && npm ci && npm run build

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Create deployment package
      run: |
        mkdir -p deployment
        
        # Copy application files
        cp -r packages/backend/dist deployment/
        cp -r packages/backend/node_modules deployment/
        cp packages/backend/package*.json deployment/
        
        # Create appspec.yml for CodeDeploy
        cat > deployment/appspec.yml << 'EOF'
        version: 0.0
        os: linux
        files:
          - source: /
            destination: /opt/sui-faucet
        permissions:
          - object: /opt/sui-faucet
            owner: ec2-user
            group: ec2-user
            mode: 755
            type:
              - directory
              - file
        hooks:
          BeforeInstall:
            - location: scripts/stop_application.sh
              timeout: 300
              runas: root
          AfterInstall:
            - location: scripts/install_dependencies.sh
              timeout: 300
              runas: root
          ApplicationStart:
            - location: scripts/start_application.sh
              timeout: 300
              runas: root
          ApplicationStop:
            - location: scripts/stop_application.sh
              timeout: 300
              runas: root
          ValidateService:
            - location: scripts/validate_service.sh
              timeout: 300
              runas: root
        EOF
        
        # Create deployment scripts
        mkdir -p deployment/scripts
        
        # Stop application script
        cat > deployment/scripts/stop_application.sh << 'EOF'
        #!/bin/bash
        systemctl stop sui-faucet || true
        EOF
        
        # Install dependencies script
        cat > deployment/scripts/install_dependencies.sh << 'EOF'
        #!/bin/bash
        
        # Install Node.js if not present
        if ! command -v node &> /dev/null; then
          curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
          yum install -y nodejs
        fi
        
        # Create environment file
        cat > /opt/sui-faucet/.env << 'ENVEOF'
        NODE_ENV=production
        PORT=3001
        SUI_NETWORK=testnet
        SUI_RPC_URL=https://fullnode.testnet.sui.io/
        SUI_PRIVATE_KEY=${{ secrets.SUI_FAUCET_PRIVATE_KEY }}
        DATABASE_URL=${{ secrets.DATABASE_URL }}
        REDIS_URL=${{ secrets.REDIS_URL }}
        API_KEY=${{ secrets.API_KEY }}
        ADMIN_PASSWORD=${{ secrets.ADMIN_PASSWORD }}
        JWT_SECRET=${{ secrets.JWT_SECRET }}
        RATE_LIMIT_MAX_PER_IP=50
        RATE_LIMIT_MAX_PER_WALLET=1
        LOG_LEVEL=info
        ENVEOF
        
        chown ec2-user:ec2-user /opt/sui-faucet/.env
        chmod 600 /opt/sui-faucet/.env
        EOF
        
        # Start application script
        cat > deployment/scripts/start_application.sh << 'EOF'
        #!/bin/bash
        
        # Create systemd service
        cat > /etc/systemd/system/sui-faucet.service << 'SERVICEEOF'
        [Unit]
        Description=Sui Faucet API
        After=network.target
        
        [Service]
        Type=simple
        User=ec2-user
        WorkingDirectory=/opt/sui-faucet
        Environment=NODE_ENV=production
        EnvironmentFile=/opt/sui-faucet/.env
        ExecStart=/usr/bin/node dist/index.js
        Restart=always
        RestartSec=10
        StandardOutput=syslog
        StandardError=syslog
        SyslogIdentifier=sui-faucet
        
        [Install]
        WantedBy=multi-user.target
        SERVICEEOF
        
        systemctl daemon-reload
        systemctl enable sui-faucet
        systemctl start sui-faucet
        EOF
        
        # Validate service script
        cat > deployment/scripts/validate_service.sh << 'EOF'
        #!/bin/bash
        
        # Wait for service to start
        sleep 10
        
        # Check if service is running
        if systemctl is-active --quiet sui-faucet; then
          echo "✅ Service is running"
          
          # Test health endpoint
          if curl -f http://localhost:3001/api/v1/health; then
            echo "✅ Health check passed"
            exit 0
          else
            echo "❌ Health check failed"
            exit 1
          fi
        else
          echo "❌ Service is not running"
          systemctl status sui-faucet
          exit 1
        fi
        EOF
        
        # Make scripts executable
        chmod +x deployment/scripts/*.sh
        
        # Create deployment archive
        cd deployment
        zip -r ../deployment.zip .
        cd ..

    - name: Upload to S3
      run: |
        aws s3 cp deployment.zip s3://${{ secrets.DEPLOYMENT_BUCKET }}/sui-faucet/deployment-$(date +%Y%m%d-%H%M%S).zip

    - name: Create CodeDeploy deployment
      run: |
        aws deploy create-deployment \
          --application-name ${{ env.APPLICATION_NAME }} \
          --deployment-group-name ${{ env.DEPLOYMENT_GROUP }} \
          --s3-location bucket=${{ secrets.DEPLOYMENT_BUCKET }},key=sui-faucet/deployment-$(date +%Y%m%d-%H%M%S).zip,bundleType=zip \
          --deployment-config-name CodeDeployDefault.EC2OneAtATime \
          --description "Automated deployment from GitHub Actions"
