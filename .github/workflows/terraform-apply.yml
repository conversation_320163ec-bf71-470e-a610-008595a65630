# Terraform Apply Workflow
name: Terraform Apply

on:
  push:
    branches: [ main ]
    paths:
      - 'terraform/**'
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      destroy:
        description: 'Destroy infrastructure'
        required: false
        default: false
        type: boolean

env:
  TF_VERSION: '1.6.0'
  AWS_REGION: 'us-west-2'

jobs:
  terraform-apply:
    name: Terraform Apply
    runs-on: ubuntu-latest
    
    environment: 
      name: ${{ github.event.inputs.environment || 'staging' }}
      url: ${{ steps.output.outputs.application_url }}
    
    permissions:
      contents: read
      id-token: write

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: ${{ secrets.AWS_ROLE_ARN }}
        role-session-name: terraform-apply-${{ github.event.inputs.environment || 'staging' }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Setup Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: ${{ env.TF_VERSION }}
        terraform_wrapper: false

    - name: Terraform Init
      run: |
        terraform init \
          -backend-config="bucket=${{ secrets.TF_STATE_BUCKET }}" \
          -backend-config="key=sui-faucet/${{ github.event.inputs.environment || 'staging' }}/terraform.tfstate" \
          -backend-config="region=${{ env.AWS_REGION }}" \
          -backend-config="dynamodb_table=${{ secrets.TF_STATE_LOCK_TABLE }}"
      working-directory: terraform

    - name: Terraform Plan
      id: plan
      run: |
        terraform plan \
          -var-file="environments/${{ github.event.inputs.environment || 'staging' }}.tfvars" \
          -var="environment=${{ github.event.inputs.environment || 'staging' }}" \
          -out=tfplan
      working-directory: terraform

    - name: Terraform Apply
      if: github.event.inputs.destroy != 'true'
      run: terraform apply -auto-approve tfplan
      working-directory: terraform

    - name: Terraform Destroy
      if: github.event.inputs.destroy == 'true'
      run: |
        terraform destroy \
          -var-file="environments/${{ github.event.inputs.environment || 'staging' }}.tfvars" \
          -var="environment=${{ github.event.inputs.environment || 'staging' }}" \
          -auto-approve
      working-directory: terraform

    - name: Get Terraform Outputs
      id: output
      if: github.event.inputs.destroy != 'true'
      run: |
        echo "application_url=$(terraform output -raw application_url)" >> $GITHUB_OUTPUT
        echo "database_endpoint=$(terraform output -raw database_endpoint)" >> $GITHUB_OUTPUT
        echo "redis_endpoint=$(terraform output -raw redis_endpoint)" >> $GITHUB_OUTPUT
        echo "ecs_cluster_name=$(terraform output -raw ecs_cluster_name)" >> $GITHUB_OUTPUT
        echo "ecs_service_name=$(terraform output -raw ecs_service_name)" >> $GITHUB_OUTPUT
      working-directory: terraform

    - name: Update GitHub Environment
      if: github.event.inputs.destroy != 'true'
      uses: actions/github-script@v7
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          const environment = '${{ github.event.inputs.environment || 'staging' }}';
          const outputs = {
            application_url: '${{ steps.output.outputs.application_url }}',
            database_endpoint: '${{ steps.output.outputs.database_endpoint }}',
            redis_endpoint: '${{ steps.output.outputs.redis_endpoint }}',
            ecs_cluster_name: '${{ steps.output.outputs.ecs_cluster_name }}',
            ecs_service_name: '${{ steps.output.outputs.ecs_service_name }}'
          };
          
          console.log(`Infrastructure deployed to ${environment}:`);
          console.log(JSON.stringify(outputs, null, 2));

    - name: Notify Slack
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        text: |
          Terraform ${{ github.event.inputs.destroy == 'true' && 'Destroy' || 'Apply' }} ${{ job.status }}
          Environment: ${{ github.event.inputs.environment || 'staging' }}
          Application URL: ${{ steps.output.outputs.application_url }}
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Wait for infrastructure to be ready
  health-check:
    name: Health Check
    runs-on: ubuntu-latest
    needs: terraform-apply
    if: github.event.inputs.destroy != 'true'
    
    steps:
    - name: Wait for application to be ready
      run: |
        echo "Waiting for application to be ready..."
        for i in {1..30}; do
          if curl -f -s "${{ needs.terraform-apply.outputs.application_url }}/api/v1/health"; then
            echo "Application is ready!"
            exit 0
          fi
          echo "Attempt $i failed, waiting 30 seconds..."
          sleep 30
        done
        echo "Application failed to become ready"
        exit 1

    - name: Run smoke tests
      run: |
        # Basic API tests
        curl -f "${{ needs.terraform-apply.outputs.application_url }}/api/v1/health"
        curl -f "${{ needs.terraform-apply.outputs.application_url }}/docs"
        echo "Smoke tests passed!"
