name: Deploy to Production

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '20'
  AWS_REGION: 'ap-southeast-2'
  ENVIRONMENT: 'prod'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    if: ${{ !inputs.force_deploy }}
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: testpassword
          POSTGRES_USER: testuser
          POSTGRES_DB: testdb
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: packages/backend/package-lock.json

    - name: Install dependencies
      run: |
        cd packages/backend
        npm ci

    - name: Run tests
      run: |
        cd packages/backend
        npm run test
      env:
        NODE_ENV: test
        DATABASE_URL: postgresql://testuser:testpassword@localhost:5432/testdb
        REDIS_URL: redis://localhost:6379

  terraform-plan:
    name: Terraform Plan
    runs-on: ubuntu-latest
    needs: [test]
    if: always() && (needs.test.result == 'success' || inputs.force_deploy)
    
    outputs:
      plan-output: ${{ steps.plan.outputs.stdout }}
      
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Setup Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: 1.6.0

    - name: Terraform Init
      run: |
        cd terraform/environments/${{ env.ENVIRONMENT }}
        terraform init

    - name: Terraform Validate
      run: |
        cd terraform/environments/${{ env.ENVIRONMENT }}
        terraform validate

    - name: Terraform Plan
      id: plan
      run: |
        cd terraform/environments/${{ env.ENVIRONMENT }}
        terraform plan -no-color -input=false
      env:
        TF_VAR_environment: ${{ env.ENVIRONMENT }}
        TF_VAR_aws_region: ${{ env.AWS_REGION }}

  terraform-apply:
    name: Terraform Apply
    runs-on: ubuntu-latest
    needs: [terraform-plan]
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Setup Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: 1.6.0

    - name: Terraform Init
      run: |
        cd terraform/environments/${{ env.ENVIRONMENT }}
        terraform init

    - name: Terraform Apply
      run: |
        cd terraform/environments/${{ env.ENVIRONMENT }}
        terraform apply -auto-approve -input=false
      env:
        TF_VAR_environment: ${{ env.ENVIRONMENT }}
        TF_VAR_aws_region: ${{ env.AWS_REGION }}

    - name: Get infrastructure outputs
      id: terraform-outputs
      run: |
        cd terraform/environments/${{ env.ENVIRONMENT }}
        echo "instance_ip=$(terraform output -raw instance_public_ip)" >> $GITHUB_OUTPUT
        echo "load_balancer_dns=$(terraform output -raw load_balancer_dns)" >> $GITHUB_OUTPUT

  deploy:
    name: Deploy Application
    runs-on: ubuntu-latest
    needs: [terraform-apply]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ env.AWS_REGION }}

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: packages/backend/package-lock.json

    - name: Install dependencies and build
      run: |
        cd packages/backend
        npm ci --production
        npm run build

    - name: Create deployment package
      run: |
        tar -czf deployment.tar.gz \
          packages/backend/dist/ \
          packages/backend/package.json \
          packages/backend/package-lock.json \
          packages/backend/ecosystem.config.js \
          scripts/

    - name: Deploy to EC2
      run: |
        # Get instance IP from Terraform outputs
        cd terraform/environments/${{ env.ENVIRONMENT }}
        INSTANCE_IP=$(terraform output -raw instance_public_ip)
        
        # Copy deployment package to EC2
        scp -o StrictHostKeyChecking=no -i ~/.ssh/deploy_key \
          deployment.tar.gz ubuntu@$INSTANCE_IP:/tmp/
        
        # Execute deployment script on EC2
        ssh -o StrictHostKeyChecking=no -i ~/.ssh/deploy_key ubuntu@$INSTANCE_IP << 'EOF'
          cd /opt/sui-faucet
          tar -xzf /tmp/deployment.tar.gz --strip-components=2
          ./scripts/deploy.sh
        EOF
      env:
        SSH_PRIVATE_KEY: ${{ secrets.EC2_SSH_PRIVATE_KEY }}

  health-check:
    name: Health Check
    runs-on: ubuntu-latest
    needs: [deploy, terraform-apply]
    
    steps:
    - name: Wait for application to start
      run: sleep 30

    - name: Health check
      run: |
        cd terraform/environments/${{ env.ENVIRONMENT }}
        LOAD_BALANCER_DNS=$(terraform output -raw load_balancer_dns)
        
        # Check health endpoint
        for i in {1..10}; do
          if curl -f "http://$LOAD_BALANCER_DNS/api/v1/health"; then
            echo "Health check passed!"
            exit 0
          fi
          echo "Health check attempt $i failed, retrying in 30s..."
          sleep 30
        done
        
        echo "Health check failed after 10 attempts"
        exit 1

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [health-check]
    if: always()
    
    steps:
    - name: Notify success
      if: needs.health-check.result == 'success'
      run: |
        echo "🎉 Production deployment successful!"
        # Add Slack/Discord notification here
        
    - name: Notify failure
      if: needs.health-check.result == 'failure'
      run: |
        echo "❌ Production deployment failed!"
        # Add Slack/Discord notification here
